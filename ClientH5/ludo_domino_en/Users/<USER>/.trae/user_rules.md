# 通用编码规则文档

## 目录
1. [代码风格规范](#代码风格规范)
2. [命名约定](#命名约定)
3. [代码结构](#代码结构)
4. [注释规范](#注释规范)
5. [版本控制](#版本控制)
6. [测试规范](#测试规范)
7. [性能优化](#性能优化)
8. [安全规范](#安全规范)
9. [文档规范](#文档规范)
10. [代码审查](#代码审查)

## 代码风格规范

### 通用原则
- 保持代码简洁、清晰、易读
- 遵循一致的缩进和格式化规则
- 使用有意义的变量和函数名
- 避免过长的代码行（建议不超过120字符）

### 缩进和空格
- 使用4个空格进行缩进，不使用Tab键
- 运算符前后添加空格：`a + b` 而不是 `a+b`
- 逗号后添加空格：`func(a, b, c)` 而不是 `func(a,b,c)`
- 大括号风格保持一致

### 代码格式化
```javascript
// 好的示例
function calculateTotal(items) {
    let total = 0;
    for (const item of items) {
        total += item.price * item.quantity;
    }
    return total;
}

// 避免的写法
function calculateTotal(items){let total=0;for(const item of items){total+=item.price*item.quantity;}return total;}
```

## 命名约定

### 变量命名
- 使用驼峰命名法（camelCase）
- 布尔变量使用 `is`、`has`、`can` 等前缀
- 常量使用全大写字母和下划线

```javascript
// 变量
const userName = 'john';
const isLoggedIn = true;
const hasPermission = false;

// 常量
const MAX_RETRY_COUNT = 3;
const API_BASE_URL = 'https://api.example.com';
```

### 函数命名
- 使用动词开头，描述函数的行为
- 保持函数名简洁但具有描述性

```javascript
// 好的函数名
function getUserById(id) { }
function validateEmail(email) { }
function calculateTotalPrice(items) { }

// 避免的函数名
function data() { }
function process() { }
function handle() { }
```

### 类和接口命名
- 使用帕斯卡命名法（PascalCase）
- 接口名可以使用 `I` 前缀（可选）

```typescript
class UserManager { }
class DatabaseConnection { }
interface IUserRepository { }
interface PaymentGateway { }
```

## 代码结构

### 文件组织
- 按功能模块组织文件和目录
- 保持目录结构清晰和一致
- 相关文件放在同一目录下
