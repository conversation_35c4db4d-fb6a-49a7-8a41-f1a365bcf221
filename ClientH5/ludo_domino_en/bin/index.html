﻿<!DOCTYPE html>
<html>

<head>
	<meta charset='utf-8' />
	<title>YallaLudo</title>
	<meta name='viewport' content='width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no'
	/>
	<meta name="renderer" content="webkit" />
	<meta name='apple-mobile-web-app-capable' content='yes' />
	<meta name='full-screen' content='true' />
	<meta name='x5-fullscreen' content='true' />
	<meta name='360-fullscreen' content='true' />
	<meta name="laya" screenorientation="portrait" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
	<meta http-equiv='expires' content='0' />
	<meta http-equiv="Cache-Control" content="no-siteapp" />
</head>

<body>
	<!--以下引用了常用类库，如果不使用，可以删除-->

	<!--核心包，封装了显示对象渲染，事件，时间管理，时间轴动画，缓动，消息交互,socket，本地存储，鼠标触摸，声音，加载，颜色滤镜，位图字体等-->
	<script type="text/javascript" src="libs/laya.core.js"></script>
	<!--封装了webgl渲染管线，如果使用webgl渲染，可以在初始化时调用Laya.init(1000,800,laya.webgl.WebGL);-->
	<script type="text/javascript" src="libs/laya.webgl.js"></script>
	<!--是动画模块，包含了swf动画，骨骼动画等-->
	<script type="text/javascript" src="libs/laya.ani.js"></script>
	<!--包含更多webgl滤镜，比如外发光，阴影，模糊以及更多-->
	<script type="text/javascript" src="libs/laya.filter.js"></script>
	<!--封装了html动态排版功能-->
	<script type="text/javascript" src="libs/laya.html.js"></script>
	<!--粒子类库-->
	<!--<script type="text/javascript" src="libs/laya.particle.js"></script>-->
	<!--提供tileMap解析支持-->
	<!--<script type="text/javascript" src="libs/laya.tiledmap.js"></script>-->
	<!--提供了制作UI的各种组件实现-->
	<script type="text/javascript" src="libs/laya.ui.js"></script>
	<script type="text/javascript" src="libs/protobuf.js"></script>
	<script type="text/javascript" src="libs/bytebuffer.js"></script>
	<script type="text/javascript" src="libs/base64.js"></script>
	<script type="text/javascript" src="libs/laya.debugtool.js"></script>
	<!-- 物理引擎matter.js -->
	<!--用户自定义顺序文件添加到这里-->
	<!--jsfile--Custom-->
	<script type="text/javascript" src="libs/md5.js"></script>
	<!--<script type="text/javascript" src="libs/logClient.js"></script>-->
	<!--jsfile--Custom-->
	<!--自定义的js(src文件夹下)文件自动添加到下面jsfile模块标签里面里-->
	<!--删除标签，ide不会自动添加js文件，请谨慎操作-->
	<!--jsfile--startTag-->
	<script src="js/view/snake/view/game/TopView.js"></script>
	<script src="js/view/snake/view/game/MyHanderView.js"></script>
	<script src="js/view/snake/view/game/BottomView.js"></script>
	<script src="js/view/snake/view/common/Setting.js"></script>
	<script src="js/view/snake/data/game/item/snakeInterface.js"></script>
	<script src="js/view/snake/data/game/item/Style.js"></script>
	<script src="js/view/snake/data/game/item/Station.js"></script>
	<script src="js/view/snake/data/game/MapData.js"></script>
	<script src="js/view/snake/data/common/Enum.js"></script>
	<script src="js/view/snake/data/common/Common.js"></script>
	<script src="js/view/public/dialog/WarningDialog.js"></script>
	<script src="js/view/public/chat/UnReadMsg.js"></script>
	<script src="js/view/public/chat/MsgItem.js"></script>
	<script src="js/view/public/chat/InteractiveItem.js"></script>
	<script src="js/view/public/baseView/TipManager.js"></script>
	<script src="js/view/public/baseView/Setting.js"></script>
	<script src="js/view/public/baseView/NameItem.js"></script>
	<script src="js/view/ludo/sub/Sparkler.js"></script>
	<script src="js/view/ludo/resultPage/AwardInfo.js"></script>
	<script src="js/view/ludo/props/gridProp/JunglePropManager.js"></script>
	<script src="js/view/ludo/props/gridProp/GridPropManager.js"></script>
	<script src="js/view/ludo/props/eventProp/EventPropManager.js"></script>
	<script src="js/view/ludo/props/effects/LudoBuffManager.js"></script>
	<script src="js/view/ludo/offline/util.js"></script>
	<script src="js/view/ludo/offline/random.js"></script>
	<script src="js/view/ludo/offline/chess.js"></script>
	<script src="js/view/ludo/offline/MapData.js"></script>
	<script src="js/view/ludo/ludoData/LudoGrid.js"></script>
	<script src="js/view/ludo/ludoData/LudoBoardData.js"></script>
	<script src="js/view/ludo/ludoData/Ludo.muteSpectator.js"></script>
	<script src="js/view/ludo/ludoData/Ludo.enum.js"></script>
	<script src="js/view/ludo/ludoData/Ludo.Undo.js"></script>
	<script src="js/view/ludo/ludoData/Ludo.Global.js"></script>
	<script src="js/view/ludo/gamePage/TestSkin.js"></script>
	<script src="js/view/ludo/funny/ChessBubbleManager.js"></script>
	<script src="js/view/ludo/duelMode/DuelManager.js"></script>
	<script src="js/view/ludo/chess/ChessManger.js"></script>
	<script src="js/view/jackaro/util/QuadraticBezier.js"></script>
	<script src="js/view/jackaro/util/JackaroTipsShow.js"></script>
	<script src="js/view/jackaro/util/JackaroHandCardMonitor.js"></script>
	<script src="js/view/jackaro/util/JackaroCardLogger.js"></script>
	<script src="js/view/jackaro/toast/ToastManager.js"></script>
	<script src="js/view/jackaro/tips/TipsViewManager.js"></script>
	<script src="js/view/jackaro/tips/JackaroChessMaskViewManager.js"></script>
	<script src="js/view/jackaro/tips/BaseTipInterface.js"></script>
	<script src="js/view/jackaro/player/JackaroGamePlayerManager.js"></script>
	<script src="js/view/jackaro/jackaroData/JackaroBoardData.js"></script>
	<script src="js/view/jackaro/head/NameItem.js"></script>
	<script src="js/view/jackaro/effectAni/EffectAniManager.js"></script>
	<script src="js/view/jackaro/data/game/Room.js"></script>
	<script src="js/view/jackaro/data/common/ToGridInterface.js"></script>
	<script src="js/view/jackaro/data/common/PokerMoveDataInterface.js"></script>
	<script src="js/view/jackaro/data/common/PlayerShowInfoInterface.js"></script>
	<script src="js/view/jackaro/data/common/PlayerShowGameDataInterface.js"></script>
	<script src="js/view/jackaro/data/common/PlayerSelfGameDataInterface.js"></script>
	<script src="js/view/jackaro/data/common/PlayerJackarooGameDataInterface.js"></script>
	<script src="js/view/jackaro/data/common/PlayerChessInterface.js"></script>
	<script src="js/view/jackaro/data/common/PlayResultItemDataInterface.js"></script>
	<script src="js/view/jackaro/data/common/PlayResultDataInterface.js"></script>
	<script src="js/view/jackaro/data/common/PlayJackResultDataInterface.js"></script>
	<script src="js/view/jackaro/data/common/PlayCommonResultDataInterface.js"></script>
	<script src="js/view/jackaro/data/common/MoveToBornDataInterface.js"></script>
	<script src="js/view/jackaro/data/common/MoveChessDataInterface.js"></script>
	<script src="js/view/jackaro/data/common/ImpactedChessDataInterface.js"></script>
	<script src="js/view/jackaro/data/common/ImpactChessResultInterface.js"></script>
	<script src="js/view/jackaro/data/common/GridInterface.js"></script>
	<script src="js/view/jackaro/data/common/GridDataInterface.js"></script>
	<script src="js/view/jackaro/data/common/Enum.js"></script>
	<script src="js/view/jackaro/data/common/DiscardedPokerInterface.js"></script>
	<script src="js/view/jackaro/data/common/ComplexPlayKingResultInterface.js"></script>
	<script src="js/view/jackaro/data/common/Common.js"></script>
	<script src="js/view/jackaro/data/common/ChessPanelInterface.js"></script>
	<script src="js/view/jackaro/data/common/ChessInterface.js"></script>
	<script src="js/view/jackaro/data/common/ChangePlayerInterface.js"></script>
	<script src="js/view/jackaro/chess/TransferGridProcessor.js"></script>
	<script src="js/view/jackaro/chess/JackaroTest.js"></script>
	<script src="js/view/jackaro/chess/JackaroChessOrbitSpecial.js"></script>
	<script src="js/view/jackaro/chess/JackaroChessOrbitManager.js"></script>
	<script src="js/view/jackaro/chess/JackaroChessOrbitEffectManager.js"></script>
	<script src="js/view/jackaro/chess/JackaroChessManagerTest.js"></script>
	<script src="js/view/jackaro/chess/JackaroChessManager.js"></script>
	<script src="js/view/jackaro/chess/JackaroChess1V1ConflictHandler.js"></script>
	<script src="js/view/jackaro/chess/JKCHtailingManager.js"></script>
	<script src="js/view/jackaro/chess/GridDataTransformer.js"></script>
	<script src="js/view/jackaro/card/JackaroHandCardView.js"></script>
	<script src="js/view/jackaro/card/JackaroCardReplenishManager.js"></script>
	<script src="js/view/jackaro/card/JackaroCardManager.js"></script>
	<script src="js/view/jackaro/card/JackaroCardEffectHintManager.js"></script>
	<script src="js/view/jackaro/card/JackaroCardDataInterface.js"></script>
	<script src="js/view/jackaro/card/CardOperateDefine.js"></script>
	<script src="js/view/jackaro/TopView.js"></script>
	<script src="js/view/jackaro/TestView.js"></script>
	<script src="js/view/jackaro/BottomView.js"></script>
	<script src="js/view/domino/view/result/RoundResult.js"></script>
	<script src="js/view/domino/view/game/playerItem/PlayerRoleItem.js"></script>
	<script src="js/view/domino/view/game/playerItem/PlayerItem.js"></script>
	<script src="js/view/domino/view/game/playerItem/PlayerGameHead.js"></script>
	<script src="js/view/domino/view/game/playerItem/GameHead.js"></script>
	<script src="js/view/domino/view/game/TopView.js"></script>
	<script src="js/view/domino/view/game/TipView.js"></script>
	<script src="js/view/domino/view/game/PlayersView.js"></script>
	<script src="js/view/domino/view/game/HandCardView.js"></script>
	<script src="js/view/domino/view/game/BottomView.js"></script>
	<script src="js/view/domino/view/dialog/AppItem.js"></script>
	<script src="js/view/domino/data/room/vo/Base.js"></script>
	<script src="js/view/domino/data/net/BaseNet.js"></script>
	<script src="js/view/domino/data/game/item/roomInterface.js"></script>
	<script src="js/view/domino/data/game/item/DominoInterface.js"></script>
	<script src="js/view/domino/data/common/TestData.js"></script>
	<script src="js/view/domino/data/common/Enum.js"></script>
	<script src="js/view/domino/data/common/Common.js"></script>
	<script src="js/view/domino/core/net/NativeGameSocket.js"></script>
	<script src="js/view/domino/core/net/GameSocket.js"></script>
	<script src="js/view/activity/view/item/LevelItem.js"></script>
	<script src="js/core/tools/Yalla.ViewExclusion.js"></script>
	<script src="js/core/tools/Yalla.Util.js"></script>
	<script src="js/core/tools/Yalla.URL.js"></script>
	<script src="js/core/tools/Yalla.Time.js"></script>
	<script src="js/core/tools/Yalla.System.js"></script>
	<script src="js/core/tools/Yalla.Str.js"></script>
	<script src="js/core/tools/Yalla.Sound.js"></script>
	<script src="js/core/tools/Yalla.Skin.js"></script>
	<script src="js/core/tools/Yalla.Screen.js"></script>
	<script src="js/core/tools/Yalla.PhoneVibrate.js"></script>
	<script src="js/core/tools/Yalla.Mute.js"></script>
	<script src="js/core/tools/Yalla.Interface.js"></script>
	<script src="js/core/tools/Yalla.Friends.js"></script>
	<script src="js/core/tools/Yalla.Font.js"></script>
	<script src="js/core/tools/Yalla.Emoji.js"></script>
	<script src="js/core/tools/Yalla.DomainUtil.js"></script>
	<script src="js/core/tools/Yalla.CLogDBA.js"></script>
	<script src="js/core/prepare/Prepare.js"></script>
	<script src="js/core/net/NetMessageCache.js"></script>
	<script src="js/core/net/GateWay.js"></script>
	<script src="js/core/data/Global.js"></script>
	<script src="js/core/data/Config.js"></script>
	<script src="js/core/data/ChatDataPool.js"></script>
	<script src="js/arabic/TashkeelLocation.js"></script>
	<script src="js/arabic/IsolatedArabicLetters.js"></script>
	<script src="js/arabic/Char.js"></script>
	<script src="js/arabic/ArabicTable.js"></script>
	<script src="js/arabic/ArabicSupport.js"></script>
	<script src="js/arabic/ArabicMapping.js"></script>
	<script src="js/arabic/ArabicFixerTool.js"></script>
	<script src="js/core/data/cache/BaseDataItem.js"></script>
	<script src="js/core/data/cache/DataItem.js"></script>
	<script src="js/core/net/Client.js"></script>
	<script src="js/core/net/ClientBrower.js"></script>
	<script src="js/core/prepare/PrepareItem.js"></script>
	<script src="js/core/prepare/PrepareData.js"></script>
	<script src="js/core/prepare/PrepareRes.js"></script>
	<script src="js/core/tools/Yalla.Cache.js"></script>
	<script src="js/core/tools/Yalla.Debug.js"></script>
	<script src="js/core/tools/Yalla.File.js"></script>
	<script src="js/core/tools/Yalla.Native.js"></script>
	<script src="js/core/tools/Yalla.NativeWebSocket.js"></script>
	<script src="js/core/tools/Yalla.Skeleton.js"></script>
	<script src="js/core/tools/yalla.Voice.js"></script>
	<script src="js/ui/layaUI.max.all.js"></script>
	<script src="js/view/public/BaseMain.js"></script>
	<script src="js/view/activity/ActivityMain.js"></script>
	<script src="js/view/activity/data/ActivityService.js"></script>
	<script src="js/view/activity/manager/ChampionAnimation.js"></script>
	<script src="js/view/activity/manager/MaqueeManager.js"></script>
	<script src="js/view/activity/view/DayGame.js"></script>
	<script src="js/view/activity/view/DayResult.js"></script>
	<script src="js/view/activity/view/DayRule.js"></script>
	<script src="js/view/activity/view/VsAnimation.js"></script>
	<script src="js/view/activity/view/item/HeadCost.js"></script>
	<script src="js/view/activity/view/item/HeadLevel.js"></script>
	<script src="js/view/activity/view/item/HeadResult.js"></script>
	<script src="js/view/activity/view/item/MaqueeItem.js"></script>
	<script src="js/view/activity/view/item/RuleItem.js"></script>
	<script src="js/view/domino/DominoMain.js"></script>
	<script src="js/view/domino/WatchMain.js"></script>
	<script src="js/view/domino/core/event/YallaEvent.js"></script>
	<script src="js/view/domino/core/net/Client.js"></script>
	<script src="js/view/domino/core/net/NativeClient.js"></script>
	<script src="js/view/domino/data/game/User.js"></script>
	<script src="js/view/domino/data/game/UserService.js"></script>
	<script src="js/view/domino/data/net/DominoNet.js"></script>
	<script src="js/view/domino/data/net/DominoStreamNet.js"></script>
	<script src="js/view/domino/data/net/RoomService.js"></script>
	<script src="js/view/domino/data/room/DominoData.js"></script>
	<script src="js/view/domino/data/room/Room.js"></script>
	<script src="js/view/domino/data/room/vo/DrawGamePool.js"></script>
	<script src="js/view/domino/data/room/vo/MugginsPool.js"></script>
	<script src="js/view/domino/data/voice/VoiceService.js"></script>
	<script src="js/view/domino/view/dialog/AppManager.js"></script>
	<script src="js/view/domino/view/game/Game.js"></script>
	<script src="js/view/domino/view/game/WatchGame.js"></script>
	<script src="js/view/domino/view/game/cardItem/CardItem.js"></script>
	<script src="js/view/domino/view/game/cardItem/HintCardItem.js"></script>
	<script src="js/view/domino/view/game/cardItem/OtherCardItem.js"></script>
	<script src="js/view/domino/view/game/cardItem/OutCardItem.js"></script>
	<script src="js/view/domino/view/game/cardItem/ResultCardItem.js"></script>
	<script src="js/view/domino/view/game/mode/AMode.js"></script>
	<script src="js/view/domino/view/game/mode/DrawGame.js"></script>
	<script src="js/view/domino/view/game/mode/Muggins.js"></script>
	<script src="js/view/domino/view/result/Result.js"></script>
	<script src="js/view/domino/view/result/RoundDrawResult.js"></script>
	<script src="js/view/domino/view/result/RoundMugginResult.js"></script>
	<script src="js/view/jackaro/CircularProgressBar.js"></script>
	<script src="js/view/jackaro/Game.js"></script>
	<script src="js/view/jackaro/JackaroMain.js"></script>
	<script src="js/view/jackaro/JackaroMap.js"></script>
	<script src="js/view/jackaro/JackaroResult.js"></script>
	<script src="js/view/jackaro/JackaroTeamResult.js"></script>
	<script src="js/view/jackaro/RoomBattleRecordInfo.js"></script>
	<script src="js/view/jackaro/SpineAniPlayer.js"></script>
	<script src="js/view/jackaro/card/JackaroCardItem.js"></script>
	<script src="js/view/jackaro/card/JackaroPickUpCardItem.js"></script>
	<script src="js/view/jackaro/chess/JackaroChess.js"></script>
	<script src="js/view/jackaro/chess/JackaroChooseChessTag.js"></script>
	<script src="js/view/jackaro/chess/JkChooseChessManager.js"></script>
	<script src="js/view/jackaro/chess/TurnGrid.js"></script>
	<script src="js/view/jackaro/data/game/JackaroUserService.js"></script>
	<script src="js/view/jackaro/data/game/User.js"></script>
	<script src="js/view/jackaro/effectAni/VoiceService.js"></script>
	<script src="js/view/jackaro/gamepage/JackaroAddTimeView.js"></script>
	<script src="js/view/jackaro/head/Head.js"></script>
	<script src="js/view/jackaro/novice/JackaroNovice.js"></script>
	<script src="js/view/jackaro/novice/JackaroNoviceBubble.js"></script>
	<script src="js/view/jackaro/novice/JackaroNoviceGame.js"></script>
	<script src="js/view/jackaro/novice/JackarooFinger.js"></script>
	<script src="js/view/jackaro/player/JackaroGamePlayer.js"></script>
	<script src="js/view/jackaro/tips/ChooseCardNoTouchOperateView.js"></script>
	<script src="js/view/jackaro/tips/ChooseChessNoTouchOperateView.js"></script>
	<script src="js/view/jackaro/tips/ChooseChessOperateView.js"></script>
	<script src="js/view/jackaro/tips/DiscardCardView.js"></script>
	<script src="js/view/jackaro/tips/DiscardSelfForceCard.js"></script>
	<script src="js/view/jackaro/tips/ExChangeChessView.js"></script>
	<script src="js/view/jackaro/tips/GmSelectCard.js"></script>
	<script src="js/view/jackaro/tips/JackaroCardNoTouchView.js"></script>
	<script src="js/view/jackaro/tips/JackaroChessMaskView.js"></script>
	<script src="js/view/jackaro/tips/JackaroChessNoTouchView.js"></script>
	<script src="js/view/jackaro/tips/PickUpCardView.js"></script>
	<script src="js/view/jackaro/tips/ProgressBarCommonView.js"></script>
	<script src="js/view/jackaro/tips/RuleCardView.js"></script>
	<script src="js/view/jackaro/tips/SelectStepNumView.js"></script>
	<script src="js/view/jackaro/tips/SelectStepOperateView.js"></script>
	<script src="js/view/jackaro/tips/TrustSystemView.js"></script>
	<script src="js/view/jackaro/toast/ToastView.js"></script>
	<script src="js/view/jackaro/util/LoopTime.js"></script>
	<script src="js/view/ludo/LudoMain.js"></script>
	<script src="js/view/ludo/chat/BarrageItem.js"></script>
	<script src="js/view/ludo/chat/BarrageManger.js"></script>
	<script src="js/view/ludo/ludoData/Ludo.interface.js"></script>
	<script src="js/view/ludo/chat/LudoChat.js"></script>
	<script src="js/view/ludo/chess/LudoChess.js"></script>
	<script src="js/view/ludo/dialog/Audience.js"></script>
	<script src="js/view/ludo/dialog/SaveGame.js"></script>
	<script src="js/view/ludo/duelMode/DuelKnife.js"></script>
	<script src="js/view/ludo/duelMode/DuelMap.js"></script>
	<script src="js/view/ludo/duelMode/DuelRank.js"></script>
	<script src="js/view/ludo/duelMode/TimePiece.js"></script>
	<script src="js/view/ludo/funny/ChessBubble.js"></script>
	<script src="js/view/ludo/funny/Combo.js"></script>
	<script src="js/view/ludo/funny/ComboManager.js"></script>
	<script src="js/view/ludo/gamePage/BaseGame.js"></script>
	<script src="js/view/ludo/gamePage/Game.js"></script>
	<script src="js/view/ludo/gamePage/JungleNovice.js"></script>
	<script src="js/view/ludo/gamePage/Map.js"></script>
	<script src="js/view/ludo/gamePage/Novice.js"></script>
	<script src="js/view/ludo/gamePage/OffGame.js"></script>
	<script src="js/view/ludo/gamePage/TestArb.js"></script>
	<script src="js/view/ludo/ludoData/Ludo.ProtoBuffer.js"></script>
	<script src="js/view/ludo/ludoData/Ludo.Room.js"></script>
	<script src="js/view/ludo/net/Ludo.GameSocket.js"></script>
	<script src="js/view/ludo/net/Queue.js"></script>
	<script src="js/view/ludo/nightMode/BrillancyArea.js"></script>
	<script src="js/view/ludo/nightMode/Curtain.js"></script>
	<script src="js/view/ludo/nightMode/KillTip.js"></script>
	<script src="js/view/ludo/nightMode/NightMap.js"></script>
	<script src="js/view/ludo/novice/NoviceBubble.js"></script>
	<script src="js/view/ludo/offline/Service.js"></script>
	<script src="js/view/ludo/offline/mapEv.js"></script>
	<script src="js/view/ludo/offline/player.js"></script>
	<script src="js/view/ludo/props/BuffIcon.js"></script>
	<script src="js/view/ludo/props/effects/base/BaseBuff.js"></script>
	<script src="js/view/ludo/props/effects/base/BaseProp.js"></script>
	<script src="js/view/ludo/props/effects/buff/ArrowDice.js"></script>
	<script src="js/view/ludo/props/effects/buff/ExpLable.js"></script>
	<script src="js/view/ludo/props/effects/buff/JungleDice.js"></script>
	<script src="js/view/ludo/props/effects/buff/JungleSnail.js"></script>
	<script src="js/view/ludo/props/effects/buff/LuckDice.js"></script>
	<script src="js/view/ludo/props/effects/buff/StarAni.js"></script>
	<script src="js/view/ludo/props/effects/prop/AureoleProp.js"></script>
	<script src="js/view/ludo/props/effects/prop/CircleAureoleProp.js"></script>
	<script src="js/view/ludo/props/effects/prop/Debut.js"></script>
	<script src="js/view/ludo/props/effects/prop/Lightning.js"></script>
	<script src="js/view/ludo/props/effects/prop/LockDown.js"></script>
	<script src="js/view/ludo/props/effects/prop/RocketProp.js"></script>
	<script src="js/view/ludo/props/effects/prop/ShieldProp.js"></script>
	<script src="js/view/ludo/props/effects/prop/TigerBg.js"></script>
	<script src="js/view/ludo/props/effects/prop/TigerClaw.js"></script>
	<script src="js/view/ludo/props/effects/prop/TigerFace.js"></script>
	<script src="js/view/ludo/props/effects/prop/Tornado.js"></script>
	<script src="js/view/ludo/props/effects/weather/DarkCloud.js"></script>
	<script src="js/view/ludo/props/effects/weather/Tiger.js"></script>
	<script src="js/view/ludo/props/eventProp/EventProp.js"></script>
	<script src="js/view/ludo/props/gridProp/GridProp.js"></script>
	<script src="js/view/ludo/props/gridProp/JungleProp.js"></script>
	<script src="js/view/ludo/resultPage/DuelResult.js"></script>
	<script src="js/view/ludo/resultPage/Result.js"></script>
	<script src="js/view/ludo/resultPage/TeamResult.js"></script>
	<script src="js/view/ludo/sub/CdAni.js"></script>
	<script src="js/view/ludo/sub/ChooseStep.js"></script>
	<script src="js/view/ludo/sub/Dice.js"></script>
	<script src="js/view/ludo/sub/GamePlayer.js"></script>
	<script src="js/view/ludo/sub/LudoBanner.js"></script>
	<script src="js/view/ludo/sub/RoadBlockManager.js"></script>
	<script src="js/view/ludo/sub/RoomInfo.js"></script>
	<script src="js/view/ludo/sub/TerminusAni.js"></script>
	<script src="js/view/ludo/sub/VoiceTip.js"></script>
	<script src="js/view/public/baseView/CdAni.js"></script>
	<script src="js/view/public/baseView/Head.js"></script>
	<script src="js/view/public/baseView/TipItem.js"></script>
	<script src="js/view/public/baseView/TrustTip.js"></script>
	<script src="js/view/public/chat/Bubble.js"></script>
	<script src="js/view/public/chat/BubbleReport.js"></script>
	<script src="js/view/public/chat/Chat.js"></script>
	<script src="js/view/public/chat/ChatItem.js"></script>
	<script src="js/view/public/chat/ChatItemL.js"></script>
	<script src="js/view/public/chat/ChatItemR.js"></script>
	<script src="js/view/public/chat/ChatManager.js"></script>
	<script src="js/view/public/chat/ChatTabItem.js"></script>
	<script src="js/view/public/chat/ChatTeam.js"></script>
	<script src="js/view/public/chat/FastChat.js"></script>
	<script src="js/view/public/chat/FastTabItem.js"></script>
	<script src="js/view/public/chat/InterActiveGiftAnimation.js"></script>
	<script src="js/view/public/chat/InteractiveGift.js"></script>
	<script src="js/view/public/chat/Listview.js"></script>
	<script src="js/view/public/chat/LiveChat.js"></script>
	<script src="js/view/public/connect/ReconnectControl.js"></script>
	<script src="js/view/public/connect/ReconnectView.js"></script>
	<script src="js/view/public/connect/WatchThinking.js"></script>
	<script src="js/view/public/dialog/Confirm.js"></script>
	<script src="js/view/public/dialog/DialogManager.js"></script>
	<script src="js/view/public/dialog/TIp.js"></script>
	<script src="js/view/snake/SnakeMain.js"></script>
	<script src="js/view/snake/data/game/Room.js"></script>
	<script src="js/view/snake/data/game/UserService.js"></script>
	<script src="js/view/snake/manager/SkAnimation.js"></script>
	<script src="js/view/snake/view/TestPanel.js"></script>
	<script src="js/view/snake/view/game/BaseMap.js"></script>
	<script src="js/view/snake/view/game/Game.js"></script>
	<script src="js/view/snake/view/game/SnakeEffect.js"></script>
	<script src="js/view/snake/view/game/item/Chess.js"></script>
	<script src="js/view/snake/view/game/item/ChessOld.js"></script>
	<script src="js/view/snake/view/result/Result.js"></script>
	<!--jsfile--endTag-->

	<!--启动类添加到这里-->
	<!--jsfile--Main-->
	<script src="js/Main.js"></script>
	<!--jsfile--Main-->
</body>

</html>