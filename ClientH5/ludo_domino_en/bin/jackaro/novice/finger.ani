{"type": "View", "props": {"sceneWidth": 600, "sceneHeight": 400, "sceneColor": "#000000"}, "compId": 1, "child": [{"type": "GraphicNode", "props": {}, "compId": 3}, {"type": "GraphicNode", "props": {}, "compId": 4}], "animations": [{"nodes": [{"target": 3, "keyframes": {"y": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "y", "index": 0}, {"value": -125, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "y", "index": 32}, {"value": -125, "tweenMethod": "linearNone", "tween": true, "target": 3, "label": null, "key": "y", "index": 53}], "x": [{"value": 73, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "x", "index": 0}, {"value": 73, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "x", "index": 32}, {"value": 73, "tweenMethod": "linearNone", "tween": true, "target": 3, "label": null, "key": "x", "index": 53}], "skin": [{"value": "jackaro/novice/finger.png", "tweenMethod": "linearNone", "tween": false, "target": 3, "key": "skin", "index": 0}], "rotation": [{"value": -20, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "rotation", "index": 0}, {"value": 27, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "rotation", "index": 32}, {"value": 27, "tweenMethod": "linearNone", "tween": true, "target": 3, "label": null, "key": "rotation", "index": 53}], "pivotY": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "pivotY", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "pivotY", "index": 32}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 3, "label": null, "key": "pivotY", "index": 53}], "pivotX": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "pivotX", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "pivotX", "index": 32}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 3, "label": null, "key": "pivotX", "index": 53}], "anchorY": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "anchorY", "index": 0}, {"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "anchorY", "index": 32}, {"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 3, "label": null, "key": "anchorY", "index": 53}], "anchorX": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "anchorX", "index": 0}, {"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "anchorX", "index": 32}, {"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 3, "label": null, "key": "anchorX", "index": 53}]}}, {"target": 4, "keyframes": {}}], "name": "silder", "id": 1, "frameRate": 60, "action": 0}, {"nodes": [{"target": 3, "keyframes": {}}, {"target": 4, "keyframes": {"y": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 4, "key": "y", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "y", "index": 10}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "y", "index": 30}], "x": [{"value": 73, "tweenMethod": "linearNone", "tween": true, "target": 4, "key": "x", "index": 0}, {"value": 56, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "x", "index": 10}, {"value": 73, "tweenMethod": "linearNone", "tween": true, "target": 4, "key": "x", "index": 20}, {"value": 56, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "x", "index": 30}, {"value": 73, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "x", "index": 40}, {"value": 73, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "x", "index": 80}], "skin": [{"value": "game/finger.png", "tweenMethod": "linearNone", "tween": false, "target": 4, "key": "skin", "index": 0}, {"value": "game/finger.png", "tweenMethod": "linearNone", "tween": false, "target": 4, "label": null, "key": "skin", "index": 10}, {"value": "game/finger.png", "tweenMethod": "linearNone", "tween": false, "target": 4, "label": null, "key": "skin", "index": 30}, {"value": "jackaro/novice/finger.png", "tweenMethod": "linearNone", "tween": false, "target": 4, "key": "skin", "index": 80}], "scaleY": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 4, "key": "scaleY", "index": 0}, {"value": 0.9, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "scaleY", "index": 10}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 4, "key": "scaleY", "index": 20}, {"value": 0.9, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "scaleY", "index": 30}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "scaleY", "index": 40}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "scaleY", "index": 80}], "scaleX": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 4, "key": "scaleX", "index": 0}, {"value": 0.9, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "scaleX", "index": 10}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 4, "key": "scaleX", "index": 20}, {"value": 0.9, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "scaleX", "index": 30}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "scaleX", "index": 40}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "scaleX", "index": 80}], "rotation": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 4, "key": "rotation", "index": 0}, {"value": -13, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "rotation", "index": 10}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 4, "key": "rotation", "index": 20}, {"value": -13, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "rotation", "index": 30}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "rotation", "index": 40}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "rotation", "index": 80}], "anchorY": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 4, "key": "anchorY", "index": 0}, {"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "anchorY", "index": 10}, {"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "anchorY", "index": 30}], "anchorX": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 4, "key": "anchorX", "index": 0}, {"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "anchorX", "index": 10}, {"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 4, "label": null, "key": "anchorX", "index": 30}]}}], "name": "dclick", "id": 2, "frameRate": 60, "action": 0}]}