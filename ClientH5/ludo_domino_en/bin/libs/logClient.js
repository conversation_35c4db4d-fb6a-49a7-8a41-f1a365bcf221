// 日志客户端API封装
function LogClient(options) {
    // 处理默认参数
    options = options || {};
    this.serverUrl = options.serverUrl || 'ws://localhost:3000';
    this.reconnectDelay = options.reconnectDelay || 3000;
    this.overrideConsole = options.overrideConsole !== undefined ? options.overrideConsole : false;
    this.deviceCode = options.deviceCode || this._generateDeviceCode();

    this.ws = null;
    this.reconnectTimeout = null;
    this.isConnected = false;
    this.listeners = { 'message': [], 'connect': [], 'disconnect': [], 'error': [] };

    // 设备信息
    this.deviceInfo = {
        deviceCode: this.deviceCode,
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        screen: {
            width: screen.width,
            height: screen.height,
            pixelRatio: window.devicePixelRatio
        }
    };

    // // 如果需要，重写console方法
    //     if (this.overrideConsole) {
    //         this._overrideConsole();
    //     }
}

/**
 * 生成设备码
 * @private
 */
LogClient.prototype._generateDeviceCode = function () {
    // 尝试从localStorage获取
    if (typeof localStorage !== 'undefined') {
        var deviceCode = localStorage.getItem('logDeviceCode');
        if (deviceCode) {
            return deviceCode;
        }
    }

    // 生成新的设备码
    deviceCode = 'device_' + Math.random().toString(36).substr(2, 9);

    // 保存到localStorage
    if (typeof localStorage !== 'undefined') {
        localStorage.setItem('logDeviceCode', deviceCode);
    }

    return deviceCode;
};

/**
 * 连接到服务器
 */
LogClient.prototype.connect = function () {
    // 关闭现有连接
    if (this.ws) {
        this.ws.close();
    }

    // 创建新连接
    try {
        this.ws = new WebSocket(this.serverUrl);

        // 连接成功
        var self = this;
        this.ws.onopen = function () {
            self.isConnected = true;
            console.log('日志客户端已连接到服务器');

            // 注册设备
            self.ws.send(JSON.stringify({
                type: 'register_device',
                deviceCode: self.deviceCode
            }));

            self._notifyListeners('connect', { serverUrl: self.serverUrl });

            // 发送设备信息 - 替换模板字符串
            self.sendLog('info', '设备已连接: ' + self.deviceInfo.userAgent, { deviceInfo: self.deviceInfo });
        };

        // 接收消息
        this.ws.onmessage = function (event) {
            try {
                var logData = JSON.parse(event.data);
                self._notifyListeners('message', logData);
            } catch (error) {
                console.error('解析消息失败:', error);
                self._notifyListeners('error', { error: '解析消息失败: ' + error.message });
            }
        };

        // 连接关闭
        this.ws.onclose = function () {
            self.isConnected = false;
            console.log('日志客户端连接已关闭');
            self._notifyListeners('disconnect', {});

            // 清除现有超时
            if (self.reconnectTimeout) {
                clearTimeout(self.reconnectTimeout);
            }

            // 尝试重连
            self.reconnectTimeout = setTimeout(function () { self.connect(); }, self.reconnectDelay);
        };

        // 发生错误
        this.ws.onerror = function (error) {
            console.error('日志客户端连接错误:', error);
            self._notifyListeners('error', { error: '连接错误: ' + error.message });
        };
    } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        this._notifyListeners('error', { error: '连接失败: ' + error.message });
    }
};

/**
 * 断开连接
 */
LogClient.prototype.disconnect = function () {
    if (this.ws) {
        this.ws.close();
        this.ws = null;
    }

    if (this.reconnectTimeout) {
        clearTimeout(this.reconnectTimeout);
        this.reconnectTimeout = null;
    }
};

/**
 * 发送日志
 * @param {string} type 日志类型 (debug, info, warning, error)
 * @param {string|object} message 日志消息
 * @param {Object} extra 额外信息
 */
LogClient.prototype.sendLog = function (type, message, extra) {
    // 处理默认参数
    extra = extra || {};
    if (!this.isConnected || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
        console.warn('日志客户端未连接，无法发送日志');
        return false;
    }

    try {
        // 构建日志数据 - 替换对象展开运算符
        console.log("sendLog1" + JSON.stringify(message));

        var logData = {
            type: type,
            message: typeof message === 'string' ? message : JSON.stringify(message),
            timestamp: new Date().toISOString()
        };

        // 使用Object.assign合并extra
        for (var key in extra) {
            if (extra.hasOwnProperty(key)) {
                logData[key] = extra[key];
            }
        }
        console.log("sendLog2" + JSON.stringify(logData));

        // 发送日志
        this.ws.send(JSON.stringify(logData));
        return true;
    } catch (error) {
        console.error('发送日志失败:', error);
        this._notifyListeners('error', { error: '发送日志失败: ' + error.message });
        return false;
    }
};

/**
 * 发送调试日志
 * @param {string|object} message 日志消息
 * @param {Object} extra 额外信息
 */
LogClient.prototype.debug = function (message, extra) {
    extra = extra || {};
    return this.sendLog('debug', message, extra);
};

/**
 * 发送信息日志
 * @param {string|object} message 日志消息
 * @param {Object} extra 额外信息
 */
LogClient.prototype.info = function (message, extra) {
    extra = extra || {};
    return this.sendLog('info', message, extra);
};

/**
 * 发送警告日志
 * @param {string|object} message 日志消息
 * @param {Object} extra 额外信息
 */
LogClient.prototype.warning = function (message, extra) {
    extra = extra || {};
    return this.sendLog('warning', message, extra);
};

/**
 * 发送错误日志
 * @param {string|object} message 日志消息
 * @param {Object} extra 额外信息
 */
LogClient.prototype.error = function (message, extra) {
    extra = extra || {};
    return this.sendLog('error', message, extra);
};

/**
 * 添加事件监听器
 * @param {string} event 事件类型 (message, connect, disconnect, error)
 * @param {Function} listener 监听函数
 */
LogClient.prototype.on = function (event, listener) {
    if (this.listeners[event]) {
        this.listeners[event].push(listener);
    }
};

/**
 * 移除事件监听器
 * @param {string} event 事件类型
 * @param {Function} listener 监听函数
 */
LogClient.prototype.off = function (event, listener) {
    if (this.listeners[event]) {
        var filtered = [];
        for (var i = 0; i < this.listeners[event].length; i++) {
            if (this.listeners[event][i] !== listener) {
                filtered.push(this.listeners[event][i]);
            }
        }
        this.listeners[event] = filtered;
    }
};

/**
 * 通知所有监听器
 * @private
 */
LogClient.prototype._notifyListeners = function (event, data) {
    if (this.listeners[event]) {
        for (var i = 0; i < this.listeners[event].length; i++) {
            try {
                this.listeners[event][i](data);
            } catch (error) {
                console.error('事件监听器错误 (' + event + '):', error);
            }
        }
    }
};

/**
 * 重写console方法
 * @private
 */
LogClient.prototype._overrideConsole = function () {
    console = laya.utils.Browser.window.console;
    // 保存原始方法
    var originalConsole = {
        log: console.log,
        info: console.info,
        warn: console.warn,
        error: console.error
    };

    var self = this;

    // 重写log方法
    console.log = function () {
        var args = Array.prototype.slice.call(arguments);
        originalConsole.log.apply(console, args);
        self.sendConsoleLog('debug', args);
    };

    // 重写info方法
    console.info = function () {
        var args = Array.prototype.slice.call(arguments);
        originalConsole.info.apply(console, args);
        self.sendConsoleLog('info', args);
    };

    // 重写warn方法
    console.warn = function () {
        var args = Array.prototype.slice.call(arguments);
        originalConsole.warn.apply(console, args);
        self.sendConsoleLog('warning', args);
    };

    // 重写error方法
    console.error = function () {
        var args = Array.prototype.slice.call(arguments);
        originalConsole.error.apply(console, args);
        self.sendConsoleLog('error', args);
    };

    // 监听全局错误
    window.addEventListener('error', function (event) {
        self.sendLog('error', event.error.name + ': ' + event.error.message + '\n' + event.error.stack, {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
        });
    });

    // 监听未捕获的Promise拒绝
    window.addEventListener('unhandledrejection', function (event) {
        self.sendLog('error', '未处理的Promise拒绝: ' + event.reason, {
            promise: event.promise
        });
    });
};

/**
 * 安全地序列化对象，处理循环引用
 * @private
 */
LogClient.prototype._safeStringify = function (obj, space) {
    // 处理默认参数
    space = space || 2;
    var seen = new WeakSet();
    try {
        return JSON.stringify(obj, function (key, value) {
            if (typeof value === 'object' && value !== null) {
                if (seen.has(value)) {
                    return '[Circular]';
                }
                seen.add(value);
            }
            return value;
        }, space);
    } catch (error) {
        console.error('序列化对象失败:', error);
        return '[无法序列化: ' + error.message + ']';
    }
};

/**
 * 发送控制台日志
 * @private
 */
LogClient.prototype.sendConsoleLog = function (type, arg) {
    var self = this;
    var message = [];
    // console.log("sendConsoleLog" + JSON.stringify(args));
    // for (var i = 0; i < args.length; i++) {
    // var arg = args[i];
    if (arg instanceof Error) {
        message.push(arg.name + ': ' + arg.message + '\n' + arg.stack);
    } else if (typeof arg === 'object') {
        message.push(self._safeStringify(arg, 2));
    } else {
        message.push(arg.toString());
    }
    // }
    message = message.join(' ');

    this.sendLog(type, message);
};

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LogClient;
} else if (typeof window !== 'undefined') {
    window.LogClient = LogClient;
}