!function(t,i,e){e.un,e.uns,e.static;var n=e.class,r=e.getset,a=(e.__newvec,laya.d3.component.Component3D);laya.d3.core.ComponentNode,laya.d3.core.MeshTerrainSprite3D,laya.d3.core.Sprite3D,function(t){function i(){this._meshTerrainSprite3D=null,this._finder=null,this._setting=null,this.grid=null,i.__super.call(this)}n(i,"laya.d3.component.PathFind",t);var e=i.prototype;e._load=function(i){if(!(i instanceof laya.d3.core.MeshTerrainSprite3D))throw new Error("PathFinding: The owner must MeshTerrainSprite3D!");t.prototype._load.call(this,i),this._meshTerrainSprite3D=i},e.findPath=function(t,i,e,n){var r=this._meshTerrainSprite3D.minX,a=this._meshTerrainSprite3D.minZ,h=this._meshTerrainSprite3D.width/this.grid.width,s=this._meshTerrainSprite3D.depth/this.grid.height,o=h/2,d=s/2,l=Math.floor((t-r)/h),f=Math.floor((i-a)/s),c=Math.floor((e-r)/h),u=Math.floor((n-a)/s),p=this.grid.width-1,_=this.grid.height-1;l>p&&(l=p),f>_&&(f=_),l<0&&(l=0),f<0&&(f=0),c>p&&(c=p),u>_&&(u=_),c<0&&(c=0),u<0&&(u=0);var g=this._finder.findPath(l,f,c,u,this.grid);this.grid.reset();for(var m=1;m<g.length-1;m++){var y=g[m];y[0]=y[0]*h+o+r,y[1]=y[1]*s+d+a}return 1==g.length?(g[0][0]=e,g[0][1]=e):g.length>1&&(g[0][0]=t,g[0][1]=i,g[g.length-1][0]=e,g[g.length-1][1]=n),g},r(0,e,"setting",function(){return this._setting},function(t){t&&(this._finder=new PathFinding.finders.AStarFinder(t)),this._setting=t})}(a)}(window,document,Laya),"function"==typeof define&&define.amd&&define("laya.core",["require","exports"],function(t,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0});for(var e in Laya){var n=Laya[e];n&&n.__isclass&&(i[e]=n)}});