!function(t,e,i){i.un,i.uns;var n=i.static,o=i.class,a=i.getset,l=(i.__newvec,laya.ui.Box),s=laya.utils.Browser,r=laya.ui.<PERSON><PERSON>,h=laya.utils.Byte,c=(laya.display.css.CSSStyle,laya.ui.Component),u=i.Config,d=laya.events.Event,b=laya.events.EventDispatcher,p=(laya.filters.Filter,laya.maths.GrahamScan),f=(laya.display.Graphics,laya.resource.HTMLCanvas),m=laya.utils.Handler,g=(laya.utils.HitArea,laya.ui.Image),Z=laya.display.Input,y=(laya.ui.Label,laya.ui.List),G=laya.net.Loader,W=laya.net.LoaderManager,v=laya.net.LocalStorage,w=laya.maths.MathUtil,R=laya.maths.Matrix,V=laya.display.Node,X=laya.maths.Point,I=laya.utils.Pool,x=laya.maths.Rectangle,Y=laya.renders.Render,C=laya.renders.RenderContext,L=laya.renders.RenderSprite,N=(laya.resource.Resource,laya.resource.ResourceManager),S=laya.utils.RunDriver,k=laya.display.Sprite,T=(laya.display.Stage,laya.utils.Stat),A=(laya.display.css.Style,laya.display.Text),B=(laya.ui.TextInput,laya.resource.Texture),F=(laya.utils.Timer,laya.ui.Tree),J=(laya.ui.UIEvent,laya.net.URL),H=laya.utils.Utils,U=laya.ui.View,D=function(){function t(){}return o(t,"laya.debug.data.Base64AtlasManager"),t.replaceRes=function(e){t.base64.replaceRes(e)},n(t,["dataO",function(){return this.dataO={"comp/button1.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGIAAABRCAYAAAApS3MNAAABSUlEQVR4Xu3a0QmFMADFUJ1JXM0h3moPZ6qg4AoNeLqAIenFn65jjLE40w2sQkxvcAMI0eggRKSDEEJUDEQ4/COEiBiIYFiEEBEDEQyLECJiIIJhEUJEDEQwLEKIiIEIhkUIETEQwbAIISIGIhgWIUTEQATDIoSIGIhgWIQQEQMRDIsQImIggnEvYvv9IzjfxDiP/XlgJsTcCyDEXP/v14UQImIggmERQkQMRDAsQoiIgQiGRQgRMRDBsAghIgYiGBYhRMRABMMihIgYiGBYhBARAxEMixAiYiCCYRFCRAxEMCxCiIiBCMa7iAjPpzG8fY3kF0KIiIEIhkUIETEQwbAIISIGIhgWIUTEQATDIoSIGIhgWIQQEQMRDIsQImIggmERQkQMRDAsQoiIgQiGRQgRMRDBsAghIgYiGBYhRMRABMMihIgYiGBcGJiOHTRZjZAAAAAASUVORK5CYII=","comp/line2.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAECAYAAACOXx+WAAAAG0lEQVQYV2NkoDJgpLJ5DIxtra3/qWko1V0IAJvgApS1libIAAAAAElFTkSuQmCC","view/create.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAkCAYAAAC9itu8AAAAdElEQVQ4T2NkwAIWLFjwH5t4QkICIyM2CXQxmAHka/j///9mXDYxMjL6YtgwBDUg+w8crIT8MBQ0oEca55JvWNPS9xgu4tISzADyNfz///8MnrRkgmHDENSALWng9fRQ0DA40xLecglbWhpqGoZCMUNKUQkANAHAJVkE5XwAAAAASUVORK5CYII=","view/rendertime.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAkCAYAAAC9itu8AAABeUlEQVQ4T+2Uv0tCURSAvyNdcwiXBlsaaomWFgeHlqAtCPsDJHwIiUtDSxERtErtmQ6CjkHo4FpDBQ0tbVFR0BYE0eQvOnFF7T17QlOTd3m88873OD8+rtA9uVzOBIPBlIisAwvd8B1QajQahXQ63bIx6QHFYrEEJHrv7qeqZhzHOfYA+Xw+Yow5B+YHoGwymdxW1QAQEFWNAk8i8uEDuZM3gUcLZIEJYNcNqWrVcZyd7p9t8jLwYIFTYBx47UHlcjmcSCQ+B5JtpU0LnAFj3br7kE+yTalb4BCYczVqoT3AjteW4T73FlgFNgY+1IGQz4hPLGCAI2DGbweu2Auw1Vmcqk4C+8DsEOgZOBCR9/6mVdU2vgIsAdOuIVwANRFpezatuahpTYVSop1m+y6pasm8NQqSvvW61KwslkSHuCRkgvErr0taiUXaal1Sr0siWRO/9HfpF+RN9nfpB/qqmrXrv7mktVhYVm5GLo1cct9LI5e8d84/3UvfAgdlKH0EO7MAAAAASUVORK5CYII=","view/cache.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAkCAYAAABSSLCCAAAAcElEQVQ4T2NcsGDB/4SEBEYGBgYGYtmMxCpENhhsA6mA8f///5tHNTEwkBcQpIYcSD15kUtWigi51vR/jVYdOGUQy2YkViGywWSnvTOkhiAonkY1gZIRqSEHTntkRe4g10RWQIyWe5Bgo2O5R7dkBADztyP+yFzirAAAAABJRU5ErkJggg==","comp/clip_selectBox.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAAAoCAYAAAAIeF9DAAAAsElEQVRoQ+3ZQQ0AMQzEwAuqEgh/Sj2pKObhIrBsrfLonHPu12MMTEGYFg+kIFaPgmA9ClIQzQDG0w0pCGYAw2khBcEMYDgtpCCYAQynhRQEM4DhtJCCYAYwnBZSEMwAhtNCCoIZwHBmd/tTh6IUBIrx/tRbiFWkIFaPFoL1KEhBNAMYTzekIJgBDKeFFAQzgOG0kIJgBjCcFlIQzACG00IKghnAcFpIQTADGE4LwYL8U/BE1dCJ3PsAAAAASUVORK5CYII=","comp/label.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAASCAYAAACQCxruAAAAmElEQVRoQ+3aMQqAQBBDUef+hx4Zq1mrbPnhWylECHmghVZ397OOqqp97TlugdNzgEXFIaaFuwROt0LmBEay5aXb920+FjIpMJItLy1wvhUyKTCSLS8tcL4VMikwki0vLXC+FTIpMJItLy1wvhUyKTCSLS89wPP1Qeh8M0zy+84gMMbruqjA15OxbtjAu7mPa5bj0fb/A8cLgD4n/wQKNiIAAAAASUVORK5CYII=","comp/clip_tree_arrow.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAQCAYAAAArij59AAAAwUlEQVQoU5WRPRKCMBCFWUt6vYQeB06RUDpoBbFDa7yDwm30FGi9dHnOMiQDBgvT5c3b7+0PRVEUlVV9A3NmzL6T//SRfMz5CgCdtVafjlmzaHAigAbM2tE8YVo1pf0yvABoc9D3wACgBbMKIgD4qqDJsqqlMV8VGL5n/88geCJKlijSMBXFZUNx/CSi9WwX1r7R99thzKKqkxXRbMUWSE2u2sEwHsxHCbrMVSq6N4xRD9HAvJstylEkarhurlqnfQC58YP5+CvQNwAAAABJRU5ErkJggg==","view/bg_panel.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAMUlEQVRYR+3QQREAAAjDMGZk/l2CDD6pgl7SduexGCBAgAABAgQIECBAgAABAgS+BQ4oyStBhXcy5AAAAABJRU5ErkJggg==","view/bg_top.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAMUlEQVRYR+3QQREAAAjDMKZp/rWBDD6pgl7SduexGCBAgAABAgQIECBAgAABAgS+BQ6WyDMhXMLeQgAAAABJRU5ErkJggg==","view/clickselect.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAqCAYAAACDdWrxAAACfElEQVRIS8WVO2iTYRSGn5OWqpMOurg0VRBdVVCsg7GgDjpZECyirl4GEYfSgBlaB5VSpApdxCJIoeKgg7dKC21ALahIiyiKKUjxAiI4qCH1lRP/hPhfAnHpGZPv+c4573nP95ukO/xHmINmtq8RtswsPiipB/gAPAFem5nCbcSWKukIsD84/A2YBh4DL8ysWLkk0qOkDcD5GLF+Ac+Ap35ZHGjAdWB5gtJvgZFYVSWdBHaFwBlg1Mw8K0ngFiAbAm+a2XBij/6HpBbgBrAEmAVeAZ1AFU40QDCWrcBZL0/S4Vq4HtgB7DWzU5XyauDBMhhWz70ryVVdb2ZuhGpI2g1MODjfiMFrxZk3s9WNwJ6snHFxQUlXgXfAPeC5mf2O2Y5oqZLcMceCw1+AseCSSTP7mSiOpM3A7RixfvgYgAd+WUQcSSnfPWBlgtIvgf5YVSVdBA6GQF/mS2bmWcvbERmHJF+payFw0MzO1TWApKXBViwL3h5/Pk4AVTjRAMFY9njJXl6wLccrcD3wAHDUzBwuRw18JtbkbkFJruomM7sf2o4u4Jals/mFRgxeFcfBQm97UyOwM+WMiwums/k3QnMps+HWpuLIRC5TCrcRW2pbT35MRiY4XDRsVmiU5uJQIZfxb0k5Ij229eQPySJ287MLGO8Rd1M0XY6AO3LjzYVSy3fAH+VICL4a6o9VtTWbnzbYGKI+IrtQ6Ns2EFuq/5jOTnWD9f4DikeFvvbqhyg2Yzo3voJSy2fAjfEJMYPRQQ2caAAfC7AW2WkvrzU79dCwnRW4Hjgg6JrrbV9VKbkKw1Csyd2Ca7on1y2krHOub3t16//2n79SarbsH7BKtfejoCjmAAAAAElFTkSuQmCC","view/resize.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAqCAYAAACDdWrxAAABeUlEQVRIS+2UvUpdURCFvxXRKJpIEBURsVAIiiBoaaGCjY2VLyH4MBaCPoWlnQlpI6SxsQmkURQL5eK/6JK57iuRnMPZtxAkuOFUhzWz96xvjcg8tluAT5LOQqJMHba/AgPAD0nOEtruAOaB6Lon6U+ucAoYTLe7Bb5XCm1/BCaAXqAVOAHyOkYn27PA5/TGWmXHxvBeT2i7TVIM4MUp7ZhGPlY3V/pVKUxEjAIjyac74LIAjK70PwCoyfYXYDJwyqDoHtiRdFOfql0naBgIrILF/ZIi1yH6h1XbYXCPpKOq7s34GEX7JB00m445YBzYlPSQ1dF2N7CaWN2W9DNXuJxAj1uGVeuVQtvh32LyuR34DexWCv+CfAXoBzYkHb8Boe1OSRcFkBdfNY18IQiUtFUpTJjNAPEFHVfAaQFyjZ3zNBzbQ8BSWkZViEbk1uIpjXR8AKbT7jwEvpVUqEk6L0pHLN5hSWWxeq7XjI/v6Sgz0vZ7Ov7DdDwCkcb1m86tSukAAAAASUVORK5CYII=","view/clickanalyse.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAqCAYAAACDdWrxAAAC7UlEQVRIS5WWT2hUZxTFfyfGFolkoUVKrHQiEaX+IfgHa54UQzUqpWYhLbQU6ULNwgYXuog6yiiTgK2LgtAu6yqbFkpRBEURQzJEBN200NqKkxoDLnQhFUrizJU7vje8SSbzZr7FwDy+c75z7z3nfU80uMxMDin9JC0zewvYAHwIrAH65wWaWQuwOdy8CVgUHnBd0sUKoJktBbYC24B1QHMVNeck3ZWZrYhtXpUg/3/gS0kzDnT2/cDqpFqBUUnnK5pjZutDgo01Tr0g6XbVrprZypBgO9AUU/EK+ErSyzLQzC5XkTkCfBR7fl/Smeh/qasOlPRp9DAkOgp8H5P9o6SriUAnMrOzgNdswNeSntcL9IYNAQ8kHYuXU5Y6u8ZIupldAO5I+nkOsNb8wjk/ljTZKFCSvMbSMrPSiOpNx9uAz3UP4IbfWSsdrcDH4eZuYHF46LCk47PT8S6wG9gbJmRhlfoPSLrhJvdERJs7E+S73dZKmnagsx8JB50UEHdY3+x0dIUEO2qcekTSr/OlY21I4N5dEJMwA6yX9CKejqkqGn8DemPPb0v6YrZXpyS1xYbsRD3AtZjsk5IuJQKdyMyGAa/ZnbNR0tN6gd6wXwAP8SfV0jGnxki6mV1xyf4ubdTkPue/Jf3TEJCMNZFRMQLtyNwqvaTrSkdHZry1MFM8bLLPgY5U8/SyeYHvncotb5b1A/t8c2QGg3sT2WBLBbD95PiGogr9Ej0Gbap8r4ZJ5kR+MPhW7WdGd5npEFaa15IE+YWW5uklf2S6/1N7OnfasG+Ad5KiAfyVzwYfVDQnlc71YTaA8Ntrvtq/y2eDgapdTZ0a60UMhjdvmcCgWDClJge7npSBqfRYYY5M6U/M/NqO1mQ+G7xf4VUH5rNBOXtviLQfzH0afizop0fZroOJQCdKpcfyUKrZFhTpfDgU/F4nMNcH9gPwLJ8Nls3xarUaI+mp9NhTg5GJbPBZQyb3OReayP17rutmHPga1PpCOk+zrlEAAAAASUVORK5CYII=","view/res.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAoCAYAAAD6xArmAAADwUlEQVRIS+3WT2gcdRQH8O/b2SwNC7l4MAEPvbilUkoPOUmLjSDrZn4hxYKH/kGwyB4tQogiu/N+GymyoWguhVBQKKkHQTHsW9fUQwqKp4AgtMXkInhILl4CkoTdmSe/6XZp2pntLli8uMedt9/3mze/33yW8Jw+9Jxy0TeYmV8FcFVVTxPRiwA6AP5U1TvZbHapUqn8nrawxGBVJWvtNVWdJ6K05h1V/dhaW08KT/wRM1sAVQCRqn5JRLdyudw9Iora7faJKIrKqnrBNSWiahAEC0+GHwpm5utEdD+KopsuBMDbzPxt0oqstRdV9Za7lslkzlar1Z8erzsUHATBJhG93C34fmJi4ly5XG6nzTEIgjoRzanqkrX2amowM98F8Fq3wK34PWb+Ii14cXExv7e3V6hWq78+axQrANwt/kVEl5j5h0G2IzMfUdWCtfa3R/VPzvhTAG8AOM/MfwwYehTANwB+ZOYPE4ODIDhJRJvMvD9IqLW2GEXRbSJ6AcBtZr6UGPzoS2Y+lc/nt+bm5v5Oa2CtvaKqywC8bs06M7+eGszMn7nTBqDOzPNpwcvLyyPb29vfAZh2Naq6Za0tpAbXarUzURS53eGKL1trv0oKZ+a3AHytqplMJlOOoui4tfaDvqOw1lZUtabubBOtqOqN0dHRB/v7++62XwHwDoB33dkAUGPmoO92e/yitXZeVT8BkE1acbdpPQiCj4hIBw52hQsLC8c6nc77AN4E8FK3yQ4R/Qzgc2b+Je0ZDPU+fjiZp1eXFD5U8CB7u+/DGybgXxnFMA3/m1GISGwegNMAeuYBuON53lKpVBrePBG5RkTuSPc1b2ZmZnDzRKRnHoDYvIODg3u5XM69/E8AKAO40G1aNcb0N6/ZbF5X1fsAbjpInXnGmETzGo3GRdew+0DPGmPSzRORTQA988bHx89NTk6mmtdoNGLziGjJ9/1085rN5l1VPWSeMSbVvLW1tXwYhoXp6en+5olIbB6A2Dzf9wcyb319/cju7m5hdnY22TwRic3zPO98qVQayLxWq3U0DMPYPGNMsnmrq6snx8bGNqempgYyT0SKzjoAsXnGmP7mNZvNU9lsdqtYLKaaJyJXABwyzxiTbp6IxOYRUd33/VTzNjY2RnZ2dnrmAdgyxqSbJyJnAMTmEdFl3/cTzROR2DzHk6qWiei4Maa/eSJScZY99FRXPM+7MTIy8iAMQ6/dbsfmEVHPPGPM4OaJiBtDqnmuqfuL4Pv+8Oa1Wq1jYRg+ZR6A2DxjzP/mPRupfwAf56Q4urCh6QAAAABJRU5ErkJggg==","view/tab_panel.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAABICAYAAADyMAW8AAAAcUlEQVRYR+3WsQ3AMAhE0TCMeyTvP1tShRQo7lxYegxA8fUPLuac97VhwuKXKhTlFxRQ9GPDClawYvGEDwxIZu7pFRZXr4ACinY1ghWsYMX/NxWQr22edyvGGHt6hcV1NqGAon8QVrCCFYteISDnBuQB3xJuQcDkEngAAAAASUVORK5CYII=","view/btn_close.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAqCAYAAACz+XvQAAACmUlEQVRIS7WWS0/bUBCFz7mJmyZxENm06mNVoVZC7LqGn9FNqy55/BSWSEhs2/4uuqFVoA150JLKJvGdaiIH2TfXNoKQpeP5PHPO3GMTK/5xxTwsAUWkBeBZ+qAByb/Zh4pIA8CL9NqY5Dj7vw9YA/ABwDsAfwB8ITnUIhF5CuATgNcAfgH4RnJSCkwLl6AA/lXBtLZQQxFxoTr6q6LOFl2WmuJAtcY7ZuXIixsczfRyTlPfhpSN7BpwBeBtFdQLFJE2gI8AXi7GBBBl3Fdnv5L87XbpWxuFfQbw3NXM0dQLLdrDIH3ylGTiLLYB8CS9lpCc3tmU+xzL1Z9lEXl/n06KavjowCiK1uM4fqMd1Ov1s3a7fZntZjabtSeTiQYHgiC4aLVavZwpbofT6TQYDAaH1tod3bMwDHc7nc5PLZrNZmG/3z8WkS1jzGm32z1oNBqjUqD+6YM2m81xFWyeNkUaulAAlyKyWdTZbdqUmZKFakEVrLRDV7P5zY6m3rQp6tA1AMC5tXY7he51Op0fdwbGcdwdDodHWc2MMdcL9wGM1tbW9sMw/L6UNm6HChuNRifW2g1XM0dTL3TJZS1KkkTDFbVaLQqCIJcm6k0URRpxuvg39Xo9rtzDh5zt1Z/lXq+32rR5dKC1dt0YM08bAGd65BxN1ZB52ojIBcl82rgdWmsDkocAdgDoW22X5DxtSIZJkhyT3AJwCuCAZD5tfCP7oMaYcRVs/tAiDT1QHX2zqLPbtCkzxYFqjXfM3GKXAR3NtC6nqTccioAeA84BbCuU5B4Af9r4gCLSBXCU1UxErjPuj0Rk3xiznDYuMIWdANhwNXM09UKXXNai9LtQ9y4yxuS/XUijr9L0lXBDMp82j370HhJdWvsftiHJYFPSIqEAAAAASUVORK5CYII=","comp/combobox.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFsAAABCCAYAAAA476rKAAACfElEQVR4Xu3bMYsTURQF4PMmExgIWkgEU5hskyJYxGYKY5VS7NzCylL8Bftj3NbKQjuxTBWbaUwhKdIYLCJotlACA5m8kQTZZZkkeN9dbuNJOXPPu/DN5ZHkMa7dbpfgx0TAEdvEedeE2HbWxDa0JjaxLQUMe3HPJrahQECrNE3RarUOJheLBbIsq9znZAdgJ0mC4XCIer1eSa/Xa4xGI+R5TuwA272RTqeDfr9fuTeZTDCfz/dmONkK/cFggGazebnCcrnEeDw+uCKxFdiNRmO3nURRBO/9bvtYrVbEVpgejXa7XfR6PUynU8xms6O1nGzlU3DO7fbu7V5dlsf/0yO2ElsSJ7ZES1lLbCWgJE5siZaylthKQEmc2BItZS2xlYCSOLElWspaYisBJXFiS7SUtcRWAkrixJZoKWuJrQSUxIkt0VLWElsJKIkTW6L1t5an6wFooRGerofKBeZ4uh4IFxrj6XqoXECOp+sBaJoIT9c1esIsT9eFYFbl/J5tJc13agyliU1sWwHDbtyziW0oYNiKk22JfXJ6xnfXjcDdFttnb43a/b9tovQ5iG30/IltBL1tQ2xiGwoYtuJkE9tQILBV/ugl4rh2MF1sPJJP59fuc7IDsTe37mHz8Bki+MoKHhFqn9+j9vs7sQN9K7G89xRx837levHzG5Lph8p1TrZK3iF//ApxdLVI4YFk/BpA9Uc5sVXYwObOCfyDJ3AoUcIh+vIRtYuve1clthJ7G8/7p4hv30Xx6weSybuDKxL7BrARxcjTF0iyN4AviH0Tpto1ONlaQUGe2AIsbSmxtYKCPLEFWNpSYmsFBXliC7C0pZfY2oWY/zeBP8uaLni/AFTVAAAAAElFTkSuQmCC","comp/textinput.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFsAAAAWCAYAAACv8OArAAAAZElEQVRYR+3UQQkAMAwEwcZI/LtsoSL2NTGwMByZ3b3HJQIDO3H+EdidNezQGjbsUiBs+dmwQ4EwZdmwQ4EwZdmwQ4EwZdmwQ4EwZdmwQ4EwZdmwQ4EwZdmwQ4EwZdmwQ4Ew9QBe0R29X9x+dwAAAABJRU5ErkJggg==","comp/vscroll.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAhCAYAAAA/F0BXAAAAOklEQVRIS2N8+OzVf2YWFgYmJiYGcgHjqCEYQTcaJpipaTRMRsOEmDJmNJ2MppPRdEJMCIymE2JCCQAYonwDuu2VMAAAAABJRU5ErkJggg==","comp/vscroll$down.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAzCAYAAABxCePHAAAC/klEQVRIS+2WS0wTURSG/zszBcrLFVvjio0LiAqRRDAmGpRodFE1MQQQkOKGqBujRo3ExLjB4MaKgDzUaGQhvoJGYwAjYoioERcuDGxYEIwPkBY6nWvObXuLnXZaSklYOIu5M/fxzZn/nvPPsInJKa5qGhRFQaIH+w8xSbcymtTd+gBFYXAdyjM9sf7ORxgGR0t5/j9jpkhq2t5B0xQwBrgqNsnJ9V0j4BzQdQNtNYXWkKz0NDiaXkBTFTCFoaWmCHVtQ+AGh+4z0HNiO2bmPNYQGiXQvkuPoaqqiIgi8Pl8eHBqtwlA86MKS6Cy8z1gjIFzjqcXHBEBlpBgRNuOd+HVlYqogJiQIChcg/BtW5k8SaSSkxPJ5PRPTttHfkI7kcghIpn8NYfp33NLXp+TnYG1OWvA3ox9499nPSjdkCsgHJxOIjc43VMrugL9dEUD4Oj/PA4CsUfDX/jOjbmisHTDCCzi4t4QgLDrQF+qTYOmqhgYGw9BvLpv0ZNjQwieaU9b7ZCDriFhSt3VBSZNartHA6aUJ7SK+jqO5n5pSp1HiqSw1e3Di0ypwBpiU1XsudwnTanraDEqrg2GmZLbGkJh2jQVZY29JlPqPe03JX/uxLE7Nk3DjjP3pCn1Ne7HrNsjdYoLQsmWYtNQ3NCBgeZKzLrn/foEoogbQgvSUmz4454P7VQikGhpHzGSZdVOUqqYTGli6gemZ9yJ+0lSTalk/TrxtQOYaBnESbTinokev4UG+p+9/xoyJQKQn8x7vf7JjEFZ1FJBBvuC12RINIdAwtkIQuksnxgHhKBUZ6scQtLSNyiWJpav47z9STjbjfJ8k5iVN0eEs911bhZjUTWpbR+RztZ6uFBERNCq1rfS2e43lFhDsjPscDS9lM7W4dyCquuvpbM9PFkq0iHm7mSl2yP+bj05uxdeXZe5FHOL6Xdr17nQ79bziwew4NXFqwUTMiaEtKBPwtZjnRi8WgXPglfqsyQITc60pwpAeNpH1GRZtRM0pWVVcTJM6S+dYaRsIf025wAAAABJRU5ErkJggg==","comp/vscroll$bar.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAA/CAYAAAAGyyO8AAABYElEQVRYR+2Wv0sDMRTH30tarCg6dRWnQnFT6OiqoP+tk+Cig+AiHayDiNSlg+jgD47K1US+Lwm5s4o/mkElN1xy73KfcF/efTi+Ht3Y0X1Btw8FffdoLy3QSnuZ+HhwZe+exrS13hGGJYsTWSszN0rJ1zHDDbJ0eDYkgHjv5Nxub3TIGEsTY/xDVq6NAN7MfW2u2aCG1nQ0GEZIOXmp7Pw5BPDF+VaGIGQfbM6k0ng5kw8/wF/eJzP5JInZkjg2CSS8zk6vCys7Wb8r5qqsncAP+pdR1Lu9rvgVT4uYg+3F+PCtAzjzu/taKdKKBSS2/wkEMBg/Q+rB50zqzZb7ZPoD/GeZ1HySxGxJHJsEEl5nc22VmCFalpFJTjLKNUtFxlDfP72IogYAP8PPZekWM5OqjErFWpjjbxprABJRA/JYjOOOX4Bgo6bWGYKsfMg5k+lmy5n8uUxm8kkSs6Vw7Cstibc9Fv5vWQAAAABJRU5ErkJggg==","comp/vscroll$up.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAzCAYAAABxCePHAAADF0lEQVRIS92WTUhUURTHz31vPv0KKmkXrtxUGNomkCANLdCUpEatJFuIClIEFRl9kGH0BYWQElLpotGKEJXAtKQooYUFpi1axLQZMCyyZJqv926cM2/uTM288emoUHfx3v16v3fuuef+72Hume/c7/cBAwaLKWaLBZjLPc0Zk0CSJGBs4SDOObDP7i9ckuXkIbLJRJDFFrJk2SGNvZNwy7ExoZEJLWnqfQ+4SlUFaHNs0gXpQhq6x0GWGe0Y7oCicGivyYsLigup7XgFJlkCJjFwNm2HqrZR4CqHoKLC3fr8GFAMpPLqEJhMoZjpay6Bnx4vpKfYoLx1kCwKBlXoOV78BygGsudCH1nwtNVBgHBBUFFzL1n0+Gx5YghOxhINiAbFG1uZODESxf+bJShKrulv8HUusp1G/IBz1qTZIGvdamBjU584Aopzs+lbDhwfFFgc2/imLq0fazgAHF5MumBtuh3YwJsPfGdeNqgY1qqqfcSprRLgr7rWZzWbwCTL8HLKFYEEgkrUn+eHIDzNbltBSG33O+jcnxNZmrYcw5Yc7hoXotRenRPyz0IgBzrGYkTp9qEtxiEV10eEKD08Wgh7bzwTonSvIV/soK5jd53rE6I0eGY3/PL5wWYxQ+nFgShRKqK6LqTwhJNEafRKNQHCcWK3WmDHqR5NlMoSQzAWUV+9vkBMsKXYLCSbs3Oe+SGqqupGrIL3h3YclifYkjo7yZ7izIzUUGrhnvXAzA+PURkR8xCwPnMVsCUVpW0bsiCUKOH9S0980JvaLJSQUTal9Q+9/RgRJQSgnvgCgdBkxkCKektSpC9cR0HCOQgiZUMI3njijwYg+COzLP9rkLr7E3Dn4Gbhp7BPDC+n0TkhlK2zJpccuSBIfVdsutVdt9U4pLbjtVC2B0cKYN/N50LZHh0rFGGguztV14aFsvWfLiVhSrVboaSlXyjbk/NlBNKFVLT0k7INX3KAx+sXfkBlKzjpJItGLlcmhmSkptAB83h9MTuCICxBRUkMwUmY5+uFPY7LmJ7GW05SZycsSos9xUsmSr8BfgGeWI6+BgEAAAAASUVORK5CYII=","comp/button.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE0AAABFCAYAAAAPWmvdAAABA0lEQVR4Xu3ZMRGDUBRFwXwfKSgxFhfRgAbUxEakkCEO3qmX+p9m5w7NW9v7cz18I4EFbeT1fwxtbgYtmEGDVgRC458GLQiExNKgBYGQWBq0IBASS4MWBEJiadCCQEgsDVoQCImlQQsCIbE0aEEgJJZW0Pbj64Q3hFvQhmL3CQ8atLlAKCwNWhAIiaVBCwIhsTRoQSAklgYtCITE0qAFgZBYGrQgEBJLgxYEQmJp0IJASCwNWhAIiaUVtOfrdMIbwi1oQ7H7hAcN2lwgFJYGLQiExNKgBYGQWBq0IBASS4MWBEJiadCCQEgsDVoQCImlQQsCIbE0aEEgJJYGLQiExNIC2g/MxaMp6CSauwAAAABJRU5ErkJggg==","view/bg_tool.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAMklEQVRYR+3QQREAAAjDMCYG/DsEGXxSBb2ke7YeiwECBAgQIECAAAECBAgQIEDgW+AAAeIuAVS/mngAAAAASUVORK5CYII=","comp/minBtn.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAA8CAYAAAB1odqiAAAArUlEQVRYR+3X0QmAMAwE0GQN19B9nM193CmiIH7ZXOAoRc/fpjl8jVDdOj/eOc8USBcXqUjLAtDQRMSOdHb3JatTYCZUXodIy10bGxTI1Lx6/YA0Ima6W2tKFcjmdpGKtCow7NBAdxozy+804Gfx/cDqbLzWDzs0ekNY4B9nOMEehMKTVIEEyKeFSKmc18+MppRtipJuYPCa1SkwEyqvo6Tlxm8bFEijvBt9n/QA/fOPydLHcUIAAAAASUVORK5CYII=","view/zoom_out.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAoCAYAAAD6xArmAAACy0lEQVRIS92WQU8TQRTH/28oQkj0CL0QOMAJQkz4DkS6A+GA+A00Hrhj0uy8NiTwEdBPAOrB0Fnq3U8g6gkOSjxUjpCQCu08M5u2qaVAt7YmOqfNZPa3b9/+Z35L6NOgPnHx98Gbm5sTlUplA0AGQBpACcBBKpXazmaz3+5607YVM/MjEXlNRPdbASJyTkRrzPz+Nvg1MDNPAvgI4AGA10qpvHPuSCk17ZwLAazV4HPM/PUmeDvwSwBPAbxl5sf+RmYWZo7XMvOehwPYYebnScAnAMaVUrNhGH5pBefz+Rnn3GcAJ8w8kQT8E8A9AEMA/HXrqM9fMrO/bjvataJvFdd7/IaZfS9/67ExZpeIngB4xczPklQ8KSKHPmoispdKpXKjo6PHp6enU5VKxXhoV6moVXhnjpVS5wDOwjD81K7qG7e033lXV1cviMjvvDEAP0TkYHBwcKtarT4UkXcALolo1RhTaIV3dVYYY9aIyOfZDw9fMcYUm+FdgWvtYgCmBisrpRbCMPxQh3cNbgM3zJzvCdhDcrncuojMA8gy8/eegTvO8U0Lk87/UY9ve9h/BI6iyJ+1GyLScB4RHQDYDoKgO+dFURSfFQCuOQ9A7LwgCJI5r1gsTlar1YbznHP5crl8NDw8PK2Uip3n4QMDA3OLi4udO89a23Ce1jp2nrVWtNbxh7bWxs4jop0gCDp3XhRFJyIy7pybXV5ejp3XDN7f359RSsXO01p37jxrbey8i4uLoZGRkWvOa5q/1Fp37rx+VtxwntY6dl5zK6Io2hWR2Hla686dV0vFoY+aP8xFJJdOp49LpdIUEZkaNHkqfIWd5JiIzkXkLAiCZM7zO09EYueJyBgRxc4joi0ADeeJyOrS0lJvnBdFkf8xbDhPKbWSyWR647xCocC+53XnAVjQWvfGeS1wo7XunfOstesA5pVS2Uwm8w877xeHf444cscwYAAAAABJRU5ErkJggg==","view/refresh2.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAA/CAYAAAAPIIPGAAAEIElEQVRYR+2XTUhjVxTH/+fGpBrGT/xoBQdFFMMQLNLNbLooLbaFzqKMUhCSZwsuhGG6KCNd6DuRLgqzmGVxUd8LUrpoYWZTKO1yNi2F1oVtceEHflSLqNEav8bklPuqgsl75sUPSsucTQj33v895+R/7y+XcA1B16CJ/6GoYRiDItKfzWZjExMTv5/XtoLlx2Kxm0qp1wH0AHgTwC4RfWRZ1mdewp6ig4ODN9Lp9CMieh+AchH41Lbtj92EXUUHBgaCh4eH3wJ4zSObGSLqtSzrZ9+ihmF8CODR8YIflFL3MplMNxF9IiJWIBC4Pz4+/ldR5RuG8QuAlwGsAWi3bTsVj8dvAWhOJpPfFPK2a/mGYewDeAHAV7Zt9+aK9PX1VYRCoVcApNxa4CX6J4B6AE9t2341V9QwjO8AvAFg27btytxxL9EvAbynJxNRj2VZX58sjMfjd4joyT9D9NiyrHf9iup+/gggBCALQPfxVwARAO8cWywD4LZt2z/5EtWT+vv774rIBIBSlx/mmT5dyWTyC9+WOpkYi8XalVIPRKQbwItEpHv9PRE9tCzrt6IsVcgyhcYLnv1CAkWXfxFBxzEXXXipq+8imz7P9CJdO3+N754y86A+vYFAIDY8PHw58DHzTQB54DNNs3jwMfONY6R4go+Z/YNvbGwsuLKyci74APQys3/wMfMZ8InIPaVUt4g44AuHw/eHhoaKAx8znwEfM6dGR0dviUizaZoXA59pmvtE5ICPmfPAx8wVABzwubXA1VLM7IBPRJ4mEok88DHzKfiY2R/4mPkUfCLSk0gkTsHHzHdE5Immnog8TiQS/sDHzK7gE5EIEZ2CTyl1e2RkxD/4TNO8S0Su4BORZ0qpftM0iwefaZrtAB4QkQM+AA74ADxk5ufgc78CfV99xdy61yMajUbfAvA5gJeKycZj7gqADygajf5xRYIn+6xoUbmCDM9I/LuidXV1qK2txdzcHPb39ZPAOwpmGgqFUFFRgerqauczm81iaWkJa2v64eLhU6+eKqXQ1NTkZOcWq6urWF5edh1zzZSI0NbWhvLyctdFBwcHmJ2dxe7urn/R+vp6J0sd6XQaCwsLqKysRGNjI9bX17G4uIhMRr8jiig/EokgHA7j6OgIU1NTjkBZWRl0f7e2tgo60LX8rq4u/UjC5uamU2ZuBAIBZ1O9mVsLXEU7OztRUlKCnZ0dTE9P54nqfmsnaNHJycm8cVfRlpYW1NTUOJN1pjrjk6iqqkJra6vzNZVKYWZmxp+oLq2jo8NpgQ7dx729PZSWlkKL6hARpwr9Q+aGp/m12Zubm6H9mhtacH5+HhsbG/4tdTJTZ9bQ0OD0LxgMOm7Y3t6GNv55R7XgMS3oH5cJ/y3Rq775V3X5bx8zSv8DuWzoa2vgb5tumbHGlerDAAAAAElFTkSuQmCC","view/settings2.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAA/CAYAAAAPIIPGAAAD2ElEQVRYR+1Xz08bRxT+ZjAGYQoH4rS9IBJBQJEsUC8VKNdQtamUE0fLayqhKHeOaGbFkT8gFVLZtXzk1qqKSs4NUg8RXCIQVoOQ2jRFHHCwBRj2VW+zttY/14BXVaPOyR7NfPN9771536xACEOEgImPDHRhYaHv/Pz8kEMVjUbjq6urxVZhayo/lUo9chzndTabfWMYxkMAGx7QrG3bL5LJ5B0p5f1MJvNz7QENQdPp9LdE9CMAZrcHYAaoxJ8AvARwD8AtAI9t2/7JD9wQdH5+/q7jOLzx04DqeCelnFlbW/s9EJQXGIbxq8eQ//4mhPieiJjlEwBf8qQQYtOyLFZRNeqYJpPJWCQSeUBEzz3JrwqFwvT6+vo575ybm4vGYrFNAF8AICnlbKlU2sxms4Uych2oYRh5AJ9UFggxb1mW5aeSTqfTRLTmm3tv2/bAVUCfWpb1zA9qGAaHwD/XGjQU+WVGHU0Ug4ZSUjXFnwMwXVP8nP1RAPG2i5/Z+q9pKpWaFUL8wvNE9FUmk9m48jWtLWavofztNZTb124oN2neH1mTvmoo/pcfHDGtdZ9nLbw4rrW+nvGZpvlISvl6aWnpjWmaD4nINT4hxKxS6sXy8vIdx3HuK6XaMz6ttWt8QohDInKNTwjhJtWzlJdCiHtEdEtK+VgpFWx8Wuu7RMQbWxofEb0TQsxordszPq11Q+MjoidCCNf4AGxqrYONb2VlJVYsFh84jvPck/yKW5/W2jU+rXWUwdj4OBQcYzbCxcXF5sanlMoLIaqMTylVZXymaVYZHxG9N02zufE1AH2qlKoyPqUUh6AyFwgaivzyVehoorxkdL6k/MUPIEdE0/7i5zcUGx8Rxdsufmbrv6ZKqSrjM01z48rXtLbFeA3FNT4At6/dUIJ7V/MV/6HOn0gkvgbwA4DPbyLZ2/sWwHcikUj82SHAMqe3DMrv+I6Ofw9USonJyUlXzfb2NhzHaamsKdPBwUGcnp7i7OwMAwMDGBsbc4H29vaQz+fR09OD3t5eHB8f1x3QEJQBR0dHcXFx4QL39/dXbTw5OXEBI5EIcrlcHXBDUGYxPj6O7u7uljJLpRJ2d3ddNf7RVD6DlhkWCgUcHrof0YjH44jFYu5vnt/Z2QmWz0lhsHIMi8Wiu/HDF6T7mMDExAT6+vjR8iHGHA5/8uqYTk1Noaurq3L6/v4+jo6OqtgMDQ1hZGSkMnd5eYmtra3K/0DQg4ODivTyLg7B8PBw+6ChyC8f39FEMWgoJRVK8TPbjl/T2mruWEO5SYMNo/P/xaDfeB712U3YeXv/ALDwD+TbY8Dbd9BBAAAAAElFTkSuQmCC","view/setting.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAkCAYAAAC9itu8AAACAklEQVQ4T5XUS4iOcRTH8c9xCeVeiiiXhSJRJFIusRO2lEtZKFlgY6GxZDUrk2TFwii22JJLlERRLKRQJmXBkHIb8+hM/2d6ememed93957n93v+55zf9/mHll9VVTNxopTPR8T3piTyT1VVs7AL9zEd+4roOn5gK25HxLfacAjL8A8TWw6ta28jorc2LMLhIu7Ds2Jah4XlRVci4mNUVTUDadiLFF/G5GL4iyOYjxsYMnQ1BDfxujk0VmJPecFAO4bV2Nk05Bqzz3Za6ut86JJDx2vN4Hbj3hjBbcOt4eCaQZXUj5daT4pGoNFimI1zpdYVEf2jsTQX+5MX5NaOFdFFJHzJ2bWI+FJv6SRWYACTWliqa68ioqc2LMWpwtJ7PCymzVhSWOqOiHeZdPachqNIcXdBJV/2B6cLa5cwZLjQYOkqnuNsOeEM1uJgE43xDBsaH9QQfJ21VNBoHfpBaWHLiKGLoeO1ZnAHkpcxgkvOeoeDa0FjTnNLEfF1PJamYkcR3YmIX6OxNA35Kb7BFKwvoqf4jeV4GRE/azQ2Yh4GMaGFpbr2OSKe1Ibse1MRJ84fimkxMqc0Pc55MrjsOYvZRoofNW6/vPUSwEQ+2+tPQ14h9fX4Ap+aQ2MB1pQTB9sx5K24qmnorKWCRvtDF0PHa+0suBaW0ry91O5mus3n/wHmQwUTIH+tVgAAAABJRU5ErkJggg==","view/refresh.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAkCAYAAAC9itu8AAACiElEQVQ4T4WVS4iPYRTGf4/7/X6XcivXcktEUhTKQkqyYCOKjWyUhezFThbIlJ3LYrIRkoWGUhhhTMko4zJujYRpxgxHj9737/P3zfh239c57/uc5/zO+UQ3T0QsBRYCtZI+5jBVx0fEcGA6MA+YCXQCVyXddWwlISL6ARuARcXvhQPrJF3/nRARvYHtwLRuFLYCFyW15ITl6XTHvwIuJzlrgHrgiqSOiqSI2ANMAL4BxyW1R8RYYKSkp8Vb8w2HgD7AE0kXSozoD0wC2nPCAWAw0CyppiRhBzAD6MgJW4D5KdDFNeSkiJgFbEvONeYE698N2K0ArPsDMAZwguN+AmeKfZgLbAb6llj7A7gk6eFfnY6I0cDKpNc1tQFNwG1JvvFPp0sKXQ2sAGokveuJpVHAHGBJ4ul76vLNapbs9dYk6R8oU7driyztA2Z3w5L1n5LUnBPWptMd/xw4l+RscsHAeeNSZMloTAG+AIcltUXERPdB0qMylk4klu5LOlni2ABgqm3Oko4BQ4Fnko6WJOxPzlXg2wV4hv2czuOYhmsBsDf1rD7fYP0HkyyzZN0twHjACZmlI0WWFgM7e2DprKQ71SyNA9YDBnFYcq0RuOZ5/h9LdsVS6yV97YmlgYDn2X3wjUa7QdKLapY8015ePrWMJVtembhewLI0YWU4eZvck/Q525pXo4M/AY+TLMP40u+SuooseVjsitm/IakzItz5QcXhKSZsBCyrpdjlwuZwfSO8mLOkdYAHqFXSrRKWvErtXFdOcJcnp0AX96ZwuldQ5uxtTrD+VUmWWXqfujwk8eQ4f68rsuRG+d/gZVb9eIk9kPS6miXvIv91rNc12TXPc5MkTyO/AFhJCujHqZlCAAAAAElFTkSuQmCC","comp/checkbox.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAqCAYAAACDdWrxAAABbUlEQVRIS+2TP0gCURzHv88GRYsrExyOo5cEQVEtWdQQpE4N0R+HxmirXXCrKYigqMF2IRqCQByaLJqNIFpyUIzu4rIwpExODy88ITD/cJ603Rsf7/OGz+/zI5TSEAE20cZRgBMySKni8XrbwICrWAwG2ESZIadFS53J0R25brCyHZNud1vbcRuPV7fDAOu9GXJatNSZHN2R6wb/PfJCrxOZCR8Gbk6hWc6Xg8PrcgBETMIVPdIGSjYG/NoOSHcfkLqDK3qsBSRIrgRAuBF1quUPEUPhYGMwb2dhywrqQ3F0Dt++jSokJMBdhmDO52pB2WwFP7OK8rgH9os99IgppNf3QWwMFP4RNHKALrmoflIj53l6CaWpRcBkgiIkYHl6gDTrh5JJg57v/kJ1YOUixw7jfWELxMpAKUmAXAR7tg3LZ7am3IbjKDBOvPiDqkUmcoj+9H1d7k3nmHdweBubB70ON9wRzQH8pVVQb+Q/zZAEfpwDCU4AAAAASUVORK5CYII=","comp/btn_close.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAA8CAYAAAB1odqiAAAE6UlEQVRYR+3Y30+bVRgH8G/T0t/0Jy0USrIsC0E2GMKAVYcRpmSbEzIGZhqyxCxeceGVF3pjvJl/wYyJWZYY4hZBFnBuBBUW2ewYAxlsSMiyLKH8aEt/0vZtSxvM+562We15C6jlxr53zfO8z+ec5z2nOTmCk598tY19fAQs+Hlvz76QX1zpAwd+1NMNXzieU1QtFeKbvn4CXvqgC95wLKegRirC1e8GCPjh+53wMnRwedkG54aLG4yhSI/ycnPawHaKJ5M1MhGuXR8k4MX3OnjBx3NPcLX3DPfepSu3odfrYC4r5X7bVlbhcrnT4kdrjlA7xYLffj9EwJ6udnhCW9TEJ08XUgWTqE6n5XLdbk9G7MjhKmodrbwAfQPDBLxw7h1ecH3dDq/Xm1GYrZqceXIgGo0GJSXFvOCNmz8RsLv9NNyhKO+icTqc8Pl8acDLyWyr1Wo1DEYDbw2dXIz+4TsE7DzbBneQH2SruDZc8Pv9GSiLqVQq6Iv0WVe5TiHG4K1RAnaceguuYCTrCx63G4FAgAoqlUpodbqs7+sVEgyN/ELAs20t2Ajwgz6vF6FgMGtL5QoF1BoNL1qklODW6DgBT518gxcM+P1gQqFdLRqZXA6lSkVFWXDk198I2NZyAs7NMDXR7XRmYBKZjMuNMEzmljHQF46hUIrR8XsEbG228IJ+T/rGFkskkMoVHBgOBRGNRNI2vkpL/5YsODZhJeCbJ47D4WeoM4wyDLai5PsWiCUQJ2aXTN4pnswzqmS4e+8BAZstDbxg1qW3hyALTlinCPh6Uz1C0Rg2w/S/tz3UpaYWSgsgF4twf3IagvOXr297PR5YGuv+bd2s71sfzkCj1ULQe+3u9vraGlg0lw+LlZhMEIzUNu7vmYYFmz/9LJeTS9We+PIymaGl6wLizo2cokJDEawDNxLg+W7EHTkGjUWw/tBPwOMdnYg7nNQZep4/Q2B9jYspS0zQHjyUlrdTPJksNBrwYGiQgE3vtiNup4O2SSuOzk5y7z2ubYKyuBiaAwe5394XzxGw29Pi5iYLdeDCYgMmfxxOgKfPIG53UBNt049SBVNo4g864HRmxMz1x3hAIybv3CZg49ttiK/bqYneFRuCLldGYTY5OfPkQBR6PTRl6cfIVEtLivHw51ECNrS2Ir62zrtKfWtrCHo8acDLyWyrFVot1CYTbw2hqQRTY2MJsLk5K8hW8TkcCPp8GSiHqdVQG41ZtxUHTkwQ8NhrFsRXyUrke3wuF0L+TSooVxVCrc9+iBKWmvDodysB65saEFtZ5cX8Hi+YQDBrS2VKBVRa/jONqKwU05NTBKyrexWxlRUquOnfBBNidrVoZHIZClWF1DqisjLMzPxBwNraasRsdHDD6c7ApDIJVzTMRDJiRQb6EUNkLsPs7DwBa6qrELPZqCNzu/1pG1siEUOhkHK5wWAYkUg0La7T0U9tIrMZc/MLBKw+XImtZTrIMBFEouQkIBEXQJaYXXJ0O8WTeQXlZsw/XSRg1SsVvGDWpbuHIAsu/LlEwMrKCsQDAcQ93j2U2H2qUKuBUKnE4uISBF9f/Hj7wJwVhyordl/hH2Q+W1zCixoLOdNUj98Ei+byYbH5lnPkmJhL6O+18/c0/1m38/c0qVbm72nYVuTvadgu5O9pUtsif0+Tv6dhF8P/657mLz4NfQVdLmZiAAAAAElFTkSuQmCC","comp/textarea.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFsAAAAXCAYAAABkrDOOAAAA4klEQVRoQ+3ZvQrCMBiF4e9rU+sPOErRqxDRe/KG9Fp0EAc3VzuIg1ML4uDmlkaaquDenMUTyJoDD+8W3ZyKlaoshSeogHOy1m1euOmoI1EU+auqQUf/8XHnnBzLp3jsWdaVJEnEGEPsADXU2Ifro8Gej/uSpqnHruvmaVegqirZX+4N9mIy8Nh13XEct7vE18RaK7vzjdiIFoiNUH5vEJvYQAHgFMsmNlAAOMWyiQ0UAE6xbGIDBYBTLJvYQAHgFMsmNlAAOMWyiQ0UAE79lM2fmrDy358a/q6Hhf68ng175QueKdEXxUGVVwAAAABJRU5ErkJggg==","view/re.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAoCAYAAAD6xArmAAACpklEQVRIS+WWPUgcQRiG3+8O70QEUwTB1EJgsTGdRRrhOMjOtEtSRbBIBMFKuCtkZleES2uRQoWQJggKKW7Of7GyTRvBLkVShhS73OXMfWGOU85Es7uXs0m2XeZh+OZ95xnCHX10R1ykBvu+P5fP59+VSqVvf9pUarBS6jWAR0Q0rbWOboP3BCaiOQAHAKTW+vtN8L8BW96W4zjPPM/78Ss8FlypVEYajYbHzALAJIAHALJdoDWl1Esi4m74rWBmpiAI5pk5AHAvJj0VrXU5Fmyhvu+/AfA8YRxfaa1LsWDf92eZeSMJlJnXtdYvEo1Ca30G4GEH/ImI1lqt1nE+nz9vNBrLnVTY39uO4zxNdHgrKytjzWbzs13FzKfDw8PFxcXF8HL3Nscd8BEAN3HcgiCYbLVaHyyIiGaUUm+7R9JzQZRSo0T0BUCGmRd831/tBttK53K5zXK5/DV1pZVSG0Q0C2BXa/0kySEmKojWeoiZD4hoKpvNTiwtLX1MC7+1IFrrQWZeJaJxx3EKN5186lF0LwiC4DEz31dKvU+z69i7Ig0stnm9wv4zsDGm7bxCodBf5xlj2s5j5mkpZf+c1wHPEdFBGIbS87z+OO8S3EnAVhRFvTnv8PBwpF6ve0QkiGiSmX9znuu66ZxXq9XmAcQ6j5krUspkzqvVaqmcJ4SId54xxl6ZiZwHYN113WTOq1arZ0R05TwAa5lM5rher5/ncrllAPYl1HZeFEXJnLe3tzd2cXHRdh6A04GBgWKxWLxyXlcqjqIochPHbWdn58p5AGaEENec13NB9vf3R5vNZtt5RLTguu4159lKA9gUQqR3njHGHpx9tOxKKfvnvGq1OmQrC2AKwIQQon/OOzk5GQzD0I5hPIqi/jvPGNN2npTyH3feTzoJOzgswwlqAAAAAElFTkSuQmCC","view/search.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABUAAAAqCAYAAABcOxDuAAABX0lEQVRIS+3VsUrEQBAG4H9HiDZiJQg+gJVaiKAoWClYXWeZ7D6CtbWFr5Ai2ayQxkLQRgsLGwtBUQsRC6sDCxHxEIvIZSRwxRGSu83pNUe23c0H+89kR2AISwzBxAiinuctCSH2AawD+AFwRkR7QRC85CO0ur5SaoOZzwGM54A3IlrJw1aolPIewEJJUY+01jvde31RKeUMgNceXdLSWk9VQl3XnSWiZhnKzF9RFE1WQrPDUsonAHNFsBDiJAzDRmXUdd1tIjoFMJaDW0KI1TAMH61RpdQ0Mx8z8zMzHxLRAYBlAG0Al2ma7hpjHqxbqgNeAJgHcKW1XutEMeE4Ttv3/axXC1dh9XPgbZqmW8aYd9t3ohCVUt4BWARwkyTJZhzHH7Zgdq4MvQbw7ThOw/f9zypgKVoVsS7UX+C+v+kgeI0Oklrvb0Yw03rwlZW8Hnz14OvqjXrw1e/pPyfwCww91CttlMG7AAAAAElFTkSuQmCC","view/save.png":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAoCAYAAAD6xArmAAAA1klEQVRIS+2VzQ3DIAyFwxwdoMMAA/VQ8ZByyEBhmA7QOVxxKLIaOcIoSZUfrlifHw/wM91Ky6zE7SZgANTaDEDhzYJ5odSMC7nA5U7+b4X2dVQr3ic4hHCTlMcY33xPZUUGcwBvdEJwjcfGGIQQ4rd2qenWA3hyAUuABwCP31NtN+i1v02qP4DicRybM885J2ceB/NCyUupfuLxBS4WbmKF9rNUv4p9gq21d0l5SunF91RWZDAH8EYnBNd4nDPPWitnXst0I6Leez+feVowEQ3e+wNk3ge7C/Qp3GfwkgAAAABJRU5ErkJggg=="}},"base64",function(){return this.base64=new E(t.dataO)}]),t}(),M=(function(){function t(){this.tShowObj=null,this.preValueO={},this.div=null,this.debug_view=null,this.height=300,this.clickedHandler=null,this.fromMe=!1,this._treeDataList=null,this._init()}o(t,"laya.debug.DebugPanel");var e=t.prototype;e.removeNoDisplayKeys=function(e){var i=0;for(i=e.length-1;i>=0;i--)t.noDisplayKeys[e[i]]&&e.splice(i,1)},e.updateShowKeys=function(){t.tObjKeys.length=0,this.tShowObj&&(t.tObjKeys=P.getObjectDisplayAbleKeys(this.tShowObj,t.tObjKeys),this.tShowObj==i.stage&&this.removeNoDisplayKeys(t.tObjKeys),t.tObjKeys.sort(w.sortSmallFirst))},e._init=function(){var e=this;this.div=s.document.createElement("div"),s.document.body.appendChild(this.div),this.clickedHandler=new m(this,this.onClickSelected),this.debug_view=s.window.layaair_debug_view,this.debug_view.initLayaAirDebugView(this.div),this.debug_view.tree.attachEvent("onSelect",function(t){var i;(i=e.getDataByID(t,e._treeDataList[0])).target&&(M.showDisBound(i.target),e.showTargetInfo(i.target))}),this.debug_view.setValueChangeHandler(function(t,i){e.onValueChange(t,i)}),this.debug_view.onRefresh(function(){t.I.setRoot(i.stage)}),this.debug_view.onInspectElement(function(){q.I.beginClickSelect(e.clickedHandler)}),this.debug_view.onLogInfo(function(){console.log(e.tShowObj)}),this.debug_view.onPrintEnabledNodeChain(function(){M.traceDisMouseEnable(e.tShowObj)}),this.debug_view.onPrintSizeChain(function(){M.traceDisSizeChain(e.tShowObj)}),this.debug_view.onToggleVisibility(function(t){e.tShowObj&&(e.tShowObj.visible=e.debug_view.getVisibility())}),this.debug_view.onToggleDebugBorder(function(t){e.tShowObj&&dt.showDisplayBorder(e.tShowObj,e.debug_view.getShowDebugBorder())}),this.debug_view.onToggleShowCurrentCache(function(t){O.showRecacheSprite=e.debug_view.getShowCurrentCache()}),this.debug_view.onToggleShowAllCache(function(t){O.showCacheSprite=e.debug_view.getShowAllCache()}),this.debug_view.onToggleShowAtlas(function(t){console.log("toggle show atlas:",e.debug_view.getShowAtlas()),e.debug_view.getShowAtlas()?z.getInstance().start():z.getInstance().end()}),mt.showToBody(this.div,0,0),i.stage.on("resize",this,this.adptPos),this.adptPos()},e.onClickSelected=function(t){this._treeDataList&&(this.debug_view.tree.selectItem(ft.getObjID(t)),this.debug_view.bounceUpInspectButton())},e.updateLoop=function(){this.tShowObj&&this.showTargetInfo(this.tShowObj)},e.onSelectItem=function(t){var e;e=t.target,this.showTargetInfo(e)},e.onValueChange=function(e,i){if("number"==e.type&&(i=t.mParseFloat(i)),"boolean"==e.type&&(i="true"==i.toString()),this.tShowObj){var n;n=e.key,this.preValueO[n]=this.tShowObj[n]=i}},e.showTargetInfo=function(e){if(e){this.debug_view.setVisibility(e.visible),this.debug_view.setShowDebugBorder(dt.isDisplayShowBorder(e));var i=0,n=0;n=t.tObjKeys.length;var o;if(this.tShowObj==e)for(i=0;i<n;i++)o=t.tObjKeys[i],this.preValueO[o]!=e[o]&&this.debug_view.changeValueByLabel(o,e[o]);else{this.tShowObj=e,this.updateShowKeys();var a;a=t.getObjectData(e),this.debug_view.setContents(a)}for(i=0;i<n;i++)o=t.tObjKeys[i],this.preValueO[o]=e[o]}},e.adptPos=function(){this.fromMe||(this.fromMe=!0,mt.setPos(this.div,0,s.clientHeight-this.height),this.debug_view.resize(s.clientWidth,this.height),t.overlay||i.stage.setScreenSize(s.clientWidth*s.pixelRatio,(s.clientHeight-this.height)*s.pixelRatio),this.fromMe=!1)},e.setRoot=function(e){var n;n=t.getSpriteTreeArr(e),this._treeDataList=[n];var o;(o={}).id=0,o.item=[n],this.debug_view.setTree(o),i.timer.loop(500,this,this.updateLoop)},e.getDataByID=function(t,e){if(!e)return null;if(t==e.id)return e;var i;if(!(i=e.item))return null;var n=0,o=0;o=i.length;var a;for(n=0;n<o;n++)if(a=this.getDataByID(t,i[n]))return a;return null},e.getDataByTarget=function(t,e){if(!e)return null;if(t==e.target)return e;var i;if(!(i=e.item))return null;var n=0,o=0;o=i.length;var a;for(n=0;n<o;n++)if(a=this.getDataByTarget(t,i[n]))return a;return null},t.init=function(e,n){void 0===e&&(e=!0),void 0===n&&(n="#ffffff"),t.I||(t.overlay=!e,j.init(),M.initBasicFunctions(),ct.init(),dt.init(),(t.I=new t).setRoot(i.stage),O.showRecacheSprite=!1,n&&(t.I.div.style.background=n))},t.getSpriteTreeArr=function(e){var i;(i={}).text=""+P.getNodeClassAndName(e),i.target=e,ft.idObj(e),i.id=ft.getObjID(e);var n,o=0,a=0;a=(n=e._childs).length;var l;for(l=[],i.item=l,o=0;o<a;o++)l.push(t.getSpriteTreeArr(n[o]));return i},t.getObjectData=function(e){var i,n,o,a,l;i=[];var s,r=0,h=0;for(h=(s=t.tObjKeys).length,r=0;r<h;r++)l=typeof(a=e[o=s[r]]),"_"!=o.charAt(0)&&t.displayTypes[l]&&((n={}).key=o,n.value=a,n.type=l,i.push(n));return i},t.mParseFloat=function(t){var e=NaN;return e=parseFloat(t),isNaN(e)?0:e},t.I=null,t.overlay=!1,t.ChildrenSign="item",t.LabelSign="text",t.tObjKeys=[],n(t,["displayTypes",function(){return this.displayTypes={boolean:!0,number:!0,string:!0}},"displayKeys",function(){return this.displayKeys=[["x","number"],["y","number"],["width","number"],["width","number"],["width","number"],["width","number"],["width","number"],["width","number"],["width","number"],["width","number"],["width","number"]]},"noDisplayKeys",function(){return this.noDisplayKeys={desginWidth:!0,desginHeight:!0}}])}(),function(){function t(){}return o(t,"laya.debug.DebugTool"),a(1,t,"target",function(){return t._target},function(e){t._target=e}),a(1,t,"isThisShow",function(){return!1}),a(1,t,"showStatu",null,function(e){e?T.show():(T.hide(),t.clearDebugLayer())}),a(1,t,"showBound",function(){return t._showBound},function(e){(t._showBound=e)||t.clearDebugLayer()}),t.getMenuShowEvent=function(){return s.onMobile?"doubleclick":"rightclick"},t.init=function(e,i,n,o,a){void 0===e&&(e=!0),void 0===i&&(i=!0),void 0===n&&(n=!0),void 0===o&&(o=!0),void 0===a&&(a=!1),(t.enableCacheAnalyse=e)&&ct.init(),o&&dt.init(),(t.enableNodeCreateAnalyse=n)&&ht.I.hookClass(V),i&&Jt.init(),O.showCacheSprite=a,jt.init(),t.initBasicFunctions()},t.initBasicFunctions=function(){st.initMe(),t.debugLayer||(Mt.init(),(t.debugLayer=Mt.I.graphicLayer).mouseEnabled=!1,t.debugLayer.mouseThrough=!0,t.showStatu=!0,i.stage.on("keydown",null,t.keyHandler),t.cmdToTypeO[1]="IMAGE",t.cmdToTypeO[2]="ALPHA",t.cmdToTypeO[4]="TRANSFORM",t.cmdToTypeO[16]="CANVAS",t.cmdToTypeO[512]="GRAPHICS",t.cmdToTypeO[1024]="CUSTOM",t.cmdToTypeO[2048]="CHILDS",it.export())},t.dTrace=function(e){null!=t._traceFun&&t._traceFun(e),console.log(e)},t.keyHandler=function(e){var i;if(i=String.fromCharCode(e.keyCode),e.altKey){switch(e.keyCode){case 38:t.showParent();break;case 40:t.showChild();break;case 37:t.showBrother(t.target,1);break;case 39:t.showBrother(t.target,-1)}t.dealCMDKey(i)}},t.dealCMDKey=function(e){switch(e){case"上":t.showParent();break;case"下":t.showChild();break;case"左":t.showBrother(t.target,1);break;case"右":t.showBrother(t.target,-1);break;case"B":t.showAllBrother();break;case"C":t.showAllChild();break;case"E":t.traceDisMouseEnable();break;case"S":t.traceDisSizeChain();break;case"D":lt.downDis(t.target);break;case"U":lt.upDis(t.target);break;case"N":t.getNodeInfo();break;case"M":t.showAllUnderMosue();break;case"I":break;case"O":ue.I.show();break;case"L":at.I.switchType();break;case"Q":t.showNodeInfo();break;case"F":t.showToolPanel();break;case"P":t.showToolFilter();break;case"V":t.selectNodeUnderMouse();break;case"A":re.I.target&&yt.analyseNode(re.I.target);break;case"K":At.traceStage();break;case"T":t.switchNodeTree();break;case"R":pe.I.show();break;case"X":Ee.I.fresh();break;case"mCMD":t.traceCMD();break;case"allCMD":t.traceCMDR()}},t.switchNodeTree=function(){Qt.I.switchShow("Tree")},t.analyseMouseHit=function(){t.target&&yt.analyseNode(t.target)},t.selectNodeUnderMouse=function(){st.instance.selectDisUnderMouse(),t.showDisBound()},t.showToolPanel=function(){Qt.I.switchShow("Find")},t.showToolFilter=function(){Qt.I.switchShow("Filter")},t.showNodeInfo=function(){jt.I.isWorkState?jt.I.recoverNodes():jt.I.showDisInfo(t.target)},t.switchDisController=function(){at.I.target?at.I.target=null:t.target&&(at.I.target=t.target)},t.showParent=function(e){if(e||(e=t.target),!e)return console.log("no targetAvalible"),null;t.target=e.parent,t.autoWork()},t.showChild=function(e){if(e||(e=t.target),!e)return console.log("no targetAvalible"),null;e.numChildren>0&&(t.target=e.getChildAt(0),t.autoWork())},t.showAllChild=function(e){if(e||(e=t.target),!e)return console.log("no targetAvalible"),null;t.selectedNodes=lt.getAllChild(e),t.showSelected()},t.showAllUnderMosue=function(){t.selectedNodes=lt.getObjectsUnderGlobalPoint(i.stage),t.showSelected()},t.showParentChain=function(e){if(e){t.selectedNodes=[];var i;for(i=e.parent;i;)t.selectedNodes.push(i),i=i.parent;t.showSelected()}},t.showAllBrother=function(e){if(e||(e=t.target),!e)return console.log("no targetAvalible"),null;e.parent&&(t.selectedNodes=lt.getAllChild(e.parent),t.showSelected())},t.showBrother=function(e,i){if(void 0===i&&(i=1),e||(e=t.target),!e)return console.log("no targetAvalible"),null;var n;if(n=e.parent){var o=0;o=n.getChildIndex(e),(o+=i)<0&&(o+=n.numChildren),o>=n.numChildren&&(o-=n.numChildren),t.target=n.getChildAt(o),t.autoWork()}},t.clearDebugLayer=function(){t.debugLayer.graphics&&t.debugLayer.graphics.clear()},t.showSelected=function(){if(t.autoShowSelected&&t.selectedNodes&&!(t.selectedNodes.length<1)){console.log("selected:",t.selectedNodes);var e=0,i=0;for(i=t.selectedNodes.length,t.clearDebugLayer(),e=0;e<i;e++)t.showDisBound(t.selectedNodes[e],!1)}},t.getClassCreateInfo=function(t){return It.getRunInfo(t)},t.autoWork=function(){t.isThisShow&&(t.showBound&&t.showDisBound(),t.autoTraceSpriteInfo&&t.target&&Yt.traceSpriteInfo(t.target,t.autoTraceBounds,t.autoTraceSize,t.autoTraceTree),t.target&&(t.autoTraceCMD&&t.traceCMD(),t.autoTraceCMDR&&t.traceCMDR(),t.autoTraceEnable&&t.traceDisMouseEnable(t.target)))},t.traceDisMouseEnable=function(e){if(console.log("----------------traceDisMouseEnable--------------------"),e||(e=t.target),!e)return console.log("no targetAvalible"),null;var i;for(i=[],t.selectedNodes=[];e;)i.push(P.getNodeClassAndName(e)+": mouseEnabled:"+e.mouseEnabled+" hitFirst:"+e.hitTestPrior),t.selectedNodes.push(e),e=e.parent;return console.log(i.join("\n")),t.showSelected(),i.join("\n")},t.traceDisSizeChain=function(e){if(console.log("---------------------traceDisSizeChain-------------------"),e||(e=t.target),!e)return console.log("no targetAvalible"),null;t.selectedNodes=[];var i;for(i=[];e;)i.push(P.getNodeClassAndName(e)+": x:"+e.x+" y:"+e.y+" w:"+e.width+" h:"+e.height+" scaleX:"+e.scaleX+" scaleY:"+e.scaleY),t.selectedNodes.push(e),e=e.parent;return console.log(i.join("\n")),t.showSelected(),i.join("\n")},t.showDisBound=function(e,i,n){if(void 0===i&&(i=!0),void 0===n&&(n="#ff0000"),e||(e=t.target),!e)return console.log("no targetAvalible"),null;i&&t.clearDebugLayer();var o;!(o=e._getBoundPointsM(!0))||o.length<1||(o=p.pListToPointList(o,!0),Nt.walkArr(o,e.localToGlobal,e),o=p.pointListToPlist(o),t._disBoundRec=x._getWrapRec(o,t._disBoundRec),t.debugLayer.graphics.drawRect(t._disBoundRec.x,t._disBoundRec.y,t._disBoundRec.width,t._disBoundRec.height,null,n),Mt.I.setTop())},t.showDisBoundToSprite=function(e,i,n,o){void 0===n&&(n="#ff0000"),void 0===o&&(o=1);var a;!(a=e._getBoundPointsM(!0))||a.length<1||(a=p.pListToPointList(a,!0),Nt.walkArr(a,e.localToGlobal,e),a=p.pointListToPlist(a),t._disBoundRec=x._getWrapRec(a,t._disBoundRec),i.graphics.drawRect(t._disBoundRec.x,t._disBoundRec.y,t._disBoundRec.width,t._disBoundRec.height,null,n,o))},t.getNodeInfo=function(){return t.counter.reset(),Nt.walkTarget(i.stage,t.addNodeInfo),console.log("node info:"),t.counter.traceSelf(),t.counter.data},t.findByClass=function(e){return t._classList=[],t._tFindClass=e,Nt.walkTarget(i.stage,t.addClassNode),t.selectedNodes=t._classList,t.showSelected(),t._classList},t.addClassNode=function(e){e.constructor.name==t._tFindClass&&t._classList.push(e)},t.traceCMD=function(e){if(e||(e=t.target),!e)return console.log("no targetAvalible"),null;console.log("self CMDs:"),console.log(e.graphics.cmds);var i;for(i=L.renders[e._renderType],console.log("renderSprite:",i),t._rSpList.length=0;i&&i._sign>0;)t._rSpList.push(t.cmdToTypeO[i._sign]),i=i._next;return console.log("fun:",t._rSpList.join(",")),t.counter.reset(),t.addCMDs(e.graphics.cmds),t.counter.traceSelf(),t.counter.data},t.addCMDs=function(e){Nt.walkArr(e,t.addCMD)},t.addCMD=function(e){t.counter.add(e.callee)},t.traceCMDR=function(e){return e||(e=t.target),e?(t.counter.reset(),Nt.walkTarget(e,t.getCMdCount),console.log("cmds include children"),t.counter.traceSelf(),t.counter.data):(console.log("no targetAvalible"),0)},t.getCMdCount=function(e){if(!e)return 0;if(!(e instanceof laya.display.Sprite))return 0;if(!e.graphics.cmds)return 0;t.addCMDs(e.graphics.cmds);return e.graphics.cmds.length},t.addNodeInfo=function(e){var i;i=e.constructor.name,t.counter.add(i)},t.find=function(e,n){void 0===n&&(n=!0);var o;return o=t.findTarget(i.stage,e),(t.selectedNodes=o)&&(t.target=t.selectedNodes[0]),n&&t.showSelected(),o},t.findByName=function(e){return t.nameFilter.name=e,t.find(t.nameFilter)},t.findNameStartWith=function(e){return t.nameFilter.name=t.getStartWithFun(e),t.find(t.nameFilter)},t.findNameHas=function(e,i){return void 0===i&&(i=!0),t.nameFilter.name=t.getHasFun(e),t.find(t.nameFilter,i)},t.getStartWithFun=function(t){return function(e){return!!e&&0==e.indexOf(t)}},t.getHasFun=function(t){return function(e){return!!e&&e.indexOf(t)>=0}},t.findTarget=function(e,i){var n=[];t.isFit(e,i)&&n.push(e);var o,a=0,l=0;for(l=e.numChildren,a=0;a<l;a++)(o=e.getChildAt(a))instanceof laya.display.Sprite&&(n=n.concat(t.findTarget(o,i)));return n},t.findClassHas=function(e,i){var n=[];P.getClassName(e).indexOf(i)>=0&&n.push(e);var o,a=0,l=0;for(l=e.numChildren,a=0;a<l;a++)(o=e.getChildAt(a))instanceof laya.display.Sprite&&(n=n.concat(t.findClassHas(o,i)));return n},t.isFit=function(t,e){if(!t)return!1;if(!e)return!0;if("function"==typeof e)return e(t);var i;for(i in e)if("function"==typeof e[i]){if(!e[i](t[i]))return!1}else if(t[i]!=e[i])return!1;return!0},t.log=function(e){var i,n=arguments;i=rt.getArgArr(n),null!=t._logFun&&t._logFun(i.join(" "))},t.enableCacheAnalyse=!1,t.enableNodeCreateAnalyse=!0,t._traceFun=null,t.debugLayer=null,t._target=null,t.selectedNodes=[],t.autoShowSelected=!0,t._showBound=!0,t._disBoundRec=null,t.autoTraceEnable=!1,t.autoTraceBounds=!1,t.autoTraceSize=!1,t.autoTraceTree=!0,t.autoTraceCMD=!0,t.autoTraceCMDR=!1,t.autoTraceSpriteInfo=!0,t._classList=null,t._tFindClass=null,t._rSpList=[],t._logFun=null,n(t,["text",function(){return this.text=new T},"cmdToTypeO",function(){return this.cmdToTypeO={}},"counter",function(){return this.counter=new tt},"nameFilter",function(){return this.nameFilter={name:"name"}}]),t}()),j=function(){function t(){}return o(t,"laya.debug.divui.DivScripts"),t.init=function(){var e;e=_.decodeToByte(t.data).readUTFBytes(),i._runScript(e)},t.data="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",t}(),z=function(){function t(){this.mSprite=null,this.mIndex=0,this.mTextureDic={}}o(t,"laya.debug.tools.AtlasTools");var e=t.prototype;return e.start=function(){Y.isWebGL&&(null==this.mSprite&&(this.mSprite=new k),i.stage.addChild(this.mSprite),this.showNext())},e.end=function(){Y.isWebGL&&this.mSprite&&i.stage.removeChild(this.mSprite)},e.showNext=function(){if(Y.isWebGL){null==this.mSprite&&(this.mSprite=new k),i.stage.addChild(this.mSprite),this.mIndex++;var t,e=(t=laya.webgl.atlas.AtlasResourceManager.instance).getAtlaserCount();this.mIndex>=e&&(this.mIndex=0);var n;if(this.mTextureDic[this.mIndex])n=this.mTextureDic[this.mIndex];else{var o=t.getAtlaserByIndex(this.mIndex);o&&o.texture&&(n=new B(o.texture,null),this.mTextureDic[this.mIndex]=n)}n&&(this.mSprite.graphics.clear(),this.mSprite.graphics.save(),this.mSprite.graphics.alpha(.9),this.mSprite.graphics.drawRect(0,0,1024,1024,"#efefefe"),this.mSprite.graphics.restore(),this.mSprite.graphics.drawTexture(n,0,0,1024,1024),this.mSprite.graphics.fillText((this.mIndex+1).toString()+"/"+e.toString(),25,100,"40px Arial","#ff0000","left"))}},t.getInstance=function(){return t.mInstance=t.mInstance||new t},t.mInstance=null,t}(),E=function(){function t(t,e){this.data=null,this.replaceO=null,this.idKey=null,this._loadedHandler=null,this.data=t,e||(e=Math.random()+"key"),this.idKey=e,this.init()}o(t,"laya.debug.tools.Base64Atlas");var e=t.prototype;return e.init=function(){this.replaceO={};var t;for(t in this.data)this.replaceO[t]=this.idKey+"/"+t},e.getAdptUrl=function(t){return this.replaceO[t]},e.preLoad=function(t){this._loadedHandler=t,i.loader.load(Q.getPreloads(this.data),new m(this,this.preloadEnd))},e.preloadEnd=function(){var t;for(t in this.data){var e;e=i.loader.getRes(this.data[t]),G.cacheRes(this.replaceO[t],e)}this._loadedHandler&&this._loadedHandler.run()},e.replaceRes=function(t){Gt.replaceValue(t,this.replaceO)},t}(),Q=function(){function t(){}return o(t,"laya.debug.tools.Base64ImageTool"),t.getCanvasPic=function(t){t=t.bitmap;var e=s.createElement("canvas"),i=e.getContext("2d");return e.height=t.height,e.width=t.width,i.drawImage(t.source,0,0),e},t.getBase64Pic=function(e){return t.getCanvasPic(e).toDataURL("image/png")},t.getPreloads=function(t){var e;e=[];var i;for(i in t)e.push({url:t[i],type:"image"});return e},t}(),_=function(){function t(){}return o(t,"laya.debug.tools.Base64Tool"),t.init=function(){if(!t.lookup){t.lookup=new Uint8Array(256);for(var e=0;e<t.chars.length;e++)t.lookup[t.chars.charCodeAt(e)]=e}},t.encode=function(e){var i=new Uint8Array(e),n=0,o=i.length,a="";for(n=0;n<o;n+=3)a+=t.chars[i[n]>>2],a+=t.chars[(3&i[n])<<4|i[n+1]>>4],a+=t.chars[(15&i[n+1])<<2|i[n+2]>>6],a+=t.chars[63&i[n+2]];return o%3==2?a=a.substring(0,a.length-1)+"=":o%3==1&&(a=a.substring(0,a.length-2)+"=="),a},t.encodeStr=function(e){var i;return(i=new h).writeUTFString(e),t.encodeByte(i)},t.encodeStr2=function(e){var i;return(i=new h).writeUTFBytes(e),t.encodeByte(i)},t.encodeByte=function(e,i,n){return void 0===i&&(i=0),void 0===n&&(n=-1),n<0&&(n=e.length),t.encode(e.buffer.slice(i,n))},t.decodeToByte=function(e){return new h(t.decode(e))},t.decode=function(e){t.init();var i=.75*e.length,n=e.length,o=0,a=0,l=0,s=0,r=0,h=0;"="===e[e.length-1]&&(i--,"="===e[e.length-2]&&i--);var c=new ArrayBuffer(i),u=new Uint8Array(c);for(o=0;o<n;o+=4)l=t.lookup[e.charCodeAt(o)],s=t.lookup[e.charCodeAt(o+1)],r=t.lookup[e.charCodeAt(o+2)],h=t.lookup[e.charCodeAt(o+3)],u[a++]=l<<2|s>>4,u[a++]=(15&s)<<4|r>>2,u[a++]=(3&r)<<6|63&h;return c},t.chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t.lookup=null,t}(),O=function(){function t(){}o(t,"laya.debug.tools.CacheAnalyser");var e=t.prototype;return e.renderCanvas=function(e,i){void 0===i&&(i=0),t.showCacheSprite&&(Mt.I.isDebugItem(e)||M.showDisBoundToSprite(e,Mt.I.cacheViewLayer,et.CANVAS_REC_COLOR,4))},e.reCacheCanvas=function(e,i){if(void 0===i&&(i=0),t.showRecacheSprite&&!Mt.I.isDebugItem(e)){var n;(n=t.getNodeInfoByNode(e)).addCount(i),t.counter.addTime(e,i),n.parent||Mt.I.nodeRecInfoLayer.addChild(n)}},t.renderLoopBegin=function(){Mt.I.cacheViewLayer.graphics.clear()},t.getNodeInfoByNode=function(e){ft.idObj(e);var i=0;return i=ft.getObjID(e),t._nodeInfoDic[i]||(t._nodeInfoDic[i]=new qt),t._nodeInfoDic[i].setTarget(e),t._nodeInfoDic[i]},t._nodeInfoDic={},t.showCacheSprite=!1,t.showRecacheSprite=!0,n(t,["counter",function(){return this.counter=new Wt},"I",function(){return this.I=new t}]),t}(),K=(function(){function t(){this._getHandler=null,this._indexHandler=null,this._pool=null,this._laters=null}o(t,"laya.debug.tools.CallLaterTool");t.prototype.callLater=function(e,i,n){null==this._getHandler(e,i)&&(t.oldCallLater.call(this,e,i,n),t._isRecording&&t._recordedCallLaters.push(this._laters[this._laters.length-1]))},t.initCallLaterRecorder=function(){t.oldCallLater||(t.oldCallLater=i.timer.callLater,i.timer.callLater=t.prototype.callLater)},t.beginRecordCallLater=function(){t.initCallLaterRecorder(),t._isRecording=!0},t.runRecordedCallLaters=function(){t._isRecording=!1;for(var e,n=(e=i.timer)._laters,o=0,a=(n=t._recordedCallLaters).length-1;o<=a;o++){var l=n[o];t._recordedCallLaters.indexOf(l)<0||(null!==l.method&&l.run(!1),e._recoverHandler(l),n.splice(o,1))}t._recordedCallLaters.length=0},t._recordedCallLaters=[],t._isRecording=!1,t.oldCallLater=null}(),function(){function t(){}return o(t,"laya.debug.tools.CanvasTools"),t.createCanvas=function(t,e){var i=new f("2D");return i.getContext("2d"),i.size(t,e),i},t.renderSpriteToCanvas=function(t,e,i,n){L.renders[t._renderType]._fun(t,e.context,i,n)},t.getImageDataFromCanvas=function(t,e,i,n,o){void 0===e&&(e=0),void 0===i&&(i=0),void 0===n&&(n=0),void 0===o&&(o=0),n<=0&&(n=t.width),o<=0&&(o=t.height);return t.context.getImageData(e,i,n,o)},t.getImageDataFromCanvasByRec=function(t,e){return t.context.getImageData(e.x,e.y,e.width,e.height)},t.getDifferCount=function(e,i){var n=e.data,o=i.data,a=0;return a=0,t.walkImageData(e,function(e,i,l,s){t.isPoinSame(l,n,o)||a++}),a},t.getDifferRate=function(e,i){return t.getDifferCount(e,i)/(e.width*e.height)},t.getCanvasDisRec=function(e){var i;i=new x;var n,o=0,a=0,l=0,s=0;o=l=0,a=(n=t.getImageDataFromCanvas(e,0,0)).width,s=n.height;var r=0,h=0,c=0,u=0;h=n.width,u=n.height;var d;d=n.data;var b=0;for(c=0;c<u;c++)for(r=0;r<h;r++)t.isEmptyPoint(d,b)||(a>r&&(a=r),o<r&&(o=r),s>c&&(s=c),l<c&&(l=c)),b+=4;return i.setTo(a,s,o-a+1,l-s+1),i},t.fillCanvasRec=function(t,e,i){var n=t.context;n.fillStyle=i,n.fillRect(e.x,e.y,e.width,e.height)},t.isEmptyPoint=function(t,e){return 0==t[e]&&0==t[e+1]&&0==t[e+2]&&0==t[e+3]},t.isPoinSame=function(t,e,i){return e[t]==i[t]&&e[t+1]==i[t+1]&&e[t+2]==i[t+2]&&e[t+3]==i[t+3]},t.walkImageData=function(t,e){var i=0,n=0,o=0,a=0;n=t.width,a=t.height;var l=0,s=t.data;for(i=0;i<n;i++)for(o=0;o<a;o++)e(i,o,l,s),l+=4},t.getSpriteByCanvas=function(t){var e;return(e=new k).graphics.drawTexture(new B(t),0,0,t.width,t.height),e},t.renderSpritesToCanvas=function(e,i,n,o,a){void 0===n&&(n=0),void 0===o&&(o=0),void 0===a&&(a=0);var l=0,s=0;for(s=i.length,l=a;l<s;l++)t.renderSpriteToCanvas(i[l],e,n,o)},t.clearCanvas=function(t){var e=NaN,i=NaN;e=t.width,i=t.height,t.size(e+1,i),t.size(e,i)},t.getImagePixels=function(t,e,i,n,o){void 0===o&&(o=4);var a=0;a=(t*i+e)*o;var l,s=0,r=0;for(l=[],r=o,s=0;s<r;s++)l.push(n[a+s]);return l},t}()),P=function(){function t(){}return o(t,"laya.debug.tools.ClassTool"),t.defineProperty=function(t,e,i){Object.defineProperty(t,e,i)},t.getOwnPropertyDescriptor=function(t,e){return Object.getOwnPropertyDescriptor(t,e)},t.getOwnPropertyDescriptors=function(t){return Object.getOwnPropertyDescriptors(t)},t.getOwnPropertyNames=function(t){return Object.getOwnPropertyNames(t)},t.getObjectGetSetKeys=function(e,i){i||(i=[]);var n;n=laya.debug.tools.ClassTool.getOwnPropertyNames(e);var o;for(o in n)(o=n[o]).indexOf("_$get_")>=0&&(o=o.replace("_$get_",""),i.push(o));return e.__proto__&&t.getObjectGetSetKeys(e.__proto__,i),i},t.getObjectDisplayAbleKeys=function(e,i){i||(i=[]);var n;for(n in e)typeof e[n],"_"!=n.charAt(0)&&i.push(n);return t.getObjectGetSetKeys(e,i),i=Gt.getNoSameArr(i)},t.getClassName=function(t){return"function"==typeof t?t.name:t.constructor.name},t.getNodeClassAndName=function(e){if(!e)return"null";return e.name?t.getClassName(e)+"("+e.name+")":t.getClassName(e)},t.getClassNameByClz=function(t){return t.name},t.getClassByName=function(t){return i._runScript(t)},t.createObjByName=function(e){return new(t.getClassByName(e))},n(t,["displayTypes",function(){return this.displayTypes={boolean:!0,number:!0,string:!0}}]),t}(),q=function(){function t(){this.completeHandler=null,this.tSelectTar=null,this._selectTip=new k,this._selectTip.setBounds(new x(0,0,0,0)),Ft.listen("ItemClicked",this,this.itemClicked)}o(t,"laya.debug.tools.ClickSelectTool");var e=t.prototype;return e.beginClickSelect=function(e){this.completeHandler=e,t.isClickSelectState=!0,this.clickSelectChange()},e.clickSelectChange=function(){s.onPC&&(this.tSelectTar=null,this.clearSelectTip(),t.isClickSelectState?i.timer.loop(200,this,this.updateSelectTar,null,!0):i.timer.clear(this,this.updateSelectTar))},e.clearSelectTip=function(){this._selectTip.removeSelf()},e.updateSelectTar=function(){if(this.clearSelectTip(),this.tSelectTar=st.instance.getDisUnderMouse(),this.tSelectTar&&!Mt.I.isDebugItem(this.tSelectTar)){var t;(t=this._selectTip.graphics).clear();var e;e=At.getGRec(this.tSelectTar),Mt.I.popLayer.addChild(this._selectTip),t.drawRect(0,0,e.width,e.height,null,et.CLICK_SELECT_COLOR,2),this._selectTip.pos(e.x,e.y)}},e.itemClicked=function(e){t.isClickSelectState&&(t.ignoreDebugTool&&Mt.I.isDebugItem(e)||e instanceof laya.debug.uicomps.ContextMenuItem||e.parent instanceof laya.debug.uicomps.ContextMenuItem||(M.showDisBound(e),this.completeHandler&&this.completeHandler.runWith(e),t.isClickSelectState=!1,this.clickSelectChange()))},a(1,t,"I",function(){return t._I||(t._I=new t),t._I}),t._I=null,t.isClickSelectState=!1,t.ignoreDebugTool=!1,t}(),$=function(){function t(){this.red=NaN,this.green=NaN,this.blue=NaN}return o(t,"laya.debug.tools.ColorTool"),t.toHexColor=function(t){return H.toHexColor(t)},t.getRGBByRGBStr=function(t){"#"==t.charAt(0)&&(t=t.substr(1));var e=parseInt(t,16);t.length;return[(16711680&e)>>16,(65280&e)>>8,255&e]},t.getColorBit=function(t){var e;return e=Math.floor(t).toString(16),e=e.length>1?e:"0"+e},t.getRGBStr=function(e){return"#"+t.getColorBit(e[0])+t.getColorBit(e[1])+t.getColorBit(e[2])},t.traseHSB=function(t){console.log("hsb:",t[0],t[1],t[2])},t.rgb2hsb=function(t,e,i){var n=[t,e,i];n.sort(Zt.sortNumSmallFirst);var o=n[2],a=n[0],l=0;return o==a?l=1:0==t&&0==e&&0==i||(o==t&&e>=i?l=60*(e-i)/(o-a)+0:o==t&&e<i?l=60*(e-i)/(o-a)+360:o==e?l=60*(i-t)/(o-a)+120:o==i&&(l=60*(t-e)/(o-a)+240)),[l,0==o?0:(o-a)/o,o/255]},t.hsb2rgb=function(t,e,i){var n=0,o=0,a=0,l=Math.floor(t/60%6),s=t/60-l,r=i*(1-e),h=i*(1-s*e),c=i*(1-(1-s)*e);switch(l){case 0:n=i,o=c,a=r;break;case 1:n=h,o=i,a=r;break;case 2:n=r,o=i,a=c;break;case 3:n=r,o=h,a=i;break;case 4:n=c,o=r,a=i;break;case 5:n=i,o=r,a=h}return[Math.floor(255*n),Math.floor(255*o),Math.floor(255*a)]},t}(),tt=(function(){function t(){}o(t,"laya.debug.tools.CommonTools"),t.bind=function(t,e){return t.bind(e)},t.insertP=function(e,i,n,o,a,l){var s;s=new k,e.parent.addChild(s),s.x=i,s.y=n,s.scaleX=o,s.scaleY=a,s.rotation=l,s.addChild(e),t.count++,s.name="insertP:"+t.count},t.insertChild=function(t,e,i,n,o,a,l){void 0===l&&(l="#ff00ff");var s;return s=new k,t.addChild(s),s.x=e,s.y=i,s.scaleX=n,s.scaleY=o,s.rotation=a,s.graphics.drawRect(0,0,20,20,l),s.name="child:"+t.numChildren,s},t.createSprite=function(t,e,i){void 0===i&&(i="#ff0000");var n;return(n=new k).graphics.drawRect(0,0,t,e,i),n.size(t,e),n},t.createBtn=function(t,e,i){void 0===e&&(e=100),void 0===i&&(i=40);var n;return(n=new k).size(e,i),n.graphics.drawRect(0,0,n.width,n.height,"#ff0000"),n.graphics.fillText(t,.5*n.width,.5*n.height,null,"#ffff00","center"),n},t.count=0}(),function(){function t(){this.data={},this.preO={},this.changeO={},this.count=0}o(t,"laya.debug.tools.CountTool");var e=t.prototype;return e.reset=function(){this.data={},this.count=0},e.add=function(t,e){void 0===e&&(e=1),this.count++,this.data.hasOwnProperty(t)||(this.data[t]=0),this.data[t]=this.data[t]+e},e.getKeyCount=function(t){return this.data.hasOwnProperty(t)||(this.data[t]=0),this.data[t]},e.getKeyChange=function(t){return this.changeO[t]?this.changeO[t]:0},e.record=function(){var t;for(t in this.changeO)this.changeO[t]=0;for(t in this.data)this.preO[t]||(this.preO[t]=0),this.changeO[t]=this.data[t]-this.preO[t],this.preO[t]=this.data[t]},e.getCount=function(t){var e,i=0;for(e in t)i+=t[e];return i},e.traceSelf=function(t){t||(t=this.data);var e=0;return e=this.getCount(t),console.log("total:"+e),"total:"+e+"\n"+Yt.traceObj(t)},e.traceSelfR=function(t){t||(t=this.data);var e=0;return e=this.getCount(t),console.log("total:"+e),"total:"+e+"\n"+Yt.traceObjR(t)},t}()),et=function(){function t(){}return o(t,"laya.debug.tools.DebugConsts"),t.CLICK_SELECT_COLOR="#ff0000",t.CANVAS_REC_COLOR="#FF00FF",t.RECACHE_REC_COLOR="#00ff00",t.SPRITE_REC_COLOR="#ff0000",t.SPRITE_REC_LINEWIDTH=2,t}(),it=function(){function e(){}return o(e,"laya.debug.tools.DebugExport"),e.export=function(){var i;i=t;var n;for(n in e._exportsDic)i[n]=e._exportsDic[n]},n(e,["_exportsDic",function(){return this._exportsDic={DebugTool:M,Watcher:St}}]),e}(),nt=function(){function t(){}return o(t,"laya.debug.tools.DebugTxt"),t.init=function(){t._txt||((t._txt=new A).pos(100,100),t._txt.color="#ff00ff",t._txt.zOrder=999,t._txt.fontSize=24,t._txt.text="debugTxt inited",i.stage.addChild(t._txt))},t.getArgArr=function(t){var e;e=[];var i=0,n=t.length;for(i=0;i<n;i++)e.push(t[i]);return e},t.dTrace=function(e){var i,n=arguments;i=(n=t.getArgArr(n)).join(" "),t._txt&&(t._txt.text=i+"\n"+t._txt.text)},t.getTimeStr=function(){return(new Date).toTimeString()},t.traceTime=function(e){t.dTrace(t.getTimeStr()),t.dTrace(e)},t.show=function(e){var i,n=arguments;i=(n=t.getArgArr(n)).join(" "),t._txt&&(t._txt.text=i)},t._txt=null,t.I=null,t}(),ot=function(){function t(t,e){this.autoTrace=!0,this.sign="",this.obj=null,void 0===t&&(t=""),void 0===e&&(e=!0),this.sign=t,this.autoTrace=e}o(t,"laya.debug.tools.DifferTool");return t.prototype.update=function(t,e){e&&console.log(e);var i=Gt.copyObj(t);this.obj||(this.obj={});var n;return n=Gt.differ(this.obj,i),this.obj=i,this.autoTrace&&(console.log(this.sign+" differ:"),Gt.traceDifferObj(n)),n},t.differ=function(e,i,n){t._differO[e]||(t._differO[e]=new t(e,!0));return t._differO[e].update(i,n)},t._differO={},t}(),at=function(){function t(){this.arrowAxis=null,this._target=null,this.recInfo=null,t.init(),this.arrowAxis=new Ut,this.arrowAxis.mouseEnabled=!0}o(t,"laya.debug.tools.DisController");var e=t.prototype;return e.switchType=function(){this.arrowAxis.switchType()},e.updateMe=function(){this._target&&(this.recInfo=vt.getGlobalRecInfo(this._target,0,0,1,0,0,1),console.log("rotation:",this.recInfo.rotation),console.log("pos:",this.recInfo.x,this.recInfo.y),console.log("scale:",this.recInfo.width,this.recInfo.height),this.arrowAxis.x=this.recInfo.x,this.arrowAxis.y=this.recInfo.y,this.arrowAxis.rotation=this.recInfo.rotation,this.arrowAxis.yAxis.rotation=this.recInfo.rotationV-this.recInfo.rotation)},a(0,e,"target",function(){return this._target},function(e){this._target=e,e?(t._container.addChild(this.arrowAxis),i.timer.loop(100,this,this.updateMe)):(this.arrowAxis.removeSelf(),i.timer.clear(this,this.updateMe)),this.arrowAxis.target=e,this.updateMe()}),a(0,e,"type",function(){return this.arrowAxis.type},function(t){this.arrowAxis.type=t}),t.init=function(){t._container?lt.setTop(t._container):((t._container=new k).mouseEnabled=!0,i.stage.addChild(t._container))},t._container=null,n(t,["I",function(){return this.I=new t}]),t}(),lt=function(){function t(){}return o(t,"laya.debug.tools.DisControlTool"),t.getObjectsUnderPoint=function(e,i,n,o,a){if(o=o||[],null!=a&&!a(e))return o;if(e.getBounds().contains(i,n)){o.push(e);var l=new X;l.setTo(i,n),i=(l=e.fromParentPoint(l)).x,n=l.y;for(var s=e._childs.length-1;s>-1;s--){var r=e._childs[s];r instanceof laya.display.Sprite&&t.getObjectsUnderPoint(r,i,n,o,a)}}return o},t.getObjectsUnderGlobalPoint=function(e,n){var o=new X;return o.setTo(i.stage.mouseX,i.stage.mouseY),e.parent&&(o=e.parent.globalToLocal(o)),t.getObjectsUnderPoint(e,o.x,o.y,null,n)},t.findFirstObjectsUnderGlobalPoint=function(){var e;if(!(e=t.getObjectsUnderGlobalPoint(i.stage)))return null;var n,o=0;for(o=e.length-1;o>=0;o--)if((n=e[o])&&n.numChildren<1)return n;return n},t.visibleAndEnableObjFun=function(t){return t.visible&&t.mouseEnabled},t.visibleObjFun=function(t){return t.visible},t.getMousePoint=function(t){var e=new X;return e.setTo(i.stage.mouseX,i.stage.mouseY),e=t.globalToLocal(e)},t.isChildE=function(t,e){if(!t)return!1;for(;e;){if(e.parent==t)return!0;e=e.parent}return!1},t.isInTree=function(e,i){return e==i||t.isChildE(e,i)},t.setTop=function(t){if(t&&t.parent){var e;(e=t.parent).setChildIndex(t,e.numChildren-1)}},t.clearItemRelativeInfo=function(t){t.getLayout().left="NaN",t.getLayout().right="NaN",t.getLayout().top="NaN",t.getLayout().bottom="NaN"},t.swap=function(t,e){if(t!=e){var i=0;i=t.parent.getChildIndex(t);var n=0;n=e.parent.getChildIndex(e);var o;o=e.parent,t.parent.addChildAt(e,i),o.addChildAt(t,n)}},t.insertToTarParent=function(e,i,n){void 0===n&&(n=!1);var o,a=0;e&&(o=e.parent)&&(a=o.getChildIndex(e),n&&a++,t.insertToParent(o,i,a))},t.insertToParent=function(e,i,n){if(void 0===n&&(n=-1),e){n<0&&(n=e.numChildren);var o=0,a=0;for(a=i.length,o=0;o<a;o++)t.transParent(i[o],e),e.addChildAt(i[o],n)}},t.transParent=function(t,e){if(t&&e&&t.parent){var i;i=t.parent;var n;n=new X(t.x,t.y),n=i.localToGlobal(n),n=e.globalToLocal(n),t.pos(n.x,n.y)}},t.transPoint=function(t,e,i){return i=t.localToGlobal(i),i=e.globalToLocal(i)},t.removeItems=function(t){var e=0,i=0;for(i=t.length,e=0;e<i;e++)t[e].removeSelf()},t.addItems=function(t,e){var i=0,n=0;for(n=t.length,i=0;i<n;i++)e.addChild(t[i])},t.getAllChild=function(t){if(!t)return[];var e=0,i=0,n=[];for(i=t.numChildren,e=0;e<i;e++)n.push(t.getChildAt(e));return n},t.upDis=function(t){if(t&&t.parent){var e,i=0;(i=(e=t.parent).getChildIndex(t)+1)>=e.numChildren&&(i=e.numChildren-1),console.log("setChildIndex:"+i),e.setChildIndex(t,i)}},t.downDis=function(t){if(t&&t.parent){var e,i=0;(i=(e=t.parent).getChildIndex(t)-1)<0&&(i=0),console.log("setChildIndex:"+i),e.setChildIndex(t,i)}},t.setResizeAbleEx=function(t){var e;(e=t.getChildByName("resizeBtn"))&&Vt.setResizeAble(e,t)},t.setResizeAble=function(e){e.on("click",null,t.resizeHandler,[e])},t.resizeHandler=function(t){Rt.setUp(t)},t.setDragingItem=function(e,i){e.on("mousedown",null,t.dragingHandler,[i]),i.on("dragend",null,t.dragingEnd,[i])},t.dragingHandler=function(t){t&&t.startDrag()},t.dragingEnd=function(e){t.intFyDisPos(e),console.log(e.x,e.y)},t.showToStage=function(e,n,o){void 0===n&&(n=0),void 0===o&&(o=0);var a=e.getBounds();e.x=i.stage.mouseX+n,e.y=i.stage.mouseY+o,e.x+a.width>i.stage.width&&(e.x-=a.width+n),e.y+a.height>i.stage.height&&(e.y-=a.height+o),t.intFyDisPos(e)},t.intFyDisPos=function(t){t&&(t.x=Math.round(t.x),t.y=Math.round(t.y))},t.showOnly=function(t,e){var i=0,n=0;for(n=t.length,i=0;i<n;i++)t[i].visible=t[i]==e},t.showOnlyByIndex=function(e,i){t.showOnly(e,e[i])},t.addOnly=function(t,e,i){var n=0,o=0;for(o=t.length,n=0;n<o;n++)t[n]!=e?t[n].removeSelf():i.addChild(t[n])},t.addOnlyByIndex=function(e,i,n){t.addOnly(e,e[i],n)},n(t,["tempP",function(){return this.tempP=new X}]),t}(),st=(function(){function t(){this.tar=null,this.rec=new k,this.rootContainer=new k}o(t,"laya.debug.tools.DisEditor");var e=t.prototype;e.setTarget=function(t){this.tar=t;var e;(e=this.rec.graphics).clear();var n;n=this.tar.getSelfBounds(),e.drawRect(n.x,n.y,n.width,n.height,null,"#00ff00"),this.createSameDisChain(),i.stage.addChild(this.rootContainer)},e.createSameDisChain=function(){var t,e,n;for(n=this.rec,t=this.tar;t&&t!=i.stage;)(e=new k).addChild(n),e.x=t.x,e.y=t.y,e.scaleX=t.scaleX,e.scaleY=t.scaleY,e.rotation=t.rotation,e.scrollRect=t.scrollRect,n=e,t=t.parent;this.rootContainer.removeChildren(),this.rootContainer.addChild(n)}}(),function(){function t(){this.mouseX=NaN,this.mouseY=NaN,this._stage=null,this._target=null,this.isGetting=!1,this._matrix=new R,this._point=new X,this._rect=new x,this._event=d.EMPTY,this._stage=i.stage,this.init(Y.context.canvas)}o(t,"laya.debug.tools.DisplayHook");var e=t.prototype;return e.init=function(e){var n=this;s.window.navigator.msPointerEnabled&&(e.style["-ms-content-zooming"]="none",e.style["-ms-touch-action"]="none");var o=this;s.document.addEventListener("mousedown",function(e){n._event._stoped=!1,t.isFirst=!0,o.check(o._stage,e.offsetX,e.offsetY,o.onMouseDown,!0,!1)},!0),s.document.addEventListener("touchstart",function(e){n._event._stoped=!1,t.isFirst=!0;for(var a=e.changedTouches,l=0,s=a.length;l<s;l++){!function(t,e){o._event._stoped=!1,o._event.nativeEvent=e||t,o._target=null,t.offsetX?(o.mouseX=t.offsetX,o.mouseY=t.offsetY):(o.mouseX=t.clientX-i.stage.offset.x,o.mouseY=t.clientY-i.stage.offset.y)}(a[l],e),o.check(o._stage,o.mouseX,o.mouseY,o.onMouseDown,!0,!1)}},!0)},e.onMouseMove=function(t,e){this.sendEvent(t,"mousemove")},e.onMouseUp=function(t,e){e&&this.sendEvent(t,"mouseup")},e.onMouseDown=function(t,e){e&&(t.$_MOUSEDOWN=!0,this.sendEvent(t,"mousedown"))},e.sendEvent=function(t,e){this._event._stoped||(t.event(e,this._event.setTo(e,t,t)),"mouseup"===e&&t.$_MOUSEDOWN&&(t.$_MOUSEDOWN=!1,t.event("click",this._event.setTo("click",t,t))))},e.selectDisUnderMouse=function(){t.isFirst=!0,this.check(i.stage,i.stage.mouseX,i.stage.mouseY,null,!0,!1),me.I.setSelectTarget(M.target)},e.getDisUnderMouse=function(){return this.isGetting=!0,t.isFirst=!0,M.target=null,this.check(i.stage,i.stage.mouseX,i.stage.mouseY,null,!0,!1),this.isGetting=!1,M.target},e.check=function(e,i,n,o,a,l){if(e==M.debugLayer)return!1;if(e==Mt.I)return!1;if(this.isGetting&&e==Mt.I)return!1;if(!e.visible||e.getSelfBounds().width<=0)return!1;var s=!1;if(!0){var r=!1;if(a&&(this._rect=e.getBounds(),s=this._rect.contains(i,n),this._point.setTo(i,n),e.fromParentPoint(this._point),i=this._point.x,n=this._point.y),s){for(var h=!1,c=e._childs.length-1;c>-1;c--){var u=e._childs[c];if(h=this.check(u,i,n,o,a,!0))break}r=e.getGraphicBounds().contains(i,n),(s=h||r)&&!h&&t.isFirst&&(t.isFirst=!1,e instanceof laya.debug.tools.debugUI.DButton||(M.target=e,this.isGetting||(M.autoWork(),Ft.notify("ItemClicked",e))))}}return s},t.initMe=function(){t.instance||(t.instance=new t)},t.ITEM_CLICKED="ItemClicked",t.instance=null,t.isFirst=!1,t}()),rt=(function(){function t(){}o(t,"laya.debug.tools.DisPool"),t.getDis=function(e){var i;i=P.getClassNameByClz(e),t._objDic[i]||(t._objDic[i]=[]);var n,o=0,a=0;for(a=(n=t._objDic[i]).length,o=0;o<a;o++)if(!n[o].parent)return n[o];return n.push(new e),n[n.length-1]},t._objDic={}}(),function(){function t(){}return o(t,"laya.debug.tools.DTrace"),t.getArgArr=function(t){var e;e=[];var i=0,n=t.length;for(i=0;i<n;i++)e.push(t[i]);return e},t.dTrace=function(e){var i=arguments;(i=t.getArgArr(i)).push(Yt.getCallLoc(2)),console.log.apply(console,i);i.join(" ")},t.timeStart=function(t){console.time(t)},t.timeEnd=function(t){console.timeEnd(t)},t.traceTable=function(t){console.table(t)},t}()),ht=function(){function t(){this.createInfo={}}o(t,"laya.debug.tools.enginehook.ClassCreateHook");var e=t.prototype;return e.hookClass=function(e){var i=this;if(!t.isInited){t.isInited=!0;bt.hook(e,"call",function(t){i.classCreated(t,e)})}},e.classCreated=function(e,i){var n;n=P.getNodeClassAndName(e);var o,a=0;for(o=e;o&&o!=i;)o=o.__super,a++;t.I.createInfo[n]||(t.I.createInfo[n]=0),t.I.createInfo[n]=t.I.createInfo[n]+1,It.run(n,a+6)},e.getClassCreateInfo=function(t){var e;return e=P.getClassName(t),It.getRunInfo(e)},t.isInited=!1,n(t,["I",function(){return this.I=new t}]),t}(),ct=(function(){function t(){}o(t,"laya.debug.tools.enginehook.FunctionTimeHook"),t.hookFun=function(e,i){if(e&&!e.timeHooked){var n;t.HookID++,n=P.getNodeClassAndName(e)+"."+i+"():"+t.HookID;e.timeHooked=!0,bt.hook(e,i,function(e){t.funBegin(n)},function(e){t.funEnd(n)})}},t.funBegin=function(e){t.funPre[e]=s.now()},t.funEnd=function(e){t.funPre[e]||(t.funPre[e]=0),t.counter.add(e,s.now()-t.funPre[e])},t.fresh=function(){t.funEnd("TotalSign"),t.counter.record(),t.funBegin("TotalSign")},t.HookID=1,t.funPre={},t.TotalSign="TotalSign",n(t,["counter",function(){return this.counter=new tt}])}(),function(){function t(){}o(t,"laya.debug.tools.enginehook.RenderSpriteHook");var e=t.prototype;return e.createRenderSprite=function(e,i){var n;return n=t._preCreateFun(e,i),16==e&&(n._oldCanvas=n._fun,n._fun=t.I._canvas),n},e._canvas=function(t,e,i,n){if(ut.allowRendering){var o=t._$P.cacheCanvas,a=this._next;if(o&&!ut.isVisibleTesting){var l;l=s.now();var r=o.ctx,h=t._needRepaint()||!r;this._oldCanvas(t,e,i,n),u.showCanvasMark,h?O.I.reCacheCanvas(t,s.now()-l):O.I.renderCanvas(t,s.now()-l)}else a._fun.call(a,t,e,i,n)}},t.init=function(){t.I||(t.I=new t,t._preCreateFun=S.createRenderSprite,S.createRenderSprite=t.I.createRenderSprite)},t.IMAGE=1,t.FILTERS=2,t.ALPHA=4,t.TRANSFORM=8,t.CANVAS=16,t.BLEND=32,t.CLIP=64,t.STYLE=128,t.GRAPHICS=256,t.CUSTOM=512,t.ENABLERENDERMERGE=1024,t.CHILDS=2048,t.INIT=69905,t.renders=[],t.I=null,t._preCreateFun=null,t}()),ut=function(){function t(){this._repaint=1,this._renderType=1,this._x=0,this._y=0,this.target=null,this.isTargetRenderd=!1,this.preFun=null,this._next=null,this.pgraphic=L.prototype._graphics,this.pimage=L.prototype._image,this.pimage2=L.prototype._image2}o(t,"laya.debug.tools.enginehook.SpriteRenderForVisibleAnalyse");var e=t.prototype;return e.setRenderHook=function(){k.prototype.render=t.I.render},e.render=function(e,i,n){var o;if(o=this,!Mt.I.isDebugItem(o))if(o==laya.debug.tools.enginehook.SpriteRenderForVisibleAnalyse.I.target&&(laya.debug.tools.enginehook.SpriteRenderForVisibleAnalyse.allowRendering=!0,laya.debug.tools.enginehook.SpriteRenderForVisibleAnalyse.I.isTargetRenderd=!0,K.clearCanvas(t.mainCanvas)),L.renders[this._renderType]._fun(this,e,i+this._x,n+this._y),o==laya.debug.tools.enginehook.SpriteRenderForVisibleAnalyse.I.target)t.tarRec=K.getCanvasDisRec(laya.debug.tools.enginehook.SpriteRenderForVisibleAnalyse.mainCanvas),console.log("rec",t.tarRec.toString()),t.tarRec.width>0&&t.tarRec.height>0?(t.isTarRecOK=!0,t.preImageData=K.getImageDataFromCanvasByRec(t.mainCanvas,t.tarRec),t.tarImageData=K.getImageDataFromCanvasByRec(t.mainCanvas,t.tarRec)):console.log("tarRec Not OK:",t.tarRec);else if(t.isTarRecOK){t.tImageData=K.getImageDataFromCanvasByRec(t.mainCanvas,t.tarRec);var a=NaN;a=K.getDifferRate(t.preImageData,t.tImageData),t.preImageData=t.tImageData,a>0&&Lt.addCoverNode(o,a)}},e.analyseNode=function(e){Lt.resetCoverList(),k.prototype.render!=t.I.render&&(this.preFun=k.prototype.render),this.target=e,k.prototype.render=this.render,t.tarCanvas||(t.tarCanvas=K.createCanvas(i.stage.width,i.stage.height)),t.mainCanvas||(t.mainCanvas=K.createCanvas(i.stage.width,i.stage.height)),this.isTargetRenderd=!1,t.isVisibleTesting=!0,t.allowRendering=!1,K.clearCanvas(t.mainCanvas),K.clearCanvas(t.tarCanvas),t.isTarRecOK=!1;var n=new C(t.mainCanvas.width,t.mainCanvas.height,t.mainCanvas);t.mainCanvas=n.canvas,this.render.call(i.stage,n,0,0),t.coverRate=t.isTarRecOK?K.getDifferRate(t.preImageData,t.tarImageData):0,Lt.coverRate=t.coverRate,Lt.isTarRecOK=t.isTarRecOK,console.log("coverRate:",t.coverRate),this.isTargetRenderd=!1,t.isVisibleTesting=!1,t.allowRendering=!0,k.prototype.render=this.preFun},e.noRenderMode=function(){},e.normalMode=function(){L.prototype._graphics=this.pgraphic,L.prototype._image=this.pimage,L.prototype._image2=this.pimage2},e.inits=function(){this.noRenderMode()},e.m_graphics=function(t,e,i,n){if(laya.debug.tools.enginehook.SpriteRenderForVisibleAnalyse.allowRendering){var o=t._style._tf;t._graphics&&t._graphics._render(t,e,i-o.translateX,n-o.translateY)}var a=this._next;a._fun.call(a,t,e,i,n)},e.m_image=function(t,e,i,n){if(laya.debug.tools.enginehook.SpriteRenderForVisibleAnalyse.allowRendering){var o=t._style;e.ctx.drawTexture2(i,n,o._tf.translateX,o._tf.translateY,t.transform,o.alpha,o.blendMode,t._graphics._one)}},e.m_image2=function(t,e,i,n){if(laya.debug.tools.enginehook.SpriteRenderForVisibleAnalyse.allowRendering){var o=t._style._tf;e.ctx.drawTexture2(i,n,o.translateX,o.translateY,t.transform,1,null,t._graphics._one)}},t.tarCanvas=null,t.mainCanvas=null,t.preImageData=null,t.tImageData=null,t.tarImageData=null,t.tarRec=null,t.isTarRecOK=!1,t.isVisibleTesting=!1,t.allowRendering=!0,t.coverRate=NaN,n(t,["I",function(){return this.I=new t}]),t}(),dt=function(){function t(){this._repaint=1,this._renderType=1,this._x=0,this._y=0}o(t,"laya.debug.tools.enginehook.SpriteRenderHook");return t.prototype.render=function(t,e,n){this==i.stage&&O.renderLoopBegin();var o=0;o=s.now(),T.spriteCount++,this.ShowBorderSign&&M.showDisBoundToSprite(this,Mt.I.cacheViewLayer,et.SPRITE_REC_COLOR,et.SPRITE_REC_LINEWIDTH),L.renders[this._renderType]._fun(this,t,e+this._x,n+this._y),this._repaint=0,wt.I.render(this,s.now()-o)},t.init=function(){t.I||(t.I=new t,t.setRenderHook())},t.setRenderHook=function(){k.prototype.render=t.I.render},t.showDisplayBorder=function(t,e){void 0===e&&(e=!0),t.ShowBorderSign=e},t.isDisplayShowBorder=function(t){return t.ShowBorderSign},t.I=null,t.ShowBorderSign="ShowBorderSign",t}(),bt=(function(){function t(){}o(t,"laya.debug.tools.exp.Observer"),t.observe=function(t,e){Object.observe(t,e)},t.unobserve=function(t,e){Object.unobserve(t,e)},t.observeDiffer=function(e,i,n){void 0===n&&(n="obDiffer");t.observe(e,function(){ot.differ(i,e,n)})}}(),function(){function t(){}o(t,"laya.debug.tools.exp.Watch"),t.watch=function(t,e,i){t.watch(e,i)},t.unwatch=function(t,e,i){t.unwatch(e,i)}}(),function(){function t(){}o(t,"laya.debug.tools.FilterTool"),t.getArrByFilter=function(t,e){var i=0,n=t.length,o=[];for(i=0;i<n;i++)e(t[i])&&o.push(t[i]);return o},t.getArr=function(t,e,i){var n=0,o=t.length,a=[];for(n=0;n<o;n++)t[n][e]==i&&a.push(t[n]);return a}}(),function(){function t(){}o(t,"laya.debug.tools.GetSetProfile"),t.removeNoDisplayKeys=function(e){var i=0;for(i=e.length-1;i>=0;i--)t.noDisplayKeys[e[i]]&&e.splice(i,1)},t.getClassCount=function(e){return t.countDic[e]},t.addClassCount=function(e){t.countDic[e]?t.countDic[e]=t.countDic[e]+1:t.countDic[e]=1},t.init=function(){if(!t._inited){t._inited=!0;bt.hook(V,"call",null,function(e){t.classCreated(e)}),(t.handlerO={}).get=function(t,e,i){return console.log("get",t,e,i),Reflect.get(t,e,i)},t.handlerO.set=function(t,e,i,n){return console.log("set",t,e,i,n),Reflect.set(t,e,i,n)}}},t.classCreated=function(e,i){if(!t.fromMe){var n;n=P.getClassName(e),t.addClassCount(n),t.addClassCount("ALL"),ft.idObj(e);var o;((o=t.hookClassDic[n])||(t.profileClass(e.constructor),o=t.hookClassDic[n]))&&t.hookObj2(e,o)}},t.hookObj=function(e,i){var n=t.handlerO;new Proxy(e,n)},t.hookObj2=function(e,i){var n=0,o=0;for(o=i.length,n=0;n<o;n++)t.hookVar(e,i[n])},t.profileClass=function(e){var i;i=P.getClassName(e),t.fromMe=!0;var n=new e;t.fromMe=!1;var o;o=P.getObjectDisplayAbleKeys(n);var a=0;for(a=(o=Gt.getNoSameArr(o)).length-1;a>=0;a--)"function"==typeof n[o[a]]&&o.splice(a,1);o.length,t.removeNoDisplayKeys(o),t.hookClassDic[i]=o},t.hookPrototype=function(e,i){console.log("hook:",i);try{t.hookVar(e,i)}catch(t){console.log("fail",i)}},t.reportCall=function(e,i,n){ft.idObj(e);var o=0;o=ft.getObjID(e);var a;a=P.getClassName(e),t.recordInfo(a,i,n,o),t.recordInfo("ALL",i,n,o)},t.recordInfo=function(e,i,n,o){var a;t.infoDic[e]||(t.infoDic[e]={});var l;(a=t.infoDic[e])[i]||(a[i]={});var s;(l=a[i])[n]||(l[n]={}),(s=l[n])[o]?s[o]=s[o]+1:(s[o]=1,s.objCount?s.objCount=s.objCount+1:s.objCount=1),s.count?s.count=s.count+1:s.count=1},t.showInfo=function(){var e;e={};var i;i={};var n;n=[];var o;o=[];var a,l,s;for(a in t.infoDic){var r,h;r=t.infoDic[a],e[a]=h={};for(l in r){var c,u;c=r[l],h[l]=u={};for(s in c){var d;(d=c[s]).rate=d.objCount/t.getClassCount(a),u[s]=d.rate;var b;i[b=a+"_"+l+"_"+s]=d.rate,"ALL"==a&&("get"==s?n.push([b,d.rate,d.count]):o.push([b,d.rate,d.count]))}}}console.log(t.infoDic),console.log(t.countDic),console.log(e),console.log(i),console.log("nodeCount:",t.getClassCount("ALL")),console.log("sort by rate"),t.showStaticInfo(n,o,"1"),console.log("sort by count"),t.showStaticInfo(n,o,"2")},t.showStaticInfo=function(e,i,n){console.log("get:"),t.showStaticArray(e,n),console.log("set:"),t.showStaticArray(i,n)},t.showStaticArray=function(t,e){void 0===e&&(e="1"),t.sort(w.sortByKey(e,!0,!0));var i=0,n=0;n=t.length;var o;for(i=0;i<n;i++)o=t[i],console.log(o[0],Math.floor(100*o[1]),o[2])},t.hookVar=function(e,i,n,o){n||(n=[]),o||(o=[]);var a,l,s=e;l=P.getOwnPropertyDescriptor(e,i);var r={},h=function(t){a=t},c=function(){return a};for(o.push(function(){return t.reportCall(this,i,"get"),a}),n.push(function(e){t.reportCall(this,i,"set")});!l&&e.__proto__;)e=e.__proto__,l=P.getOwnPropertyDescriptor(e,i);l&&(r.set=l.set?l.set:h,r.get=l.get?l.get:c,l.get||(a=s[i]),r.enumerable=l.enumerable,n.push(r.set),o.push(r.get),bt.hookFuns(r,"set",n),bt.hookFuns(r,"get",o,o.length-1),P.defineProperty(s,i,r)),l||(r.set=h,r.get=c,a=s[i],n.push(r.set),o.push(r.get),bt.hookFuns(r,"set",n),bt.hookFuns(r,"get",o,o.length-1),P.defineProperty(s,i,r))},t._inited=!1,t.handlerO=null,t.ALL="ALL",t.countDic={},t.fromMe=!1,t.hookClassDic={},t.infoDic={},n(t,["noDisplayKeys",function(){return this.noDisplayKeys={conchModel:!0}}])}(),function(){function t(){}return o(t,"laya.debug.tools.hook.FunHook"),t.hook=function(e,i,n,o){t.hookFuns(e,i,[n,e[i],o],1)},t.hookAllFun=function(e){var i,n;n=P.getOwnPropertyNames(e);for(i in n)i=n[i],t.special[i]||(console.log("try hook:",i),"function"==typeof e[i]&&(console.log("hook:",i),t.hookFuns(e,i,[t.getTraceMsg("call:"+i),e[i]],1)));e.__proto__?t.hookAllFun(e.__proto__):console.log("end:",e)},t.getTraceMsg=function(t){return function(){console.log(t)}},t.hookFuns=function(t,e,i,n){void 0===n&&(n=-1);var o,a=t[e];(o=function(t){var e,o=arguments,a=0,l=0;for(l=i.length,a=0;a<l;a++)i[a]&&(a==n?e=i[a].apply(this,o):i[a].apply(this,o));return e}).pre=a,t[e]=o},t.removeHook=function(t,e){null!=t[e].pre&&(t[e]=t[e].pre)},t.debugHere=function(){},t.traceLoc=function(t,e){void 0===t&&(t=0),void 0===e&&(e=""),console.log(e,"fun loc:",Yt.getCallLoc(3+t))},t.getLocFun=function(e,i){void 0===e&&(e=0),void 0===i&&(i=""),e+=1;return function(){t.traceLoc(e,i)}},n(t,["special",function(){return this.special={length:!0,name:!0,arguments:!0,caller:!0,prototype:!0,is:!0,isExtensible:!0,isFrozen:!0,isSealed:!0,preventExtensions:!0,seal:!0,unobserve:!0,apply:!0,call:!0,bind:!0,freeze:!0,unobserve:!0}}]),t}()),pt=function(){function t(){}return o(t,"laya.debug.tools.hook.VarHook"),t.hookVar=function(t,e,i,n){i||(i=[]),n||(n=[]);var o,a=t,l=t[e],s={},r=function(t){console.log("var hook set "+e+":",t),l=t},h=function(){return console.log("var hook get"+e+":",l),l};if(o=P.getOwnPropertyDescriptor(t,e))return s.set=r,s.get=h,s.enumerable=o.enumerable,i.push(s.set),n.push(s.get),bt.hookFuns(s,"set",i),bt.hookFuns(s,"get",n,n.length-1),void P.defineProperty(t,e,s);for(;!o&&t.__proto__;)t=t.__proto__,o=P.getOwnPropertyDescriptor(t,e);o&&(s.set=o.set?o.set:r,s.get=o.get?o.get:h,s.enumerable=o.enumerable,i.push(s.set),n.push(s.get),bt.hookFuns(s,"set",i),bt.hookFuns(s,"get",n,n.length-1),P.defineProperty(a,e,s)),o||(console.log("get des fail add directly"),s.set=r,s.get=h,i.push(s.set),n.push(s.get),bt.hookFuns(s,"set",i),bt.hookFuns(s,"get",n,n.length-1),P.defineProperty(t,e,s))},t.getLocFun=function(t,e){void 0===t&&(t=""),void 0===e&&(e=0),e+=1;return function(){bt.traceLoc(e,t)}},t}(),ft=function(){function t(){this.tID=1}o(t,"laya.debug.tools.IDTools");return t.prototype.getID=function(){return this.tID++},t.getAID=function(){return t._ID.getID()},t.idObjE=function(e,i){return void 0===i&&(i="default"),e._M_id_?e:(i||(i="default"),t._idDic[i]||(t._idDic[i]=new t),e._M_id_=t._idDic[i].getAID(),e)},t.setObjID=function(t,e){return t._M_id_=e,t},t.idObj=function(e){return e._M_id_?e:(e._M_id_=t.getAID(),e)},t.getObjID=function(t){return t?t._M_id_:-1},t.idSign="_M_id_",n(t,["_ID",function(){return this._ID=new t},"_idDic",function(){return this._idDic={default:new t}}]),t}(),mt=(function(){function t(){}o(t,"laya.debug.tools.JsonTool"),t.getJsonString=function(e,i,n,o,a){void 0===i&&(i=!0),void 0===n&&(n="\n"),void 0===o&&(o=0),void 0===a&&(a=4);var l="";l=t.getEmptyStr(o*a);var s;s={};var r,h,c;c=[];for(r in e)c.push(r),h=e[r],t.singleLineKey[r]?s[r]=t.getValueStr(h,!0,n,o+1,a):s[r]=t.getValueStr(h,i,n,o+1,a);var u=0,d=0;d=c.length,c.sort(),c=c.reverse();var b;b=t.getEmptyStr((o+1)*a),i&&(n="",l="",b="");var p;for(p=[],u=0;u<d;u++)r=c[u],p.push(b+t.wrapValue(r)+":"+s[r]);return"{"+n+p.join(","+n)+n+l+"}"},t.wrapValue=function(t,e){return void 0===e&&(e='"'),e+t+e},t.getArrStr=function(e,i,n,o,a){void 0===i&&(i=!0),void 0===n&&(n="\n"),void 0===o&&(o=0),void 0===a&&(a=4);var l=0,s=0;s=e.length;var r;for(r=[],l=0;l<s;l++)r.push(t.getValueStr(e[l],i,n,o+1,a));var h="";return h=t.getEmptyStr((o+1)*a),i&&(n="",h=""),"["+n+h+r.join(","+n+h)+"]"},t.quote=function(e){return t.escapable.lastIndex=0,t.escapable.test(e)?'"'+e.replace(t.escapable,function(e){var i=t.meta[e];return"string"==typeof i?i:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'},t.getValueStr=function(e,i,n,o,a){void 0===i&&(i=!0),void 0===n&&(n="\n"),void 0===o&&(o=0),void 0===a&&(a=0);return"string"==typeof e?t.quote(e):null==e?"null":"number"==typeof e||"number"==typeof e&&Math.floor(e)==e||"boolean"==typeof e?e:e instanceof Array?t.getArrStr(e,i,n,o,a):"object"==typeof e?t.getJsonString(e,i,n,o,a):e},t.getEmptyStr=function(e){if(!t.emptyDic.hasOwnProperty(e)){var i=0,n=0;n=e;var o;for(o="",i=0;i<n;i++)o+=" ";t.emptyDic[e]=o}return t.emptyDic[e]},t.emptyDic={},n(t,["singleLineKey",function(){return this.singleLineKey={props:!0}},"escapable",function(){return this.escapable=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g},"meta",function(){return this.meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"}}])}(),function(){function t(){}return o(t,"laya.debug.tools.JSTools"),t.showToBody=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=0),s.document.body.appendChild(t);var n;(n=t.style).position="absolute",n.top=i+"px",n.left=e+"px"},t.showToParent=function(t,e,i,n){void 0===e&&(e=0),void 0===i&&(i=0),n.appendChild(t);var o;(o=t.style).position="absolute",o.top=i+"px",o.left=e+"px"},t.addToBody=function(t){s.document.body.appendChild(t)},t.setPos=function(t,e,i){var n;(n=t.style).top=i+"px",n.left=e+"px"},t.setSize=function(t,e,i){var n;(n=t.style).width=e+"px",n.height=i+"px"},t.setTransform=function(t,e){var i;(i=t.style).transformOrigin=i.webkitTransformOrigin=i.msTransformOrigin=i.mozTransformOrigin=i.oTransformOrigin="0px 0px 0px",i.transform=i.webkitTransform=i.msTransform=i.mozTransform=i.oTransform="matrix("+e.toString()+")"},t.noMouseEvent=function(t){t.style["pointer-events"]="none"},t.setMouseEnable=function(t,e){t.style["pointer-events"]=e?"auto":"none"},t.setZIndex=function(t,e){t.style["z-index"]=e},t.showAboveSprite=function(e,n,o,a){void 0===o&&(o=0),void 0===a&&(a=0);var l;l=new X,(l=n.localToGlobal(l)).x+=o,l.y+=a,l.x+=i.stage.offset.x,l.y+=i.stage.offset.y,t.showToBody(e,l.x,l.y)},t.removeElement=function(t){s.removeElement(t)},t.isElementInDom=function(t){return t&&t.parentNode},t.getImageSpriteByFile=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=0);var n;(n=new FileReader).readAsDataURL(t);var o;return o=new k,n.onload=function(t){var a;(a=new B).load(n.result),o.graphics.drawTexture(a,0,0,e,i)},o},t.getPixelRatio=function(){if(t._pixelRatio>0)return t._pixelRatio;var e=s.createElement("canvas").getContext("2d"),i=(s.window.devicePixelRatio||1)/(e.webkitBackingStorePixelRatio||e.mozBackingStorePixelRatio||e.msBackingStorePixelRatio||e.oBackingStorePixelRatio||e.backingStorePixelRatio||1);return console.log("pixelRatioc:",i),t._pixelRatio=i,i},t._pixelRatio=-1,t}()),gt=function(){function t(){this.data=null,this._items=null,this.layoutFun=null,this._sX=0,this._width=0}o(t,"laya.debug.tools.layout.Layouter");var e=t.prototype;return e.layout=function(){this.layoutFun(this._width,this._items,this.data,this._sX)},e.changed=function(){i.timer.callLater(this,this.layout)},e.calSize=function(){var t,e=0,i=0;t=this.items[0],this._sX=t.x;var n=NaN;for(n=this._sX+t.width,i=this.items.length,e=1;e<i;e++)t=this.items[e],this._sX>t.x&&(this._sX=t.x),n<t.x+t.width&&(n=t.x+t.width);this._width=n-this._sX},a(0,e,"width",function(){return this._width},function(t){this._width=t,this.changed()}),a(0,e,"x",function(){return this._sX},function(t){this._sX=t,this.changed()}),a(0,e,"items",function(){return this._items},function(t){this._items=t,this.calSize()}),t}(),Zt=(function(){function t(){}o(t,"laya.debug.tools.layout.LayoutFuns"),t.sameWidth=function(t,e,i,n){void 0===n&&(n=0);var o=0;i&&i.dWidth&&(o=i.dWidth);var a=NaN;a=(t-(e.length-1)*o)/e.length;var l,s=0,r=0,h=NaN;for(h=n,r=e.length,s=0;s<r;s++)(l=e[s]).x=h,l.width=a,h+=o+a},t.getSameWidthLayout=function(e,i){var n;return n={},n.dWidth=i,t.getLayouter(e,n,laya.debug.tools.layout.LayoutFuns.sameWidth)},t.getLayouter=function(t,e,i){var n;return n=new gt,n.items=t,n.data=e,n.layoutFun=i,n},t.sameDis=function(e,i,n,o){void 0===o&&(o=0);var a=NaN;a=e;var l,s=0,r=0;for(r=i.length,t.prepareForLayoutWidth(e,i),s=0;s<r;s++)a-=(l=i[s]).width;i.length>1&&(a/=i.length-1);var h=NaN;for(h=o,r=i.length,s=0;s<r;s++)(l=i[s]).x=h,h+=a+l.width},t.getSameDisLayout=function(e,i){void 0===i&&(i=!1);var n;if(n={},i){var o=0,a=0;a=e.length;var l,s=NaN;for(s=0,o=0;o<a;o++)s+=(l=e[o]).width;for(s=l.x+l.width,o=0;o<a;o++)l=e[o],t.setItemRate(l,l.width/s)}return t.getLayouter(e,n,laya.debug.tools.layout.LayoutFuns.sameDis)},t.fullFill=function(t,e,i,n){void 0===n&&(n=0);var o=0,a=0;i&&(i.dL&&(o=i.dL),i.dR&&(a=i.dR));var l,s=0,r=0;for(r=e.length,s=0;s<r;s++)(l=e[s]).x=n+o,l.width=t-o-a},t.getFullFillLayout=function(e,i,n){void 0===i&&(i=0),void 0===n&&(n=0);var o;return o={},o.dL=i,o.dR=n,t.getLayouter(e,o,laya.debug.tools.layout.LayoutFuns.fullFill)},t.fixPos=function(t,e,i,n){void 0===n&&(n=0);var o=0,a=[],l=!1;i&&(i.dLen&&(o=i.dLen),i.poss&&(a=i.poss),i.isRate&&(l=i.isRate));var s,r=0,h=0;h=a.length;var c,u=NaN;for(c=null,r=0;r<h;r++)s=e[r],u=n+a[r],l&&(u=n+a[r]*t),s.x=u,c&&(c.width=s.x-o-c.x),c=s;var d;(d=e[e.length-1]).width=n+t-o-d.x},t.getFixPos=function(e,i,n,o){void 0===i&&(i=0),void 0===n&&(n=!1);var a,l,s=0,r=0,h=NaN,c=NaN;if(h=(l=t.getLayouter(e,a={},t.fixPos)).x,c=l.width,!o){o=[],r=e.length;var u=NaN;for(s=0;s<r;s++)u=e[s].x-h,n&&(u/=c),o.push(u)}return a.dLen=i,a.poss=o,a.isRate=n,l},t.clearItemsRelativeInfo=function(e){var i=0,n=0;for(n=e.length,i=0;i<n;i++)t.clearItemRelativeInfo(e[i])},t.clearItemRelativeInfo=function(t){t.getLayout().left="NaN",t.getLayout().right="NaN"},t.prepareForLayoutWidth=function(e,i){var n=0,o=0;for(o=i.length,n=0;n<o;n++)t.prepareItemForLayoutWidth(e,i[n])},t.getSumWidth=function(t){var e=NaN;e=0;var i=0,n=0;for(n=t.length,i=0;i<n;i++)e+=t[i].width;return e},t.prepareItemForLayoutWidth=function(e,i){t.getItemRate(i)>0&&(i.width=e*t.getItemRate(i))},t.setItemRate=function(t,e){t.layoutRate=e},t.getItemRate=function(t){return t.layoutRate?t.layoutRate:-1},t.setItemFreeSize=function(t,e){void 0===e&&(e=!0),t.layoutFreeSize=e},t.isItemFreeSize=function(t){return t.layoutFreeSize},t.lockedDis=function(e,i,n,o){void 0===o&&(o=0);var a;a=n.dists;var l=NaN;l=n.sumDis;var s,r,h=0,c=0;t.prepareForLayoutWidth(e,i);var u=NaN;u=e-l-t.getSumWidth(i);var d;for((d=t.getFreeItem(i))&&(d.width+=u),(r=i[0]).x=o,c=i.length,h=1;h<c;h++)(s=i[h]).x=r.x+r.width+a[h-1],r=s},t.getFreeItem=function(e){var i=0,n=0;for(n=e.length,i=0;i<n;i++)if(t.isItemFreeSize(e[i]))return e[i];return null},t.getLockedDis=function(e){var i;i={};var n,o,a,l=0,s=0,r=NaN;r=0;var h=NaN;for(a=e[0],n=[],s=e.length,l=1;l<s;l++)h=(o=e[l]).x-a.x-a.width,n.push(h),r+=h,a=o;return i.dists=n,i.sumDis=r,t.getLayouter(e,i,laya.debug.tools.layout.LayoutFuns.lockedDis)},t.RateSign="layoutRate",t.FreeSizeSign="layoutFreeSize"}(),function(){function t(){}o(t,"laya.debug.tools.LayoutTools"),t.layoutToXCount=function(t,e,i,n,o,a){void 0===e&&(e=1),void 0===i&&(i=0),void 0===n&&(n=0),void 0===o&&(o=0),void 0===a&&(a=0);var l,s=NaN,r=NaN,h=0,c=0,u=0,d=0;for(u=0,d=0,s=o,r=a,c=t.length,h=0;h<c;h++)(l=t[h]).x=s,l.y=r,l.height>d&&(d=l.height),++u>=e?(u%=e,l.y+=d+n,d=0):s+=l.width+i},t.layoutToWidth=function(t,e,i,n,o,a){var l,s=NaN,r=NaN,h=0,c=0;for(s=o,r=a,c=t.length,h=0;h<c;h++)s+(l=t[h]).width+i>e&&(s=o,r+=n+l.height),l.x=s,l.y=r,s+=i+l.width}}(),function(){function t(){}return o(t,"laya.debug.tools.MathTools"),t.sortBigFirst=function(t,e){return t==e?0:e>t?1:-1},t.sortSmallFirst=function(t,e){return t==e?0:e>t?-1:1},t.sortNumBigFirst=function(t,e){return parseFloat(e)-parseFloat(t)},t.sortNumSmallFirst=function(t,e){return parseFloat(t)-parseFloat(e)},t.sortByKey=function(e,i,n){void 0===i&&(i=!1),void 0===n&&(n=!0);var o;return o=i?n?t.sortNumBigFirst:t.sortBigFirst:n?t.sortNumSmallFirst:t.sortSmallFirst,function(t,i){return o(t[e],i[e])}},t}()),yt=function(){function t(){}return o(t,"laya.debug.tools.MouseEventAnalyser"),t.analyseNode=function(e){M.showDisBound(e,!0);var n;n=e,Gt.clearObj(t.infoO),Gt.clearObj(t.nodeO),Gt.clearObj(t.hitO);var o;for(o=[];e;)ft.idObj(e),t.nodeO[ft.getObjID(e)]=e,o.push(e),e=e.parent;t.check(i.stage,i.stage.mouseX,i.stage.mouseY,null);var a;t.hitO[ft.getObjID(n)]?(console.log("can hit"),a="can hit"):(console.log("can't hit"),a="can't hit");var l=0,s=0;s=(o=o.reverse()).length;var r;for(r=["[分析对象]:"+P.getNodeClassAndName(n)+":"+a],l=0;l<s;l++)e=o[l],t.hitO[ft.getObjID(e)]?(console.log("can hit:",P.getNodeClassAndName(e)),console.log("原因:",t.infoO[ft.getObjID(e)]),r.push("can hit: "+P.getNodeClassAndName(e)),r.push("原因: "+t.infoO[ft.getObjID(e)])):(console.log("can't hit:"+P.getNodeClassAndName(e)),console.log("原因:",t.infoO[ft.getObjID(e)]?t.infoO[ft.getObjID(e)]:"鼠标事件在父级已停止派发"),r.push("can't hit: "+P.getNodeClassAndName(e)),r.push("原因: "+(t.infoO[ft.getObjID(e)]?t.infoO[ft.getObjID(e)]:"鼠标事件在父级已停止派发")));var h;h=r.join("\n"),Qt.I.showTxtInfo(h)},t.check=function(e,i,n,o){ft.idObj(e);var a=!1;a=t.nodeO[ft.getObjID(e)],t._point.setTo(i,n),e.fromParentPoint(t._point),i=t._point.x,n=t._point.y;var l=e.scrollRect;if(l){t._rect.setTo(l.x,l.y,l.width,l.height);var s=t._rect.contains(i,n);if(!s)return a&&(t.infoO[ft.getObjID(e)]="scrollRect没有包含鼠标"+t._rect.toString()+":"+i+","+n),!1}var r,h=0,c=0;c=(r=e._childs).length;var u,d;for(d=null,h=0;h<c;h++)if(u=r[h],ft.idObj(u),t.nodeO[ft.getObjID(u)]){d=u;break}var b=!1;b=!!d;if(e.hitTestPrior&&!e.mouseThrough&&!t.hitTest(e,i,n))return t.infoO[ft.getObjID(e)]="hitTestPrior=true，宽高区域不包含鼠标::"+i+","+n+" size:"+e.width+","+e.height,!1;for(h=e._childs.length-1;h>-1;h--)if((u=e._childs[h])==d&&(d.mouseEnabled||(t.infoO[ft.getObjID(d)]="mouseEnabled=false"),d.visible||(t.infoO[ft.getObjID(d)]="visible=false"),b=!1),u.mouseEnabled&&u.visible){if(t.check(u,i,n,o))return t.hitO[ft.getObjID(e)]=!0,t.infoO[ft.getObjID(e)]="子对象被击中",u==d?t.infoO[ft.getObjID(e)]="子对象被击中,击中对象在分析链中":(t.infoO[ft.getObjID(e)]="子对象被击中,击中对象不在分析链中",b&&(t.infoO[ft.getObjID(d)]="被兄弟节点挡住,兄弟节点信息:"+P.getNodeClassAndName(u)+","+u.getBounds().toString(),M.showDisBound(u,!1,"#ffff00"))),!0;u==d&&(b=!1)}var p=new x,f=!1;if(f=e.getGraphicBounds().contains(i,n),e.width>0&&e.height>0){var m=t._rect;e.mouseThrough?(s=f,p.copyFrom(e.getGraphicBounds())):(e.hitArea?m=e.hitArea:m.setTo(0,0,e.width,e.height),p.copyFrom(m),s=m.contains(i,n)),s&&(t.hitO[ft.getObjID(e)]=!0)}return t.infoO[ft.getObjID(e)]=s?"自身区域被击中":f?"子对象未包含鼠标，实际绘图区域包含鼠标，设置的宽高区域不包含鼠标::"+i+","+n+" hitRec:"+p.toString()+" graphicBounds:"+e.getGraphicBounds().toString()+"，设置mouseThrough=true或将宽高设置到实际绘图区域可解决问题":"子对象未包含鼠标，实际绘图区域不包含鼠标，设置的宽高区域不包含鼠标::"+i+","+n+" hitRec:"+p.toString()+" graphicBounds:"+e.getGraphicBounds().toString(),s},t.hitTest=function(e,i,n){var o=!1;if(e.hitArea instanceof laya.utils.HitArea)return e.hitArea.isHit(i,n);if(e.width>0&&e.height>0||e.mouseThrough||e.hitArea){var a=t._rect;e.mouseThrough?o=e.getGraphicBounds().contains(i,n):(e.hitArea?a=e.hitArea:a.setTo(0,0,e.width,e.height),o=a.contains(i,n))}return o},t.infoO={},t.nodeO={},t.hitO={},n(t,["_matrix",function(){return this._matrix=new R},"_point",function(){return this._point=new X},"_rect",function(){return this._rect=new x}]),t}(),Gt=function(){function t(){}return o(t,"laya.debug.tools.ObjectTools"),t.getFlatKey=function(e,i){return""==e?i:e+t.sign+i},t.flatObj=function(e,i,n){void 0===n&&(n=""),i=i||{};var o;for(o in e)"object"==typeof e[o]?t.flatObj(e[o],i,t.getFlatKey(n,o)):(e[o],i[t.getFlatKey(n,o)]=e[o]);return i},t.recoverObj=function(e){var i,n={};for(i in e)t.setKeyValue(n,i,e[i]);return n},t.differ=function(e,i){var n;e=t.flatObj(e),i=t.flatObj(i);var o={};for(n in e)i.hasOwnProperty(n)||(o[n]="被删除");for(n in i)i[n]!=e[n]&&(o[n]={pre:e[n],now:i[n]});return o},t.traceDifferObj=function(t){var e,i;for(e in t)"string"==typeof t[e]?console.log(e+":",t[e]):(i=t[e],console.log(e+":","now:",i.now,"pre:",i.pre))},t.setKeyValue=function(e,i,n){if(i.indexOf(t.sign)>=0){for(var o,a=i.split(t.sign);a.length>1;)if(o=a.shift(),e[o]||(e[o]={},console.log("addKeyObj:",o)),!(e=e[o]))return void console.log("wrong flatKey:",i);e[a.shift()]=n}else e[i]=n},t.clearObj=function(t){var e;for(e in t)delete t[e]},t.copyObjFast=function(t){var e;return e=laya.debug.tools.ObjectTools.getJsonString(t),laya.debug.tools.ObjectTools.getObj(e)},t.copyObj=function(e){if(e instanceof Array)return t.copyArr(e);var i,n={};for(i in e)null===e[i]||void 0===e[i]?n[i]=e[i]:e[i]instanceof Array?n[i]=t.copyArr(e[i]):"object"==typeof e[i]?n[i]=t.copyObj(e[i]):n[i]=e[i];return n},t.copyArr=function(e){var i;i=[];var n=0,o=0;for(o=e.length,n=0;n<o;n++)i.push(t.copyObj(e[n]));return i},t.concatArr=function(t,e){if(!e)return t;if(!t)return e;var i=0,n=e.length;for(i=0;i<n;i++)t.push(e[i]);return t},t.insertArrToArr=function(t,e,i){void 0===i&&(i=0),i<0&&(i=0),i>t.length&&(i=t.length);t.length;var n=0,o=0;t.length+=e.length;var a=0;for(a=e.length,n=t.length-1;n>=i;n--)t[n]=t[n-a];for(o=e.length,n=0;n<o;n++)t[i+n]=e[n];return t},t.clearArr=function(t){return t?(t.length=0,t):t},t.removeFromArr=function(t,e){var i=0,n=0;for(n=t.length,i=0;i<n;i++)if(t[i]==e)return void t[i].splice(i,1)},t.setValueArr=function(e,i){return e||(e=[]),e.length=0,t.concatArr(e,i)},t.getFrom=function(t,e,i){var n=0;for(n=0;n<i;n++)t.push(e[n]);return t},t.getFromR=function(t,e,i){var n=0;for(n=0;n<i;n++)t.push(e.pop());return t},t.enableDisplayTree=function(t){for(;t;)t.mouseEnabled=!0,t=t.parent},t.getJsonString=function(t){return JSON.stringify(t)},t.getObj=function(t){return JSON.parse(t)},t.getKeyArr=function(t){var e,i;e=[];for(i in t)e.push(i);return e},t.getObjValues=function(t,e){var i,n=0,o=0;for(o=t.length,i=[],n=0;n<o;n++)i.push(t[n][e]);return i},t.hasKeys=function(t,e){var i=0,n=0;for(n=e.length,i=0;i<n;i++)if(!t.hasOwnProperty(e[i]))return!1;return!0},t.copyValueByArr=function(t,e,i){var n=0,o=i.length;for(n=0;n<o;n++)null!==e[i[n]]&&(t[i[n]]=e[i[n]])},t.getNoSameArr=function(t){var e,i=0,n=0;e=[];var o;for(n=t.length,i=0;i<n;i++)o=t[i],e.indexOf(o)<0&&e.push(o);return e},t.insertValue=function(t,e){var i;for(i in e)t[i]=e[i]},t.replaceValue=function(e,i){var n;for(n in e)i.hasOwnProperty(e[n])&&(e[n]=i[e[n]]),"object"==typeof e[n]&&t.replaceValue(e[n],i)},t.setKeyValues=function(t,e,i){var n=0,o=0;for(o=t.length,n=0;n<o;n++)t[n][e]=i},t.findItemPos=function(t,e,i){var n=0,o=0;for(o=t.length,n=0;n<o;n++)if(t[n][e]==i)return n;return-1},t.setObjValue=function(t,e,i){return t[e]=i,t},t.setAutoTypeValue=function(e,i,n){return e.hasOwnProperty(i)&&t.isNumber(e[i])?e[i]=parseFloat(n):e[i]=n,e},t.getAutoValue=function(t){var e=parseFloat(t);return"string"==typeof t&&e+""===xt.trimSide(t)?e:t},t.isNumber=function(t){return parseFloat(t)==t},t.isNaNS=function(t){return"NaN"==t.toString()},t.isNaN=function(t){return"number"!=typeof t&&("string"!=typeof t||"NaN"==parseFloat(t).toString()||parseFloat(t)!=t)},t.getStrTypedValue=function(e){return"false"!=e&&("true"==e||("null"==e?null:"undefined"==e?null:t.getAutoValue(e)))},t.createKeyValueDic=function(t,e){var i;i={};var n=0,o=0;o=t.length;var a;for(n=0;n<o;n++)i[(a=t[n])[e]]=a;return i},t.sign="_",t}(),Wt=function(){function t(){this.timeDic={},this.resultDic={},this.countDic={},this.resultCountDic={},this.nodeDic={},this.resultNodeDic={}}o(t,"laya.debug.tools.ObjTimeCountTool");var e=t.prototype;return e.addTime=function(t,e){ft.idObj(t);var i=0;i=ft.getObjID(t),this.timeDic.hasOwnProperty(i)||(this.timeDic[i]=0),this.timeDic[i]=this.timeDic[i]+e,this.countDic.hasOwnProperty(i)||(this.countDic[i]=0),this.countDic[i]=this.countDic[i]+1,this.nodeDic[i]=t},e.getTime=function(t){ft.idObj(t);var e=0;return e=ft.getObjID(t),this.resultDic[e]?this.resultDic[e]:0},e.getCount=function(t){ft.idObj(t);var e=0;return e=ft.getObjID(t),this.resultCountDic[e]},e.reset=function(){var t;for(t in this.timeDic)this.timeDic[t]=0,this.countDic[t]=0;Gt.clearObj(this.nodeDic)},e.updates=function(){Gt.clearObj(this.resultDic),Gt.insertValue(this.resultDic,this.timeDic),Gt.clearObj(this.resultCountDic),Gt.insertValue(this.resultCountDic,this.countDic),Gt.insertValue(this.resultNodeDic,this.nodeDic),this.reset()},t}(),vt=function(){function t(){this.oX=0,this.oY=0,this.hX=1,this.hY=0,this.vX=0,this.vY=1}o(t,"laya.debug.tools.RecInfo");var e=t.prototype;return e.initByPoints=function(t,e,i){this.oX=t.x,this.oY=t.y,this.hX=e.x,this.hY=e.y,this.vX=i.x,this.vY=i.y},a(0,e,"rotation",function(){return this.rotationRad/Math.PI*180}),a(0,e,"width",function(){return Math.sqrt((this.hX-this.oX)*(this.hX-this.oX)+(this.hY-this.oY)*(this.hY-this.oY))}),a(0,e,"x",function(){return this.oX}),a(0,e,"rotationRadV",function(){var t=this.vX-this.oX,e=this.vY-this.oY;return Math.atan2(e,t)}),a(0,e,"y",function(){return this.oY}),a(0,e,"rotationRad",function(){var t=this.hX-this.oX,e=this.hY-this.oY;return Math.atan2(e,t)}),a(0,e,"height",function(){return Math.sqrt((this.vX-this.oX)*(this.vX-this.oX)+(this.vY-this.oY)*(this.vY-this.oY))}),a(0,e,"rotationV",function(){return this.rotationRadV/Math.PI*180}),t.createByPoints=function(e,i,n){var o;return(o=new t).initByPoints(e,i,n),o},t.getGlobalPoints=function(t,e,i){return t.localToGlobal(new X(e,i))},t.getGlobalRecInfo=function(e,i,n,o,a,l,s){return void 0===i&&(i=0),void 0===n&&(n=0),void 0===o&&(o=1),void 0===a&&(a=0),void 0===l&&(l=0),void 0===s&&(s=1),t.createByPoints(t.getGlobalPoints(e,i,n),t.getGlobalPoints(e,o,a),t.getGlobalPoints(e,l,s))},t}(),wt=function(){function t(){this.timeDic={},this.resultDic={},this.countDic={},this.resultCountDic={},this.nodeDic={},this.isWorking=!1,this.working=!0}o(t,"laya.debug.tools.RenderAnalyser");var e=t.prototype;return e.render=function(t,e){this.addTime(t,e)},e.addTime=function(t,e){ft.idObj(t);var i=0;i=ft.getObjID(t),this.timeDic.hasOwnProperty(i)||(this.timeDic[i]=0),this.timeDic[i]=this.timeDic[i]+e,this.countDic.hasOwnProperty(i)||(this.countDic[i]=0),this.countDic[i]=this.countDic[i]+1,this.nodeDic[i]=t},e.getTime=function(t){ft.idObj(t);var e=0;return e=ft.getObjID(t),this.resultDic[e]?this.resultDic[e]:0},e.getCount=function(t){ft.idObj(t);var e=0;return e=ft.getObjID(t),this.resultCountDic[e]},e.reset=function(){var t;for(t in this.timeDic)this.timeDic[t]=0,this.countDic[t]=0;Gt.clearObj(this.nodeDic)},e.updates=function(){Gt.clearObj(this.resultDic),Gt.insertValue(this.resultDic,this.timeDic),Gt.clearObj(this.resultCountDic),Gt.insertValue(this.resultCountDic,this.countDic),this.reset()},a(0,e,"working",null,function(t){this.isWorking=t,t?i.timer.loop(Tt.RenderCostMaxTime,this,this.updates):i.timer.clear(this,this.updates)}),n(t,["I",function(){return this.I=new t}]),t}(),Rt=function(){function t(){}return o(t,"laya.debug.tools.resizer.DisResizer"),t.init=function(){t._up||((t._up=new Ot("T")).height=2,t._up.type=0,(t._down=new Ot("T")).height=2,t._down.type=0,(t._left=new Ot("R")).width=2,t._left.type=1,(t._right=new Ot("R")).width=2,t._right.type=1,t._barList=[t._up,t._down,t._left,t._right],t.addEvent())},t.stageDown=function(e){var i;i=e.target,t._tar&&lt.isInTree(t._tar,i)||t.clear()},t.clear=function(){t._tar=null,i.stage.off("mouseup",null,t.stageDown),lt.removeItems(t._barList),t.clearDragEvents()},t.addEvent=function(){var e=0,i=0;for(i=t._barList.length,e=0;e<i;e++)t._barList[e].on("mousedown",null,t.barDown)},t.barDown=function(e){if(t.clearDragEvents(),t.tBar=e.target){var i;i=new x,0==t.tBar.type?(i.x=t.tBar.x,i.width=0,i.y=t.tBar.y-200,i.height=400):(i.x=t.tBar.x-200,i.width=400,i.y=0,i.height=0);({}).area=i,t.tBar.record(),t.tBar.startDrag(i),t.tBar.on("dragmove",null,t.draging),t.tBar.on("dragend",null,t.dragEnd)}},t.draging=function(e){if(console.log("draging"),t.tBar&&t._tar){switch(t.tBar){case t._left:t._tar.x+=t.tBar.getDx(),t._tar.width-=t.tBar.getDx(),t._up.width-=t.tBar.getDx(),t._down.width-=t.tBar.getDx(),t._right.x-=t.tBar.getDx(),t.tBar.x-=t.tBar.getDx();break;case t._right:t._tar.width+=t.tBar.getDx(),t._up.width+=t.tBar.getDx(),t._down.width+=t.tBar.getDx();break;case t._up:t._tar.y+=t.tBar.getDy(),t._tar.height-=t.tBar.getDy(),t._right.height-=t.tBar.getDy(),t._left.height-=t.tBar.getDy(),t._down.y-=t.tBar.getDy(),t.tBar.y-=t.tBar.getDy();break;case t._down:t._tar.height+=t.tBar.getDy(),t._right.height+=t.tBar.getDy(),t._left.height+=t.tBar.getDy()}t.tBar.record()}},t.dragEnd=function(e){console.log("dragEnd"),t.clearDragEvents(),t.updates()},t.clearDragEvents=function(){t.tBar&&(t.tBar.off("dragmove",null,t.draging),t.tBar.off("dragend",null,t.dragEnd))},t.setUp=function(e,n){void 0===n&&(n=!1),n&&e==t._tar||(lt.removeItems(t._barList),(t._tar!=e||(t._tar=null,t.clearDragEvents(),n))&&(t._tar=e,t.updates(),lt.addItems(t._barList,e),i.stage.off("mouseup",null,t.stageDown),i.stage.on("mouseup",null,t.stageDown)))},t.updates=function(){var e;if(e=t._tar){var i;i=new x(0,0,e.width,e.height),t._up.x=i.x,t._up.y=i.y,t._up.width=i.width,t._down.x=i.x,t._down.y=i.y+i.height-2,t._down.width=i.width,t._left.x=i.x,t._left.y=i.y,t._left.height=i.height,t._right.x=i.x+i.width-2,t._right.y=i.y,t._right.height=i.height}},t.Side=2,t.Vertical=1,t.Horizon=0,t._up=null,t._down=null,t._left=null,t._right=null,t._barList=null,t._tar=null,t.barWidth=2,t.useGetBounds=!1,t.tBar=null,t}(),Vt=function(){function t(){}return o(t,"laya.debug.tools.resizer.SimpleResizer"),t.setResizeAble=function(e,i,n,o){void 0===n&&(n=150),void 0===o&&(o=150),e.on("mousedown",null,t.onMouseDown,[i,n,o])},t.onMouseDown=function(e,n,o,a){if(t.clearEvents(),e){t.preMousePoint.setTo(i.stage.mouseX,i.stage.mouseY),t.preTarSize.setTo(e.width,e.height),t.preScale.setTo(1,1);var l;for(l=e;l&&l!=i.stage;)t.preScale.x*=l.scaleX,t.preScale.y*=l.scaleY,l=l.parent;i.stage.on("mouseup",null,t.onMouseMoveEnd),i.timer.loop(100,null,t.onMouseMoving,[e,n,o])}},t.onMouseMoving=function(e,n,o,a){var l=(i.stage.mouseX-t.preMousePoint.x)/t.preScale.x+t.preTarSize.x,s=(i.stage.mouseY-t.preMousePoint.y)/t.preScale.y+t.preTarSize.y;e.width=l>n?l:n,e.height=s>o?s:o},t.onMouseMoveEnd=function(e){t.clearEvents()},t.clearEvents=function(){i.timer.clear(null,t.onMouseMoving),i.stage.off("mouseup",null,t.onMouseMoveEnd)},n(t,["preMousePoint",function(){return this.preMousePoint=new X},"preTarSize",function(){return this.preTarSize=new X},"preScale",function(){return this.preScale=new X}]),t}(),Xt=function(){function t(){}return o(t,"laya.debug.tools.ResTools"),t.getCachedResList=function(){return Y.isWebGL?t.getWebGlResList():t.getCanvasResList()},t.getWebGlResList=function(){var t;t=[];var e,i;i=N.currentResourceManager._resources;for(var n=0;n<i.length;n++)if(e=i[n],"WebGLImage"==P.getClassName(e)){var o=e.src;o&&o.indexOf("data:image/png;base64")<0&&t.push(o)}return t},t.getCanvasResList=function(){var e;e={};var i;return i=G.loadedMap,t.collectPics(i,e),t.getArrFromDic(e)},t.getArrFromDic=function(t){var e,i;i=[];for(e in t)i.push(e);return i},t.collectPics=function(t,e){if(t){var i,n;for(i in t)if((n=t[i])&&n.bitmap&&n.bitmap.src){n.bitmap.src.indexOf("data:image/png;base64")<0&&(e[n.bitmap.src]=!0)}}},t}(),It=function(){function t(){}return o(t,"laya.debug.tools.RunProfile"),t.run=function(e,i){void 0===i&&(i=3);var n;t.infoDic.hasOwnProperty(e)||(t.infoDic[e]=new tt),n=t.infoDic[e];var o;o=Yt.getCallLoc(i)+"\n"+Yt.getCallStack(1,i-3),n.add(o),t._runShowDic[e]&&(console.log("Create:"+e),console.log(o))},t.showClassCreate=function(e){t._runShowDic[e]=!0},t.hideClassCreate=function(e){t._runShowDic[e]=!1},t.getRunInfo=function(e){return t.infoDic[e],t.infoDic[e]},t.runTest=function(t,e,i){void 0===i&&(i="runTest"),rt.timeStart(i);var n=0;for(n=0;n<e;n++)t();rt.timeEnd(i)},t.runTest2=function(t,e,i){void 0===i&&(i="runTest");var n=NaN;n=s.now();var o=0;for(o=0;o<e;o++)t();return s.now()-n},t.infoDic={},t._runShowDic={},t}(),xt=(function(){function t(){this._objDic={}}o(t,"laya.debug.tools.SingleTool");var e=t.prototype;e.getArr=function(t){var e;return(e=this.getTypeDic("Array"))[t]||(e[t]=[]),e[t]},e.getObject=function(t){var e;return(e=this.getTypeDic("Object"))[t]||(e[t]={}),e[t]},e.getByClass=function(t,e,i){var n;return(n=this.getTypeDic(e))[t]||(n[t]=new i),n[t]},e.getTypeDic=function(t){return this._objDic[t]||(this._objDic[t]={}),this._objDic[t]},n(t,["I",function(){return this.I=new t}])}(),function(){function t(){}return o(t,"laya.debug.tools.StringTool"),t.toUpCase=function(t){return t.toUpperCase()},t.toLowCase=function(t){return t.toLowerCase()},t.toUpHead=function(t){return t.length<=1?t.toUpperCase():t.charAt(0).toUpperCase()+t.substr(1)},t.toLowHead=function(t){return t.length<=1?t.toLowerCase():t.charAt(0).toLowerCase()+t.substr(1)},t.packageToFolderPath=function(t){return t.replace(".","/")},t.insert=function(t,e,i){return t.substring(0,i)+e+t.substr(i)},t.insertAfter=function(e,i,n,o){void 0===o&&(o=!1);var a=0;return(a=o?e.lastIndexOf(n):e.indexOf(n))>=0?t.insert(e,i,a+n.length):e},t.insertBefore=function(e,i,n,o){void 0===o&&(o=!1);var a=0;return(a=o?e.lastIndexOf(n):e.indexOf(n))>=0?t.insert(e,i,a):e},t.insertParamToFun=function(e,i){var n;n=t.getParamArr(e);var o;return o=i.join(","),n.length>0&&(o=","+o),t.insertBefore(e,o,")",!0)},t.trim=function(e,i){i||(i=[" ","\r","\n","\t",String.fromCharCode(65279)]);var n,o=0,a=0;for(n=e,a=i.length,o=0;o<a;o++)n=t.getReplace(n,i[o],"");return n},t.isEmpty=function(e){return e.length<1||t.emptyStrDic.hasOwnProperty(e)},t.trimLeft=function(e){var i=0;i=0;var n=0;for(n=e.length;t.isEmpty(e.charAt(i))&&i<n;)i++;return i<n?e.substr(i):""},t.trimRight=function(e){var i=0;for(i=e.length-1;t.isEmpty(e.charAt(i))&&i>=0;)i--;return e.substring(0,i),i>=0?e.substring(0,i+1):""},t.trimSide=function(e){var i;return i=t.trimLeft(e),i=t.trimRight(i)},t.isOkFileName=function(e){if(""==laya.debug.tools.StringTool.trimSide(e))return!1;var i=0,n=0;for(n=e.length,i=0;i<n;i++)if(t.specialChars[e.charAt(i)])return!1;return!0},t.trimButEmpty=function(e){return t.trim(e,["\r","\n","\t"])},t.removeEmptyStr=function(e){var i,n=0;for(n=n=e.length-1;n>=0;n--)i=e[n],i=laya.debug.tools.StringTool.trimSide(i),t.isEmpty(i)?e.splice(n,1):e[n]=i;return e},t.ifNoAddToTail=function(t,e){return t.indexOf(e)>=0?t:t+e},t.trimEmptyLine=function(e){var i,n,o=0;for(o=(i=e.split("\n")).length-1;o>=0;o--)n=i[o],t.isEmptyLine(n)&&i.splice(o,1);return i.join("\n")},t.isEmptyLine=function(t){return""==(t=laya.debug.tools.StringTool.trim(t))},t.removeCommentLine=function(e){var i;i=[];var n,o,a=0;a=0;var l=0,s=0;for(l=e.length;a<l;){if(o=n=e[a],(s=n.indexOf("/**"))>=0)for(o=n.substring(0,s-1),t.addIfNotEmpty(i,o);a<l;){if(n=e[a],(s=n.indexOf("*/"))>=0){o=n.substring(s+2),t.addIfNotEmpty(i,o);break}a++}else n.indexOf("//")>=0?0==laya.debug.tools.StringTool.trim(n).indexOf("//")||t.addIfNotEmpty(i,o):t.addIfNotEmpty(i,o);a++}return i},t.addIfNotEmpty=function(e,i){if(i){""!=t.trim(i)&&e.push(i)}},t.trimExt=function(e,i){var n;n=t.trim(e);var o=0,a=0;for(a=i.length,o=0;o<a;o++)n=t.getReplace(n,i[o],"");return n},t.getBetween=function(t,e,i,n){if(void 0===n&&(n=!1),!t)return"";if(!e)return"";if(!i)return"";var o=0,a=0;if((o=t.indexOf(e))<0)return"";if(n){if((a=t.lastIndexOf(i))<o)return""}else a=t.indexOf(i,o+1);return a<0?"":t.substring(o+e.length,a)},t.getSplitLine=function(t,e){return void 0===e&&(e=" "),t.split(e)},t.getLeft=function(t,e){var i=0;return i=t.indexOf(e),t.substr(0,i)},t.getRight=function(t,e){var i=0;return i=t.indexOf(e),t.substr(i+1)},t.delelteItem=function(t){for(;t.length>0&&""==t[0];)t.shift()},t.getWords=function(e){var i=t.getSplitLine(e);return t.delelteItem(i),i},t.getLinesI=function(t,e,i){var n=0,o=[];for(n=t;n<=e;n++)o.push(i[n]);return o},t.structfy=function(e,i,n){void 0===i&&(i=4),void 0===n&&(n=!0),n&&(e=laya.debug.tools.StringTool.trimEmptyLine(e));var o,a=0;a=0;var l;l=t.getEmptyStr(0);var s,r=0,h=0;for(h=(o=e.split("\n")).length,r=0;r<h;r++)s=o[r],s=laya.debug.tools.StringTool.trimLeft(s),s=laya.debug.tools.StringTool.trimRight(s),a+=t.getPariCount(s),s.indexOf("}")>=0&&(l=t.getEmptyStr(a*i)),s=l+s,o[r]=s,l=t.getEmptyStr(a*i);return o.join("\n")},t.getEmptyStr=function(e){if(!t.emptyDic.hasOwnProperty(e)){var i=0,n=0;n=e;var o;for(o="",i=0;i<n;i++)o+=" ";t.emptyDic[e]=o}return t.emptyDic[e]},t.getPariCount=function(t,e,i){void 0===e&&(e="{"),void 0===i&&(i="}");var n;(n={})[e]=1,n[i]=-1;var o,a=0,l=0;l=t.length;var s=0;for(s=0,a=0;a<l;a++)o=t.charAt(a),n.hasOwnProperty(o)&&(s+=n[o]);return s},t.readInt=function(t,e){void 0===e&&(e=0);var i=NaN;i=0;var n,o=0,a=!1;a=!1;var l=0;for(l=t.length,o=e;o<l;o++)if(n=t.charAt(o),Number(n)>0||"0"==n)(i=10*i+Number(n))>0&&(a=!0);else if(a)return i;return i},t.getReplace=function(t,e,i){if(!t)return"";return t.replace(new RegExp(e,"g"),i)},t.getWordCount=function(t,e){var i=new RegExp(e,"g");return t.match(i).length},t.getResolvePath=function(e,i){if(t.isAbsPath(e))return e;var n;n="\\",i.indexOf("/")>=0&&(n="/"),i.charAt(i.length-1)==n&&(i=i.substr(0,i.length-1));var o,a;a="."+n;var l=0;l=t.getWordCount(e,o=".."+n),e=laya.debug.tools.StringTool.getReplace(e,o,""),e=laya.debug.tools.StringTool.getReplace(e,a,"");var s=0,r=0;r=l;for(s=0;s<r;s++)i=t.removeLastSign(e,n);return i+n+e},t.isAbsPath=function(t){return t.indexOf(":")>=0},t.removeLastSign=function(t,e){var i=0;return i=t.lastIndexOf(e),t=t.substring(0,i)},t.getParamArr=function(e){var i;return i=laya.debug.tools.StringTool.getBetween(e,"(",")",!0),t.trim(i).length<1?[]:i.split(",")},t.copyStr=function(t){return t.substring()},t.ArrayToString=function(e){return"[{items}]".replace(new RegExp("\\{items\\}","g"),t.getArrayItems(e))},t.getArrayItems=function(e){var i;if(e.length<1)return"";i=t.parseItem(e[0]);var n=0,o=0;for(o=e.length,n=1;n<o;n++)i+=","+t.parseItem(e[n]);return i},t.parseItem=function(t){return'"'+t+'"',""},t.initAlphaSign=function(){t.alphaSigns||(t.addSign("a","z",t.alphaSigns={}),t.addSign("A","Z",t.alphaSigns),t.addSign("0","9",t.alphaSigns))},t.addSign=function(t,e,i){var n=0,o=0,a=0;for(a=t.charCodeAt(0),o=e.charCodeAt(0),n=a;n<=o;n++)i[String.fromCharCode(n)]=!0,console.log("add :"+String.fromCharCode(n))},t.isPureAlphaNum=function(e){if(t.initAlphaSign(),!e)return!0;var i=0,n=0;for(n=e.length,i=0;i<n;i++)if(!t.alphaSigns[e.charAt(i)])return!1;return!0},t.emptyDic={},t.alphaSigns=null,n(t,["emptyStrDic",function(){return this.emptyStrDic={" ":!0,"\r":!0,"\n":!0,"\t":!0}},"specialChars",function(){return this.specialChars={"*":!0,"&":!0,"%":!0,"#":!0,"?":!0}}]),t}()),Yt=(function(){function t(){}o(t,"laya.debug.tools.TimerControlTool"),t.now=function(){return 1!=t._timeRate?t.getRatedNow():Date.now()},t.getRatedNow=function(){return(t.getNow()-t._startTime)*t._timeRate+t._startTime},t.getNow=function(){return Date.now()},t.setTimeRate=function(e){null==t._browerNow&&(t._browerNow=s.now),t._startTime=t.getNow(),t._timeRate=e,1!=e?s.now=t.now:null!=t._browerNow&&(s.now=t._browerNow)},t.recoverRate=function(){t.setTimeRate(1)},t._startTime=NaN,t._timeRate=1,t._browerNow=null}(),function(){function t(){}o(t,"laya.debug.tools.TimeTool"),t.getTime=function(e,i){void 0===i&&(i=!0),t.timeDic[e]||(t.timeDic[e]=0);var n=NaN,o=NaN;return o=(n=s.now())-t.timeDic[e],t.timeDic[e]=n,o},t.runAllCallLater=function(){t._deep++;for(var e,n=(e=i.timer)._laters,o=0,a=n.length-1;o<=a;o++){var l=n[o];l&&(null!==l.method&&l.run(!1),e._recoverHandler(l)),o===a&&(a=n.length-1)}n.length=0,t._deep--},t.timeDic={},t._deep=0}(),function(){function t(){}o(t,"laya.debug.tools.TouchDebugTools"),t.getTouchIDs=function(t){var e;e=[];var i=0,n=0;for(n=t.length,i=0;i<n;i++)e.push(t[i].identifier||0);return e},t.traceTouchIDs=function(e,i){nt.dTrace(e+":"+t.getTouchIDs(i).join(","))}}(),function(){function t(){}return o(t,"laya.debug.tools.TraceTool"),t.closeAllLog=function(){var e;e=t.emptyLog,s.window.console.log=e},t.emptyLog=function(){},t.traceObj=function(e){t.tempArr.length=0;var i;for(i in e)t.tempArr.push(i+":"+e[i]);var n;return n=t.tempArr.join("\n"),console.log(n),n},t.traceObjR=function(e){t.tempArr.length=0;var i;for(i in e)t.tempArr.push(e[i]+":"+i);var n;return n=t.tempArr.join("\n"),console.log(n),n},t.traceSize=function(t){M.dTrace("Size: x:"+t.x+" y:"+t.y+" w:"+t.width+" h:"+t.height+" scaleX:"+t.scaleX+" scaleY:"+t.scaleY)},t.traceSplit=function(t){console.log("---------------------"+t+"---------------------------")},t.group=function(t){console.group(t)},t.groupEnd=function(){console.groupEnd()},t.getCallStack=function(e,i){void 0===e&&(e=1),void 0===i&&(i=1);var n;n=(n=t.getCallStack).caller.caller;var o;for(o="";n&&e>0;)i<=0&&(o+=n+"<-",e--),n=n.caller,i--;return o},t.getCallLoc=function(e){void 0===e&&(e=2);var i;try{t.Erroer.i++}catch(t){var n;n=t.stack.replace(/Error\n/).split(/\n/),i=n[e]?n[e].replace(/^\s+|\s+$/,""):"unknow"}return i},t.traceCallStack=function(){var e;try{t.Erroer.i++}catch(t){e=t.stack}return console.log(e),e},t.getPlaceHolder=function(e){if(!t.holderDic.hasOwnProperty(e)){var i;i="";var n=0;for(n=0;n<e;n++)i+="-";t.holderDic[e]=i}return t.holderDic[e]},t.traceTree=function(e,i,n){if(void 0===i&&(i=0),void 0===n&&(n=!0),n&&console.log("traceTree"),e){var o=0,a=0;if(e.numChildren<1)console.log(e);else{for(t.group(e),a=e.numChildren,i++,o=0;o<a;o++)t.traceTree(e.getChildAt(o),i,!1);t.groupEnd()}}},t.getClassName=function(t){return t.constructor.name},t.traceSpriteInfo=function(e,i,n,o){void 0===i&&(i=!0),void 0===n&&(n=!0),void 0===o&&(o=!0),e instanceof laya.display.Sprite?e?(t.traceSplit("traceSpriteInfo"),M.dTrace(laya.debug.tools.TraceTool.getClassName(e)+":"+e.name),o?t.traceTree(e):console.log(e),n&&t.traceSize(e),i&&console.log("bounds:"+e.getBounds())):console.log("null Sprite"):console.log("not Sprite")},t.tempArr=[],t.Erroer=null,t.holderDic={},t}()),Ct=(function(){function t(){}o(t,"laya.debug.tools.UVTools",null,"UVTools$1"),t.getUVByRec=function(t,e,i,n){return[t,e,t+i,e,t+i,e+n,t,e+n]},t.getRecFromUV=function(t){return new x(t[0],t[1],t[2]-t[0],t[5]-t[1])},t.isUVRight=function(t){return t[0]==t[6]&&(t[1]==t[3]&&(t[2]==t[4]&&t[5]==t[7]))},t.getTextureRec=function(e){var i;return i=t.getRecFromUV(e.uv),i.x*=e.bitmap.width,i.y*=e.bitmap.height,i.width*=e.bitmap.width,i.height*=e.bitmap.height,i}}(),function(){function t(){this.target=null,this.key=null,this._tValue=NaN,this.preValue=0}o(t,"laya.debug.tools.ValueChanger");var e=t.prototype;return e.record=function(){this.preValue=this.value},e.showValueByAdd=function(t){this.value=this.preValue+t},e.showValueByScale=function(t){this.value=this.preValue*t},e.recover=function(){this.value=this.preValue},e.dispose=function(){this.target=null},a(0,e,"value",function(){return this.target&&(this._tValue=this.target[this.key]),this._tValue},function(t){this._tValue=t,this.target&&(this.target[this.key]=t)}),a(0,e,"dValue",function(){return this.value-this.preValue}),a(0,e,"scaleValue",function(){return this.value/this.preValue}),t.create=function(e,i){var n;return n=new t,n.target=e,n.key=i,n},t}()),Lt=function(){function t(){}return o(t,"laya.debug.tools.VisibleAnalyser"),t.analyseTarget=function(e){var n=!1;n=e.displayedInStage;var o;o=At.getGRec(e);var a=new x;a.setTo(0,0,i.stage.width,i.stage.height);var l,s=!1;s=(l=a.intersection(o)).width>0&&l.height>0;var r=NaN;r=At.getGAlpha(e);var h,c=!1;h="",h+="isInstage:"+n+"\n",h+="isInVisibleRec:"+s+"\n",h+="gVisible:"+(c=At.getGVisible(e))+"\n",h+="gAlpha:"+r+"\n",n&&s&&c&&r>0&&(Y.isWebGL?t.anlyseRecVisible(e):ut.I.analyseNode(e),h+="coverRate:"+t.coverRate+"\n",t._coverList.length>0&&i.timer.once(1e3,null,t.showListLater)),console.log(h),be.I.showTxt(h)},t.showListLater=function(){se.I.showList(t._coverList)},t.isCoverByBrother=function(t){var e=t.parent;if(e){if(!(e._childs.indexOf(t)<0)){var i;(i=e.getSelfBounds()).width<=0||i.height}}},t.anlyseRecVisible=function(e){t.isNodeWalked=!1,t._analyseTarget=e,t.mainCanvas||(t.mainCanvas=K.createCanvas(i.stage.width,i.stage.height)),K.clearCanvas(t.mainCanvas),t.tColor=1,t.resetCoverList(),Nt.walkTargetEX(i.stage,t.recVisibleWalker,null,t.filterFun),t.coverRate=t.isTarRecOK?K.getDifferRate(t.preImageData,t.tarImageData):0,console.log("coverRate:",t.coverRate)},t.getRecArea=function(t){return t.width*t.height},t.addCoverNode=function(e,i){var n;(n={}).path=e,n.label=P.getNodeClassAndName(e)+":"+i,n.coverRate=i,t._coverList.push(n),console.log("coverByNode:",e,i)},t.resetCoverList=function(){t._coverList.length=0},t.recVisibleWalker=function(e){if(e==t._analyseTarget)t.isNodeWalked=!0,t.tarRec.copyFrom(At.getGRec(e)),console.log("tarRec:",t.tarRec.toString()),t.tarRec.width>0&&t.tarRec.height>0?(t.isTarRecOK=!0,t.tColor++,K.fillCanvasRec(t.mainCanvas,t.tarRec,$.toHexColor(t.tColor)),t.preImageData=K.getImageDataFromCanvasByRec(t.mainCanvas,t.tarRec),t.tarImageData=K.getImageDataFromCanvasByRec(t.mainCanvas,t.tarRec)):console.log("tarRec Not OK:",t.tarRec);else if(t.isTarRecOK){var i;if(i=At.getGRec(e),(t.interRec=t.tarRec.intersection(i,t.interRec))&&t.interRec.width>0&&t.interRec.height>0){t.tColor++,K.fillCanvasRec(t.mainCanvas,i,$.toHexColor(t.tColor)),t.tImageData=K.getImageDataFromCanvasByRec(t.mainCanvas,t.tarRec);var n=NaN;n=K.getDifferRate(t.preImageData,t.tImageData),t.preImageData=t.tImageData,t.addCoverNode(e,n)}}},t.filterFun=function(t){return 0!=t.visible&&(!(t.alpha<0)&&!Mt.I.isDebugItem(t))},t.isNodeWalked=!1,t._analyseTarget=null,t.isTarRecOK=!1,t.mainCanvas=null,t.preImageData=null,t.tImageData=null,t.tarImageData=null,t.coverRate=NaN,t.tColor=0,t._coverList=[],n(t,["tarRec",function(){return this.tarRec=new x},"interRec",function(){return this.interRec=new x}]),t}(),Nt=function(){function t(){}return o(t,"laya.debug.tools.WalkTools"),t.walkTarget=function(e,i,n){i.apply(n,[e]);var o,a=0,l=0;for(l=e.numChildren,a=0;a<l;a++)o=e.getChildAt(a),t.walkTarget(o,i,o)},t.walkTargetEX=function(e,i,n,o){if(null==o||o(e)){i.apply(n,[e]);var a,l,s=0,r=0;for(r=(l=e._childs).length,s=0;s<r;s++)a=l[s],t.walkTarget(a,i,a)}},t.walkChildren=function(e,i,n){!e||e.numChildren<1||t.walkArr(lt.getAllChild(e),i,n)},t.walkArr=function(t,e,i){if(t){var n=0,o=0;for(o=t.length,n=0;n<o;n++)e.apply(i,[t[n],n])}},t}(),St=function(){function t(){}return o(t,"laya.debug.tools.Watcher"),t.watch=function(t,e,i){pt.hookVar(t,e,i)},t.traceChange=function(e,i,n){void 0===n&&(n="var changed:"),pt.hookVar(e,i,[t.getTraceValueFun(i),pt.getLocFun(n)])},t.debugChange=function(t,e){pt.hookVar(t,e,[pt.getLocFun("debug loc"),bt.debugHere])},t.differChange=function(e,i,n,o){void 0===o&&(o=""),pt.hookVar(e,i,[t.getDifferFun(e,i,n,o)])},t.getDifferFun=function(t,e,i,n){void 0===n&&(n="");return function(){ot.differ(i,t[e],n)}},t.traceValue=function(t){console.log("value:",t)},t.getTraceValueFun=function(t){return function(e){console.log("set "+t+" :",e)}},t}(),kt=(function(){function t(){}o(t,"laya.debug.tools.XML2Object"),a(1,t,"arrays",function(){return t._arrays||(t._arrays=[]),t._arrays},function(e){t._arrays=e}),t.parse=function(e,i){void 0===i&&(i=!0);var n={};i&&(n.Name=e.localName);var o=e.children.length,a=[],l={};n.c=l,n.cList=a;for(var s=0;s<o;s++){var r,h,c=e.children[s],u=c.localName;r=t.parse(c,!0),a.push(r),l[u]?"array"==t.getTypeof(l[u])?l[u].push(r):l[u]=[l[u],r]:t.isArray(u)?l[u]=[r]:l[u]=r}if(h=0,e.attributes){h=e.attributes.length;var d={};for(n.p=d,s=0;s<h;s++)d[e.attributes[s].name.toString()]=String(e.attributes[s].nodeValue)}return 0==o&&0==h&&(n=""),n},t.getArr=function(e){return e?"array"==t.getTypeof(e)?e:[e]:[]},t.isArray=function(e){for(var i=t._arrays?t._arrays.length:0,n=0;n<i;n++)if(e==t._arrays[n])return!0;return!1},t.getTypeof=function(t){return"object"==typeof t?null==t.length?"object":"number"==typeof t.length?"array":"object":typeof t},t._arrays=null}(),function(){function t(){}o(t,"laya.debug.tools.XML2ObjectNodejs"),a(1,t,"arrays",function(){return t._arrays||(t._arrays=[]),t._arrays},function(e){t._arrays=e}),t.parse=function(e,i){void 0===i&&(i=!0);var n={};i&&(n.Name=e.localName);var o=e[t.ChildrenSign]?e[t.ChildrenSign].length:0,a=[],l={};n.c=l,n.cList=a;for(var s=0;s<o;s++){var r,h=e[t.ChildrenSign][s],c=h.localName,u=0;c&&(r=t.parse(h,!0),a.push(r),l[c]?"array"==t.getTypeof(l[c])?l[c].push(r):l[c]=[l[c],r]:t.isArray(c)?l[c]=[r]:l[c]=r)}if(u=0,e.attributes){u=e.attributes.length;var d={};for(n.p=d,s=0;s<u;s++)d[e.attributes[s].name.toString()]=String(e.attributes[s].nodeValue)}return n},t.getArr=function(e){return e?"array"==t.getTypeof(e)?e:[e]:[]},t.isArray=function(e){for(var i=t._arrays?t._arrays.length:0,n=0;n<i;n++)if(e==t._arrays[n])return!0;return!1},t.getTypeof=function(t){return"object"==typeof t?null==t.length?"object":"number"==typeof t.length?"array":"object":typeof t},t._arrays=null,t.ChildrenSign="childNodes"}(),function(){function t(){this._tar=null,this._menu=null,this._shareBtns=["信息面板","边框","进入节点","树定位","Enable链","Size链","节点工具","可见分析","输出到控制台"],this._menuItems=["隐藏节点"],this._menuHide=null,this._menuItemsHide=["显示节点"],this._menu1=null,this._menuItems1=["输出到控制台"]}o(t,"laya.debug.view.nodeInfo.menus.NodeMenu");var e=t.prototype;return e.showNodeMenu=function(t){t._style?(this._tar=t,this._menu||(this._menuItems=this._menuItems.concat(this._shareBtns),this._menu=te.createMenuByArray(this._menuItems),this._menu.on("select",this,this.onEmunSelect),this._menuItemsHide=this._menuItemsHide.concat(this._shareBtns),this._menuHide=te.createMenuByArray(this._menuItemsHide),this._menuHide.on("select",this,this.onEmunSelect)),t.visible?this._menu.show():this._menuHide.show()):M.log("该节点已不存在，请刷新列表")},e.nodeDoubleClick=function(t){re.I.showByNode(t)},e.setNodeListDoubleClickAction=function(t){s.onMobile||t.on("doubleclick",this,this.onListDoubleClick,[t])},e.onListDoubleClick=function(t){if(t.selectedItem){var e;e=t.selectedItem.path,laya.debug.view.nodeInfo.menus.NodeMenu.I.nodeDoubleClick(e)}},e.setNodeListAction=function(t){t.on(M.getMenuShowEvent(),this,this.onListRightClick,[t])},e.onListRightClick=function(t){if(t.selectedItem){var e;e=t.selectedItem.path,laya.debug.view.nodeInfo.menus.NodeMenu.I.objRightClick(e)}},e.objRightClick=function(t){t instanceof laya.display.Sprite?laya.debug.view.nodeInfo.menus.NodeMenu.I.showNodeMenu(t):"object"==typeof t&&laya.debug.view.nodeInfo.menus.NodeMenu.I.showObjectMenu(t)},e.showObjectMenu=function(t){this._tar=t,this._menu1||(this._menu1=te.createMenuByArray(this._menuItems1),this._menu1.on("select",this,this.onEmunSelect)),this._menu1.show()},e.onEmunSelect=function(t){var e=t.target.data;if("string"==typeof e){switch(e){case"信息面板":de.showObject(this._tar);break;case"边框":M.showDisBound(this._tar);break;case"输出到控制台":console.log(this._tar);break;case"树节点":case"进入节点":Qt.I.showNodeTree(this._tar);break;case"树定位":Qt.I.showSelectInStage(this._tar);break;case"Enable链":be.I.dTrace(M.traceDisMouseEnable(this._tar)),me.I.setSelectList(M.selectedNodes);break;case"Size链":be.I.dTrace(M.traceDisSizeChain(this._tar)),me.I.setSelectList(M.selectedNodes);break;case"节点工具":re.I.showByNode(this._tar);break;case"显示节点":this._tar.visible=!0;break;case"隐藏节点":this._tar.visible=!1;break;case"可见分析":this._tar&&Lt.analyseTarget(this._tar)}}},a(1,t,"I",function(){return t._I||(t._I=new t),t._I}),t._I=null,t}()),Tt=function(){function t(){}return o(t,"laya.debug.view.nodeInfo.NodeConsts"),t.defaultFitlerStr="x,y,width,height,scaleX,scaleY,alpha,renderCost",t.RenderCostMaxTime=3e3,t}(),At=function(){function t(){}return o(t,"laya.debug.view.nodeInfo.NodeUtils"),t.getFilterdTree=function(e,i){i||(i=t.defaultKeys);var n;n={};var o,a=0,l=0;for(l=i.length,a=0;a<l;a++)n[o=i[a]]=e[o];var s,r;l=(s=e._childs).length;var h;for(h=[],a=0;a<l;a++)r=s[a],h.push(t.getFilterdTree(r,i));return n.childs=h,n},t.getPropertyDesO=function(e,i){i||(i=t.defaultKeys);var n={};n.label="object"==typeof e?""+P.getNodeClassAndName(e):""+e,n.type="",n.path=e,n.childs=[],n.isDirectory=!1;var o,a,l=0,s=0;if(e instanceof laya.display.Node){for(n.des=P.getNodeClassAndName(e),n.isDirectory=!0,s=i.length,l=0;l<s;l++)o=i[l],a=t.getPropertyDesO(e[o],i),e.hasOwnProperty(o)?a.label=o+":"+a.des:a.label=o+":"+de.getNodeValue(e,o),n.childs.push(a);(a=t.getPropertyDesO(e[o="_childs"],i)).label=o+":"+a.des,a.isChilds=!0,n.childs.push(a)}else if(e instanceof Array){n.des="Array["+e.length+"]",n.isDirectory=!0;var r;for(s=(r=e).length,l=0;l<s;l++)(a=t.getPropertyDesO(r[l],i)).label=l+":"+a.des,n.childs.push(a)}else if("object"==typeof e){n.des=P.getNodeClassAndName(e),n.isDirectory=!0;for(o in e)(a=t.getPropertyDesO(e[o],i)).label=o+":"+a.des,n.childs.push(a)}else n.des=""+e;return n.hasChild=n.childs.length>0,n},t.adptShowKeys=function(t){var e=0;for(e=t.length-1;e>=0;e--)t[e]=xt.trimSide(t[e]),t[e].length<1&&t.splice(e,1);return t},t.getNodeTreeData=function(e,i){t.adptShowKeys(i);var n;n=t.getPropertyDesO(e,i);var o;return o=[],t.getTreeArr(n,o),o},t.getTreeArr=function(e,i,n){void 0===n&&(n=!0),n&&i.push(e);var o=e.childs,a=0,l=o.length;for(a=0;a<l;a++)o[a].nodeParent=n?e:null,o[a].isDirectory?t.getTreeArr(o[a],i):i.push(o[a])},t.traceStage=function(){console.log(t.getFilterdTree(i.stage,null)),console.log("treeArr:",t.getNodeTreeData(i.stage,null))},t.getNodeCount=function(e,i){if(void 0===i&&(i=!1),i&&!e.visible)return 0;var n=0;n=1;var o,a=0,l=0;for(l=(o=e._childs).length,a=0;a<l;a++)n+=t.getNodeCount(o[a],i);return n},t.getGVisible=function(t){for(;t;){if(!t.visible)return!1;t=t.parent}return!0},t.getGAlpha=function(t){var e=NaN;for(e=1;t;)e*=t.alpha,t=t.parent;return e},t.getGPos=function(t){var e;return e=new X,t.localToGlobal(e),e},t.getGRec=function(t){var e;if(!(e=t._getBoundPointsM(!0))||e.length<1)return x.TEMP.setTo(0,0,0,0);e=p.pListToPointList(e,!0),Nt.walkArr(e,t.localToGlobal,t),e=p.pointListToPlist(e);var i;return i=x._getWrapRec(e,i)},t.getGGraphicRec=function(t){var e;if(!(e=t.getGraphicBounds()._getBoundPoints())||e.length<1)return x.TEMP.setTo(0,0,0,0);e=p.pListToPointList(e,!0),Nt.walkArr(e,t.localToGlobal,t),e=p.pointListToPlist(e);var i;return i=x._getWrapRec(e,i)},t.getNodeCmdCount=function(t){return t.graphics?t.graphics.cmds?t.graphics.cmds.length:t.graphics._one?1:0:0},t.getNodeCmdTotalCount=function(e){var i,n=0,o=0,a=0;for(a=(i=e._childs).length,n=t.getNodeCmdCount(e),o=0;o<a;o++)n+=t.getNodeCmdTotalCount(i[o]);return n},t.getRenderNodeCount=function(e){if("none"!=e.cacheAs)return 1;var i,n=0,o=0,a=0;for(a=(i=e._childs).length,n=1,o=0;o<a;o++)n+=t.getRenderNodeCount(i[o]);return n},t.getReFreshRenderNodeCount=function(e){var i,n=0,o=0,a=0;for(a=(i=e._childs).length,n=1,o=0;o<a;o++)n+=t.getRenderNodeCount(i[o]);return n},t.showCachedSpriteRecs=function(){(t.g=Mt.I.graphicLayer.graphics).clear(),Nt.walkTarget(i.stage,t.drawCachedBounds,null)},t.drawCachedBounds=function(e){if("none"!=e.cacheAs&&!Mt.I.isDebugItem(e)){var i;i=t.getGRec(e),t.g.drawRect(i.x,i.y,i.width,i.height,null,"#0000ff",2)}},t.g=null,n(t,["defaultKeys",function(){return this.defaultKeys=["x","y","width","height"]}]),t}(),Bt=function(){function t(){}return o(t,"laya.debug.view.StyleConsts"),t.setViewScale=function(e){e.scaleX=e.scaleY=t.PanelScale},n(t,["PanelScale",function(){return this.PanelScale=s.onPC?1:s.pixelRatio}]),t}(),Ft=function(t){function e(){e.__super.call(this)}return o(e,"laya.debug.tools.Notice",b),e.notify=function(t,i){e.I.event(t,i)},e.listen=function(t,i,n,o,a){void 0===a&&(a=!1),a&&e.cancel(t,i,n),e.I.on(t,i,n,o)},e.cancel=function(t,i,n){e.I.off(t,i,n)},n(e,["I",function(){return this.I=new e}]),e}(),Jt=function(t){function e(){e.__super.call(this)}o(e,"laya.debug.tools.enginehook.LoaderHook",t);var n=e.prototype;return n.checkUrls=function(t){var i;i="string"==typeof t?t:t.url,e.preFails[i]},n.chekUrlList=function(t){var e=0,i=0;for(i=t.length,e=0;e<i;e++)this.checkUrls(t[e])},n.load=function(e,i,n,o,a,l,s,r){return void 0===a&&(a=1),void 0===l&&(l=!0),void 0===r&&(r=!1),e instanceof Array?this.chekUrlList(e):this.checkUrls(e),t.prototype.load.call(this,e,i,n,o,a,l,s,r)},e.init=function(){e.isInited||(e.isInited=!0,i.loader=new e,i.loader.on("error",null,e.onFail),(e.preFails=v.getJSON("LoadFailItems"))||(e.preFails={}))},e.onFail=function(t){be.I.dTrace("LoadFail:"+t),e.nowFails[t]=!0,v.setJSON("LoadFailItems",e.nowFails)},e.resetFails=function(){e.nowFails={},v.setJSON("LoadFailItems",e.nowFails)},e.preFails={},e.nowFails={},e.enableFailDebugger=!0,e.FailSign="LoadFailItems",e.isInited=!1,e}(W),Ht=(function(t){function e(){this.sideColor=null,this.mainColor=null,this.demoColor=null,this.posSp=null,this.hPos=null,this.container=null,this.isChanging=!1,this.tColor=null,this.tH=NaN,e.__super.call(this),this.container=this,this.createUI()}o(e,"laya.debug.tools.ColorSelector",k);var n=e.prototype;n.createUI=function(){this.sideColor=new k,this.container.addChild(this.sideColor),this.posSp=new k,this.posSp.pos(100,100),this.posSp.graphics.drawCircle(0,0,5,null,"#ff0000"),this.posSp.graphics.drawCircle(0,0,6,null,"#ffff00"),this.posSp.autoSize=!0,this.posSp.cacheAsBitmap=!0,this.sideColor.addChild(this.posSp),this.sideColor.pos(0,0),this.sideColor.size(150,150),this.sideColor.on("mousedown",this,this.sideColorClick);var t=0;this.mainColor=new k;var e;e=this.mainColor.graphics;var i;for(t=0;t<150;t++)i=$.hsb2rgb(t/150*360,1,1),e.drawLine(0,t,20,t,$.getRGBStr(i));this.mainColor.pos(160,0),this.mainColor.size(20,t),this.mainColor.cacheAsBitmap=!0,this.hPos=new k,this.hPos.graphics.drawPie(0,0,10,-10,10,"#ff0000"),this.hPos.x=this.mainColor.x+22,this.container.addChild(this.hPos),this.container.addChild(this.mainColor),this.mainColor.on("mousedown",this,this.mainColorClick),this.demoColor=new k,this.demoColor.pos(this.sideColor.x,this.sideColor.y+this.sideColor.height+10),this.demoColor.size(150,20),this.container.addChild(this.demoColor),this.setColorByRGBStr("#099599"),this.posSp.on("dragmove",this,this.posDraging)},n.posMouseDown=function(t){},n.posDraging=function(){this.updatePosSpAndShowColor()},n.posDragEnd=function(){this.isChanging=!1,this.updatePosSpAndShowColor()},n.setColorByRGBStr=function(t){var e;e=$.getRGBByRGBStr(t),this.setColor(e[0],e[1],e[2])},n.setColor=function(t,e,i,n){void 0===n&&(n=!0);var o;o=$.rgb2hsb(t,e,i);$.hsb2rgb(o[0],o[1],o[2]),this.setColorByHSB(o[0],o[1],o[2],n)},n.setColorByHSB=function(t,e,i,n){void 0===n&&(n=!0),this.hPos.y=this.mainColor.y+t/360*150,this.posSp.x=150*e,this.posSp.y=150*(1-i),this.updateSideColor(t,n)},n.sideColorClick=function(t){this.isChanging=!0,this.posSp.startDrag(),this.updatePosSpAndShowColor(),i.stage.off("mouseup",this,this.sideColorMouseUp),i.stage.once("mouseup",this,this.sideColorMouseUp)},n.sideColorMouseUp=function(t){this.isChanging=!1,this.updatePosSpAndShowColor()},n.updatePosSpAndShowColor=function(){this.posSp.x=this.sideColor.mouseX,this.posSp.y=this.sideColor.mouseY,this.posSp.x<0&&(this.posSp.x=0),this.posSp.y<0&&(this.posSp.y=0),this.posSp.x>150&&(this.posSp.x=150),this.posSp.y>150&&(this.posSp.y=150),this.updateDemoColor()},n.updateDemoColor=function(t){void 0===t&&(t=!0);var e=NaN,i=NaN,n=NaN;e=this.tH,i=this.posSp.x/150,n=1-this.posSp.y/150,this.tColor=$.hsb2rgb(e,i,n);var o;(o=this.demoColor.graphics).clear(),o.drawRect(0,0,this.demoColor.width,this.demoColor.height,$.getRGBStr(this.tColor)),this.isChanging||t&&this.event("ColorChanged",this)},n.mainColorClick=function(t){var e=NaN;e=this.mainColor.mouseY,this.hPos.y=e+this.mainColor.y;var i=NaN;i=e/150*360,this.updateSideColor(i)},n.updateSideColor=function(t,e){void 0===e&&(e=!0),this.tH=t;this.sideColor.graphics.clear(),this.sideColor.cacheAsBitmap=!1;var i;i=$.hsb2rgb(t,1,1);var n=s.context.createLinearGradient(0,0,80,0);n.addColorStop(0,"white"),n.addColorStop(1,$.getRGBStr(i)),this.sideColor.graphics.drawRect(0,0,150,150,n),this.sideColor.graphics.loadImage("comp/colorpicker_overlay.png",0,0),this.sideColor.size(150,150),this.sideColor.cacheAsBitmap=!0,this.updateDemoColor(e)},e.COLOR_CHANGED="ColorChanged",e.COLOR_CLEARED="COLOR_CLEARED",e.RecWidth=150}(),function(t){function e(){e.__super.call(this),this.drawMe()}o(e,"laya.debug.tools.comps.Arrow",k);e.prototype.drawMe=function(){var t;(t=this.graphics).clear(),t.drawLine(0,0,-1,-1,"#ff0000"),t.drawLine(0,0,1,-1,"#ff0000")}}(),function(t){function e(t){this.lineLen=160,this.arrowLen=10,this.sign="Y",this._targetChanger=null,this._isMoving=!1,this.lenControl=new Dt,this.rotationControl=new Dt,this.lenChanger=Ct.create(this,"lineLen"),this.lenControlXChanger=Ct.create(this.lenControl,"x"),void 0===t&&(t="X"),e.__super.call(this),this.sign=t,this.addChild(this.lenControl),this.addChild(this.rotationControl),this.lenControl.on("mousedown",this,this.controlMouseDown),this.drawMe()}o(e,"laya.debug.tools.comps.ArrowLine",k);var n=e.prototype;return n.drawMe=function(){var t;(t=this.graphics).clear(),t.drawLine(0,0,this.lineLen,0,"#ffff00"),t.drawLine(this.lineLen,0,this.lineLen-this.arrowLen,-this.arrowLen,"#ff0000"),t.drawLine(this.lineLen,0,this.lineLen-this.arrowLen,this.arrowLen,"#ff0000"),t.fillText(this.sign,50,-5,"","#ff0000","left"),this._isMoving&&this._targetChanger&&t.fillText(this._targetChanger.key+":"+this._targetChanger.value.toFixed(2),this.lineLen-15,-25,"","#ffff00","center"),this.lenControl.posTo(this.lineLen-15,0),this.rotationControl.posTo(this.lineLen+10,0),this.size(this.arrowLen,this.lineLen)},n.clearMoveEvents=function(){i.stage.off("mousemove",this,this.stageMouseMove),i.stage.off("mouseup",this,this.stageMouseUp)},n.controlMouseDown=function(t){this.clearMoveEvents(),this.lenControlXChanger.record(),this.lenChanger.record(),this.targetChanger&&this.targetChanger.record(),this._isMoving=!0,i.stage.on("mousemove",this,this.stageMouseMove),i.stage.on("mouseup",this,this.stageMouseUp)},n.stageMouseMove=function(t){this.lenControlXChanger.value=this.mouseX,this.lenChanger.showValueByScale(this.lenControlXChanger.scaleValue),this.targetChanger&&this.targetChanger.showValueByScale(this.lenControlXChanger.scaleValue),this.drawMe()},n.stageMouseUp=function(t){this._isMoving=!1,this.noticeChange(),this.clearMoveEvents(),this.lenControlXChanger.recover(),this.lenChanger.recover(),this.drawMe()},n.noticeChange=function(){var t=NaN;t=this.lenChanger.dValue,console.log("lenChange:",t)},a(0,n,"targetChanger",function(){return this._targetChanger},function(t){this._targetChanger&&this._targetChanger.dispose(),this._targetChanger=t}),e}()),Ut=(function(t){function e(t){this.type=0,this._color="#ffffff",this.preX=NaN,this.preY=NaN,e.__super.call(this)}o(e,"laya.debug.tools.comps.AutoSizeRec",t);var n=e.prototype;n.setColor=function(t){this._color=t,this.reRender()},n.changeSize=function(){this.reRender()},n.reRender=function(){var t=this.graphics;t.clear(),t.drawRect(0,0,this.width,this.height,this._color)},n.record=function(){this.preX=this.x,this.preY=this.y},n.getDx=function(){return this.x-this.preX},n.getDy=function(){return this.y-this.preY},a(0,n,"height",t.prototype._$get_height,function(t){i.superSet(k,this,"height",t),this.changeSize()}),a(0,n,"width",t.prototype._$get_width,function(t){i.superSet(k,this,"width",t),this.changeSize()})}(k),function(t){function e(){this._target=null,this._lenType=[["width","height"],["scaleX","scaleY"]],this._type=1,this.xAxis=new Ht("X"),this.yAxis=new Ht("Y"),this.controlBox=new Dt,this._point=new X,this.oPoint=new X,this.myRotationChanger=Ct.create(this,"rotation"),this.targetRotationChanger=Ct.create(null,"rotation"),this.stageMouseRotationChanger=new Ct,e.__super.call(this),this.mouseEnabled=!0,this.size(1,1),this.initMe(),this.xAxis.rotationControl.on("mousedown",this,this.controlMouseDown),this.yAxis.rotationControl.on("mousedown",this,this.controlMouseDown),this.controlBox.on("mousedown",this,this.controlBoxMouseDown),this.on("dragmove",this,this.dragging)}o(e,"laya.debug.tools.comps.Axis",k);var n=e.prototype;return n.updateChanges=function(){if(this._target){var t;t=this._lenType[this._type],this.xAxis.targetChanger=Ct.create(this._target,t[0]),this.yAxis.targetChanger=Ct.create(this._target,t[1])}},n.switchType=function(){this._type++,this._type=this._type%this._lenType.length,this.type=this._type},n.controlBoxMouseDown=function(t){this.startDrag()},n.dragging=function(){this._target&&(this._point.setTo(this.x,this.y),lt.transPoint(this.parent,this._target.parent,this._point),this._target.pos(this._point.x,this._point.y))},n.initMe=function(){this.addChild(this.xAxis),this.addChild(this.yAxis),this.yAxis.rotation=90,this.addChild(this.controlBox),this.controlBox.posTo(0,0)},n.clearMoveEvents=function(){i.stage.off("mousemove",this,this.stageMouseMove),i.stage.off("mouseup",this,this.stageMouseUp)},n.controlMouseDown=function(t){this.targetRotationChanger.target=this.target,this.clearMoveEvents(),this.oPoint.setTo(0,0),this.myRotationChanger.record(),this.oPoint=this.localToGlobal(this.oPoint),this.stageMouseRotationChanger.value=this.getStageMouseRatation(),this.stageMouseRotationChanger.record(),this.targetRotationChanger.record(),i.stage.on("mousemove",this,this.stageMouseMove),i.stage.on("mouseup",this,this.stageMouseUp)},n.getStageMouseRatation=function(){return w.getRotation(this.oPoint.x,this.oPoint.y,i.stage.mouseX,i.stage.mouseY)},n.stageMouseMove=function(t){this.stageMouseRotationChanger.value=this.getStageMouseRatation();var e=NaN;e=-this.stageMouseRotationChanger.dValue,this.target?this.targetRotationChanger.showValueByAdd(e):this.myRotationChanger.showValueByAdd(e)},n.stageMouseUp=function(t){this.noticeChange(),this.clearMoveEvents()},n.noticeChange=function(){console.log("rotate:",-this.stageMouseRotationChanger.dValue)},a(0,n,"target",function(){return this._target},function(t){this._target=t,this.updateChanges()}),a(0,n,"type",function(){return this._type},function(t){this._type=t,this.updateChanges()}),e}()),Dt=function(t){function e(){this.recWidth=10,e.__super.call(this),this.drawMe()}o(e,"laya.debug.tools.comps.Rect",k);var i=e.prototype;return i.drawMe=function(){var t;(t=this.graphics).clear(),t.drawRect(0,0,this.recWidth,this.recWidth,"#22ff22"),this.size(this.recWidth,this.recWidth)},i.posTo=function(t,e){this.x=t-.5*this.recWidth,this.y=e-.5*this.recWidth},e}(),Mt=(function(t){function e(t){this._box=null,this._target=null,this._currDir=null,this._type=0,this.fixScale=NaN,e.__super.call(this),this._left=this.drawBlock(),this._right=this.drawBlock(),this._top=this.drawBlock(),this._bottom=this.drawBlock(),this._topLeft=this.drawBlock(),this._topRight=this.drawBlock(),this._bottomLeft=this.drawBlock(),this._bottomRight=this.drawBlock(),this._lastPoint=new X,this._type=t=3,this.addChild(this._box=this.drawBorder(0,0,16711680)),1!=t&&3!=t||(this.addChild(this._left),this.addChild(this._right)),2!=t&&3!=t||(this.addChild(this._top),this.addChild(this._bottom)),3==t&&(this.addChild(this._topLeft),this.addChild(this._topRight),this.addChild(this._bottomLeft),this.addChild(this._bottomRight)),this.on("mousedown",this,this.onMouseDown),this.mouseThrough=!0}o(e,"laya.debug.tools.DragBox",k);var n=e.prototype;n.onMouseDown=function(t){this._currDir=t.target,t.nativeEvent.shiftKey&&this.initFixScale(),this._currDir!=this&&(this._lastPoint.x=i.stage.mouseX,this._lastPoint.y=i.stage.mouseY,i.stage.on("mousemove",this,this.onMouseMove),i.stage.on("mouseup",this,this.onMouseUp),t.stopPropagation())},n.onMouseUp=function(t){i.stage.off("mousemove",this,this.onMouseMove),i.stage.off("mouseup",this,this.onMouseUp)},n.initFixScale=function(){this.fixScale=this._target.height/this._target.width},n.onMouseMove=function(t){var e=(i.stage.mouseX-this._lastPoint.x)/1,n=(i.stage.mouseY-this._lastPoint.y)/1,o=!1;if(t.nativeEvent.shiftKey)switch(this.fixScale<0&&this.initFixScale(),e*this.fixScale,n/this.fixScale,o=!0,this._currDir){case this._topLeft:case this._bottomLeft:this._currDir=this._left;break;case this._topRight:case this._bottomRight:this._currDir=this._right}if(0!=e||0!=n){this._lastPoint.x+=1*e,this._lastPoint.y+=1*n;var a=e/this._target.scaleX,l=n/this._target.scaleY;this._currDir==this._left?(this._target.x+=e,this._target.width-=a,o&&(this._target.height=this._target.width*this.fixScale)):this._currDir==this._right?(this._target.width+=a,o&&(this._target.height=this._target.width*this.fixScale)):this._currDir==this._top?(this._target.y+=n,this._target.height-=l,o&&(this._target.width=this._target.height/this.fixScale)):this._currDir==this._bottom?(this._target.height+=l,o&&(this._target.width=this._target.height/this.fixScale)):this._currDir==this._topLeft?(this._target.x+=e,this._target.y+=n,this._target.width-=a,this._target.height-=l):this._currDir==this._topRight?(this._target.y+=n,this._target.width+=a,this._target.height-=l):this._currDir==this._bottomLeft?(this._target.x+=e,this._target.width-=a,this._target.height+=l):this._currDir==this._bottomRight&&(this._target.width+=a,this._target.height+=l),this._target.width<1&&(this._target.width=1),this._target.height<1&&(this._target.height=1),this._target.width=Math.round(this._target.width),this._target.x=Math.round(this._target.x),this._target.y=Math.round(this._target.y),this._target.height=Math.round(this._target.height),this.refresh()}},n.drawBorder=function(t,e,i,n){void 0===n&&(n=1);var o=new k,a=o.graphics;return a.clear(),a.drawRect(0,0,t,e,null,"#"+i),o},n.drawBlock=function(){var t=new k,i=t.graphics;return i.clear(),t.width=e.BLOCK_WIDTH,t.height=e.BLOCK_WIDTH,i.drawRect(.5*-e.BLOCK_WIDTH,.5*-e.BLOCK_WIDTH,e.BLOCK_WIDTH,e.BLOCK_WIDTH,"#ffffff","#ff0000",1),t.mouseEnabled=!0,t.mouseThrough=!0,t},n.setTarget=function(t){this._target=t,this.refresh()},n.refresh=function(){this.changePoint(),this.changeSize()},n.changePoint=function(){var t=this._target.localToGlobal(new X),e=this.parent.globalToLocal(t);this.x=e.x,this.y=e.y},n.changeSize=function(){var t=this._target.width*this._target.scaleX,e=this._target.height*this._target.scaleY;console.log("change size"),this.rotation=this._target.rotation,this._box.width==t&&this._box.height==e||(this._box.graphics.clear(),this._box.graphics.drawRect(0,0,Math.abs(t),Math.abs(e),null,"#ff0000"),this._box.size(t,e),this.size(t,e),this._box.scaleX=Math.abs(this._box.scaleX)*(this._target.scaleX>0?1:-1),this._box.scaleY=Math.abs(this._box.scaleY)*(this._target.scaleY>0?1:-1),this._left.x=0,this._left.y=.5*e,this._right.x=t,this._right.y=.5*e,this._top.x=.5*t,this._top.y=0,this._bottom.x=.5*t,this._bottom.y=e,this._topLeft.x=this._topLeft.y=0,this._topRight.x=t,this._topRight.y=0,this._bottomLeft.x=0,this._bottomLeft.y=e,this._bottomRight.x=t,this._bottomRight.y=e)},e.BLOCK_WIDTH=6}(),function(t){function e(){this.nodeRecInfoLayer=null,this.lineLayer=null,this.txtLayer=null,this.popLayer=null,this.graphicLayer=null,this.cacheViewLayer=null,e.__super.call(this),this.nodeRecInfoLayer=new k,this.lineLayer=new k,this.txtLayer=new k,this.popLayer=new k,this.graphicLayer=new k,this.cacheViewLayer=new k,this.nodeRecInfoLayer.name="nodeRecInfoLayer",this.lineLayer.name="lineLayer",this.txtLayer.name="txtLayer",this.popLayer.name="popLayer",this.graphicLayer.name="graphicLayer",this.cacheViewLayer.name="cacheViewLayer",this.addChild(this.lineLayer),this.addChild(this.cacheViewLayer),this.addChild(this.nodeRecInfoLayer),this.addChild(this.txtLayer),this.addChild(this.popLayer),this.addChild(this.graphicLayer),e.I=this,this.zOrder=999,i.stage.on("doubleclick",this,this.setTop)}o(e,"laya.debug.view.nodeInfo.DebugInfoLayer",k);var n=e.prototype;return n.setTop=function(){lt.setTop(this)},n.isDebugItem=function(t){return lt.isInTree(this,t)},e.init=function(){e.I||(new e,i.stage.addChild(e.I))},e.I=null,e}()),jt=function(t){function e(){this._stateDic={},this.isWorkState=!1,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.NodeInfoPanel",k);var i=e.prototype;return i.showDisInfo=function(t){this.recoverNodes(),zt.showDisInfos(t),this.showOnly(t),this.isWorkState=!0},i.showOnly=function(t){t&&(this.hideBrothers(t),this.showOnly(t.parent))},i.recoverNodes=function(){zt.hideAllInfos();var t,e,i;for(t in this._stateDic)if(e=this._stateDic[t],i=e.target)try{i.visible=e.visible}catch(t){}this.isWorkState=!1},i.hideOtherChain=function(t){if(t)for(;t;)this.hideBrothers(t),t=t.parent},i.hideChilds=function(t){if(t){var e,i=0,n=0;n=(e=t._childs).length;var o;for(i=0;i<n;i++)(o=e[i])!=zt.NodeInfoContainer&&(this.saveNodeInfo(o),o.visible=!1)}},i.hideBrothers=function(t){if(t){var e;if(e=t.parent){var i,n=0,o=0;o=(i=e._childs).length;var a;for(n=0;n<o;n++)(a=i[n])!=zt.NodeInfoContainer&&a!=t&&(this.saveNodeInfo(a),a.visible=!1)}}},i.saveNodeInfo=function(t){if(ft.idObj(t),!this._stateDic.hasOwnProperty(ft.getObjID(t))){var e;(e={}).target=t,e.visible=t.visible,this._stateDic[ft.getObjID(t)]=e}},i.recoverNodeInfo=function(t){if(ft.idObj(t),this._stateDic.hasOwnProperty(ft.getObjID(t))){var e;e=this._stateDic[ft.getObjID(t)],t.visible=e.visible}},e.init=function(){e.I||(e.I=new e,zt.init(),Qt.init())},e.I=null,e}(),zt=function(t){function e(){this._infoTxt=null,this._tar=null,e.__super.call(this),this._infoTxt=new A,this._infoTxt.color="#ff0000",this._infoTxt.bgColor="#00ff00",this._infoTxt.fontSize=12}o(e,"laya.debug.view.nodeInfo.NodeInfosItem",k);var a=e.prototype;return a.removeSelf=function(){return this._infoTxt.removeSelf(),laya.display.Node.prototype.removeSelf.call(this)},a.showToUI=function(){e.NodeInfoContainer.nodeRecInfoLayer.addChild(this),this._infoTxt.removeSelf(),e.NodeInfoContainer.txtLayer.addChild(this._infoTxt),this.findOkPos()},a.randomAPos=function(t){this._infoTxt.x=this.x+i.stage.width*Math.random(),this._infoTxt.y=this.y+t*Math.random()},a.findOkPos=function(){var t=0;return t=20,void this.randomAPos(t)},a.isPosOk=function(){var t,n=0,o=0;o=(t=e.NodeInfoContainer.nodeRecInfoLayer._childs).length;var a,l;if((l=this._infoTxt.getBounds()).x<0)return!1;if(l.y<0)return!1;if(l.right>i.stage.width)return!1;for(n=0;n<o;n++)if((a=t[n])!=this._infoTxt&&l.intersects(a.getBounds()))return!1;return!0},a.showInfo=function(t){if(this._tar=t,t){e._txts.length=0;var i,n=0,o=0;for(o=e.showValues.length,t.name?e._txts.push(P.getClassName(t)+"("+t.name+")"):e._txts.push(P.getClassName(t)),n=0;n<o;n++)i=e.showValues[n],e._txts.push(i+":"+e.getNodeValue(t,i));this._infoTxt.text=e._txts.join("\n"),this.graphics.clear();var a;!(a=t._getBoundPointsM(!0))||a.length<1||(a=p.pListToPointList(a,!0),Nt.walkArr(a,t.localToGlobal,t),a=p.pointListToPlist(a),e._disBoundRec=x._getWrapRec(a,e._disBoundRec),this.graphics.drawRect(0,0,e._disBoundRec.width,e._disBoundRec.height,null,"#00ffff"),this.pos(e._disBoundRec.x,e._disBoundRec.y))}},a.fresh=function(){this.showInfo(this._tar)},a.clearMe=function(){this._tar=null},a.recover=function(){I.recover("NodeInfosItem",this)},e.init=function(){e.NodeInfoContainer||(Mt.init(),e.NodeInfoContainer=Mt.I,i.stage.addChild(e.NodeInfoContainer))},e.getNodeInfoByNode=function(t){ft.idObj(t);var i=0;return i=ft.getObjID(t),e._nodeInfoDic[i]||(e._nodeInfoDic[i]=new e),e._nodeInfoDic[i]},e.hideAllInfos=function(){var t;for(t in e._nodeInfoDic)e._nodeInfoDic[t].removeSelf();e.clearRelations()},e.showNodeInfo=function(t){var i;(i=e.getNodeInfoByNode(t)).showInfo(t),i.showToUI()},e.showDisInfos=function(t){var i;if(i=t,t){for(;t;)e.showNodeInfo(t),t=t.parent;lt.setTop(e.NodeInfoContainer),e.apdtTxtInfoPoss(i),e.updateRelations()}},e.apdtTxtInfoPoss=function(t){var n;for(n=[];t;)n.push(t),t=t.parent;var o,a,l=0,s=0;s=n.length;var r=NaN;r=i.stage.width-150;var h=0;for(h=100,(t=n[0])&&(o=e.getNodeInfoByNode(t))&&(a=o._infoTxt,r=i.stage.width-a.width-10,h=a.height+10),n=n.reverse(),l=0;l<s;l++)t=n[l],(o=e.getNodeInfoByNode(t))&&(a=o._infoTxt).pos(r,h*l)},e.clearRelations=function(){e.NodeInfoContainer.lineLayer.graphics.clear()},e.updateRelations=function(){var t;(t=e.NodeInfoContainer.lineLayer.graphics).clear();var i,n;for(i in e._nodeInfoDic)(n=e._nodeInfoDic[i]).parent&&t.drawLine(n.x,n.y,n._infoTxt.x,n._infoTxt.y,"#0000ff")},e.getNodeValue=function(t,i){var n;switch(e._nodePoint.setTo(0,0),i){case"x":n=t.x+" (g:"+t.localToGlobal(e._nodePoint).x+")";break;case"y":n=t.y+" (g:"+t.localToGlobal(e._nodePoint).y+")";break;default:n=t[i]}return n},e.NodeInfoContainer=null,e._nodeInfoDic={},e._txts=[],n(e,["showValues",function(){return this.showValues=["x","y","scaleX","scaleY","width","height","visible","mouseEnabled"]},"_disBoundRec",function(){return this._disBoundRec=new x},"_nodePoint",function(){return this._nodePoint=new X}]),e}(),Et=function(t){function e(){this.txt=null,this._tar=null,this.recColor="#00ff00",e.__super.call(this),this.txt=new A,this.txt.color="#ff0000",this.txt.bgColor="#00ff00",this.txt.fontSize=12,this.addChild(this.txt)}o(e,"laya.debug.view.nodeInfo.recinfos.NodeRecInfo",k);var i=e.prototype;return i.setInfo=function(t){this.txt.text=t},i.setTarget=function(t){this._tar=t},i.showInfo=function(t){if(this._tar=t,t&&t._$P){this.graphics.clear();var i;!(i=t._getBoundPointsM(!0))||i.length<1||(i=p.pListToPointList(i,!0),Nt.walkArr(i,t.localToGlobal,t),i=p.pointListToPlist(i),e._disBoundRec=x._getWrapRec(i,e._disBoundRec),this.graphics.drawRect(0,0,e._disBoundRec.width,e._disBoundRec.height,null,et.RECACHE_REC_COLOR,2),this.pos(e._disBoundRec.x,e._disBoundRec.y))}},i.fresh=function(){this.showInfo(this._tar)},i.clearMe=function(){this._tar=null},n(e,["_disBoundRec",function(){return this._disBoundRec=new x}]),e}(),Qt=function(t){function e(){e.__super.call(this),D.base64.preLoad(m.create(this,this.showToolBar)),te.init(),Rt.init();new Kt}o(e,"laya.debug.view.nodeInfo.ToolPanel",k);var i=e.prototype;return i.showToolBar=function(){ne.I.show()},i.createViews=function(){e.typeClassDic.Find=le,e.typeClassDic.Filter=oe,e.typeClassDic.TxtInfo=ge,e.typeClassDic.Tree=ce},i.switchShow=function(t){var e;(e=this.getView(t))&&e.switchShow()},i.getView=function(t){var i;return!(i=e.viewDic[t])&&e.typeClassDic[t]&&(i=e.viewDic[t]=new e.typeClassDic[t]),i},i.showTxtInfo=function(t){be.I.showTxt(t)},i.showNodeTree=function(t){Ee.I.setDis(t),ne.I.switchToTree()},i.showSelectInStage=function(t){Ee.I.showSelectInStage(t),ne.I.switchToTree()},i.showSelectItems=function(t){ne.I.swichToSelect(),me.I.setSelectList(t)},e.init=function(){e.I||(e.I=new e)},e.I=null,e.viewDic={},e.Find="Find",e.Filter="Filter",e.TxtInfo="TxtInfo",e.Tree="Tree",n(e,["typeClassDic",function(){return this.typeClassDic={}}]),e}(),_t=function(t){function e(){e.__super.call(this),this.bgColor="#ffff00",this.wordWrap=!1,this.mouseEnabled=!0}return o(e,"laya.debug.tools.debugUI.DButton",A),e}(),Ot=function(t){function e(t){this.type=0,this.preX=NaN,this.preY=NaN,e.__super.call(this)}o(e,"laya.debug.tools.resizer.AutoFillRec",t);var i=e.prototype;return i.changeSize=function(){t.prototype.changeSize.call(this);var e=this.graphics;e.clear(),e.drawRect(0,0,this.width,this.height,"#33c5f5")},i.record=function(){this.preX=this.x,this.preY=this.y},i.getDx=function(){return this.x-this.preX},i.getDy=function(){return this.y-this.preY},e}(c),Kt=function(t){function e(){this._tipBox=null,this._tipText=null,this._defaultTipHandler=null,e.__super.call(this),this._tipBox=new c,this._tipBox.addChild(this._tipText=new A),this._tipText.x=this._tipText.y=5,this._tipText.color=e.tipTextColor,this._defaultTipHandler=this.showDefaultTip,i.stage.on("showtip",this,this.onStageShowTip),i.stage.on("hidetip",this,this.onStageHideTip)}o(e,"laya.debug.tools.TipManagerForDebug",c);var n=e.prototype;return n.onStageHideTip=function(t){i.timer.clear(this,this.showTip),this.closeAll(),this.removeSelf()},n.onStageShowTip=function(t){i.timer.once(e.tipDelay,this,this.showTip,[t],!0)},n.showTip=function(t){if("string"==typeof t){var e=String(t);Boolean(e)&&this._defaultTipHandler(e)}else t instanceof laya.utils.Handler?t.run():"function"==typeof t&&t.apply();i.stage.on("mousemove",this,this.onStageMouseMove),i.stage.on("mousedown",this,this.onStageMouseDown),this.onStageMouseMove(null)},n.onStageMouseDown=function(t){this.closeAll()},n.onStageMouseMove=function(t){this.showToStage(this,e.offsetX,e.offsetY)},n.showToStage=function(t,e,n){void 0===e&&(e=0),void 0===n&&(n=0);var o=t.getBounds();t.x=i.stage.mouseX+e,t.y=i.stage.mouseY+n,t.x+o.width>i.stage.width&&(t.x-=o.width+e),t.y+o.height>i.stage.height&&(t.y-=o.height+n)},n.closeAll=function(){i.timer.clear(this,this.showTip),i.stage.off("mousemove",this,this.onStageMouseMove),i.stage.off("mousedown",this,this.onStageMouseDown),this.removeChildren()},n.showDisTip=function(t){this.addChild(t),this.showToStage(this),i.stage.addChild(this)},n.showDefaultTip=function(t){this._tipText.text=t;var n=this._tipBox.graphics;n.clear(),n.drawRect(0,0,this._tipText.width+10,this._tipText.height+10,e.tipBackColor),this.addChild(this._tipBox),this.showToStage(this),i.stage.addChild(this)},a(0,n,"defaultTipHandler",function(){return this._defaultTipHandler},function(t){this._defaultTipHandler=t}),e.offsetX=10,e.offsetY=15,e.tipTextColor="#ffffff",e.tipBackColor="#111111",e.tipDelay=200,e}(),Pt=function(t){function e(){this.minHandler=null,this.maxHandler=null,this.isFirstShow=!0,this.dis=null,e.__super.call(this),this.dis=this,this.minHandler=new m(this,this.close),this.maxHandler=new m(this,this.show),this.createPanel(),this.dis&&(this.dis.on("mousedown",this,this.bringToTop),this.dis.cacheAsBitmap=!0)}o(e,"laya.debug.view.nodeInfo.views.UIViewBase",c);var n=e.prototype;return n.show=function(){Mt.I.setTop(),Mt.I.popLayer.addChild(this.dis),this.isFirstShow&&(this.firstShowFun(),this.isFirstShow=!1)},n.firstShowFun=function(){this.dis.x=.5*(i.stage.width-this.dis.width),this.dis.y=.5*(i.stage.height-this.dis.height),lt.intFyDisPos(this.dis)},n.bringToTop=function(){lt.setTop(this.dis)},n.switchShow=function(){this.dis.parent?this.close():this.show()},n.close=function(){this.dis.removeSelf()},n.createPanel=function(){},n.getInput=function(){var t;return(t=new $t).size(200,30),t.fontSize=30,t},n.getButton=function(){var t;return(t=new _t).size(40,30),t.fontSize=30,t},e}(),qt=function(t){function e(){this.isWorking=!1,this.count=0,this.mTime=0,e.__super.call(this),this.txt.fontSize=12}o(e,"laya.debug.view.nodeInfo.recinfos.ReCacheRecInfo",Et);var n=e.prototype;return n.addCount=function(t){void 0===t&&(t=0),this.count++,this.mTime+=t,this.isWorking||(this.working=!0)},n.updates=function(){this._tar.displayedInStage||(this.working=!1,this.removeSelf()),this.txt.text=P.getNodeClassAndName(this._tar)+"\nreCache:"+this.count+"\ntime:"+this.mTime,this.count>0?(this.fresh(),i.timer.clear(this,this.removeSelfLater)):(this.working=!1,i.timer.once(3e3,this,this.removeSelfLater)),this.count=0,this.mTime=0},n.removeSelfLater=function(){this.working=!1,this.removeSelf()},a(0,n,"working",null,function(t){this.isWorking=t,t?i.timer.loop(1e3,this,this.updates):i.timer.clear(this,this.updates)}),e.showTime=3e3,e}(),$t=function(t){function e(){e.__super.call(this),this.bgColor="#11ff00"}return o(e,"laya.debug.tools.debugUI.DInput",Z),e}(),te=function(t){function e(){this._tY=0,e.__super.call(this),Bt.setViewScale(this)}o(e,"laya.debug.uicomps.ContextMenu",l);var n=e.prototype;return n.addItem=function(t){this.addChild(t),t.y=this._tY,this._tY+=t.height,t.on("mousedown",this,this.onClick)},n.onClick=function(t){this.event("select",t),this.removeSelf()},n.show=function(t,n){void 0===t&&(t=-999),void 0===n&&(n=-999),i.timer.once(100,this,e.showMenu,[this,t,n])},e.init=function(){i.stage.on("click",null,e.cleanMenu)},e.cleanMenu=function(t){var i=0,n=0;for(n=e._menuList.length,i=0;i<n;i++)e._menuList[i]&&e._menuList[i].removeSelf();e._menuList.length=0},e.showMenu=function(t,n,o){void 0===n&&(n=-999),void 0===o&&(o=-999),e.cleanMenu(),e.adptMenu(t),i.stage.addChild(t),lt.showToStage(t),-999!=n&&-999!=o&&t.pos(n,o),e._menuList.push(t)},e.createMenu=function(t){return e.createMenuByArray(arguments)},e.createMenuByArray=function(t){for(var i,n=new e,o=!1,a=0,l=t.length;a<l;a++){var s=t[a],r={};"string"==typeof s?r.label=s:r=s,""!=r.label?((i=new ee(r.label,o)).data=s,n.addItem(i),o=!1):((i=new ee("",o)).data=s,n.addItem(i),o=!0)}return n.zOrder=9999,n},e.adptMenu=function(t){var e=80,i=80,n=0,o=t.numChildren;for(n=0;n<o;n++)i<(e=t.getChildAt(n).width)&&(i=e);for(n=0;n<o;n++)t.getChildAt(n).width=i},e._menuList=[],e}(),ee=function(t){function e(t,i){this.data=null,this.img=null,e.__super.call(this),this.img||(this.img=new g),""!=t?(this.label=t,this.name=t):(this.label="------",this.height=5,this.mouseEnabled=!1,this.img.skin=D.base64.getAdptUrl("comp/line2.png"),this.img.sizeGrid="0,2,0,2",this.addChild(this.img)),this.labelColors="#000000,#000000,#000000,#000000",this._text.x=10,this._text.padding=[-2,0,0,0],this._text.align="left",this._text.wordWrap=!1,this._text.typeset(),this.width=this._text.width+25,this.sizeGrid="3,3,3,3",this.skin=D.base64.getAdptUrl("comp/button1.png")}o(e,"laya.debug.uicomps.ContextMenuItem",t);return a(0,e.prototype,"width",t.prototype._$get_width,function(t){i.superSet(r,this,"width",t),this.img.width=this.width,this.img.x=0}),e}(r),ie=function(t){function e(){this.view=null,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.views.CacheRankView",Pt);var n=e.prototype;return n.createPanel=function(){this.view=new Pe,this.view.top=this.view.bottom=this.view.left=this.view.right=0,this.addChild(this.view),kt.I.setNodeListAction(this.view.itemList),this.view.closeBtn.on("click",this,this.close),this.view.freshBtn.on("click",this,this.fresh),this.view.itemList.scrollBar.hide=!0,this.view.autoUpdate.on("change",this,this.onAutoUpdateChange),this.dis=this,this.view.itemList.array=[],this.onAutoUpdateChange(),this.fresh()},n.onRightClick=function(){var t;if((t=this.view.itemList).selectedItem){var e;e=t.selectedItem.path,kt.I.objRightClick(e)}},n.onAutoUpdateChange=function(){this.autoUpdate=this.view.autoUpdate.selected},n.fresh=function(){O.counter.updates(),this.view.title.text="ReCache排行",M.enableCacheAnalyse||(this.view.title.text="ReCache排行(未开启)",this.view.title.toolTip="DebugTool.init(true)可开启该功能");var t;t=O.counter.resultNodeDic;var i,n,o,a;a=[];for(i in t)n=t[i],e.filterDebugNodes&&lt.isInTree(Mt.I,n)||O.counter.getCount(n)<=0||((o={}).time=O.counter.getCount(n),o.path=n,o.label=P.getNodeClassAndName(n)+":"+o.time,a.push(o));a.sort(Zt.sortByKey("time",!0,!0)),this.view.itemList.array=a},a(0,n,"autoUpdate",null,function(t){i.timer.clear(this,this.fresh),t&&(this.fresh(),i.timer.loop(Tt.RenderCostMaxTime,this,this.fresh))}),a(1,e,"I",function(){return e._I||(e._I=new e),e._I},laya.debug.view.nodeInfo.views.UIViewBase._$SET_I),e._I=null,e.filterDebugNodes=!0,e}(),ne=function(t){function e(){this.view=null,this.dragIcon=null,this.tSelectTar=null,this._selectTip=new k,e.__super.call(this),this._selectTip.setBounds(new x(0,0,0,0))}o(e,"laya.debug.view.nodeInfo.views.DebugPanelView",Pt);var l=e.prototype;return l.createPanel=function(){this.view=new Je,this.dis=this.view,this.view.minBtn.minHandler=this.minHandler,this.view.minBtn.maxHandler=this.maxHandler,this.view.minBtn.tar=this.view,lt.setDragingItem(this.view.bg,this.view),lt.setDragingItem(this.view.tab,this.view),lt.setDragingItem(this.view.clearBtn,this.view),this.clickSelectChange(),this.view.selectWhenClick.on("change",this,this.clickSelectChange),Ft.listen("ItemClicked",this,this.itemClicked),Bt.setViewScale(this.view),this.dragIcon=this.view.dragIcon,this.dragIcon.removeSelf(),this.view.mouseAnalyseBtn.on("mousedown",this,this.mouseAnalyserMouseDown),this.dragIcon.on("dragend",this,this.mouseAnalyserDragEnd),this.view.clearBtn.on("mousedown",this,this.clearBtnClick)},l.clearBtnClick=function(){M.clearDebugLayer()},l.mouseAnalyserMouseDown=function(){var t=e.tempPos;t.setTo(0,0),t=this.view.mouseAnalyseBtn.localToGlobal(t),this.dragIcon.pos(t.x,t.y),this.dragIcon.mouseEnabled=!1,i.stage.addChild(this.dragIcon),this.dragIcon.startDrag()},l.mouseAnalyserDragEnd=function(){this.dragIcon.removeSelf(),this.selectTarget(st.instance.getDisUnderMouse()),re.I.showByNode(st.instance.getDisUnderMouse(),!1)},l.switchToTree=function(){this.view.tab.selectedIndex=0},l.swichToSelect=function(){this.view.tab.selectedIndex=1},l.itemClicked=function(t){e.isClickSelectState&&(e.ignoreDebugTool&&Mt.I.isDebugItem(t)||t instanceof laya.debug.uicomps.ContextMenuItem||t.parent instanceof laya.debug.uicomps.ContextMenuItem||(Qt.I.showSelectInStage(t),re.I.showByNode(t,!1),this.view.selectWhenClick.selected=!1,M.showDisBound(t),this.clickSelectChange()))},l.selectTarget=function(t){t&&(Qt.I.showSelectInStage(t),M.showDisBound(t))},l.clickSelectChange=function(){e.isClickSelectState=this.view.selectWhenClick.selected,s.onPC&&(this.tSelectTar=null,this.clearSelectTip(),e.isClickSelectState?i.timer.loop(200,this,this.updateSelectTar,null,!0):i.timer.clear(this,this.updateSelectTar))},l.clearSelectTip=function(){this._selectTip.removeSelf()},l.updateSelectTar=function(){if(this.clearSelectTip(),this.tSelectTar=st.instance.getDisUnderMouse(),this.tSelectTar&&!Mt.I.isDebugItem(this.tSelectTar)){var t;(t=this._selectTip.graphics).clear();var e;e=At.getGRec(this.tSelectTar),Mt.I.popLayer.addChild(this._selectTip),t.drawRect(0,0,e.width,e.height,null,"#00ffff",2),this._selectTip.pos(e.x,e.y)}},a(1,e,"I",function(){return e._I||(e._I=new e),e._I},laya.debug.view.nodeInfo.views.UIViewBase._$SET_I),e._I=null,e.ignoreDebugTool=!0,e.isClickSelectState=!1,n(e,["tempPos",function(){return this.tempPos=new X}]),e}(),oe=function(t){function e(){this.input=null,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.views.FilterView",t);var i=e.prototype;return i.createPanel=function(){this.input=new Z,this.input.size(400,500),this.input.multiline=!0,this.input.bgColor="#ff00ff",this.input.fontSize=24,this.addChild(this.input)},i.show=function(){this.input.text=zt.showValues.join("\n"),t.prototype.show.call(this)},i.close=function(){t.prototype.close.call(this),zt.showValues=this.input.text.split("\n")},e}(Pt),ae=function(t){function e(){this.view=null,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.views.FindSmallView",Pt);var n=e.prototype;return n.createPanel=function(){this.view=new He,Bt.setViewScale(this.view),lt.setDragingItem(this.view.bg,this.view),this.view.typeSelect.selectedIndex=1,this.view.closeBtn.on("click",this,this.close),this.view.findBtn.on("click",this,this.onFind),this.dis=this.view},n.onFind=function(){var t;t=this.view.findTxt.text,t=xt.trimSide(t);var e;e=0==this.view.typeSelect.selectedIndex?M.findNameHas(t,!1):M.findClassHas(i.stage,t),Qt.I.showSelectItems(e),this.close()},a(1,e,"I",function(){return e._I||(e._I=new e),e._I},laya.debug.view.nodeInfo.views.UIViewBase._$SET_I),e._I=null,e}(),le=function(t){function e(){this.view=null,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.views.FindView",Pt);var n=e.prototype;return n.createPanel=function(){this.view=new Ue,lt.setDragingItem(this.view.bg,this.view),this.view.result.scrollBar.hide=!0,this.view.result.array=[],this.view.typeSelect.selectedIndex=1,this.view.closeBtn.on("click",this,this.close),this.view.findBtn.on("click",this,this.onFind),kt.I.setNodeListAction(this.view.result),this.dis=this.view},n.onRightClick=function(){var t;if((t=this.view.result).selectedItem){var e;e=t.selectedItem.path,kt.I.objRightClick(e)}},n.onFind=function(){var t;t=this.view.findTxt.text,t=xt.trimSide(t);var e;e=0==this.view.typeSelect.selectedIndex?M.findNameHas(t,!1):M.findClassHas(i.stage,t),this.showFindResult(e)},n.showFindResult=function(t){if(t){var e=0,i=0;i=t.length;var n;n=[];var o,a;for(e=0;e<i;e++)a=t[e],(o={}).label=P.getNodeClassAndName(a),o.path=a,n.push(o);this.view.result.array=n}},a(1,e,"I",function(){return e._I||(e._I=new e),e._I},laya.debug.view.nodeInfo.views.UIViewBase._$SET_I),e._I=null,e}(),se=function(t){function e(){this.view=null,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.views.NodeListPanelView",Pt);var i=e.prototype;return i.createPanel=function(){this.view=new Me,this.addChild(this.view),lt.setDragingItem(this.view.bg,this.view),kt.I.setNodeListAction(this.view.itemList),this.view.closeBtn.on("click",this,this.close),this.view.itemList.scrollBar.hide=!0,this.dis=this,this.view.itemList.array=[]},i.showList=function(t){this.view.itemList.array=t,this.show()},a(1,e,"I",function(){return e._I||(e._I=new e),e._I},laya.debug.view.nodeInfo.views.UIViewBase._$SET_I),e._I=null,e.filterDebugNodes=!0,e}(),re=function(t){function e(){this.view=null,this.dragIcon=null,this._tar=null,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.views.NodeToolView",t);var l=e.prototype;return l.show=function(){this.showByNode()},l.createPanel=function(){this.view=new je,this.addChild(this.view),this.view.on("click",this,this.onBtnClick),this.view.closeBtn.on("click",this,this.onCloseBtn),lt.setDragingItem(this.view.bg,this.view),this.dis=this.view,this.view.freshBtn.on("click",this,this.onFreshBtn),this.dragIcon=this.view.dragIcon,this.dragIcon.removeSelf(),this.dragIcon.on("dragend",this,this.mouseAnalyserDragEnd),this.view.mouseAnalyseBtn.on("mousedown",this,this.mouseAnalyserMouseDown)},l.mouseAnalyserMouseDown=function(){var t=e.tempPos;t.setTo(0,0),t=this.view.mouseAnalyseBtn.localToGlobal(t),this.dragIcon.pos(t.x,t.y),this.dragIcon.mouseEnabled=!1,i.stage.addChild(this.dragIcon),this.dragIcon.startDrag()},l.mouseAnalyserDragEnd=function(){this.dragIcon.removeSelf(),laya.debug.view.nodeInfo.views.NodeToolView.I.target&&yt.analyseNode(laya.debug.view.nodeInfo.views.NodeToolView.I.target)},l.onFreshBtn=function(){this._tar&&(this._tar.reCache(),this._tar.repaint())},l.onCloseBtn=function(){this.close()},l.onBtnClick=function(t){if(this._tar){var e;e=t.target,console.log("onBtnClick:",e);switch(e.label){case"父链":M.showParentChain(this._tar),me.I.setSelectList(M.selectedNodes);break;case"子":M.showAllChild(this._tar),me.I.setSelectList(M.selectedNodes);break;case"兄弟":M.showAllBrother(this._tar),me.I.setSelectList(M.selectedNodes);break;case"Enable链":be.I.dTrace(M.traceDisMouseEnable(this._tar)),me.I.setSelectList(M.selectedNodes);break;case"Size链":be.I.dTrace(M.traceDisSizeChain(this._tar)),me.I.setSelectList(M.selectedNodes);break;case"隐藏旁支":jt.I.recoverNodes(),jt.I.hideOtherChain(this._tar);break;case"隐藏兄弟":jt.I.recoverNodes(),jt.I.hideBrothers(this._tar);break;case"隐藏子":jt.I.recoverNodes(),jt.I.hideChilds(this._tar);break;case"恢复":jt.I.recoverNodes();break;case"节点树定位":Qt.I.showSelectInStage(this._tar);break;case"显示边框":M.showDisBound(this._tar);break;case"输出到控制台":console.log(this._tar);break;case"显示切换":this._tar.visible=!this._tar.visible}}},l.showByNode=function(e,n){void 0===n&&(n=!0),e||(e=i.stage),n&&t.prototype.show.call(this),this._tar=e,this.fresh()},l.fresh=function(){this._tar&&(this.view.tarTxt.text=P.getNodeClassAndName(this._tar))},a(0,l,"target",function(){return this._tar}),a(1,e,"I",function(){return e._I||(e._I=new e),e._I},laya.debug.view.nodeInfo.views.UIViewBase._$SET_I),e._I=null,n(e,["tempPos",function(){return this.tempPos=new X}]),e}(Pt),he=function(t){function e(){this.view=null,this._handler=null,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.views.NodeTreeSettingView",t);var i=e.prototype;return i.createPanel=function(){t.prototype.createPanel.call(this),this.view=new ze,Bt.setViewScale(this.view),this.addChild(this.view),this.inits(),this.dis=this.view},i.show=function(){t.prototype.show.call(this)},i.showSetting=function(t,e,i){i instanceof laya.display.Node?this.view.showTxt.text=Tt.defaultFitlerStr.split(",").join("\n"):this.view.showTxt.text=t.join("\n"),this._handler=e,this.show()},i.inits=function(){this.view.okBtn.on("click",this,this.onOkBtn),this.view.closeBtn.on("click",this,this.onCloseBtn),lt.setDragingItem(this.view.bg,this.view),this.dis=this.view},i.onCloseBtn=function(){this.close()},i.onOkBtn=function(){this.close();var t;t=this.view.showTxt.text.split("\n"),this._handler&&(this._handler.runWith([t]),this._handler=null)},a(1,e,"I",function(){return e._I||(e._I=new e),e._I},laya.debug.view.nodeInfo.views.UIViewBase._$SET_I),e._I=null,e}(Pt),ce=function(t){function e(){this.nodeTree=null,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.views.NodeTreeView",t);var n=e.prototype;return n.show=function(){this.showByNode()},n.showByNode=function(t){t||(t=i.stage),this.nodeTree.setDis(t)},n.createPanel=function(){t.prototype.createPanel.call(this),this.nodeTree||(this.nodeTree=new Ee),this.dis=null;var e;(e=this.nodeTree).top=e.bottom=e.left=e.right=0,this.addChild(e),this.showByNode(i.stage)},n.showSelectInStage=function(t){this.showByNode(i.stage),this.nodeTree.selectByNode(t)},e}(Pt),ue=function(t){function e(){this.view=null,this._menu=null,this._menuItems=["统计详情","增量详情"],this._tSelectKey=null,this.preInfo={},e.__super.call(this),e._I=this}o(e,"laya.debug.view.nodeInfo.views.ObjectCreateView",Pt);var i=e.prototype;return i.createPanel=function(){this.view=new Qe,this.view.top=this.view.bottom=this.view.left=this.view.right=0,this.addChild(this.view),this.view.itemList.on(M.getMenuShowEvent(),this,this.onRightClick),this.view.closeBtn.on("click",this,this.close),this.view.freshBtn.on("click",this,this.fresh),this.view.itemList.scrollBar.hide=!0,this._menu=te.createMenuByArray(this._menuItems),this._menu.on("select",this,this.onEmunSelect),this.fresh()},i.onEmunSelect=function(t){if(this._tSelectKey){var e=t.target.data;if("string"==typeof e){var i;switch(e){case"统计详情":(i=It.getRunInfo(this._tSelectKey))&&be.I.showTxt(this._tSelectKey+" createInfo:\n"+i.traceSelfR());break;case"增量详情":(i=It.getRunInfo(this._tSelectKey))&&be.I.showTxt(this._tSelectKey+" createInfo:\n"+i.traceSelfR(i.changeO))}}}},i.onRightClick=function(){var t;if((t=this.view.itemList).selectedItem){var e;e=t.selectedItem.path,this._tSelectKey=e,this._tSelectKey&&this._menu.show()}},i.show=function(){this.fresh()},i.fresh=function(){var t;t=ht.I.createInfo;var e,i;i=[];var n,o;for(e in t)this.preInfo[e]||(this.preInfo[e]=0),(n={}).path=e,n.count=t[e],n.add=t[e]-this.preInfo[e],n.add>0?n.label=e+":"+t[e]+" +"+n.add:n.label=e+":"+t[e],(o=It.getRunInfo(e))&&o.record(),n.rank=1e3*n.add+n.count,this.preInfo[e]=t[e],i.push(n);i.sort(Zt.sortByKey("rank",!0,!0)),this.view.itemList.array=i},a(1,e,"I",function(){return e._I||(e._I=new e),e._I},laya.debug.view.nodeInfo.views.UIViewBase._$SET_I),e._I=null,e}(),de=function(t){function e(){this.view=null,this.showKeys=["x","y","width","height","renderCost"],this._closeSettingHandler=null,this._tar=null,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.views.ObjectInfoView",t);var n=e.prototype;return n.createPanel=function(){t.prototype.createPanel.call(this),this.view=new _e,Bt.setViewScale(this.view),this.addChild(this.view),this.inits()},n.inits=function(){this.view.closeBtn.on("click",this,this.close),this.view.settingBtn.on("click",this,this.onSettingBtn),this.view.autoUpdate.on("change",this,this.onAutoUpdateChange),lt.setDragingItem(this.view.bg,this.view),lt.setResizeAbleEx(this.view),this._closeSettingHandler=new m(this,this.closeSetting),this.dis=this.view},n.onAutoUpdateChange=function(){this.autoUpdate=this.view.autoUpdate.selected},n.onSettingBtn=function(){he.I.showSetting(this.showKeys,this._closeSettingHandler,this._tar)},n.reset=function(){this.showKeys=["x","y","width","height","renderCost"]},n.closeSetting=function(t){this.showKeys=t,this.fresh()},n.showObjectInfo=function(t){this._tar=t,this.fresh(),this.show(),this.onAutoUpdateChange()},n.fresh=function(){this._tar?(this.view.title.text=P.getNodeClassAndName(this._tar),this.view.showTxt.text=e.getObjValueStr(this._tar,this.showKeys,!1)):(this.view.showTxt.text="",this.view.title.text="未选中对象")},n.freshKeyInfos=function(){this.fresh()},n.close=function(){t.prototype.close.call(this),this.autoUpdate=!1,I.recover("ObjectInfoView",this)},n.show=function(){t.prototype.show.call(this)},a(0,n,"autoUpdate",null,function(t){i.timer.clear(this,this.freshKeyInfos),t&&i.timer.loop(2e3,this,this.freshKeyInfos)}),e.getObjValueStr=function(t,i,n){void 0===n&&(n=!0);var o,a=0,l=0;for(e._txts.length=0,l=i.length,n&&(t.name?e._txts.push(P.getClassName(t)+"("+t.name+")"):e._txts.push(P.getClassName(t))),a=0;a<l;a++)o=i[a],e._txts.push(o+":"+e.getNodeValue(t,o));return e._txts.join("\n")},e.getNodeValue=function(t,e){var i;if(t instanceof laya.display.Sprite){var n;switch(n=t,e){case"gRec":i=At.getGRec(n).toString();break;case"gAlpha":i=At.getGAlpha(n)+"";break;case"cmdCount":i=At.getNodeCmdCount(n)+"";break;case"cmdAll":i=At.getNodeCmdTotalCount(n)+"";break;case"nodeAll":i=""+At.getNodeCount(n);break;case"nodeVisible":i=""+At.getNodeCount(n,!0);break;case"nodeRender":i=""+At.getRenderNodeCount(n);break;case"nodeReCache":i=""+At.getReFreshRenderNodeCount(n);break;case"renderCost":i=""+wt.I.getTime(n);break;case"renderCount":i=""+wt.I.getCount(n);break;default:i=t[e]+""}}else i=t[e]+"";return i},e.showObject=function(t){var i;(i=I.getItemByClass("ObjectInfoView",e)).reset(),i.showObjectInfo(t)},e._txts=[],e}(Pt),be=function(t){function e(){this.view=null,e.__super.call(this),M._logFun=e.log}o(e,"laya.debug.view.nodeInfo.views.OutPutView",Pt);var i=e.prototype;return i.createPanel=function(){this.view=new Oe,lt.setDragingItem(this.view.txt,this.view),lt.setDragingItem(this.view.bg,this.view),Bt.setViewScale(this.view),this.view.txt.textField.overflow=A.SCROLL,this.view.txt.textField.wordWrap=!0,this.view.on("mousewheel",this,this.mouseWheel),this.view.txt.text="",lt.setResizeAbleEx(this.view),this.view.closeBtn.on("click",this,this.close),this.view.clearBtn.on("click",this,this.onClearBtn),this.dis=this.view},i.onClearBtn=function(){this.clearText()},i.mouseWheel=function(t){this.view.txt.textField.scrollY-=10*t.delta},i.showTxt=function(t){this.view.txt.text=t,this.show(),this.view.txt.textField.scrollY=this.view.txt.textField.maxScrollY},i.clearText=function(){this.view.txt.text=""},i.dTrace=function(t){var e=arguments;this.view.txt.textField.scrollY>1e3&&(this.view.txt.text="");var i,n=0,o=0;for(o=e.length,i=e[0],n=1;n<o;n++)i+=" "+e[n];this.addStr(i)},i.addStr=function(t){this.view.txt.text+="\n"+t,this.show(),this.view.txt.textField.scrollY=this.view.txt.textField.maxScrollY},a(1,e,"I",function(){return e._I||(e._I=new e),e._I},laya.debug.view.nodeInfo.views.UIViewBase._$SET_I),e.log=function(t){e.I.addStr(t)},e._I=null,e}(),pe=function(t){function e(){this.view=null,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.views.RenderCostRankView",Pt);var n=e.prototype;return n.createPanel=function(){this.view=new Pe,this.view.top=this.view.bottom=this.view.left=this.view.right=0,this.addChild(this.view),kt.I.setNodeListAction(this.view.itemList),this.view.closeBtn.on("click",this,this.close),this.view.freshBtn.on("click",this,this.fresh),this.view.itemList.scrollBar.hide=!0,this.view.autoUpdate.on("change",this,this.onAutoUpdateChange),this.dis=this,this.view.itemList.array=[],this.onAutoUpdateChange(),this.fresh(),i.timer.once(5e3,this,this.fresh)},n.onRightClick=function(){var t;if((t=this.view.itemList).selectedItem){var e;e=t.selectedItem.path,kt.I.objRightClick(e)}},n.onAutoUpdateChange=function(){this.autoUpdate=this.view.autoUpdate.selected},n.fresh=function(){this.view.title.text="渲染用时排行("+Tt.RenderCostMaxTime+"ms)";var t;t=wt.I.nodeDic;var n,o,a,l;l=[];for(n in t)o=t[n],e.filterDebugNodes&&lt.isInTree(Mt.I,o)||wt.I.getTime(o)<=0||((a={}).time=wt.I.getTime(o),e.filterDebugNodes&&o==i.stage&&(a.time-=wt.I.getTime(Mt.I)),a.path=o,a.label=P.getNodeClassAndName(o)+":"+a.time,l.push(a));l.sort(Zt.sortByKey("time",!0,!0)),this.view.itemList.array=l},a(0,n,"autoUpdate",null,function(t){i.timer.clear(this,this.fresh),t&&(this.fresh(),i.timer.loop(Tt.RenderCostMaxTime,this,this.fresh))}),a(1,e,"I",function(){return e._I||(e._I=new e),e._I},laya.debug.view.nodeInfo.views.UIViewBase._$SET_I),e._I=null,e.filterDebugNodes=!0,e}(),fe=function(t){function e(){this.view=null,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.views.ResRankView",Pt);var n=e.prototype;return n.createPanel=function(){this.view=new Pe,this.view.top=this.view.bottom=this.view.left=this.view.right=0,this.addChild(this.view),this.view.closeBtn.on("click",this,this.close),this.view.freshBtn.on("click",this,this.fresh),this.view.itemList.scrollBar.hide=!0,this.view.autoUpdate.on("change",this,this.onAutoUpdateChange),this.dis=this,this.view.itemList.array=[],this.view.itemList.on("rightclick",this,this.onRightClick),this.onAutoUpdateChange(),this.fresh()},n.onRightClick=function(){var t;(t=this.view.itemList).selectedItem&&console.log(t.selectedItem.url)},n.onAutoUpdateChange=function(){this.autoUpdate=this.view.autoUpdate.selected},n.fresh=function(){this.view.title.text="图片缓存列表";var t,e,i;i=[];var n=0,o=0;for(o=(t=Xt.getCachedResList()).length,n=0;n<o;n++){e={};var a;a=(a=t[n]).replace(J.rootPath,""),e.label=a,e.url=a,i.push(e)}this.view.itemList.array=i},a(0,n,"autoUpdate",null,function(t){i.timer.clear(this,this.fresh),t&&(this.fresh(),i.timer.loop(Tt.RenderCostMaxTime,this,this.fresh))}),a(1,e,"I",function(){return e._I||(e._I=new e),e._I},laya.debug.view.nodeInfo.views.UIViewBase._$SET_I),e._I=null,e.filterDebugNodes=!0,e}(),me=function(t){function e(){this.showKeys=[],this.view=null,this.fliterTxt=null,this.itemList=null,e.__super.call(this),e._I=this,this.setSelectList(null)}o(e,"laya.debug.view.nodeInfo.views.SelectInfosView",Pt);var i=e.prototype;return i.createPanel=function(){this.view=new qe,this.addChild(this.view),this.view.top=this.view.bottom=this.view.left=this.view.right=0,kt.I.setNodeListAction(this.view.selectList),this.view.closeBtn.on("click",this,this.close),this.view.selectList.scrollBar.hide=!0,this.dis=null,this.view.findBtn.on("click",this,this.onFindBtn),this.fliterTxt=this.view.fliterTxt,this.view.fliterTxt.on("enter",this,this.onFliterTxtChange),this.view.fliterTxt.on("blur",this,this.onFliterTxtChange)},i.onFliterTxtChange=function(t){var e;""==(e=this.fliterTxt.text)?0!=this.showKeys.length&&(this.showKeys.length=0,this.fresh()):e!=this.showKeys.join(",")&&(this.showKeys=e.split(","),this.fresh())},i.onFindBtn=function(){ae.I.show()},i.onRightClick=function(){var t;if((t=this.view.selectList).selectedItem){var e;e=t.selectedItem.path,kt.I.objRightClick(e)}},i.setSelectTarget=function(t){t&&this.setSelectList([t])},i.setSelectList=function(t){this.itemList=t,this.fresh()},i.fresh=function(){var t;if(!(t=this.itemList)||t.length<1)this.view.selectList.array=[];else{var e,i,n=0,o=0;o=t.length;var a;for(a=[],n=0;n<o;n++)e=t[n],(i={}).label=this.getLabelTxt(e),i.path=e,a.push(i);this.view.selectList.array=a}},i.getLabelTxt=function(t){var e;e=P.getNodeClassAndName(t);var i=0,n=0;for(n=this.showKeys.length,i=0;i<n;i++)e+=","+de.getNodeValue(t,this.showKeys[i]);return e},a(1,e,"I",function(){return e._I||(e._I=new e),e._I},laya.debug.view.nodeInfo.views.UIViewBase._$SET_I),e._I=null,e}(),ge=(function(t){function e(){this.view=null,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.views.ToolBarView",Pt);var n=e.prototype;n.createPanel=function(){this.view=new $e,this.addChild(this.view),lt.setDragingItem(this.view.bg,this.view),this.view.on("click",this,this.onBtnClick),this.view.minBtn.minHandler=this.minHandler,this.view.minBtn.maxHandler=this.maxHandler,this.view.minBtn.tar=this.view,this.clickSelectChange(),this.view.selectWhenClick.on("change",this,this.clickSelectChange),Ft.listen("ItemClicked",this,this.itemClicked),this.dis=this.view},n.itemClicked=function(t){e.isClickSelectState&&(lt.isInTree(this.view.selectWhenClick,t)||!e.ignoreDebugTool||Mt.I.isDebugItem(t))},n.clickSelectChange=function(){e.isClickSelectState=this.view.selectWhenClick.selected},n.firstShowFun=function(){this.dis.x=i.stage.width-this.dis.width-20,this.dis.y=5},n.onBtnClick=function(t){switch(t.target){case this.view.treeBtn:Qt.I.switchShow("Tree");break;case this.view.findBtn:Qt.I.switchShow("Find");break;case this.view.clearBtn:M.clearDebugLayer();break;case this.view.rankBtn:pe.I.show();break;case this.view.nodeRankBtn:ue.I.show();break;case this.view.cacheBtn:At.showCachedSpriteRecs()}},a(1,e,"I",function(){return e._I||(e._I=new e),e._I},laya.debug.view.nodeInfo.views.UIViewBase._$SET_I),e._I=null,e.ignoreDebugTool=!0,e.isClickSelectState=!1}(),function(t){function e(){this.input=null,this.btn=null,e.__super.call(this)}o(e,"laya.debug.view.nodeInfo.views.TxtInfoView",Pt);var n=e.prototype;return n.createPanel=function(){this.input=new Z,this.input.size(200,400),this.input.multiline=!0,this.input.bgColor="#ff00ff",this.input.fontSize=12,this.input.wordWrap=!0,this.addChild(this.input),this.btn=this.getButton(),this.btn.text="关闭",this.btn.size(50,20),this.btn.align="center",this.btn.on("mousedown",this,this.onCloseBtn),this.btn.pos(5,this.input.height+5),this.addChild(this.btn)},n.showInfo=function(t){this.input.text=t,this.show()},n.show=function(){Mt.I.setTop(),Mt.I.popLayer.addChild(this),this.x=i.stage.width-this.width,this.y=0},n.onCloseBtn=function(){this.close()},e}()),Ze=(function(t){function e(){this.tab=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.CodeUsedResUI",U);var i=e.prototype;i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){},n(e,["uiView",function(){return this.uiView={type:"View",child:[{props:{x:359,y:91,skin:"comp/button1.png"},type:"Image"},{props:{x:309,y:283,skin:"comp/line2.png"},type:"Image"},{type:"Tab",child:[{props:{skin:"view/create.png",label:"  对象创建",width:70,height:17,name:"item0"},type:"CheckBox"},{props:{x:70,skin:"view/rendertime.png",label:"渲染用时",width:70,height:19,name:"item1"},type:"CheckBox"},{props:{x:140,skin:"view/cache.png",label:"Cache",width:70,height:16,name:"item2"},type:"CheckBox"}],props:{x:76,y:210,selectedIndex:0,var:"tab"}}],props:{width:600,height:400,base64pic:!0}}}])}(),function(t){function e(){e.__super.call(this)}o(e,"laya.debug.ui.debugui.comps.ListItemUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){},n(e,["uiView",function(){return this.uiView={type:"View",props:{base64pic:!0,width:244,height:19},child:[{type:"Clip",props:{y:-1,skin:"comp/clip_selectBox.png",clipY:2,height:19,name:"selectBox",left:2,right:2,x:0}},{type:"Label",props:{x:25,text:"render",color:"#dcea36",width:77,height:17,name:"label",y:2,fontSize:12}},{type:"Clip",props:{skin:"comp/clip_tree_arrow.png",clipY:2,name:"arrow",x:8,y:4,mouseEnabled:!1}}]}}]),e}()),ye=function(t){function e(){e.__super.call(this)}o(e,"laya.debug.ui.debugui.comps.RankListItemUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){},n(e,["uiView",function(){return this.uiView={type:"View",child:[{props:{y:-1,skin:"comp/clip_selectBox.png",clipY:2,height:19,name:"selectBox",left:0,right:0,x:0},type:"Clip"},{props:{text:"render",color:"#a0a0a0",height:15,name:"label",y:2,left:11,right:5,fontSize:12,x:11,width:163},type:"Label"}],props:{width:179,height:19}}}]),e}(),Ge=function(t){function e(){this.bg=null,this.minBtn=null,this.treePanel=null,this.selectWhenClick=null,this.profilePanel=null,this.resizeBtn=null,this.mouseAnalyseBtn=null,this.dragIcon=null,this.clearBtn=null,this.selectPanel=null,this.tab=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.DebugPanelUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){U.regComponent("laya.debug.view.nodeInfo.nodetree.MinBtnComp",De),U.regComponent("laya.debug.view.nodeInfo.views.NodeTreeView",ce),U.regComponent("laya.debug.view.nodeInfo.nodetree.Profile",Ke),U.regComponent("laya.debug.view.nodeInfo.views.SelectInfosView",me)},n(e,["uiView",function(){return this.uiView={type:"View",props:{base64pic:!0,width:260,height:400},child:[{type:"Image",props:{x:205,y:254,skin:"view/bg_panel.png",left:0,right:0,top:0,bottom:0,var:"bg",sizeGrid:"5,5,5,5"}},{type:"Image",props:{y:0,skin:"view/bg_top.png",left:0,right:0}},{type:"MinBtnComp",props:{y:-3,var:"minBtn",runtime:"laya.debug.view.nodeInfo.nodetree.MinBtnComp",right:-3,x:207}},{type:"NodeTree",props:{left:0,right:0,top:32,bottom:0,name:"节点树",var:"treePanel",runtime:"laya.debug.view.nodeInfo.views.NodeTreeView"}},{type:"CheckBox",props:{x:8,y:9,skin:"view/clickselect.png",toolTip:"点击选取",var:"selectWhenClick",mouseEnabled:!0,width:14,height:14}},{type:"Profile",props:{name:"性能",top:32,right:0,left:0,bottom:0,var:"profilePanel",runtime:"laya.debug.view.nodeInfo.nodetree.Profile"}},{type:"Button",props:{x:169,y:247,skin:"view/resize.png",right:2,bottom:2,name:"resizeBtn",var:"resizeBtn",stateNum:3}},{type:"Clip",props:{y:9,skin:"view/clickanalyse.png",var:"mouseAnalyseBtn",toolTip:"拖动选取",left:33,x:33,clipY:3}},{type:"Clip",props:{y:0,skin:"view/clickanalyse.png",var:"dragIcon",x:33,clipY:3}},{type:"Button",props:{y:7,skin:"view/res.png",stateNum:2,toolTip:"清除边框",var:"clearBtn",right:34,x:184}},{type:"SelectInfos",props:{top:32,left:0,right:0,bottom:0,name:"选中",var:"selectPanel",runtime:"laya.debug.view.nodeInfo.views.SelectInfosView"}},{type:"Tab",props:{x:59,y:0,name:"tab",var:"tab",selectedIndex:0},child:[{type:"Button",props:{skin:"view/tab_panel.png",label:"节点",width:42,height:32,name:"item0",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Button",props:{x:42,skin:"view/tab_panel.png",label:"查询",width:42,height:32,name:"item1",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Button",props:{x:84,skin:"view/tab_panel.png",label:"性能",width:42,height:32,name:"item2",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}}]}]}}]),e}(),We=function(t){function e(){this.bg=null,this.closeBtn=null,this.title=null,this.typeSelect=null,this.findTxt=null,this.findBtn=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.FindNodeSmallUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){},n(e,["uiView",function(){return this.uiView={type:"View",child:[{props:{x:185,y:234,skin:"view/bg_tool.png",left:0,right:0,top:0,bottom:0,var:"bg"},type:"Image"},{props:{x:185,y:15,skin:"view/btn_close.png",var:"closeBtn",top:2,right:2},type:"Button"},{props:{x:6,y:4,text:"查找对象",width:67,height:20,color:"#288edf",var:"title"},type:"Label"},{props:{x:60,y:81,skin:"comp/combobox.png",labels:"name,class",width:63,height:21,var:"typeSelect",sizeGrid:"5,35,5,5",labelColors:"#a0a0a0,#fffff,#ffffff#fffff"},type:"ComboBox"},{props:{x:27,y:83,text:"类型",width:27,height:20,color:"#288edf",align:"right"},type:"Label"},{props:{x:7,y:40,text:"包含内容",width:47,height:20,color:"#288edf",align:"right"},type:"Label"},{props:{x:60,y:37,skin:"comp/textinput.png",text:"Sprite",width:164,height:22,var:"findTxt",sizeGrid:"5,5,5,5",color:"#a0a0a0"},type:"TextInput"},{props:{x:158,y:79,skin:"comp/button.png",label:"查找",width:65,height:23,var:"findBtn",mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"},type:"Button"}],props:{base64pic:!0,width:233,height:120}}}]),e}(),ve=function(t){function e(){this.bg=null,this.closeBtn=null,this.title=null,this.typeSelect=null,this.findTxt=null,this.result=null,this.findBtn=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.FindNodeUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){U.regComponent("laya.debug.uicomps.RankListItem",Be)},n(e,["uiView",function(){return this.uiView={type:"View",child:[{props:{x:185,y:234,skin:"view/bg_panel.png",left:0,right:0,top:0,bottom:0,var:"bg",sizeGrid:"5,5,5,5"},type:"Image"},{props:{x:185,y:15,skin:"view/btn_close.png",var:"closeBtn",top:2,right:2},type:"Button"},{props:{x:6,y:4,text:"查找对象",width:67,height:20,color:"#88ef19",var:"title"},type:"Label"},{props:{x:52,y:75,skin:"comp/combobox.png",labels:"name,class",width:63,height:21,var:"typeSelect",sizeGrid:"5,35,5,5",labelColors:"#a0a0a0,#fffff,#ffffff#fffff"},type:"ComboBox"},{props:{x:10,y:77,text:"类型",width:27,height:20,color:"#88ef19",align:"right"},type:"Label"},{props:{x:7,y:34,text:"包含内容",width:47,height:20,color:"#88ef19",align:"right"},type:"Label"},{props:{x:59,y:31,skin:"comp/textinput.png",text:"Sprite",width:131,height:22,var:"findTxt",sizeGrid:"5,5,5,5",color:"#a0a0a0"},type:"TextInput"},{type:"List",child:[{type:"RankListItem",props:{y:30,left:5,right:5,name:"render",x:30,runtime:"laya.debug.uicomps.RankListItem"}}],props:{x:6,y:106,width:188,height:180,vScrollBarSkin:"comp/vscroll.png",var:"result"}},{props:{x:125,y:73,skin:"comp/button.png",label:"查找",width:65,height:23,var:"findBtn",mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"},type:"Button"}],props:{width:200,height:300,base64pic:!0}}}]),e}(),we=function(t){function e(){this.minBtn=null,this.maxUI=null,this.bg=null,this.maxBtn=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.MinBtnCompUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){},n(e,["uiView",function(){return this.uiView={type:"View",child:[{props:{x:7,y:8,skin:"comp/minBtn.png",stateNum:"3",var:"minBtn",width:22,height:20,toolTip:"最小化"},type:"Button"},{type:"Box",child:[{props:{x:0,y:0,skin:"view/bg_panel.png",var:"bg",width:36,height:36,sizeGrid:"5,5,5,5"},type:"Image"},{props:{x:6,y:8,skin:"view/zoom_out.png",stateNum:"2",var:"maxBtn"},type:"Button"}],props:{var:"maxUI"}}],props:{width:36,height:36,base64pic:!0}}}]),e}(),Re=function(t){function e(){this.bg=null,this.closeBtn=null,this.title=null,this.itemList=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.NodeListPanelUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){U.regComponent("laya.debug.uicomps.RankListItem",Be)},n(e,["uiView",function(){return this.uiView={type:"View",child:[{props:{x:235,y:284,skin:"view/bg_panel.png",left:0,right:0,top:0,bottom:0,var:"bg",sizeGrid:"5,5,5,5"},type:"Image"},{props:{x:204,y:32,skin:"view/btn_close.png",var:"closeBtn",top:2,right:2,visible:!0},type:"Button"},{props:{x:10,y:6,text:"节点信息",width:147,height:16,color:"#288edf",var:"title"},type:"Label"},{type:"List",child:[{type:"RankListItem",props:{left:5,right:5,name:"render",runtime:"laya.debug.uicomps.RankListItem"}}],props:{vScrollBarSkin:"comp/vscroll.png",var:"itemList",left:2,right:2,top:26,bottom:0,repeatX:1,x:20}}],props:{width:200,height:300}}}]),e}(),Ve=function(t){function e(){this.bg=null,this.closeBtn=null,this.tarTxt=null,this.freshBtn=null,this.mouseAnalyseBtn=null,this.dragIcon=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.NodeToolUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){},n(e,["uiView",function(){return this.uiView={type:"View",props:{base64pic:!0,width:200,height:341},child:[{type:"Image",props:{x:195,y:244,skin:"view/bg_panel.png",left:0,right:0,top:0,bottom:0,var:"bg",sizeGrid:"5,5,5,5"}},{type:"Label",props:{x:9,y:5,text:"当前选中对象",width:67,height:16,color:"#a0a0a0"}},{type:"Button",props:{x:195,y:25,skin:"view/btn_close.png",var:"closeBtn",top:2,right:2}},{type:"Label",props:{x:10,y:25,text:"当前对象",width:67,height:16,color:"#a0a0a0",var:"tarTxt"}},{type:"Button",props:{x:15,y:65,skin:"comp/button.png",label:"父链",width:39,height:23,mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Button",props:{x:66,y:65,skin:"comp/button.png",label:"子",width:35,height:23,mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Button",props:{x:112,y:65,skin:"comp/button.png",label:"兄弟",width:49,height:23,mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Button",props:{x:13,y:117,skin:"comp/button.png",label:"Enable链",mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Button",props:{x:100,y:117,skin:"comp/button.png",label:"Size链",mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Label",props:{x:14,y:97,text:"节点链信息",width:67,height:16,color:"#a0a0a0"}},{type:"Label",props:{x:15,y:45,text:"对象选取",width:67,height:16,color:"#a0a0a0"}},{type:"Label",props:{x:16,y:145,text:"节点显示",width:67,height:16,color:"#a0a0a0"}},{type:"Button",props:{x:13,y:164,skin:"comp/button.png",label:"隐藏旁支",mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Button",props:{x:100,y:164,skin:"comp/button.png",label:"隐藏兄弟",mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Button",props:{x:13,y:197,skin:"comp/button.png",label:"隐藏子",mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Button",props:{x:99,y:197,skin:"comp/button.png",label:"恢复",mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Label",props:{x:15,y:228,text:"其他",width:67,height:16,color:"#a0a0a0"}},{type:"Button",props:{x:12,y:247,skin:"comp/button.png",label:"节点树定位",mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Button",props:{x:99,y:247,skin:"comp/button.png",label:"显示边框",mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Label",props:{x:12,y:315,text:"Alt+A分析鼠标能否够点中对象",width:173,height:16,color:"#a0a0a0"}},{type:"Button",props:{x:156,y:1,skin:"view/refresh2.png",var:"freshBtn",left:156,toolTip:"recache节点"}},{type:"Button",props:{x:12,y:279,skin:"comp/button.png",label:"输出到控制台",mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Button",props:{x:99,y:279,skin:"comp/button.png",label:"显示切换",mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"}},{type:"Clip",props:{y:44,skin:"view/clickanalyse.png",var:"mouseAnalyseBtn",toolTip:"拖动到对象上方判断是否能够点中",left:84,x:84,clipY:3}},{type:"Clip",props:{y:35,skin:"view/clickanalyse.png",var:"dragIcon",x:94,clipY:3}}]}}]),e}(),Xe=function(t){function e(){this.bg=null,this.showTxt=null,this.okBtn=null,this.closeBtn=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.NodeTreeSettingUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){},n(e,["uiView",function(){return this.uiView={type:"View",child:[{props:{x:0,y:0,skin:"view/bg_panel.png",left:0,top:0,bottom:0,right:0,var:"bg",width:200,height:300,sizeGrid:"5,5,5,5"},type:"Image"},{props:{x:9,y:7,text:"要显示的属性",width:76,height:16,color:"#ffffff",align:"left"},type:"Label"},{props:{x:6,y:29,skin:"comp/textinput.png",text:"x\\ny\\nwidth\\nheight",width:188,height:230,multiline:!0,var:"showTxt",color:"#a0a0a0",sizeGrid:"5,5,5,5"},type:"TextInput"},{props:{x:57,y:269,skin:"comp/button.png",label:"确定",var:"okBtn",mouseEnabled:"true",labelColors:"#ffffff,#ffffff,#ffffff,#ffffff"},type:"Button"},{props:{x:175,y:5,skin:"view/btn_close.png",var:"closeBtn"},type:"Button"}],props:{base64pic:!0,width:200,height:300}}}]),e}(),Ie=function(t){function e(){this.nodeTree=null,this.controlBar=null,this.settingBtn=null,this.freshBtn=null,this.fliterTxt=null,this.closeBtn=null,this.ifShowProps=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.NodeTreeUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){U.regComponent("laya.debug.uicomps.TreeListItem",Fe)},n(e,["uiView",function(){return this.uiView={type:"View",props:{width:200,height:260,base64pic:!0},child:[{type:"Image",props:{x:-22,y:-47,skin:"view/bg_panel.png",width:211,height:206,left:0,right:0,top:0,bottom:0,sizeGrid:"5,5,5,5"}},{props:{y:0,skin:"view/bg_tool.png",left:0,right:0},type:"Image"},{type:"Tree",props:{x:0,scrollBarSkin:"comp/vscroll.png",width:195,height:229,var:"nodeTree",left:0,right:0,top:38,bottom:20},child:[{type:"ListItem",props:{y:0,name:"render",left:0,right:0,runtime:"laya.debug.uicomps.TreeListItem"}}]},{type:"Box",props:{x:3,y:5,var:"controlBar",left:3,right:3,top:5,height:23},child:[{type:"Button",props:{x:6,skin:"view/setting.png",stateNum:3,var:"settingBtn",toolTip:"设置显示的属性",y:6}},{type:"Button",props:{y:6,skin:"view/refresh.png",var:"freshBtn",left:30,toolTip:"刷新数据"}},{type:"TextInput",props:{y:0,skin:"view/bg_top.png",height:22,var:"fliterTxt",left:53,right:0,color:"#a0a0a0"}},{type:"Button",props:{x:172,y:2,skin:"view/btn_close.png",var:"closeBtn",right:1,visible:!1}}]},{props:{y:243,skin:"comp/checkbox.png",label:"显示属性",var:"ifShowProps",bottom:3,selected:!0,visible:!0,x:2,width:70,height:14,labelColors:"#a0a0a0,#fffff,#ffffff,#fffff"},type:"CheckBox"}]}}]),e}(),xe=function(t){function e(){this.bg=null,this.closeBtn=null,this.itemList=null,this.freshBtn=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.ObjectCreateUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){U.regComponent("laya.debug.uicomps.RankListItem",Be)},n(e,["uiView",function(){return this.uiView={type:"View",child:[{props:{x:215,y:264,skin:"view/bg_panel.png",left:0,right:0,top:0,bottom:0,var:"bg",sizeGrid:"5,5,5,5"},type:"Image"},{props:{x:184,y:12,skin:"view/btn_close.png",var:"closeBtn",top:2,right:2,visible:!1},type:"Button"},{props:{x:11,y:5,text:"对象创建统计",width:83,height:16,color:"#288edf"},type:"Label"},{type:"List",child:[{type:"RankListItem",props:{y:0,left:5,right:5,name:"render",runtime:"laya.debug.uicomps.RankListItem"}}],props:{vScrollBarSkin:"comp/vscroll.png",var:"itemList",top:26,bottom:5,left:5,right:5,repeatX:1}},{props:{y:1,skin:"view/refresh2.png",var:"freshBtn",toolTip:"刷新数据",right:1,x:178},type:"Button"}],props:{width:200,height:300,base64pic:!0}}}]),e}(),Ye=function(t){function e(){this.bg=null,this.title=null,this.showTxt=null,this.closeBtn=null,this.autoUpdate=null,this.settingBtn=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.ObjectInfoUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){},n(e,["uiView",function(){return this.uiView={type:"View",child:[{props:{x:-1,y:0,skin:"view/bg_panel.png",left:-1,right:1,top:0,bottom:0,var:"bg",sizeGrid:"5,5,5,5"},type:"Image"},{props:{x:7,y:5,text:"对象类型",width:67,height:20,color:"#ffffff",var:"title",left:7,right:6},type:"Label"},{props:{x:2,skin:"comp/textinput.png",text:"属性内容",width:196,height:228,left:2,right:2,var:"showTxt",top:25,bottom:20,editable:!1,multiline:!0,sizeGrid:"5,5,5,5",color:"#a0a0a0"},type:"TextArea"},{props:{x:178,y:4,skin:"view/btn_close.png",var:"closeBtn",top:4,right:2},type:"Button"},{props:{skin:"comp/checkbox.png",label:"自动刷新属性",var:"autoUpdate",bottom:2,x:3,labelColors:"#a0a0a0,#fffff,#ffffff,#fffff"},type:"CheckBox"},{props:{x:164,skin:"view/setting.png",stateNum:"3",var:"settingBtn",y:6,top:6,right:24,toolTip:"设置显示属性"},type:"Button"},{props:{x:179,y:257,skin:"view/resize.png",right:2,bottom:2,name:"resizeBtn",stateNum:3},type:"Button"}],props:{base64pic:!0,width:200,height:200}}}]),e}(),Ce=function(t){function e(){this.bg=null,this.txt=null,this.closeBtn=null,this.clearBtn=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.OutPutUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){},n(e,["uiView",function(){return this.uiView={type:"View",props:{width:300,height:200,base64pic:!0},child:[{type:"Image",props:{x:205,y:254,skin:"view/bg_panel.png",left:0,right:0,top:0,bottom:0,var:"bg",sizeGrid:"5,5,5,5"}},{type:"Label",props:{skin:"comp/textarea.png",text:"TextArea",color:"#a0a0a0",var:"txt",left:5,right:5,top:22,bottom:5,mouseEnabled:!0,sizeGrid:"3,3,3,3"}},{type:"Button",props:{x:185,y:15,skin:"view/btn_close.png",var:"closeBtn",top:2,right:2}},{props:{x:253,y:1,skin:"view/re.png",stateNum:"2",var:"clearBtn",right:25},type:"Button"},{props:{x:169,y:247,skin:"view/resize.png",right:2,bottom:2,name:"resizeBtn",stateNum:3},type:"Button"}]}}]),e}(),Le=function(t){function e(){this.renderPanel=null,this.createPanel=null,this.cachePanel=null,this.tab=null,this.resPanel=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.ProfileUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){U.regComponent("laya.debug.view.nodeInfo.views.RenderCostRankView",pe),U.regComponent("laya.debug.view.nodeInfo.views.ObjectCreateView",ue),U.regComponent("laya.debug.view.nodeInfo.views.CacheRankView",ie),U.regComponent("laya.debug.view.nodeInfo.views.ResRankView",fe)},n(e,["uiView",function(){return this.uiView={type:"View",props:{width:260,height:329,base64pic:!0},child:[{type:"Image",props:{y:0,skin:"view/bg_tool.png",right:0,left:0}},{type:"Rank",props:{var:"renderPanel",top:29,runtime:"laya.debug.view.nodeInfo.views.RenderCostRankView",right:0,name:"渲染用时",left:0,bottom:0}},{type:"ObjectCreate",props:{var:"createPanel",top:29,runtime:"laya.debug.view.nodeInfo.views.ObjectCreateView",right:0,name:"对象创建统计",left:0,bottom:0}},{type:"Rank",props:{x:10,var:"cachePanel",top:29,runtime:"laya.debug.view.nodeInfo.views.CacheRankView",right:0,name:"cache用时",left:0,bottom:0}},{type:"Tab",props:{y:9,x:7,width:191,var:"tab",selectedIndex:0,height:19},child:[{type:"CheckBox",props:{y:0,x:0,width:50,skin:"view/create.png",name:"item0",labelColors:"#a0a0a0,#ffffff,#ffffff,#ffffff",label:"  对象",height:17}},{type:"CheckBox",props:{y:0,x:55,width:50,skin:"view/rendertime.png",name:"item1",labelColors:"#a0a0a0,#ffffff,#ffffff,#ffffff",label:" 渲染",height:19}},{type:"CheckBox",props:{y:0,x:110,width:50,skin:"view/cache.png",name:"item2",labelColors:"#a0a0a0,#ffffff,#ffffff,#ffffff",label:" 重绘",height:16}},{type:"CheckBox",props:{y:0,x:165,width:50,skin:"view/cache.png",name:"item3",labelColors:"#a0a0a0,#ffffff,#ffffff,#ffffff",label:" 资源",height:16}}]},{type:"Rank",props:{y:40,x:50,var:"resPanel",top:29,runtime:"laya.debug.view.nodeInfo.views.ResRankView",right:0,name:"资源缓存",left:0,bottom:0}}]}}]),e}(),Ne=function(t){function e(){this.bg=null,this.closeBtn=null,this.title=null,this.itemList=null,this.autoUpdate=null,this.freshBtn=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.RankUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){U.regComponent("laya.debug.uicomps.RankListItem",Be)},n(e,["uiView",function(){return this.uiView={type:"View",child:[{props:{x:225,y:274,skin:"view/bg_panel.png",left:0,right:0,top:0,bottom:0,var:"bg",sizeGrid:"5,5,5,5"},type:"Image"},{props:{x:194,y:22,skin:"view/btn_close.png",var:"closeBtn",top:2,right:2,visible:!1},type:"Button"},{props:{x:8,y:5,text:"渲染用时表(3000ms)",width:109,height:16,color:"#288edf",var:"title"},type:"Label"},{type:"List",child:[{type:"RankListItem",props:{left:5,right:5,name:"render",runtime:"laya.debug.uicomps.RankListItem"}}],props:{vScrollBarSkin:"comp/vscroll.png",var:"itemList",left:2,right:2,top:26,bottom:25,repeatX:1,x:10,y:10}},{props:{skin:"comp/checkbox.png",label:"自动刷新属性",var:"autoUpdate",bottom:3,selected:!1,visible:!0,left:2,labelColors:"#a0a0a0,#fffff,#ffffff,#fffff"},type:"CheckBox"},{props:{y:1,skin:"view/refresh2.png",var:"freshBtn",toolTip:"刷新数据",right:1},type:"Button"}],props:{width:200,height:300}}}]),e}(),Se=function(t){function e(){this.bg=null,this.closeBtn=null,this.selectList=null,this.findBtn=null,this.fliterTxt=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.SelectInfosUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){U.regComponent("laya.debug.uicomps.RankListItem",Be)},n(e,["uiView",function(){return this.uiView={type:"View",child:[{props:{x:205,y:254,skin:"view/bg_panel.png",left:0,right:0,top:0,bottom:0,var:"bg",sizeGrid:"5,5,5,5"},type:"Image"},{props:{skin:"view/btn_close.png",var:"closeBtn",top:32,visible:!1,right:2},type:"Button"},{props:{x:7,y:36,text:"当前选中列表",width:83,height:16,color:"#288edf"},type:"Label"},{type:"List",child:[{type:"RankListItem",props:{left:5,right:5,name:"render",runtime:"laya.debug.uicomps.RankListItem"}}],props:{vScrollBarSkin:"comp/vscroll.png",var:"selectList",left:5,right:5,top:56,bottom:25,repeatX:1,x:20}},{props:{x:6,text:"Alt+V选取鼠标下的对象",width:189,height:16,color:"#a0a0a0",bottom:3},type:"Label"},{type:"Image",props:{y:0,skin:"view/bg_tool.png",left:0,right:0}},{type:"Clip",props:{y:6,skin:"view/search.png",clipY:2,var:"findBtn",right:5,toolTip:"查找",x:174}},{type:"TextInput",props:{y:6,skin:"view/bg_top.png",height:22,var:"fliterTxt",left:8,right:45,color:"#a0a0a0",x:8,width:147}}],props:{width:200,height:300,base64pic:!0}}}]),e}(),ke=function(t){function e(){this.bg=null,this.treeBtn=null,this.findBtn=null,this.minBtn=null,this.selectWhenClick=null,this.clearBtn=null,this.rankBtn=null,this.nodeRankBtn=null,this.cacheBtn=null,e.__super.call(this)}o(e,"laya.debug.ui.debugui.ToolBarUI",U);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists(),laya.ui.Component.prototype.createChildren.call(this),this.createView(e.uiView)},i.viewMapRegists=function(){U.regComponent("laya.debug.view.nodeInfo.nodetree.MinBtnComp",De)},n(e,["uiView",function(){return this.uiView={type:"View",props:{base64pic:!0,width:250,height:30},child:[{type:"Image",props:{x:195,y:244,skin:"view/bg_panel.png",left:0,right:0,top:0,bottom:0,var:"bg",sizeGrid:"5,5,5,5"}},{type:"Button",props:{x:2,y:6,skin:"view/save.png",stateNum:2,var:"treeBtn",toolTip:"节点树"}},{type:"Button",props:{x:25,y:6,skin:"view/save.png",stateNum:2,var:"findBtn",toolTip:"查找面板"}},{type:"MinBtnComp",props:{x:218,y:-3,var:"minBtn",runtime:"laya.debug.view.nodeInfo.nodetree.MinBtnComp"}},{type:"CheckBox",props:{x:124,y:8,skin:"comp/checkbox.png",label:"点击选取",var:"selectWhenClick",labelColors:"#a0a0a0,#fffff,#ffffff,#fffff"}},{type:"Button",props:{x:193,y:5,skin:"view/res.png",stateNum:2,toolTip:"清除边框",var:"clearBtn"}},{type:"Button",props:{x:49,y:6,skin:"view/save.png",stateNum:2,var:"rankBtn",toolTip:"渲染用时排行"}},{type:"Button",props:{x:72,y:6,skin:"view/save.png",stateNum:2,var:"nodeRankBtn",toolTip:"创建对象排行"}},{type:"Button",props:{x:94,y:6,skin:"view/save.png",stateNum:2,var:"cacheBtn",toolTip:"cache对象"}}]}}]),e}(),Te=function(t){function e(){e.__super.call(this)}o(e,"laya.debug.uicomps.ListBase",t);return a(0,e.prototype,"selectedIndex",t.prototype._$get_selectedIndex,function(t){if(this._selectedIndex!=t&&(this._selectedIndex=t,this.changeSelectStatus(),this.event("change"),this.selectHandler&&this.selectHandler.runWith(t)),this.selectEnable&&this._scrollBar){var e=this._isVertical?this.repeatX:this.repeatY;(t<this._startIndex||t+e>this._startIndex+this.repeatX*this.repeatY)&&this.scrollTo(t)}}),e}(y),Ae=function(t){function e(){e.__super.call(this)}o(e,"laya.debug.uicomps.TreeBase",F);return e.prototype.createChildren=function(){this.addChild(this._list=new Te),this._list.renderHandler=m.create(this,this.renderItem,null,!1),this._list.repeatX=1,this._list.on("change",this,this.onListChange)},e}(),Be=function(t){function e(){e.__super.call(this),D.replaceRes(ye.uiView),this.createView(ye.uiView)}o(e,"laya.debug.uicomps.RankListItem",ye);return e.prototype.createChildren=function(){},e}(),Fe=function(t){function e(){e.__super.call(this),D.replaceRes(Ze.uiView),this.createView(Ze.uiView)}o(e,"laya.debug.uicomps.TreeListItem",Ze);return e.prototype.createChildren=function(){},e}(),Je=function(t){function e(){this.views=null,e.__super.call(this),this.msRec=new x,D.replaceRes(Ge.uiView),this.createView(Ge.uiView),lt.setResizeAbleEx(this),this.views=[this.treePanel,this.selectPanel,this.profilePanel],this.tab.selectedIndex=0,this.tabChange(),this.tab.on("change",this,this.tabChange),this.changeSize()}o(e,"laya.debug.view.nodeInfo.nodetree.DebugPage",Ge);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists()},i.tabChange=function(){lt.addOnlyByIndex(this.views,this.tab.selectedIndex,this),lt.setTop(this.resizeBtn)},i.changeSize=function(){this.width<245&&(this.width=245),this.height<100&&(this.height=200),laya.ui.Component.prototype.changeSize.call(this),this.msRec.setTo(0,0,this.width,this.height),this.scrollRect=this.msRec},e}(),He=function(t){function e(){e.__super.call(this),D.replaceRes(We.uiView),this.createView(We.uiView)}o(e,"laya.debug.view.nodeInfo.nodetree.FindNodeSmall",We);return e.prototype.createChildren=function(){},e}(),Ue=function(t){function e(){e.__super.call(this),D.replaceRes(ve.uiView),this.createView(ve.uiView)}o(e,"laya.debug.view.nodeInfo.nodetree.FindNode",ve);return e.prototype.createChildren=function(){this.viewMapRegists()},e}(),De=function(t){function e(){this.tar=null,this.minHandler=null,this.maxHandler=null,this.prePos=new X,e.__super.call(this),D.replaceRes(we.uiView),this.createView(we.uiView),this.init()}o(e,"laya.debug.view.nodeInfo.nodetree.MinBtnComp",we);var i=e.prototype;return i.createChildren=function(){},i.init=function(){this.minBtn.on("click",this,this.onMinBtn),this.maxBtn.on("click",this,this.onMaxBtn),this.minState=!1,this.maxUI.removeSelf(),lt.setDragingItem(this.bg,this.maxUI)},i.onMaxBtn=function(){this.maxUI.removeSelf(),this.maxHandler&&this.maxHandler.run(),this.tar&&(this.tar.x+=this.maxUI.x-this.prePos.x,this.tar.y+=this.maxUI.y-this.prePos.y)},i.onMinBtn=function(){if(this.displayedInStage){var t;(t=X.TEMP).setTo(0,0),t=this.localToGlobal(t),t=Mt.I.popLayer.globalToLocal(t),this.maxUI.pos(t.x,t.y),Mt.I.popLayer.addChild(this.maxUI),this.tar&&this.prePos.setTo(t.x,t.y),this.minHandler&&this.minHandler.run()}},a(0,i,"minState",null,function(t){}),e}(),Me=function(t){function e(){e.__super.call(this),D.replaceRes(Re.uiView),this.createView(Re.uiView)}o(e,"laya.debug.view.nodeInfo.nodetree.NodeListPanel",Re);return e.prototype.createChildren=function(){this.viewMapRegists()},e}(),je=function(t){function e(){e.__super.call(this),D.replaceRes(Ve.uiView),this.createView(Ve.uiView)}o(e,"laya.debug.view.nodeInfo.nodetree.NodeTool",Ve);return e.prototype.createChildren=function(){},e}(),ze=function(t){function e(){e.__super.call(this),D.replaceRes(Xe.uiView),this.createView(Xe.uiView)}o(e,"laya.debug.view.nodeInfo.nodetree.NodeTreeSetting",Xe);return e.prototype.createChildren=function(){},e}(),Ee=function(t){function e(){this.showProps=!1,this._closeSettingHandler=null,this._tar=null,e.__super.call(this),D.replaceRes(Ie.uiView),U.regComponent("Tree",Ae),this.createView(Ie.uiView),U.regComponent("Tree",F),this.inits(),e.I=this}o(e,"laya.debug.view.nodeInfo.nodetree.NodeTree",Ie);var a=e.prototype;return a.createChildren=function(){this.viewMapRegists()},a.inits=function(){this.nodeTree.list.scrollBar.hide=!0,this.nodeTree.list.selectEnable=!0,this.settingBtn.on("click",this,this.onSettingBtn),this.freshBtn.on("click",this,this.fresh),this.closeBtn.on("click",this,this.onCloseBtn),this.fliterTxt.on("enter",this,this.onFliterTxtChange),this.fliterTxt.on("blur",this,this.onFliterTxtChange),kt.I.setNodeListAction(this.nodeTree.list),this.nodeTree.list.on("click",this,this.onListClick,[this.nodeTree.list]),this.nodeTree.renderHandler=new m(this,this.treeRender),this._closeSettingHandler=new m(this,this.closeSetting),this.onIfShowPropsChange(),this.ifShowProps.on("change",this,this.onIfShowPropsChange)},a.onIfShowPropsChange=function(){this.showProps=this.ifShowProps.selected,this.fresh()},a.onListClick=function(t){t.selectedItem&&t.selectedItem.isDirectory&&(t.selectedItem.isOpen=!t.selectedItem.isOpen,this.nodeTree.fresh())},a.onFindBtn=function(){ae.I.show()},a.onCloseBtn=function(){this.removeSelf()},a.onTreeDoubleClick=function(t){if(this.nodeTree.selectedItem){var e;e=this.nodeTree.selectedItem.path,kt.I.objRightClick(e)}},a.onTreeRightMouseDown=function(t){if(this.nodeTree.selectedItem){var e;e=this.nodeTree.selectedItem.path,kt.I.objRightClick(e)}},a.onSettingBtn=function(){he.I.showSetting(e.showKeys,this._closeSettingHandler,this._tar)},a.closeSetting=function(t){e.showKeys=t,this.fresh()},a.onFliterTxtChange=function(t){var i;""!=(i=this.fliterTxt.text)&&i!=e.showKeys.join(",")&&(e.showKeys=i.split(","),this.fresh())},a.selecteByFile=function(t){var e;e=this.nodeTree.source;var i;if((i=M.findNameHas(t,!1))&&i.length>0){var n;n=i[0],this.parseOpen(e,n)}},a.showSelectInStage=function(t){this.setDis(i.stage),this.selectByNode(t)},a.selectByNode=function(t){if(t){var e;e=this.nodeTree.source,this.parseOpen(e,t)}},a.showNodeList=function(t){if(t){var e=0,i=0;i=t.length;var n;n=[];var o,a;for(e=0;e<i;e++)a=t[e],(o={}).label=P.getNodeClassAndName(a),o.path=a,n.push(o);this.nodeTree.array=n}},a.parseOpen=function(t,e){if(!(t.length<1)&&e){var i=0,n=0;n=t.length;var o;for(i=0;i<n;i++)if((o=t[i]).path==e){var a;for(a=o;o;)o.isOpen=!0,this.nodeTree.fresh(),o=o.nodeParent;return void(this.nodeTree.selectedItem=a)}}},a.getFilterSource=function(t,e,i){i=i.toLocaleLowerCase();var n;for(var o in t)(n=t[o]).isDirectory&&String(n.label).toLowerCase().indexOf(i)>-1&&(n.x=0,e.push(n)),n.child&&n.child.length>0&&this.getFilterSource(n.child,e,i)},a.onControlDown=function(){this.startDrag()},a.setDis=function(t){this._tar=t,this.fresh()},a.fresh=function(){var t;if(this.nodeTree.selectedItem){var i;for(i=this.nodeTree.selectedItem;i&&!(i.path instanceof laya.display.Sprite);)i=i.nodeParent;i&&i.path&&(t=i.path)}this._tar?this.nodeTree.array=At.getNodeTreeData(this._tar,this.showProps?e.showKeys:e.emptyShowKey):this.nodeTree.array=[],t&&this.selectByNode(t)},a.treeRender=function(t,e){var i=t.dataSource;if(i){i.child||i.isDirectory;var n=t.getChildByName("label");i.path instanceof laya.display.Node?n.color="#09a4f6":i.isChilds?n.color="#00ff11":n.color="#838bc5"}},e.I=null,e.emptyShowKey=[],n(e,["showKeys",function(){return this.showKeys=["x","y","width","height","renderCost"]}]),e}(),Qe=function(t){function e(){e.__super.call(this),D.replaceRes(xe.uiView),this.createView(xe.uiView)}o(e,"laya.debug.view.nodeInfo.nodetree.ObjectCreate",xe);return e.prototype.createChildren=function(){this.viewMapRegists()},e}(),_e=function(t){function e(){e.__super.call(this),D.replaceRes(Ye.uiView),this.createView(Ye.uiView)}o(e,"laya.debug.view.nodeInfo.nodetree.ObjectInfo",Ye);return e.prototype.createChildren=function(){},e}(),Oe=function(t){function e(){e.__super.call(this),D.replaceRes(Ce.uiView),this.createView(Ce.uiView)}o(e,"laya.debug.view.nodeInfo.nodetree.OutPut",Ce);return e.prototype.createChildren=function(){},e}(),Ke=function(t){function e(){this.views=null,e.__super.call(this),D.replaceRes(Le.uiView),this.createView(Le.uiView),this.views=[this.createPanel,this.renderPanel,this.cachePanel,this.resPanel],this.tab.selectedIndex=0,this.tabChange(),this.tab.on("change",this,this.tabChange)}o(e,"laya.debug.view.nodeInfo.nodetree.Profile",Le);var i=e.prototype;return i.createChildren=function(){this.viewMapRegists()},i.tabChange=function(){lt.addOnlyByIndex(this.views,this.tab.selectedIndex,this)},e}(),Pe=function(t){function e(){e.__super.call(this),D.replaceRes(Ne.uiView),this.createView(Ne.uiView)}o(e,"laya.debug.view.nodeInfo.nodetree.Rank",Ne);return e.prototype.createChildren=function(){this.viewMapRegists()},e}(),qe=function(t){function e(){e.__super.call(this),D.replaceRes(Se.uiView),this.createView(Se.uiView)}o(e,"laya.debug.view.nodeInfo.nodetree.SelectInfos",Se);return e.prototype.createChildren=function(){this.viewMapRegists()},e}(),$e=function(t){function e(){e.__super.call(this),D.replaceRes(ke.uiView),this.createView(ke.uiView)}o(e,"laya.debug.view.nodeInfo.nodetree.ToolBar",ke);return e.prototype.createChildren=function(){},e}()}(window,document,Laya),"function"==typeof define&&define.amd&&define("laya.core",["require","exports"],function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});for(var i in Laya){var n=Laya[i];n&&n.__isclass&&(e[i]=n)}});