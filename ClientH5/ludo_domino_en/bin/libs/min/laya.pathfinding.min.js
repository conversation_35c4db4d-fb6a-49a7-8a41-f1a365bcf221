!function(t,e,i){var n=(i.un,i.uns,i.static,i.class),s=(i.getset,i.__newvec,function(){function t(){}return n(t,"PathFinding.core.DiagonalMovement"),t.Always=1,t.Never=2,t.IfAtMostOneObstacle=3,t.OnlyWhenNoObstacles=4,t}()),a=function(){function t(t,e,i){this.width=0,this.height=0,this.nodes=null;var n=0;"number"==typeof t?n=t:(e=t.length,n=t[0].length,i=t),this.width=n,this.height=e,this.nodes=this._buildNodes(n,e,i)}n(t,"PathFinding.core.Grid");var e=t.prototype;return e._buildNodes=function(t,e,i){var n=0,s=0,a=[];for(n=0;n<e;++n)for(a[n]=[],s=0;s<t;++s)a[n][s]=new r(s,n);if(null==i)return a;if(i.length!=e||i[0].length!=t)throw new Error("Matrix size does not fit");for(n=0;n<e;++n)for(s=0;s<t;++s)i[n][s]&&(a[n][s].walkable=!1);return a},e.getNodeAt=function(t,e){return this.nodes[e][t]},e.isWalkableAt=function(t,e){return this.isInside(t,e)&&this.nodes[e][t].walkable},e.isInside=function(t,e){return t>=0&&t<this.width&&e>=0&&e<this.height},e.setWalkableAt=function(t,e,i){this.nodes[e][t].walkable=i},e.getNeighbors=function(t,e){var i=t.x,n=t.y,a=[],o=!1,r=!1,h=!1,l=!1,u=!1,d=!1,f=!1,c=!1,p=this.nodes;if(this.isWalkableAt(i,n-1)&&(a.push(p[n-1][i]),o=!0),this.isWalkableAt(i+1,n)&&(a.push(p[n][i+1]),h=!0),this.isWalkableAt(i,n+1)&&(a.push(p[n+1][i]),u=!0),this.isWalkableAt(i-1,n)&&(a.push(p[n][i-1]),f=!0),e==s.Never)return a;if(e==s.OnlyWhenNoObstacles)r=f&&o,l=o&&h,d=h&&u,c=u&&f;else if(e==s.IfAtMostOneObstacle)r=f||o,l=o||h,d=h||u,c=u||f;else{if(e!=s.Always)throw new Error("Incorrect value of diagonalMovement");r=!0,l=!0,d=!0,c=!0}return r&&this.isWalkableAt(i-1,n-1)&&a.push(p[n-1][i-1]),l&&this.isWalkableAt(i+1,n-1)&&a.push(p[n-1][i+1]),d&&this.isWalkableAt(i+1,n+1)&&a.push(p[n+1][i+1]),c&&this.isWalkableAt(i-1,n+1)&&a.push(p[n+1][i-1]),a},e.clone=function(){var e=0,i=0,n=this.width,s=this.height,a=this.nodes,o=new t(n,s),h=[];for(e=0;e<s;++e)for(h[e]=[],i=0;i<n;++i)h[e][i]=new r(i,e,a[e][i].walkable);return o.nodes=h,o},e.reset=function(){for(var t,e=0;e<this.height;++e)for(var i=0;i<this.width;++i)t=this.nodes[e][i],t.g=0,t.f=0,t.h=0,t.by=0,t.parent=null,t.opened=null,t.closed=null,t.tested=null},t}(),o=function(){function t(){}return n(t,"PathFinding.core.Heuristic"),t.manhattan=function(t,e){return t+e},t.euclidean=function(t,e){return Math.sqrt(t*t+e*e)},t.octile=function(t,e){var i=Math.SQRT2-1;return t<e?i*t+e:i*e+t},t.chebyshev=function(t,e){return Math.max(t,e)},t}(),r=function(){function t(t,e,i){this.x=0,this.y=0,this.g=0,this.f=0,this.h=0,this.by=0,this.parent=null,this.opened=null,this.closed=null,this.tested=null,this.walkable=!1,void 0===i&&(i=!0),this.x=t,this.y=e,this.walkable=i}return n(t,"PathFinding.core.Node",null,"Node1"),t}(),h=function(){function t(){}return n(t,"PathFinding.core.Util"),t.backtrace=function(t){for(var e=[[t.x,t.y]];t.parent;)t=t.parent,e.push([t.x,t.y]);return e.reverse()},t.biBacktrace=function(e,i){var n=t.backtrace(e),s=t.backtrace(i);return n.concat(s.reverse())},t.pathLength=function(t){var e=0,i=0,n=0,s=0,a=0,o=0;for(e=1;e<t.length;++e)n=t[e-1],s=t[e],a=n[0]-s[0],o=n[1]-s[1],i+=Math.sqrt(a*a+o*o);return i},t.interpolate=function(t,e,i,n){var s=Math.abs,a=[],o=0,r=0,h=0,l=0,u=0,d=0;for(h=s(i-t),l=s(n-e),o=t<i?1:-1,r=e<n?1:-1,u=h-l;;){if(a.push([t,e]),t==i&&e==n)break;d=2*u,d>-l&&(u-=l,t+=o),d<h&&(u+=h,e+=r)}return a},t.expandPath=function(e){var i,n,s,a=[],o=e.length,r=0,h=0,l=0;if(o<2)return a;for(h=0;h<o-1;++h)for(i=e[h],n=e[h+1],s=t.interpolate(i[0],i[1],n[0],n[1]),r=s.length,l=0;l<r-1;++l)a.push(s[l]);return a.push(e[o-1]),a},t.smoothenPath=function(e,i){var n,s,a,o,r,h=i.length,l=i[0][0],u=i[0][1],d=i[h-1][0],f=i[h-1][1],c=0,p=0,g=0,b=0,m=0,v=0,A=!1;for(c=l,p=u,n=[[c,p]],m=2;m<h;++m){for(s=i[m],g=s[0],b=s[1],a=t.interpolate(c,p,g,b),A=!1,v=1;v<a.length;++v)if(o=a[v],!e.isWalkableAt(o[0],o[1])){A=!0;break}A&&(r=i[m-1],n.push(r),c=r[0],p=r[1])}return n.push([d,f]),n},t.compressPath=function(t){if(t.length<3)return t;var e=[],i=t[0][0],n=t[0][1],s=t[1][0],a=t[1][1],o=s-i,r=a-n,h=0,l=0,u=0,d=0,f=NaN,c=0;for(f=Math.sqrt(o*o+r*r),o/=f,r/=f,e.push([i,n]),c=2;c<t.length;c++)h=s,l=a,u=o,d=r,s=t[c][0],a=t[c][1],o=s-h,r=a-l,f=Math.sqrt(o*o+r*r),o/=f,r/=f,o===u&&r===d||e.push([h,l]);return e.push([s,a]),e},t}(),l=function(){function t(t){this.allowDiagonal=!1,this.dontCrossCorners=!1,this.heuristic=null,this.weight=0,this.diagonalMovement=0,t=t||{},this.allowDiagonal=t.allowDiagonal,this.dontCrossCorners=t.dontCrossCorners,this.heuristic=t.heuristic||o.manhattan,this.weight=t.weight||1,this.diagonalMovement=t.diagonalMovement,this.diagonalMovement||(this.allowDiagonal?this.dontCrossCorners?this.diagonalMovement=s.OnlyWhenNoObstacles:this.diagonalMovement=s.IfAtMostOneObstacle:this.diagonalMovement=s.Never),this.diagonalMovement===s.Never?this.heuristic=t.heuristic||o.manhattan:this.heuristic=t.heuristic||o.octile}return n(t,"PathFinding.finders.AStarFinder"),t.prototype.findPath=function(t,e,i,n,s){var a,o,r,l=new f(function(t,e){return t.f-e.f}),u=s.getNodeAt(t,e),d=s.getNodeAt(i,n),c=this.heuristic,p=this.diagonalMovement,g=this.weight,b=Math.abs,m=Math.SQRT2,v=0,A=0,k=0,W=0,M=0;for(u.g=0,u.f=0,l.push(u),u.opened=!0;!l.empty();){if(a=l.pop(),a.closed=!0,a===d)return h.backtrace(d);for(o=s.getNeighbors(a,p),v=0,A=o.length;v<A;++v)r=o[v],r.closed||(k=r.x,W=r.y,M=a.g+(k-a.x==0||W-a.y==0?1:m),(!r.opened||M<r.g)&&(r.g=M,r.h=r.h||g*c(b(k-i),b(W-n)),r.f=r.g+r.h,r.parent=a,r.opened?l.updateItem(r):(l.push(r),r.opened=!0)))}return[]},t}(),u=function(){function t(t){this.allowDiagonal=!1,this.dontCrossCorners=!1,this.diagonalMovement=0,this.heuristic=null,this.weight=0,t=t||{},this.allowDiagonal=t.allowDiagonal,this.dontCrossCorners=t.dontCrossCorners,this.diagonalMovement=t.diagonalMovement,this.heuristic=t.heuristic||o.manhattan,this.weight=t.weight||1,this.diagonalMovement||(this.allowDiagonal?this.dontCrossCorners?this.diagonalMovement=s.OnlyWhenNoObstacles:this.diagonalMovement=s.IfAtMostOneObstacle:this.diagonalMovement=s.Never),this.diagonalMovement==s.Never?this.heuristic=t.heuristic||o.manhattan:this.heuristic=t.heuristic||o.octile}return n(t,"PathFinding.finders.BiAStarFinder"),t.prototype.findPath=function(t,e,i,n,s){var a,o,r,l=function(t,e){return t.f-e.f},u=new f(l),d=new f(l),c=s.getNodeAt(t,e),p=s.getNodeAt(i,n),g=this.heuristic,b=this.diagonalMovement,m=this.weight,v=Math.abs,A=Math.SQRT2,k=0,W=0,M=0,y=0,w=0;for(c.g=0,c.f=0,u.push(c),c.opened=1,p.g=0,p.f=0,d.push(p),p.opened=2;!u.empty()&&!d.empty();){for(a=u.pop(),a.closed=!0,o=s.getNeighbors(a,b),k=0,W=o.length;k<W;++k)if(r=o[k],!r.closed){if(2===r.opened)return h.biBacktrace(a,r);M=r.x,y=r.y,w=a.g+(M-a.x==0||y-a.y==0?1:A),(!r.opened||w<r.g)&&(r.g=w,r.h=r.h||m*g(v(M-i),v(y-n)),r.f=r.g+r.h,r.parent=a,r.opened?u.updateItem(r):(u.push(r),r.opened=1))}for(a=d.pop(),a.closed=!0,o=s.getNeighbors(a,b),k=0,W=o.length;k<W;++k)if(r=o[k],!r.closed){if(1===r.opened)return h.biBacktrace(r,a);M=r.x,y=r.y,w=a.g+(M-a.x==0||y-a.y==0?1:A),(!r.opened||w<r.g)&&(r.g=w,r.h=r.h||m*g(v(M-t),v(y-e)),r.f=r.g+r.h,r.parent=a,r.opened?d.updateItem(r):(d.push(r),r.opened=2))}}return[]},t}(),d=(function(){function t(t){this.allowDiagonal=!1,this.dontCrossCorners=!1,this.heuristic=null,this.weight=0,this.diagonalMovement=0,t=t||{},this.allowDiagonal=t.allowDiagonal,this.dontCrossCorners=t.dontCrossCorners,this.diagonalMovement=t.diagonalMovement,this.diagonalMovement||(this.allowDiagonal?this.dontCrossCorners?this.diagonalMovement=s.OnlyWhenNoObstacles:this.diagonalMovement=s.IfAtMostOneObstacle:this.diagonalMovement=s.Never)}n(t,"PathFinding.finders.BiBreadthFirstFinder"),t.prototype.findPath=function(t,e,i,n,s){var a,o,r,l=s.getNodeAt(t,e),u=s.getNodeAt(i,n),d=[],f=[],c=this.diagonalMovement,p=0,g=0;for(d.push(l),l.opened=!0,l.by=0,f.push(u),u.opened=!0,u.by=1;d.length&&f.length;){for(r=d.shift(),r.closed=!0,a=s.getNeighbors(r,c),p=0,g=a.length;p<g;++p)if(o=a[p],!o.closed)if(o.opened){if(1===o.by)return h.biBacktrace(r,o)}else d.push(o),o.parent=r,o.opened=!0,o.by=0;for(r=f.shift(),r.closed=!0,a=s.getNeighbors(r,c),p=0,g=a.length;p<g;++p)if(o=a[p],!o.closed)if(o.opened){if(0===o.by)return h.biBacktrace(o,r)}else f.push(o),o.parent=r,o.opened=!0,o.by=1}return[]},t}(),function(){function t(t){this.allowDiagonal=!1,this.dontCrossCorners=!1,this.heuristic=null,this.weight=0,this.diagonalMovement=0,t=t||{},this.allowDiagonal=t.allowDiagonal,this.dontCrossCorners=t.dontCrossCorners,this.diagonalMovement=t.diagonalMovement,this.diagonalMovement||(this.allowDiagonal?this.dontCrossCorners?this.diagonalMovement=s.OnlyWhenNoObstacles:this.diagonalMovement=s.IfAtMostOneObstacle:this.diagonalMovement=s.Never)}n(t,"PathFinding.finders.BreadthFirstFinder"),t.prototype.findPath=function(t,e,i,n,s){var a,o,r,l=[],u=this.diagonalMovement,d=s.getNodeAt(t,e),f=s.getNodeAt(i,n),c=0,p=0;for(l.push(d),d.opened=!0;l.length;){if(r=l.shift(),r.closed=!0,r===f)return h.backtrace(f);for(a=s.getNeighbors(r,u),c=0,p=a.length;c<p;++c)o=a[c],o.closed||o.opened||(l.push(o),o.opened=!0,o.parent=r)}return[]},t}(),function(){function t(t){this.allowDiagonal=!1,this.dontCrossCorners=!1,this.heuristic=null,this.weight=0,this.diagonalMovement=0,this.trackRecursion=!1,this.timeLimit=NaN,t=t||{},this.allowDiagonal=t.allowDiagonal,this.dontCrossCorners=t.dontCrossCorners,this.diagonalMovement=t.diagonalMovement,this.heuristic=t.heuristic||o.manhattan,this.weight=t.weight||1,this.trackRecursion=t.trackRecursion||!1,this.timeLimit=t.timeLimit||1/0,this.diagonalMovement||(this.allowDiagonal?this.dontCrossCorners?this.diagonalMovement=s.OnlyWhenNoObstacles:this.diagonalMovement=s.IfAtMostOneObstacle:this.diagonalMovement=s.Never),this.diagonalMovement===s.Never?this.heuristic=t.heuristic||o.manhattan:this.heuristic=t.heuristic||o.octile}n(t,"PathFinding.finders.IDAStarFinder"),t.prototype.findPath=function(t,e,i,n,s){var a,o=this,r=0,h=(new Date).getTime(),l=function(t,e){return o.heuristic(Math.abs(e.x-t.x),Math.abs(e.y-t.y))}.bind(this),u=function(t,e){return t.x===e.x||t.y===e.y?1:Math.SQRT2},d=function(t,e,i,n,a){if(r++,o.timeLimit>0&&(new Date).getTime()-h>1e3*o.timeLimit)return 1/0;var f=e+l(t,c)*o.weight;if(f>i)return f;if(t==c)return n[a]=[t.x,t.y],t;var p,g=0,b=0,m=0,v=s.getNeighbors(t,o.diagonalMovement);for(m=0,g=1/0;p=v[m];++m){if(o.trackRecursion&&(p.retainCount=p.retainCount+1||1,1!=p.tested&&(p.tested=!0)),(b=d(p,e+u(t,p),i,n,a+1))instanceof PathFinding.core.Node)return n[a]=[t.x,t.y],b;o.trackRecursion&&0==--p.retainCount&&(p.tested=!1),b<g&&(g=b)}return g}.bind(this),f=s.getNodeAt(t,e),c=s.getNodeAt(i,n),p=l(f,c),g=0,b=0;for(g=0;!0;++g){if(a=[],(b=d(f,0,p,a,0))==1/0)return[];if(b instanceof PathFinding.core.Node)return a;p=b}},t}(),function(){function t(t){this.grid=null,this.openList=null,this.startNode=null,this.endNode=null,this.heuristic=null,this.trackJumpRecursion=!1,t=t||{},this.heuristic=t.heuristic||o.manhattan,this.trackJumpRecursion=t.trackJumpRecursion||!1}n(t,"PathFinding.finders.JumpPointFinderBase");var e=t.prototype;return e.findPath=function(t,e,i,n,s){var a,o=this.openList=new f(function(t,e){return t.f-e.f}),r=this.startNode=s.getNodeAt(t,e),l=this.endNode=s.getNodeAt(i,n);for(this.grid=s,r.g=0,r.f=0,o.push(r),r.opened=!0;!o.empty();){if(a=o.pop(),a.closed=!0,a==l)return h.expandPath(h.backtrace(l));this._identifySuccessors(a)}return[]},e._identifySuccessors=function(t){var e,i,n,s,a=this.grid,r=this.heuristic,h=this.openList,l=this.endNode.x,u=this.endNode.y,d=0,f=0,c=t.x,p=t.y,g=0,b=0,m=0,v=0,A=Math.abs;Math.max;for(e=this._findNeighbors(t),d=0,f=e.length;d<f;++d)if(i=e[d],n=this._jump(i[0],i[1],c,p)){if(g=n[0],b=n[1],s=a.getNodeAt(g,b),s.closed)continue;m=o.octile(A(g-c),A(b-p)),v=t.g+m,(!s.opened||v<s.g)&&(s.g=v,s.h=s.h||r(A(g-l),A(b-u)),s.f=s.g+s.h,s.parent=t,s.opened?h.updateItem(s):(h.push(s),s.opened=!0))}},e._jump=function(t,e,i,n){},e._findNeighbors=function(t){},t}()),f=(function(){function t(t){return t=t||{},t.diagonalMovement===s.Never?new m(t):t.diagonalMovement===s.Always?new p(t):t.diagonalMovement===s.OnlyWhenNoObstacles?new b(t):new g(t)}n(t,"PathFinding.finders.JumpPointFinder"),t}(),function(){function t(t){this.allowDiagonal=!1,this.dontCrossCorners=!1,this.diagonalMovement=0,this.heuristic=null,t=t||{},this.allowDiagonal=t.allowDiagonal,this.dontCrossCorners=t.dontCrossCorners,this.heuristic=t.heuristic||o.manhattan,this.diagonalMovement=t.diagonalMovement,this.diagonalMovement||(this.allowDiagonal?this.dontCrossCorners?this.diagonalMovement=s.OnlyWhenNoObstacles:this.diagonalMovement=s.IfAtMostOneObstacle:this.diagonalMovement=s.Never),this.diagonalMovement==s.Never?this.heuristic=t.heuristic||o.manhattan:this.heuristic=t.heuristic||o.octile}n(t,"PathFinding.finders.TraceFinder"),t.prototype.findPath=function(t,e,i,n,s){var a,o,r,l=new f(function(t,e){return t.f-e.f}),u=s.getNodeAt(t,e),d=s.getNodeAt(i,n),c=this.heuristic,p=(this.allowDiagonal,this.dontCrossCorners,Math.abs),g=Math.SQRT2,b=0,m=0,v=0,A=0,k=0;for(u.g=0,u.f=0,l.push(u),u.opened=!0;!l.empty();){if(a=l.pop(),a.closed=!0,a===d)return h.backtrace(d);for(o=s.getNeighbors(a,this.diagonalMovement),b=0,m=o.length;b<m;++b)r=o[b],r.closed||(v=r.x,A=r.y,k=a.g+(v-a.x==0||A-a.y==0?1:g),(!r.opened||k<r.g)&&(r.g=k*m/9,r.h=r.h||c(p(v-i),p(A-n)),r.f=r.g+r.h,r.parent=a,r.opened?l.updateItem(r):(l.push(r),r.opened=!0)))}return[]},t}(),function(){function t(t){this.cmp=null,this.nodes=null,this.heapFunction=new c,this.cmp=null!=t?t:this.heapFunction.defaultCmp,this.nodes=[]}n(t,"PathFinding.libs.Heap");var e=t.prototype;return e.push=function(t){return this.heapFunction.heappush(this.nodes,t,this.cmp)},e.pop=function(){return this.heapFunction.heappop(this.nodes,this.cmp)},e.peek=function(){return this.nodes[0]},e.contains=function(t){return this.nodes.indexOf(t)!==-1},e.replace=function(t){return this.heapFunction.heapreplace(this.nodes,t,this.cmp)},e.pushpop=function(t){return this.heapFunction.heappushpop(this.nodes,t,this.cmp)},e.heapify=function(){return this.heapFunction.heapify(this.nodes,this.cmp)},e.updateItem=function(t){return this.heapFunction.updateItem(this.nodes,t,this.cmp)},e.clear=function(){return this.nodes=[]},e.empty=function(){return 0===this.nodes.length},e.size=function(){return this.nodes.length},e.clone=function(){var e=new t;return e.nodes=this.nodes.slice(0),e},e.toArray=function(){return this.nodes.slice(0)},t}()),c=function(){function t(){this.defaultCmp=function(t,e){return t<e?-1:t>e?1:0}}n(t,"PathFinding.libs.HeapFunction");var e=t.prototype;return e.insort=function(t,e,i,n,s){var a=NaN;if(null==i&&(i=0),null==s&&(s=this.defaultCmp),i<0)throw new Error("lo must be non-negative");for(null==n&&(n=t.length);i<n;)a=Math.floor((i+n)/2),s(e,t[a])<0?n=a:i=a+1;return[].splice.apply(t,[i,i-i].concat(e)),e},e.heappush=function(t,e,i){return null==i&&(i=this.defaultCmp),t.push(e),this._siftdown(t,0,t.length-1,i)},e.heappop=function(t,e){var i,n;return null==e&&(e=this.defaultCmp),i=t.pop(),t.length?(n=t[0],t[0]=i,this._siftup(t,0,e)):n=i,n},e.heapreplace=function(t,e,i){var n;return null==i&&(i=this.defaultCmp),n=t[0],t[0]=e,this._siftup(t,0,i),n},e.heappushpop=function(t,e,i){var n;return null==i&&(i=this.defaultCmp),t.length&&i(t[0],e)<0&&(n=[t[0],e],e=n[0],t[0]=n[1],this._siftup(t,0,i)),e},e.heapify=function(t,e){var i,n,s,a,o=0,r=0,h=0,l=0;for(null==e&&(e=this.defaultCmp),n=function(){for(a=[],h=0,i=Math.floor(t.length/2);0<=i?h<i:h>i;0<=i?h++:h--)a.push(h);return a}.apply(this).reverse(),s=[],r=0,l=n.length;r<l;r++)o=n[r],s.push(this._siftup(t,o,e));return s},e.updateItem=function(t,e,i){var n=0;return null==i&&(i=this.defaultCmp),(n=t.indexOf(e))===-1?null:(this._siftdown(t,0,n,i),this._siftup(t,n,i))},e.nlargest=function(t,e,i){var n,s,a,o=0,r=0;if(null==i&&(i=this.defaultCmp),s=t.slice(0,e),!s.length)return s;for(this.heapify(s,i),a=t.slice(e),o=0,r=a.length;o<r;o++)n=a[o],this.heappushpop(s,n,i);return s.sort(i).reverse()},e.nsmallest=function(t,e,i){var n,s,a,o,r,h,l,u=0,d=0;if(null==i&&(i=this.defaultCmp),10*e<=t.length){if(a=t.slice(0,e).sort(i),!a.length)return a;for(s=a[a.length-1],r=t.slice(e),u=0,o=r.length;u<o;u++)n=r[u],i(n,s)<0&&(this.insort(a,n,0,null,i),a.pop(),s=a[a.length-1]);return a}for(this.heapify(t,i),l=[],d=0,h=Math.min(e,t.length);0<=h?d<h:d>h;0<=h?++d:--d)l.push(this.heappop(t,i));return l},e._siftdown=function(t,e,i,n){var s,a,o=0;for(null==n&&(n=this.defaultCmp),s=t[i];i>e&&(o=i-1>>1,a=t[o],n(s,a)<0);)t[i]=a,i=o;return t[i]=s},e._siftup=function(t,e,i){var n,s=0,a=0,o=0,r=0;for(null==i&&(i=this.defaultCmp),a=t.length,r=e,n=t[e],s=2*e+1;s<a;)o=s+1,o<a&&!(i(t[s],t[o])<0)&&(s=o),t[e]=t[s],e=s,s=2*e+1;return t[e]=n,this._siftdown(t,r,e,i)},t}(),p=(function(){function t(){var t={allowDiagonal:!0,dontCrossCorners:!1,heuristic:o.manhattan,weight:1},e=new l(t),i=new a(64,36),n=e.findPath(23,16,33,16,i);console.log(n.toString())}n(t,"TestPathFinding"),t}(),function(t){function e(t){e.__super.call(this,t);var i=this.heuristic;this.heuristic=function(t,e){return 1e6*i(t,e)}}n(e,"PathFinding.finders.BestFirstFinder",t),e}(l),function(t){function e(t){e.__super.call(this,t);var i=this.heuristic;this.heuristic=function(t,e){return 1e6*i(t,e)}}n(e,"PathFinding.finders.BiBestFirstFinder",t),e}(u),function(t){function e(t){e.__super.call(this,t),this.heuristic=function(t,e){return 0}}n(e,"PathFinding.finders.BiDijkstraFinder",t),e}(u),function(t){function e(t){e.__super.call(this,t),this.heuristic=function(t,e){return 0}}n(e,"PathFinding.finders.DijkstraFinder",t),e}(l),function(t){function e(t){e.__super.call(this,t)}n(e,"PathFinding.finders.JPFAlwaysMoveDiagonally",t);var i=e.prototype;return i._jump=function(t,e,i,n){var s=this.grid,a=t-i,o=e-n;if(!s.isWalkableAt(t,e))return null;if(1==this.trackJumpRecursion&&(s.getNodeAt(t,e).tested=!0),s.getNodeAt(t,e)==this.endNode)return[t,e];if(0!==a&&0!==o){if(s.isWalkableAt(t-a,e+o)&&!s.isWalkableAt(t-a,e)||s.isWalkableAt(t+a,e-o)&&!s.isWalkableAt(t,e-o))return[t,e];if(this._jump(t+a,e,t,e)||this._jump(t,e+o,t,e))return[t,e]}else if(0!==a){if(s.isWalkableAt(t+a,e+1)&&!s.isWalkableAt(t,e+1)||s.isWalkableAt(t+a,e-1)&&!s.isWalkableAt(t,e-1))return[t,e]}else if(s.isWalkableAt(t+1,e+o)&&!s.isWalkableAt(t+1,e)||s.isWalkableAt(t-1,e+o)&&!s.isWalkableAt(t-1,e))return[t,e];return this._jump(t+a,e+o,t,e)},i._findNeighbors=function(t){var e,i,n=t.parent,a=t.x,o=t.y,r=this.grid,h=0,l=0,u=0,d=0,f=[],c=0,p=0;if(n)h=n.x,l=n.y,u=(a-h)/Math.max(Math.abs(a-h),1),d=(o-l)/Math.max(Math.abs(o-l),1),0!==u&&0!==d?(r.isWalkableAt(a,o+d)&&f.push([a,o+d]),r.isWalkableAt(a+u,o)&&f.push([a+u,o]),r.isWalkableAt(a+u,o+d)&&f.push([a+u,o+d]),r.isWalkableAt(a-u,o)||f.push([a-u,o+d]),r.isWalkableAt(a,o-d)||f.push([a+u,o-d])):0===u?(r.isWalkableAt(a,o+d)&&f.push([a,o+d]),r.isWalkableAt(a+1,o)||f.push([a+1,o+d]),r.isWalkableAt(a-1,o)||f.push([a-1,o+d])):(r.isWalkableAt(a+u,o)&&f.push([a+u,o]),r.isWalkableAt(a,o+1)||f.push([a+u,o+1]),r.isWalkableAt(a,o-1)||f.push([a+u,o-1]));else for(e=r.getNeighbors(t,s.Always),c=0,p=e.length;c<p;++c)i=e[c],f.push([i.x,i.y]);return f},e}(d)),g=function(t){function e(t){e.__super.call(this,t)}n(e,"PathFinding.finders.JPFMoveDiagonallyIfAtMostOneObstacle",t);var i=e.prototype;return i._jump=function(t,e,i,n){var s=this.grid,a=t-i,o=e-n;if(!s.isWalkableAt(t,e))return null;if(this.trackJumpRecursion===!0&&(s.getNodeAt(t,e).tested=!0),s.getNodeAt(t,e)==this.endNode)return[t,e];if(0!==a&&0!==o){if(s.isWalkableAt(t-a,e+o)&&!s.isWalkableAt(t-a,e)||s.isWalkableAt(t+a,e-o)&&!s.isWalkableAt(t,e-o))return[t,e];if(this._jump(t+a,e,t,e)||this._jump(t,e+o,t,e))return[t,e]}else if(0!==a){if(s.isWalkableAt(t+a,e+1)&&!s.isWalkableAt(t,e+1)||s.isWalkableAt(t+a,e-1)&&!s.isWalkableAt(t,e-1))return[t,e]}else if(s.isWalkableAt(t+1,e+o)&&!s.isWalkableAt(t+1,e)||s.isWalkableAt(t-1,e+o)&&!s.isWalkableAt(t-1,e))return[t,e];return s.isWalkableAt(t+a,e)||s.isWalkableAt(t,e+o)?this._jump(t+a,e+o,t,e):null},i._findNeighbors=function(t){var e,i,n=t.parent,a=t.x,o=t.y,r=this.grid,h=0,l=0,u=0,d=0,f=[],c=0,p=0;if(n)h=n.x,l=n.y,u=(a-h)/Math.max(Math.abs(a-h),1),d=(o-l)/Math.max(Math.abs(o-l),1),0!==u&&0!==d?(r.isWalkableAt(a,o+d)&&f.push([a,o+d]),r.isWalkableAt(a+u,o)&&f.push([a+u,o]),(r.isWalkableAt(a,o+d)||r.isWalkableAt(a+u,o))&&f.push([a+u,o+d]),!r.isWalkableAt(a-u,o)&&r.isWalkableAt(a,o+d)&&f.push([a-u,o+d]),!r.isWalkableAt(a,o-d)&&r.isWalkableAt(a+u,o)&&f.push([a+u,o-d])):0===u?r.isWalkableAt(a,o+d)&&(f.push([a,o+d]),r.isWalkableAt(a+1,o)||f.push([a+1,o+d]),r.isWalkableAt(a-1,o)||f.push([a-1,o+d])):r.isWalkableAt(a+u,o)&&(f.push([a+u,o]),r.isWalkableAt(a,o+1)||f.push([a+u,o+1]),r.isWalkableAt(a,o-1)||f.push([a+u,o-1]));else for(e=r.getNeighbors(t,s.IfAtMostOneObstacle),c=0,p=e.length;c<p;++c)i=e[c],f.push([i.x,i.y]);return f},e}(d),b=function(t){function e(t){e.__super.call(this,t)}n(e,"PathFinding.finders.JPFMoveDiagonallyIfNoObstacles",t);var i=e.prototype;return i._jump=function(t,e,i,n){var s=this.grid,a=t-i,o=e-n;if(!s.isWalkableAt(t,e))return null;if(this.trackJumpRecursion===!0&&(s.getNodeAt(t,e).tested=!0),s.getNodeAt(t,e)===this.endNode)return[t,e];if(0!==a&&0!==o){if(this._jump(t+a,e,t,e)||this._jump(t,e+o,t,e))return[t,e]}else if(0!==a){if(s.isWalkableAt(t,e-1)&&!s.isWalkableAt(t-a,e-1)||s.isWalkableAt(t,e+1)&&!s.isWalkableAt(t-a,e+1))return[t,e]}else if(0!==o&&(s.isWalkableAt(t-1,e)&&!s.isWalkableAt(t-1,e-o)||s.isWalkableAt(t+1,e)&&!s.isWalkableAt(t+1,e-o)))return[t,e];return s.isWalkableAt(t+a,e)&&s.isWalkableAt(t,e+o)?this._jump(t+a,e+o,t,e):null},i._findNeighbors=function(t){var e,i,n=t.parent,a=t.x,o=t.y,r=this.grid,h=0,l=0,u=0,d=0,f=[],c=0,p=0;if(n)if(h=n.x,l=n.y,u=(a-h)/Math.max(Math.abs(a-h),1),d=(o-l)/Math.max(Math.abs(o-l),1),0!==u&&0!==d)r.isWalkableAt(a,o+d)&&f.push([a,o+d]),r.isWalkableAt(a+u,o)&&f.push([a+u,o]),r.isWalkableAt(a,o+d)&&r.isWalkableAt(a+u,o)&&f.push([a+u,o+d]);else{var g;if(0!==u){g=r.isWalkableAt(a+u,o);var b=r.isWalkableAt(a,o+1),m=r.isWalkableAt(a,o-1);g&&(f.push([a+u,o]),b&&f.push([a+u,o+1]),m&&f.push([a+u,o-1])),b&&f.push([a,o+1]),m&&f.push([a,o-1])}else if(0!==d){g=r.isWalkableAt(a,o+d);var v=r.isWalkableAt(a+1,o),A=r.isWalkableAt(a-1,o);g&&(f.push([a,o+d]),v&&f.push([a+1,o+d]),A&&f.push([a-1,o+d])),v&&f.push([a+1,o]),A&&f.push([a-1,o])}}else for(e=r.getNeighbors(t,s.OnlyWhenNoObstacles),c=0,p=e.length;c<p;++c)i=e[c],f.push([i.x,i.y]);return f},e}(d),m=function(t){function e(t){e.__super.call(this,t)}n(e,"PathFinding.finders.JPFNeverMoveDiagonally",t);var i=e.prototype;return i._jump=function(t,e,i,n){var s=this.grid,a=t-i,o=e-n;if(!s.isWalkableAt(t,e))return null;if(this.trackJumpRecursion===!0&&(s.getNodeAt(t,e).tested=!0),s.getNodeAt(t,e)==this.endNode)return[t,e];if(0!==a){if(s.isWalkableAt(t,e-1)&&!s.isWalkableAt(t-a,e-1)||s.isWalkableAt(t,e+1)&&!s.isWalkableAt(t-a,e+1))return[t,e]}else{if(0===o)throw new Error("Only horizontal and vertical movements are allowed");if(s.isWalkableAt(t-1,e)&&!s.isWalkableAt(t-1,e-o)||s.isWalkableAt(t+1,e)&&!s.isWalkableAt(t+1,e-o))return[t,e];if(this._jump(t+1,e,t,e)||this._jump(t-1,e,t,e))return[t,e]}return this._jump(t+a,e+o,t,e)},i._findNeighbors=function(t){var e,i,n=t.parent,a=t.x,o=t.y,r=this.grid,h=0,l=0,u=0,d=0,f=[],c=0,p=0;if(n)h=n.x,l=n.y,u=(a-h)/Math.max(Math.abs(a-h),1),d=(o-l)/Math.max(Math.abs(o-l),1),0!==u?(r.isWalkableAt(a,o-1)&&f.push([a,o-1]),r.isWalkableAt(a,o+1)&&f.push([a,o+1]),r.isWalkableAt(a+u,o)&&f.push([a+u,o])):0!==d&&(r.isWalkableAt(a-1,o)&&f.push([a-1,o]),r.isWalkableAt(a+1,o)&&f.push([a+1,o]),r.isWalkableAt(a,o+d)&&f.push([a,o+d]));else for(e=r.getNeighbors(t,s.Never),c=0,p=e.length;c<p;++c)i=e[c],f.push([i.x,i.y]);return f},e}(d)}(window,document,Laya);