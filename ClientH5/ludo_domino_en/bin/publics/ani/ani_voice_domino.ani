{"type": "View", "props": {"sceneWidth": 1334, "sceneHeight": 750, "sceneColor": "#675655"}, "compId": 1, "child": [{"type": "GraphicNode", "props": {"y": 46, "x": 2, "width": 40, "skin": "public/clip_lobby_voice_on.png", "height": 40}, "compId": 9}, {"type": "GraphicNode", "props": {"y": 37, "x": 10, "width": 24, "skin": "public/clip_voice_bg_02.png", "height": 6, "alpha": 0}, "compId": 15}, {"type": "GraphicNode", "props": {"y": 27, "x": 10, "width": 24, "skin": "public/clip_voice_bg_02.png", "height": 6, "alpha": 0}, "compId": 16}, {"type": "GraphicNode", "props": {"y": 18, "x": 10, "width": 24, "skin": "public/clip_voice_bg_02.png", "height": 6, "alpha": 0}, "compId": 17}, {"type": "GraphicNode", "props": {"y": 9, "x": 10, "width": 24, "skin": "public/clip_voice_bg_02.png", "height": 6, "alpha": 0}, "compId": 18}, {"type": "GraphicNode", "props": {"y": 0, "x": 10, "width": 24, "skin": "public/clip_voice_bg_02.png", "height": 6, "alpha": 0}, "compId": 19}, {"type": "GraphicNode", "props": {"y": 0, "x": 10, "width": 24, "skin": "public/clip_voice_bg_01.png", "height": 6, "alpha": 1}, "compId": 14}, {"type": "GraphicNode", "props": {"y": 9, "x": 10, "width": 24, "skin": "public/clip_voice_bg_01.png", "height": 6, "alpha": 1}, "compId": 13}, {"type": "GraphicNode", "props": {"y": 18, "x": 10, "width": 24, "skin": "public/clip_voice_bg_01.png", "height": 6, "alpha": 1}, "compId": 12}, {"type": "GraphicNode", "props": {"y": 27, "x": 10, "width": 24, "skin": "public/clip_voice_bg_01.png", "height": 6, "alpha": 1}, "compId": 11}, {"type": "GraphicNode", "props": {"y": 37, "x": 10, "width": 24, "skin": "public/clip_voice_bg_01.png", "height": 6, "alpha": 1}, "compId": 10}], "animations": [{"nodes": [{"target": 9, "keyframes": {"y": [{"value": 41, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "y", "index": 0}], "x": [{"value": 2, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "x", "index": 0}], "width": [{"value": 52, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "width", "index": 0}], "skin": [{"value": "public/clip_lobby_voice_on.png", "tweenMethod": "linearNone", "tween": false, "target": 9, "key": "skin", "index": 0}], "height": [{"value": 52, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "height", "index": 0}]}}, {"target": 10, "keyframes": {"y": [{"value": 32, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "width", "index": 0}, {"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "width", "index": 3}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 10, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "height", "index": 0}, {"value": 6, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "height", "index": 3}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "alpha", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "alpha", "index": 3}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "alpha", "index": 6}]}}, {"target": 11, "keyframes": {"y": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "width", "index": 0}, {"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "width", "index": 3}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 11, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "height", "index": 0}, {"value": 6, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "height", "index": 3}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "alpha", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "alpha", "index": 3}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "alpha", "index": 9}]}}, {"target": 12, "keyframes": {"y": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "width", "index": 0}, {"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "width", "index": 3}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 12, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "height", "index": 0}, {"value": 6, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "height", "index": 3}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "alpha", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "alpha", "index": 3}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "alpha", "index": 12}]}}, {"target": 13, "keyframes": {"y": [{"value": 8, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "width", "index": 0}, {"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "width", "index": 3}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 13, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "height", "index": 0}, {"value": 6, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "height", "index": 3}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "alpha", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "alpha", "index": 3}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "alpha", "index": 15}]}}, {"target": 14, "keyframes": {"y": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "width", "index": 0}, {"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "width", "index": 3}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 14, "key": "skin", "index": 0}, {"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 14, "key": "skin", "index": 18}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "height", "index": 0}, {"value": 6, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "height", "index": 3}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "alpha", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "alpha", "index": 3}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "alpha", "index": 18}]}}, {"target": 15, "keyframes": {"y": [{"value": 32, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 15, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "height", "index": 0}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "alpha", "index": 0}]}}, {"target": 16, "keyframes": {"y": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 16, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "height", "index": 0}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "alpha", "index": 0}]}}, {"target": 17, "keyframes": {"y": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 17, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "height", "index": 0}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "alpha", "index": 0}]}}, {"target": 18, "keyframes": {"y": [{"value": 8, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 18, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "height", "index": 0}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "alpha", "index": 0}]}}, {"target": 19, "keyframes": {"y": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 19, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "height", "index": 0}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "alpha", "index": 0}]}}], "name": "play", "id": 1, "frameRate": 24, "action": 0}, {"nodes": [{"target": 9, "keyframes": {"y": [{"value": 42, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "y", "index": 0}], "x": [{"value": 2, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "x", "index": 0}], "width": [{"value": 52, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "width", "index": 0}], "skin": [{"value": "public/clip_lobby_voice_on.png", "tweenMethod": "linearNone", "tween": false, "target": 9, "key": "skin", "index": 0}], "height": [{"value": 52, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "height", "index": 0}]}}, {"target": 10, "keyframes": {"y": [{"value": 32, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 10, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "height", "index": 0}], "alpha": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "alpha", "index": 0}]}}, {"target": 11, "keyframes": {"y": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 11, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "height", "index": 0}], "alpha": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "alpha", "index": 0}]}}, {"target": 12, "keyframes": {"y": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 12, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "height", "index": 0}], "alpha": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "alpha", "index": 0}]}}, {"target": 13, "keyframes": {"y": [{"value": 8, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 13, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "height", "index": 0}], "alpha": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "alpha", "index": 0}]}}, {"target": 14, "keyframes": {"x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "x", "index": 0}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 14, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "height", "index": 0}], "alpha": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "alpha", "index": 0}]}}, {"target": 15, "keyframes": {"y": [{"value": 32, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 15, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "height", "index": 0}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "alpha", "index": 0}]}}, {"target": 16, "keyframes": {"y": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 16, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "height", "index": 0}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "alpha", "index": 0}]}}, {"target": 17, "keyframes": {"y": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 17, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "height", "index": 0}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "alpha", "index": 0}]}}, {"target": 18, "keyframes": {"y": [{"value": 8, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 18, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "height", "index": 0}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "alpha", "index": 0}]}}, {"target": 19, "keyframes": {"x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "x", "index": 0}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "width", "index": 0}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 19, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "height", "index": 0}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "alpha", "index": 0}]}}], "name": "pause", "id": 2, "frameRate": 24, "action": 0}, {"nodes": [{"target": 9, "keyframes": {"y": [{"value": 42, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "y", "index": 0}], "x": [{"value": 2, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "x", "index": 0}], "width": [{"value": 52, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "width", "index": 0}], "skin": [{"value": "public/clip_lobby_voice_off.png", "tweenMethod": "linearNone", "tween": false, "target": 9, "key": "skin", "index": 0}, {"value": "public/clip_lobby_voice_off.png", "tweenMethod": "linearNone", "tween": false, "target": 9, "key": "skin", "index": 2}], "height": [{"value": 52, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "height", "index": 0}]}}, {"target": 15, "keyframes": {"y": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "x", "index": 0}, {"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "x", "index": 2}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "width", "index": 0}, {"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "width", "index": 18}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 15, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "height", "index": 0}, {"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "height", "index": 2}, {"value": 6, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "height", "index": 18}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "alpha", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 15, "key": "alpha", "index": 18}]}}, {"target": 16, "keyframes": {"y": [{"value": 32, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "x", "index": 0}, {"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "x", "index": 2}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "width", "index": 0}, {"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "width", "index": 18}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 16, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "height", "index": 0}, {"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "height", "index": 2}, {"value": 6, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "height", "index": 18}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "alpha", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 16, "key": "alpha", "index": 18}]}}, {"target": 17, "keyframes": {"y": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "x", "index": 0}, {"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "x", "index": 2}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "width", "index": 0}, {"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "width", "index": 18}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 17, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "height", "index": 0}, {"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "height", "index": 2}, {"value": 6, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "height", "index": 18}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "alpha", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 17, "key": "alpha", "index": 18}]}}, {"target": 18, "keyframes": {"y": [{"value": 8, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "x", "index": 0}, {"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "x", "index": 2}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "width", "index": 0}, {"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "width", "index": 18}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 18, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "height", "index": 0}, {"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "height", "index": 2}, {"value": 6, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "height", "index": 18}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "alpha", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 18, "key": "alpha", "index": 18}]}}, {"target": 19, "keyframes": {"x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "x", "index": 0}, {"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "x", "index": 2}, {"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "x", "index": 18}], "width": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "width", "index": 0}, {"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "width", "index": 18}], "skin": [{"value": "public/clip_voice_bg_02.png", "tweenMethod": "linearNone", "tween": false, "target": 19, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "height", "index": 0}, {"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "height", "index": 2}, {"value": 6, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "height", "index": 18}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "alpha", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 19, "key": "alpha", "index": 18}]}}, {"target": 14, "keyframes": {"x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "x", "index": 0}, {"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "x", "index": 2}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 14, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "height", "index": 0}], "alpha": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 14, "key": "alpha", "index": 0}]}}, {"target": 13, "keyframes": {"y": [{"value": 8, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "x", "index": 0}, {"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "x", "index": 2}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 13, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "height", "index": 0}], "alpha": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 13, "key": "alpha", "index": 0}]}}, {"target": 12, "keyframes": {"y": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "x", "index": 0}, {"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "x", "index": 2}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 12, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "height", "index": 0}], "alpha": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 12, "key": "alpha", "index": 0}]}}, {"target": 11, "keyframes": {"y": [{"value": 24, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "x", "index": 0}, {"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "x", "index": 2}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 11, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "height", "index": 0}], "alpha": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 11, "key": "alpha", "index": 0}]}}, {"target": 10, "keyframes": {"y": [{"value": 32, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "y", "index": 0}], "x": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "x", "index": 0}], "skin": [{"value": "public/clip_voice_bg_01.png", "tweenMethod": "linearNone", "tween": false, "target": 10, "key": "skin", "index": 0}], "height": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "height", "index": 0}], "alpha": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "alpha", "index": 0}]}}], "name": "stop", "id": 2, "frameRate": 24, "action": 0}]}