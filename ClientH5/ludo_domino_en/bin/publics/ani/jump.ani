{"type": "View", "props": {"sceneWidth": 1334, "sceneHeight": 750, "sceneColor": "#000000"}, "compId": 1, "child": [{"type": "GraphicNode", "props": {}, "compId": 2}], "animations": [{"nodes": [{"target": 2, "keyframes": {"y": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "y", "index": 0}, {"value": -11, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "y", "index": 10}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "y", "index": 20}], "x": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "x", "index": 0}], "width": [{"value": 32, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "width", "index": 0}], "skin": [{"value": "game/Arrow.png", "tweenMethod": "linearNone", "tween": false, "target": 2, "key": "skin", "index": 0}], "pivotY": [{"value": 48, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "pivotY", "index": 0}], "pivotX": [{"value": 16, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "pivotX", "index": 0}], "height": [{"value": 48, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "height", "index": 0}]}}], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}