{"type": "View", "props": {"sceneWidth": 600, "sceneHeight": 600, "sceneColor": "#9f6766"}, "compId": 1, "child": [{"type": "GraphicNode", "props": {}, "compId": 2}, {"type": "GraphicNode", "props": {}, "compId": 3}], "animations": [{"nodes": [{"target": 2, "keyframes": {"y": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "y", "index": 0}], "x": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "x", "index": 0}], "skin": [{"value": "curtain/safeCicle_big.png", "tweenMethod": "linearNone", "tween": false, "target": 2, "key": "skin", "index": 0}], "scaleY": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 0}, {"value": 1.6, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 5}], "scaleX": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 0}, {"value": 1.6, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 5}], "anchorY": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "anchorY", "index": 0}], "anchorX": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "anchorX", "index": 0}], "alpha": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "alpha", "index": 0}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "alpha", "index": 5}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "alpha", "index": 8}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "alpha", "index": 10}]}}, {"target": 3, "keyframes": {"y": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "y", "index": 0}], "x": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "x", "index": 0}], "skin": [{"value": "curtain/safeCicle_smail.png", "tweenMethod": "linearNone", "tween": false, "target": 3, "key": "skin", "index": 0}], "scaleY": [{"value": 0.3, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "scaleY", "index": 0}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "scaleY", "index": 5}, {"value": 1.8, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "scaleY", "index": 10}], "scaleX": [{"value": 0.3, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "scaleX", "index": 0}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "scaleX", "index": 5}, {"value": 1.8, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "scaleX", "index": 10}], "anchorY": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "anchorY", "index": 0}], "anchorX": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "anchorX", "index": 0}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "alpha", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 3, "key": "alpha", "index": 10}]}}], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}