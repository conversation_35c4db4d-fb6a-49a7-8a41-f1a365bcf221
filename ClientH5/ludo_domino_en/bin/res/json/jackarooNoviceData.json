{"login": {"result": 1, "code": 1, "command": 1, "playerSelfData": {"gold": 0, "diamond": 0, "isFirstRecharge": true}, "selfGameData": {}, "restoreGame": {"gameData": {"playerDataMap": {"10001": {"idx": 10001, "banker": true, "color": 0, "chessPosition": {"1": {"order": 1, "position": 201, "gridColor": 0}, "2": {"order": 2, "position": 202, "gridColor": 0}, "3": {"order": 3, "position": 203, "gridColor": 0}, "4": {"order": 4, "position": 204, "gridColor": 0}}, "remainThinkingTime": 0}, "10002": {"idx": 10002, "color": 1, "chessPosition": {"1": {"order": 1, "position": 401, "gridColor": 1}, "2": {"order": 2, "position": 402, "gridColor": 1}, "3": {"order": 3, "position": 403, "gridColor": 1}, "4": {"order": 4, "position": 404, "gridColor": 1}}, "remainThinkingTime": 0}, "10003": {"idx": 10003, "color": 2, "chessPosition": {"1": {"order": 1, "position": 301, "gridColor": 2}, "2": {"order": 2, "position": 302, "gridColor": 2}, "3": {"order": 3, "position": 303, "gridColor": 2}, "4": {"order": 4, "position": 304, "gridColor": 2}}, "remainThinkingTime": 0}, "10004": {"idx": 10004, "color": 3, "chessPosition": {"1": {"order": 1, "position": 101, "gridColor": 3}, "2": {"order": 2, "position": 102, "gridColor": 3}, "3": {"order": 3, "position": 103, "gridColor": 3}, "4": {"order": 4, "position": 104, "gridColor": 3}}, "playerStatus": 1}}, "againstType": 2, "teamInfo": {"teamAPlayer1": 10001, "teamAPlayer2": 10003, "teamBPlayer1": 10002, "teamBPlayer2": 10004}, "chessPanel": {"gameStatus": 1, "currentPlayer": 10001}, "bets": 0}, "playShowInfo": {"10001": {"playerShowData": {"idx": 10001, "faceUrl": "unkown", "nickName": "حسين", "prettyId": "10001", "royalLevelNameAnimation": true}, "showGameData": {"color": 0}}, "10002": {"playerShowData": {"idx": 10002, "faceUrl": "unkown", "nickName": "يوسف", "prettyId": "10002"}, "showGameData": {"color": 1}}, "10003": {"playerShowData": {"idx": 10003, "faceUrl": "unkown", "nickName": "مصط<PERSON>ى", "prettyId": "10003"}, "showGameData": {"color": 2}}, "10004": {"playerShowData": {"idx": 10004, "faceUrl": "unkown", "nickName": "مصط<PERSON>ى", "prettyId": "10004"}, "showGameData": {"color": 3}}}}}, "dealPokerPub": {"command": 101, "totalPokerCount": 52, "handPoker": [14, 28, 17, 5], "idx": 10002}, "changePlayerResponse": {"command": 103, "chessPanel": {"gameStatus": 1, "currentPoker": 14, "currentPlayer": 10002}, "currentPlayerStatus": 1, "allowPokerData": {"14": {"commonResult": {"playResultData": {"0": {"result": [{"fromGrid": {"grid": {"index": 401, "color": 1}, "chess": {"idx": 10002, "order": 1, "color": 1}}, "toGrid": {"grid": {"index": 2, "color": 1, "type": 3}}}]}, "11": {"result": [{"fromGrid": {"grid": {"index": 401, "color": 1}, "chess": {"idx": 10002, "order": 1, "color": 1}}, "toGrid": {"grid": {"index": 2, "color": 1, "type": 3}}}]}, "1": {"result": [{"fromGrid": {"grid": {"index": 401, "color": 1}, "chess": {"idx": 10002, "order": 1, "color": 1}}, "toGrid": {"grid": {"index": 2, "color": 1, "type": 3}}}]}}}, "showPreResult": true}}}, "forcedDiscardPubResponse": {"command": 114, "forcedDiscardIdx": 10002, "currentPoker": 10, "playPokerPlayerId": 10001}, "changePlayerResponse2": {"command": 103, "chessPanel": {"gameStatus": 1, "currentPoker": 28, "currentPlayer": 10002}, "currentPlayerStatus": 2, "allowPokerData": {"28": {"pokerTenDiscard": true, "commonResult": null, "showPreResult": true}}}, "changePlayerResponse3": {"command": 103, "chessPanel": {"gameStatus": 1, "currentPoker": 17, "currentPlayer": 10002}, "currentPlayerStatus": 1, "allowPokerData": {"17": {"commonResult": {"playResultData": {"-4": {"result": [{"fromGrid": {"grid": {"index": 2, "color": 1, "type": 0}, "chess": {"idx": 10002, "order": 1, "color": 1}}, "toGrid": {"grid": {"index": 17, "color": 2, "type": 0}, "chess": {}}}]}}}, "showPreResult": true}}}, "changePlayerResponse4": {"command": 103, "chessPanel": {"gameStatus": 1, "currentPoker": 5, "currentPlayer": 10002}, "currentPlayerStatus": 1, "allowPokerData": {"5": {"commonResult": {"playResultData": {"5": {"result": [{"fromGrid": {"grid": {"index": 17, "color": 2, "type": 0}, "chess": {"idx": 10002, "order": 1, "color": 1}}, "toGrid": {"grid": {"index": -3, "color": 1, "type": 0}, "chess": {}}}, {"fromGrid": {"grid": {"index": 5, "color": 2, "type": 0}, "chess": {"idx": 10003, "order": 1, "color": 2}}, "toGrid": {"grid": {"index": 10, "color": 2, "type": 0}, "chess": {}}}]}}}, "showPreResult": true}}}, "dealPokerPub5": {"command": 101, "totalPokerCount": 36, "handPoker": [7, 34, 24, 26], "idx": 10002}, "gameData5": {"playerDataMap": {"10001": {"idx": 10001, "banker": true, "color": 0, "chessPosition": {"1": {"order": 1, "position": 18, "gridColor": 2}, "2": {"order": 2, "position": 202, "gridColor": 0}, "3": {"order": 3, "position": 203, "gridColor": 0}, "4": {"order": 4, "position": 204, "gridColor": 0}}, "remainThinkingTime": 0}, "10002": {"idx": 10002, "color": 1, "chessPosition": {"1": {"order": 1, "position": -4, "gridColor": 1}, "2": {"order": 2, "position": -3, "gridColor": 1}, "3": {"order": 3, "position": 8, "gridColor": 2}, "4": {"order": 4, "position": 404, "gridColor": 1}}, "remainThinkingTime": 0}, "10003": {"idx": 10003, "color": 2, "chessPosition": {"1": {"order": 1, "position": 301, "gridColor": 2}, "2": {"order": 2, "position": 302, "gridColor": 2}, "3": {"order": 3, "position": 303, "gridColor": 2}, "4": {"order": 4, "position": 304, "gridColor": 2}}, "remainThinkingTime": 0}, "10004": {"idx": 10004, "color": 3, "chessPosition": {"1": {"order": 1, "position": -1, "gridColor": 3}, "2": {"order": 2, "position": -2, "gridColor": 3}, "3": {"order": 3, "position": -3, "gridColor": 3}, "4": {"order": 4, "position": -4, "gridColor": 3}}, "playerStatus": 1}}, "againstType": 2, "teamInfo": {"teamAPlayer1": 10001, "teamAPlayer2": 10003, "teamBPlayer1": 10002, "teamBPlayer2": 10004}, "chessPanel": {"gameStatus": 1, "currentPlayer": 10001}, "bets": 0}, "changePlayerResponse5": {"command": 103, "chessPanel": {"gameStatus": 1, "currentPoker": 26, "currentPlayer": 10002}, "currentPlayerStatus": 1, "allowPokerData": {"26": {"commonResult": {"playResultData": {"0": {"result": [{"fromGrid": {"grid": {"index": 404, "color": 1}, "chess": {"idx": 10002, "order": 4, "color": 1}}, "toGrid": {"grid": {"index": 2, "color": 1, "type": 3}}}]}}}, "showPreResult": true}}}, "changePlayerResponse6": {"command": 103, "chessPanel": {"gameStatus": 1, "currentPoker": 26, "currentPlayer": 10002}, "currentPlayerStatus": 1, "allowPokerData": {"24": {"jackResult": {"playResultData": {"10001": {"result": [{"fromGrid": {"grid": {"index": 2, "color": 1, "type": 2}, "chess": {"idx": 10002, "order": 4}}, "toGrid": {"grid": {"index": 18, "color": 2, "type": 3}, "chess": {"idx": 10001, "order": 1, "color": 2}}}]}}}, "showPreResult": true}}}, "exchangeChessPub": {"selfMoveResultData": {"fromGrid": {"grid": {"index": 18, "color": 2, "type": 3}, "chess": {"idx": 10002, "order": 4}}, "toGrid": {"grid": {"index": 2, "color": 1, "type": 1}, "chess": {"idx": 10001, "order": 1, "color": 2}}}, "currentPoker": 24, "remainPokerCount": 2, "moveChessPlayer": 10002, "moveChess": true}, "changePlayerResponse7": {"command": 103, "chessPanel": {"gameStatus": 1, "currentPoker": 7, "currentPlayer": 10002}, "currentPlayerStatus": 1, "allowPokerData": {"7": {"commonResult": {"playResultData": {"3": {"result": [{"fromGrid": {"grid": {"index": 18, "color": 2, "type": 3}, "chess": {"idx": 10002, "order": 4, "color": 1}}, "toGrid": {"grid": {"index": -2, "color": 1, "type": 3}}}]}, "7": {"result": [{"fromGrid": {"grid": {"index": 8, "color": 2, "type": 2}, "chess": {"idx": 10002, "order": 3, "color": 1}}, "toGrid": {"grid": {"index": 12, "color": 2, "type": 4}}}]}}}, "showPreResult": true}}}, "changePlayerResponse8": {"command": 103, "chessPanel": {"gameStatus": 1, "currentPoker": 34, "currentPlayer": 10002}, "currentPlayerStatus": 1, "allowPokerData": {"34": {"commonResult": {"playResultData": {"8": {"result": [{"fromGrid": {"grid": {"index": 12, "color": 2}, "chess": {"idx": 10002, "order": 3, "color": 1}}, "toGrid": {"grid": {"index": -1, "color": 1, "type": 3}}}]}}}, "showPreResult": true}}}}