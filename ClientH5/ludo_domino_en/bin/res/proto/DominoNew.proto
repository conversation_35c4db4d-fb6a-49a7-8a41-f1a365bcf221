syntax = "proto3";
option java_package = "io.grpc.domino";

service DominoNew {

  rpc callGame(stream DominoGameReq) returns (stream DominoGameResp){};

  rpc quitRoom(QuitRoomRequest) returns (DominoGameResp){};
  rpc gameOperate(GameOperateRequest) returns (DominoGameResp){};
  rpc UpdataCoin(UpdataCoinRequest) returns (UpdataCoinResponse){};

  rpc playerChatToAll(GameRoomPlayerChatToAllRequestAndResponse) returns (DominoGameResp) {};
  rpc getAgoraToken(GetAgoraTokenRequest) returns (GetAgoraTokenResponse){};
  rpc getZegoToken(GetZegoTokenRequest) returns (GetZegoTokenResponse){};

  rpc GameGiftCnfList(GameGiftCnfListRequest) returns (GameGiftCnfListResponse){};
  rpc GameGiftSend(GameGiftSendRequest) returns (DominoGameResp){};

  rpc getDominoInfo(DominoInfoReq) returns (DominoInfoResp) {};

}

//游戏命令
enum Command{
  CommandAdapt3       = 0;// 适配proto3
  TICKET_LOGIN        = 11; //票据登陆

  QUICK_START         = 20; //快速开始
  QUIT_ROOM           = 21; //退出房间
  JOIN_ROOM           = 22; //加入房间


  GAME_OPERATE        = 30; //游戏操作
  GAME_EVENT          = 31; //游戏操作

  PLAYER_CHAT_ALL     = 70; //聊天
  UPDATA_PLAYER_COIN  = 80; //刷新用户金币钻石
  UPDATA_PLAYER_LEVEL = 81; //玩家升级

  GET_AGORA_TOKEN     = 90; //获取语音频道token
  AGORA_OPERATE       = 91; //语音频道操作
  GET_ZEGO_TOKEN      = 92;

  GAME_GIFT_CNF_LIST  = 110; // 获取游戏内互动礼物配置列表 1.3.1 add
  GAME_GIFT_SEND      = 111; // 游戏内互动礼物赠送, 1.3.1 add

  OUT_GAME            = 126; //踢出服务
  SEG_OPERATE         = 119;//段位相关操作

  VIP_ROOM_OPERATE    = 52; // 大厅和viproom间的消息


  CHAMPIONSHIP        = 120;//锦标赛

}

message AuthRequest {
  string app_id = 1;
  oneof identity {
    string open_id = 2;
    int32 user_id = 3;
    int64 long_user_id = 4;
  }
  string token = 5;
  string sign = 6;
  string device_Id = 7;//设备Id
  int32 channel_Id = 8;//渠道Id 0apple 1google,2:未知，3:U3D_ios,4:U3D_android, 5:cocos_ios,6:cocos_android,7:huawei 8:honor 9:小程序安卓 10:小程序ios
  string version = 9;
  int64 timestamp = 10;//时间戳
}

message AuthResponse{

}

message TyrOption{
  int64 ring_route_key = 1;//一致性hash路由标识
}

message DominoGameReq{
  Command command = 1;
  int64   roomId  = 2;
  bytes   data    = 3;
}

message DominoGameResp{
  Command command = 1;
  int64   roomId  = 2;
  bytes   data    = 3;
}

message DominoInfoReq{

}

message DominoInfoResp{

}

//登陆
message TicketLoginRequest {
  int32  idx       = 1; //用户id
  string token     = 2; //用户token
  int64  roomid    = 3; //房间id
  int32  msgindex  = 4;//当前消息号
  string version   = 5;//登陆时传递主包的版本号, etc: 1.2.8.0
  int32  hdVersion = 6;//登陆时传递HD版本号
}

//快速进入房间请求
message QuickEnterRoomRequest {
  int64 roomid = 1;//房间id
  int32 type   = 2; //1私密 0公开PlayerTurnStatus
}

//快速进入房间请求
message QuitRoomRequest {
  int64 roomid = 2; //房间号
}

//游戏操作
message GameOperateRequest {
  GameTypeV2  type         = 1; //游戏类型
  int64       roomid       = 2; //房间号
  GameOperate op           = 3; //操作
  int32       opid         = 4;
  int32       dominoId     = 5;
  int32       sideDominoId = 6;
  int32       trackId      = 7; //赛道ID
  int32       deskSort     = 8; //桌牌方向  1上 2下 3左 4右
}

//获取语音频道token
message GetAgoraTokenRequest {
  int32 idx    = 1; //用户id
  int64 roomid = 2; //房间号
}

//踢出原因
message OutGameResponse {
  OutReason reason = 1;
  int32     idx    = 2;
}

// 获取 即构 token, 暂未用
message GetZegoTokenRequest{
  int32 idx     = 1;//用户id
  int64 roomid  = 2;//房间号
  int32 version = 3;//版本号
}

// 发送互动礼物
message GameGiftSendRequest {
  int64          roomid = 1;//房间id
  int32          giftId = 2; // 本次赠送的互动礼物ID: GameGiftCnf->id
  repeated int32 rcvIdx = 3; // 本次接收互动礼物的玩家ID
}

// 获取游戏内互动礼物配置
message GameGiftCnfListRequest {

}


//多米诺
message DominoPack {
  int32 id             = 1;
  int32 headNum        = 2; //首数
  int32 tailNum        = 3; //尾数
  int32 leftDominoId   = 4; //左侧多米诺ID
  int32 rightDominoId  = 5; //右侧多米诺ID
  int32 side           = 6; //多米诺方向  0 垂直方向  1 水平方向
  int32 topDomainoId   = 7; //上侧多米诺ID
  int32 bottomDominoId = 8; //下侧多米诺ID
  int32 deskSort       = 9;
}


//登陆回复
message LoginResponse {
  OperateResult result       = 1;
  PlayerInfo    palyerInfo   = 2;
  ErrorCode     code         = 3;
  string        banTalkData  = 4; // 禁言数据,没禁言时为空串
  bool          gameGiftOpen = 5; // 互动表情功能开关，true:打开,false:关闭
  VoiceType     voiceType    = 6; // 语音房类别,1.3.2 add, 1.3.2 以前版本始终是声网，1.3.2以后版本开关控制
}

//用户信息
message PlayerInfo {
  int64            money          = 1; //金币
  int64            gold           = 2; //钻石
  int32            exp            = 3; //经验值
  PlayerShowInfoV2 playerShowInfo = 4; //用户显示信息
  bool             isRecharged    = 5; //是否首充
}

//用户展示信息
message PlayerShowInfoV2 {
  FPlayerShowInfo fPlayerInfo   = 1; //用户id
  int32           score         = 2;//分数
  int32           roundScore    = 3;//分数
  int32           talkSkinId    = 4;//气泡皮肤
  int32           segId         = 5;//段位
  int32           starNum       = 6;//传奇之星
  int64           prettyId      = 7;//显示id
  int32           isInChatOff   = 8;//1-屏蔽  0-不屏蔽
  int32           roylevel      = 9; //玩家皇室等级, 1.2.7 add
  bool            royVisibility = 10; //玩家皇室等级可见性, 1.2.7 add, false-皇室等级对所有用户可见，true-仅自己可见~~
  int32           giftId        = 11; // 别人赠送的互动礼物ID
  int32           cardSkinId    = 12; // domino牌的皮肤id
  int32           sceneId       = 13; // domino桌布的皮肤id
  int32           realRoyLevel  = 14; // 真实的玩家皇室等级
  optional bool royalLevelNameAnimation = 15; // rl扫光 1.4.0添加
}
//显示用信息
message FPlayerShowInfo {
  int32  idx        = 1; //用户id
  int32  faceId     = 2; //特效id
  string nikeName   = 3; //昵称
  string faceUrl    = 4; //头像地址
  int32  placeId    = 5; //所在位置    0大厅 其他对应的gameid -1 离线
  int32  ftype      = 6; //好友类型    0
  int32  country    = 7; //国家
  int32  level      = 8; //等级
  int32  viplevel   = 9; //vip等级
  int32  winCount   = 10; //获胜次数
  int32  totalCount = 11; //总次数
}

//聊天
message GameRoomPlayerChatToAllRequestAndResponse {
  int32  idx       = 1; //用户id
  int64  roomid    = 2; //房间号
  string msg       = 3; //消息
  string extension = 5;//扩展字段
}

//更新余额
message UpdataCoinRequest {
  int64 roomid = 1;//房间id
  int32 type   = 2; //0金币 1钻石
}

//更新余额
message UpdataCoinResponse {
  int32 type  = 1; //0金币 1钻石
  int64 value = 2; //余额
}

//获取语音频道token回复
message GetAgoraTokenResponse {
  string token = 1; //token
  string cName = 2; //频道名
}

message GetZegoTokenResponse{
  string token = 1;//token
  string cName = 2;//频道名
}

//语音频道操作
message AgoraOperateRequestAndResponse {
  int32        idx     = 1; //用户id
  int64        roomid  = 2; //房间号
  AgoraOperate operate = 3; //
}

//快速进入房间请求
message QuitRoomResponse {
  int64         roomid = 1; //房间号
  OperateResult result = 2;
  int32         idx    = 3;
  ErrorCode     code   = 4;
}

//快速进入房间回复
message QuickEnterRoomResponse {
  OperateResult      result          = 1;
  int64              roomid          = 2; //房间号
  int32              cost            = 3; //花费
  int32              sceneId         = 4; //场景id
  RoomInfoPlayerInfo player          = 5; //所有房间用户
  ErrorCode          code            = 6;
  repeated Sheep     sheep           = 7; //可出羊群
  int32              gameType        = 8; //多米诺房间类型  1积分场  2轮次场
  int64              showId          = 9; //房间号
  int32              isPrivate       = 10;//是否私有房间
  GameStatus         status          = 11;//游戏状态
  int32              roundNum        = 12;
  int32              msgindex        = 13;//当前消息号
  int32              playerRoundTime = 14;//玩家轮次时长毫秒
  int32              gameRoundTime   = 15;//游戏轮次间隔时长毫秒
  int32              nextDiamond     = 16;//延时所需钻石
  repeated int32     addTimeCost     = 17;//每次延时所需花费
  int32              quitPunishTime  = 18; // 强退惩罚的1个触发时间阀值, 若在该值以内强退，则给客户端惩罚提示
  int32              gameBeginTime   = 19; // 游戏开始时间戳
  int32              autoAddExp      = 20; // 托管自动加经验 托管加经验 0 可以加  1 不可以加
}

//房间用户列表
message RoomInfoPlayerInfo {
  repeated PlayerShowInfoV2 player = 1;
}

//游戏操作回复
message GameOperateResponse {
  GameTypeV2     type           = 1; //游戏类型
  int64          roomid         = 2; //房间号
  GameOperate    op             = 3; //操作
  int32          opid           = 4;
  OperateResult  result         = 5;
  ErrorCode      code           = 6;
  repeated Sheep sheep          = 7; //可出羊群
  TrackResponse  track          = 8; //赛道信息
  SheepGameGrade sheepGameGrade = 9; //羊得分信息
  int32          msgindex       = 10;//当前消息号
  int64          leftDiamond    = 11;//剩余钻石
  int32          nextDiamond    = 12;//延时所需钻石
}

//游戏操作回复
message GameEventResponse {
  GameTypeV2                 type           = 2; //游戏类型
  int64                      roomid         = 3; //房间号
  GameEvent                  event          = 4; //游戏事件
  repeated GameColorSelect   color          = 5; //颜色分配
  GameStatus                 status         = 6; //游戏状态
  PlayerTurnStatus           ptStatus       = 7; //玩家状态
  int32                      idx            = 8; //用户idx
  int32                      num            = 9; //数量, 当cmd=CHAT_OFF 时，表示是否屏蔽了聊天，此时同 isInChatOff
  DominoRoundInfo            roundResut     = 10; //多米诺单局游戏结果
  DominoGameResult           gResut         = 11; //多米诺整局游戏结果
  repeated PlayerAndDominoes playDominoed   = 12; //多米诺玩家手牌
  repeated DominoPack        remainDominoes = 13; //多米诺补牌区手牌
  int32                      roundNum       = 14; //多米诺当前局数
  DominoPack                 autoPlayDomino = 15; //多米诺自动出的牌
  int32                      deskSort       = 16; //多米诺自动出牌桌面的首尾 1：首  2：尾
  repeated DominoPack        autoFillDomino = 17; //多米诺自动补的牌
  int32                      connectScore   = 18; //连接得分
  int32                      roomType       = 19; //房间类型
  repeated DominoPack        deskDominoes   = 20; //多米诺桌面牌
  int32                      msgindex       = 21;//当前消息号
  int32                      roundLeftTime  = 22;//当前轮次剩余时间
  int32                      roundTime      = 23;//当前轮次时间
}

message PlayerAndDominoes {
  int64               playerId = 1;
  repeated DominoPack dominoes = 2;
}

//
message SideObject {
  int32 leftDominoId   = 1; //左侧多米诺ID
  int32 rightDominoId  = 2; //右侧多米诺ID
  int32 topDomainoId   = 3; //上侧多米诺ID
  int32 bottomDominoId = 4; //下侧多米诺ID
}

message PlayerDomino {
  int32               playerId             = 1;
  string              playerNickName       = 2;
  int32               playerIntergral      = 3;
  int32               playerRoundIntergral = 4;
  repeated DominoPack dominoes             = 5;
  int32               exp                  = 6;//经验
}


//多米诺单局结果
message DominoRoundInfo {
  int32                 winnerId             = 1; //优胜玩家ID
  string                winnerNickName       = 2; //优胜玩家昵称
  int32                 winnerIntergral      = 3; //优胜玩家积分
  int32                 roundIntergral       = 4; //本局积分
  int32                 roundNum             = 5; //局数
  repeated DominoPack   winnerDomino         = 6; //优胜玩家剩余多米诺a
  repeated PlayerDomino losersInfo           = 7; //失败玩家剩余多米诺
  int32                 winnerRoundIntergral = 8; //优胜玩家本局得分
  int32                 winnerExp            = 9;//本局经验
}
//多米诺整局结果
message DominoGameResult {
  int32                 winnerId             = 1; //优胜玩家ID
  string                winnerNickName       = 2; //优胜玩家昵称
  int32                 winnerIntergral      = 3; //优胜玩家积分
  int32                 winnerRoundIntergral = 4; //优胜
  int32                 roundIntergral       = 5; //本局积分
  int32                 roundNum             = 6; //局数
  repeated DominoPack   winnerDomino         = 7; //优胜玩家剩余多米诺
  repeated PlayerDomino losersInfo           = 8; //失败玩家剩余多米诺
  int32                 winGoldNum           = 9;
  int32                 winnerExp            = 10;//本局经验
}
message GameColorSelect {
  int32       idx   = 1; //用户id
  PlayerColor color = 2; //用户颜色
}

message Sheep {
  int32  id              = 1; //羊ID
  int32  playerId        = 2; //用户ID
  int32  weight          = 3; //体重
  int32  damageValue     = 4; //伤害值
  int64  roomId          = 5; //房间id
  int32  trackId         = 6; //赛道id
  double currentPosition = 7; //当前位置
  bool   isOverlap       = 8; //是否重叠
}

//顶羊赛道信息
message TrackResponse {
  repeated Sheep sheep = 5; //羊群
}

//顶羊用户得分通知
message SheepGameGrade {
  int32 playerId = 5; //用户ID
  int32 grade    = 6; //得分情况
}

// 游戏内互动礼物配置响应
message GameGiftCnfListResponse {
  repeated GameGiftCnf gameGiftCnf  = 2; // 互动礼物列表
  int32                limitCount   = 3; // 冷却触发阀值 N
  int32                coolDownTime = 4; // 冷却触发阀值 M
}

message GameGiftSendResponse {
  OperateResult  result        = 1;
  ErrorCode      code          = 2;
  int32          giftId        = 3; // 本次赠送的礼物ID: GameGiftCnf->id
  int32          senderIdx     = 4; // 发送者玩家ID
  int64          senderMoney   = 5; // 发送者当前金币, 非-1表示有变动
  int64          senderDiamond = 6; // 发送者当前钻石, 非-1表示有变动
  repeated int32 rcvIdx        = 7; // 本次接收互动礼物的玩家ID
}

// 游戏互动礼物配置
message GameGiftCnf {
  int32        id           = 1; // 唯一标识，赠送的时候上传该 id
  string       icon         = 2; // 互动礼物图标地址
  int32        money        = 3; // 金币价格
  int32        diamond      = 4; // 钻石价格
  int32        sort         = 5; // 排序权重
  GameGiftType gameGiftType = 6; // 互动礼物类型
}

// 游戏内互动礼物类型
enum GameGiftType {
  GAMEADAPT3 = 0;
  EMOJ       = 1; // 表情
}

// 语音房类别
enum VoiceType{
  VoiceTypeADAPT3 = 0;
  Agora           = 1;//声网
  Zego            = 2;//即构
}

enum GameTypeV2 {
  GameTypeAdapt3 = 0;
  SNAKEANDLADER  = 10020; //蛇棋
  DOMINO         = 10021; //多米诺
  SHEEP          = 10022; //顶🐏
  BLACKANDWHITE  = 10023; //黑白棋
}

enum PlayerColor {
  RED    = 0;
  YELLOW = 2;
  BLUE   = 3;
  GREEN  = 1;
  ORANGE = 4;
}


enum ErrorCode{
  ErrorCodeAdapt3                     = 0;// 适配proto3
  NONE_ERROE                          = 1; //无错误
  LOGIN_FAILD_PW                      = 101; //密码错误
  LOGIN_FAILD_ID                      = 102; //登陆ID错误
  LOGIN_FAILD_OLDSIGN                 = 103; //重复的签名
  LOGIN_FAILD_SIGN                    = 104; //签名错误
  LOGIN_FAILD_TIME                    = 105; //客户端时间错误
  QUICKSTART_FAILD_NOROOM             = 203; //房间已满
  ENTER_ROOM_FAILD_NOROOM             = 204; //房间已满
  ENTER_ROOM_FAILD_ROOMID             = 205; //错误的房间号
  GAMEOPERATE_FAILD_SIT_NOSTOOL       = 301; // 已有人
  GAMEOPERATE_FAILD_SIT_WRONGSTOOLNUM = 302; //错误的凳子ID
  GAMEOPERATE_FAILD_SIT_WRONGTIME     = 303; //错误的操作时间
  NO_MONEY                            = 401; //余额不足
  NO_DIAMOND                          = 402; //钻石不足
  MAX_LIMIT                           = 601; //超出限制
  JOIN_ROOM_FAILD_NOROOM              = 701; //无此房间
  JOIN_ROOM_FAILD_MAX                 = 702; //房间人数已满
  TRACK_FULL                          = 801; //赛道已满
  SHEEP_COOLING                       = 802; //正在冷却
  GAME_OVER                           = 803; //游戏结束
  USERINFO_ERR                        = 901; //用户信息错误
  DOMINO_PLAY_NOTALLOW                = 902; //当前不允许出牌
  GET_DOMINO_ERR                      = 903; //获取骨牌出错
  DOMINO_LEFT_FORBID                  = 904; //左部不允许再放牌
  DOMINO_RIGHT_FORBID                 = 905; //右部不允许再放牌
  DOMINO_CONNECT_ERR                  = 906; //不能相连
  DOMINO_REMAIN_NULL                  = 907; //补牌区已空
  DOMINO_REMAIN_EXSITERR              = 908; //此牌已存在
  DOMINO_PLAY_ERR                     = 909; //出牌错误
  DOMINO_ADDTIME_NOTSELF              = 1000; //非自己回合
  DOMINO_ADDTIME_USED                 = 1001; //已使用过
  DOMINO_ADDTIME_NODIAMOND            = 1002; //钻石不够
  DOMINO_ADDTIME_OUTLIMIT             = 1003; //超出限制次数
  ACCOUNT_BE_FROZEN                   = 403;//账号被冻结
}

//游戏状态
enum GameStatus {
  GAME_PREPARE      = 0; //开始报名
  GAME_START        = 1; //游戏开始
  GAME_ING          = 2; //游戏中
  GAME_RESULT_V2    = 3; //游戏结果
  GAME_END          = 4; //游戏结束
  GAME_ROUNT_RESULT = 5; //游戏单局结果
}

//操作结果
enum OperateResult {
  FAILED  = 0; //失败
  SUCCESS = 1; //成功
}

//游戏操作
enum GameOperate {
  SIT                       = 0; //入座
  UP                        = 1; //离开座位
  SIGN_UP                   = 2; //准备
  SIGN_CANCEL               = 3; //取消准备
  THROW                     = 4; //掷骰子
  RESET_THROW               = 5; //重置投掷
  CHOOSE_CHESS              = 6; //选择棋子
  CHESS_MOVE                = 7; //棋子移动
  SYSTEM_TRUST              = 8; //系统托管
  SYSTEM_TRUST_CANCEL       = 9; //取消系统托管
  DOMINO_PLAY               = 10; //多米诺出牌
  FILL_DOMAIN_CARD          = 11; //补牌
  PUSH_SHEEP                = 12; //放羊
  TRACK_NOTICE              = 13; //赛道通知
  GRADE_ADD                 = 14; //得分通知
  DESK_DOMINO_TOP_FORBID    = 15; //上部禁止放牌
  DESK_DOMINO_BOTTOM_FORBID = 16; //下部禁止放牌
  DOMINO_PLAYER_CHANGE      = 17; //不能连接骨牌，切换玩家
  DESK_DOMINO_LEFT_FORBID   = 18; //左部禁止放牌
  DESK_DOMINO_RIGHT_FORBID  = 19; //右部禁止放牌
  DOMINO_RESTART            = 20; //多米诺重新开始
  DOMINO_PASS               = 21;
  DOMINO_ADD_TIM            = 22;//增加回合时间

  CHAT_OFF                  = 23;//屏蔽聊天
  CHAT_OFF_CANCEL           = 24;//取消屏蔽聊天
}

//游戏操作, 服务端推给客户端的消息
enum GameEvent {
  GAMEEVENTADAPT3           = 0;
  GAME_STATE_CHANGE         = 1; //游戏状态改变
  GAME_COLOR_SELECT         = 2; //玩家颜色选择
  GAME_PLAYER_STATUS_CHANGE = 3; //用户状态改变
  GAME_PLAYER_THROW         = 4; //玩家掷骰子
  GAME_SHOW_RESULT          = 5; //游戏结果
  SAL_CHESS_MOVE            = 7; //蛇棋棋子移动
  SAL_CHESS_MOVE_SNAKE      = 8; //蛇棋🐍移动
  SAL_CHESS_MOVE_LADDER     = 9; //蛇棋梯子移动
  SAL_CHESS_BORN            = 10; //蛇棋棋子移动到初始点
  DOMINO_AUTO_PLAY          = 11; //多米诺自动出牌
  DOMINO_AUTO_FILL          = 12; //多米诺自动补牌
  DOMINO_INFO               = 13;
  DOMINO_ADD_TIME_V2        = 14;//使用延时

  DOMINO_CHAT_OFF           = 15;//屏蔽聊天
}

//玩家游戏状态
enum PlayerTurnStatus {
  THROW_START       = 0; //开始投掷
  THROW_END         = 1; //投掷结束
  RETHROW_START     = 2; //是否重置开始
  RETHROW_END       = 3; //是否重置结束
  CHOOSECHESS_START = 4; //选择数字和棋子开始
  CHOOSECHESS_END   = 5; //选择数字和棋子结束
  CHESSMOVE_START   = 6; //棋子移动开始
  CHESSMOVE_END     = 7; //棋子移动结束
  DOMINO_PLAY_START = 8; //多米诺出牌
  DOMINO_FILL_CARD  = 9; //多米诺补牌
  DOMINO_PASS_SELF  = 10;
}

enum ChessEvent {
  CHESSADAPT3 = 0;
  BORN        = 1;
  MOVE        = 2;
  SNAKE       = 3;
  LADDER      = 4;

}
//踢出原因
enum OutReason {
  OUTADAPT3       = 0;
  LOGIN_OTHER     = 1; //别处登陆
  SERVER_MAINTAIN = 2; //服务器维护
  GM_OUT          = 3; //被踢
}

enum AgoraOperate {
  START_SPEAK = 0; //开始说话
  STOP_SPEAK  = 1; //停止说话
}


