syntax = "proto3";
option java_package = "io.grpc.usermesssage";

//消息信封
message UserMessageEnvelope {
  //序列
  int64 seqNum = 1;
  //消息接收方用户id
  string receiverId = 2;
  //消息发送方用户id
  string senderId = 3;
  //当前发送消息体类型
  EnvelopeType MessageType = 4;
  //消息体
  bytes message = 5;
}

//消息体类型
enum EnvelopeType{
  //显示用信息
  PLAYER_SHOW_DATA = 12;
  //快速进入房间
  QUICK_ENTER_ROOM_RESPONSE = 13;
  //游戏观战聊天消息
  WATCH_GAME_CHAT = 14;
  //游戏操作回复
  GAME_EVENT_RESPONSE = 15;
  //观战人数
  WATCH_PLAYER_RESPONSE = 16;
  ////加入观战首次获取操作列表
  DOMINO_GAME_EVENT_LIST = 17;
  //观战结束 系统维护 游戏结束
  WATCH_OVER_MESSAGE = 18;
}

//1.4.0联赛优化，加入联赛观战后，原生通知服务端（原先4条消息流改成2条，降低流量）
message WatchGameResponse {
  int32 op = 1; //0:游戏消息 1:人数消息 2: 游戏退出  3:对局最新消息
  GameEventResponse gameEventResponse = 2; //游戏消息
  WatchPlayerResponse watchPlayerResponse = 3;//人数消息
  WatchOverMessage watchOverMessage = 4;//退出游戏
  DominoGameEventList list = 5;//观战房间初始消息
}

message WatchRoomReply {
  int32 result = 1;
  int32 watchNum = 2;//当前观战人数
  int64 watchingRoomId = 3;//正在观战的房间id
  DominoGameEventList list = 4;//观战房间初始消息
  string banTalkData = 5;//禁言数据,没禁言时为空串
}

//加入观战首次获取操作列表
message DominoGameEventList {
   QuickEnterRoomResponse quickEnterRoomResponse=1;
   repeated GameEventResponse gameEventResponse = 2;
}

//快速进入房间回复
message QuickEnterRoomResponse {
   int64 roomid = 1; //房间号
   int32 cost = 2; //花费
   int32 sceneId = 3; //场景id
   repeated PlayerShowInfo player = 4; //所有房间用户
   int32 gameType = 5; //多米诺房间类型  1积分场  2轮次场
   int64 showId = 6; //房间号
   int32 isPrivate=7;//是否私有房间
   GameStatus status=8;//游戏状态
   int32 roundNum=9;
   int32 playerRoundTime=10;//玩家轮次时长毫秒
   int32 gameRoundTime=11;//游戏轮次间隔时长毫秒
}

//游戏操作回复
message GameEventResponse {
   GameType type = 1; //游戏类型
   int64 roomid = 2; //房间号
   GameEvent event = 3; //游戏事件
   GameStatus status = 4; //游戏状态
   PlayerTurnStatus ptStatus = 5; //玩家状态
   int32 idx = 6; //用户idx
   DominoRoundInfo roundResut = 7; //多米诺单局游戏结果
   DominoGameResult gResut = 8; //多米诺整局游戏结果
   repeated PlayerAndDominoes playDominoed = 9; //多米诺玩家手牌
   repeated Domino remainDominoes = 10; //多米诺补牌区手牌
   int32 roundNum = 11; //多米诺当前局数
   Domino autoPlayDomino = 12; //多米诺自动出的牌
   int32 deskSort = 13; //多米诺自动出牌桌面的首尾 1：首  2：尾
   repeated Domino autoFillDomino = 14; //多米诺自动补的牌
   int32 connectScore = 15; //连接得分
   int32   roomType = 16; //房间类型
   repeated Domino deskDominoes = 17; //多米诺桌面牌
   int32 roundLeftTime=18;//当前轮次剩余时间
   int32 roundTime=19;//当前轮次时间
   int64 sendMessageTime = 20;//广播消息时间戳(暂时用于新观战)
   repeated Score score = 21;//玩家当前积分
}

message Score{
   int32 idx = 1;
   int32 score=2;//分数
}

//观战人数
message WatchPlayerResponse {
   int32 watchNum = 1;
}

//游戏观战聊天
//message WatchGameChat{
//   PlayerShowInfo playerShowInfo = 1;//用户显示详情
//   string msg = 2;//聊天内容
//}
//游戏观战聊天
message WatchGameChat{
  int32 idx = 1; //用户id
   string nikeName = 2; //昵称
   string faceUrl = 3; //头像地址
   int32 vip = 4;
   int32 royal = 5;
   string msg = 6;//聊天内容Ï
   string extension = 7;//扩展字段
}

message PlayerAndDominoes {
   int64 playerId = 1;
   repeated Domino dominoes = 2;
}

//多米诺单局结果
message DominoRoundInfo {
   int32 winnerId = 1; //优胜玩家ID
   string winnerNickName = 2; //优胜玩家昵称
   int32 winnerIntergral = 3; //优胜玩家积分
   int32 roundIntergral = 4; //本局积分
   int32 roundNum = 5; //局数
   repeated Domino winnerDomino = 6; //优胜玩家剩余多米诺a
   repeated PlayerDomino losersInfo = 7; //失败玩家剩余多米诺
   int32 winnerRoundIntergral = 8; //优胜玩家本局得分
   int32 winnerExp=9;//本局经验
}

//多米诺整局结果
message DominoGameResult {
   int32 winnerId = 1; //优胜玩家ID
   string winnerNickName = 2; //优胜玩家昵称
   int32 winnerIntergral = 3; //优胜玩家积分
   int32 winnerRoundIntergral = 4; //优胜
   int32 roundIntergral = 5; //本局积分
   int32 roundNum = 6; //局数
   repeated Domino winnerDomino = 7; //优胜玩家剩余多米诺
   repeated PlayerDomino losersInfo = 8; //失败玩家剩余多米诺
   int32 winGoldNum=9;
   int32 winnerExp=10;//本局经验
}
message PlayerDomino {
   int32 playerId = 1;
   string playerNickName = 2;
   int32 playerIntergral = 3;
   int32 playerRoundIntergral = 4;
   repeated Domino dominoes = 5;
   int32 exp=6;//经验
}

//多米诺
message Domino {
   int32 id = 1;
   int32 headNum = 2; //首数
   int32 tailNum = 3; //尾数
   int32 leftDominoId = 4; //左侧多米诺ID
   int32 rightDominoId = 5; //右侧多米诺ID
   int32 side = 6; //多米诺方向  0 垂直方向  1 水平方向
   int32 topDomainoId = 7; //上侧多米诺ID
   int32 bottomDominoId = 8; //下侧多米诺ID
   int32 deskSort=9;
}

//用户展示信息
message PlayerShowInfo {
   FPlayerShowInfo fPlayerInfo = 1; //用户id
   int32 score=2;//分数
   int32 roundScore=3;//分数
   int32 talkSkinId=4;//气泡皮肤
   int32 segId=5;//段位
   int32 starNum=6;//传奇之星
   int64 prettyId=7;//显示id
   int32 isInChatOff=8;//1-屏蔽  0-不屏蔽
   int32 roylevel=9; //玩家皇室等级, 1.2.7 add
   bool royVisibility=10; //玩家皇室等级可见性, 1.2.7 add, false-皇室等级对所有用户可见，true-仅自己可见~~
   int32 giftId = 11; // 别人赠送的互动礼物ID
   int32 cardSkinId = 12; // domino牌的皮肤id
   int32 sceneId = 13; // domino桌布皮肤id
   int32 realRoyLevel = 14; // 真实的玩家皇室等级
   bool royalLevelNameAnimation = 15; // rl扫光 1.4.0添加

}

//显示用信息
message FPlayerShowInfo {
   int32 idx = 1; //用户id
   int32 faceId = 2; //特效id
   string nikeName = 3; //昵称
   string faceUrl = 4; //头像地址
   int32 placeId = 5; //所在位置    0大厅 其他对应的gameid -1 离线
   int32 ftype = 6; //好友类型    0
   int32 country = 7; //国家
   int32 level = 8; //等级
   int32 viplevel = 9; //vip等级
   int32 winCount = 10; //获胜次数
   int32 totalCount = 11; //总次数
}

enum GameType {
   GAMETYPE_NULL = 0;
   DOMINO = 10021; //多米诺
}

//游戏操作, 服务端推给客户端的消息
enum GameEvent {
   GAMEEVENT_NULL = 0;
   GAME_STATE_CHANGE = 1; //游戏状态改变
   GAME_PLAYER_STATUS_CHANGE = 3; //用户状态改变
   GAME_PLAYER_THROW = 4; //玩家掷骰子
   GAME_SHOW_RESULT = 5; //游戏结果
   DOMINO_AUTO_PLAY = 11; //多米诺自动出牌
   DOMINO_AUTO_FILL = 12; //多米诺自动补牌
   DOMINO_INFO = 13;
   DOMINO_ADD_TIME = 14;//使用延时

}

//游戏状态
enum GameStatus {
   GAME_PREPARE = 0; //开始报名
   GAME_START = 1; //游戏开始
   GAME_ING = 2; //游戏中
   GAME_RESULT = 3; //游戏结果
   GAME_END = 4; //游戏结束
   GAME_ROUNT_RESULT = 5; //游戏单局结果
}

//玩家游戏状态
enum PlayerTurnStatus {
   THROW_START = 0; //开始投掷
   THROW_END = 1; //投掷结束
   RETHROW_START = 2; //是否重置开始
   RETHROW_END = 3; //是否重置结束
   CHOOSECHESS_START = 4; //选择数字和棋子开始
   CHOOSECHESS_END = 5; //选择数字和棋子结束
   CHESSMOVE_START = 6; //棋子移动开始
   CHESSMOVE_END = 7; //棋子移动结束
   DOMINO_PLAY_START = 8; //多米诺出牌
   DOMINO_FILL_CARD = 9; //多米诺补牌
   DOMINO_PASS_SELF = 10;//多米诺PASS
}
//游戏观战结束，系统踢出玩家
message WatchOverMessage{
    ErrorCode code = 1;
}

enum ErrorCode{
   SUCCESS = 0;
   GAME_OVER = 1;//游戏结束
   SYSTEM_UPDATE =2;//系统维护
}