syntax = "proto3";
option java_package = "io.grpc.snakeladder";

service SnakeLadder {
  rpc call(stream SnakeLadderMessage) returns (stream SnakeLadderResponse){}
  rpc getSnakeInfo(SnakeInfoReq) returns (SnakeInfoReply) {};

  rpc message(stream SnakeLadderStreamRequest) returns (stream SnakeLadderStreamResponse){}

  rpc gameOperate(GameOperateRequest) returns (SnakeMessage) {};
  rpc playerChatToAll(GameRoomPlayerChatToAllRequestAndResponse) returns (SnakeMessage) {};
  rpc getAgoraToken(GetAgoraTokenRequest) returns (GetAgoraTokenResponse) {};
  rpc getZegoToken(GetZegoTokenRequest) returns (GetZegoTokenResponse) {};
  rpc flushCurrency(FlushCurrencyRequest) returns (FlushCurrencyResponse) {};
  rpc quitRoom(QuitRoomRequest) returns (SnakeMessage) {};
  rpc gameGiftCnfList(GameGiftCnfListRequest) returns (GetGameGiftCnfListResponse) {};
  rpc gameGiftSend(GameGiftSendRequest) returns (SnakeMessage) {};
}

//游戏命令
enum Command{
  CommandAdapt3 = 0;// 适配proto3
  LOGIN = 10;//登陆

  TICKET_LOGIN = 11;//票据登陆

  GAME_DATA_INFO = 12;//游戏相关信息

  QUICKSTART = 20;//快速开始
  QUIT_ROOM = 21;//主动退出房间

  GAMEOPERATE = 30;//游戏操作
  GAME_EVENT = 31; //游戏操作
  GAMESTATUS = 40;//游戏状态

  GAME_MAP_CHESS_MOVE = 46;//走棋子
  GAMERESULT = 49;//游戏结果

  GAMEPLAYERSTATUS = 50;//玩家状态 undo 投骰子 移动棋子开始结束

  PLAYER_ENTER = 60;//玩家进入

  PLAYER_CHAT_ALL = 70;//聊天
  UPDATA_PLAYER_COIN = 80;//更新金币
  UPDATA_PLAYER_LEVEL = 81;//玩家升级

  FLUSH_CURRENCY = 82;//刷新货币 参考domino
  GET_AGORA_TOKEN = 90;
  AGORA_OPERATE = 91;
  GET_ZEGO_TOKEN = 92;

  PLAYER_REPORT = 110;//举报

  GAME_GIFT_CNF_LIST = 111; // 获取游戏内互动礼物配置列表 1.3.1 add
  GAME_GIFT_SEND = 112; // 游戏内互动礼物赠送, 1.3.1 add

  SEG_OPERATE=119;//段位相关操作

  OUT_GAME = 126;
}

enum ErrorCode{
  ErrorCodeAdapt3 = 0;// 适配proto3
  NONE_ERROE = 1;//无错误
  LOGIN_FAILD_PW = 101;//密码错误
  LOGIN_FAILD_ID = 102;//登陆ID错误
  LOGIN_FAILD_OLDSIGN = 103;//重复的签名
  LOGIN_FAILD_SIGN = 104;//签名错误
  LOGIN_FAILD_TIME = 105;//客户端时间错误
  QUICKSTART_FAILD_NOROOM = 203;//房间已满
  ENTER_ROOM_FAILD_NOROOM = 204;//房间已满
  ENTER_ROOM_FAILD_ROOMID = 205;//错误的房间号
  GAMEOPERATE_FAILD_SIT_NOSTOOL = 301;// 已有人
  GAMEOPERATE_FAILD_SIT_WRONGSTOOLNUM = 302;//错误的凳子ID
  GAMEOPERATE_FAILD_SIT_WRONGTIME = 303;//错误的操作时间
  NO_MONEY = 401;//余额不足
  NO_DIAMOND = 402;//钻石不足
  ACCOUNT_BE_FROZEN = 403;//1.2.2 add 账号被冻结
  MAX_LIMIT = 601;//超出限制
  JOIN_ROOM_FAILD_NOROOM = 701;//无此房间
  JOIN_ROOM_FAILD_MAX = 702;//房间人数已满
}

message SnakeInfoReq{
  int64 roomId = 1;
}

message SnakeInfoReply{
  int32 roomSize = 1;
  int32 clientSize = 2;
  int64 gameStartTime = 3;
  int32 snakeLadderVersion = 4;
  RoomInfoPlayerInfo player = 5; //所有房间用户
  GameStatus status = 6;//游戏状态
  int32 msgIndex = 7;//消息序号
  repeated int64 roomIds = 8;
}

message SnakeLadderMessage{
  Command command = 1;
  int64 roomId = 2;
  TicketLoginRequest ticketLogin = 3;
  GameOperateRequest gameOperateRequest = 4;
  GameRoomPlayerChatToAllRequestAndResponse chatToAll = 5;
  GetAgoraTokenRequest agoraTokenRequest = 6;
  GetZegoTokenRequest zegoTokenRequest = 8;
  FlushCurrencyRequest flushCurrencyRequest = 9;
  QuitRoomRequest  quitRoomRequest = 10;
  GameGiftCnfListRequest gameGiftCnfListRequest = 11;
  GameGiftSendRequest gameGiftSendRequest = 12;

}

message SnakeLadderResponse{
  Command command = 1;
  int64 roomId = 2;
  LoginResponse loginResponse = 3;
  GameGiftCnfListRespone gameGiftCnfList = 4;
  GameOperateResponse gameOperateResponse = 5;
  GameRoomPlayerChatToAllRequestAndResponse chatResponse = 6;
  GetAgoraTokenResponse agoraTokenResponse = 7;
  GetZegoTokenResponse zegoTokenResponse = 9;
  FlushCurrencyResponse flushCurrencyResponse = 10;
  QuickEnterRoomResponse quickEnterRoomResponse = 11;
  GameDataResponse gameDataResponse = 12;
  GameStatusResponse gameStatusResponse = 13;
  QuitRoomResponse quitRoomResponse = 14;
  GamePlayerStatusResponse gamePlayerStatusResponse = 15;
  GameEventResponse gameEventResponse = 16;
  GameResultResponse gameResultResponse = 17;
  OutGameResponse  outGameResponse = 18;
  GameGiftSendResponse gameGiftSendResponse = 19;
  UpdateCoinResponse updateCoinResponse = 20;
}

message AuthRequest {
  string app_id = 1;
  oneof identity {
    string open_id = 2;
    int32 user_id = 3;
    int64 long_user_id = 4;
  }
  string token = 5;
  string sign = 6;
  string device_Id = 7;//设备Id
  int32 channel_Id = 8;//渠道Id 0apple 1google,2:未知，3:U3D_ios,4:U3D_android, 5:cocos_ios,6:cocos_android,7:huawei 8:honor 9:小程序安卓 10:小程序ios
  string version = 9;
  int64 timestamp = 10;//时间戳
}

message AuthResponse{

}

message TyrOption {
    // todo route key 调整为oneof
    int64 ring_route_key = 1;//一致性hash路由标识
    string fixed_route_key = 2;// 固定路由标识
    string fixed_address_key = 3;// 固定地址

    string group_id = 4;//群组Id
}

//登陆
message TicketLoginRequest {
  int32 idx = 1; //用户id
  string token = 2;//用户token
  int32 msgindex = 3;//消息序号默认0
  string version = 4;//登陆时传递主包的版本号, etc: *******
  int32 hdVersion = 5;//登陆时传递HD版本号
}

//登陆回复
message LoginResponse{
  OperateResult result = 1;
  PlayerInfo palyerInfo = 2;
  RoomList roomlist = 3;
  ErrorCode code = 4;
  int32 watchIdx = 5;
  string banTalkData = 6; // 禁言数据,没禁言时为空串
  VoiceType voiceType = 7; // 语音房类别,1.3.2 add, 1.3.2 以前版本始终是声网，1.3.2以后版本开关控制
  bool gameGiftOpen = 8; // 互动表情功能开关，true:打开,false:关闭
}

//游戏信息 GAME_DATA_INFO
message GameDataResponse{
  Command command = 1 ;
  int32 idx = 2; //房间id
  int64 roomId = 3; //房间id
  GameMapInfo gameMapInfo = 4;//地图信息
  repeated ChessData chessData= 5; //棋子信息

}

//快速进入房间请求 QUICKSTART
message QuickEnterRoomRequest {
  int32 type = 1 ; //1私密 0公开PlayerTurnStatus
}

//快速进入房间回复 QUICKSTART
message QuickEnterRoomResponse {
  Command command = 1 ;
  OperateResult result = 2;
  int64 roomid = 3; //房间号
  int32 cost = 4; //花费
  RoomInfoPlayerInfo player = 6; //所有房间用户
  ErrorCode code = 7;
  RoomType roomType = 8;
  RoomGroup group = 9;
  int64 showRoomId=10;//显示id
  int32 isPrivate = 11;//是否私有房间
  GameStatus status = 12;//游戏状态
  int32 sceneId = 13;//场景id
  int32 msgindex = 14;//当前消息号
  int32 maxReThrowNum = 15;//最大重置次数
  int32 maxTurnReThrowNum = 16;//每轮最大重置次数
  repeated int32 reThrowCost = 17;//每次重置消耗数量
  int32 currentReThrowNum = 18;//当前重置次数
  int32 currentTurnReThrowNum = 19;//当回合重置次数
  int32 freeReThrowNum = 20;//当前可免费重置次数
  int32 quitPunishTime = 21; // 强退惩罚的1个触发时间阀值, 若在该值以内强退，则给客户端惩罚提示
  int32 gameBeginTime = 22; // 游戏开始时间戳
  int32 throwDiceWaitTime = 23;//用户投掷等待时间
}


//退出房间房间请求 QUIT_ROOM
message QuitRoomRequest {
  Command command = 1;
  int32 idx = 2;
  int64 roomId = 3;
}

//退出房间 QUIT_ROOM
message QuitRoomResponse {
  Command command = 1 ;
  int64 roomId = 2; //房间号
  OperateResult result = 3 ;
  int32 idx = 4;
  ErrorCode code = 5;
}


//用户信息
message PlayerInfo{
  int64 money = 1;//金币
  int64 gold = 2;//钻石
  int32 exp = 3;//经验值
  PlayerShowInfoV2 playerShowInfo = 4;//用户显示信息
  bool isRecharged = 5;//是否首充
  // int32 winCount=6;//获胜次数
  // int32 totalCount=7;//总次数
}

//用户展示信息
message PlayerShowInfoV2{
  FPlayerShowInfo fPlayerInfo = 1;//用户id
  int32 sitNum = 2;//座位【-1】没入座
  int32 isSignUp = 3;//0未准备1准备
  int32 winIndex = 4;//1-4  1未没有排名
  int32 isLeft = 5;//0没有离去 1离去
  int32 isFighted = 6;//0没有战斗  1战斗过
  int32 diceNum = 7;//骰子
  int32 isInSysTrust = 9;//0没有托管  1托管中
  int32 reThrowNum = 10;//已重置次数
  int32 reThrowCost = 11;//重置需要消耗
  int32 diceSkinId = 12;//骰子皮肤
  int32 talkSkinId = 13;//气泡皮肤
  int32 chessSkinId = 14;//棋子皮肤
  int32 segId = 15;//段位
  int32 starNum = 16;//传奇之星
  int64 prettyId = 17;//显示id
  int32 isInChatOff = 18;//0开启屏蔽聊天  1关闭屏蔽聊天
  int32 isQuit = 19;//0未主动离开  1主动离开
  int32 roylevel = 20; //皇室等级 1.2.7 add
  bool royVisibility = 21; //皇室等级可见性,false:所有人可见，true：仅自己可见
  int32 giftId = 22; // 别人赠送的互动礼物ID
  int32 playColor = 23;//玩家颜色 0 红 3：蓝色
  int32 realRoyLevel = 24; // 真实的玩家皇室等级
  bool royalLevelNameAnimation = 25; // rl扫光 1.4.0添加
}

//显示用信息
message FPlayerShowInfo{
  int32 idx = 1;//用户id
  int32 faceId = 2;//特效id
  string nikeName = 3;//昵称
  string faceUrl = 4;//头像地址
  int32 placeId = 5;//所在位置    0大厅 其他对应的gameid -1 离线
  int32 ftype = 6;//好友类型    0
  int32 country = 7;//国家
  int32 level = 8;//等级
  int32 viplevel = 9;//vip等级
  int32 winCount = 10;//获胜次数
  int32 totalCount = 11;//总次数
}

//房间列表
message RoomList{
  repeated RoomInfo roomInfo = 1;
}

//房间信息
message RoomInfo{
  RoomType roomType = 1;//房间类型
  RoomGroup group = 2;//道具or竞技
  string name = 3;//房间名字
}

//房间用户列表
message RoomInfoPlayerInfo {
  repeated PlayerShowInfoV2 player = 1;
}

//走棋子 GAME_MAP_CHESS_MOVE
message ChessMoveResponse{
  Command command = 1;
  int32 chessId = 2;//棋子id
  int32 gridPosition = 3;//要去的格子坐标
  int32 time = 4;//棋子移动需要时间
  int32 msgindex = 5;//消息序号默认0
}

//游戏结果 GAMERESULT
message GameResultResponse{
  Command command = 1;
  int32 winMoney1 = 2;//用户id
  int32 winMoney2 = 3;//用户id
  repeated PlayerShowInfoV2 player = 4;
  int32 winnerExp = 5; // 游戏经验
}


//游戏房间组类型
enum RoomGroup {
  RoomGroup_SPORTS_MODE = 0;//竞技
}
//游戏房间类型
enum RoomType {
  Classic = 0;//正常
}

// 语音房类别
enum VoiceType{
  VoiceTypeAdapt3 = 0;// 适配proto3
  Agora = 1;//声网
  Zego = 2;//即构
}

// 获取游戏内互动礼物配置
message GameGiftCnfListRequest {
  Command command = 1;
  int32 idx = 2;
  int64 roomId = 3;
}

// 游戏内互动礼物配置响应
message GameGiftCnfListRespone {
  repeated GameGiftCnf gameGiftCnf = 1; // 互动礼物列表
  int32 limitCount = 2; // 冷却触发阀值 N
  int32 coolDownTime = 3; // 冷却触发阀值 M
}

// 游戏内互动礼物配置响应
message GetGameGiftCnfListResponse {
  Command command = 1;
  int64 roomId = 2;
  repeated GameGiftCnf gameGiftCnf = 3; // 互动礼物列表
  int32 limitCount = 4; // 冷却触发阀值 N
  int32 coolDownTime = 5; // 冷却触发阀值 M
}

// 游戏互动礼物配置
message GameGiftCnf {
  int32 id = 1; // 唯一标识，赠送的时候上传该 id
  string icon = 2; // 互动礼物图标地址
  int32 money = 3; // 金币价格
  int32 diamond = 4; // 钻石价格
  int32 sort = 5; // 排序权重
  GameGiftType gameGiftType = 6; // 互动礼物类型
}

// 游戏内互动礼物类型
enum GameGiftType {
  GameGiftTypeAdapt3 = 0;// 适配proto3
  EMOJ = 1; // 表情
}

enum PlayerColor {
  RED = 0;
  BLUE = 3;
}

//游戏操作 GAME_OPERATE
message GameOperateRequest {
  Command command = 1;
  int32 idx = 2; //用户id
  GameOperate op = 3; //操作
  int32 msgIndex = 4;//当前消息号
  int64 roomId = 5;
}
//游戏操作回复 GAME_OPERATE
message GameOperateResponse {
  Command command = 1;
  int64 roomId = 2; //房间号
  GameOperate op = 3; //操作
  int32 throwNum = 4;//掷的数字
  int32 idx = 5;//用户id
  OperateResult result = 6 ;
  ErrorCode code = 7;
  int32 msgIndex = 8;//当前消息号
  int32 sitNum = 9;//座位号
  int32 moveStatus = 10;//0能走 1：不能走
}
//游戏操作回复 GAME_EVENT
message GameEventResponse {
  Command command = 1;
  GameIdType type = 2; //游戏类型
  int64 roomId = 3; //房间号
  GameEvent event = 4; //游戏事件
  int32 oldPos = 5;//之前的位置
  int32 targetPos = 6;//目标位置
  int32 throwNum = 7;//掷的数字
  int32 idx = 8; //用户idx
  int32 num = 9; //数量, 当cmd=CHAT_OFF 时，表示是否屏蔽了聊天，此时同 isInChatOff
  int32   roomType = 10; //房间类型
  int32 msgIndex = 11;//当前消息号

}

//游戏状态广播 GAMESTATUS
message GameStatusResponse{
  Command command = 1;
  GameStatus status = 2;
  int32 msgindex = 3;//消息序号默认0
}

// 有玩家进入房间，广播给其余玩家 PLAYER_ENTER
message GamePlayerEnterResponse{
  Command command = 1;
  PlayerShowInfoV2 player = 2;
}

message PlayerColorSelect{
  int32 idx = 1;//用户id
  PlayerColor playerColor = 2;
}


//游戏用户状态广播
message GamePlayerStatusResponse{
  Command command = 1;
  int32 idx = 2;//用户id
  PlayerTurnStatus status = 3;
  int32 msgindex = 4;//消息序号默认0
  int32 costDiamond=5;//消耗钻石
}

//聊天  PLAYER_CHAT_ALL
message GameRoomPlayerChatToAllRequestAndResponse {
  Command command = 1 ;
  int32 idx = 2; //用户id
  int64 roomId = 3; //房间号
  string msg = 4; //消息
  string extension = 5;//扩展字段
}

//更新余额 UPDATA_PLAYER_COIN
message UpdataCoinRequest {
  Command command = 1;
  int32 type = 2; //0金币 1钻石
  int64 value = 3; //余额
}

//更新余额 UPDATA_PLAYER_COIN
message UpdateCoinResponse {
  Command command = 1 ;
  int32 type = 2; //0金币 1钻石
  int64 value = 3; //余额
}

//玩家升级 UPDATA_PLAYER_LEVEL
message MassagePlayerLevelUPResponse {
  Command command = 1;
  int32 level = 2; //新等级
  int32 exp = 3; //新经验
}

//刷新货币 FLUSH_CURRENCY
message FlushCurrencyRequest{
  Command command = 1;
  int32 idx = 2;//用户id
  int64 roomId = 3;
}

//刷新货币 FLUSH_CURRENCY
message FlushCurrencyResponse{
  Command command = 1;
  int64 money = 2;//金币
  int64 gold = 3;//钻石
  int32 exp = 4;
  int32 level = 5;
  int32 vipExp = 6;
  int32 vipLevel = 7;
}

//GET_AGORA_TOKEN
message GetAgoraTokenRequest{
  Command command = 1;
  int32 idx = 2;//用户id
  int64 roomId = 3;
}
//GET_AGORA_TOKEN
message GetAgoraTokenResponse{
  Command command = 1;
  string token = 2;//token
  string cName = 3;//频道名
}

//AGORA_OPERATE
message AgoraOperateRequestAndResponse{
  Command command = 1;
  int32 idx = 2;//用户id
  int64 roomId = 3;//房间号
  AgoraOperate operate = 4;//
}

// 获取 即构 token GET_ZEGO_TOKEN
message GetZegoTokenRequest{
  Command command = 1;
  int32 idx = 2;//用户id
  int32 version = 3;//版本号
  int64 roomId = 4;
}

//GET_ZEGO_TOKEN
message GetZegoTokenResponse{
  Command command = 1;
  string token = 2;//token
  string cName = 3;//频道名
}

// 发送互动礼物 GAME_GIFT_SEND
message GameGiftSendRequest {
  Command command = 1 ;
  int32 idx = 2;
  int32 giftId = 3; // 本次赠送的互动礼物ID: GameGiftCnf->id
  repeated int32 rcvIdx = 4; // 本次接收互动礼物的玩家ID
  int64 roomId = 5;
}

message GameGiftSendResponse {
  OperateResult result = 1;
  ErrorCode code = 2;
  int32 giftId = 3; // 本次赠送的礼物ID: GameGiftCnf->id
  int32 senderIdx = 4; // 发送者玩家ID
  int64 senderMoney = 5; // 发送者当前金币, 非-1表示有变动
  int64 senderDiamond = 6; // 发送者当前钻石, 非-1表示有变动
  repeated int32 rcvIdx = 7; // 本次接收互动礼物的玩家ID
}

//OUT_GAME
message OutGameResponse{
  Command command = 1;
  OutReason reason = 2;
  int32 idx = 3;//被提出人idx
  int64 bannedTime = 4;//封禁截止时间
}

message GameMapInfo{
  int32 sceneId = 1;//地图id
  int32 mapSkinId = 2;//地图皮肤id
  int32 row = 3;//地图行数
  int32 col = 4;//地图列数
  repeated MapInfo mapInfo = 5;//地图具体信息
}

message MapInfo{
  string gId = 1;//蛇/梯子组合编号 id
  string skinId = 2;//皮肤id
  int32 type = 3;//蛇1 梯子2
  string trace = 4;//轨迹信息
  int32 head = 5;//蛇头  梯头所在 格子id
  int32 end = 6;//蛇尾   梯尾所在 格子id
  int32 pos = 7;//蛇/梯子 在地图中位置 的格子id
  int32 scaleX = 8;//蛇或者梯子是否翻转
}

//棋子信息
message ChessData{
  int32 idx = 1;
  int32 curPos = 2;
}

message GameColorSelect {
  int32 idx = 1; //用户id
  PlayerColor color = 2; //用户颜色
}

message SnakeMessage{
  OperateResult result = 1;
  ErrorCode code = 2;
}

message SnakeLadderStreamResponse{
  Command command = 1;
  int64 roomId = 2;
  bytes data = 3;
}

message SnakeLadderStreamRequest{
  Command command = 1;
  int64 roomId = 2;
  bytes data = 3;
}

enum AgoraOperate{
  START_SPEAK = 0;//开始说话
  STOP_SPEAK = 1;//停止说话
}

enum GameIdType {
  GameTypeAdapt3 = 0;// 适配proto3
  SNAKEANDLADER = 10020; //蛇棋
  DOMINO = 10021; //多米诺
  SHEEP = 10022; //顶🐏
  BLACKANDWHITE = 10023; //黑白棋
}

//游戏状态
enum GameStatus {
  GAME_PREPARE = 0; //开始报名
  GAME_START = 1; //游戏开始
  GAME_ING = 2; //游戏中
  GAME_RESULT_NOTICE = 3; //游戏结果通知
  GAME_END = 4; //游戏结束
  GAME_ROUNT_RESULT = 5; //游戏单局结果
}

//游戏操作
enum GameOperate {
  GameOperateAdapt3 = 0;// 适配proto3
  THROW = 4; //掷骰子
  RESET_THROW = 5; //重置投掷
  CHOOSE_CHESS = 6; //选择棋子
  CHESS_MOVE = 7; //棋子移动
  SYSTEM_TRUST = 8; //系统托管
  SYSTEM_TRUST_CANCEL = 9; //取消系统托管
  GRADE_ADD = 14; //得分通知
  CHAT_OFF = 23;//屏蔽聊天
  CHAT_OFF_CANCEL = 24;//取消屏蔽聊天
}

//游戏操作, 服务端推给客户端的消息
enum GameEvent {
  GameEventAdapt3 = 0;// 适配proto3
  SAL_CHESS_MOVE = 7; //蛇棋棋子移动
  SAL_CHESS_MOVE_SNAKE = 8; //蛇棋🐍移动
  SAL_CHESS_MOVE_LADDER = 9; //蛇棋梯子移动
  SAL_CHESS_BORN = 10; //蛇棋棋子移动到初始点
}


//玩家游戏状态
enum PlayerTurnStatus {
  THROW_START = 0; //开始投掷
  THROW_END = 1; //投掷结束
  RETHROW_START = 2; //是否重置开始
  RETHROW_END = 3; //是否重置结束
  CHESSMOVE_START = 6; //棋子移动开始
  CHESSMOVE_END = 7; //棋子移动结束
}

enum OutReason{
  OutReasonDapt3 = 0;// 适配proto3
  LOGIN_OTHER = 1;//别处登陆
  SERVER_MAINTAIN = 2;//服务器维护
  GM_OUT = 3;//被踢
  BACK_ACCOUNT = 4;//找回账号被踢
}

//操作结果
enum OperateResult {
  FAILED = 0; //失败
  SUC = 1; //成功
}
