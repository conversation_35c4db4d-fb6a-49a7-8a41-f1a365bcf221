syntax = "proto2";

option java_package = "com.yalla.protobuf";
option java_outer_classname = "VsGameProtobuf";


//游戏命令
enum Command {
    PLAYER_LOGIN = 10; //登陆

    TICKET_LOGIN = 11; //票据登陆

    QUICK_START = 20; //快速开始
    QUIT_ROOM = 21; //退出房间
    JOIN_ROOM = 22; //加入房间


    GAME_OPERATE = 30; //游戏操作
    GAME_EVENT = 31; //游戏操作

    PLAYER_CHAT_ALL = 70; //聊天
    UPDATA_PLAYER_COIN = 80; //刷新用户金币钻石
    UPDATA_PLAYER_LEVEL = 81; //玩家升级

    GET_AGORA_TOKEN = 90; //获取语音频道token
    AGORA_OPERATE = 91; //语音频道操作
    GET_ZEGO_TOKEN=92;
    FRIEND_ADD_MSG = 100; //添加好友
    FRIEND_LIST = 101; //好友列表
    FRIEND_INVITE = 102; //邀请好友

    GAME_GIFT_CNF_LIST = 110; // 获取游戏内互动礼物配置列表 1.3.1 add
    GAME_GIFT_SEND = 111; // 游戏内互动礼物赠送, 1.3.1 add

    OUT_GAME = 126; //踢出服务
    HEART = 127; //心跳包
}
enum GameType {
    SNAKEANDLADER = 10020; //蛇棋
    DOMINO = 10021; //多米诺
    SHEEP = 10022; //顶🐏
    BLACKANDWHITE = 10023; //黑白棋
}

enum PlayerColor {
    RED = 0;
    YELLOW = 2;
    BLUE = 3;
    GREEN = 1;
    ORANGE = 4;
}

enum ErrorCode {
    NONE_ERROE = 1; //无错误
    LOGIN_FAILD_PW = 101; //密码错误
    LOGIN_FAILD_ID = 102; //登陆ID错误
    LOGIN_FAILD_OLDSIGN = 103; //重复的签名
    LOGIN_FAILD_SIGN = 104; //签名错误
    LOGIN_FAILD_TIME = 105; //客户端时间错误
    QUICKSTART_FAILD_NOROOM = 203; //房间已满
    ENTER_ROOM_FAILD_NOROOM = 204; //房间已满
    ENTER_ROOM_FAILD_ROOMID = 205; //错误的房间号
    GAMEOPERATE_FAILD_SIT_NOSTOOL = 301; // 已有人
    GAMEOPERATE_FAILD_SIT_WRONGSTOOLNUM = 302; //错误的凳子ID
    GAMEOPERATE_FAILD_SIT_WRONGTIME = 303; //错误的操作时间
    NO_MONEY = 401; //余额不足
    NO_DIAMOND = 402; //钻石不足
    MAX_LIMIT = 601; //超出限制
    JOIN_ROOM_FAILD_NOROOM = 701; //无此房间
    JOIN_ROOM_FAILD_MAX = 702; //房间人数已满
    TRACK_FULL = 801; //赛道已满
    SHEEP_COOLING = 802; //正在冷却
    GAME_OVER = 803; //游戏结束
    USERINFO_ERR = 901; //用户信息错误
    DOMINO_PLAY_NOTALLOW = 902; //当前不允许出牌
    GET_DOMINO_ERR = 903; //获取骨牌出错
    DOMINO_LEFT_FORBID = 904; //左部不允许再放牌
    DOMINO_RIGHT_FORBID = 905; //右部不允许再放牌
    DOMINO_CONNECT_ERR = 906; //不能相连
    DOMINO_REMAIN_NULL = 907; //补牌区已空
    DOMINO_REMAIN_EXSITERR = 908; //此牌已存在
    DOMINO_PLAY_ERR = 909; //出牌错误
    DOMINO_ADDTIME_NOTSELF = 1000; //非自己回合
    DOMINO_ADDTIME_USED = 1001; //已使用过
    DOMINO_ADDTIME_NODIAMOND = 1002; //钻石不够
    DOMINO_ADDTIME_OUTLIMIT = 1003; //超出限制次数
    ACCOUNT_BE_FROZEN = 403;//账号被冻结
}
//游戏状态
enum GameStatus {
    GAME_PREPARE = 0; //开始报名
    GAME_START = 1; //游戏开始
    GAME_ING = 2; //游戏中
    GAME_RESULT = 3; //游戏结果
    GAME_END = 4; //游戏结束
    GAME_ROUNT_RESULT = 5; //游戏单局结果
}
//操作结果
enum OperateResult {
    FAILED = 0; //失败
    SUCCESS = 1; //成功
}

//游戏操作
enum GameOperate {
    SIT = 0; //入座
    UP = 1; //离开座位
    SIGN_UP = 2; //准备
    SIGN_CANCEL = 3; //取消准备
    THROW = 4; //掷骰子
    RESET_THROW = 5; //重置投掷
    CHOOSE_CHESS = 6; //选择棋子
    CHESS_MOVE = 7; //棋子移动
    SYSTEM_TRUST = 8; //系统托管
    SYSTEM_TRUST_CANCEL = 9; //取消系统托管
    DOMINO_PLAY = 10; //多米诺出牌
    FILL_DOMAIN_CARD = 11; //补牌
    PUSH_SHEEP = 12; //放羊
    TRACK_NOTICE = 13; //赛道通知
    GRADE_ADD = 14; //得分通知
    DESK_DOMINO_TOP_FORBID = 15; //上部禁止放牌
    DESK_DOMINO_BOTTOM_FORBID = 16; //下部禁止放牌
    DOMINO_PLAYER_CHANGE = 17; //不能连接骨牌，切换玩家
    DESK_DOMINO_LEFT_FORBID = 18; //左部禁止放牌
    DESK_DOMINO_RIGHT_FORBID = 19; //右部禁止放牌
    DOMINO_RESTART = 20; //多米诺重新开始
    DOMINO_PASS = 21;
    DOMINO_ADD_TIM = 22;//增加回合时间

    CHAT_OFF=23;//屏蔽聊天
    CHAT_OFF_CANCEL=24;//取消屏蔽聊天
}
//游戏操作, 服务端推给客户端的消息
enum GameEvent {
    GAME_STATE_CHANGE = 1; //游戏状态改变
    GAME_COLOR_SELECT = 2; //玩家颜色选择
    GAME_PLAYER_STATUS_CHANGE = 3; //用户状态改变
    GAME_PLAYER_THROW = 4; //玩家掷骰子
    GAME_SHOW_RESULT = 5; //游戏结果
    SAL_CHESS_MOVE = 7; //蛇棋棋子移动
    SAL_CHESS_MOVE_SNAKE = 8; //蛇棋🐍移动
    SAL_CHESS_MOVE_LADDER = 9; //蛇棋梯子移动
    SAL_CHESS_BORN = 10; //蛇棋棋子移动到初始点
    DOMINO_AUTO_PLAY = 11; //多米诺自动出牌
    DOMINO_AUTO_FILL = 12; //多米诺自动补牌
    DOMINO_INFO = 13;//桌面牌、手牌、剩余牌信息
    DOMINO_ADD_TIME = 14;//使用延时
   
    DOMINO_CHAT_OFF=15;//屏蔽聊天
}

//玩家游戏状态
enum PlayerTurnStatus {
    THROW_START = 0; //开始投掷
    THROW_END = 1; //投掷结束
    RETHROW_START = 2; //是否重置开始
    RETHROW_END = 3; //是否重置结束
    CHOOSECHESS_START = 4; //选择数字和棋子开始
    CHOOSECHESS_END = 5; //选择数字和棋子结束
    CHESSMOVE_START = 6; //棋子移动开始
    CHESSMOVE_END = 7; //棋子移动结束
    DOMINO_PLAY_START = 8; //多米诺出牌
    DOMINO_FILL_CARD = 9; //多米诺补牌
    DOMINO_PASS_SELF = 10;
}

enum ChessEvent {
    BORN = 1;
    MOVE = 2;
    SNAKE = 3;
    LADDER = 4;

}
//踢出原因
enum OutReason {
    LOGIN_OTHER = 1; //别处登陆
    SERVER_MAINTAIN = 2; //服务器维护
    GM_OUT = 3; //被踢
}

//踢出原因
message OutGameResponse {
    required Command command = 1 [default = OUT_GAME];
    required OutReason reason = 2;
    optional int32 idx = 3;
}
//心跳
message HeartRequestAndResponse {
    required Command command = 1 [default = HEART];
}

//登陆
message TicketLoginRequest {
    required Command command = 1 [default = TICKET_LOGIN];
    required int32 idx = 2; //用户id
    required string token = 3; //用户token
    required int64 roomid = 4; //房间id
    optional int32 msgindex=5;//当前消息号
    optional string version=6;//登陆时传递主包的版本号, etc: 1.2.8.0
    optional int32 hdVersion=7;//登陆时传递HD版本号
}

//登陆
message LoginRequest {
    required Command command = 1 [default = PLAYER_LOGIN];
    required int32 idx = 2; //用户id
    //required string token=3;//用户token
    required string time = 3; //时间戳
    required string sign = 4; //MD5（idx+token+time+key
    required int32 pid = 5; //渠道id 0apple 1google
    required string did = 6; //设备唯一码
    required string version = 7; //版本号
}
//登陆回复
message LoginResponse {
    required Command command = 1 [default = PLAYER_LOGIN];
    required OperateResult result = 2 [default = SUCCESS];
    optional PlayerInfo palyerInfo = 3;
    optional ErrorCode code = 4;
    optional string banTalkData=5; // 禁言数据,没禁言时为空串
    optional bool gameGiftOpen=6; // 互动表情功能开关，true:打开,false:关闭
    optional VoiceType voiceType=7; // 语音房类别,1.3.2 add, 1.3.2 以前版本始终是声网，1.3.2以后版本开关控制
}
//用户信息
message PlayerInfo {
    required int64 money = 1; //金币
    required int64 gold = 2; //钻石
    required int32 exp = 3; //经验值
    required PlayerShowInfo playerShowInfo = 4; //用户显示信息
    required bool isRecharged = 5; //是否首充
    //required int32 winCount=6;//获胜次数
    //required int32 totalCount=7;//总次数
}
//用户展示信息
message PlayerShowInfo {
    required FPlayerShowInfo fPlayerInfo = 1; //用户id
    optional int32 score=2;//分数
    optional int32 roundScore=3;//分数
    optional int32 talkSkinId=4;//气泡皮肤
    optional int32 segId=5;//段位
    optional int32 starNum=6;//传奇之星
    optional int64 prettyId=7;//显示id
    optional int32 isInChatOff=8;//1-屏蔽  0-不屏蔽
    optional int32 roylevel=9; //玩家皇室等级, 1.2.7 add
    optional bool royVisibility=10; //玩家皇室等级可见性, 1.2.7 add, false-皇室等级对所有用户可见，true-仅自己可见~~
    optional int32 giftId = 11; // 别人赠送的互动礼物ID
    optional int32 cardSkinId = 12; // domino牌的皮肤id
    optional int32 sceneId = 13; // domino桌布的皮肤id
    optional int32 realRoyLevel = 14; // 真实的玩家皇室等级
    optional bool royalLevelNameAnimation = 15; // rl扫光 1.4.0添加
}
//显示用信息
message FPlayerShowInfo {
    required int32 idx = 1; //用户id
    required int32 faceId = 2; //特效id
    required string nikeName = 3; //昵称
    required string faceUrl = 4; //头像地址
    required int32 placeId = 5; //所在位置    0大厅 其他对应的gameid -1 离线
    required int32 ftype = 6; //好友类型    0
    required int32 country = 7; //国家
    required int32 level = 8; //等级
    required int32 viplevel = 9; //vip等级
    required int32 winCount = 10; //获胜次数
    required int32 totalCount = 11; //总次数
}

//邀请好友请求
message FriendInviteRequest {
    required Command command = 1 [default = FRIEND_INVITE];
    required FPlayerShowInfo from = 2; //发起人
    required FPlayerShowInfo to = 3; //受邀人
    required int64 roomid = 4; //房间id
}
//举报
message PlayerReportRequest {
    required Command command = 1 [default = FRIEND_INVITE];
    required int32 fromId = 2; //发起人
    required int32 toId = 3; //举报人
    required int64 roomid = 4; //房间id
    optional string msg = 5; //消息
}
//玩家升级
message MassagePlayerLevelUPResponse {
    required Command command = 1 [default = UPDATA_PLAYER_LEVEL];
    required int32 level = 2; //新等级
    required int32 exp = 3; //新经验
}
//聊天
message GameRoomPlayerChatToAllRequestAndResponse {
    required Command command = 1 [default = PLAYER_CHAT_ALL];
    required int32 idx = 2; //用户id
    required int64 roomid = 3; //房间号
    required string msg = 4; //消息
    optional string extension = 5;//扩展字段
}

//更新余额
message UpdataCoinRequest {
    required Command command = 1 [default = UPDATA_PLAYER_COIN];
    required int32 type = 2; //0金币 1钻石
    required int64 value = 3; //余额
}

//更新余额
message UpdataCoinResponse {
    required Command command = 1 [default = UPDATA_PLAYER_COIN];
    required int32 type = 2; //0金币 1钻石
    required int64 value = 3; //余额
}
//获取语音频道token
message GetAgoraTokenRequest {
    required Command command = 1 [default = GET_AGORA_TOKEN];
    required int32 idx = 2; //用户id
    required int64 roomid = 3; //房间号
}
//获取语音频道token回复
message GetAgoraTokenResponse {
    required Command command = 1 [default = GET_AGORA_TOKEN];
    required string token = 2; //token
    required string cName = 3; //频道名
}

// 获取 即构 token, 暂未用
message GetZegoTokenRequest{
	required Command command=1[default = GET_ZEGO_TOKEN];
	required int32 idx=2;//用户id
	required int64 roomid=3;//房间号
    required int32 version=4;//版本号
}

message GetZegoTokenResponse{
	required Command command=1[default = GET_ZEGO_TOKEN];
	optional string token=2;//token
	optional string cName=3;//频道名
}



enum AgoraOperate {
    START_SPEAK = 0; //开始说话
    STOP_SPEAK = 1; //停止说话
}

//语音频道操作
message AgoraOperateRequestAndResponse {
    required Command command = 1 [default = AGORA_OPERATE];
    required int32 idx = 2; //用户id
    required int64 roomid = 3; //房间号
    required AgoraOperate operate = 4; //
}

//添加好友请求
message AddFriendMsgRequest {
    required Command command = 1 [default = FRIEND_ADD_MSG];
    required int32 fromidx = 2; //用户id
    required int32 toidx = 3; //用户id
}
//获取好友列表
message FriendListRequest {
    required Command command = 1 [default = FRIEND_LIST];
}
//返回好友列表
message FriendListResponse {
    required Command command = 1 [default = FRIEND_LIST];
    repeated FPlayerShowInfo playerList = 2;
}

//加入房间
message JoinRoomRequest {
    required Command command = 1 [default = JOIN_ROOM];
    required int64 roomid = 2; //房间号

}

//加入房间
message JoinRoomResponse {
    required Command command = 1 [default = JOIN_ROOM];
    required int64 roomid = 2; //房间号
    required OperateResult result = 3 [default = SUCCESS];
    required ErrorCode code = 4;
}

//快速进入房间请求
message QuickEnterRoomRequest {
    required Command command = 1 [default = QUICK_START];
    required int32 type = 2 [default = 0]; //1私密 0公开PlayerTurnStatus
}

//快速进入房间请求
message QuitRoomRequest {
    required Command command = 1 [default = QUIT_ROOM];
    required int64 roomid = 2; //房间号
}

//快速进入房间请求
message QuitRoomResponse {
    required Command command = 1 [default = QUIT_ROOM];
    required int64 roomid = 2; //房间号
    required OperateResult result = 3 [default = SUCCESS];
    required int32 idx = 4;
    optional ErrorCode code = 5;
}

//快速进入房间回复
message QuickEnterRoomResponse {
    required Command command = 1 [default = QUICK_START];
    required OperateResult result = 2 [default = SUCCESS];
    required int64 roomid = 3; //房间号
    required int32 cost = 4; //花费
    required int32 sceneId = 5; //场景id
    optional RoomInfoPlayerInfo player = 6; //所有房间用户
    optional ErrorCode code = 7;
    repeated Sheep sheep = 8; //可出羊群
    optional int32 gameType = 9; //多米诺房间类型  1积分场  2轮次场
    optional int64 showId = 10; //房间号
    optional int32 isPrivate=11;//是否私有房间
    optional GameStatus status=12;//游戏状态
    optional int32 roundNum=13;
    optional int32 msgindex=14;//当前消息号
    optional int32 playerRoundTime=15;//玩家轮次时长毫秒
    optional int32 gameRoundTime=16;//游戏轮次间隔时长毫秒
    optional int32 nextDiamond=17;//延时所需钻石
    repeated int32 addTimeCost=18;//每次延时所需花费
    optional int32 quitPunishTime=19; // 强退惩罚的1个触发时间阀值, 若在该值以内强退，则给客户端惩罚提示
    optional int32 gameBeginTime=20; // 游戏开始时间戳
    optional int32 autoAddExp = 21; // 托管自动加经验 托管加经验 0 可以加  1 不可以加
    optional string activityPropUrl=22;//活动道具的url
}

//房间用户列表
message RoomInfoPlayerInfo {
    repeated PlayerShowInfo player = 1;
}

//游戏操作
message GameOperateRequest {
    required Command command = 1 [default = GAME_OPERATE];
    required GameType type = 2; //游戏类型
    required int64 roomid = 3; //房间号
    required GameOperate op = 4; //操作
    optional int32 opid = 5;
    optional int32 dominoId = 6;
    optional int32 sideDominoId = 7;
    optional int32 trackId = 8; //赛道ID
    optional int32 deskSort = 9; //桌牌方向  1上 2下 3左 4右
}
//游戏操作回复
message GameOperateResponse {
    required Command command = 1 [default = GAME_OPERATE];
    required GameType type = 2; //游戏类型
    required int64 roomid = 3; //房间号
    required GameOperate op = 4; //操作
    optional int32 opid = 5;
    required OperateResult result = 6 [default = SUCCESS];
    optional ErrorCode code = 7;
    repeated Sheep sheep = 8; //可出羊群
    optional TrackResponse track = 9; //赛道信息
    optional SheepGameGrade sheepGameGrade = 10; //羊得分信息
    optional int32 msgindex=11;//当前消息号
    optional int64 leftDiamond=12;//剩余钻石
    optional int32 nextDiamond=13;//延时所需钻石
}
//游戏操作回复
message GameEventResponse {
    required Command command = 1 [default = GAME_EVENT];
    required GameType type = 2; //游戏类型
    required int64 roomid = 3; //房间号
    required GameEvent event = 4; //游戏事件
    repeated GameColorSelect color = 5; //颜色分配
    optional GameStatus status = 6; //游戏状态
    optional PlayerTurnStatus ptStatus = 7; //玩家状态
    optional int32 idx = 8; //用户idx
    optional int32 num = 9; //数量, 当cmd=CHAT_OFF 时，表示是否屏蔽了聊天，此时同 isInChatOff 
    optional DominoRoundInfo roundResut = 10; //多米诺单局游戏结果
    optional DominoGameResult gResut = 11; //多米诺整局游戏结果
    repeated PlayerAndDominoes playDominoed = 12; //多米诺玩家手牌
    repeated Domino remainDominoes = 13; //多米诺补牌区手牌
    optional int32 roundNum = 14; //多米诺当前局数
    optional Domino autoPlayDomino = 15; //多米诺自动出的牌
    optional int32 deskSort = 16; //多米诺自动出牌桌面的首尾 1：首  2：尾
    repeated Domino autoFillDomino = 17; //多米诺自动补的牌
    optional int32 connectScore = 18; //连接得分
    optional int32   roomType = 19; //房间类型
    repeated Domino deskDominoes = 20; //多米诺桌面牌
    optional int32 msgindex=21;//当前消息号
    optional int32 roundLeftTime=22;//当前轮次剩余时间
    optional int32 roundTime=23;//当前轮次时间
}

message PlayerAndDominoes {
    required int64 playerId = 1;
    repeated Domino dominoes = 2;
}

//
message SideObject {
    optional int32 leftDominoId = 1; //左侧多米诺ID
    optional int32 rightDominoId = 2; //右侧多米诺ID
    optional int32 topDomainoId = 3; //上侧多米诺ID
    optional int32 bottomDominoId = 4; //下侧多米诺ID
}

message PlayerDomino {
    required int32 playerId = 1;
    required string playerNickName = 2;
    optional int32 playerIntergral = 3;
    optional int32 playerRoundIntergral = 4;
    repeated Domino dominoes = 5;
    optional int32 exp=6;//经验
}
//多米诺
message Domino {
    required int32 id = 1;
    required int32 headNum = 2; //首数
    required int32 tailNum = 3; //尾数
    optional int32 leftDominoId = 4; //左侧多米诺ID
    optional int32 rightDominoId = 5; //右侧多米诺ID
    optional int32 side = 6; //多米诺方向  0 垂直方向  1 水平方向
    optional int32 topDomainoId = 7; //上侧多米诺ID
    optional int32 bottomDominoId = 8; //下侧多米诺ID
    optional int32 deskSort=9;
}

//多米诺单局结果
message DominoRoundInfo {
    required int32 winnerId = 1; //优胜玩家ID
    required string winnerNickName = 2; //优胜玩家昵称
    required int32 winnerIntergral = 3; //优胜玩家积分
    required int32 roundIntergral = 4; //本局积分
    required int32 roundNum = 5; //局数
    repeated Domino winnerDomino = 6; //优胜玩家剩余多米诺a
    repeated PlayerDomino losersInfo = 7; //失败玩家剩余多米诺
    optional int32 winnerRoundIntergral = 8; //优胜玩家本局得分
    optional int32 winnerExp=9;//本局经验
}
//多米诺整局结果
message DominoGameResult {
    required int32 winnerId = 1; //优胜玩家ID
    required string winnerNickName = 2; //优胜玩家昵称
    optional int32 winnerIntergral = 3; //优胜玩家积分
    optional int32 winnerRoundIntergral = 4; //优胜
    optional int32 roundIntergral = 5; //本局积分
    required int32 roundNum = 6; //局数
    repeated Domino winnerDomino = 7; //优胜玩家剩余多米诺
    repeated PlayerDomino losersInfo = 8; //失败玩家剩余多米诺
    required int32 winGoldNum=9;
    optional int32 winnerExp=10;//本局经验
    optional int32 winnerPropNum=11;//优胜玩家道具掉落数量
}
message GameColorSelect {
    required int32 idx = 1; //用户id
    required PlayerColor color = 2; //用户颜色
}
message Sheep {
    required int32 id = 1; //羊ID
    required int32 playerId = 2; //用户ID
    required int32 weight = 3; //体重
    required int32 damageValue = 4; //伤害值
    required int64 roomId = 5; //房间id
    required int32 trackId = 6; //赛道id
    required double currentPosition = 7; //当前位置
    required bool isOverlap = 8; //是否重叠
}
//顶羊赛道信息
message TrackResponse {
    //required Command command = 1 [default = GAME_OPERATE];
    //required GameType type = 2; //游戏类型
    //required int64 roomid = 3; //房间号
    //required GameOperate op = 4; //操作
    repeated Sheep sheep = 5; //羊群
}
//顶羊用户得分通知
message SheepGameGrade {
    //required Command command = 1 [default = GAME_OPERATE];
    //required GameType type = 2; //游戏类型
    //required int64 roomid = 3; //房间号
    //required GameOperate op = 4; //操作
    required int32 playerId = 5; //用户ID
    required int32 grade = 6; //得分情况
}

// 获取游戏内互动礼物配置
message GameGiftCnfListRequest {
    required Command command = 1 [default = GAME_GIFT_CNF_LIST];
}

// 游戏内互动礼物配置响应
message GameGiftCnfListRespone {
    required Command command = 1 [default = GAME_GIFT_CNF_LIST];
    repeated GameGiftCnf gameGiftCnf = 2; // 互动礼物列表
    optional int32 limitCount = 3; // 冷却触发阀值 N
    optional int32 coolDownTime = 4; // 冷却触发阀值 M
}

// 发送互动礼物
message GameGiftSendRequest {
    required Command command = 1 [default = GAME_GIFT_SEND];
    optional int32 giftId = 2; // 本次赠送的互动礼物ID: GameGiftCnf->id
    repeated int32 rcvIdx = 3; // 本次接收互动礼物的玩家ID
}

message GameGiftSendResponse {
    required OperateResult result = 1 [default = SUCCESS];
    optional ErrorCode code = 2;
    optional int32 giftId = 3; // 本次赠送的礼物ID: GameGiftCnf->id
    optional int32 senderIdx = 4; // 发送者玩家ID
    optional int64 senderMoney = 5 [default = -1]; // 发送者当前金币, 非-1表示有变动
    optional int64 senderDiamond = 6 [default = -1]; // 发送者当前钻石, 非-1表示有变动
    repeated int32 rcvIdx = 7; // 本次接收互动礼物的玩家ID
}

// 游戏互动礼物配置
message GameGiftCnf {
  optional int32 id = 1; // 唯一标识，赠送的时候上传该 id
  optional string icon = 2; // 互动礼物图标地址
  optional int32 money = 3; // 金币价格
  optional int32 diamond = 4; // 钻石价格
  optional int32 sort = 5; // 排序权重
  optional GameGiftType gameGiftType = 6; // 互动礼物类型
}

// 游戏内互动礼物类型
enum GameGiftType {
  EMOJ = 1; // 表情
}

// 语音房类别
enum VoiceType{
  Agora=1;//声网
  Zego=2;//即构
}

