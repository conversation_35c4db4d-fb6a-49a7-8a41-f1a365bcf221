syntax = "proto3";

import "google/protobuf/empty.proto";

option java_multiple_files = true;
option java_package = "io.grpc.jackroo.game";

//Tyr项目中的指令编码,从200开始
service JackarooGameService {

  //登录游戏,TICKET_LOGIN
  rpc loginGame (stream JackarooGameStreamReq) returns (stream JackarooGameStreamRsp) {}

  //选牌,配合服务端实现托管需求,SELECT_POKER,不给客户端回消息,客户端只要请求了就可以
  rpc selectPoker (SelectPokerReq) returns (google.protobuf.Empty);

  //出牌,PLAY_POKER
  rpc playPoker (PlayPokerReq) returns (google.protobuf.Empty);

  //延长思考时间,EXTEND_TIME
  rpc extendTime (ExtendTimeReq) returns (google.protobuf.Empty);

  //弃牌,DISCARD_POKER
  rpc discardPoker (DiscardPokerReq) returns (google.protobuf.Empty);

  //指定下一个玩家弃牌,ASSIGN_NEXT_PLAYER_DISCARD,支持出10时让其它玩家弃牌
  rpc assignNextPlayerDiscard (AssignNextPlayerDiscardReq) returns (google.protobuf.Empty);

  //主动退出游戏,QUIT_GAME
  rpc quitGame (QuitGameReq) returns (google.protobuf.Empty);

  //刷金币,FLUSH_CURRENCY
  rpc flushCurrency (FlushCurrencyReq) returns (google.protobuf.Empty);

  //同步游戏数据,暂定,SYNC_GAME_DATA
  rpc syncGameData (SyncGameDataReq) returns (google.protobuf.Empty);

  //取消托管,CANCEL_TRUST
  rpc cancelTrust (CancelTrustReq) returns (google.protobuf.Empty);

  //语音Token
  rpc voiceToken (VoiceTokenReq) returns (VoiceTokenRsp) {};

  //游戏礼物配置
  rpc gameGiftConfig (GameGiftConfigReq) returns (GameGiftConfigRsp) {};

  //游戏礼物赠送
  rpc gameGiftSend (GameGiftSendReq) returns (google.protobuf.Empty) {};

  //游戏聊天
  rpc gameChat (GameChatReq) returns (google.protobuf.Empty) {};

  //抽取玩家扑克牌
  rpc drawNextPlayerPoker (DrawNextPlayerPokerReq) returns (google.protobuf.Empty);

  //设置聊天开关
  rpc setOpenChat (SetChatReq) returns (google.protobuf.Empty);

  //测试时换牌,只在开发和测试环境有效
  rpc testExchangePoker (TestExchangePokerReq) returns (google.protobuf.Empty);

  //选子接口
  rpc selectChess (SelectChessReq) returns (google.protobuf.Empty);

  //增加选项接口
  rpc selectItem (SelectItemReq) returns (google.protobuf.Empty);

  //增加同步时间接口
  rpc syncTime(SyncTimeReq) returns (google.protobuf.Empty);
}

// 与客户端通信去掉事件概念
// 命令分为:
// 1 请求命令,2 服务端推送给客户端的命令
//命令右侧：双向流表示请求是通过登录接口建立的双向流进行响应的; grpc:请求是通过grpc响应的;增加注释便于客户端解析
enum Command {
  NONE_COMMAND = 0; //占位符
  TICKET_LOGIN = 1; //登录,Tyr指令:200,双向流
  SELECT_POKER = 3; //选牌 Tyr指令:201,双向流
  PLAY_POKER = 4; //出牌 Tyr指令:202,双向流
  EXTEND_TIME = 7; //延长时间 Tyr指令:205,双向流
  DISCARD_POKER = 8; //弃牌 Tyr指令:206,双向流
  QUIT_GAME = 9; //主动退出游戏  Tyr指令:207,双向流
  FLUSH_CURRENCY = 10; //刷新金币  Tyr指令:208,双向流
  SYNC_GAME_DATA = 11; //同步游戏数据,暂定 Tyr指令:209,双向流
  CANCEL_TRUST = 12; //取消托管 Tyr指令:210,双向流
  VOICE_TOKEN = 13; //语音token Tyr指令:211,grpc
  GAME_GIFT_CONFIG = 14; //游戏礼物配置 Tyr指令:212,grpc
  GAME_GIFT_SEND = 15; //赠送游戏礼物 Tyr指令:213,双向流
  GAME_CHAT = 16; //游戏聊天 Tyr指令:214,双向流
  ASSIGN_NEXT_PLAYER_DISCARD = 17; //指定下一个玩家弃牌,支持出10时让其玩家其牌,Tyr指令:215,双向流
  DRAW_NEXT_PLAYER_POKER = 18; //抽取下一个玩家的牌,Tyr指令:216,双向流
  SET_CHAT = 19; //设置聊天,Tyr指令217,双向流
  TEST_EXCHANGE_POKER = 20; //支持测试时换牌,Tyr指令218,双向流
  SELECT_CHESS = 21; //选子,Tyr指令219,双向流
  SELECT_ITEM = 22; //选项,Tyr指令220,双向流
  SYNC_TIME = 23; //同步服务端时间,Tyr指令221,双向流


  //从100开始,服务端推给客户端的命令
  GAME_PLAYER_LOGIN = 100; //游戏玩家登录游戏,给场上其它人推送的消息
  DEAL_POKER = 101; //没有手牌时给玩家发牌的消息
  GAME_START = 102; //四个玩家都登录或等待超时后,开始游戏给玩家推送的命令,推送的消息为RestoreGameRsp
  CHANGE_PLAYER = 103; //切换玩家的消息
  SETTLEMENT = 104; //游戏结束的结算消息
  KICK_PLAYER = 105; //踢玩家
  TRUST_PUB = 106; //托管推送,触发托管时给当前玩家发送推送,

  MOVE_CHESS_PUB = 107; //走子后的推送消息
  EXCHANGE_CHESS_PUB = 108; //交换棋子后的推送消息
  EXTEND_TIME_PUB = 109; //延长思考时间后的推送消息
  QUIT_GAME_PUB = 110; //有玩家主动退出时的推送消息

  GAME_GIFT_PUB = 111; //游戏礼物赠推送消息
  GAME_CHAT_PUB = 112; //游戏聊天推送消息
  FINISH_DISCARD_PUB = 113; //完成弃牌后的推送
  FORCED_DISCARD_PUB = 114; //指定下一玩家弃牌后的推送
  POKER_SEVEN_MOVE_PUB = 115; //出牌7的移动推送
  IMPACT_MOVE_PUB = 116; //撞击移动后的推送
  DRAW_POKER_PUB = 117; //抽取下家牌后的推送,
  END_EXCEPTION_GAME_PUB = 118; //游戏异常结束,客户端收到此消息,则弹框提示,玩家点击确认后关闭游戏
  SET_CHAT_PUB = 119; //别人设置聊天开关的推送
  TEST_EXCHANGE_POKER_PUB = 120; //换牌成功后,给被换牌的玩家推送消息,

}

// 错误码定义
enum ErrorCode {
  UNDEFINED_ERROR_CODE = 0; // 未定义,占位符
  NONE_ERROR = 1; //无错误
  SYSTEM_ERROR = 2; //系统错误
  PARAM_ERROR = 3;//参数错误

  LOGIN_FAILD_PW = 101;//密码错误
  LOGIN_FAILD_ID = 102;//登陆ID错误
  LOGIN_FAILD_OLDSIGN = 103;//重复的签名
  LOGIN_FAILD_SIGN = 104;//签名错误
  LOGIN_FAILD_TIME = 105;//客户端时间错误
  QUICKSTART_FAILD_NOROOM = 203;//房间已满
  ENTER_ROOM_FAILD_NOROOM = 204;//房间已满
  ENTER_ROOM_FAILD_ROOMID = 205;//错误的房间号
  GAMEOPERATE_FAILD_SIT_NOSTOOL = 301;// 已有人
  GAMEOPERATE_FAILD_SIT_WRONGSTOOLNUM = 302;//错误的凳子ID
  GAMEOPERATE_FAILD_SIT_WRONGTIME = 303;//错误的操作时间

  NOT_YOUR_TURN = 304; //不是你的回合
  OPERATE_FAIL = 305; //操作失败
  PLAYER_NOT_ALLOWED_DISCARD = 306; //此不允许要求此玩家弃牌
  TRUST_PUNISHMENT_KICKED_OUT = 307; //玩家因为托管惩罚被踢出游戏


  NO_MONEY = 401;//余额不足
  NO_DIAMOND = 402;//钻石不足
  ACCOUNT_BE_FROZEN = 403;//1.2.2 add 账号被冻结
  MAX_LIMIT = 601;//超出限制
  JOIN_ROOM_FAILD_NOROOM = 701;//无此房间
  JOIN_ROOM_FAILD_MAX = 702;//房间人数已满
}

//请求 玩家id可以通过上下文获取,此处不用定义
message JackarooGameStreamReq {
  Command command = 1; //命令id
  int64 roomId = 2; //房间id,
  bytes data = 3; //
}

message JackarooGameStreamRsp {
  Command command = 1; //命令id
  int64 roomId = 2;
  bytes data = 3;
}

//颜色,约定红色为庄家所在位置,红色区域逆时针依次为:蓝,黄、绿、
//约定红黄一队,绿蓝一队,根据玩家的颜色即可判断是否是队友
enum JackarooColor {
  RED = 0;
  BLUE = 1;
  YELLOW = 2;
  GREEN = 3;
}

//登录请求 命令TICKET_LOGIN
message TicketLoginReq {
  int32 idx = 1; //用户id
  string token = 2; //用户的token
  string version = 3; //玩家主包的版本号,格式,例如: 1.4.3.0
  bool reconnection = 4; //是否是断线重连
  int32 pokerConfigId = 5; //手牌配置id,用于调试,0:不用配置的手牌
  int32 chessPanelConfigId = 6; //棋盘配置id,用于调试,0:不用配置的棋盘
  int32 gameType = 7; //需要的模式,只在开发环境有效
  int32 voiceVersion = 8; // 语音token版本
}

//登录回复
message TicketLoginRsp {
  OperateResult result = 1;//是否成功
  ErrorCode code = 2; //错误码
  PlayerSelfData playerSelfData = 3; //玩家自己的信息
  PlayerSelfGameData selfGameData = 4; //仅属于玩家自己的游戏数据
  RestoreGameRsp restoreGame = 5; //恢复游戏的数据,根据玩家的请求参数确定是否有值
  int32 endpointNewCode = 6; //0棋子到顶点加经验，其他值棋子进终点赛道加经验
  bool allowAcross2OtherChess = 7; //是否能穿越2个别人的棋子,false:不可以,true:可以
}


//恢复房间所用数据
message RestoreGameRsp {
  JackarooGameData gameData = 1;
  //所有玩家展示信息,key:玩家id,value:玩家消息
  map<int32, PlayerShowInfo> playShowInfo = 2;
  //出牌7时当前玩家第1次选择的棋子序号
  int32 sevenPokerFirstOrder = 3;
  //出牌7时,当前玩家第1次选择的数值,如果大于0,表示玩家正在选择第2个棋子
  int32 sevenPokerFirstNumber = 4;
  map<int32, PokerMoveData> allowPokerData = 5; //轮到此玩家时,此字段有值,当前玩家可出的牌及相关数据,key:扑克牌id,value为相关数据
  map<int32, int32> expConfig = 6; //经验配置,key为经验类型对应的值,value为经验值
  map<int32, PlayRule> playRuleMap = 7; //key:牌的枚举数值,value:牌的玩法规则,如果map为空或者不在这个map中,则使用基础玩法
  TrustConfigInfo trustConfig = 8; //托管配置
  int32 extendTime = 9; //花费钻石后每次延长的时间，单位秒;
  repeated DiscardedPoker discardPokers = 10; //玩家弃的牌,最后一张是最新出的牌,
  //出牌7时当前玩家第1次选择的棋子的玩家id（当前玩家可能在走队友棋子）
  int32 sevenPokerFirstChessUser = 11;
  int32 totalThinkingTime = 12; //总的思考时间,一局游戏初始化后不会改变,如果改变也是从下一局开始,单位秒
  Poker drawRewardPoker = 13; // 抽的奖励牌
}

//弃的牌
message DiscardedPoker {
  Poker poker = 1; //弃的牌
  bool valid = 2; //是否是有效牌
}

message TrustConfigInfo {
  bool punishTrust = 1; //是否开启托管惩罚
  bool ignoreTrustExperience = 2; //是否开启托管不加经验,true:不加经验
  int32 allowConsecutiveTrustMax = 3; //允许连续托管的最大次数
}

//经验类型定义,
enum ExpType {
  NONE_EXP_TYPE = 0; //占位符
  ENTER_END_AREA = 1; //进入终点区域
  KILL_CHESS = 2; //击杀棋子
  FLY_CHESS = 5; //起飞棋子
}


//游戏数据
message JackarooGameData {
  map<int32, PlayerJackarooGameData> playerDataMap = 1; //玩家的游戏数据,key:玩家id,value:玩家的数据
  GameAgainstType againstType = 2; //对战模式
  TeamInfo teamInfo = 3; //组队信息
  ChessPanel chessPanel = 4; //棋盘信息
  int32 bets = 5; //本局筹码数量
  GameGroup gameGroup = 6; //模式
  GameType gameType = 7; //游戏类型
}

//可以展示给其它玩家的信息
message PlayerShowInfo {
  PlayerShowData playerShowData = 1; //玩家展示信息
  PlayerShowGameData showGameData = 2;
}

//当前玩家选牌,命令SELECT_POKER
message SelectPokerReq {
  int64 roomId = 1;
  int32 idx = 2; //玩家id
  Poker poker = 3; //所选的牌
  bool flyToChessPanel = 4; //是否已飞到棋盘上
}

//当前玩家选子,命令为SELECT_CHESS
message SelectChessReq {
  int64 roomId = 1;
  int32 idx = 2;
  Chess chess = 3; //玩家所选的子
}

//记录当前玩家出牌选项,
//命令为SELECT_ITEM
message SelectItemReq {
  int64 roomId = 1;
  int32 idx = 2;
  Poker poker = 3; //当前选的牌
  SelectItemType itemType = 4; //当前选项

}

//如果是起飞选项,让下家弃牌选项,都不用记录,因为玩家点击选就请求相关接口了
enum SelectItemType {
  NONE_ITEM_TYPE = 0; //占位符,无选项
  MOVE_ONE = 1; //出牌A,选择走1步
  MOVE_TEN = 2; //出牌10,选择走10步
  MOVE_ELEVEN = 3; //出牌A,选择走11步
  MOVE_THIRTEEN = 4; //复杂模式,出牌K,选择走13步
  MOVE_TWO = 5;//复杂模式1vs1,出牌2,选择走2步
}

//出牌,命令PLAY_POKER
message PlayPokerReq {
  int64 roomId = 1;
  int32 idx = 2; //玩家id
  Poker poker = 3; //正在出的牌
  oneof moveData {
    MoveChessData commonMove = 4;//普通移动棋子时,传的相关信息,没有棋子移动时order传0
    ExchangeChessData exchange = 5;//交换棋子时传这个数据
    PokerSevenMoveData pokerServen = 6; //出牌为七时
  }
}

//出牌的结果
message PlayPokerRsp {
  OperateResult result = 1;//是否成功
  ErrorCode code = 2; //错误码
  Poker playingPoker = 3; //正在出的牌
  //快速玩法出牌7时,quickFinish,toBornData这两个字段才可能有值
  bool quickFinish = 4; //是否完成快速模式(已经有N个棋子进终点了,目前N=1)
  repeated MoveToBornData toBornData = 5; //移动到出生点的数据
}

//出牌的通用结果数据
//当出的牌为A,K,Q,2,3,4,5,6,7,8,9,10时返回的结果类型
//这个对象中都是操作前的数据,
//例如:红色棋子1要从1位置,走到4位置,4位置没有棋子,则格子1中的棋子是红色棋子1,格子4中无棋子
//当出的牌为7时,为了支持预览,key:可移动的最大步数,value:棋子移动数据列表
message PlayCommonResultData {
  //Key:可走的步数,当步数为0时表示起飞,-4:后退4步
  //客户端根据这个map中的数据来判断是否需要选步数或选子
  //因为客户端知道出的什么牌,客户端需要根据不同的牌展示不同的界面
  map<int32, PlayResultData> playResultData = 1; //不同步数可以移动的棋子
}

//出Jack的结果,
//这个对象中都是操作前的数据
message PlayJackResultData {

  //Key:可以被交换的玩家id,value:被交换的棋子数据
  map<int32, PlayResultData> playResultData = 2;

}



//出牌结果数据
message PlayResultData {
  repeated PlayResultItemData result = 1;
}

//出牌后,可以移动的棋子的数据
message PlayResultItemData {
  GridData fromGrid = 1; //要移动的棋子所在的格子的数据,自己的棋子所在的格子
  GridData toGrid = 2; //到达的格子所在的格子数据
  bool kill = 3; //是否发生吃子
  //针对出牌7的特殊处理,只有出牌7时可能用到notAllowMoveNumber,
  //说明,大于可走的最大步数的数值不在这个集合。例如,最多可以走3步,则4，5，6，7这个几个数值不在这个集合中。
  repeated int32 notAllowMoveNumber = 4; //不能走的移动步数
  bool quickFinish = 5; //是否完成快速模式(已经有N个棋子进终点了,目前N=1)
  repeated MoveToBornData toBornData = 6; //移动到出生点的数据
  GridData transferGrid = 7; // 转移格子
  bool previewRewardPoker = 8; // 是否预览奖励牌
  repeated int32 drawPokerMoveNumber = 9; // 针对出牌7有效,触发抽牌的移动步数

  //针对出牌7的特殊场景,走子组合是否在drawPokerCombination中,如果在,在说明可以触发抽牌。
  //ChessOrder是一个序号列表,如果另一个棋子的序号在这个序号列表中,则当前棋子走key步可以抽牌
  map<int32, ChessOrder> drawPokerCombination = 10; //触发抽牌的移动组合。key:当前棋子走的步数,value:另一个棋子的序号。
}

message ChessOrder{
  repeated int32 order = 1; //棋子序号列表
}

//延长思考时间,EXTEND_TIME
message ExtendTimeReq {
  int64 roomId = 1;
  int32 idx = 2;

}

message ExtendTimeRsp {
  OperateResult result = 1;//是否成功
  ErrorCode code = 2; //错误码
  int32 extendTimeCount = 3; //已经延时的次数
  int32 extendTimeCost = 4; //下次延时需要的花费,钻石数
  int32 extendRemainCount = 5; //剩余延时次数
  int64 thinkingEndTime = 6; //延迟时间后,思考结束时间
}

//弃牌,DISCARD_POKER
message DiscardPokerReq {
  int64 roomId = 1;
  int32 idx = 2; //弃牌的玩家id
  repeated Poker discardPoker = 3; //要丢弃的牌
  bool discardAll = 4; //是否要丢弃所有牌,此字段为true,上面的discardPoker字段可以不设置
}

//弃牌结果
message DiscardPokerRsp {
  OperateResult result = 1;//是否成功
  ErrorCode code = 2; //错误码
  Poker poker = 3; //当前出牌区中的牌
}

//主动退出游戏,QUIT_GAME
message QuitGameReq {
  int64 roomId = 1;
  int32 idx = 2; //主动退出游戏的玩家id
}

message QuitGameRsp {
  OperateResult result = 1;//是否成功
  ErrorCode code = 2; //错误码
}

//刷新玩家金币,FLUSH_CURRENCY
message FlushCurrencyReq {
  int32 idx = 1;
  int64 roomId = 2;
}

//刷新玩家金币结果
message FlushCurrencyRsp {
  OperateResult result = 1;//是否成功
  ErrorCode code = 2; //错误码
  int64 gold = 3;//金币
  int64 diamond = 4;//钻石
  int32 exp = 5;
  int32 level = 6;
  int32 vipExp = 7;
  int32 vipLevel = 8;
}

//同步游戏数据 SYNC_GAME_DATA
message SyncGameDataReq {
  int32 idx = 1;
  int64 roomId = 2;
}

//同步游戏数据的返回值
message SyncGameDataRsp {
  OperateResult result = 1;//是否成功
  ErrorCode code = 2; //错误码
  JackarooGameData gameData = 3;
  repeated DiscardedPoker discardPokers = 4; //玩家弃的牌,最后一张是最新出的牌,
  map<int32, PokerMoveData> allowPokerData = 5; //轮到此玩家时,此字段有值,当前玩家可出的牌及相关数据,key:扑克牌id,value为相关数据
}

//取消托管,CANCEL_TRUST
message CancelTrustReq {
  int32 idx = 1;
  int64 roomId = 2;

}

//取消托管
message CancelTrustRsp {
  OperateResult result = 1;//是否成功
  ErrorCode code = 2; //错误码
}

//托管时给玩家的推送 SET_CHAT_PUB
message SetChatPub {
  int32 idx = 1; //设置聊天开关的玩家
  bool open = 2; //是否开启屏蔽聊天,true:开启,false:关闭
}

//给被换牌的玩家推送消息 TEST_EXCHANGE_POKER_PUB
message TestExchangePokerPub {
  int32 idx = 1; //发起换牌的玩家
  int32 exchangedPokerIdx = 2; //被换牌的玩家id
  Poker sourcePoker = 3; //发起换牌的玩家给被换牌的玩家的牌,
  Poker targetPoker = 4; //发起换牌的玩家的目标牌, 被换牌的玩家需要把手牌中的targetPoker替换为sourcePoker
}

//指定下一个玩家弃牌,ASSIGN_NEXT_PLAYER_DISCARD
message AssignNextPlayerDiscardReq {
  int32 idx = 1; //当前玩家id
  int64 roomId = 3;
  Poker poker = 4; //正在出的牌
}

//指定下一个玩家弃牌弃牌返回值
message AssignNextPlayerDiscardRsp {
  OperateResult result = 1; //是否成功
  ErrorCode code = 2; //错误码
}

//抽取下家一张手牌,DRAW_NEXT_PLAYER_POKER
message DrawNextPlayerPokerReq {
  int32 idx = 1; //当前玩家id
  int64 roomId = 2; //
  int32 drawnPokerIndex = 3; //被抽取的牌所在位置,从0开始,屏幕左边的第1张牌,位置为0,从左到右依次加1。
  Poker poker = 4; //正在出的牌
}

//抽取结果
//接着轮到被抽牌的玩家时,此玩家直接弃掉被抽取的牌,客户端发送DISCARD_POKER命令
message DrawNextPlayerPokerRsp {
  OperateResult result = 1; //是否成功
  ErrorCode code = 2; //错误码
}

//设置玩家聊天的开关,SET_CHAT
message SetChatReq {
  int32 idx = 1; //当前玩家id
  int64 roomId = 2; //
  bool open = 3; //是否开启屏蔽聊天,ture:开启,false:关闭
}

//设置玩家聊天开关的结果
message SetChatRsp {
  OperateResult result = 1; //是否成功
  ErrorCode code = 2; //错误码
}

//测试时换牌 TEST_EXCHANGE_POKER
message TestExchangePokerReq {
  int32 idx = 1; //玩家id
  int64 roomId = 2; //房间id
  Poker sourcePoker = 3; //原来的牌
  Poker targetPoker = 5; //目标牌
}

message TestExchangePokerRsp {
  OperateResult result = 1; //是否成功
  ErrorCode code = 2; //错误码
}

//同步时间
message SyncTimeReq{
  int32 idx = 1; //当前玩家id
  int64 roomId = 2; //房间id
}

message SyncTimeRsp{
  int64 serverTime = 1; //服务端的时间戳
}

//抽取下一玩家牌后的推送 DRAW_POKER_PUB
message DrawPokerPub {
  int32 drawnPokerIdx = 1; //被抽取牌的玩家id
  Poker currentPoker = 2; //当前牌,红色12
  int32 playPokerPlayerId = 3; //出牌的玩家id,即:出牌红色12的玩家id
  Poker drawnPoker = 4; //被抽取的牌
}

//游戏出现异常时,强制结束游戏,END_EXCEPTION_GAME_PUB
message EndExceptionGamePub {


}


//语音token
message VoiceTokenReq {
  int64 roomId = 1;//房间id
  int32 version = 2;//版本号
  VoiceType type = 3;//语音类型
}

//语音token返回值
message VoiceTokenRsp {
  OperateResult result = 1;//是否成功
  ErrorCode code = 2; //错误码
  VoiceType type = 3;//语音类型
  string token = 4;//token
  string cName = 5;//频道名
}

//获取游戏内互动礼物配置
message GameGiftConfigReq {
  int64 roomId = 1;
}

//游戏内互动礼物配置返回值
message GameGiftConfigRsp {
  OperateResult result = 1;//是否成功
  ErrorCode code = 2; //错误码
  int64 roomId = 3;
  repeated GameGiftConfig gameGiftCnf = 4; // 互动礼物列表
  int32 limitCount = 5; // 冷却触发阀值 N
  int32 coolDownTime = 6; // 冷却触发阀值 M
}

//游戏互动礼物配置
message GameGiftConfig {
  int32 id = 1; // 唯一标识，赠送的时候上传该 id
  string icon = 2; // 互动礼物图标地址
  int32 money = 3; // 金币价格
  int32 diamond = 4; // 钻石价格
  int32 sort = 5; // 排序权重
  GameGiftType gameGiftType = 6; // 互动礼物类型
}

//游戏内互动礼物类型
enum GameGiftType {
  GIFT_NONE = 0;
  GIFT_EMOJI = 1; // emoji表情
}

//游戏礼物赠送
message GameGiftSendReq {
  int32 idx = 1;
  int64 roomId = 2;
  int32 giftId = 3; // 本次赠送的互动礼物ID: GameGiftCnf->id
  repeated int32 rcvIdx = 4; // 本次接收互动礼物的玩家ID
}

//游戏礼物赠送返回值
message GameGiftSendPub {
  OperateResult result = 1; // 是否成功
  ErrorCode code = 2; // 错误码
  int32 giftId = 3; // 本次赠送的礼物ID: GameGiftCnf->id
  int32 senderIdx = 4; // 发送者玩家ID
  int64 senderMoney = 5; // 发送者当前金币, 非-1表示有变动
  int64 senderDiamond = 6; // 发送者当前钻石, 非-1表示有变动
  repeated int32 rcvIdx = 7; // 本次接收互动礼物的玩家ID
}

//聊天消息请求
message GameChatReq {
  int64 roomId = 1; // 房间号
  string msg = 2; // 消息
  GameChatMessageType messageType = 3; // 消息类型
  GameChatChannelType channelType = 4; // 消息类型
}

//聊天消息响应
message GameChatPub {
  OperateResult result = 1; // 是否成功
  ErrorCode code = 2; // 错误码
  int32 idx = 3;
  int64 roomId = 4; // 房间号
  string msg = 5; // 消息
  GameChatMessageType messageType = 6; // 消息类型
  GameChatChannelType channelType = 7; // 消息类型
}

//聊天消息类型
enum GameChatMessageType {
  CHAT_MESSAGE_NONE = 0;
  CHAT_MESSAGE_TEXT = 1; // 文本
  CHAT_MESSAGE_EMOJI = 2; // emoji表情
  CHAT_MESSAGE_PHRASE = 3; // 快捷短语
}

//聊天消息频道类型
enum GameChatChannelType {
  CHAT_CHANNEL_NONE = 0;
  CHAT_CHANNEL_ALL = 1; //所有
  CHAT_CHANNEL_TEAM = 2; //组队
}


//玩家登录游戏后给其它玩家推送的消息
//GAME_PLAYER_LOGIN
message GamePlayerLoginPub {
  PlayerShowInfo playerShowData = 1; //玩家展示信息
}

//游戏中,玩家都没有手牌时给玩家发牌的消息
//DEAL_POKER
message DealPokerPub {
  repeated Poker handPoker = 1; //玩家的手牌
  bool shuffleAnimation = 2; //是否需要播放洗牌动画
  int32 idx = 3;//玩家id
  int32 totalPokerCount = 4; //当前扑克牌总数,
}

//游戏开始,给所有玩家推送的消息
//命令GAME_START
message GameStartPub {
  JackarooGameData gameData = 1; //游戏数据
}

//切换玩家的消息,
// 命令:CHANGE_PLAYER
message ChangePlayerPub {
  ChessPanel chessPanel = 1;
  PlayerStatus currentPlayerStatus = 2; //当前玩家状态
  map<int32, PokerMoveData> allowPokerData = 3; //当前玩家可出的牌及相关数据,key:扑克牌id,value为相关数据
  bool allowMoveTeammateChess = 4; //当前玩家能否走队友的棋子,
  Poker drawnPoker = 5; //被抽取的牌,如果不等于NONE_POKER,此玩家被要求弃掉被抽取的牌
  bool selfAddExp = 6; //当前轮到的玩家是否可以加经验
  bool teammateAddExp = 7; //当前轮到的玩家的队友是否可以加经验
  map<int32, int32> handPokerAmount = 8; //当前所有玩家手牌数量,用于判断客户端与服务端数据是否一致
  Poker drawRewardPoker = 9; // 抽的奖励牌

}

//可以操的扑克牌移动棋子相关的数据
message PokerMoveData {
  oneof data {
    PlayCommonResultData commonResult = 1;  //当出的牌为A,K,Q,2,3,4,5,6,7,8,9,10时返回的值
    PlayJackResultData jackResult = 2; //当出牌为J时的返回值
    ComplexPlayKingResult complexKing = 5; //复杂出牌K时的返回结果
  }
  bool showPreResult = 3; //是否显示预览
  bool pokerTenDiscard = 4; //出牌为10时,是否可以让其他玩家弃牌
  int32 waitDrawnPokerCount = 6; //复杂玩法,出牌为红色Q时,可以被抽取的下家的手牌数,大于0表示可以抽牌
}


//游戏结算消息,命令:SETTLEMENT
message SettlementPub {
  int32 winMoney1 = 1; //获胜玩家第1名,获得的金币数
  int32 winMoney2 = 2; //获胜玩家第2名,获得的金币数
  repeated PlayerShowData player = 3; //玩家信息
  map<int32, bool > leftData = 4; //逃跑的的信息,如果玩家主动离开,或者被踢出,则会在此集合中,
}

//踢除玩家的消息,命令:KICK_PLAYER
message KickPlayerPub {
  int32 idx = 1; //被踢玩家的id
}


//移动到出生点
//移动之前的数据，toGrid是出生点格子，toGrid格子中没有棋子。
message MoveToBornData {
  GridData fromGrid = 1; //要移动的棋子所在的格子的数据,自己的棋子所在的格子
  GridData toGrid = 2; //到达的格子所在的格子数据
}

//走子完成,给其他玩家推送的消息
//命令MOVE_CHESS_PUB
message MoveChessPub {
  /**
    PlayResultItemData的各字段的说明:
        GridData fromGrid = 1; //棋子之前所在格子，走子后的结果(这个格子中的棋子已经设置为空)
        GridData toGrid = 2; //棋子所到的格子,走子后的结果(这个格子中的棋子已经设置为走子玩家的棋子了)
        bool kill = 3; //是否发生了吃子
   */
  PlayResultItemData moveResultData = 1; //走子结果
  Chess killedChess = 2; //被吃子的玩家棋子
  Poker currentPoker = 3; //当前出的牌
  bool moveChess = 4; //是否走子,false:没有走子,只播出牌动画,true:有走子,先播出牌动画,再播走子动画
  int32 remainPokerCount = 5; //出牌玩家，剩余的手牌数
  int32 moveChessPlayer = 6; //当前走子玩家id
  int32 expPlayer = 7; //加经验玩家id
  bool previewRewardPoker = 8; // 是否预览奖励牌
}

//POKER_SEVEN_MOVE_PUB 出牌7时的移动推送
message PokerSevenMovePub {
  repeated PokerSevenMoveItem moveResultData = 1; //走子结果,有顺序,第1元素表示第1个棋子的移动结果
  Poker currentPoker = 2; //当前出的牌
  bool moveChess = 3; //是否走子,false:没有走子,只播出牌动画,true:有走子,先播出牌动画,再播走子动画
  int32 remainPokerCount = 4; //出牌玩家，剩余的手牌数
  int32 moveChessPlayer = 5; //当前走子玩家id
  bool previewRewardPoker = 6; // 是否预览奖励牌
}

message PokerSevenMoveItem {
  PlayResultItemData moveResultData = 1; //走子结果
  Chess killedChess = 2; //被吃子的玩家棋子
  int32 expPlayer = 3; //加经验的玩家
}

//交换棋子,给其他玩家推送的消息
//命令EXCHANGE_CHESS_PUB
message ExchangeChessPub {
  /**
   PlayResultItemData的各字段的说明:
       GridData fromGrid = 1; //自己棋子之前所在格子，走子后的结果(这个格子中的棋子已经设置为别人的棋子)
       GridData toGrid = 2; //别人的棋子所在的格子,走子后的结果(这个格子中的棋子已经设置为自己的棋子了)
       bool kill = 3; //是否发生了吃子
  */
  PlayResultItemData selfMoveResultData = 1; //棋子走的结果
  Poker currentPoker = 2; //当前出的牌
  int32 remainPokerCount = 3; //出牌玩家，剩余的手牌数
  int32 moveChessPlayer = 4; //当前走子玩家id
  bool moveChess = 5; //是否走子,false:没有走子,只播出牌动画,true:有走子,先播出牌动画,再播走子动画
}

//复杂玩法出牌K的预览
message ComplexPlayKingResult {
  PlayResultItemData flyData = 1; //可起飞棋子的信息,
  repeated ImpactChessResult impactResult = 2; //可撞击的棋子信息
}

message ImpactChessResult {
  GridData fromGrid = 1; //要移动的棋子所在的格子的数据,自己的棋子所在的格子
  GridData toGrid = 2; //到达的格子所在的格子数据
  bool kill = 3; //是否发生吃子
  repeated ImpactedChessData impactedChess = 4; //被撞击的棋子的移动数据
  GridData transferGrid = 5; // 转移格子
  bool previewRewardPoker = 6; // 是否预览奖励牌
}

//撞击移动后的推送
message ImpactMovePub {
  PlayResultItemData impactMoveResult = 1; //棋子移动后的结果,(说明详见MoveChessPub定义)
  repeated ImpactedChessData impactedChess = 2; //被撞击的棋子的移动数据
  Poker currentPoker = 3; //当前出的牌
  bool moveChess = 4; //是否走子,false:没有走子,只播出牌动画,true:有走子,先播出牌动画,再播走子动画
  int32 remainPokerCount = 5; //出牌玩家，剩余的手牌数
  int32 moveChessPlayer = 6; //当前走子玩家id
  int32 expPlayer = 7; //加经验玩家id
  bool previewRewardPoker = 8; // 是否预览奖励牌
}

//被撞击的棋子数据
message ImpactedChessData {
  GridData from = 1; //撞击前棋子所在的位置(没有被击飞前的位置,在冲击路径上)
  int32 bornIndex = 2; //撞击后棋子回到的出生点的索引
}

//有玩家延迟了时间的消息,推给其他玩家的消息
//命令EXTEND_TIME_PUB
message ExtendTimePub {
  int32 idx = 1; //延迟出牌时间的玩家id
  int32 extendTime = 2; //延迟的时间,精确到秒
  int64 thinkingEndTime = 3; //延迟后思考结束时间
}




//主动退出游戏的消息,命令:QUIT_GAME
message QuitGamePub {
  int32 idx = 1; //主动退出游戏的玩家
}

//完成弃牌后的推送 FINISH_DISCARD_PUB
message FinishDiscardPub {
  Poker currentPoker = 1; //棋盘上弃牌区中的牌
  int32 remainPokerCount = 2; //弃牌玩家，剩余的手牌数
  repeated Poker discardedPoker = 3; //玩家弃的牌
  int32 discardPlayerId = 4; //完成弃牌的玩家id
}

//指定下一玩家弃牌后的推送 FORCED_DISCARD_PUB
message ForcedDiscardPub {
  int32 forcedDiscardIdx = 1; //被要求强制弃牌的玩家id
  Poker currentPoker = 2; //当前牌
  int32 playPokerPlayerId = 3; //出牌的玩家id,即:出牌10的玩家id
}

//托管时给玩家的推送 TRUST_PUB
message TrustPub {
  int32 idx = 1; //托管的玩家
  int32 consecutiveTrustCount = 2; //连续托管的出牌轮次数,超过指定次数将被踢除游戏
}

message TeamInfo {
  int32 teamAPlayer1 = 1;//队伍A玩家1 idx
  int32 teamAPlayer2 = 2;//队伍A玩家2 idx
  int32 teamBPlayer1 = 3;//队伍B玩家1 idx
  int32 teamBPlayer2 = 4;//队伍B玩家2 idx
}

//玩家游戏数据
/**
格子索引说明:
同一颜色区域内的索引
约定：
从最后一个终点格子开始,终点格子的索引依次为:-4,-3,-2,-1
终点区域前一格索引为:0
从终点格子前一格开始,索引依次为:1,2,3,4.....18
起飞点格子的索引为2,
一个玩家总共的格子：
4个出生点+4个终点+1个终点前一格(两个区域的连接点)+1个起飞点+17个普通格子=27个格子

红色区域出生点的索引为:101, 102, 103, 104
绿色区域出生点的索引为:201, 202, 203, 204
黄色区域出生点的索引为:301, 302, 303, 304
蓝色区域出生点的索引为:401, 402, 403, 404

 */
//以后扩展时,和游戏玩法、棋盘（即游戏系统）相关的玩家字段（字段的值对玩法有影响），其它玩家关注的字段,定义在这里,
//例如：玩家延时次数，延时花费,是否托管,就不用放在这里,因为,没有延时功能游戏可以继续进行,和玩法不是紧相关,
//是否托管,不能被别的玩家知道,没有托管功能游戏也可以继续进行,和玩法不是紧相关。
//和展示相关的玩家游戏数据字段（字段的值对玩法没有影响）定义在PlayerShowGameData
//和玩法不是紧相关的字段,且只有玩家自己可以看到的,定义在PlayerSelfGameData

message PlayerJackarooGameData {
  int32 idx = 1; //玩家id
  JackarooColor color = 2;//玩家的颜色,和玩家的棋子颜色是一样的
  map<int32, GridData> gridMap = 3; //属于当前玩家的格子,key:格子的索引,
  bool banker = 4; //是否是庄家,
  map<int32, PlayerChess> chessPosition = 5; //当前玩家所有棋子的位置,key:棋子的序号,所在方便客户端确定棋子的位置,此字段暂定
  repeated Poker handPoker = 6; //玩家的手牌
  PlayerStatus playerStatus = 7; //玩家的状态
  int32 remainThinkingTime = 8; //当前玩家剩余思考时间,精确到秒
  bool allowMoveTeammateChess = 9; //是否允许走队友的棋子
  bool forcedDiscard = 10; //是否被要求强制弃牌
  int32 handPokerNumber = 11; //玩家手牌数量,
  int32 totalThinkingTime = 12; //当前轮次思考总时间,

}

//棋盘数据
message ChessPanel {
  GameStatus gameStatus = 1;//游戏状态
  Poker currentPoker = 2; //当前出的牌
  int32 currentPlayer = 3; //当前玩家id
  int32 sendPokerRound = 4; //已发牌次数
  Poker selectedPoker = 5; //当前玩家已经选的牌(只有当前轮到的玩家断线重联时有值)
  SelectItemType selectedItem = 6; //当前玩家已经选择的选项(只有当前轮到的玩家断线重联时有值)
  Chess chess = 7; //当前玩家已经选择的棋子(只有当前轮到的玩家断线重联时有值)
  bool flyToChessPanel = 8; //当前玩家选择的牌是否已经飞到棋盘上(只有当前轮到的玩家断线重联时有值)

  //从1开始递增,客户端本地缓存此字段,如果服务端重启后,同一房间id重开一局,此字段会加1,
  // 客户端对比服务端返回的值与本地的值,如果不一样则清空本地数据
  int32 version = 9;
  int32 forcedDiscardPokerPlayer = 10; //被要求强制弃牌的玩家id
  int32 outRound = 11; //出牌操作的轮次,每次切换玩家加1,一直累计
  bool sendingPoker = 12; //当前是否正在执行发牌,true:正在发牌,false:没有发牌
  map<int32, KilledResult> killedResult = 13; //击杀结果,key:玩家id,value:吃子结果
  int64 thinkingEndTime = 14; //当前玩家思考结束时间。0:表示此字段无效
  int64 thinkingTotalTime = 15; //当前玩家思考总时间。0:表示此字段无效
}

//击杀结果
message KilledResult{
  int32 killChess = 1; //吃子数
  int32 chessKilled = 2; //被吃子数
}

//交换棋子时
message ExchangeChessData {
  MoveChessData selfData = 1; //自己的棋子数据
  MoveChessData otherData = 2; //别人的棋子数据
}

//移动棋子数据
message MoveChessData {
  int32 idx = 1; //玩家id
  int32 order = 2; //棋子的序号
  int32 number = 3; //移动的步数,如果是交换棋子,此字段不传,取默认值0
}

//出牌为7的时候传递的数据
message PokerSevenMoveData {
  MoveChessData firstData = 1; //第1颗棋子
  MoveChessData secondData = 2; //第2颗棋子

  // 正在操作的棋子,0:玩家只有1颗棋子可操作,或者玩家操作第1颗棋子后,第2颗棋子和要走的步数是确定的。
  //1:当前操作的是第1颗棋子,第1顆棋子处理完还要等玩家选择第2颗棋子
  int32 operatedChess = 3;
}

//格子数据
message GridData {
  Grid grid = 1; //地图格子
  Chess chess = 2; //棋子
}

//格子
message Grid {
  int32 index = 1; //格子的索引
  JackarooColor color = 2; //格子所属的颜色
  GridType type = 3; //格子的类型
}



//棋子数据
message Chess {
  int32 idx = 1; //棋子所属玩家
  int32 order = 2; //棋子的序号,从1开始
  JackarooColor color = 3; //棋子的颜色
}

//玩家的棋子
message PlayerChess {
  int32 order = 1; //棋子序号
  int32 position = 2; //玩家棋子所在格子的索引
  JackarooColor gridColor = 3; //棋子所在格子的颜色
}


//格子类型
enum GridType {

  BORN_POINT = 0; //出生点
  BEFORE_END = 1; //终点区域的前一格
  FLY_POINT = 2; //起飞点
  END_POINT = 3; //终点区域的格子
  COMMON_POINT = 4; //普通区域
  TRANSFER_POINT = 5; // 转移格子
  TRANSFER_END = 6; // 转移终点
}



//只有玩家自己可以看到的信息
message PlayerSelfData {
  int64 gold = 1; //金币
  int64 diamond = 2; //钻石
  bool isFirstRecharge = 4; //是否是首充
  int32 pokerSkinId = 5; //扑克牌皮肤id
  int32 talkSkinId = 6; //气泡皮肤id
  int32 chessSkinId = 7; //棋子皮肤id
  int32 chessPanelId = 8; //棋盘皮肤id
}


//可展示的游戏数据
message PlayerShowGameData {
  JackarooColor color = 1; //玩家颜色,根据玩家的颜色客户端可以确定玩家的位置,去掉以前的位置字段
  int32 winIndex = 2; //结算时本局游戏获胜玩家的排名,-1:失败，

}

//仅自己可见的游戏数据
message PlayerSelfGameData {
  int32 extendTimeCount = 1; //延时次数
  int32 extendTimeCost = 2; //延时需要消耗,钻石数
  bool trustSystem = 3; //是否托管,
  bool left = 4; //是否离开
  int32 consecutiveTrustCount = 5; //连续托管的出牌轮次数,超过指定次数将被踢除游戏
  Poker drawnPoker = 6; //被抽取的牌,用于断线重连,如果不为None_Poker,此玩家在这一轮要弃掉此牌
  int32 extendRemainCount = 7; //剩余延时次数
  bool selfAddExp = 8; //当前轮到的玩家是否可以加经验
  bool teammateAddExp = 9; //当前轮到的玩家的队友是否可以加经验
  string voiceToken = 10;// voice token
  string voiceCName = 11;// voice 频道名
  int32 currentRoundExtendTimeCount=12; //当前回合延时次数
}


//玩家展示信息
message PlayerShowData {
  int32 idx = 1; //用户id
  int32 faceId = 2; //头像框id
  string faceUrl = 3; //头像框地址
  string nickName = 4; //昵称
  int32 placeId = 5; // 所在位置  0大厅 其他对应的gameid -1 离线
  int32 friendType = 6; //好友类型
  int32 country = 7; //国家
  int32 level = 8; //等级
  int32 vipLevel = 9; //vip等级
  int32 winCount = 10; //获胜次数
  int32 totalCount = 11; //总次数
  int32 segId = 15; //段位
  int32 starNum = 16; //传奇之星
  int64 prettyId = 17; //显示id
  bool openChatOff = 18; //是否开启屏蔽聊天, true:开启,false:关闭
  int32 royalLevel = 20; //皇室等级,
  bool royalInvisible = 21; //是否不可见,false:所有人可见,true:仅自己可见
  bool royalLevelNameAnimation = 22; //昵称是否扫光,true:开启扫光,false:关闭扫光
  int32 giftId = 23; //别人赠送的互动礼物id

  string banTalkData = 24; //禁言数据,没有禁言时为空
  VoiceType voiceType = 25; //语音房类别
  bool openGameGift = 26; //是否开启互动表情
  int32 exp = 27; //经验
  int32 vipExp = 28; //vip经验
  int32 talkSkinId = 29; //气泡皮肤id
  int32 pokerSkinId = 30; //扑克牌皮肤id
}

//游戏状态
enum GameStatus {
  GAME_PREPARE = 0; //准备阶段
  GAMING = 1; //进行中
  GAME_RESULT = 2; //游戏结算
  GAME_END = 3;//游戏结束
}

//玩家状态
enum PlayerStatus {
  NONE_PLAYER_STATUS = 0;//初始状态
  WAIT_OUT = 1; //等待出牌
  WAIT_DISCARD = 2; //等待玩家弃牌
  WAIT_MOVE = 3; //等待玩家走子

  OPERATE_END = 1000; //操作结束
}


// 语音房类别
enum VoiceType {
  NONE_VOICE_TYPE = 0;// 占位符
  AGORA = 1;//声网
  ZEGO = 2;//即构
}


//操作结果
enum OperateResult {
  FAIL = 0; //失败
  SUC = 1; //成功
}

//游戏道具类型
enum GameGroup {
  SPORTS_MODE = 0;//竞技
  PROP_MODE = 1;//道具
}

// 游戏模式类型，Ludo使用全部，Domino使用0和1
enum GameType {
  NORMAL = 0;//正常
  MASTER = 1;//大师
  QUICK = 2;//快速
  FIRE = 3;//火拼
  ARROW = 4; //箭头
  DUEL = 5;//决斗
  JUNGLE = 6; //丛林模式
  NIGHT = 7; //黑夜模式

  // Jackaroo
  JACKAROO_BASIC = 100;    // JACKAROO basic
  JACKAROO_COMPLEX = 101;    // JACKAROO complex
  JACKAROO_BASIC_QUICK = 102;    // JACKAROO basic quick
  JACKAROO_COMPLEX_QUICK = 103; // JACKAROO complex quick
  JACKAROO_COMPLEX_ONE_VS_ONE = 104; // JACKAROO 1vs1 complex
}

//玩法枚举
enum PlayMethod {
  BASIC_PLAY = 0; //基础玩法
  COMPLEX_PLAY = 1; //复杂玩法
  COMPLEX_ONE_VS_ONE_PLAY = 2; // 1vs1复杂玩法
}

//玩法规则,定义一个对象包装一下,为了方便以后扩展
message PlayRule {
  PlayMethod playMethod = 1; //玩法类型
}


// 游戏对战类型
enum GameAgainstType {
  MELEE = 0;    // 4人混战
  TWO_VS_TWO = 1;    // 2对2
  ONE_VS_ONE = 2; // 1对1
}


//扑克牌定义
//快速获得点数,枚举的整数num,point=num%13,point=0表示K
enum Poker {
  NONE_POKER = 0; //占位符

  SPADE_ACE = 1; //黑桃A,spade表示黑桃
  SPADE_TWO = 2;
  SPADE_THREE = 3;
  SPADE_FOUR = 4;
  SPADE_FIVE = 5;
  SPADE_SIX = 6;
  SPADE_SEVEN = 7;
  SPADE_EIGHT = 8;
  SPADE_NINE = 9;
  SPADE_TEN = 10;
  SPADE_JACK = 11;
  SPADE_QUEEN = 12;
  SPADE_KING = 13;

  HEART_ACE = 14; //红桃A,heart表示红桃
  HEART_TWO = 15; //红桃2
  HEART_THREE = 16;
  HEART_FOUR = 17;
  HEART_FIVE = 18;
  HEART_SIX = 19;
  HEART_SEVEN = 20;
  HEART_EIGHT = 21;
  HEART_NINE = 22;
  HEART_TEN = 23;
  HEART_JACK = 24;
  HEART_QUEEN = 25;
  HEART_KING = 26; //红桃K

  DIAMOND_ACE = 27; //方片A
  DIAMOND_TWO = 28;
  DIAMOND_THREE = 29;
  DIAMOND_FOUR = 30;
  DIAMOND_FIVE = 31;
  DIAMOND_SIX = 32;
  DIAMOND_SEVEN = 33;
  DIAMOND_EIGHT = 34;
  DIAMOND_NINE = 35;
  DIAMOND_TEN = 36;
  DIAMOND_JACK = 37;
  DIAMOND_QUEEN = 38;
  DIAMOND_KING = 39; //方片K

  CLUB_ACE = 40; //梅花A
  CLUB_TWO = 41;
  CLUB_THREE = 42;
  CLUB_FOUR = 43;
  CLUB_FIVE = 44;
  CLUB_SIX = 45;
  CLUB_SEVEN = 46;
  CLUB_EIGHT = 47;
  CLUB_NINE = 48;
  CLUB_TEN = 49;
  CLUB_JACK = 50;
  CLUB_QUEEN = 51;
  CLUB_KING = 52; //梅花K
}

//仅客户端使用 开始
message AuthRequest {
  string app_id = 1;
  oneof identity {
    string open_id = 2;
    int32 user_id = 3;
    int64 long_user_id = 4;
  }
  string token = 5;
  string sign = 6;
  string device_Id = 7;//设备Id
  int32 channel_Id = 8;//渠道Id 0apple 1google,2:未知，3:U3D_ios,4:U3D_android, 5:cocos_ios,6:cocos_android,7:huawei 8:honor 9:小程序安卓 10:小程序ios
  string version = 9;
  int64 timestamp = 10;//时间戳
}

message AuthResponse {

}

message TyrOption {
          // todo route key 调整为oneof
    int64 ring_route_key = 1;//一致性hash路由标识
    string fixed_route_key = 2;// 固定路由标识
    string fixed_address_key = 3;// 固定地址

    string group_id = 4;//群组Id
}

//仅客户端使用结束