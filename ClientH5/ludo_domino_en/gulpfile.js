var gulp = require('gulp');
var ts = require('gulp-typescript');
var livereload = require('gulp-livereload');
var gulpMerge = require('gulp-merge');
var gulpConcat = require('gulp-concat');
var uglify = require('gulp-uglify');
var fs = require('fs');
var stripJsonComments = require("strip-json-comments");
var child_process = require('child_process');
var cheerio = require('cheerio');

var argv = require('minimist')(process.argv.slice(2));

function readFiles() {
    var myHtml = fs.readFileSync("./bin/index.html");
    var $ = cheerio.load(myHtml);
    var t = $('html').find('script');
    var t2 = t.nextAll();

    var files = [];
    t.each(function(i, elem) {
        var file = "./bin/" + elem.attribs.src;
        console.log(file);
        files.push(file);
    });

    return files;
}

// JS合并
gulp.task('concat', function () {
    var files = readFiles();

    return gulpMerge(gulp.src(files))
        .pipe(gulpConcat('code.js'))
        .pipe(gulp.dest('./bin'))
        .pipe(gulp.dest('.././release/web'));
});

// JS压缩
gulp.task('min',['concat'], function () {
    var files = readFiles();

    return gulpMerge(gulp.src(files))
        .pipe(gulpConcat('code.js'))
        .pipe(uglify())
        .pipe(gulp.dest('./bin'))
        .pipe(gulp.dest('.././release/web'));
});