<project version="0.9.8">
   <asynRes>img,temp,sound                  </asynRes>
   <unDealRes>embed                  </unDealRes>
   <resTypes>png,jpg   </resTypes>
   <resExportPath>bin/res/atlas   </resExportPath>
   <asynResExportPath>bin   </asynResExportPath>
   <codeExportPath>src/ui   </codeExportPath>
   <codeImports><![CDATA[import laya.ui.*;]]>   </codeImports>
   <codeImportsJS><![CDATA[var View=laya.ui.View;
var Dialog=laya.ui.Dialog;]]>   </codeImportsJS>
   <uiType>0   </uiType>
   <uiExportPath>bin/ui.json   </uiExportPath>
   <boxTypes>Box,List,Tab,RadioGroup,ViewStack,Panel,HBox,VBox,Tree,Sprite   </boxTypes>
   <pageTypes>View,Dialog   </pageTypes>
   <shareResPath/>
   <codeType>1   </codeType>
   <resCanCompress/>
   <resPublishQuality>80                  </resPublishQuality>
   <langPath/>
   <defaultFont/>
   <codeImportsTS><![CDATA[import View=laya.ui.View;
import Dialog=laya.ui.Dialog;]]>   </codeImportsTS>
   <textureWidth>2048   </textureWidth>
   <textureHeight>2048   </textureHeight>
   <picWidth>512   </picWidth>
   <picHeight>512   </picHeight>
   <power2>false   </power2>
   <trimempty>false   </trimempty>
   <codeViewExportPath>src/view   </codeViewExportPath>
   <picType>0   </picType>
   <atlasType>1   </atlasType>
   <atlasScale/>
   <copyRes>true   </copyRes>
   <dataCompact>true   </dataCompact>
   <ver>0.9.9 beta   </ver>
</project>