{"x": 0, "type": "View", "selectedBox": 201, "selecteID": 135, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "mouseThrough": false, "mouseEnabled": true, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"top": 0, "skin": "domino/maskBg.png", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 244, "child": []}, {"x": 15, "type": "Box", "props": {"var": "content", "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Box(content)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 242, "child": [{"x": 30, "type": "Image", "props": {"y": 0, "x": 0, "width": 718, "var": "bgImg", "skin": "domino/image_bg_title.png", "height": 846}, "nodeParent": 242, "label": "Image(bgImg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 36, "child": [{"x": 45, "type": "Image", "props": {"x": 12, "width": 694, "top": 93, "skin": "domino/image_bg_content.png", "bottom": 12}, "nodeParent": 36, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 136, "child": []}, {"x": 45, "type": "Image", "props": {"y": 108, "x": 25, "width": 667, "visible": false, "var": "item_bg_0", "skin": "domino/bg_oneself.png", "height": 153}, "nodeParent": 36, "label": "Image(item_bg_0)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 137, "child": [{"x": 60, "type": "Image", "props": {"y": 54, "x": 196, "width": 262, "skin": "domino/bg_oneself2.png", "name": "bg0", "height": 35}, "nodeParent": 137, "label": "Image(bg0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 144, "child": []}, {"x": 60, "type": "Image", "props": {"y": 99, "x": 196, "width": 262, "skin": "domino/bg_oneself2.png", "name": "bg1", "height": 35}, "nodeParent": 137, "label": "Image(bg1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 145, "child": []}, {"x": 60, "type": "Image", "props": {"y": 15, "x": 425, "width": 27, "visible": false, "var": "exp", "skin": "domino/EXP.png", "height": 34}, "nodeParent": 137, "label": "Image(exp)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 199, "child": []}, {"x": 60, "type": "Box", "props": {"y": 25, "x": 472, "width": 180, "name": "cardList", "height": 107}, "nodeParent": 137, "label": "Box(cardList)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 54, "child": []}], "$HIDDEN": false}, {"x": 45, "type": "Image", "props": {"y": 271, "x": 25, "width": 667, "visible": false, "var": "item_bg_1", "skin": "domino/bg_others.png", "height": 153}, "nodeParent": 36, "label": "Image(item_bg_1)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 138, "child": [{"x": 60, "type": "Image", "props": {"y": 57, "x": 196, "width": 262, "skin": "domino/bg_others2.png", "name": "bg0", "height": 35}, "nodeParent": 138, "label": "Image(bg0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 203, "child": []}, {"x": 60, "type": "Image", "props": {"y": 102, "x": 196, "width": 262, "skin": "domino/bg_others2.png", "name": "bg1", "height": 35}, "nodeParent": 138, "label": "Image(bg1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 204, "child": []}, {"x": 60, "type": "Box", "props": {"y": 28, "x": 472, "width": 180, "name": "cardList", "height": 107}, "nodeParent": 138, "label": "Box(cardList)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 207, "child": []}], "$HIDDEN": false}, {"x": 45, "type": "Image", "props": {"y": 434, "x": 25, "width": 667, "visible": false, "var": "item_bg_2", "skin": "domino/bg_others.png", "height": 153}, "nodeParent": 36, "label": "Image(item_bg_2)", "isOpen": null, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 139, "child": [{"type": "Image", "props": {"y": 56, "x": 196, "width": 262, "skin": "domino/bg_others2.png", "name": "bg0", "height": 35}, "nodeParent": 139, "label": "Image(bg0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 216, "child": []}, {"type": "Image", "props": {"y": 101, "x": 196, "width": 262, "skin": "domino/bg_others2.png", "name": "bg1", "height": 35}, "nodeParent": 139, "label": "Image(bg1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 217, "child": []}, {"type": "Box", "props": {"y": 27, "x": 472, "width": 180, "name": "cardList", "height": 107}, "nodeParent": 139, "label": "Box(cardList)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 220, "child": []}], "$HIDDEN": false}, {"x": 45, "type": "Image", "props": {"y": 597, "x": 25, "width": 667, "visible": false, "var": "item_bg_3", "skin": "domino/bg_others.png", "height": 153}, "nodeParent": 36, "label": "Image(item_bg_3)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 140, "child": [{"type": "Image", "props": {"y": 56, "x": 196, "width": 262, "skin": "domino/bg_others2.png", "name": "bg0", "height": 35}, "nodeParent": 140, "label": "Image(bg0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 229, "child": []}, {"type": "Image", "props": {"y": 101, "x": 196, "width": 262, "skin": "domino/bg_others2.png", "name": "bg1", "height": 35}, "nodeParent": 140, "label": "Image(bg1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 230, "child": []}, {"type": "Box", "props": {"y": 27, "x": 472, "width": 180, "name": "cardList", "height": 107}, "nodeParent": 140, "label": "Box(cardList)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 233, "child": []}]}, {"x": 45, "type": "Box", "props": {"y": 123, "x": 51, "var": "nameItemBox", "renderType": "render"}, "nodeParent": 36, "label": "Box(nameItemBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 201, "child": [{"x": 60, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 8, "x": 170, "visible": false, "var": "nameItem_0"}, "nodeParent": 201, "label": "UIView(nameItem_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 250, "child": [], "$HIDDEN": false}, {"x": 60, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 174, "x": 170, "visible": false, "var": "nameItem_1"}, "nodeParent": 201, "label": "UIView(nameItem_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 251, "child": [], "$HIDDEN": false}, {"x": 60, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 336, "x": 170, "visible": false, "var": "nameItem_2"}, "nodeParent": 201, "label": "UIView(nameItem_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 252, "child": [], "$HIDDEN": false}, {"x": 60, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 500, "x": 170, "visible": false, "var": "nameItem_3"}, "nodeParent": 201, "label": "UIView(nameItem_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 253, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Image", "props": {"y": -5, "x": 122, "width": 37, "skin": "domino/WIN.png", "height": 60}, "nodeParent": 201, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 135, "child": []}]}, {"x": 45, "type": "Box", "props": {"y": 131, "x": 51, "visible": false, "var": "box_txt_0"}, "nodeParent": 36, "label": "Box(box_txt_0)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 254, "child": [{"type": "Label", "props": {"y": 33, "x": 191, "width": 103, "valign": "middle", "text": "SCORE", "height": 31, "fontSize": 18, "color": "#FFFFFF", "align": "left"}, "nodeParent": 254, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 61, "child": [], "$LOCKED": false}, {"type": "Label", "props": {"y": 34, "x": 290, "width": 64, "valign": "middle", "text": "0", "name": "scoreTxt", "height": 30, "fontSize": 30, "color": "#fffc9d", "align": "center"}, "nodeParent": 254, "label": "Label(scoreTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 62, "child": []}, {"type": "Label", "props": {"y": 78, "x": 191, "width": 103, "valign": "middle", "text": "TOTAL", "height": 31, "fontSize": 18, "color": "#FFFFFF", "align": "left"}, "nodeParent": 254, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 66, "child": [], "$LOCKED": false}, {"type": "Label", "props": {"y": 78, "x": 290, "width": 64, "valign": "middle", "text": "0", "name": "totalTxt", "height": 30, "fontSize": 30, "color": "#fffc9d", "align": "center"}, "nodeParent": 254, "label": "Label(totalTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 67, "child": []}, {"type": "Label", "props": {"y": 24, "width": 66, "text": "1", "name": "numTxt", "height": 60, "fontSize": 54, "color": "#f2a500"}, "nodeParent": 254, "label": "Label(numTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 68, "child": [], "$LOCKED": false}]}, {"x": 45, "type": "Box", "props": {"y": 297, "x": 51, "visible": false, "var": "box_txt_1"}, "nodeParent": 36, "label": "Box(box_txt_1)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 255, "child": [{"type": "Label", "props": {"y": 33, "x": 191, "width": 103, "valign": "middle", "text": "SCORE", "height": 31, "fontSize": 18, "color": "#FFFFFF", "align": "left"}, "nodeParent": 255, "label": "Label", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 209, "child": [], "$LOCKED": false}, {"type": "Label", "props": {"y": 34, "x": 290, "width": 64, "valign": "middle", "text": "0", "name": "scoreTxt", "height": 30, "fontSize": 30, "color": "#fffc9d", "align": "center"}, "nodeParent": 255, "label": "Label(scoreTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 210, "child": []}, {"type": "Label", "props": {"y": 78, "x": 191, "width": 103, "valign": "middle", "text": "TOTAL", "height": 31, "fontSize": 18, "color": "#FFFFFF", "align": "left"}, "nodeParent": 255, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 211, "child": [], "$LOCKED": false}, {"type": "Label", "props": {"y": 78, "x": 290, "width": 64, "valign": "middle", "text": "0", "name": "totalTxt", "height": 30, "fontSize": 30, "color": "#fffc9d", "align": "center"}, "nodeParent": 255, "label": "Label(totalTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 212, "child": []}, {"type": "Label", "props": {"y": 24, "width": 66, "text": "2", "name": "numTxt", "height": 60, "fontSize": 54, "color": "#7ab8a6"}, "nodeParent": 255, "label": "Label(numTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 213, "child": [], "$LOCKED": false}]}, {"x": 45, "type": "Box", "props": {"y": 459, "x": 51, "visible": false, "var": "box_txt_2"}, "nodeParent": 36, "label": "Box(box_txt_2)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 256, "child": [{"type": "Label", "props": {"y": 33, "x": 191, "width": 103, "valign": "middle", "text": "SCORE", "height": 31, "fontSize": 18, "color": "#FFFFFF", "align": "left"}, "nodeParent": 256, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 222, "child": [], "$LOCKED": false}, {"type": "Label", "props": {"y": 34, "x": 290, "width": 64, "valign": "middle", "text": "0", "name": "scoreTxt", "height": 30, "fontSize": 30, "color": "#fffc9d", "align": "center"}, "nodeParent": 256, "label": "Label(scoreTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 223, "child": []}, {"type": "Label", "props": {"y": 78, "x": 191, "width": 103, "valign": "middle", "text": "TOTAL", "height": 31, "fontSize": 18, "color": "#FFFFFF", "align": "left"}, "nodeParent": 256, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 224, "child": [], "$LOCKED": false}, {"type": "Label", "props": {"y": 78, "x": 290, "width": 64, "valign": "middle", "text": "0", "name": "totalTxt", "height": 30, "fontSize": 30, "color": "#fffc9d", "align": "center"}, "nodeParent": 256, "label": "Label(totalTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 225, "child": []}, {"type": "Label", "props": {"y": 24, "width": 66, "text": "3", "name": "numTxt", "height": 60, "fontSize": 54, "color": "#7ab8a6"}, "nodeParent": 256, "label": "Label(numTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 226, "child": [], "$LOCKED": false}]}, {"x": 45, "type": "Box", "props": {"y": 622, "x": 51, "visible": false, "var": "box_txt_3"}, "nodeParent": 36, "label": "Box(box_txt_3)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 257, "child": [{"type": "Label", "props": {"y": 33, "x": 191, "width": 103, "valign": "middle", "text": "SCORE", "height": 31, "fontSize": 18, "color": "#FFFFFF", "align": "left"}, "nodeParent": 257, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 235, "child": [], "$LOCKED": false}, {"type": "Label", "props": {"y": 34, "x": 290, "width": 64, "valign": "middle", "text": "0", "name": "scoreTxt", "height": 30, "fontSize": 30, "color": "#fffc9d", "align": "center"}, "nodeParent": 257, "label": "Label(scoreTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 236, "child": []}, {"type": "Label", "props": {"y": 78, "x": 191, "width": 103, "valign": "middle", "text": "TOTAL", "height": 31, "fontSize": 18, "color": "#FFFFFF", "align": "left"}, "nodeParent": 257, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 237, "child": [], "$LOCKED": false}, {"type": "Label", "props": {"y": 78, "x": 290, "width": 64, "valign": "middle", "text": "0", "name": "totalTxt", "height": 30, "fontSize": 30, "color": "#fffc9d", "align": "center"}, "nodeParent": 257, "label": "Label(totalTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 238, "child": []}, {"type": "Label", "props": {"y": 24, "width": 66, "text": "4", "name": "numTxt", "height": 60, "fontSize": 54, "color": "#7ab8a6"}, "nodeParent": 257, "label": "Label(numTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 239, "child": [], "$LOCKED": false}]}, {"x": 45, "type": "Label", "props": {"y": 131, "x": 221, "width": 210, "visible": false, "var": "nameTxt_0", "text": "You", "height": 30, "fontSize": 24, "color": "#fa9300"}, "nodeParent": 36, "label": "Label(nameTxt_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 142, "child": [], "$LOCKED": false, "$HIDDEN": true}, {"x": 45, "type": "Label", "props": {"y": 297, "x": 221, "width": 266, "visible": false, "var": "nameTxt_1", "text": "You", "height": 30, "fontSize": 24, "color": "#428371"}, "nodeParent": 36, "label": "Label(nameTxt_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 214, "child": [], "$LOCKED": true, "$HIDDEN": false}, {"x": 45, "type": "Label", "props": {"y": 459, "x": 221, "width": 266, "visible": false, "var": "nameTxt_2", "text": "You", "height": 30, "fontSize": 24, "color": "#428371"}, "nodeParent": 36, "label": "Label(nameTxt_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 227, "child": [], "$LOCKED": false, "$HIDDEN": true}, {"x": 45, "type": "Label", "props": {"y": 623, "x": 221, "width": 266, "visible": false, "var": "nameTxt_3", "text": "You", "height": 30, "fontSize": 24, "color": "#428371"}, "nodeParent": 36, "label": "Label(nameTxt_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 240, "child": [], "$LOCKED": false, "$HIDDEN": true}, {"x": 45, "type": "Label", "props": {"y": 125, "x": 478, "width": 32, "var": "expTxt", "text": "+1", "strokeColor": "#b76c01", "stroke": 2, "height": 28, "fontSize": 24, "color": "#ffe400"}, "nodeParent": 36, "label": "Label(expTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 200, "child": []}, {"x": 45, "type": "Label", "props": {"y": 26, "x": 122, "width": 334, "var": "roundTxt", "valign": "middle", "text": "ROUND  1", "strokeColor": "#30876d", "stroke": 3, "height": 54, "fontSize": 42, "color": "#ffffff", "centerX": 0, "align": "center"}, "nodeParent": 36, "label": "Label(roundTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 73, "child": []}, {"x": 45, "type": "Label", "props": {"y": 767, "x": 190, "width": 249, "var": "nextRoundTxt", "text": "Next round in", "height": 56, "fontSize": 40, "color": "#7ab8a6"}, "nodeParent": 36, "label": "Label(nextRoundTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 245, "child": []}], "$LOCKED": false}, {"x": 30, "type": "Label", "props": {"y": 767, "x": 474, "width": 50, "var": "nextTimeTxt", "text": "0s", "height": 56, "fontSize": 40, "color": "#7ab8a6"}, "nodeParent": 242, "label": "Label(nextTimeTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 134, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}