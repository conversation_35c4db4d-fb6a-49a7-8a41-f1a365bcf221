{"x": 0, "type": "View", "selectedBox": 196, "selecteID": 126, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "mouseThrough": false, "mouseEnabled": true, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"top": 0, "skin": "domino/maskBg.png", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 190, "child": []}, {"x": 15, "type": "Box", "props": {"var": "content", "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Box(content)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 167, "child": [{"x": 30, "type": "Image", "props": {"width": 718, "var": "bgImg", "skin": "domino/image_bg_title.png", "height": 846}, "nodeParent": 167, "label": "Image(bgImg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 127, "child": [{"x": 45, "type": "Image", "props": {"x": 12, "width": 694, "top": 93, "skin": "domino/image_bg_content.png", "bottom": 12}, "nodeParent": 127, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 128, "child": []}, {"x": 45, "type": "Image", "props": {"y": 108, "x": 25, "width": 667, "visible": false, "var": "bg0", "skin": "domino/bg_oneself.png", "height": 153}, "nodeParent": 127, "label": "Image(bg0)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 129, "child": [{"type": "Image", "props": {"y": 79, "x": 419, "width": 227, "skin": "domino/bg_oneself2.png", "name": "bg", "height": 41}, "nodeParent": 129, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 134, "child": []}, {"type": "Image", "props": {"y": 79, "x": 159, "width": 227, "skin": "domino/bg_oneself2.png", "name": "bg0", "height": 41}, "nodeParent": 129, "label": "Image(bg0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 133, "child": []}, {"type": "Image", "props": {"y": 15, "x": 547, "visible": false, "skin": "domino/EXP.png", "name": "exp"}, "nodeParent": 129, "label": "Image(exp)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 168, "child": []}]}, {"x": 45, "type": "Image", "props": {"y": 274, "x": 25, "width": 667, "visible": false, "var": "bg1", "skin": "domino/bg_others.png", "height": 153}, "nodeParent": 127, "label": "Image(bg1)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 130, "child": [{"type": "Image", "props": {"y": 79, "x": 419, "width": 227, "skin": "domino/bg_others2.png", "name": "bg", "height": 41}, "nodeParent": 130, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 135, "child": [], "$HIDDEN": false}, {"type": "Image", "props": {"y": 79, "x": 159, "width": 227, "skin": "domino/bg_others2.png", "name": "bg0", "height": 41}, "nodeParent": 130, "label": "Image(bg0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 136, "child": [], "$HIDDEN": false}, {"type": "Image", "props": {"y": 14, "x": 547, "visible": false, "skin": "domino/EXP.png", "name": "exp"}, "nodeParent": 130, "label": "Image(exp)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 184, "child": [], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 45, "type": "Image", "props": {"y": 440, "x": 25, "width": 667, "visible": false, "var": "bg2", "skin": "domino/bg_others.png", "height": 153}, "nodeParent": 127, "label": "Image(bg2)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 131, "child": [{"type": "Image", "props": {"y": 78, "x": 419, "width": 227, "skin": "domino/bg_others2.png", "name": "bg", "height": 41}, "nodeParent": 131, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 144, "child": []}, {"type": "Image", "props": {"y": 78, "x": 159, "width": 227, "skin": "domino/bg_others2.png", "name": "bg0", "height": 41}, "nodeParent": 131, "label": "Image(bg0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 145, "child": []}, {"type": "Image", "props": {"y": 14, "x": 547, "visible": false, "skin": "domino/EXP.png", "name": "exp"}, "nodeParent": 131, "label": "Image(exp)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 186, "child": []}]}, {"x": 45, "type": "Image", "props": {"y": 606, "x": 25, "width": 667, "visible": false, "var": "bg3", "skin": "domino/bg_others.png", "height": 153}, "nodeParent": 127, "label": "Image(bg3)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 132, "child": [{"type": "Image", "props": {"y": 77, "x": 419, "width": 227, "skin": "domino/bg_others2.png", "name": "bg", "height": 41}, "nodeParent": 132, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 154, "child": []}, {"type": "Image", "props": {"y": 77, "x": 159, "width": 227, "skin": "domino/bg_others2.png", "name": "bg0", "height": 41}, "nodeParent": 132, "label": "Image(bg0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 155, "child": []}, {"type": "Image", "props": {"y": 13, "x": 547, "visible": false, "skin": "domino/EXP.png", "name": "exp"}, "nodeParent": 132, "label": "Image(exp)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 188, "child": []}]}], "$LOCKED": false}, {"x": 30, "type": "Box", "props": {"y": 0, "x": 0, "var": "nameItemBox"}, "nodeParent": 167, "label": "Box(nameItemBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 196, "child": [{"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 147, "x": 189, "visible": false, "var": "nameItem_0"}, "nodeParent": 196, "label": "UIView(nameItem_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 192, "child": []}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 315, "x": 189, "visible": false, "var": "nameItem_1"}, "nodeParent": 196, "label": "UIView(nameItem_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 193, "child": [], "$HIDDEN": false}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 481, "x": 189, "visible": false, "var": "nameItem_2"}, "nodeParent": 196, "label": "UIView(nameItem_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 194, "child": []}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 646, "x": 189, "visible": false, "var": "nameItem_3"}, "nodeParent": 196, "label": "UIView(nameItem_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 195, "child": []}, {"x": 45, "type": "Image", "props": {"y": 118, "x": 130, "skin": "domino/WIN.png"}, "nodeParent": 196, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 126, "child": []}]}, {"x": 30, "type": "Box", "props": {"y": 125, "x": 47, "visible": false, "var": "item_0", "renderType": "render"}, "nodeParent": 167, "label": "Box(item_0)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 164, "child": [{"type": "Label", "props": {"y": 5, "x": 575, "width": 55, "text": "+1", "strokeColor": "#b76c01", "stroke": 2, "name": "expTxt", "height": 40, "fontSize": 34, "color": "#ffe400"}, "nodeParent": 164, "label": "Label(expTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 169, "child": []}, {"type": "Label", "props": {"y": 66, "x": 421, "width": 76, "valign": "middle", "text": "TOTAL", "height": 31, "fontSize": 20, "color": "#FFFFFF", "align": "right"}, "nodeParent": 164, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 69, "child": [], "$LOCKED": false, "$HIDDEN": false}, {"type": "Label", "props": {"y": 64, "x": 523, "width": 64, "valign": "middle", "text": "0", "name": "totalTxt", "height": 33, "fontSize": 33, "color": "#fffc9d", "align": "center"}, "nodeParent": 164, "label": "Label(totalTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 70, "child": []}, {"type": "Label", "props": {"y": 66, "x": 174, "width": 103, "valign": "middle", "text": "SCORE", "height": 31, "fontSize": 20, "color": "#FFFFFF", "align": "left"}, "nodeParent": 164, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 170, "child": [], "$LOCKED": false}, {"type": "Label", "props": {"y": 64, "x": 264, "width": 64, "valign": "middle", "text": "0", "name": "scoreTxt", "height": 33, "fontSize": 33, "color": "#fffc9d", "align": "center"}, "nodeParent": 164, "label": "Label(scoreTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 171, "child": []}]}, {"x": 30, "type": "Box", "props": {"y": 290, "x": 47, "visible": false, "var": "item_1"}, "nodeParent": 167, "label": "Box(item_1)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 163, "child": [{"type": "Label", "props": {"y": 67, "x": 174, "width": 103, "valign": "middle", "text": "SCORE", "height": 31, "fontSize": 20, "color": "#FFFFFF", "align": "left"}, "nodeParent": 163, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 172, "child": [], "$LOCKED": false, "$HIDDEN": false}, {"type": "Label", "props": {"y": 65, "x": 264, "width": 64, "valign": "middle", "text": "0", "name": "scoreTxt", "height": 30, "fontSize": 33, "color": "#fffc9d", "align": "center"}, "nodeParent": 163, "label": "Label(scoreTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 173, "child": [], "$HIDDEN": false}, {"type": "Label", "props": {"y": 67, "x": 421, "width": 76, "valign": "middle", "text": "TOTAL", "height": 31, "fontSize": 20, "color": "#FFFFFF", "align": "right"}, "nodeParent": 163, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 178, "child": [], "$LOCKED": false, "$HIDDEN": false}, {"type": "Label", "props": {"y": 65, "x": 523, "width": 64, "valign": "middle", "text": "0", "name": "totalTxt", "height": 30, "fontSize": 33, "color": "#fffc9d", "align": "center"}, "nodeParent": 163, "label": "Label(totalTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 179, "child": [], "$HIDDEN": false}, {"type": "Label", "props": {"y": 5, "x": 575, "width": 55, "text": "+1", "strokeColor": "#b76c01", "stroke": 2, "name": "expTxt", "height": 40, "fontSize": 34, "color": "#ffe400"}, "nodeParent": 163, "label": "Label(expTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 185, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Box", "props": {"y": 456, "x": 47, "width": 627, "visible": false, "var": "item_2", "renderType": "render", "height": 121}, "nodeParent": 167, "label": "Box(item_2)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 98, "child": [{"type": "Label", "props": {"y": 67, "x": 174, "width": 103, "valign": "middle", "text": "SCORE", "height": 31, "fontSize": 20, "color": "#FFFFFF", "align": "left"}, "nodeParent": 98, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 174, "child": [], "$LOCKED": false}, {"type": "Label", "props": {"y": 65, "x": 264, "width": 64, "valign": "middle", "text": "0", "name": "scoreTxt", "height": 30, "fontSize": 33, "color": "#fffc9d", "align": "center"}, "nodeParent": 98, "label": "Label(scoreTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 175, "child": []}, {"type": "Label", "props": {"y": 67, "x": 421, "width": 76, "valign": "middle", "text": "TOTAL", "height": 31, "fontSize": 20, "color": "#FFFFFF", "align": "right"}, "nodeParent": 98, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 180, "child": [], "$LOCKED": false, "$HIDDEN": false}, {"type": "Label", "props": {"y": 65, "x": 523, "width": 64, "valign": "middle", "text": "0", "name": "totalTxt", "height": 30, "fontSize": 33, "color": "#fffc9d", "align": "center"}, "nodeParent": 98, "label": "Label(totalTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 181, "child": []}, {"type": "Label", "props": {"y": 5, "x": 575, "width": 55, "text": "+1", "strokeColor": "#b76c01", "stroke": 2, "name": "expTxt", "height": 40, "fontSize": 34, "color": "#ffe400"}, "nodeParent": 98, "label": "Label(expTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 187, "child": []}]}, {"x": 30, "type": "Box", "props": {"y": 621, "x": 47, "width": 627, "visible": false, "var": "item_3", "renderType": "render", "height": 121}, "nodeParent": 167, "label": "Box(item_3)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 153, "child": [{"type": "Label", "props": {"y": 67, "x": 174, "width": 103, "valign": "middle", "text": "SCORE", "height": 31, "fontSize": 20, "color": "#FFFFFF", "align": "left"}, "nodeParent": 153, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 176, "child": [], "$LOCKED": false}, {"type": "Label", "props": {"y": 65, "x": 264, "width": 64, "valign": "middle", "text": "0", "name": "scoreTxt", "height": 30, "fontSize": 33, "color": "#fffc9d", "align": "center"}, "nodeParent": 153, "label": "Label(scoreTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 177, "child": []}, {"type": "Label", "props": {"y": 67, "x": 421, "width": 76, "valign": "middle", "text": "TOTAL", "height": 31, "fontSize": 20, "color": "#FFFFFF", "align": "right"}, "nodeParent": 153, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 182, "child": [], "$LOCKED": false, "$HIDDEN": false}, {"type": "Label", "props": {"y": 65, "x": 523, "width": 64, "valign": "middle", "text": "0", "name": "totalTxt", "height": 30, "fontSize": 33, "color": "#fffc9d", "align": "center"}, "nodeParent": 153, "label": "Label(totalTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 183, "child": []}, {"type": "Label", "props": {"y": 5, "x": 575, "width": 55, "text": "+1", "strokeColor": "#b76c01", "stroke": 2, "name": "expTxt", "height": 40, "fontSize": 34, "color": "#ffe400"}, "nodeParent": 153, "label": "Label(expTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 189, "child": []}]}, {"x": 30, "type": "Label", "props": {"y": 139, "x": 189, "width": 420, "visible": false, "var": "nameTxt_0", "text": "You", "height": 36, "fontSize": 32, "color": "#fa9300"}, "nodeParent": 167, "label": "Label(nameTxt_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": [], "$LOCKED": false}, {"x": 30, "type": "Label", "props": {"y": 306, "x": 189, "width": 420, "visible": false, "var": "nameTxt_1", "text": "You", "height": 36, "fontSize": 32, "color": "#428371"}, "nodeParent": 167, "label": "Label(nameTxt_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 143, "child": [], "$LOCKED": true, "$HIDDEN": false}, {"x": 30, "type": "Label", "props": {"y": 472, "x": 189, "width": 420, "visible": false, "var": "nameTxt_2", "text": "You", "height": 36, "fontSize": 32, "color": "#428371"}, "nodeParent": 167, "label": "Label(nameTxt_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 151, "child": [], "$LOCKED": false}, {"x": 30, "type": "Label", "props": {"y": 637, "x": 189, "width": 420, "visible": false, "var": "nameTxt_3", "text": "You", "height": 36, "fontSize": 32, "color": "#428371"}, "nodeParent": 167, "label": "Label(nameTxt_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 162, "child": [], "$LOCKED": false}, {"x": 30, "type": "Label", "props": {"y": 24, "x": 183, "width": 334, "var": "roundTxt", "valign": "middle", "text": "ROUND  1", "height": 54, "fontSize": 42, "color": "#ffffff", "centerX": -9, "align": "center"}, "nodeParent": 167, "label": "Label(roundTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 56, "child": []}, {"x": 30, "type": "Label", "props": {"y": 767, "x": 474, "width": 50, "var": "nextTimeTxt", "text": "0s", "height": 56, "fontSize": 40, "color": "#7ab8a6"}, "nodeParent": 167, "label": "Label(nextTimeTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 165, "child": []}, {"x": 30, "type": "Label", "props": {"y": 767, "x": 190, "width": 249, "var": "nextRoundTxt", "text": "Next round in", "height": 56, "fontSize": 40, "color": "#7ab8a6"}, "nodeParent": 167, "label": "Label(nextRoundTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 191, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}