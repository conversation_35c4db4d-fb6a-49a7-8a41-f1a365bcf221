{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 426, "props": {"width": 750, "top": 0, "sceneColor": "#272525", "right": 0, "renderType": "mask", "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"width": 1024, "var": "bg", "skin": "domino/BG_21000.jpg", "height": 1336, "centerY": 0, "centerX": -2}, "nodeParent": 1, "label": "Image(bg)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 278, "child": [], "$LOCKED": false}, {"x": 15, "type": "Image", "props": {"y": 667, "x": 373, "var": "furniture", "skin": "domino/furniture.png", "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image(furniture)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 421, "child": []}, {"x": 15, "type": "Image", "props": {"y": -1, "x": -139, "var": "topBg", "skin": "domino/top-bg.png", "sizeGrid": "0,0,20,0", "right": 0, "left": 0, "height": 96}, "nodeParent": 1, "label": "Image(topBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 277, "child": []}, {"x": 15, "type": "Sprite", "props": {"y": 732, "x": 368, "width": 2, "var": "deskSp", "mouseThrough": true, "mouseEnabled": true, "height": 2}, "nodeParent": 1, "label": "Sprite(deskSp)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 337, "child": []}, {"x": 15, "type": "Box", "props": {"y": 0, "x": 0, "var": "applyBox", "mouseThrough": true}, "nodeParent": 1, "label": "Box(applyBox)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 423, "child": []}, {"x": 15, "type": "Box", "props": {"y": -155, "x": -20, "visible": false, "var": "scoreBox"}, "nodeParent": 1, "label": "Box(scoreBox)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 382, "child": [{"type": "Image", "props": {"x": 34, "var": "scoreImg", "skin": "domino/10.png"}, "nodeParent": 382, "label": "Image(scoreImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 380, "child": []}, {"type": "Image", "props": {"y": 68, "var": "scoreTypeImg", "skin": "domino/good.png"}, "nodeParent": 382, "label": "Image(scoreTypeImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 381, "child": []}]}, {"x": 15, "type": "Box", "props": {"y": -106, "x": 436, "width": 139, "visible": false, "var": "passMc", "height": 84}, "nodeParent": 1, "label": "Box(passMc)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 350, "child": [{"type": "Image", "props": {"y": 41, "x": 71, "width": 142, "var": "passBg", "skin": "domino/pass.png", "scaleX": -1, "pivotY": 41, "pivotX": 71, "height": 82}, "nodeParent": 350, "label": "Image(passBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 349, "child": []}, {"type": "Label", "props": {"y": 28, "x": 0, "width": 120, "var": "passTxt", "text": "PASS", "height": 24, "fontSize": 24, "color": "#7a7a7a", "align": "center"}, "nodeParent": 350, "label": "Label(passTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 351, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 964, "x": -251, "visible": false, "var": "roundTimeBox"}, "nodeParent": 1, "label": "Box(roundTimeBox)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 407, "child": [{"type": "Label", "props": {"y": 0, "x": 0, "width": 120, "text": "+10", "height": 80, "fontSize": 60, "color": "#ffec89", "align": "right"}, "nodeParent": 407, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 410, "child": []}, {"type": "Label", "props": {"y": 12, "x": 124, "width": 48, "text": "s", "height": 51, "fontSize": 44, "color": "#ffec89", "align": "left"}, "nodeParent": 407, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 411, "child": []}]}, {"x": 15, "type": "Box", "props": {"var": "giftBox", "top": 0, "right": 0, "mouseThrough": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(giftBox)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 425, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"var": "topBox", "top": 0, "right": 0, "mouseThrough": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(topBox)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 424, "child": [], "$HIDDEN": false}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 333, "x": 273, "width": 171, "var": "testBtn", "skin": "public/btn_G.png", "labelStrokeColor": "#61f2ca", "labelStroke": 2, "labelSize": 39, "labelColors": "#08846b", "labelAlign": "center", "label": "断线重连", "height": 68, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "<PERSON><PERSON>(testBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 426, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}