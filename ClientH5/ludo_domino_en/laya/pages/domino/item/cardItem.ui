{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "props": {"y": 0, "x": 0, "width": 110, "sceneColor": "#000000", "rotation": 0, "height": 172}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 110, "skin": "domino/alphaBg.png", "height": 174}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": []}, {"x": 15, "type": "Image", "props": {"y": 15, "width": 84, "visible": false, "var": "selectBg", "skin": "domino/domino_bg_select.png", "sizeGrid": "8,8,8,8", "height": 157, "centerX": -6}, "nodeParent": 1, "label": "Image(selectBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 25, "skin": "domino/image_domino_normal.png", "centerX": -6}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 8, "child": [{"x": 30, "type": "Image", "props": {"y": 0, "x": 0, "width": 68, "visible": false, "var": "head", "skin": "domino/image_domino_number_1.png"}, "nodeParent": 8, "label": "Image(head)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 68, "x": 0, "width": 68, "visible": false, "var": "tail", "skin": "domino/image_domino_number_2.png", "height": 68}, "nodeParent": 8, "label": "Image(tail)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 0, "x": 0, "width": 68, "visible": false, "var": "maskBg", "skin": "domino/maskBg.png", "sizeGrid": "6,6,6,6", "height": 136}, "nodeParent": 8, "label": "Image(maskBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"var": "cardSkinImg"}, "nodeParent": 8, "label": "Image(cardSkinImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": [], "$HIDDEN": false}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}