{"x": 0, "type": "View", "selectedBox": 1, "props": {"x": 0, "width": 150, "sceneColor": "#000000", "height": 80}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 150, "var": "chat", "skin": "domino/chat_bubble.png", "height": 80}, "nodeParent": 1, "label": "Image(chat)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 15, "type": "Image", "props": {"y": 78, "x": 28, "width": 25, "var": "arrow", "skin": "domino/chat_bubble_arrow.png", "height": 14}, "nodeParent": 1, "label": "Image(arrow)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}