{"x": 0, "type": "View", "selectedBox": 29, "selecteID": 34, "props": {"width": 400, "sceneColor": "#5d5555", "height": 129}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 400, "skin": "domino/image_playerbg.png", "height": 129}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 15, "type": "Image", "props": {"y": 9, "x": 170, "width": 190, "skin": "domino/image_title_bg.png", "height": 51}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 12, "x": -7, "width": 49, "var": "voice", "scaleY": 1.2, "scaleX": 1.2, "mouseEnabled": false, "height": 97}, "nodeParent": 1, "label": "<PERSON><PERSON>(voice)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"x": 15, "type": "Image", "props": {"y": 3, "x": 45, "width": 115, "var": "roleSp", "height": 125}, "nodeParent": 1, "label": "Image(roleSp)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": [], "$LOCKED": true}, {"x": 15, "type": "UIView", "source": "domino/item/gameHead.ui", "props": {"y": 64, "x": 95, "var": "face", "scaleY": 0.6, "scaleX": 0.6, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>(face)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": []}, {"x": 15, "type": "Box", "props": {"y": -28, "x": 1, "visible": false, "var": "nameBox"}, "nodeParent": 1, "label": "Box(nameBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 29, "child": [{"x": 30, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 0, "x": 0, "visible": false, "var": "nameItem"}, "nodeParent": 29, "label": "UIView(nameItem)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 37, "child": []}, {"x": 30, "type": "Image", "props": {"width": 40, "visible": false, "var": "vipIcon", "scaleY": 0.6, "scaleX": 0.6, "height": 24}, "nodeParent": 29, "label": "Image(vipIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}, {"x": 30, "type": "Image", "props": {"width": 40, "visible": false, "var": "royalIcon", "scaleY": 0.6, "scaleX": 0.6, "height": 24}, "nodeParent": 29, "label": "Image(royalIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 28, "child": []}, {"x": 30, "type": "Label", "props": {"y": 0, "x": 4, "width": 158, "visible": false, "var": "nameTxt", "text": "11111111", "height": 28, "fontSize": 20, "color": "#FFE416", "align": "center"}, "nodeParent": 29, "label": "Label(nameTxt)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 34, "child": [], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 15, "type": "Label", "props": {"y": 16, "x": 288, "width": 70, "var": "scoreTxt", "text": "0", "height": 34, "fontSize": 34, "color": "#ffe461"}, "nodeParent": 1, "label": "Label(scoreTxt)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"x": 15, "type": "Label", "props": {"y": 24, "x": 205, "width": 70, "var": "title", "text": "Score", "height": 19, "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 1, "label": "Label(title)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}