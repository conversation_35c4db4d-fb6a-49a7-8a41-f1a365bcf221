{"x": 0, "type": "View", "selectedBox": 30, "selecteID": 27, "props": {"width": 124, "sceneColor": "#5d5555", "height": 143}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 124, "skin": "domino/image_playerbg.png", "height": 143}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 15, "type": "Image", "props": {"y": 91, "x": 9, "width": 108, "skin": "domino/image_title_bg.png", "height": 48}, "nodeParent": 1, "label": "Image", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 6, "x": -5, "width": 61, "var": "voice", "scaleY": 0.8, "scaleX": 0.8, "height": 101}, "nodeParent": 1, "label": "<PERSON><PERSON>(voice)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"x": 15, "type": "Image", "props": {"y": -6, "x": 46, "width": 72, "var": "roleSp", "height": 106}, "nodeParent": 1, "label": "Image(roleSp)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": [], "$LOCKED": true}, {"x": 15, "type": "UIView", "source": "domino/item/gameHead.ui", "props": {"y": 48, "x": 80, "var": "face", "scaleY": 0.538, "scaleX": 0.538, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>(face)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": []}, {"x": 15, "type": "Box", "props": {"y": -20, "x": 1, "var": "nameBox"}, "nodeParent": 1, "label": "Box(nameBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 30, "child": [{"x": 30, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": -3, "x": 0, "visible": false, "var": "nameItem"}, "nodeParent": 30, "label": "UIView(nameItem)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 31, "child": []}, {"x": 30, "type": "Image", "props": {"y": 3, "x": 1, "width": 32, "visible": false, "var": "vipIcon", "scaleY": 0.45, "scaleX": 0.45, "height": 19}, "nodeParent": 30, "label": "Image(vipIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}, {"x": 30, "type": "Image", "props": {"y": 3, "x": 1, "width": 32, "visible": false, "var": "royalIcon", "scaleY": 0.45, "scaleX": 0.45, "height": 19}, "nodeParent": 30, "label": "Image(royalIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}, {"x": 30, "type": "Label", "props": {"y": 0, "x": 0, "width": 122, "visible": false, "var": "nameTxt", "text": "11111111", "height": 20, "fontSize": 16, "color": "#ffffff", "align": "center"}, "nodeParent": 30, "label": "Label(nameTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}]}, {"x": 15, "type": "Label", "props": {"y": 105, "x": 11, "width": 107, "var": "scoreTxt", "text": "0", "height": 34, "fontSize": 34, "color": "#ffe461", "align": "center"}, "nodeParent": 1, "label": "Label(scoreTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"x": 15, "type": "Label", "props": {"y": 93, "x": 27, "width": 69, "var": "title", "text": "Score", "height": 17, "fontSize": 16, "color": "#FFFFFF", "align": "center"}, "nodeParent": 1, "label": "Label(title)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}