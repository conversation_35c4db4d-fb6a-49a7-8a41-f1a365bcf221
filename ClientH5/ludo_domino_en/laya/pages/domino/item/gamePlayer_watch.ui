{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 17, "props": {"width": 166, "sceneColor": "#5d5555", "height": 100}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 166, "skin": "domino/image_playerbg.png", "height": 89}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 15, "type": "Image", "props": {"y": 8, "x": 89, "width": 66, "skin": "domino/image_title_bg.png", "height": 73}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 6, "x": 70, "width": 61, "visible": false, "var": "voice", "scaleY": 0.8, "scaleX": 0.8, "mouseEnabled": true, "height": 110}, "nodeParent": 1, "label": "<PERSON><PERSON>(voice)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": []}, {"x": 15, "type": "Image", "props": {"y": 1, "x": 1, "width": 87, "var": "roleSp", "height": 96}, "nodeParent": 1, "label": "Image(roleSp)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 36, "child": []}, {"x": 15, "type": "UIView", "source": "domino/item/gameHead.ui", "props": {"y": 44, "x": 44, "var": "face", "scaleY": 0.538, "scaleX": 0.538, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>(face)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 35, "child": []}, {"x": 15, "type": "Box", "props": {"y": -20, "x": 2, "var": "nameBox"}, "nodeParent": 1, "label": "Box(nameBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 39, "child": [{"x": 30, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": -3, "x": 0, "visible": false, "var": "nameItem"}, "nodeParent": 39, "label": "UIView(nameItem)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 40, "child": []}, {"x": 30, "type": "Image", "props": {"y": 3, "width": 22, "visible": false, "var": "vipIcon", "scaleY": 0.45, "scaleX": 0.45, "height": 18}, "nodeParent": 39, "label": "Image(vipIcon)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 37, "child": []}, {"x": 30, "type": "Image", "props": {"y": 3, "width": 22, "visible": false, "var": "royalIcon", "scaleY": 0.45, "scaleX": 0.45, "height": 18}, "nodeParent": 39, "label": "Image(royalIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 38, "child": []}, {"x": 30, "type": "Label", "props": {"y": 0, "x": 6, "width": 124, "var": "nameTxt", "height": 20, "fontSize": 16, "color": "#ffffff", "align": "center"}, "nodeParent": 39, "label": "Label(nameTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}]}, {"x": 15, "type": "Label", "props": {"y": 41, "x": 90, "width": 65, "var": "scoreTxt", "text": "0", "height": 34, "fontSize": 34, "color": "#ffe461", "align": "center"}, "nodeParent": 1, "label": "Label(scoreTxt)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"x": 15, "type": "Label", "props": {"y": 15, "x": 90, "width": 64, "var": "title", "text": "Score", "height": 23, "fontSize": 16, "color": "#FFFFFF", "align": "center"}, "nodeParent": 1, "label": "Label(title)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}