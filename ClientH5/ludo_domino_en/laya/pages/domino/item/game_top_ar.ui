{"x": 0, "type": "View", "selectedBox": 9, "selecteID": 11, "props": {"width": 750, "sceneColor": "#000000", "right": 0, "mouseThrough": true, "left": 0, "height": 200}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "HBox", "props": {"y": 0, "x": 0, "var": "topMc", "top": 0, "right": 0, "mouseThrough": true, "left": 0, "height": 93, "cacheAs": "normal"}, "nodeParent": 1, "label": "HBox(topMc)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"var": "winBtn", "top": 4, "stateNum": "1", "skin": "domino/btn_reward.png", "right": 110}, "nodeParent": 2, "label": "Button(winBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 30, "type": "Image", "props": {"width": 80, "top": 6, "skin": "domino/alphaBg.png", "right": 14, "height": 80}, "nodeParent": 2, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 0, "var": "settingBtn", "stateNum": "1", "skin": "domino/system.png"}, "nodeParent": 4, "label": "Button(settingBtn)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}]}, {"x": 30, "type": "Image", "props": {"width": 242, "var": "titleImg", "top": 18, "skin": "domino/image_title_bg.png", "height": 46, "centerX": 58}, "nodeParent": 2, "label": "Image(titleImg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"x": 45, "type": "Text", "props": {"y": 2, "x": 16, "width": 160, "var": "titleTxt", "valign": "middle", "height": 40, "fontSize": 22, "color": "#ffffff", "align": "center"}, "nodeParent": 6, "label": "Text(titleTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}, {"x": 45, "type": "Text", "props": {"y": 4, "x": 167, "width": 67, "var": "titleNumTxt", "valign": "middle", "height": 40, "fontSize": 30, "color": "#fff95b", "align": "left"}, "nodeParent": 6, "label": "Text(titleNumTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}]}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 18, "width": 262, "visible": false, "var": "titleSpectator", "stateNum": "1", "skin": "domino/image_title_bg.png", "height": 48, "centerX": 24}, "nodeParent": 2, "label": "<PERSON><PERSON>(titleSpectator)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 40, "child": [{"x": 45, "type": "Image", "props": {"y": 15, "x": 30, "skin": "domino/Audience.png"}, "nodeParent": 40, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 41, "child": []}, {"x": 45, "type": "Label", "props": {"y": 15, "x": 82, "width": 90, "valign": "middle", "text": "Spectator", "height": 20, "fontSize": 18, "color": "#ffffff", "align": "center"}, "nodeParent": 40, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 42, "child": []}, {"x": 45, "type": "Label", "props": {"y": 9, "x": 185, "width": 52, "var": "audience_num", "valign": "middle", "text": "0", "height": 31, "fontSize": 24, "color": "#ffffff", "align": "center"}, "nodeParent": 40, "label": "Label(audience_num)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 43, "child": []}], "$HIDDEN": true}, {"x": 30, "type": "HBox", "props": {"var": "infoMc", "top": 6, "space": 12, "left": 6}, "nodeParent": 2, "label": "HBox(infoMc)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 62, "child": [{"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 0, "visible": false, "var": "store_btn", "stateNum": "1", "skin": "game/btn_store.png", "labelSize": 24, "labelColors": "#ffffff"}, "nodeParent": 62, "label": "Button(store_btn)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 61, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Image", "props": {"y": 4, "x": 12, "width": 90, "var": "roundMc", "skin": "domino/image_title_bg.png", "height": 66}, "nodeParent": 62, "label": "Image(roundMc)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 57, "child": [{"x": 60, "type": "Label", "props": {"y": 8, "x": 0, "width": 90, "text": "Round", "height": 27, "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 57, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 59, "child": []}, {"x": 60, "type": "Text", "props": {"y": 29, "x": 0, "width": 90, "var": "roundNumTxt", "valign": "middle", "text": "1", "height": 33, "fontSize": 32, "color": "#fff95b", "align": "center"}, "nodeParent": 57, "label": "Text(roundNumTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 58, "child": []}]}, {"x": 45, "type": "Image", "props": {"y": 4, "x": 138, "width": 90, "visible": false, "var": "stockMc", "skin": "domino/image_title_bg.png", "height": 66}, "nodeParent": 62, "label": "Image(stockMc)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 9, "child": [{"x": 60, "type": "Label", "props": {"y": 8, "x": 0, "width": 90, "var": "stockImg", "text": "Stock", "height": 27, "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 9, "label": "Label(stockImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}, {"x": 60, "type": "Text", "props": {"y": 29, "x": 0, "width": 90, "var": "fillNumTxt", "valign": "middle", "text": "14", "height": 26, "fontSize": 32, "color": "#fff95b", "align": "center"}, "nodeParent": 9, "label": "Text(fillNumTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}]}]}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}