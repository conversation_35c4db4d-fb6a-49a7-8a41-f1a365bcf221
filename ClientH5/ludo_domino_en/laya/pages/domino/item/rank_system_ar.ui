{"x": 0, "type": "View", "selectedBox": 31, "selecteID": 33, "referenceLines": null, "props": {"width": 750, "sceneColor": "#000000", "mouseThrough": true, "height": 200}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"width": 472, "visible": false, "var": "rankMc", "top": 90, "skin": "domino/BG_Expression.png", "right": 86, "mouseEnabled": false, "height": 68}, "nodeParent": 1, "label": "Image(rankMc)", "isOpen": null, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 12, "child": [{"type": "Image", "props": {"y": -8, "x": 399, "width": 28, "skin": "domino/BG_chat_arrow.png", "height": 15}, "nodeParent": 12, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": []}, {"type": "Image", "props": {"y": 73, "width": 436, "visible": false, "var": "rank1", "skin": "domino/table_words.png", "height": 53, "centerX": 2}, "nodeParent": 12, "label": "Image(rank1)", "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 19, "child": [{"type": "Image", "props": {"y": -11, "x": 349, "var": "rankImg1", "skin": "domino/RANK1.png"}, "nodeParent": 19, "label": "Image(rankImg1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"type": "Label", "props": {"y": 7, "x": 30, "width": 149, "var": "rank_1", "height": 38, "fontSize": 36, "color": "#ffffff", "align": "center"}, "nodeParent": 19, "label": "Label(rank_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}, {"type": "Label", "props": {"y": 9, "x": 28, "width": 305, "var": "rankImgTxt1", "text": "RANK 1", "height": 38, "fontSize": 31, "color": "#fff690", "bold": true, "align": "right"}, "nodeParent": 19, "label": "Label(rankImgTxt1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}]}, {"type": "Box", "props": {"y": 17, "x": 8, "width": 474, "visible": false, "var": "league", "height": 47}, "nodeParent": 12, "label": "Box(league)", "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 47, "child": [{"type": "Image", "props": {"y": 0, "x": 8, "width": 250, "skin": "domino/table_words.png", "height": 40}, "nodeParent": 47, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 48, "child": []}, {"type": "Image", "props": {"y": 0, "x": 272, "width": 180, "skin": "domino/table_words.png", "height": 40}, "nodeParent": 47, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 49, "child": []}, {"type": "Label", "props": {"y": 10, "x": 21, "width": 136, "var": "roomIdTxt", "height": 22, "fontSize": 18, "color": "#f3f3f3", "align": "center"}, "nodeParent": 47, "label": "Label(roomIdTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 50, "child": []}, {"type": "Label", "props": {"y": 10, "x": 138, "width": 99, "text": "ROOM ID:", "height": 21, "fontSize": 18, "color": "#fff692", "bold": true, "align": "right"}, "nodeParent": 47, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 51, "child": []}, {"type": "Label", "props": {"y": 10, "x": 338, "width": 94, "var": "league_groupTitle_txt", "text": "Group No.", "height": 21, "fontSize": 18, "color": "#fff692", "bold": true, "align": "right"}, "nodeParent": 47, "label": "Label(league_groupTitle_txt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 44, "child": []}, {"type": "Label", "props": {"y": 10, "x": 288, "width": 42, "var": "league_groupNum_txt", "text": 999, "height": 29, "fontSize": 18, "color": "#f3f3f3", "align": "center"}, "nodeParent": 47, "label": "Label(league_groupNum_txt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 45, "child": []}]}, {"type": "Box", "props": {"y": 18, "x": 10, "var": "commonRank"}, "nodeParent": 12, "label": "Box(commonRank)", "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 56, "child": [{"type": "Image", "props": {"width": 306, "skin": "domino/table_words.png", "height": 36}, "nodeParent": 56, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}, {"type": "Image", "props": {"x": 310, "width": 144, "skin": "domino/table_words.png", "height": 36}, "nodeParent": 56, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"type": "Label", "props": {"y": 9, "x": 13, "width": 180, "var": "rank_roomId", "height": 22, "fontSize": 19, "color": "#f3f3f3", "align": "center"}, "nodeParent": 56, "label": "Label(rank_roomId)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": []}, {"type": "Label", "props": {"y": 10, "x": 182, "width": 100, "var": "roomIDImg", "text": "ROOM ID:", "height": 21, "fontSize": 18, "color": "#fff692", "bold": true, "align": "right"}, "nodeParent": 56, "label": "Label(roomIDImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}, {"type": "Label", "props": {"y": 7, "x": 312, "width": 138, "var": "typeTxt", "height": 29, "fontSize": 20, "color": "#f3f3f3", "align": "center"}, "nodeParent": 56, "label": "Label(typeTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}]}]}, {"x": 15, "type": "Image", "props": {"width": 728, "visible": false, "var": "set", "top": 93, "skin": "domino/BG_Expression.png", "sizeGrid": "15,15,15,15", "right": 17, "height": 125}, "nodeParent": 1, "label": "Image(set)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 23, "child": [{"x": 30, "type": "Image", "props": {"y": -11, "skin": "domino/BG_chat_arrow.png", "right": 34}, "nodeParent": 23, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 13, "x": 298, "width": 132, "var": "ruleBtn", "stateNum": 1, "skin": "domino/table_words.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "<PERSON><PERSON>(ruleBtn)", "isOpen": null, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 25, "child": [{"type": "Image", "props": {"y": 12, "skin": "domino/rule.png", "centerX": 0}, "nodeParent": 25, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": []}, {"type": "Label", "props": {"y": 66, "width": 120, "text": "Rules", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 0, "align": "center"}, "nodeParent": 25, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}]}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 13, "x": 12, "width": 132, "var": "exitBtn", "stateNum": 1, "skin": "domino/table_words.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "Button(exitBtn)", "isOpen": null, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 28, "child": [{"type": "Image", "props": {"y": 12, "skin": "domino/exit.png", "centerX": 0}, "nodeParent": 28, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}, {"type": "Label", "props": {"y": 66, "width": 120, "text": "Exit", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 0, "align": "center"}, "nodeParent": 28, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 30, "child": []}]}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 13, "x": 584, "width": 132, "var": "soundBtn", "stateNum": 1, "skin": "domino/table_words.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "Button(soundBtn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 31, "child": [{"x": 45, "type": "Image", "props": {"y": 12, "var": "soundIcon", "skin": "domino/soundON.png", "centerX": 0}, "nodeParent": 31, "label": "Image(soundIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": []}, {"x": 45, "type": "Label", "props": {"y": 66, "width": 120, "var": "soundTxt", "text": "ON", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 1, "align": "center"}, "nodeParent": 31, "label": "Label(soundTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 33, "child": []}]}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 13, "x": 155, "width": 132, "var": "chatSwitchBtn", "stateNum": 1, "skin": "domino/table_words.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "Button(chatSwitchBtn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 34, "child": [{"x": 45, "type": "Image", "props": {"y": 12, "var": "switchIcon", "skin": "domino/ChatON.png", "centerX": 0}, "nodeParent": 34, "label": "Image(switchIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 35, "child": []}, {"x": 45, "type": "Label", "props": {"y": 66, "width": 206, "var": "switchTxt", "text": "Chat ON", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 0, "align": "center"}, "nodeParent": 34, "label": "Label(switchTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 36, "child": []}]}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 13, "x": 441, "width": 132, "var": "musicBtn", "stateNum": 1, "skin": "domino/table_words.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "<PERSON><PERSON>(musicBtn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 37, "child": [{"x": 45, "type": "Image", "props": {"y": 12, "var": "musicIcon", "skin": "domino/musicON.png", "centerX": 0}, "nodeParent": 37, "label": "Image(musicIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 38, "child": []}, {"x": 45, "type": "Label", "props": {"y": 66, "width": 120, "var": "musicTxt", "text": "ON", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 1, "align": "center"}, "nodeParent": 37, "label": "Label(musicTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 39, "child": []}]}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}