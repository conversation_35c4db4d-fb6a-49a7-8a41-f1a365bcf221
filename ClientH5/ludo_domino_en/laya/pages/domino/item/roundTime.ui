{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "referenceLines": null, "props": {"width": 420, "sceneColor": "#000000", "mouseThrough": true, "height": 166}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 420, "visible": false, "var": "roundTime_add_View", "skin": "domino/BG_Expression.png", "height": 166}, "nodeParent": 1, "label": "Image(roundTime_add_View)", "isOpen": null, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 28, "child": [{"type": "Image", "props": {"y": 37, "width": 390, "skin": "domino/table_ROOM.png", "height": 88, "centerX": 0}, "nodeParent": 28, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}, {"type": "Image", "props": {"y": 163, "x": 128, "skin": "domino/BG_Expression_V.png"}, "nodeParent": 28, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 30, "child": []}, {"type": "CheckBox", "props": {"y": 111, "x": 336, "width": 90, "var": "addTimeCheckBox", "stateNum": 2, "skin": "domino/extendon.png", "sizeGrid": "-16,0,0,-16", "selected": false, "height": 80}, "nodeParent": 28, "label": "CheckBox(addTimeCheckBox)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 31, "child": []}, {"type": "Label", "props": {"y": 0, "width": 428, "valign": "middle", "text": "Extend Round", "strokeColor": "#327d65", "stroke": 2, "height": 37, "fontSize": 24, "color": "#ffffff", "centerX": 0, "bold": true, "align": "center"}, "nodeParent": 28, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": []}, {"type": "Label", "props": {"y": 42, "wordWrap": true, "width": 353, "valign": "middle", "text": "You can spend diamonds extending the round by 10 seconds in your turn.", "height": 78, "fontSize": 18, "color": "#f3f3f3", "centerX": 0, "align": "center"}, "nodeParent": 28, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 33, "child": []}, {"type": "Label", "props": {"y": 136, "x": 354, "width": 85, "var": "extendOnTxt", "text": "Extend", "height": 22, "fontSize": 16, "color": "#fff690", "anchorX": 1, "align": "right"}, "nodeParent": 28, "label": "Label(extendOnTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 34, "child": []}, {"type": "Label", "props": {"y": 122, "x": 118, "width": 44, "var": "addTimeCount", "valign": "middle", "strokeColor": "#327d65", "stroke": 3, "height": 37, "fontSize": 28, "color": "#ffffff", "align": "left"}, "nodeParent": 28, "label": "Label(addTimeCount)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 35, "child": []}, {"type": "Label", "props": {"y": 136, "x": 26, "width": 80, "text": "Remaining", "height": 22, "fontSize": 16, "color": "#fff690", "align": "left"}, "nodeParent": 28, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 36, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}