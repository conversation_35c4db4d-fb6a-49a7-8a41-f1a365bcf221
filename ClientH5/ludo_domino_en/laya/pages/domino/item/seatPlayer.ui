{"x": 0, "type": "View", "selectedBox": 5, "selecteID": 62, "referenceLines": null, "props": {"width": 750, "sceneColor": "#000000", "mouseThrough": true, "height": 1334}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"y": 10, "x": 10, "width": 326, "visible": false, "var": "cardSp_2", "top": 90, "name": 2, "mouseThrough": true, "height": 112, "centerX": 0}, "nodeParent": 1, "label": "Box(cardSp_2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Image", "props": {"width": 330, "top": 2, "skin": "domino/domino_bg_select_0.png", "sizeGrid": "12,12,12,12", "right": 3, "name": "cardList", "height": 57, "anchorX": 1}, "nodeParent": 2, "label": "Image(cardList)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}, {"x": 30, "type": "UIView", "source": "domino/item/gamePlayer_up.ui", "props": {"top": 23, "right": -204, "name": "player"}, "nodeParent": 2, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>(player)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}]}, {"x": 15, "type": "Box", "props": {"y": 10, "x": 10, "width": 120, "visible": false, "var": "cardSp_3", "name": 3, "mouseThrough": true, "left": 0, "height": 584, "centerY": -2}, "nodeParent": 1, "label": "Box(cardSp_3)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"x": 30, "type": "Image", "props": {"y": 157, "x": 1, "width": 330, "top": 157, "skin": "domino/domino_bg_select_0.png", "sizeGrid": "12,12,12,12", "rotation": -90, "right": 119, "name": "cardList", "height": 57, "anchorX": 1}, "nodeParent": 3, "label": "Image(cardList)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 30, "type": "UIView", "source": "domino/item/gamePlayer_horiz.ui", "props": {"top": 6, "name": "player", "left": 4}, "nodeParent": 3, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>(player)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}]}, {"x": 15, "type": "Box", "props": {"y": 10, "x": 10, "width": 120, "visible": false, "var": "cardSp_1", "right": 0, "name": 1, "mouseThrough": true, "height": 584, "centerY": 0}, "nodeParent": 1, "label": "Box(cardSp_1)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"type": "Image", "props": {"width": 330, "top": 423, "skin": "domino/domino_bg_select_0.png", "sizeGrid": "12,12,12,12", "rotation": 90, "right": 1, "name": "cardList", "height": 57, "anchorX": 1}, "nodeParent": 4, "label": "Image(cardList)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"type": "UIView", "source": "domino/item/gamePlayer_right.ui", "props": {"y": 448, "x": -4, "name": "player"}, "nodeParent": 4, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>(player)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}]}, {"x": 15, "type": "Box", "props": {"y": 10, "x": 10, "width": 750, "visible": false, "var": "cardSp_0", "right": 0, "name": 0, "mouseThrough": true, "left": 0, "height": 298, "bottom": 0}, "nodeParent": 1, "label": "Box(cardSp_0)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 5, "child": [{"x": 30, "type": "Box", "props": {"width": 750, "name": "cardList", "height": 180, "centerX": 0, "bottom": 0}, "nodeParent": 5, "label": "Box(cardList)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": []}, {"x": 30, "type": "UIView", "source": "domino/item/gamePlayer_down.ui", "props": {"top": -14, "name": "player", "left": 8}, "nodeParent": 5, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>(player)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}, {"x": 30, "type": "Box", "props": {"y": 51, "x": 152, "var": "btnBox"}, "nodeParent": 5, "label": "Box(btnBox)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 50, "child": [{"type": "<PERSON><PERSON>", "props": {"width": 58, "var": "voiceBtn", "stateNum": 1, "skin": "domino/btn_nospeak.png", "height": 56}, "nodeParent": 50, "label": "<PERSON><PERSON>(voiceBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"type": "<PERSON><PERSON>", "props": {"y": 1, "x": 64, "width": 58, "var": "emojiBtn", "stateNum": 2, "skin": "domino/btn_Expression.png", "name": "emoji", "height": 56}, "nodeParent": 50, "label": "Button(emojiBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}, {"type": "<PERSON><PERSON>", "props": {"y": 1, "x": 191, "width": 58, "visible": false, "var": "friendBtn", "stateNum": 2, "skin": "domino/btn_friendChat.png", "name": "friend", "height": 56}, "nodeParent": 50, "label": "<PERSON><PERSON>(friendBtn)", "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 43, "child": [{"type": "<PERSON><PERSON>", "props": {"y": -12, "x": 30, "visible": false, "var": "friend_toBeR<PERSON>", "stateNum": 1, "skin": "domino/icon_mailpoint.png", "labelSize": 16, "labelPadding": 0, "labelColors": "#ffffff", "labelBold": true, "labelAlign": "center", "label": "99+"}, "nodeParent": 43, "label": "<PERSON><PERSON>(friend_to<PERSON><PERSON><PERSON><PERSON>)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 52, "child": []}]}, {"type": "Sprite", "props": {"y": 1, "x": 129, "width": 58, "var": "chatSp", "mouseEnabled": true, "height": 56}, "nodeParent": 50, "label": "Sprite(chatSp)", "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 19, "child": [{"type": "<PERSON><PERSON>", "props": {"width": 58, "var": "chatBtn", "stateNum": 2, "skin": "domino/btn_chat.png", "height": 56}, "nodeParent": 19, "label": "Button(chatBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"type": "<PERSON><PERSON>", "props": {"y": -12, "x": 30, "visible": false, "var": "toBeRead", "stateNum": 1, "skin": "domino/icon_mailpoint.png", "labelSize": 16, "labelPadding": 0, "labelColors": "#ffffff", "labelBold": true, "labelAlign": "center", "label": "99+"}, "nodeParent": 19, "label": "Button(toBeRead)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 51, "child": []}]}]}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 23, "x": 430, "width": 70, "visible": false, "var": "addTimeBtn", "stateNum": 1, "skin": "domino/alphaBg.png", "height": 82}, "nodeParent": 5, "label": "Button(addTimeBtn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 23, "child": [{"type": "Image", "props": {"y": 0, "x": 4, "var": "clockImg", "skin": "domino/clock_on.png"}, "nodeParent": 23, "label": "Image(clockImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": []}, {"type": "Image", "props": {"y": 58, "x": 8, "width": 42, "skin": "domino/bottom.png", "sizeGrid": "16,16,16,16", "height": 16}, "nodeParent": 23, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": []}, {"type": "Image", "props": {"y": 60, "x": 35, "width": 12, "visible": false, "var": "coinImg", "skin": "domino/diamond.png", "height": 13}, "nodeParent": 23, "label": "Image(coinImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": []}, {"type": "Label", "props": {"y": 57, "x": 8, "width": 30, "var": "addTimeGoldTxt", "valign": "middle", "height": 18, "fontSize": 13, "color": "#ffffff", "bold": true, "align": "center"}, "nodeParent": 23, "label": "Label(addTimeGoldTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": -160, "x": 316, "width": 420, "visible": false, "var": "roundTime_add_View", "skin": "domino/BG_Expression.png", "height": 166}, "nodeParent": 5, "label": "Image(roundTime_add_View)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 28, "child": [{"type": "Image", "props": {"y": 37, "width": 390, "skin": "domino/table_ROOM.png", "height": 88, "centerX": 0}, "nodeParent": 28, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}, {"type": "Image", "props": {"y": 163, "x": 128, "skin": "domino/BG_Expression_V.png"}, "nodeParent": 28, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 30, "child": []}, {"type": "CheckBox", "props": {"y": 111, "x": 336, "width": 90, "var": "addTimeCheckBox", "stateNum": 2, "skin": "domino/extendon.png", "sizeGrid": "-16,0,0,-16", "selected": false, "height": 80}, "nodeParent": 28, "label": "CheckBox(addTimeCheckBox)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 31, "child": []}, {"type": "Label", "props": {"y": 0, "width": 428, "valign": "middle", "text": "Extend Round", "strokeColor": "#327d65", "stroke": 2, "height": 37, "fontSize": 24, "color": "#ffffff", "centerX": 0, "bold": true, "align": "center"}, "nodeParent": 28, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": []}, {"type": "Label", "props": {"y": 42, "wordWrap": true, "width": 353, "valign": "middle", "text": "You can spend diamonds extending the round by 10 seconds in your turn.", "height": 78, "fontSize": 18, "color": "#f3f3f3", "centerX": 0, "align": "center"}, "nodeParent": 28, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 33, "child": []}, {"type": "Label", "props": {"y": 136, "x": 354, "width": 85, "var": "extendOnTxt", "text": "Extend", "height": 22, "fontSize": 16, "color": "#fff690", "anchorX": 1, "align": "right"}, "nodeParent": 28, "label": "Label(extendOnTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 34, "child": []}, {"type": "Label", "props": {"y": 122, "x": 118, "width": 44, "var": "addTimeCount", "valign": "middle", "strokeColor": "#327d65", "stroke": 3, "height": 37, "fontSize": 28, "color": "#ffffff", "align": "left"}, "nodeParent": 28, "label": "Label(addTimeCount)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 35, "child": []}, {"type": "Label", "props": {"y": 136, "x": 26, "width": 80, "text": "Remaining", "height": 22, "fontSize": 16, "color": "#fff690", "align": "left"}, "nodeParent": 28, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 36, "child": []}]}, {"x": 30, "type": "Box", "props": {"y": -160, "x": 316, "var": "roundTime_placehold_box"}, "nodeParent": 5, "label": "Box(roundTime_placehold_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 62, "child": []}, {"x": 30, "type": "Image", "props": {"y": -57, "x": 26, "visible": false, "var": "trusteeship", "skin": "domino/auto.png"}, "nodeParent": 5, "label": "Image(trusteeship)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 38, "child": [{"type": "Label", "props": {"y": 7, "x": 8, "width": 68, "text": "AUTO", "strokeColor": "#000000", "stroke": 2, "height": 24, "fontSize": 22, "color": "#ffffff", "bold": true, "align": "center"}, "nodeParent": 38, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 39, "child": []}, {"type": "CheckBox", "props": {"y": 3, "x": 81, "width": 50, "var": "trust", "stateNum": 2, "skin": "domino/check_auto.png", "height": 33}, "nodeParent": 38, "label": "CheckBox(trust)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 40, "child": []}, {"type": "Animation", "props": {"y": 0, "x": 67, "var": "trust_jump", "source": "publics/ani/jump.ani"}, "nodeParent": 38, "label": "Animation(trust_jump)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 41, "child": []}, {"type": "Animation", "props": {"y": 18, "x": 96, "visible": false, "var": "trust_wait", "source": "publics/ani/relogin.ani", "scaleY": 0.3, "scaleX": 0.3}, "nodeParent": 38, "label": "Animation(trust_wait)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 42, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": -183, "x": 330, "width": 390, "visible": false, "var": "quick_buy_view", "skin": "public/Recharge_label.png", "height": 197}, "nodeParent": 5, "label": "Image(quick_buy_view)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 37, "child": [{"type": "Image", "props": {"y": 190, "x": 117, "skin": "public/Recharge_label_N.png"}, "nodeParent": 37, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 44, "child": []}]}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}