{"x": 0, "type": "View", "selectedBox": 5, "selecteID": 22, "props": {"width": 750, "sceneColor": "#000000", "mouseThrough": true, "height": 1334}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"width": 531, "visible": false, "var": "cardSp_2", "top": 98, "name": "2", "mouseThrough": true, "left": 20, "height": 112}, "nodeParent": 1, "label": "Box(cardSp_2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Image", "props": {"width": 330, "top": 2, "skin": "domino/domino_bg_select_0.png", "sizeGrid": "12,12,12,12", "right": 11, "name": "cardList", "height": 100, "anchorX": 1}, "nodeParent": 2, "label": "Image(cardList)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}, {"x": 30, "type": "UIView", "source": "domino/item/gamePlayer_watch.ui", "props": {"y": 16, "x": 7, "name": "player"}, "nodeParent": 2, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>(player)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 41, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"width": 120, "visible": false, "var": "cardSp_3", "name": "3", "mouseThrough": true, "left": 0, "height": 584, "centerY": 0}, "nodeParent": 1, "label": "Box(cardSp_3)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"x": 30, "type": "Image", "props": {"y": 157, "x": 1, "width": 330, "top": 157, "skin": "domino/domino_bg_select_0.png", "sizeGrid": "12,12,12,12", "rotation": -90, "right": 119, "name": "cardList", "height": 100, "anchorX": 1}, "nodeParent": 3, "label": "Image(cardList)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 30, "type": "UIView", "source": "domino/item/gamePlayer_watch_horiz.ui", "props": {"y": 0, "x": 0, "name": "player"}, "nodeParent": 3, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>(player)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 43, "child": []}]}, {"x": 15, "type": "Box", "props": {"width": 120, "visible": false, "var": "cardSp_1", "right": 0, "name": "1", "mouseThrough": true, "height": 584, "centerY": 0}, "nodeParent": 1, "label": "Box(cardSp_1)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 30, "type": "Image", "props": {"width": 330, "top": 423, "skin": "domino/domino_bg_select_0.png", "sizeGrid": "12,12,12,12", "rotation": 90, "right": 1, "name": "cardList", "height": 100, "anchorX": 1}, "nodeParent": 4, "label": "Image(cardList)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 30, "type": "UIView", "source": "domino/item/gamePlayer_watch_horiz.ui", "props": {"y": 449, "right": 1, "name": "player"}, "nodeParent": 4, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>(player)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 44, "child": []}]}, {"x": 15, "type": "Box", "props": {"width": 750, "visible": false, "var": "cardSp_0", "right": 0, "name": "0", "mouseThrough": true, "left": 0, "height": 138, "bottom": 70}, "nodeParent": 1, "label": "Box(cardSp_0)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 5, "child": [{"x": 30, "type": "Image", "props": {"width": 330, "top": 2, "skin": "domino/domino_bg_select_0.png", "sizeGrid": "12,12,12,12", "right": 204, "name": "cardList", "height": 100, "anchorX": 1}, "nodeParent": 5, "label": "Image(cardList)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 38, "child": []}, {"x": 30, "type": "UIView", "source": "domino/item/gamePlayer_watch.ui", "props": {"y": 16, "right": 20, "name": "player"}, "nodeParent": 5, "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>(player)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 42, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}