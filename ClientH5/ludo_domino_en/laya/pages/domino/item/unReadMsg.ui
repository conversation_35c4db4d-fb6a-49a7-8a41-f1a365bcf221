{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 2, "props": {"width": 36, "sceneColor": "#000000", "mouseThrough": true, "height": 36}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 0, "var": "unReadBtn", "stateNum": "1", "skin": "domino/icon_mailpoint.png", "labelSize": 16, "labelPadding": "0", "labelColors": "#ffffff", "labelBold": true, "labelAlign": "center", "label": "99+"}, "nodeParent": 1, "label": "Button(unReadBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}