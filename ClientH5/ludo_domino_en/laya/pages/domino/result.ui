{"x": 0, "type": "View", "selectedBox": 90, "selecteID": 59, "props": {"y": 0, "x": 0, "width": 750, "top": 0, "sceneWidth": 750, "sceneHeight": 1334, "sceneColor": "#000000", "right": 0, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"top": 0, "skin": "public/alphaBg80.png", "sizeGrid": "1,1,1,1", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 63, "child": []}, {"x": 15, "type": "Box", "props": {"width": 694, "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Box", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 33, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 1029, "x": 525, "width": 297, "var": "again", "skin": "public/btn_y.png", "labelStrokeColor": "#fff08b", "labelStroke": 2, "labelSize": 39, "labelColors": "#ab6e0f", "labelAlign": "center", "label": "Play Again", "height": 115, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 33, "label": "<PERSON><PERSON>(again)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 1029, "x": 169, "width": 297, "var": "back", "skin": "public/btn_G.png", "labelStrokeColor": "#61f2ca", "labelStroke": 2, "labelSize": 39, "labelColors": "#08846b", "labelAlign": "center", "label": "Back", "height": 115, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 33, "label": "<PERSON><PERSON>(back)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}, {"x": 30, "type": "Image", "props": {"y": 336, "x": 0, "width": 694, "visible": false, "var": "info_0", "skin": "public/BG_1ST.png", "height": 154}, "nodeParent": 33, "label": "Image(info_0)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 45, "type": "Image", "props": {"y": -17, "x": -18, "width": 730, "visible": false, "var": "my_0", "skin": "public/bg_oneself.png", "sizeGrid": "30,30,30,30", "height": 190}, "nodeParent": 4, "label": "Image(my_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 84, "child": []}, {"x": 45, "type": "Image", "props": {"x": 12, "skin": "public/1.png", "centerY": 0}, "nodeParent": 4, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 64, "child": []}, {"x": 45, "type": "Box", "props": {"y": 21, "x": 494, "visible": false, "var": "goldExp"}, "nodeParent": 4, "label": "Box(goldExp)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 90, "child": [{"x": 60, "type": "Image", "props": {"y": 67, "x": 23, "width": 168, "var": "expBg", "skin": "public/bg_coinyellow.png", "height": 44}, "nodeParent": 90, "label": "Image(expBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 73, "child": []}, {"x": 60, "type": "Image", "props": {"y": 63, "x": 17, "width": 44, "var": "expIcon", "skin": "public/EXP.png", "height": 53}, "nodeParent": 90, "label": "Image(expIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 59, "child": []}, {"x": 60, "type": "Image", "props": {"y": 2, "x": 23, "width": 168, "var": "goldBg", "skin": "public/bg_coinyellow.png", "height": 44}, "nodeParent": 90, "label": "Image(goldBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 58, "child": []}, {"x": 60, "type": "Image", "props": {"y": -2, "x": 12, "var": "goldIcon", "skin": "public/coins.png", "scaleY": 0.9, "scaleX": 0.9}, "nodeParent": 90, "label": "Image(goldIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 71, "child": []}, {"x": 60, "type": "Label", "props": {"y": 7, "x": 68, "width": 124, "var": "gold_0", "text": "+1000", "strokeColor": "#ca7c21", "stroke": 2, "height": 36, "fontSize": 32, "color": "#ffffff", "bold": true, "align": "left"}, "nodeParent": 90, "label": "Label(gold_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": []}, {"x": 60, "type": "Label", "props": {"y": 72, "x": 67, "width": 71, "var": "expTxt", "text": "+5", "strokeColor": "#ca7c21", "stroke": 2, "height": 36, "fontSize": 32, "color": "#ffffff", "bold": true, "align": "left"}, "nodeParent": 90, "label": "Label(expTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 74, "child": []}]}, {"x": 45, "type": "Label", "props": {"y": 44, "x": 220, "width": 268, "var": "nikeName_0", "text": "111111111", "strokeColor": "#b1741c", "stroke": 2, "height": 35, "fontSize": 26, "color": "#ffffff"}, "nodeParent": 4, "label": "Label(nikeName_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": [], "$LOCKED": true}, {"x": 45, "type": "Label", "props": {"y": 94, "x": 228, "width": 103, "valign": "middle", "text": "TOTAL", "height": 28, "fontSize": 26, "color": "#9b5f19"}, "nodeParent": 4, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 60, "child": [], "$LOCKED": false}, {"x": 45, "type": "Label", "props": {"y": 94, "x": 317, "width": 64, "var": "totalTxt_0", "valign": "middle", "text": "0", "height": 28, "fontSize": 26, "color": "#ff6c00", "align": "center"}, "nodeParent": 4, "label": "Label(totalTxt_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 61, "child": [], "$LOCKED": false}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 48, "x": 220, "visible": false, "var": "nameItem_0"}, "nodeParent": 4, "label": "UIView(nameItem_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 91, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 509, "x": 0, "width": 694, "visible": false, "var": "info_1", "skin": "public/BG_234.png", "height": 125}, "nodeParent": 33, "label": "Image(info_1)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 7, "child": [{"x": 45, "type": "Image", "props": {"y": -21, "x": -18, "width": 730, "visible": false, "var": "my_1", "skin": "public/bg_oneself.png", "sizeGrid": "30,30,30,30", "height": 168}, "nodeParent": 7, "label": "Image(my_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 86, "child": []}, {"x": 45, "type": "Label", "props": {"y": 36, "x": 39, "width": 30, "text": "2", "height": 46, "fontSize": 48, "color": "#ffffff"}, "nodeParent": 7, "label": "Label", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"x": 45, "type": "Label", "props": {"y": 74, "x": 228, "width": 103, "valign": "middle", "text": "TOTAL", "height": 28, "fontSize": 26, "color": "#ffffff"}, "nodeParent": 7, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 75, "child": [], "$LOCKED": false}, {"x": 45, "type": "Label", "props": {"y": 74, "x": 317, "width": 64, "var": "totalTxt_1", "valign": "middle", "text": "0", "height": 28, "fontSize": 26, "color": "#fff268", "align": "center"}, "nodeParent": 7, "label": "Label(totalTxt_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 76, "child": []}, {"x": 45, "type": "Label", "props": {"y": 29, "x": 220, "width": 268, "var": "nikeName_1", "text": "1111111111", "height": 35, "fontSize": 26, "color": "#8dfff8"}, "nodeParent": 7, "label": "Label(nikeName_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 77, "child": [], "$LOCKED": true, "$HIDDEN": true}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 33, "x": 220, "visible": false, "var": "nameItem_1"}, "nodeParent": 7, "label": "UIView(nameItem_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 92, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 655, "x": 0, "width": 694, "visible": false, "var": "info_2", "skin": "public/BG_234.png", "height": 125}, "nodeParent": 33, "label": "Image(info_2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 36, "child": [{"x": 45, "type": "Image", "props": {"y": -21, "x": -18, "width": 730, "visible": false, "var": "my_2", "skin": "public/bg_oneself.png", "sizeGrid": "30,30,30,30", "height": 168}, "nodeParent": 36, "label": "Image(my_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 87, "child": []}, {"x": 45, "type": "Label", "props": {"y": 36, "x": 40, "width": 30, "text": "3", "height": 46, "fontSize": 48, "color": "#ffffff"}, "nodeParent": 36, "label": "Label", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 68, "child": []}, {"x": 45, "type": "Label", "props": {"y": 74, "x": 228, "width": 103, "valign": "middle", "text": "TOTAL", "height": 28, "fontSize": 26, "color": "#ffffff"}, "nodeParent": 36, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 78, "child": [], "$LOCKED": false}, {"x": 45, "type": "Label", "props": {"y": 74, "x": 317, "width": 64, "var": "totalTxt_2", "valign": "middle", "text": "0", "height": 28, "fontSize": 26, "color": "#fff268", "align": "center"}, "nodeParent": 36, "label": "Label(totalTxt_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 79, "child": []}, {"x": 45, "type": "Label", "props": {"y": 29, "x": 220, "width": 268, "var": "nikeName_2", "text": "111111111", "height": 35, "fontSize": 26, "color": "#8dfff8"}, "nodeParent": 36, "label": "Label(nikeName_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 80, "child": []}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 33, "x": 220, "visible": false, "var": "nameItem_2"}, "nodeParent": 36, "label": "UIView(nameItem_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 93, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 801, "x": 0, "width": 694, "visible": false, "var": "info_3", "skin": "public/BG_234.png", "height": 125}, "nodeParent": 33, "label": "Image(info_3)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 41, "child": [{"x": 45, "type": "Image", "props": {"y": -21, "x": -18, "width": 730, "visible": false, "var": "my_3", "skin": "public/bg_oneself.png", "sizeGrid": "30,30,30,30", "height": 168}, "nodeParent": 41, "label": "Image(my_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 88, "child": []}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 33, "x": 220, "visible": false, "var": "nameItem_3"}, "nodeParent": 41, "label": "UIView(nameItem_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 94, "child": []}, {"x": 45, "type": "Label", "props": {"y": 36, "x": 40, "width": 30, "text": "4", "height": 46, "fontSize": 48, "color": "#ffffff"}, "nodeParent": 41, "label": "Label", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 70, "child": []}, {"x": 45, "type": "Label", "props": {"y": 74, "x": 228, "width": 103, "valign": "middle", "text": "TOTAL", "height": 28, "fontSize": 26, "color": "#ffffff"}, "nodeParent": 41, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 81, "child": [], "$LOCKED": false}, {"x": 45, "type": "Label", "props": {"y": 74, "x": 317, "width": 64, "var": "totalTxt_3", "valign": "middle", "text": "0", "height": 28, "fontSize": 26, "color": "#fff268", "align": "center"}, "nodeParent": 41, "label": "Label(totalTxt_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 82, "child": []}, {"x": 45, "type": "Label", "props": {"y": 29, "x": 220, "width": 268, "var": "nikeName_3", "text": "111111111", "height": 35, "fontSize": 26, "color": "#8dfff8"}, "nodeParent": 41, "label": "Label(nikeName_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 83, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Box", "props": {"y": -23, "x": 26, "width": 642, "var": "topMc", "height": 284, "centerX": 0}, "nodeParent": 33, "label": "Box(topMc)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 89, "child": []}, {"x": 30, "type": "Label", "props": {"y": 132, "width": 553, "var": "titleTxt", "valign": "middle", "height": 95, "fontSize": 52, "color": "#FFFFFF", "centerX": 4, "align": "center"}, "nodeParent": 33, "label": "Label(titleTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}