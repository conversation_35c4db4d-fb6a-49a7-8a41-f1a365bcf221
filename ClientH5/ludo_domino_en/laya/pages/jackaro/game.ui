{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 164, "referenceLines": null, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "name": "jackaroMapGame", "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View(jackaroMapGame)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "bg", "top": 0, "right": 0, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 132, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 667, "x": 375, "var": "furniture", "skin": "jackaro/BG_52000.jpg", "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image(furniture)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 133, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 0, "x": 0, "var": "map_layer", "top": 0, "right": 0, "name": "map_layer", "mouseThrough": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(map_layer)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 44, "child": []}, {"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "var": "topImg", "top": 0, "skin": "snake/image_ludo_bg0.png", "sizeGrid": "0,0,20,0", "right": 0, "left": 0, "height": 95}, "nodeParent": 1, "label": "Image(topImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 134, "child": []}, {"x": 15, "type": "Box", "props": {"x": 0, "width": 750, "var": "bodyMc", "top": 110, "right": 0, "mouseThrough": true, "left": 0, "height": 1229, "bottom": 0}, "nodeParent": 1, "label": "Box(bodyMc)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 78, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"width": 750, "var": "players_layer", "right": 0, "mouseThrough": true, "mouseEnabled": true, "left": 0, "height": 890, "centerY": -100}, "nodeParent": 1, "label": "Box(players_layer)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 163, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"y": 0, "x": 0, "var": "shuffle<PERSON><PERSON><PERSON><PERSON><PERSON>", "top": 0, "right": 0, "name": "shuffle<PERSON><PERSON><PERSON><PERSON><PERSON>", "mouseThrough": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(shuffleSpine<PERSON>ayer)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 430, "child": []}, {"x": 15, "type": "Box", "props": {"var": "<PERSON><PERSON><PERSON>er", "top": 0, "right": 0, "name": "<PERSON><PERSON><PERSON>er", "mouseThrough": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(middleLayer)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 124, "child": []}, {"x": 15, "type": "Box", "props": {"var": "otherHandCardLayer", "top": 0, "right": 0, "name": "otherHandCardLayer", "mouseThrough": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(otherHandCardLayer)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 125, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"width": 750, "var": "handCardLayerPlaceHolder", "name": "handCardLayerPlaceHolder", "mouseThrough": false, "height": 173, "centerY": 550, "centerX": 0}, "nodeParent": 1, "label": "Box(handCardLayerPlaceHolder)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 415, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"var": "giftAniBox", "top": 0, "right": 0, "mouseThrough": true, "mouseEnabled": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(giftAniBox)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 164, "child": [{"type": "Image", "props": {"visible": false, "var": "giftAniBg", "top": 0, "skin": "jackaro/alphaBg.png", "right": 0, "mouseThrough": false, "left": 0, "bottom": 0}, "nodeParent": 164, "label": "Image(giftAniBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 431, "child": []}]}, {"x": 15, "type": "Box", "props": {"var": "operateLayer", "top": 0, "right": 0, "name": "operateLayer", "mouseThrough": true, "mouseEnabled": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(operateLayer)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 417, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"var": "handCardLayer", "top": 0, "right": 0, "name": "handCardLayer", "mouseThrough": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(handCardLayer)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 123, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"y": 0, "x": 0, "var": "replenish<PERSON>ard<PERSON><PERSON><PERSON>", "top": 0, "right": 0, "name": "replenish<PERSON>ard<PERSON><PERSON><PERSON>", "mouseThrough": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(replenishCardLayer)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 432, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"y": 567, "x": 375, "width": 1, "visible": false, "var": "dealPokerAni", "name": "dealPokerAni", "height": 1, "centerY": -100, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(dealPokerAni)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 420, "child": [{"type": "Image", "props": {"y": 0, "x": 0, "skin": "jackaroCard/card_back_52000.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 420, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 423, "child": []}]}, {"x": 15, "type": "Box", "props": {"var": "toastLayer", "top": 0, "right": 0, "name": "toastLayer", "mouseThrough": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(toastLayer)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 418, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"var": "trustLayer", "top": 0, "right": 0, "name": "trustLayer", "mouseThrough": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(trustLayer)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 427, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"var": "tip<PERSON>ayer", "top": 0, "right": 0, "name": "tip<PERSON>ayer", "mouseThrough": true, "mouseEnabled": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(tipLayer)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 428, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"var": "topLayer", "top": 0, "right": 0, "name": "topLayer", "mouseThrough": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(topLayer)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 122, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"visible": false, "var": "roundTimeBox", "right": 130, "mouseThrough": false, "mouseEnabled": false, "bottom": 58, "anchorY": 1, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(roundTimeBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 407, "child": [{"x": 30, "type": "Label", "props": {"y": 0, "x": 0, "width": 120, "var": "roundTimeBoxLabel", "text": "+15s", "name": "roundTimeBoxLabel", "height": 80, "fontSize": 60, "color": "#ffec89", "align": "right"}, "nodeParent": 407, "label": "Label(roundTimeBoxLabel)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 410, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"width": 1, "var": "dealPokerPlaceholder", "name": "dealPokerPlaceholder", "height": 1, "centerY": -100, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(dealPokerPlaceholder)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 416, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}