{"x": 0, "type": "View", "selectedBox": 28, "selecteID": 29, "referenceLines": null, "props": {"width": 430, "sceneColor": "#000000", "right": 0, "mouseThrough": true, "height": 266, "bottom": 28}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 212, "width": 146, "visible": true, "var": "addTimeFeeBtn", "stateNum": 1, "skin": "domino/alphaBg.png", "right": 57, "name": "addTimeFeeBtn", "height": 54}, "nodeParent": 1, "label": "Button(addTimeFeeBtn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 28, "child": [{"x": 30, "type": "Image", "props": {"y": 0, "x": 0, "width": 146, "var": "bg_addTime", "skin": "jackaro/bg_addTime.png", "height": 54}, "nodeParent": 28, "label": "Image(bg_addTime)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}, {"type": "Image", "props": {"y": 20, "x": 66, "var": "icon_diamond", "skin": "jackaro/icon_diamond.png"}, "nodeParent": 28, "label": "Image(icon_diamond)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 34, "child": []}, {"type": "Label", "props": {"y": 19, "x": 89, "width": 55, "var": "txt_money", "valign": "middle", "text": "10", "height": 23, "fontSize": 24, "color": "#ffffff", "bold": true, "align": "left"}, "nodeParent": 28, "label": "Label(txt_money)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": [], "$HIDDEN": true}], "$HIDDEN": false}, {"x": 15, "type": "Label", "props": {"y": 231, "width": 60, "visible": false, "var": "txt_lack", "valign": "middle", "text": "Lack", "right": 75, "name": "txt_lack", "height": 23, "fontSize": 24, "color": "#ffffff", "bold": true, "align": "center"}, "nodeParent": 1, "label": "Label(txt_lack)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 35, "child": [], "$HIDDEN": false}, {"x": 15, "type": "UIView", "source": "jackaro/item/jackaroRoundTime.ui", "props": {"y": 27, "x": 0, "width": 423, "var": "extendRound", "name": "extendRound", "mouseThrough": true, "height": 166}, "nodeParent": 1, "label": "UIView(extendRound)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 36, "child": []}, {"x": 15, "type": "Image", "props": {"visible": false, "var": "quick_buy_btn", "skin": "public/Recharge_label.png"}, "nodeParent": 1, "label": "Image(quick_buy_btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 37, "child": [{"x": 30, "type": "Image", "props": {"y": 201, "x": 297, "skin": "public/Recharge_label_1.png"}, "nodeParent": 37, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 39, "child": []}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}