{"x": 0, "type": "View", "selectedBox": 2, "selecteID": 10, "props": {"width": 460, "var": "name<PERSON><PERSON><PERSON>", "sceneColor": "#000000", "height": 40}, "nodeParent": -1, "label": "View(nameLabel)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"y": 0, "x": 0, "width": 460, "var": "teamBox"}, "nodeParent": 1, "label": "Box(teamBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Image", "props": {"y": 2, "x": 11, "visible": false, "var": "teamIcon", "skin": "jackaro/chess_50000_0.png"}, "nodeParent": 2, "label": "Image(teamIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 30, "type": "Label", "props": {"y": 7, "x": 56, "width": 163, "var": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "height": 19, "fontSize": 20, "color": "#5ee4b7", "bold": true}, "nodeParent": 2, "label": "Label(playerNameLabel)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 30, "type": "Image", "props": {"y": 2, "x": 241, "var": "killIcon", "skin": "jackaro/icon_kill.png"}, "nodeParent": 2, "label": "Image(killIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}, {"x": 30, "type": "Label", "props": {"y": 8, "x": 276, "var": "killNumLabel", "text": "0", "fontSize": 24, "color": "#ffffff"}, "nodeParent": 2, "label": "Label(killNumLabel)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 30, "type": "Image", "props": {"y": 5, "x": 327, "var": "beKilledIcon", "skin": "jackaro/icon_beKilled.png"}, "nodeParent": 2, "label": "Image(beKilledIcon)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": []}, {"x": 30, "type": "Label", "props": {"y": 8, "x": 361, "var": "beKilledLabel", "text": "0", "fontSize": 24, "color": "#ffffff"}, "nodeParent": 2, "label": "Label(beKilledLabel)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}