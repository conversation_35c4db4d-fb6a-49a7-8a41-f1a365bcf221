{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 14, "props": {"y": 100, "x": 70, "width": 140, "sceneColor": "#000000", "height": 200, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 140, "visible": false, "var": "baise", "skin": "jackaroCard/baise.png", "scaleX": 1, "name": "baise", "height": 200, "alpha": 0}, "nodeParent": 1, "label": "Image(baise)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 100, "x": 69, "width": 140, "visible": false, "var": "yinying", "skin": "jackaroCard/yinying.png", "scaleX": 1, "name": "yinying", "height": 200, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(yinying)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 0, "x": 0, "width": 140, "visible": true, "var": "aniBox", "name": "aniBox", "height": 200}, "nodeParent": 1, "label": "Box(aniBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 10, "child": [{"x": 30, "type": "Image", "props": {"y": 100, "x": 70, "width": 140, "var": "cardSkinImg", "scaleY": 1, "scaleX": 1, "name": "cardSkinImg", "height": 200, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 10, "label": "Image(cardSkinImg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 45, "type": "Image", "props": {"y": 18, "visible": false, "var": "info_sp", "skin": "jackaroCard/card_KILL_en.png", "right": 15, "name": "info_sp", "anchorX": 1}, "nodeParent": 2, "label": "Image(info_sp)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": [], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 100, "x": 70, "width": 140, "visible": false, "var": "backBg", "skin": "jackaroCard/card_back_52000.png", "scaleX": 1, "name": "backBg", "height": 200, "anchorY": 0.5, "anchorX": 0.5, "alpha": 1}, "nodeParent": 10, "label": "Image(backBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": [], "$HIDDEN": false}]}, {"x": 15, "type": "Image", "props": {"y": -10, "x": -10, "visible": false, "var": "selectBg", "skin": "jackaroCard/card_select_52000.png", "sizeGrid": "8,8,8,8", "rotation": 0, "name": "selectBg"}, "nodeParent": 1, "label": "Image(selectBg)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "visible": false, "var": "cardMask", "skin": "jackaroCard/card_mask.png", "sizeGrid": "8,8,8,8", "name": "cardMask"}, "nodeParent": 1, "label": "Image(cardMask)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Image", "props": {"y": -14, "x": -12, "visible": false, "var": "bg_reward", "skin": "jackaroCard/bg_reward.png", "scaleY": 1, "scaleX": 1, "name": "bg_reward"}, "nodeParent": 1, "label": "Image(bg_reward)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 0, "x": 0, "width": 140, "var": "starAnimationBox", "name": "starAnimationBox", "height": 200}, "nodeParent": 1, "label": "Box(starAnimationBox)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": [], "$HIDDEN": true}], "animations": [{"nodes": [{"target": 6, "keyframes": {"visible": [{"value": true, "tweenMethod": "linearNone", "tween": false, "target": 6, "key": "visible", "index": 0}, {"value": true, "tweenMethod": "linearNone", "tween": false, "target": 6, "key": "visible", "index": 20}], "scaleX": [{"value": 1, "tweenMethod": "cubicOut", "tween": true, "target": 6, "key": "scaleX", "index": 0}, {"value": 0.1, "tweenMethod": "linearNone", "tween": true, "target": 6, "key": "scaleX", "index": 4}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 6, "key": "scaleX", "index": 5}]}}, {"target": 2, "keyframes": {"scaleX": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 4}, {"value": 0.11111111, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 5}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 12}]}}, {"target": 10, "keyframes": {"y": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "y", "index": 0}, {"value": -15, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "y", "index": 5}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 10, "key": "y", "index": 12}]}}, {"target": 8, "keyframes": {"y": [{"value": 100, "tweenMethod": "linearNone", "tween": true, "target": 8, "key": "y", "index": 0}, {"value": 107, "tweenMethod": "linearNone", "tween": true, "target": 8, "key": "y", "index": 5}, {"value": 100, "tweenMethod": "linearNone", "tween": true, "target": 8, "key": "y", "index": 12}], "x": [{"value": 69, "tweenMethod": "linearNone", "tween": true, "target": 8, "key": "x", "index": 0}, {"value": 61, "tweenMethod": "linearNone", "tween": true, "target": 8, "key": "x", "index": 5}, {"value": 54, "tweenMethod": "linearNone", "tween": true, "target": 8, "key": "x", "index": 10}, {"value": 69, "tweenMethod": "linearNone", "tween": true, "target": 8, "key": "x", "index": 12}], "scaleX": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 8, "key": "scaleX", "index": 0}, {"value": 0.1, "tweenMethod": "linearNone", "tween": true, "target": 8, "key": "scaleX", "index": 5}, {"value": 0.7, "tweenMethod": "linearNone", "tween": true, "target": 8, "key": "scaleX", "index": 10}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 8, "key": "scaleX", "index": 12}]}}, {"target": 9, "keyframes": {"scaleX": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "scaleX", "index": 0}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "scaleX", "index": 2}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "alpha", "index": 0}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "alpha", "index": 2}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 9, "key": "alpha", "index": 4}]}}], "name": "CardFlip", "id": 1, "frameRate": 30, "action": 0}]}