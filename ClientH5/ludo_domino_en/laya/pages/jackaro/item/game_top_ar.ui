{"x": 0, "type": "View", "selectedBox": 62, "selecteID": 63, "referenceLines": null, "props": {"width": 750, "var": "game_top_ar", "sceneColor": "#000000", "right": 0, "mouseThrough": true, "left": 0, "height": 200}, "nodeParent": -1, "label": "View(game_top_ar)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "HBox", "props": {"y": 0, "x": 0, "var": "topMc", "top": 0, "right": 0, "left": 0, "height": 93, "cacheAs": "normal"}, "nodeParent": 1, "label": "HBox(topMc)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"var": "winBtn", "top": 4, "stateNum": 1, "skin": "jackaro/btn_reward.png", "right": 110}, "nodeParent": 2, "label": "Button(winBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 30, "type": "Image", "props": {"width": 80, "top": 6, "skin": "jackaro/alphaBg.png", "right": 14, "height": 80}, "nodeParent": 2, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 0, "var": "settingBtn", "stateNum": 1, "skin": "jackaro/system.png"}, "nodeParent": 4, "label": "Button(settingBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}]}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 18, "width": 262, "visible": false, "var": "titleSpectator", "stateNum": 1, "skin": "jackaro/image_title_bg.png", "height": 48, "centerX": 24}, "nodeParent": 2, "label": "<PERSON><PERSON>(titleSpectator)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 40, "child": [{"x": 45, "type": "Image", "props": {"y": 15, "x": 30, "skin": "jackaro/Audience.png"}, "nodeParent": 40, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 41, "child": []}, {"x": 45, "type": "Label", "props": {"y": 15, "x": 82, "width": 90, "valign": "middle", "text": "Spectator", "height": 20, "fontSize": 18, "color": "#ffffff", "align": "center"}, "nodeParent": 40, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 42, "child": []}, {"x": 45, "type": "Label", "props": {"y": 9, "x": 185, "width": 52, "var": "audience_num", "valign": "middle", "text": 0, "height": 31, "fontSize": 24, "color": "#ffffff", "align": "center"}, "nodeParent": 40, "label": "Label(audience_num)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 43, "child": [], "$HIDDEN": true}]}, {"x": 30, "type": "Image", "props": {"width": 735, "visible": false, "var": "set", "top": 88, "skin": "jackaro/BG_Expression.png", "sizeGrid": "15,15,15,15", "right": 7, "height": 125}, "nodeParent": 2, "label": "Image(set)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 23, "child": [{"x": 45, "type": "Image", "props": {"y": 5, "x": 702, "skin": "jackaro/BG_Expression_V.png", "rotation": 180}, "nodeParent": 23, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": []}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 14, "x": 609, "width": 110, "var": "soundBtn", "stateNum": 1, "skin": "jackaro/BG_system.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "Button(soundBtn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 31, "child": [{"type": "Image", "props": {"y": 12, "var": "soundIcon", "skin": "jackaro/soundON.png", "centerX": 0}, "nodeParent": 31, "label": "Image(soundIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": []}, {"type": "Label", "props": {"y": 66, "width": 131, "var": "soundTxt", "text": "ON", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 0, "align": "center"}, "nodeParent": 31, "label": "Label(soundTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 33, "child": []}]}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 14, "x": 490, "width": 110, "var": "musicBtn", "stateNum": 1, "skin": "jackaro/BG_system.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "<PERSON><PERSON>(musicBtn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 37, "child": [{"type": "Image", "props": {"y": 12, "var": "musicIcon", "skin": "jackaro/musicON.png", "centerX": 0}, "nodeParent": 37, "label": "Image(musicIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 38, "child": []}, {"type": "Label", "props": {"y": 66, "width": 142, "var": "musicTxt", "text": "ON", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 0, "align": "center"}, "nodeParent": 37, "label": "Label(musicTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 39, "child": []}]}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 14, "x": 371, "width": 110, "var": "ruleBtn", "stateNum": 1, "skin": "jackaro/BG_system.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "<PERSON><PERSON>(ruleBtn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 25, "child": [{"type": "Image", "props": {"y": 12, "skin": "jackaro/rule.png", "centerX": 0}, "nodeParent": 25, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": []}, {"type": "Label", "props": {"y": 66, "width": 120, "text": "Rules", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 0, "align": "center"}, "nodeParent": 25, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}]}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 14, "x": 252, "width": 110, "var": "chatSwitchBtn", "stateNum": 1, "skin": "jackaro/BG_system.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "Button(chatSwitchBtn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 34, "child": [{"type": "Image", "props": {"y": 12, "var": "switchIcon", "skin": "jackaro/ChatON.png", "centerX": 0}, "nodeParent": 34, "label": "Image(switchIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 35, "child": []}, {"type": "Label", "props": {"y": 66, "width": 157, "var": "switchTxt", "text": "Chat ON", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 0, "align": "center"}, "nodeParent": 34, "label": "Label(switchTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 36, "child": []}]}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 14, "x": 133, "width": 110, "var": "tipsBtn", "stateNum": 1, "skin": "jackaro/BG_system.png", "sizeGrid": "12,12,12,12", "name": "tipsBtn", "height": 98}, "nodeParent": 23, "label": "Button(tipsBtn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 62, "child": [{"x": 60, "type": "Image", "props": {"y": 12, "var": "tipsIcon", "skin": "jackaro/tipsON.png", "name": "tipsIcon", "centerX": 0}, "nodeParent": 62, "label": "Image(tipsIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 63, "child": []}, {"x": 60, "type": "Label", "props": {"y": 66, "width": 157, "var": "tipsTxt", "text": "Tips", "strokeColor": "#105740", "name": "tipsTxt", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 0, "align": "center"}, "nodeParent": 62, "label": "Label(tipsTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 64, "child": []}]}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 14, "x": 15, "width": 110, "var": "exitBtn", "stateNum": 1, "skin": "jackaro/BG_system.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "Button(exitBtn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 28, "child": [{"type": "Image", "props": {"y": 12, "skin": "jackaro/exit.png", "centerX": 0}, "nodeParent": 28, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}, {"type": "Label", "props": {"y": 66, "width": 120, "text": "Exit", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 0, "align": "center"}, "nodeParent": 28, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 30, "child": []}]}]}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 10, "visible": false, "var": "store_btn", "stateNum": 1, "skin": "game/btn_store.png", "left": 10, "labelSize": 24, "labelColors": "#ffffff", "label": "-"}, "nodeParent": 2, "label": "Button(store_btn)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 61, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}