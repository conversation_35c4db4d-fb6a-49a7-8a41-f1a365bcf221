{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 2, "props": {"width": 800, "visible": false, "sceneColor": "#000000", "height": 800, "centerY": -104, "centerX": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "bg", "skin": "jackaro/Base_52000.png", "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"var": "check", "rotation": 0, "centerY": 0, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(check)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 400, "x": 400, "width": 800, "var": "sign_box", "height": 800, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(sign_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"y": 400, "x": 400, "width": 800, "var": "props_box", "name": "props_box", "height": 800, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(props_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": [], "$LOCKED": true, "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 400, "x": 400, "width": 800, "var": "chess_box", "name": "chess_box", "hitTestPrior": true, "height": 800, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(chess_box)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"type": "Image", "props": {"y": 594, "x": 177, "visible": false, "skin": "jackaro/chess_50000_0.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 4, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": [], "$HIDDEN": true}], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"y": 400, "x": 400, "width": 800, "var": "ani_box", "name": "ani_box", "mouseThrough": true, "height": 800, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(ani_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": [], "$HIDDEN": true}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 239, "x": 58, "width": 130, "visible": false, "var": "close_socket", "stateNum": "1", "skin": "jackaro/btn_G.png", "name": "close_socket", "labelSize": 24, "label": "断开网络", "height": 77}, "nodeParent": 1, "label": "But<PERSON>(close_socket)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": [], "$HIDDEN": false}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 240, "x": 616, "width": 130, "visible": false, "var": "debug_log", "stateNum": "1", "skin": "jackaro/btn_G.png", "name": "debug_log", "labelSize": 24, "label": "日志打点", "height": 77}, "nodeParent": 1, "label": "Button(debug_log)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": [], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}