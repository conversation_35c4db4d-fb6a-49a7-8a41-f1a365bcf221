{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "props": {"y": 100, "x": 100, "width": 200, "sceneColor": "#000000", "mouseThrough": true, "height": 200, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Animation", "props": {"y": 100, "x": 100, "var": "finger", "source": "jackaro/novice/finger.ani"}, "nodeParent": 1, "label": "Animation(finger)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}