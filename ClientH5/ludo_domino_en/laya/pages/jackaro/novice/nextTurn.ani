{"x": 0, "type": "View", "selectedBox": 1, "props": {"sceneWidth": 600, "sceneHeight": 400, "sceneColor": "#000000"}, "nodeParent": -1, "label": "View", "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"type": "GraphicNode", "props": {}, "nodeParent": 1, "label": "GraphicNode", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}], "animations": [{"nodes": [{"target": 2, "keyframes": {"y": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "y", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "y", "index": 45}, {"value": 72, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "y", "index": 60}], "x": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "x", "index": 0}], "skin": [{"value": "jackaro/novice/next_turn_en.png", "tweenMethod": "linearNone", "tween": false, "target": 2, "key": "skin", "index": 0}], "scaleY": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 0}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 15}, {"value": 0.9, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 45}], "scaleX": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 0}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 15}, {"value": 0.9, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 45}], "anchorY": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "anchorY", "index": 0}], "anchorX": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "anchorX", "index": 0}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "alpha", "index": 0}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "alpha", "index": 45}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "alpha", "index": 60}]}}], "name": "ani_en", "id": 1, "frameRate": 60, "action": 0}, {"nodes": [{"target": 2, "keyframes": {"y": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "y", "index": 0}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "y", "index": 45}, {"value": 72, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "y", "index": 60}], "x": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "x", "index": 0}], "skin": [{"value": "jackaro/novice/next_turn_ar.png", "tweenMethod": "linearNone", "tween": false, "target": 2, "key": "skin", "index": 0}, {"value": "jackaro/novice/next_turn_ar.png", "tweenMethod": "linearNone", "tween": false, "target": 2, "key": "skin", "index": 60}], "scaleY": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 0}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 15}, {"value": 0.9, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 45}], "scaleX": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 0}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 15}, {"value": 0.9, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 45}], "anchorY": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "anchorY", "index": 0}], "anchorX": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "anchorX", "index": 0}], "alpha": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "alpha", "index": 0}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "alpha", "index": 45}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "alpha", "index": 60}]}}], "name": "ani_ar", "id": 1, "frameRate": 60, "action": 0}]}