{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 3, "props": {"y": 0, "x": 0, "width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "mouseThrough": true, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"visible": false, "var": "mask_bg", "top": 0, "skin": "public/alphaBg80.png", "right": 0, "mouseEnabled": false, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image(mask_bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": [], "$HIDDEN": false}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"width": 221, "var": "btn_skip", "top": 40, "stateNum": "1", "skin": "jackaro/novice/skip_bg.png", "right": -4, "mouseThrough": false, "labelStrokeColor": "#412062", "labelStroke": 2, "labelSize": 24, "labelColors": "#E4E3F0", "labelBold": true, "label": "<PERSON><PERSON>", "height": 58}, "nodeParent": 1, "label": "<PERSON><PERSON>(btn_skip)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 15, "type": "Animation", "props": {"y": 617, "x": 391, "visible": false, "var": "nextTurn", "source": "jackaro/novice/nextTurn.ani", "autoPlay": false}, "nodeParent": 1, "label": "Animation(nextTurn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}