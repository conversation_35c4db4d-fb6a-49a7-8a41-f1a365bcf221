{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 5, "props": {"y": 127, "width": 718, "visible": false, "sceneColor": "#000000", "height": 254, "centerX": 0, "anchorY": 0.5}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 359, "var": "bg", "skin": "jackaro/novice/img_tipBg_l.png", "scaleX": -1, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(bg)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 15, "type": "Image", "props": {"y": 68, "x": 526, "var": "card", "skin": "jackaro/novice/card_r_10.png"}, "nodeParent": 1, "label": "Image(card)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 15, "type": "Image", "props": {"y": 147, "x": 282, "var": "tip_img", "skin": "jackaro/novice/tip_1_en.png", "centerY": 20, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(tip_img)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}