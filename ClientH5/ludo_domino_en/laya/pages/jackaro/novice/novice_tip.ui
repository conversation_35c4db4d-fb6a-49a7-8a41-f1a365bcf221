{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 2, "props": {"width": 704, "visible": false, "sceneColor": "#000000", "height": 428, "centerY": 0, "centerX": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "var": "bg", "skin": "jackaro/novice/finish_bg.png"}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 260, "x": 478, "width": 195, "var": "btn_playnow", "skin": "public/btn_y.png", "labelStrokeColor": "#FFF08B", "labelStroke": 2, "labelSize": 26, "labelColors": "#AB6E0F", "labelBold": true, "label": "Play Now", "height": 85}, "nodeParent": 1, "label": "Button(btn_playnow)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 260, "x": 271, "width": 195, "var": "btn_watchagain", "skin": "public/btn_G.png", "labelStrokeColor": "#61F2CA", "labelStroke": 2, "labelSize": 24, "labelColors": "#08846B", "labelBold": true, "label": "Watch Again", "height": 85}, "nodeParent": 1, "label": "Button(btn_watchagain)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}, {"x": 15, "type": "Image", "props": {"y": 157, "x": 469, "var": "tip", "skin": "jackaro/novice/tip_end_en.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(tip)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}