{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 3, "props": {"y": 0, "x": 0, "width": 82, "sceneColor": "#000000", "height": 82}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 11, "x": 11, "var": "img_chess_num", "skin": "jackaro/choose_bg_0.png", "name": "img_chess_num"}, "nodeParent": 1, "label": "Image(img_chess_num)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 15, "type": "Image", "props": {"y": 8, "x": 81, "visible": false, "var": "img_dividing_line", "skin": "jackaro/img_dividing_line.png", "name": "img_dividing_line", "height": 64}, "nodeParent": 1, "label": "Image(img_dividing_line)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 15, "type": "Image", "props": {"y": 27, "x": 29, "var": "img_chess_text", "skin": "jackaro/n1.png"}, "nodeParent": 1, "label": "Image(img_chess_text)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}