{"x": 0, "type": "View", "selectedBox": 3, "selecteID": 10, "props": {"y": 100, "x": 500, "width": 1000, "sceneColor": "#000000", "height": 100, "anchorY": 1, "anchorX": 0.5}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"width": 172, "var": "nums_bg", "skin": "jackaro/bg_chess_tag.png", "height": 86, "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image(nums_bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"x": 30, "type": "HBox", "props": {"var": "nums_box", "top": 2, "space": 0, "right": 2, "left": 2, "bottom": 2}, "nodeParent": 3, "label": "HBox(nums_box)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}]}, {"x": 15, "type": "Box", "props": {"width": 26, "var": "arrow", "rotation": 0, "height": 102, "centerY": 0, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(arrow)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 11, "child": [{"x": 30, "type": "Image", "props": {"var": "arrow_skin", "skin": "jackaro/bg_chess_arrow.png", "centerX": 0, "bottom": 0}, "nodeParent": 11, "label": "Image(arrow_skin)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}