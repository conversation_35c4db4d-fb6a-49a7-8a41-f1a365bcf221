{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 145, "referenceLines": null, "props": {"width": 300, "sceneColor": "#000000", "height": 220, "autoAnimation": "pause"}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"y": 190, "x": 96, "width": 180, "var": "name_box", "height": 32, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(name_box)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 19, "child": [{"type": "HBox", "props": {"y": 16, "x": 0, "space": 5, "left": 0, "centerY": 0, "anchorY": 0.5, "align": "middle"}, "nodeParent": 19, "label": "HBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 17, "child": [{"type": "Box", "props": {"y": 2, "width": 40, "visible": false, "var": "flag", "height": 40}, "nodeParent": 17, "label": "Box(flag)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"type": "Box", "props": {"y": 2, "width": 40, "visible": false, "var": "RL", "height": 28}, "nodeParent": 17, "label": "Box(RL)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}, {"type": "Box", "props": {"y": 2, "width": 30, "visible": false, "var": "vip", "height": 28}, "nodeParent": 17, "label": "Box(vip)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"type": "Label", "props": {"y": 0, "var": "nick<PERSON><PERSON>", "valign": "middle", "text": "name", "rotation": 0, "height": 32, "fontSize": 20, "color": "#ffffff", "align": "left"}, "nodeParent": 17, "label": "Label(nickName)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}]}]}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 52, "x": 0, "width": 52, "var": "voice_btn", "height": 146}, "nodeParent": 1, "label": "<PERSON><PERSON>(voice_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"type": "Animation", "props": {"y": 34, "x": 4, "var": "voice_ani", "source": "publics/ani/ani_voice.ani"}, "nodeParent": 6, "label": "Animation(voice_ani)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}]}, {"x": 15, "type": "Animation", "props": {"y": 65, "x": 97, "width": 1, "visible": false, "var": "throw_start", "source": "publics/ani/jumpJackaro.ani", "scaleY": 1.6, "scaleX": 1.6, "height": 1}, "nodeParent": 1, "label": "Animation(throw_start)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 145, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 120, "x": 96, "width": 90, "var": "head_box", "rotation": 0, "height": 90, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(head_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": []}, {"x": 15, "type": "HBox", "props": {"y": 43, "x": 230, "var": "buff_box", "space": 5, "height": 40, "anchorY": 0.5, "anchorX": 0.5, "align": "middle"}, "nodeParent": 1, "label": "HBox(buff_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 15, "type": "Box", "props": {"y": 88, "x": 140, "width": 99, "var": "handCard_box", "name": "handCard_box", "height": 64}, "nodeParent": 1, "label": "Box(handCard_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": []}, {"x": 15, "type": "Image", "props": {"y": 120, "x": 96, "visible": false, "var": "icon_discard", "skin": "jackaro/icon_discard.png", "name": "icon_discard", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(icon_discard)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}