{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 19, "referenceLines": null, "props": {"width": 300, "sceneColor": "#000000", "height": 220}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"y": -10, "x": 96, "width": 180, "var": "name_box", "height": 32, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(name_box)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 19, "child": [{"x": 30, "type": "HBox", "props": {"x": 0, "space": 5, "centerY": 0, "anchorY": 0.5, "align": "middle"}, "nodeParent": 19, "label": "HBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 16, "child": [{"x": 45, "type": "Box", "props": {"width": 40, "visible": false, "var": "flag", "height": 40}, "nodeParent": 16, "label": "Box(flag)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"x": 45, "type": "Box", "props": {"width": 40, "visible": false, "var": "RL", "height": 28}, "nodeParent": 16, "label": "Box(RL)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}, {"x": 45, "type": "Box", "props": {"width": 30, "visible": false, "var": "vip", "height": 28}, "nodeParent": 16, "label": "Box(vip)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"x": 45, "type": "Label", "props": {"y": 0, "var": "nick<PERSON><PERSON>", "valign": "middle", "text": "name", "rotation": 0, "height": 32, "fontSize": 20, "color": "#ffffff", "align": "left"}, "nodeParent": 16, "label": "Label(nickName)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}]}]}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 0, "width": 52, "var": "voice_btn", "height": 136}, "nodeParent": 1, "label": "<PERSON><PERSON>(voice_btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"x": 30, "type": "Animation", "props": {"y": 24, "x": 4, "var": "voice_ani", "source": "publics/ani/ani_voice.ani"}, "nodeParent": 6, "label": "Animation(voice_ani)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}]}, {"x": 15, "type": "Box", "props": {"y": 65, "x": 96, "width": 90, "var": "head_box", "height": 90, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(head_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": []}, {"x": 15, "type": "Box", "props": {"y": 50, "x": 190, "width": 1, "var": "handCard_box", "name": "handCard_box", "height": 1}, "nodeParent": 1, "label": "Box(handCard_box)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": []}, {"x": 15, "type": "Image", "props": {"y": 65, "x": 96, "visible": false, "var": "icon_discard", "skin": "jackaro/icon_discard.png", "name": "icon_discard", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(icon_discard)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}