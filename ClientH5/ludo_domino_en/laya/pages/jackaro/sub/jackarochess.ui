{"x": 0, "type": "View", "selectedBox": 2, "selecteID": 6, "props": {"width": 40, "sceneWidth": 300, "sceneHeight": 300, "sceneColor": "#000000", "height": 40, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"width": 40, "var": "box", "mouseThrough": true, "height": 40}, "nodeParent": 1, "label": "Box(box)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Box", "props": {"y": 20, "x": 20, "width": 40, "var": "clickArea", "mouseThrough": false, "mouseEnabled": true, "height": 40, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 2, "label": "Box(clickArea)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 30, "type": "Image", "props": {"y": 20, "x": 20, "visible": false, "var": "chessImg", "skin": "jackaro/chess_50000_0.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 2, "label": "Image(chessImg)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 30, "type": "Image", "props": {"y": 9, "x": 11, "width": 18, "visible": false, "var": "text_chess_tag", "skin": "jackaro/n1.png", "height": 22}, "nodeParent": 2, "label": "Image(text_chess_tag)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}