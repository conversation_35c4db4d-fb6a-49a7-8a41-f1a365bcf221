{"x": 0, "type": "View", "selectedBox": 14, "selecteID": 3, "props": {"width": 268, "visible": false, "sceneColor": "#ffffff", "height": 268, "centerY": 0, "centerX": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"y": 136, "x": 133, "width": 134, "var": "progressBox", "rotation": 270, "pivotY": 121, "pivotX": 121, "height": 134}, "nodeParent": 1, "label": "Box(progressBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 14, "child": [{"x": 30, "type": "Image", "props": {"y": 0, "x": 0, "width": 134, "var": "bg", "skin": "jackaro/cProgressBg.png", "rotation": 0, "height": 134}, "nodeParent": 14, "label": "Image(bg)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 30, "type": "Image", "props": {"y": 9, "x": 9, "width": 117, "var": "progress_img", "skin": "jackaro/cPro_0.png", "height": 117}, "nodeParent": 14, "label": "Image(progress_img)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"x": 45, "type": "Sprite", "props": {"y": -9, "x": -9, "width": 135, "var": "maskSp", "renderType": "mask", "height": 136}, "nodeParent": 3, "label": "Sprite(maskSp)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 10, "child": [{"x": 60, "type": "Pie", "props": {"y": 122, "x": 122, "startAngle": -182, "radius": 120, "lineWidth": 0, "fillColor": "#ff6600", "endAngle": -88}, "nodeParent": 10, "label": "Pie", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}]}]}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}