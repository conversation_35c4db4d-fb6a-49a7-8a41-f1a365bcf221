{"x": 0, "type": "View", "selectedBox": 2, "selecteID": 27, "referenceLines": null, "props": {"width": 478, "sceneColor": "#000000", "height": 299, "cacheAs": "bitmap"}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 16, "x": 92, "skin": "jackaro/bg_teamRoomInfo_arrow.png", "rotation": -90}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": []}, {"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 478, "var": "bg", "skin": "jackaro/bg_teamRoomInfo.png", "height": 299, "cacheAs": "bitmap"}, "nodeParent": 1, "label": "Image(bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Image", "props": {"y": 18, "x": 223, "width": 245, "skin": "jackaro/bg_teamRoomInfoLabel.png", "height": 45}, "nodeParent": 2, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 8, "child": [{"x": 45, "type": "Image", "props": {"y": 0, "x": -13, "width": 41, "skin": "game/RANK1.png", "height": 43}, "nodeParent": 8, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 45, "type": "Label", "props": {"y": 10, "x": 37, "text": "RANK 1", "fontSize": 26, "color": "#fff690", "bold": true}, "nodeParent": 8, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 45, "type": "Label", "props": {"y": 9, "x": 148, "width": 90, "var": "rank_1", "overflow": "scroll", "height": 32, "fontSize": 30, "color": "#ffffff", "bold": true, "align": "center"}, "nodeParent": 8, "label": "Label(rank_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 38, "x": 95, "var": "rank_roomType", "skin": "jackaro/img_gameTitle102.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 2, "label": "Image(rank_roomType)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": []}, {"x": 30, "type": "Image", "props": {"y": 71, "x": 10, "width": 458, "var": "recordInfoBg1", "skin": "jackaro/bg_recordInfo.png"}, "nodeParent": 2, "label": "Image(recordInfoBg1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 30, "child": []}, {"x": 30, "type": "Image", "props": {"y": 169, "x": 10, "width": 458, "var": "recordInfoBg2", "skin": "jackaro/bg_recordInfo.png"}, "nodeParent": 2, "label": "Image(recordInfoBg2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 31, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Label", "props": {"y": 101, "x": 25, "var": "teamATxt", "text": "A", "fontSize": 32, "color": "#ffffff", "bold": true}, "nodeParent": 2, "label": "Label(teamATxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": []}, {"x": 30, "type": "Label", "props": {"y": 198, "x": 27, "var": "teamBTxt", "text": "B", "fontSize": 32, "color": "#ffffff", "bold": true}, "nodeParent": 2, "label": "Label(teamBTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 33, "child": []}, {"x": 30, "type": "Label", "props": {"y": 262, "x": 195, "width": 271, "var": "rank_roomId", "valign": "middle", "text": "12132334", "strokeColor": "#007C4B", "height": 23, "fontSize": 18, "color": "#198A64", "bold": true, "align": "right"}, "nodeParent": 2, "label": "Label(rank_roomId)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 30, "type": "Image", "props": {"y": 77, "x": 62, "width": 398, "visible": false, "var": "img_teamA_self_bg", "skin": "jackaro/img_selfRecord_right_up.png", "scaleX": 1, "height": 38}, "nodeParent": 2, "label": "Image(img_teamA_self_bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 34, "child": []}, {"x": 30, "type": "Image", "props": {"y": 175, "x": 62, "width": 398, "visible": false, "var": "img_teamB_self_bg", "skin": "jackaro/img_selfRecord_right_up.png", "scaleX": 1, "height": 38}, "nodeParent": 2, "label": "Image(img_teamB_self_bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 35, "child": []}, {"x": 30, "type": "UIView", "source": "jackaro/item/battleRecordItemEn.ui", "props": {"y": 76, "x": 66, "var": "recordInfo0"}, "nodeParent": 2, "label": "UIView(recordInfo0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": []}, {"x": 30, "type": "UIView", "source": "jackaro/item/battleRecordItemEn.ui", "props": {"y": 117, "x": 66, "var": "recordInfo1"}, "nodeParent": 2, "label": "UIView(recordInfo1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}, {"x": 30, "type": "UIView", "source": "jackaro/item/battleRecordItemEn.ui", "props": {"y": 175, "x": 66, "var": "recordInfo2"}, "nodeParent": 2, "label": "UIView(recordInfo2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 28, "child": []}, {"x": 30, "type": "UIView", "source": "jackaro/item/battleRecordItemEn.ui", "props": {"y": 217, "x": 66, "var": "recordInfo3"}, "nodeParent": 2, "label": "UIView(recordInfo3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}