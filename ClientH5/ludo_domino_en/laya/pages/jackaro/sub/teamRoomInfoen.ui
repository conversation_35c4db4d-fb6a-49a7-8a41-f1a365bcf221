{"x": 0, "type": "View", "selectedBox": 2, "selecteID": 4, "referenceLines": null, "props": {"width": 341, "sceneColor": "#000000", "height": 192, "cacheAs": "bitmap"}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 16, "x": 36, "skin": "jackaro/bg_teamRoomInfo_arrow.png", "rotation": -90}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": []}, {"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 341, "var": "bg", "skin": "jackaro/bg_teamRoomInfo.png", "height": 192, "cacheAs": "bitmap"}, "nodeParent": 1, "label": "Image(bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Image", "props": {"y": 80, "x": 16, "width": 310, "skin": "jackaro/bg_teamRoomInfoLabel.png", "height": 57, "centerX": 0}, "nodeParent": 2, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 8, "child": [{"x": 45, "type": "Image", "props": {"y": -7, "x": 20, "skin": "game/RANK1.png"}, "nodeParent": 8, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 45, "type": "Label", "props": {"y": 17, "x": 92, "text": "RANK 1", "fontSize": 26, "color": "#fff690", "bold": true}, "nodeParent": 8, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 45, "type": "Label", "props": {"y": 13, "x": 213, "width": 90, "var": "rank_1", "overflow": "scroll", "height": 32, "fontSize": 32, "color": "#ffffff", "bold": true, "align": "center"}, "nodeParent": 8, "label": "Label(rank_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 44, "x": 170, "var": "rank_roomType", "skin": "jackaro/img_gameTitle100.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 2, "label": "Image(rank_roomType)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": []}, {"x": 30, "type": "Label", "props": {"y": 152, "width": 384, "var": "rank_roomId", "valign": "middle", "strokeColor": "#007c4b", "stroke": 3, "height": 23, "fontSize": 22, "color": "#ffffff", "centerX": 0, "bold": true, "align": "center"}, "nodeParent": 2, "label": "Label(rank_roomId)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}