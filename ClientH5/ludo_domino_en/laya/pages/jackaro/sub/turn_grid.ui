{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "props": {"width": 120, "sceneColor": "#000000", "height": 40}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"skin": "jackaro/turn_grid.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 15, "type": "Image", "props": {"y": 38, "x": 120, "skin": "jackaro/turn_arrow.png", "rotation": 180}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}