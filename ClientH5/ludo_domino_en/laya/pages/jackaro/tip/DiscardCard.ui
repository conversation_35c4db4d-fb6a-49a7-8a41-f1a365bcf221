{"x": 0, "type": "View", "selectedBox": 62, "selecteID": 63, "referenceLines": null, "props": {"width": 750, "top": 0, "right": 0, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"top": 0, "skin": "public/maskBg.png", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"skin": "jackaro/bg_discard_all.png", "right": 22, "left": 22, "centerY": -78}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 62, "child": [{"x": 30, "type": "Image", "props": {"y": 26, "var": "discard_txt", "skin": "jackaro/txt_discard.png", "name": "discardI_txt", "centerX": 0}, "nodeParent": 62, "label": "Image(discard_txt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 63, "child": []}, {"x": 30, "type": "Label", "props": {"y": 120, "x": 361, "wordWrap": false, "width": 400, "var": "tip", "valign": "middle", "text": "No valid cards", "strokeColor": "#de7f63", "stroke": 2, "overflow": "visible", "name": "tip", "height": 40, "fontSize": 26, "color": "#731A16", "bold": true, "anchorY": 0.5, "anchorX": 0.5, "align": "center"}, "nodeParent": 62, "label": "Label(tip)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": [], "$HIDDEN": false}]}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"width": 78, "var": "btnDiscardAll_R", "stateNum": 1, "skin": "jackaro/btn_discard all.png", "right": 61, "name": "btnDiscardAll_R", "labelSize": 28, "labelColors": "#08846B", "height": 68, "centerY": 42}, "nodeParent": 1, "label": "Button(btnDiscardAll_R)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 65, "child": [{"x": 30, "type": "Label", "props": {"y": 58, "x": 39, "wordWrap": false, "width": 200, "valign": "middle", "text": "Discard All", "strokeColor": "#7C3614", "stroke": 3, "overflow": "visible", "name": "btnDiscardAll_txt", "height": 42, "fontSize": 20, "color": "#FFFFFF", "bold": true, "anchorY": 0.5, "anchorX": 0.5, "align": "center"}, "nodeParent": 65, "label": "Label(btnDiscardAll_txt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 66, "child": [], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"width": 78, "var": "btnDiscardAll_L", "stateNum": 1, "skin": "jackaro/btn_discard all.png", "name": "btnDiscardAll_R", "left": 61, "labelSize": 28, "labelColors": "#08846B", "height": 68, "centerY": 42}, "nodeParent": 1, "label": "Button(btnDiscardAll_L)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 30, "type": "Label", "props": {"y": 58, "x": 39, "wordWrap": false, "width": 200, "var": "btnDiscardAll_txt", "valign": "middle", "text": "Discard All", "strokeColor": "#7C3614", "stroke": 3, "overflow": "visible", "name": "btnDiscardAll_txt", "height": 42, "fontSize": 20, "color": "#FFFFFF", "bold": true, "anchorY": 0.5, "anchorX": 0.5, "align": "center"}, "nodeParent": 4, "label": "Label(btnDiscardAll_txt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 64, "child": [], "$HIDDEN": false}], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"width": 630, "var": "progress_box", "name": "progress_box", "height": 6, "centerY": 565, "centerX": 0}, "nodeParent": 1, "label": "Box(progress_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 60, "child": [], "$HIDDEN": true}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}