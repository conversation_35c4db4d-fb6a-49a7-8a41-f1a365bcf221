{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "referenceLines": null, "props": {"width": 750, "top": 0, "right": 0, "mouseEnabled": true, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "bg_mask", "top": 0, "skin": "public/maskBg.png", "right": 0, "name": "bg_mask", "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image(bg_mask)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"skin": "jackaro/bg_discard_self.png", "right": 22, "left": 22, "centerY": -78}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 62, "child": [{"x": 30, "type": "Image", "props": {"y": 43, "x": 241, "var": "selfForcedDiscard", "skin": "jackaro/txt_discard.png", "name": "selfForcedDiscard"}, "nodeParent": 62, "label": "Image(selfForcedDiscard)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 61, "child": []}, {"x": 30, "type": "Image", "props": {"y": 33, "x": 156, "var": "selfForcedIdent", "skin": "jackaro/icon_discard.png"}, "nodeParent": 62, "label": "Image(selfForcedIdent)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 63, "child": []}]}, {"x": 15, "type": "Box", "props": {"width": 630, "var": "progress_box", "name": "progress_box", "height": 6, "centerY": 565, "centerX": 0}, "nodeParent": 1, "label": "Box(progress_box)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 60, "child": [], "$HIDDEN": true}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}