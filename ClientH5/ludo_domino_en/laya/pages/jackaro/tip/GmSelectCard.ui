{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 58, "referenceLines": null, "props": {"width": 750, "top": 0, "right": 0, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"x": 0, "top": 0, "skin": "jackaro/bg_mask.png", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": false}, {"x": 15, "type": "HBox", "props": {"height": 68, "centerY": -360, "centerX": 5}, "nodeParent": 1, "label": "HBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 75, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 101, "width": 101, "var": "btnCard1", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnCard1", "labelSize": 28, "labelColors": "#08846B", "label": "A", "height": 68}, "nodeParent": 75, "label": "Button(btnCard1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 62, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"x": 274, "width": 101, "var": "btnCard2", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnCard2", "labelSize": 28, "labelColors": "#08846B", "label": "2", "height": 68}, "nodeParent": 75, "label": "Button(btnCard2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"x": 361, "width": 101, "var": "btnCard3", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnCard3", "labelSize": 28, "labelColors": "#08846B", "label": "3", "height": 68}, "nodeParent": 75, "label": "<PERSON>ton(btnCard3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 64, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"x": 447, "width": 101, "var": "btnCard4", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnCard4", "labelSize": 28, "labelColors": "#08846B", "label": "4", "height": 68}, "nodeParent": 75, "label": "<PERSON><PERSON>(btnCard4)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 66, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"x": 534, "width": 101, "var": "btnCard5", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnCard5", "labelSize": 28, "labelColors": "#08846B", "label": "5", "height": 68}, "nodeParent": 75, "label": "<PERSON>ton(btnCard5)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 67, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"x": 620, "width": 101, "var": "btnCard6", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnCard6", "labelSize": 28, "labelColors": "#08846B", "label": "6", "height": 68}, "nodeParent": 75, "label": "<PERSON><PERSON>(btnCard6)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 68, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 707, "width": 101, "var": "btnCard7", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnCard7", "labelSize": 28, "labelColors": "#08846B", "label": "7", "height": 68}, "nodeParent": 75, "label": "Button(btnCard7)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 69, "child": [], "$HIDDEN": false}]}, {"x": 15, "type": "HBox", "props": {"centerY": -260, "centerX": 5}, "nodeParent": 1, "label": "HBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 76, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 0, "width": 101, "var": "btnCard8", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnCard8", "labelSize": 28, "labelColors": "#08846B", "label": "8", "height": 68}, "nodeParent": 76, "label": "<PERSON><PERSON>(btnCard8)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 85, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 404, "width": 101, "var": "btnCard9", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnCard9", "labelSize": 28, "labelColors": "#08846B", "label": "9", "height": 68}, "nodeParent": 76, "label": "<PERSON><PERSON>(btnCard9)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 65, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"x": 414, "width": 101, "var": "btnCard10", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnCard10", "labelSize": 28, "labelColors": "#08846B", "label": "10", "height": 68}, "nodeParent": 76, "label": "Button(btnCard10)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 90, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 424, "width": 101, "var": "btnCard11", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnCard11", "labelSize": 28, "labelColors": "#08846B", "label": "J", "height": 68}, "nodeParent": 76, "label": "<PERSON>ton(btnCard11)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 91, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 434, "width": 101, "var": "btnCard12", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnCard12", "labelSize": 28, "labelColors": "#08846B", "label": "Q", "height": 68}, "nodeParent": 76, "label": "<PERSON><PERSON>(btnCard12)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 92, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 444, "width": 101, "var": "btnCard13", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnCard13", "labelSize": 28, "labelColors": "#08846B", "label": "K", "height": 68}, "nodeParent": 76, "label": "<PERSON>ton(btnCard13)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 93, "child": [], "$HIDDEN": false}]}, {"x": 15, "type": "HBox", "props": {"centerY": -100, "centerX": 11}, "nodeParent": 1, "label": "HBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 94, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 0, "width": 101, "var": "btnColor0", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnColor0", "labelSize": 28, "labelColors": "#08846B", "label": "黑桃", "height": 68}, "nodeParent": 94, "label": "Button(btnColor0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 95, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 404, "width": 101, "var": "btnColor1", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnColor1", "labelSize": 28, "labelColors": "#08846B", "label": "红桃", "height": 68}, "nodeParent": 94, "label": "Button(btnColor1)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 96, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"x": 414, "width": 101, "var": "btnColor2", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnColor2", "labelSize": 28, "labelColors": "#08846B", "label": "方片", "height": 68}, "nodeParent": 94, "label": "Button(btnColor2)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 97, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 424, "width": 101, "var": "btnColor3", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnColor3", "labelSize": 28, "labelColors": "#08846B", "label": "梅花", "height": 68}, "nodeParent": 94, "label": "Button(btnColor3)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 98, "child": [], "$HIDDEN": false}]}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"width": 202, "var": "btnExchange", "stateNum": 1, "skin": "jackaro/btn_green.png", "name": "btnExchange", "labelSize": 28, "labelColors": "#08846B", "label": "发送", "height": 68, "centerY": 517, "centerX": 31}, "nodeParent": 1, "label": "Button(btnExchange)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 99, "child": [], "$HIDDEN": false}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"width": 202, "var": "btnClose", "top": 130, "stateNum": 1, "skin": "jackaro/btn_green.png", "right": 54, "name": "btnClose", "labelSize": 28, "labelColors": "#08846B", "label": "关闭", "height": 68}, "nodeParent": 1, "label": "<PERSON><PERSON>(btnClose)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 100, "child": [], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}