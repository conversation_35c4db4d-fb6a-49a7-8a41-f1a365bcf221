{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 61, "referenceLines": null, "props": {"width": 750, "top": 0, "right": 0, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"top": 0, "skin": "public/maskBg.png", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"skin": "jackaro/bg_tips_discard.png", "height": 88, "centerY": -246, "centerX": 0}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 58, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Label", "props": {"wordWrap": true, "width": 708, "var": "tip", "valign": "middle", "text": "Please choose a card", "strokeColor": "#000000", "stroke": 2, "overflow": "visible", "name": "tip", "height": 88, "fontSize": 26, "color": "#ffffff", "centerY": -246, "centerX": 0, "bold": true, "anchorY": 1, "anchorX": 0.5, "align": "center"}, "nodeParent": 1, "label": "Label(tip)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"width": 630, "var": "progress_box", "name": "progress_box", "height": 6, "centerY": 565, "centerX": 0}, "nodeParent": 1, "label": "Box(progress_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 60, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"var": "handCard_box", "top": 0, "right": 0, "name": "handCard_box", "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(handCard_box)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 61, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}