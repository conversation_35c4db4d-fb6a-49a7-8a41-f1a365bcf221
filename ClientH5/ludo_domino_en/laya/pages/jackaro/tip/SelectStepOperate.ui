{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "referenceLines": null, "props": {"y": 0, "x": 0, "width": 750, "sceneColor": "#000000", "right": 0, "mouseThrough": false, "left": 0, "height": 488, "centerY": 390, "centerX": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "maskBg", "top": 0, "skin": "jackaro/alphaBg.png", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "disabled": true, "bottom": 0}, "nodeParent": 1, "label": "Image(maskBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 63, "child": []}, {"x": 15, "type": "Box", "props": {"width": 694, "var": "container", "height": 291, "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Box(container)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 62, "child": [{"x": 30, "type": "Image", "props": {"y": 0, "x": 0, "width": 694, "skin": "jackaro/bg_select_dot.png", "height": 291}, "nodeParent": 62, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 46, "child": []}, {"x": 30, "type": "Image", "props": {"y": 0, "x": 96, "skin": "jackaro/bg_title.png"}, "nodeParent": 62, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 64, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Label", "props": {"y": 8, "x": 8, "width": 678, "valign": "middle", "text": "What's your choice?", "strokeColor": "#575690", "stroke": 2, "height": 52, "fontSize": 26, "color": "#ffffff", "bold": true, "align": "center"}, "nodeParent": 62, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 50, "child": []}, {"x": 30, "type": "Box", "props": {"y": 66, "x": 0, "width": 694, "var": "operateHbox", "name": "operateHbox", "height": 184}, "nodeParent": 62, "label": "Box(operateHbox)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 59, "child": []}, {"x": 30, "type": "Box", "props": {"x": 24, "width": 658, "var": "progress_box", "name": "progress_box", "height": 12, "centerX": 0, "bottom": 15}, "nodeParent": 62, "label": "Box(progress_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 60, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}