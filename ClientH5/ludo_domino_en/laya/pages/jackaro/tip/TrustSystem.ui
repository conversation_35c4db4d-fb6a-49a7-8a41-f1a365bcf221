{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 46, "referenceLines": null, "props": {"y": 0, "x": 0, "width": 750, "sceneColor": "#000000", "right": 0, "mouseThrough": false, "left": 0, "height": 600, "centerY": 550, "centerX": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"width": 1024, "top": 0, "skin": "jackaro/bg_handPokerMask.png", "centerX": 0, "bottom": -100}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 46, "child": []}, {"x": 15, "type": "Image", "props": {"y": 32, "var": "iconAuto", "skin": "jackaro/AUTO_en.png", "centerX": 0}, "nodeParent": 1, "label": "Image(iconAuto)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 60, "child": []}, {"x": 15, "type": "VBox", "props": {"y": 100, "width": 750, "var": "vBox", "space": 10, "centerX": 0, "align": "center"}, "nodeParent": 1, "label": "VBox(vBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 68, "child": [{"x": 30, "type": "Label", "props": {"x": -195, "wordWrap": true, "width": 570, "var": "label_content", "valign": "middle", "text": "No EXP in AUTO mode. You will be kicked out due to prolonged use of AUTO mode.", "strokeColor": "#000000", "stroke": 2, "fontSize": 26, "color": "#ffffff", "align": "center"}, "nodeParent": 68, "label": "Label(label_content)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 50, "child": []}, {"x": 30, "type": "HBox", "props": {"visible": false, "var": "autoBox", "space": 8, "height": 40, "centerX": 0, "align": "bottom"}, "nodeParent": 68, "label": "HBox(autoBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 65, "child": [{"x": 45, "type": "Label", "props": {"var": "auto", "text": "AUTO:", "height": 31, "fontSize": 26, "color": "#CC4141", "align": "right"}, "nodeParent": 65, "label": "Label(auto)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 62, "child": []}, {"x": 45, "type": "Label", "props": {"var": "autoRatio", "text": "x/x", "height": 31, "fontSize": 26, "color": "#CC4141", "align": "left"}, "nodeParent": 65, "label": "Label(autoRatio)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 66, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"width": 248, "var": "btnTrust", "stateNum": 1, "skin": "jackaro/btn_yellow.png", "name": "btnTrust", "labelSize": 34, "labelColors": "#AB6E0F", "label": "Cancel", "height": 90}, "nodeParent": 68, "label": "Button(btnTrust)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 59, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}