{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 2, "referenceLines": null, "props": {"width": 750, "top": 0, "right": 0, "name": "ChooseChessOperateView", "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View(ChooseChessOperateView)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"var": "chessMask", "top": 0, "right": 0, "name": "chessMask", "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(chessMask)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 15, "type": "Box", "props": {"width": 630, "visible": false, "var": "progress_box", "name": "progress_box", "height": 6, "centerY": 565, "centerX": 0}, "nodeParent": 1, "label": "Box(progress_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 60, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}