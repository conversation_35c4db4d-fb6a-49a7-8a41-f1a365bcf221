{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 2, "referenceLines": null, "props": {"width": 750, "top": 0, "right": 0, "name": "ChooseChessOperateView", "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View(ChooseChessOperateView)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"var": "chessMask", "top": 0, "right": 0, "name": "chessMask", "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(chessMask)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 15, "type": "Image", "props": {"skin": "jackaro/bg_tips_discard.png", "height": 88, "centerY": -100, "centerX": 0}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 58, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Label", "props": {"wordWrap": true, "width": 795, "var": "tip", "valign": "middle", "text": "Choose a stone", "strokeColor": "#000000", "stroke": 2, "overflow": "visible", "name": "tip", "height": 88, "fontSize": 32, "color": "#ffffff", "centerY": -100, "centerX": 0, "bold": true, "anchorY": 1, "anchorX": 0.5, "align": "center"}, "nodeParent": 1, "label": "Label(tip)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 15, "type": "Box", "props": {"width": 630, "visible": true, "var": "progress_box", "name": "progress_box", "height": 6, "centerY": 565, "centerX": 0}, "nodeParent": 1, "label": "Box(progress_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 60, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}