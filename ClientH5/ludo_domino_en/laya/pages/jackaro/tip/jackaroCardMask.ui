{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 4, "referenceLines": null, "props": {"width": 750, "top": 0, "sceneColor": "#393025", "right": 0, "mouseThrough": false, "mouseEnabled": true, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"var": "boxMask", "top": 0, "right": 0, "name": "boxMask", "mouseThrough": false, "mouseEnabled": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(boxMask)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}