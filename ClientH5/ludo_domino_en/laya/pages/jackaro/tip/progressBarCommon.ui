{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "props": {"width": 658, "sceneColor": "#000000", "name": "bar", "height": 12, "centerX": 0}, "nodeParent": -1, "label": "View(bar)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "ProgressBar", "props": {"y": 0, "x": 0, "width": 658, "visible": true, "var": "progress_bar", "value": 0.5, "skin": "jackaro/progress_bar_icloud.png", "sizeGrid": "0,6,0,6", "name": "progress_bar", "height": 12}, "nodeParent": 1, "label": "ProgressBar(progress_bar)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": [], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}