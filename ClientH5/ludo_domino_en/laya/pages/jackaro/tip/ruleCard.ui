{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "props": {"width": 644, "sceneColor": "#000000", "mouseThrough": false, "height": 116, "centerY": -534, "centerX": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"skin": "jackaro/bg_card_intro.png"}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 102, "x": 23, "var": "img_poker", "skin": "jackaroCard/card_top_52000_1.png", "scaleY": 0.43, "scaleX": 0.43, "rotation": 0, "name": "img_poker", "anchorY": 1, "anchorX": 0}, "nodeParent": 1, "label": "Image(img_poker)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"x": 96, "var": "img_tip", "skin": "jackaro/pokerRule/en_J_c.png", "centerY": 0}, "nodeParent": 1, "label": "Image(img_tip)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}