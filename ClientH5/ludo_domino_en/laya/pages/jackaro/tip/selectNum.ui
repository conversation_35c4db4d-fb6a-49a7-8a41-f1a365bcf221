{"x": 0, "type": "View", "selectedBox": 64, "selecteID": 51, "referenceLines": null, "props": {"y": 0, "x": 0, "width": 750, "sceneColor": "#000000", "right": 0, "mouseThrough": false, "left": 0, "height": 488, "centerY": 390, "centerX": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "maskBg", "top": 0, "skin": "jackaro/alphaBg.png", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image(maskBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 61, "child": []}, {"x": 15, "type": "Image", "props": {"width": 694, "skin": "jackaro/bg_select_dot.png", "height": 291, "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 46, "child": [{"x": 30, "type": "Image", "props": {"y": 32, "x": 96, "skin": "jackaro/bg_title.png"}, "nodeParent": 46, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 65, "child": []}, {"x": 30, "type": "Label", "props": {"y": 44, "x": 13, "width": 668, "valign": "middle", "text": "What's your choice?", "strokeColor": "#575690", "stroke": 2, "height": 52, "fontSize": 26, "color": "#ffffff", "align": "center"}, "nodeParent": 46, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 50, "child": []}, {"x": 30, "type": "HBox", "props": {"y": 117, "x": 14, "width": 666, "space": 10, "centerX": 0}, "nodeParent": 46, "label": "HBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 64, "child": [{"x": 45, "type": "<PERSON><PERSON>", "props": {"x": 1, "width": 86, "var": "btn0", "stateNum": 1, "skin": "jackaro/btn_num.png", "labelStrokeColor": "#126D44", "labelStroke": 2, "labelSize": 36, "labelColors": "#ffffff,#ffffff,#ffffff,#ffffff", "labelBold": true, "label": 1, "height": 86}, "nodeParent": 64, "label": "Button(btn0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 47, "child": []}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"x": 2, "width": 86, "var": "btn1", "stateNum": 1, "skin": "jackaro/btn_num.png", "labelStrokeColor": "#126D44", "labelStroke": 2, "labelSize": 36, "labelColors": "#ffffff,#ffffff,#ffffff,#ffffff", "labelBold": true, "label": 2, "height": 86}, "nodeParent": 64, "label": "<PERSON><PERSON>(btn1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 51, "child": []}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"x": 3, "width": 86, "var": "btn2", "stateNum": 1, "skin": "jackaro/btn_num.png", "labelStrokeColor": "#126D44", "labelStroke": 2, "labelSize": 36, "labelColors": "#ffffff,#ffffff,#ffffff,#ffffff", "labelBold": true, "label": 3, "height": 86}, "nodeParent": 64, "label": "<PERSON><PERSON>(btn2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 52, "child": []}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"x": 4, "width": 86, "var": "btn3", "stateNum": 1, "skin": "jackaro/btn_num.png", "labelStrokeColor": "#126D44", "labelStroke": 2, "labelSize": 32, "labelColors": "#ffffff,#ffffff,#ffffff,#ffffff", "labelBold": true, "label": 4, "height": 86}, "nodeParent": 64, "label": "<PERSON><PERSON>(btn3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 53, "child": []}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"x": 5, "width": 86, "var": "btn4", "stateNum": 1, "skin": "jackaro/btn_num.png", "labelStrokeColor": "#126D44", "labelStroke": 2, "labelSize": 32, "labelColors": "#ffffff,#ffffff,#ffffff,#ffffff", "labelBold": true, "label": 5, "height": 86}, "nodeParent": 64, "label": "<PERSON><PERSON>(btn4)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 54, "child": []}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"x": 6, "width": 86, "var": "btn5", "stateNum": 1, "skin": "jackaro/btn_num.png", "labelStrokeColor": "#126D44", "labelStroke": 2, "labelSize": 32, "labelColors": "#ffffff,#ffffff,#ffffff,#ffffff", "labelBold": true, "label": 6, "height": 86}, "nodeParent": 64, "label": "<PERSON><PERSON>(btn5)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 55, "child": []}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"x": 7, "width": 86, "var": "btn6", "stateNum": 1, "skin": "jackaro/btn_num.png", "labelStrokeColor": "#126D44", "labelStroke": 2, "labelSize": 32, "labelColors": "#ffffff,#ffffff,#ffffff,#ffffff", "labelBold": true, "label": 7, "height": 86}, "nodeParent": 64, "label": "<PERSON><PERSON>(btn6)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 56, "child": []}]}]}, {"x": 15, "type": "Box", "props": {"y": 358, "width": 658, "var": "progress_box", "name": "progress_box", "height": 12, "centerX": 0}, "nodeParent": 1, "label": "Box(progress_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 60, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}