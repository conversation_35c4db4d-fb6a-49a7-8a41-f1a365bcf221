{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 13, "props": {"y": 0, "x": 0, "width": 300, "sceneWidth": 750, "sceneHeight": 1334, "sceneColor": "#000000", "height": 90}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "bg", "top": 0, "skin": "game/bg_bubble.png", "right": 0, "left": 22, "bottom": 0}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 14, "x": 36}, "nodeParent": 1, "label": "Box", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 9, "child": [{"x": 30, "type": "Image", "props": {"width": 54, "var": "_headUrl", "skin": "game/default_head.png", "height": 54}, "nodeParent": 9, "label": "Image(_headUrl)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"type": "Image", "props": {"width": 54, "skin": "game/default_head.png", "renderType": "mask", "height": 54}, "nodeParent": 4, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": false}], "$HIDDEN": false}]}, {"x": 15, "type": "Box", "props": {"y": 10, "x": 100}, "nodeParent": 1, "label": "Box", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 13, "child": [{"x": 30, "type": "Box", "props": {"y": -2, "x": 36, "width": 30, "visible": false, "var": "VIP", "height": 28}, "nodeParent": 13, "label": "Box(VIP)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}, {"x": 30, "type": "Box", "props": {"y": -2, "x": 0, "width": 30, "visible": false, "var": "RL", "height": 28}, "nodeParent": 13, "label": "Box(RL)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 30, "type": "HTMLDivElement", "props": {"y": 0, "x": 0, "width": 300, "var": "_nickName", "innerHTML": "1231321321", "height": 22}, "nodeParent": 13, "label": "HTMLDivElement(_nickName)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": [], "$LOCKED": true, "$HIDDEN": false}]}, {"x": 15, "type": "HTMLDivElement", "props": {"y": 39, "x": 102, "width": 400, "var": "_chat", "innerHTML": "1", "height": 40}, "nodeParent": 1, "label": "HTMLDivElement(_chat)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": [], "$LOCKED": true}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}