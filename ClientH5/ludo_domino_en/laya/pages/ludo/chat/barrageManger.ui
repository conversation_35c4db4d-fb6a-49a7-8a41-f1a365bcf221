{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 7, "props": {"width": 750, "sceneWidth": 750, "sceneHeight": 1334, "sceneColor": "#000000", "mouseThrough": true, "height": 550}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 1, "child": [], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}