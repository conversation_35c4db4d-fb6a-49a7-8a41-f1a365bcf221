{"x": 0, "type": "View", "selectedBox": 9, "selecteID": 13, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "mouseThrough": true, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "<PERSON><PERSON>", "props": {"x": 12, "var": "voice_btn", "stateNum": "1", "skin": "game/btn_nospeak.png", "bottom": 17}, "nodeParent": 1, "label": "<PERSON><PERSON>(voice_btn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"x": 144, "var": "fastChat_btn", "stateNum": "2", "skin": "game/btn_Expression.png", "bottom": 10}, "nodeParent": 1, "label": "Button(fastChat_btn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"x": 406, "visible": false, "var": "friendChat_btn", "stateNum": "2", "skin": "game/btn_friendChat.png", "bottom": 10}, "nodeParent": 1, "label": "Button(friendChat_btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 9, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": -4, "x": 86, "visible": false, "var": "friend_unreadTx", "stateNum": "1", "skin": "game/icon_mailpoint.png", "labelSize": 16, "labelPadding": "0", "labelFont": "<PERSON><PERSON>", "labelColors": "#ffffff", "labelBold": true, "labelAlign": "center", "label": "99+"}, "nodeParent": 9, "label": "<PERSON><PERSON>(friend_unreadTx)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": [], "$HIDDEN": false}]}, {"x": 15, "type": "Box", "props": {"x": 276, "width": 124, "var": "chatSp", "height": 80, "bottom": 10}, "nodeParent": 1, "label": "Box(chatSp)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 8, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 8, "var": "chat_btn", "stateNum": "2", "skin": "game/btn_chat.png"}, "nodeParent": 8, "label": "Button(chat_btn)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 4, "x": 86, "visible": false, "var": "unreadTx", "stateNum": "1", "skin": "game/icon_mailpoint.png", "labelSize": 16, "labelPadding": "0", "labelFont": "<PERSON><PERSON>", "labelColors": "#ffffff", "labelBold": true, "labelAlign": "center", "label": "99+"}, "nodeParent": 8, "label": "<PERSON><PERSON>(unreadTx)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": [], "$HIDDEN": false}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}