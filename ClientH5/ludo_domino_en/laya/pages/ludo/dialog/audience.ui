{"x": 0, "type": "Dialog", "selectedBox": 5, "selecteID": 7, "props": {"sceneWidth": 750, "sceneHeight": 1334, "sceneColor": "#000000"}, "nodeParent": -1, "label": "Dialog", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 718, "skin": "public/image_bg_title.png", "height": 740}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Image", "props": {"x": 6, "top": 90, "skin": "public/image_bg_content.png", "right": 6, "left": 6, "bottom": 10}, "nodeParent": 2, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}, {"x": 30, "type": "Label", "props": {"y": 17, "width": 676, "text": "Spectator", "strokeColor": "#34866e", "stroke": 6, "height": 53, "fontSize": 42, "color": "#ffffff", "centerX": 0, "bold": true, "align": "center"}, "nodeParent": 2, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}]}, {"x": 15, "type": "List", "props": {"y": 107, "x": 20, "width": 676, "var": "list", "vScrollBarSkin": "game/scroll.png", "spaceY": 10, "spaceX": 10, "height": 604}, "nodeParent": 1, "label": "List(list)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 30, "type": "Image", "props": {"y": 0, "x": 8, "width": 214, "skin": "public/bg_content26.png", "renderType": "render", "height": 212}, "nodeParent": 4, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": false, "hasChild": true, "compId": 5, "child": [{"x": 45, "type": "Label", "props": {"y": 158, "x": 0, "width": 213, "valign": "middle", "text": "NAME", "overflow": "hidden", "name": "nick<PERSON><PERSON>", "height": 20, "fontSize": 20, "color": "#626262", "bold": true, "align": "center"}, "nodeParent": 5, "label": "Label(nickName)", "isDirectory": false, "isAniNode": false, "hasChild": false, "compId": 7, "child": []}]}]}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 22, "x": 645, "var": "close_btn", "stateNum": "1", "skin": "public/btn_close.png", "name": "close"}, "nodeParent": 1, "label": "Button(close_btn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}