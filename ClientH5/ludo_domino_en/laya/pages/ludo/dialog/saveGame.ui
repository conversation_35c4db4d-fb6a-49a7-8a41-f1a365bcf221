{"x": 0, "type": "Dialog", "selectedBox": 2, "selecteID": 11, "props": {"sceneWidth": 750, "sceneHeight": 1334, "sceneColor": "#000000"}, "nodeParent": -1, "label": "Dialog", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 10, "x": 10, "width": 638, "skin": "public/image_bg_title.png", "height": 414, "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Image", "props": {"x": -198, "top": 10, "skin": "public/image_bg_content.png", "right": 6, "left": 6, "bottom": 10}, "nodeParent": 2, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": [], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 298, "x": 47, "width": 240, "var": "cancel", "skin": "game/btn_G.png", "name": "close", "labelStrokeColor": "#61f2ca", "labelStroke": 2, "labelSize": 40, "labelColors": "#08846b", "label": "Cancel", "height": 82}, "nodeParent": 2, "label": "<PERSON><PERSON>(cancel)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}, {"x": 30, "type": "TextInput", "props": {"y": 120, "wordWrap": false, "width": 544, "var": "input", "type": "text", "text": "15:43:56  May 17 2019", "skin": "game/pic_input_bg.png", "sizeGrid": "16,13,15,15", "promptColor": "#ffffff", "maxChars": 40, "height": 82, "fontSize": 32, "color": "#ffffff", "centerX": 0, "align": "center"}, "nodeParent": 2, "label": "TextInput(input)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 298, "x": 350, "width": 240, "var": "save", "skin": "game/btn_y.png", "labelStrokeColor": "#fff08b", "labelStroke": 2, "labelSize": 40, "labelColors": "#ab6e0f", "label": "Confirm", "height": 82}, "nodeParent": 2, "label": "<PERSON><PERSON>(save)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}, {"x": 30, "type": "CheckBox", "props": {"y": 234, "x": 250, "width": 60, "var": "isSave", "stateNum": "2", "skin": "game/check_lattice.png", "height": 57}, "nodeParent": 2, "label": "CheckBox(isSave)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 30, "type": "Label", "props": {"y": 36, "x": 18, "wordWrap": true, "width": 603, "valign": "middle", "text": "Leave the current game?", "height": 62, "fontSize": 38, "color": "#428371", "centerX": 0, "bold": true, "align": "center"}, "nodeParent": 2, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 30, "type": "Label", "props": {"y": 232, "x": 305, "wordWrap": true, "width": 286, "valign": "middle", "text": "Save", "height": 37, "fontSize": 38, "color": "#626262", "bold": true, "align": "left"}, "nodeParent": 2, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}