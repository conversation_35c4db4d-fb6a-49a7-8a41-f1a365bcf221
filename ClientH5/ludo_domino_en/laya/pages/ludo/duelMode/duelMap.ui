{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 8, "props": {"width": 750, "sceneColor": "#000000", "height": 750, "centerY": 0, "centerX": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "bg", "skin": "game/Base_13000.png", "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 15, "type": "Image", "props": {"y": 375, "x": 375, "var": "check", "skin": "duel/Checkerboard_duel.png", "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(check)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 375, "x": 375, "width": 736, "var": "sign_box", "height": 736, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(sign_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}, {"x": 15, "type": "Box", "props": {"y": 375, "x": 375, "width": 736, "var": "props_box", "height": 736, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(props_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": [], "$LOCKED": true}, {"x": 15, "type": "Box", "props": {"y": 375, "x": 375, "width": 736, "var": "chess_box", "height": 736, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(chess_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 15, "type": "Box", "props": {"y": 375, "x": 375, "width": 736, "var": "ani_box", "height": 736, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(ani_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 336, "x": 310, "width": 130, "visible": false, "var": "close_socket", "skin": "game/btn_y.png", "labelSize": 24, "label": "断开网络", "height": 77}, "nodeParent": 1, "label": "But<PERSON>(close_socket)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}