{"x": 0, "type": "View", "selectedBox": 10, "selecteID": 12, "props": {"width": 204, "sceneColor": "#000000", "height": 204}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 102, "x": 102, "var": "bg", "skin": "duel/duel_0.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 0, "x": 0, "width": 204, "var": "info", "height": 204}, "nodeParent": 1, "label": "Box(info)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 10, "child": [{"x": 30, "type": "Image", "props": {"var": "score_bg", "skin": "duel/score_0.png", "centerX": 0, "bottom": 0}, "nodeParent": 10, "label": "Image(score_bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 67, "x": 39, "var": "no", "skin": "duel/no_0.png"}, "nodeParent": 10, "label": "Image(no)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 30, "type": "Image", "props": {"y": 67, "x": 125, "var": "rank_img", "skin": "duel/kill_0_0.png"}, "nodeParent": 10, "label": "Image(rank_img)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 172, "x": 25, "skin": "duel/duel_knife.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 10, "label": "Image", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 5, "child": [{"type": "EffectAnimation", "props": {"var": "ani_knife", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/bounceOutIn1.efc"}, "nodeParent": 5, "label": "EffectAnimation(ani_knife)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Label", "props": {"y": 170, "x": 99, "width": 125, "var": "kill_score", "valign": "middle", "text": "-", "strokeColor": "#000000", "stroke": 3, "height": 40, "fontSize": 40, "color": "#ffffff", "bold": true, "anchorY": 0.5, "anchorX": 0.5, "align": "center"}, "nodeParent": 10, "label": "Label(kill_score)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"type": "EffectAnimation", "props": {"var": "ani_rank", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/bounceOutIn2.efc"}, "nodeParent": 6, "label": "EffectAnimation(ani_rank)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}], "$HIDDEN": false}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}