{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 2, "props": {"width": 204, "sceneColor": "#000000", "height": 78}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 3, "x": 38, "width": 165, "skin": "duel/duel_clock_bg.png", "height": 70}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Label", "props": {"y": 3, "x": 5, "width": 154, "valign": "middle", "text": "Countdown", "height": 30, "fontSize": 22, "color": "#FBDD56", "bold": true, "align": "center"}, "nodeParent": 2, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Label", "props": {"y": 34, "x": 8, "width": 146, "var": "timeTx", "valign": "middle", "text": "--:--", "height": 31, "fontSize": 30, "color": "#FFFFFF", "bold": true, "align": "center"}, "nodeParent": 2, "label": "Label(timeTx)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": [], "$HIDDEN": false}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}