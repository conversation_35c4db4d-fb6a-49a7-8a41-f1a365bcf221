{"x": 0, "type": "View", "selectedBox": 2, "selecteID": 3, "props": {"y": 23, "x": 23, "width": 46, "sceneColor": "#000000", "height": 46, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"width": 46, "var": "prop_icon", "skin": "public/propIcon.png", "height": 46, "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image(prop_icon)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Label", "props": {"y": 29, "x": 0, "width": 46, "var": "_num", "strokeColor": "#4D4D4D", "stroke": 1, "height": 16, "fontSize": 16, "color": "#FFF95B", "bold": true, "align": "center"}, "nodeParent": 2, "label": "Label(_num)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}