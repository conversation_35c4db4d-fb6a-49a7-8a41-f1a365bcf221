{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "props": {"visible": false, "sceneWidth": 268, "sceneHeight": 80, "sceneColor": "#000000"}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "skin": "game/nextturn.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}], "animations": [{"nodes": [{"target": 2, "keyframes": {"scaleY": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 0}, {"value": 1.2, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 5}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 7}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 20}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 23}], "scaleX": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 0}, {"value": 1.2, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 5}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 7}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 20}, {"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 23}]}}], "name": "show", "id": 1, "frameRate": 24, "action": 0}]}