{"x": 0, "type": "View", "selectedBox": 2, "selecteID": 15, "props": {"width": 736, "sceneColor": "#ffffff", "height": 736}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"width": 736, "height": 736}, "nodeParent": 1, "label": "Box", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Image", "props": {"y": 424, "x": -4, "width": 318, "visible": true, "var": "fixed_me", "skin": "curtain/img_rect_1.png", "height": 320}, "nodeParent": 2, "label": "Image(fixed_me)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 30, "type": "Image", "props": {"y": -13, "x": 429, "width": 318, "visible": false, "var": "fixed_mate", "skin": "curtain/img_rect_1.png", "height": 320}, "nodeParent": 2, "label": "Image(fixed_mate)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}, {"x": 30, "type": "Image", "props": {"width": 190, "visible": false, "var": "_terminal", "skin": "curtain/img_rect_2.png", "height": 190, "centerY": 0, "centerX": 0}, "nodeParent": 2, "label": "Image(_terminal)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 30, "type": "Image", "props": {"y": -96, "x": -86, "width": 922, "visible": false, "var": "globle_eye", "skin": "curtain/img_rect_1.png", "height": 914}, "nodeParent": 2, "label": "Image(globle_eye)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": [], "$HIDDEN": true}, {"x": 30, "type": "Image", "props": {"y": 368, "x": 368, "width": 608, "visible": false, "var": "prop_light_1", "skin": "curtain/slight.png", "pivotY": 662, "pivotX": 614, "height": 656, "alpha": 0}, "nodeParent": 2, "label": "Image(prop_light_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": []}, {"x": 30, "type": "Image", "props": {"y": 368, "x": 368, "width": 608, "visible": false, "var": "prop_light_2", "skin": "curtain/slight.png", "rotation": 90, "pivotY": 662, "pivotX": 614, "height": 656, "alpha": 0}, "nodeParent": 2, "label": "Image(prop_light_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}, {"x": 30, "type": "Image", "props": {"y": 368, "x": 368, "width": 608, "visible": false, "var": "prop_light_3", "skin": "curtain/slight.png", "rotation": 180, "pivotY": 662, "pivotX": 614, "height": 656, "alpha": 0}, "nodeParent": 2, "label": "Image(prop_light_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 30, "type": "Image", "props": {"y": 368, "x": 368, "width": 608, "visible": false, "var": "prop_light_0", "skin": "curtain/slight.png", "rotation": 270, "pivotY": 662, "pivotX": 614, "height": 656, "alpha": 0}, "nodeParent": 2, "label": "Image(prop_light_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}