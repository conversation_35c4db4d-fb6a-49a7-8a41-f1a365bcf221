{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "props": {"width": 736, "sceneColor": "#000000", "mouseThrough": true, "height": 736}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 368, "x": 368, "visible": false, "skin": "game/Checkerboard_13000.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Image", "props": {"y": 368, "x": 368, "width": 63, "var": "light0", "skin": "game/light_d.png", "scaleX": -1, "pivotY": -11, "pivotX": 11, "height": 62}, "nodeParent": 1, "label": "Image(light0)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"x": 30, "type": "Image", "props": {"y": -32, "x": -10, "skin": "game/ray.png", "blendMode": "lighter"}, "nodeParent": 6, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}]}, {"x": 15, "type": "Box", "props": {"y": 368, "x": 368, "width": 248, "var": "light1", "pivotY": 232, "pivotX": 244, "height": 231}, "nodeParent": 1, "label": "Box(light1)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 11, "child": [{"x": 30, "type": "Image", "props": {"y": 254, "x": 266, "skin": "game/ray.png", "rotation": 180, "blendMode": "lighter"}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 30, "type": "Image", "props": {"y": 160, "x": 178, "skin": "game/light_u.png"}, "nodeParent": 11, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": [], "$HIDDEN": false}]}, {"x": 15, "type": "Box", "props": {"y": 368, "x": 368, "width": 248, "var": "light2", "scaleX": -1, "pivotY": 232, "pivotX": 244, "height": 231}, "nodeParent": 1, "label": "Box(light2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 15, "child": [{"x": 30, "type": "Image", "props": {"y": 254, "x": 266, "skin": "game/ray.png", "rotation": 180, "blendMode": "lighter"}, "nodeParent": 15, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}, {"x": 30, "type": "Image", "props": {"y": 160, "x": 178, "skin": "game/light_u.png"}, "nodeParent": 15, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": [], "$HIDDEN": false}]}, {"x": 15, "type": "Image", "props": {"y": 368, "x": 368, "width": 63, "var": "light3", "skin": "game/light_d.png", "pivotY": -11, "pivotX": 11, "height": 62}, "nodeParent": 1, "label": "Image(light3)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 30, "type": "Image", "props": {"y": -32, "x": -10, "skin": "game/ray.png", "blendMode": "lighter"}, "nodeParent": 4, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}