{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 54, "props": {"width": 750, "sceneColor": "#000000", "height": 750, "centerY": 0, "centerX": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "night_bg", "skin": "game/Base_13000.png", "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image(night_bg)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"y": 375, "x": 375, "width": 736, "var": "night", "height": 736, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(night)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 32, "child": [{"x": 30, "type": "Image", "props": {"y": 368, "x": 368, "var": "night_check", "skin": "game/Checkerboard_13000.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 32, "label": "Image(night_check)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 393, "x": 106, "width": 44, "skin": "game/safe_icon.png", "height": 44}, "nodeParent": 32, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 28, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 106, "x": 298, "width": 44, "skin": "game/safe_icon.png", "height": 44}, "nodeParent": 32, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}, {"x": 30, "type": "Image", "props": {"y": 298, "x": 586, "width": 44, "skin": "game/safe_icon.png", "height": 44}, "nodeParent": 32, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 30, "child": []}, {"x": 30, "type": "Image", "props": {"y": 586, "x": 394, "width": 44, "skin": "game/safe_icon.png", "height": 44}, "nodeParent": 32, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 31, "child": []}, {"x": 30, "type": "Image", "props": {"y": 368, "x": 368, "width": 2000, "var": "night_mask", "skin": "game/hei.png", "sizeGrid": "0,0,0,0", "height": 2000, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 32, "label": "Image(night_mask)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": [], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 375, "x": 375, "width": 736, "var": "curtain", "height": 736, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(curtain)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 8, "child": [{"x": 30, "type": "Image", "props": {"y": -29, "x": -26, "var": "bg", "skin": "game/Base_13000.png", "centerY": 0, "centerX": 0}, "nodeParent": 8, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 368, "x": 368, "var": "check", "skin": "game/Checkerboard_13000.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 8, "label": "Image(check)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Box", "props": {"y": 368, "x": 187, "width": 736, "var": "sign_box", "height": 736, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 8, "label": "Box(sign_box)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 5, "child": [{"x": 45, "type": "Image", "props": {"y": 393, "x": 106, "width": 44, "skin": "game/safe_icon.png", "height": 44}, "nodeParent": 5, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}, {"x": 45, "type": "Image", "props": {"y": 106, "x": 298, "width": 44, "skin": "game/safe_icon.png", "height": 44}, "nodeParent": 5, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 45, "type": "Image", "props": {"y": 298, "x": 586, "width": 44, "skin": "game/safe_icon.png", "height": 44}, "nodeParent": 5, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": []}, {"x": 45, "type": "Image", "props": {"y": 586, "x": 394, "width": 44, "skin": "game/safe_icon.png", "height": 44}, "nodeParent": 5, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Box", "props": {"y": 368, "x": 187, "width": 736, "var": "props_box", "height": 736, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 8, "label": "Box(props_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": [], "$LOCKED": true}, {"x": 30, "type": "Box", "props": {"y": 368, "x": 187, "width": 736, "var": "chess_box", "height": 736, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 8, "label": "Box(chess_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 375, "x": 375, "width": 736, "var": "ani_box", "height": 736, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(ani_box)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 7, "child": [{"x": 30, "type": "Image", "props": {"y": 676, "x": 51, "var": "visibility_bg", "skin": "game/dibu.png"}, "nodeParent": 7, "label": "Image(visibility_bg)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 15, "child": [{"type": "Image", "props": {"y": 10, "x": 16, "var": "visibility_img", "skin": "game/visibility.png"}, "nodeParent": 15, "label": "Image(visibility_img)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}, {"type": "Label", "props": {"y": 0, "x": 115, "width": 60, "var": "visibility_num", "valign": "bottom", "text": "3x3", "height": 33, "fontSize": 26, "color": "#ffffff", "align": "center"}, "nodeParent": 15, "label": "Label(visibility_num)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Box", "props": {"y": 368, "x": 368, "width": 100, "visible": false, "var": "light0", "pivotY": 0, "pivotX": 100, "mouseThrough": true, "height": 100}, "nodeParent": 7, "label": "Box(light0)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 47, "child": [{"x": 45, "type": "Image", "props": {"y": -22, "x": 116, "visible": false, "var": "ray0", "skin": "game/ray.png", "scaleX": -1, "mouseThrough": true, "blendMode": "lighter"}, "nodeParent": 47, "label": "Image(ray0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 40, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "Box", "props": {"y": 368, "x": 368, "width": 100, "visible": false, "var": "light1", "pivotY": 100, "pivotX": 100, "height": 100}, "nodeParent": 7, "label": "Box(light1)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 37, "child": [{"x": 45, "type": "Image", "props": {"y": 121, "x": 118, "visible": false, "var": "ray1", "skin": "game/ray.png", "rotation": 180, "blendMode": "lighter"}, "nodeParent": 37, "label": "Image(ray1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 41, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "Box", "props": {"y": 368, "x": 368, "width": 100, "visible": false, "var": "light3", "scaleX": -1, "pivotY": 0, "pivotX": 100, "mouseThrough": true, "height": 100}, "nodeParent": 7, "label": "Box(light3)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 50, "child": [{"x": 45, "type": "Image", "props": {"y": -22, "x": 116, "visible": false, "var": "ray3", "skin": "game/ray.png", "scaleX": -1, "mouseThrough": true, "blendMode": "lighter"}, "nodeParent": 50, "label": "Image(ray3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 51, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "Box", "props": {"y": 368, "x": 368, "width": 100, "visible": false, "var": "light2", "scaleX": -1, "pivotY": 100, "pivotX": 100, "height": 100}, "nodeParent": 7, "label": "Box(light2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 52, "child": [{"x": 45, "type": "Image", "props": {"y": 121, "x": 118, "visible": false, "var": "ray2", "skin": "game/ray.png", "rotation": 180, "blendMode": "lighter"}, "nodeParent": 52, "label": "Image(ray2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 53, "child": [], "$HIDDEN": false}]}], "$HIDDEN": true}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}