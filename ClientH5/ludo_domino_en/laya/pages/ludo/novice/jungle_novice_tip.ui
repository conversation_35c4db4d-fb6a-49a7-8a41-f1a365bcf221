{"x": 0, "type": "View", "selectedBox": 16, "selecteID": 19, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "mouseThrough": true, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 40, "var": "next_btn", "skin": "game/SkipTutorial.png", "right": -5}, "nodeParent": 1, "label": "Image(next_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"type": "Label", "props": {"y": 5, "x": 29, "width": 144, "valign": "middle", "text": "<PERSON><PERSON>", "height": 43, "fontSize": 20, "color": "#9b9b9b", "align": "center"}, "nodeParent": 3, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}]}, {"x": 15, "type": "Animation", "props": {"y": -46, "x": -279, "visible": false, "var": "finger", "source": "publics/ani/finger.ani", "autoPlay": true}, "nodeParent": 1, "label": "Animation(finger)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}, {"x": 15, "type": "HBox", "props": {"var": "tab_box", "space": 8, "height": 118, "centerY": -440, "centerX": 0}, "nodeParent": 1, "label": "HBox(tab_box)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 15, "type": "Image", "props": {"visible": false, "var": "mask_bg", "top": 0, "skin": "public/alphaBg80.png", "right": 0, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image(mask_bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 8, "child": [{"x": 30, "type": "Image", "props": {"width": 734, "var": "novice_end", "skin": "jungleNovice/jungle_result_bg.png", "height": 396, "centerY": 0, "centerX": 0}, "nodeParent": 8, "label": "Image(novice_end)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 16, "child": [{"x": 45, "type": "Label", "props": {"y": 34, "wordWrap": true, "width": 437, "var": "end_tip", "valign": "middle", "text": "Congrats, warriors! You have finished the tutorial, so start the game right now!", "strokeColor": "#146E78", "stroke": 2, "right": 56, "height": 166, "fontSize": 28, "color": "#ffffff", "bold": true}, "nodeParent": 16, "label": "Label(end_tip)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 202, "x": 336, "width": 228, "var": "btn_playNow", "skin": "public/btn_y.png", "labelStrokeColor": "#FFF08B", "labelStroke": 2, "labelSize": 34, "labelColors": "#AB6E0F", "labelBold": true, "label": "Play Now", "height": 77}, "nodeParent": 16, "label": "Button(btn_playNow)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 286, "x": 336, "width": 228, "var": "btn_watchAgain", "skin": "public/btn_G.png", "labelStrokeColor": "#61F2CA", "labelStroke": 2, "labelSize": 30, "labelColors": "#08846B", "labelBold": true, "label": "Watch Again", "height": 77}, "nodeParent": 16, "label": "Button(btn_watchAgain)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 19, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"width": 734, "var": "net_error", "skin": "jungleNovice/jungle_net_error.png", "height": 418, "centerY": 0, "centerX": 0}, "nodeParent": 8, "label": "Image(net_error)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 20, "child": [{"x": 45, "type": "Label", "props": {"y": 34, "wordWrap": true, "width": 437, "var": "error_tip", "valign": "middle", "text": "Oops, the network connection failed. Please check the connection and refresh.", "strokeColor": "#146E78", "stroke": 2, "right": 56, "height": 166, "fontSize": 28, "color": "#ffffff", "bold": true}, "nodeParent": 20, "label": "Label(error_tip)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 202, "x": 336, "width": 228, "var": "btn_tryAgain", "skin": "activity/btn_y.png", "labelStrokeColor": "#FFF08B", "labelStroke": 2, "labelSize": 34, "labelColors": "#AB6E0F", "labelBold": true, "label": "Refresh", "height": 77}, "nodeParent": 20, "label": "Button(btn_tryAgain)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 286, "x": 336, "width": 228, "var": "btn_back", "skin": "domino/btn_G.png", "labelStrokeColor": "#61F2CA", "labelStroke": 2, "labelSize": 34, "labelColors": "#08846B", "labelBold": true, "label": "Quit", "height": 77}, "nodeParent": 20, "label": "<PERSON><PERSON>(btn_back)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 23, "child": []}], "$HIDDEN": true}], "$HIDDEN": true}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}