{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 4, "props": {"width": 114, "sceneColor": "#000000", "height": 118}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "var": "bg", "skin": "jungleNovice/bg_disabled.png"}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 15, "type": "Image", "props": {"var": "icon", "skin": "jungleNovice/ico_cloud.png", "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image(icon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 15, "type": "Image", "props": {"y": -34, "visible": false, "var": "abled", "skin": "jungleNovice/arrow.png", "centerX": 0}, "nodeParent": 1, "label": "Image(abled)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}