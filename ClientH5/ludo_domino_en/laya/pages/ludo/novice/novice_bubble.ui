{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 12, "props": {"width": 722, "sceneColor": "#000000", "height": 168, "centerX": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"y": -74, "x": 0, "width": 722, "var": "left_side", "height": 242}, "nodeParent": 1, "label": "Box(left_side)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"type": "Image", "props": {"y": 75, "x": 91, "width": 634, "skin": "jungleNovice/tip_bg.png", "height": 168}, "nodeParent": 4, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"type": "Image", "props": {"x": 0, "skin": "jungleNovice/tip_house.png", "bottom": -15}, "nodeParent": 4, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": -74, "x": 0, "width": 722, "visible": false, "var": "right_side", "height": 242}, "nodeParent": 1, "label": "Box(right_side)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 5, "child": [{"type": "Image", "props": {"y": 75, "x": 317, "width": 634, "skin": "jungleNovice/tip_bg.png", "scaleX": -1, "height": 168, "anchorX": 0.5}, "nodeParent": 5, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}, {"type": "Image", "props": {"x": 629, "skin": "jungleNovice/tip_house.png", "scaleX": -1, "bottom": -15, "anchorX": 0.5}, "nodeParent": 5, "label": "Image", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}], "$HIDDEN": true}, {"x": 15, "type": "Label", "props": {"y": 21, "wordWrap": true, "width": 525, "var": "tip", "valign": "middle", "right": 26, "height": 129, "fontSize": 26, "color": "#626262"}, "nodeParent": 1, "label": "Label(tip)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}