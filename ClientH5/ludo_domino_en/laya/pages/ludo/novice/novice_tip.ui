{"x": 0, "type": "View", "selectedBox": 2, "selecteID": 4, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "mouseThrough": true, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"visible": false, "var": "mask_bg", "top": 0, "skin": "public/alphaBg80.png", "right": 0, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image(mask_bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 8, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"width": 300, "visible": false, "var": "btn_ok", "skin": "game/btn_y.png", "labelSize": 40, "labelColors": "#ab6e0f", "label": "Confirm", "height": 110, "centerX": 0, "bottom": 420}, "nodeParent": 8, "label": "Button(btn_ok)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 1280, "x": 72, "width": 118, "var": "voice_btn", "stateNum": "2", "skin": "game/btn_speak.png", "mouseEnabled": false, "height": 74, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 8, "label": "<PERSON><PERSON>(voice_btn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 82, "var": "tip_ui", "skin": "game/banner_guide.png", "right": 10, "left": 10, "height": 210}, "nodeParent": 1, "label": "Image(tip_ui)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Label", "props": {"y": 66, "wordWrap": true, "width": 480, "var": "tip", "valign": "middle", "text": "Your microphone is open. Try voice chatting with other players. Have fun!", "right": 30, "left": 220, "height": 125, "fontSize": 32, "color": "#626262", "align": "left"}, "nodeParent": 2, "label": "Label(tip)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 40, "var": "next_btn", "skin": "game/SkipTutorial.png", "right": -5}, "nodeParent": 1, "label": "Image(next_btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"x": 30, "type": "Label", "props": {"y": 5, "x": 29, "width": 144, "valign": "middle", "text": "<PERSON><PERSON>", "height": 43, "fontSize": 20, "color": "#9b9b9b", "align": "center"}, "nodeParent": 3, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}]}, {"x": 15, "type": "Animation", "props": {"y": 631, "x": 371, "visible": false, "var": "finger", "source": "publics/ani/finger.ani", "autoPlay": true}, "nodeParent": 1, "label": "Animation(finger)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}