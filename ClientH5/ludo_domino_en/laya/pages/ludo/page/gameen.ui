{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 77, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "bg", "top": 0, "skin": "game/BG_13000.png", "right": 0, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image(bg)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 15, "type": "Image", "props": {"var": "furniture", "skin": "game/furniture.png", "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image(furniture)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 120, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 0, "x": 0, "var": "map_layer", "top": 0, "right": 0, "mouseThrough": true, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Box(map_layer)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 44, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 101, "x": 0, "var": "players_layer", "right": 0, "mouseThrough": true, "mouseEnabled": true, "left": 0, "height": 1133, "centerY": 0}, "nodeParent": 1, "label": "Box(players_layer)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 42, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 993, "x": 44, "var": "myHander", "mouseThrough": true, "left": 44, "centerY": 431}, "nodeParent": 1, "label": "Box(myHander)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 77, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 143, "x": 266, "var": "reset", "stateNum": "1", "skin": "game/btn_refresh_on.png"}, "nodeParent": 77, "label": "<PERSON><PERSON>(reset)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 19, "child": [{"x": 45, "type": "Image", "props": {"y": 24, "x": 38, "width": 16, "var": "reset<PERSON>oin", "skin": "game/Diamonds.png", "height": 19}, "nodeParent": 19, "label": "Image(resetCoin)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"x": 45, "type": "Label", "props": {"y": 17, "x": 5, "width": 36, "var": "useCoin", "valign": "middle", "text": "-", "height": 28, "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 19, "label": "Label(useCoin)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 73, "x": -3, "visible": false, "var": "trusteeship", "skin": "game/auto.png"}, "nodeParent": 77, "label": "Image(trusteeship)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 22, "child": [{"x": 45, "type": "Label", "props": {"y": 7, "x": 8, "width": 68, "text": "AUTO", "strokeColor": "#000000", "stroke": 2, "height": 24, "fontSize": 22, "color": "#ffffff", "bold": true, "align": "center"}, "nodeParent": 22, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 94, "child": []}, {"x": 45, "type": "CheckBox", "props": {"y": 3, "x": 81, "width": 50, "var": "trust", "stateNum": "2", "skin": "game/check_auto.png", "height": 33}, "nodeParent": 22, "label": "CheckBox(trust)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 58, "child": []}, {"x": 45, "type": "Animation", "props": {"y": 0, "x": 67, "var": "trust_jump", "source": "publics/ani/jump.ani"}, "nodeParent": 22, "label": "Animation(trust_jump)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 62, "child": []}, {"x": 45, "type": "Animation", "props": {"y": 18, "x": 96, "visible": false, "var": "trust_wait", "source": "publics/ani/relogin.ani", "scaleY": 0.3, "scaleX": 0.3}, "nodeParent": 22, "label": "Animation(trust_wait)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 85, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": -213, "x": 183, "width": 490, "visible": false, "var": "reset_info", "skin": "game/BG_Expression.png", "height": 312}, "nodeParent": 77, "label": "Image(reset_info)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 63, "child": [{"type": "Image", "props": {"y": 58, "x": 22, "width": 448, "skin": "game/table_words.png", "height": 100}, "nodeParent": 63, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 64, "child": []}, {"type": "Image", "props": {"y": 309, "x": 98, "skin": "game/BG_Expression_V.png"}, "nodeParent": 63, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 65, "child": []}, {"type": "Label", "props": {"y": 3, "x": 33, "width": 428, "var": "reset_title", "valign": "middle", "text": "<PERSON><PERSON>", "strokeColor": "#575690", "stroke": 2, "height": 52, "fontSize": 35, "color": "#ffffff", "align": "center"}, "nodeParent": 63, "label": "Label(reset_title)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 66, "child": []}, {"type": "Label", "props": {"y": 57, "x": 31, "wordWrap": true, "width": 433, "var": "reset_body", "valign": "middle", "text": "You can undo dice within 2 secs.", "height": 101, "fontSize": 25, "color": "#f3f3f3", "align": "center"}, "nodeParent": 63, "label": "Label(reset_body)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 67, "child": []}, {"type": "Label", "props": {"y": 217, "x": 39, "text": "<PERSON><PERSON>", "height": 32, "fontSize": 23, "color": "#39365e", "align": "right"}, "nodeParent": 63, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 70, "child": []}, {"type": "Label", "props": {"y": 170, "x": 39, "text": "<PERSON><PERSON> Left for this round", "height": 32, "fontSize": 23, "color": "#39365e", "align": "right"}, "nodeParent": 63, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 111, "child": []}, {"type": "Label", "props": {"y": 164, "x": 423, "width": 44, "var": "round_count_text", "valign": "middle", "text": "0", "strokeColor": "#575690", "stroke": 3, "height": 44, "fontSize": 39, "color": "#ffffff", "align": "center"}, "nodeParent": 63, "label": "Label(round_count_text)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 110, "child": []}, {"type": "Label", "props": {"y": 211, "x": 423, "width": 44, "var": "count_text", "valign": "middle", "text": "0", "strokeColor": "#575690", "stroke": 3, "height": 44, "fontSize": 39, "color": "#ffffff", "align": "center"}, "nodeParent": 63, "label": "Label(count_text)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 107, "child": []}, {"type": "Label", "props": {"y": 265, "x": 39, "width": 172, "var": "isreset_tx", "text": "Undo ON", "height": 32, "fontSize": 23, "color": "#39365e", "align": "left"}, "nodeParent": 63, "label": "Label(isreset_tx)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 71, "child": []}, {"type": "CheckBox", "props": {"y": 244, "x": 396, "width": 100, "var": "isreset", "stateNum": "2", "skin": "game/undoon.png", "height": 90}, "nodeParent": 63, "label": "CheckBox(isreset)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 68, "child": []}, {"type": "Image", "props": {"y": 178, "x": 22, "skin": "game/spot.png"}, "nodeParent": 63, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 113, "child": []}, {"type": "Image", "props": {"y": 224, "x": 22, "skin": "game/spot.png"}, "nodeParent": 63, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 114, "child": []}, {"type": "Image", "props": {"y": 270, "x": 22, "skin": "game/spot.png"}, "nodeParent": 63, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 115, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": -80, "x": 239, "visible": false, "var": "quick_buy_btn", "skin": "public/Recharge_label.png"}, "nodeParent": 77, "label": "Image(quick_buy_btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 108, "child": [{"x": 45, "type": "Image", "props": {"y": 201, "x": 45, "skin": "public/Recharge_label_1.png"}, "nodeParent": 108, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 121, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Animation", "props": {"y": 82, "x": 190, "visible": false, "var": "throw_jump", "source": "publics/ani/jump.ani", "scaleY": 1.6, "scaleX": 1.6}, "nodeParent": 77, "label": "Animation(throw_jump)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 61, "child": [], "$HIDDEN": false}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}