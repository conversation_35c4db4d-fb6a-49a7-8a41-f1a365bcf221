{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 4, "props": {"width": 750, "sceneColor": "#000000", "height": 1334}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 110, "x": -7, "width": 750, "skin": "public/tip_bg.png", "height": 1242}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"x": 15, "type": "List", "props": {"y": 141, "x": 28, "width": 696, "var": "word_list", "vScrollBarSkin": "game/scroll.png", "spaceY": 10, "spaceX": 10, "repeatX": 2, "height": 450}, "nodeParent": 1, "label": "List(word_list)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 12, "child": [{"type": "Label", "props": {"y": 0, "x": 0, "width": 340, "valign": "middle", "text": "label", "renderType": "render", "height": 45, "fontSize": 30, "color": "#000000", "bgColor": "#919a4f", "align": "center"}, "nodeParent": 12, "label": "Label", "isDirectory": false, "isAniNode": false, "hasChild": false, "compId": 16, "child": []}]}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 601, "x": 502, "width": 218, "var": "add", "skin": "activity/btn_y.png", "labelSize": 30, "label": "add", "height": 97}, "nodeParent": 1, "label": "<PERSON><PERSON>(add)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 15, "type": "TextInput", "props": {"y": 607, "x": 11, "width": 236, "var": "word_key", "type": "text", "styleSkin": "activity/image_bg_content.png", "skin": "activity/image_bg_content.png", "sizeGrid": "40,40,40,40", "prompt": "key", "height": 87, "fontSize": 26}, "nodeParent": 1, "label": "TextInput(word_key)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}, {"x": 15, "type": "TextInput", "props": {"y": 607, "x": 261, "width": 228, "var": "word_value", "type": "number", "styleSkin": "activity/image_bg_content.png", "skin": "activity/image_bg_content.png", "sizeGrid": "40,40,40,40", "prompt": "value", "height": 87, "fontSize": 26}, "nodeParent": 1, "label": "TextInput(word_value)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 19, "child": []}, {"x": 15, "type": "TextArea", "props": {"y": 705, "x": 10, "width": 719, "var": "cp", "text": "TextArea", "height": 133}, "nodeParent": 1, "label": "TextArea(cp)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"x": 15, "type": "Box", "props": {"y": 1113, "x": 14, "width": 710, "var": "lb_box", "height": 113}, "nodeParent": 1, "label": "Box(lb_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 15, "type": "TextInput", "props": {"y": 1241, "x": 17, "width": 499, "var": "testinput", "styleSkin": "activity/image_bg_content.png", "skin": "activity/image_bg_content.png", "sizeGrid": "40,40,40,40", "height": 87, "fontSize": 26}, "nodeParent": 1, "label": "TextInput(testinput)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 1240, "x": 524, "width": 183, "var": "test", "skin": "activity/btn_y.png", "labelSize": 30, "label": "test", "height": 97}, "nodeParent": 1, "label": "<PERSON><PERSON>(test)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}, {"x": 15, "type": "Label", "props": {"y": 850, "x": 12, "wordWrap": true, "width": 714, "var": "log", "text": "label", "height": 252, "fontSize": 30, "color": "#000000"}, "nodeParent": 1, "label": "Label(log)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 23, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}