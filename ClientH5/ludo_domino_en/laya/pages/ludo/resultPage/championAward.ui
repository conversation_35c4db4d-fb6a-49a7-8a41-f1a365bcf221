{"x": 0, "type": "View", "selectedBox": 5, "selecteID": 6, "props": {"width": 174, "sceneColor": "#000000", "height": 107}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "gold_bg", "skin": "public/bg_coinyellow.png"}, "nodeParent": 1, "label": "Image(gold_bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Image", "props": {"y": -4, "x": -19, "width": 53, "var": "gold_icon", "skin": "public/coins.png", "height": 56}, "nodeParent": 2, "label": "Image(gold_icon)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 30, "type": "Label", "props": {"y": 3, "x": 29, "width": 140, "var": "gold_num", "valign": "middle", "text": "-", "height": 40, "fontSize": 28, "color": "#ffffff", "align": "center"}, "nodeParent": 2, "label": "Label(gold_num)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 60, "var": "prop_bg", "skin": "public/bg_coinyellow.png"}, "nodeParent": 1, "label": "Image(prop_bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 5, "child": [{"x": 30, "type": "Image", "props": {"y": -3, "width": 53, "var": "prop_icon", "skin": "public/propIcon.png", "left": -20, "height": 53}, "nodeParent": 5, "label": "Image(prop_icon)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}, {"x": 30, "type": "Label", "props": {"y": 3, "x": 30, "width": 140, "var": "prop_num", "valign": "middle", "text": "-", "height": 40, "fontSize": 28, "color": "#ffffff", "align": "center"}, "nodeParent": 5, "label": "Label(prop_num)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 37, "x": 61, "width": 113, "visible": false, "var": "isQuit", "height": 27}, "nodeParent": 1, "label": "Box(isQuit)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 8, "child": [{"x": 30, "type": "Image", "props": {"y": 1, "x": 0, "skin": "public/leave.png"}, "nodeParent": 8, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 30, "type": "Label", "props": {"y": 0, "x": 30, "width": 78, "valign": "middle", "text": "Escape", "height": 30, "fontSize": 22, "color": "#70D5A9", "align": "left"}, "nodeParent": 8, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}