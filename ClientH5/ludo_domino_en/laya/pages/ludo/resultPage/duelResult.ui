{"x": 0, "type": "View", "selectedBox": 1, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "bg_mask", "top": 0, "skin": "public/alphaBg80.png", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image(bg_mask)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"right": 0, "left": 0, "height": 1027, "centerY": 0}, "nodeParent": 1, "label": "Box", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 47, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 1026.5, "width": 296, "var": "back_btn", "skin": "public/btn_G.png", "scaleY": 0, "scaleX": 0, "labelStrokeColor": "#61f2ca", "labelStroke": 2, "labelSize": 46, "labelColors": "#08846b", "labelBold": true, "label": "Back", "height": 114, "centerX": -176, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 47, "label": "Button(back_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"type": "EffectAnimation", "props": {"y": 38, "x": 166, "var": "mode1", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/modeBtn.efc"}, "nodeParent": 3, "label": "EffectAnimation(mode1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 1026.5, "width": 296, "var": "again_btn", "skin": "public/btn_y.png", "scaleY": 0, "scaleX": 0, "labelStrokeColor": "#fff08b", "labelStroke": 2, "labelSize": 46, "labelColors": "#ab6e0f", "labelBold": true, "label": "Play Again", "height": 114, "centerX": 176, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 47, "label": "Button(again_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"type": "EffectAnimation", "props": {"var": "mode2", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/modeBtn.efc"}, "nodeParent": 2, "label": "EffectAnimation(mode2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "Box", "props": {"var": "skBox", "top": -50, "right": 0, "left": 0, "height": 445}, "nodeParent": 47, "label": "Box(skBox)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 30, "type": "Image", "props": {"y": 326.5, "x": 1745.5, "visible": true, "var": "info_0", "skin": "public/BG_1ST.png", "anchorX": 0.5}, "nodeParent": 47, "label": "Image(info_0)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 45, "type": "Image", "props": {"y": -17, "x": -19, "width": 734, "visible": false, "var": "self_0", "skin": "public/bg_oneself.png", "name": "self", "height": 192}, "nodeParent": 4, "label": "Image(self_0)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Box", "props": {"y": 29, "x": 10, "var": "box_0"}, "nodeParent": 4, "label": "Box(box_0)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 76, "child": [{"x": 60, "type": "Label", "props": {"y": 24, "x": 10, "width": 58, "valign": "middle", "text": "-", "height": 56, "fontSize": 48, "color": "#ffffff", "align": "center"}, "nodeParent": 76, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 69, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Image", "props": {"y": 11, "var": "index_0", "skin": "public/1.png"}, "nodeParent": 76, "label": "Image(index_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Image", "props": {"y": 28, "x": 490, "var": "gold_0_box", "skin": "public/bg_coinyellow.png"}, "nodeParent": 76, "label": "Image(gold_0_box)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 19, "child": [{"type": "Image", "props": {"y": -4, "x": -19, "width": 53, "skin": "public/coins.png", "height": 56}, "nodeParent": 19, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}, {"type": "Label", "props": {"y": 3, "x": 29, "width": 140, "var": "gold_0", "valign": "middle", "text": "30K", "height": 40, "fontSize": 28, "color": "#ffffff", "align": "center"}, "nodeParent": 19, "label": "Label(gold_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": []}], "$HIDDEN": false}, {"x": 60, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 16, "x": 216, "visible": false, "var": "nameItem_0"}, "nodeParent": 76, "label": "UIView(nameItem_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 48, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Label", "props": {"x": 216, "width": 251, "var": "nikeName_0", "valign": "middle", "text": "name", "strokeColor": "#b1741c", "stroke": 4, "overflow": "hidden", "height": 48, "fontSize": 28, "color": "#ffffff"}, "nodeParent": 76, "label": "Label(nikeName_0)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Image", "props": {"y": 57, "x": 216, "var": "fightNumBg_0", "skin": "duel/result_bg_win.png"}, "nodeParent": 76, "label": "Image(fightNumBg_0)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 52, "child": [{"type": "Image", "props": {"y": -8, "x": -8, "width": 44, "skin": "duel/duel_knife.png", "height": 54}, "nodeParent": 52, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 53, "child": []}, {"type": "Label", "props": {"y": 4, "x": 42, "width": 94, "var": "fightNum_0", "text": "-", "strokeColor": "#D85D2D", "stroke": 2, "height": 29, "fontSize": 28, "color": "#FDEA71"}, "nodeParent": 52, "label": "Label(fightNum_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 56, "child": []}], "$HIDDEN": false}]}]}, {"x": 30, "type": "Image", "props": {"y": 499.5, "x": 1745.5, "visible": true, "var": "info_1", "skin": "public/BG_234.png", "anchorX": 0.5}, "nodeParent": 47, "label": "Image(info_1)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 5, "child": [{"x": 45, "type": "Label", "props": {"y": 34, "x": 24, "width": 58, "valign": "middle", "text": "-", "height": 56, "fontSize": 48, "color": "#ffffff", "align": "center"}, "nodeParent": 5, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 67, "child": []}, {"x": 45, "type": "Image", "props": {"y": -16, "x": -17, "width": 728, "visible": false, "skin": "public/bg_oneself.png", "name": "self", "height": 160}, "nodeParent": 5, "label": "Image(self)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}, {"x": 45, "type": "Image", "props": {"y": 20, "x": 14, "var": "index_1", "skin": "public/2.png"}, "nodeParent": 5, "label": "Image(index_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 23, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Image", "props": {"y": 46, "x": 515, "width": 162, "var": "gold_1_box", "skin": "public/bg_coingreen.png", "height": 44}, "nodeParent": 5, "label": "Image(gold_1_box)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 20, "child": [{"type": "Image", "props": {"y": -5, "x": -23, "width": 53, "skin": "public/coins.png", "height": 57}, "nodeParent": 20, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"type": "Label", "props": {"y": 2, "x": 25, "width": 132, "var": "gold_1", "valign": "middle", "text": "30K", "height": 40, "fontSize": 28, "color": "#ffffff", "align": "center"}, "nodeParent": 20, "label": "Label(gold_1)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 34, "child": []}]}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 30, "x": 230, "visible": false, "var": "nameItem_1"}, "nodeParent": 5, "label": "UIView(nameItem_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 49, "child": []}, {"x": 45, "type": "Label", "props": {"y": 24, "x": 226, "width": 256, "var": "nikeName_1", "valign": "middle", "text": "name", "strokeColor": "#000000", "stroke": 4, "overflow": "hidden", "height": 28, "fontSize": 28, "color": "#8dfff8"}, "nodeParent": 5, "label": "Label(nikeName_1)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}, {"x": 45, "type": "Image", "props": {"y": 68, "x": 226, "skin": "duel/result_bg_2.png"}, "nodeParent": 5, "label": "Image", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 57, "child": [{"type": "Image", "props": {"y": -8, "x": -8, "width": 44, "skin": "duel/duel_knife.png", "height": 54}, "nodeParent": 57, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 58, "child": []}, {"type": "Label", "props": {"y": 4, "x": 42, "width": 94, "var": "fightNum_1", "text": "-", "strokeColor": "#217E27", "stroke": 2, "height": 29, "fontSize": 28, "color": "#60E59B"}, "nodeParent": 57, "label": "Label(fightNum_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 59, "child": []}]}, {"x": 45, "type": "Box", "props": {"y": 54, "x": 532, "width": 113, "visible": false, "var": "isQuit_1", "height": 27}, "nodeParent": 5, "label": "Box(isQuit_1)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 77, "child": [{"type": "Image", "props": {"y": 1, "x": 0, "skin": "public/leave.png"}, "nodeParent": 77, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 78, "child": []}, {"type": "Label", "props": {"y": 0, "x": 30, "width": 78, "valign": "middle", "text": "Escape", "height": 30, "fontSize": 22, "color": "#70D5A9", "align": "left"}, "nodeParent": 77, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 79, "child": []}]}]}, {"x": 30, "type": "Image", "props": {"y": 639.5, "x": 1745.5, "visible": false, "var": "info_2", "skin": "public/BG_234.png", "anchorX": 0.5}, "nodeParent": 47, "label": "Image(info_2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 9, "child": [{"x": 45, "type": "Image", "props": {"y": -16, "x": -17, "width": 728, "visible": false, "skin": "public/bg_oneself.png", "name": "self", "height": 160}, "nodeParent": 9, "label": "Image(self)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}, {"x": 45, "type": "Label", "props": {"y": 34, "x": 24, "width": 58, "var": "index_2", "valign": "middle", "text": "3", "height": 56, "fontSize": 48, "color": "#ffffff", "align": "center"}, "nodeParent": 9, "label": "Label(index_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": []}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 30, "x": 230, "visible": false, "var": "nameItem_2"}, "nodeParent": 9, "label": "UIView(nameItem_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 50, "child": []}, {"x": 45, "type": "Label", "props": {"y": 24, "x": 226, "width": 315, "var": "nikeName_2", "valign": "middle", "text": "name", "strokeColor": "#000000", "stroke": 4, "overflow": "hidden", "height": 30, "fontSize": 30, "color": "#8dfff8"}, "nodeParent": 9, "label": "Label(nikeName_2)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}, {"x": 45, "type": "Image", "props": {"y": 68, "x": 226, "skin": "duel/result_bg_2.png"}, "nodeParent": 9, "label": "Image", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 60, "child": [{"type": "Image", "props": {"y": -8, "x": -8, "width": 44, "skin": "duel/duel_knife.png", "height": 54}, "nodeParent": 60, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 61, "child": []}, {"type": "Label", "props": {"y": 4, "x": 42, "width": 94, "var": "fightNum_2", "text": "-", "strokeColor": "#217E27", "stroke": 2, "height": 29, "fontSize": 28, "color": "#60E59B"}, "nodeParent": 60, "label": "Label(fightNum_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 62, "child": []}]}, {"x": 45, "type": "Image", "props": {"y": 46, "x": 515, "width": 162, "var": "gold_2_box", "skin": "public/bg_coingreen.png", "height": 44}, "nodeParent": 9, "label": "Image(gold_2_box)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 70, "child": [{"type": "Image", "props": {"y": -5, "x": -23, "width": 53, "skin": "public/coins.png", "height": 57}, "nodeParent": 70, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 71, "child": []}, {"type": "Label", "props": {"y": 2, "x": 25, "width": 132, "var": "gold_2", "valign": "middle", "text": "30K", "height": 40, "fontSize": 28, "color": "#ffffff", "align": "center"}, "nodeParent": 70, "label": "Label(gold_2)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 72, "child": []}]}, {"x": 45, "type": "Box", "props": {"y": 54, "x": 532, "width": 113, "visible": false, "var": "isQuit_2", "height": 27}, "nodeParent": 9, "label": "Box(isQuit_2)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 80, "child": [{"type": "Image", "props": {"y": 1, "x": 0, "skin": "public/leave.png"}, "nodeParent": 80, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 81, "child": []}, {"type": "Label", "props": {"y": 0, "x": 30, "width": 78, "valign": "middle", "text": "Escape", "height": 30, "fontSize": 22, "color": "#70D5A9", "align": "left"}, "nodeParent": 80, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 82, "child": []}]}]}, {"x": 30, "type": "Image", "props": {"y": 780.5, "x": 1743.5, "visible": false, "var": "info_3", "skin": "public/BG_234.png", "anchorX": 0.5}, "nodeParent": 47, "label": "Image(info_3)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 11, "child": [{"x": 45, "type": "Image", "props": {"y": -16, "x": -17, "width": 728, "visible": false, "skin": "public/bg_oneself.png", "name": "self", "height": 160}, "nodeParent": 11, "label": "Image(self)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 45, "type": "Label", "props": {"y": 34, "x": 24, "width": 58, "var": "index_3", "valign": "middle", "text": "4", "height": 56, "fontSize": 48, "color": "#ffffff", "align": "center"}, "nodeParent": 11, "label": "Label(index_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": []}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 30, "x": 230, "visible": false, "var": "nameItem_3"}, "nodeParent": 11, "label": "UIView(nameItem_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 51, "child": []}, {"x": 45, "type": "Label", "props": {"y": 24, "x": 226, "width": 319, "var": "nikeName_3", "valign": "middle", "text": "name", "strokeColor": "#000000", "stroke": 4, "overflow": "hidden", "height": 30, "fontSize": 30, "color": "#8dfff8"}, "nodeParent": 11, "label": "Label(nikeName_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 31, "child": []}, {"x": 45, "type": "Image", "props": {"y": 68, "x": 226, "skin": "duel/result_bg_2.png"}, "nodeParent": 11, "label": "Image", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 63, "child": [{"type": "Image", "props": {"y": -8, "x": -8, "width": 44, "skin": "duel/duel_knife.png", "height": 54}, "nodeParent": 63, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 64, "child": []}, {"type": "Label", "props": {"y": 4, "x": 42, "width": 94, "var": "fightNum_3", "text": "-", "strokeColor": "#217E27", "stroke": 2, "height": 29, "fontSize": 28, "color": "#60E59B"}, "nodeParent": 63, "label": "Label(fightNum_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 65, "child": []}]}, {"x": 45, "type": "Image", "props": {"y": 46, "x": 515, "width": 162, "var": "gold_3_box", "skin": "public/bg_coingreen.png", "height": 44}, "nodeParent": 11, "label": "Image(gold_3_box)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 73, "child": [{"type": "Image", "props": {"y": -5, "x": -23, "width": 53, "skin": "public/coins.png", "height": 57}, "nodeParent": 73, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 74, "child": []}, {"type": "Label", "props": {"y": 2, "x": 25, "width": 132, "var": "gold_3", "valign": "middle", "text": "30K", "height": 40, "fontSize": 28, "color": "#ffffff", "align": "center"}, "nodeParent": 73, "label": "Label(gold_3)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 75, "child": []}]}, {"x": 45, "type": "Box", "props": {"y": 54, "x": 532, "width": 113, "visible": false, "var": "isQuit_3", "height": 27}, "nodeParent": 11, "label": "Box(isQuit_3)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 83, "child": [{"type": "Image", "props": {"y": 1, "x": 0, "skin": "public/leave.png"}, "nodeParent": 83, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 84, "child": []}, {"type": "Label", "props": {"y": 0, "x": 30, "width": 78, "valign": "middle", "text": "Escape", "height": 30, "fontSize": 22, "color": "#70D5A9", "align": "left"}, "nodeParent": 83, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 85, "child": []}]}]}, {"x": 30, "type": "Label", "props": {"y": 163.5, "width": 553, "var": "result_tx", "valign": "middle", "text": "You Lose", "strokeColor": "#a62619", "stroke": 4, "height": 95, "fontSize": 64, "color": "#fdea80", "centerX": 0, "bold": true, "alpha": 0, "align": "center"}, "nodeParent": 47, "label": "Label(result_tx)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 35, "child": [], "$HIDDEN": false}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}