{"x": 0, "type": "View", "selectedBox": 14, "selecteID": 15, "props": {"width": 162, "sceneColor": "#000000", "height": 100}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"width": 162, "var": "gold_bg", "skin": "public/bg_coingreen.png", "height": 44}, "nodeParent": 1, "label": "Image(gold_bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 11, "child": [{"x": 30, "type": "Image", "props": {"y": -5, "x": -23, "width": 53, "var": "gold_icon", "skin": "public/coins.png", "height": 57}, "nodeParent": 11, "label": "Image(gold_icon)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 30, "type": "Label", "props": {"y": 2, "x": 25, "width": 132, "var": "gold_num", "valign": "middle", "text": "30K", "height": 40, "fontSize": 28, "color": "#ffffff", "align": "center"}, "nodeParent": 11, "label": "Label(gold_num)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": []}]}, {"x": 15, "type": "Image", "props": {"y": 56, "width": 162, "var": "prop_bg", "skin": "public/bg_coingreen.png", "height": 44}, "nodeParent": 1, "label": "Image(prop_bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 14, "child": [{"x": 30, "type": "Image", "props": {"y": -4, "x": -23, "width": 53, "var": "prop_icon", "skin": "public/propIcon.png", "height": 53}, "nodeParent": 14, "label": "Image(prop_icon)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": []}, {"x": 30, "type": "Label", "props": {"y": 2, "x": 25, "width": 132, "var": "prop_num", "valign": "middle", "text": "30K", "height": 40, "fontSize": 28, "color": "#ffffff", "align": "center"}, "nodeParent": 14, "label": "Label(prop_num)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}]}, {"x": 15, "type": "Box", "props": {"y": 37, "x": 49, "width": 113, "visible": false, "var": "isQuit", "height": 27}, "nodeParent": 1, "label": "Box(isQuit)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 8, "child": [{"x": 30, "type": "Image", "props": {"y": 1, "x": 0, "skin": "public/leave.png"}, "nodeParent": 8, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 30, "type": "Label", "props": {"y": 0, "x": 30, "width": 78, "valign": "middle", "text": "Escape", "height": 30, "fontSize": 22, "color": "#70D5A9", "align": "left"}, "nodeParent": 8, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}