{"x": 0, "type": "View", "selectedBox": 11, "selecteID": 31, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "bg_mask", "top": 0, "skin": "public/alphaBg80.png", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image(bg_mask)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"right": 0, "left": 0, "height": 1027, "centerY": 0}, "nodeParent": 1, "label": "Box", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 47, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 1026.5, "width": 296, "var": "back_btn", "skin": "public/btn_G.png", "scaleY": 0, "scaleX": 0, "labelStrokeColor": "#61f2ca", "labelStroke": 2, "labelSize": 46, "labelColors": "#08846b", "labelBold": true, "label": "Back", "height": 114, "centerX": -176, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 47, "label": "Button(back_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"type": "EffectAnimation", "props": {"y": 38, "x": 166, "var": "mode1", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/modeBtn.efc"}, "nodeParent": 3, "label": "EffectAnimation(mode1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 1026.5, "width": 296, "var": "again_btn", "skin": "public/btn_y.png", "scaleY": 0, "scaleX": 0, "labelStrokeColor": "#fff08b", "labelStroke": 2, "labelSize": 46, "labelColors": "#ab6e0f", "labelBold": true, "label": "Play Again", "height": 114, "centerX": 176, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 47, "label": "Button(again_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"type": "EffectAnimation", "props": {"var": "mode2", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/modeBtn.efc"}, "nodeParent": 2, "label": "EffectAnimation(mode2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "Box", "props": {"var": "skBox", "top": -50, "right": 0, "left": 0, "height": 445}, "nodeParent": 47, "label": "Box(skBox)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 30, "type": "Image", "props": {"y": 326.5, "x": 1745.5, "visible": true, "var": "info_0", "skin": "public/BG_1ST.png", "anchorX": 0.5}, "nodeParent": 47, "label": "Image(info_0)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 45, "type": "Image", "props": {"y": -17, "x": -20, "width": 734, "visible": false, "skin": "public/bg_oneself.png", "name": "self", "height": 192}, "nodeParent": 4, "label": "Image(self)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}, {"x": 45, "type": "Image", "props": {"y": 40, "x": 14, "var": "index_0", "skin": "public/1.png"}, "nodeParent": 4, "label": "Image(index_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 68, "x": 230, "var": "nameItem_0"}, "nodeParent": 4, "label": "UIView(nameItem_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 48, "child": []}, {"x": 45, "type": "Label", "props": {"y": 57, "x": 230, "width": 251, "var": "nikeName_0", "valign": "middle", "text": "name", "strokeColor": "#b1741c", "stroke": 4, "overflow": "hidden", "height": 48, "fontSize": 26, "color": "#ffffff"}, "nodeParent": 4, "label": "Label(nikeName_0)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 499.5, "x": 1745.5, "visible": true, "var": "info_1", "skin": "public/BG_234.png", "anchorX": 0.5}, "nodeParent": 47, "label": "Image(info_1)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 5, "child": [{"x": 45, "type": "Image", "props": {"y": -16, "x": -17, "width": 728, "visible": false, "skin": "public/bg_oneself.png", "name": "self", "height": 160}, "nodeParent": 5, "label": "Image(self)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}, {"x": 45, "type": "Image", "props": {"y": 20, "x": 14, "var": "index_1", "skin": "public/2.png"}, "nodeParent": 5, "label": "Image(index_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 23, "child": []}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 57, "x": 230, "var": "nameItem_1"}, "nodeParent": 5, "label": "UIView(nameItem_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 49, "child": []}, {"x": 45, "type": "Label", "props": {"y": 52, "x": 230, "width": 256, "var": "nikeName_1", "valign": "middle", "text": "name", "strokeColor": "#000000", "stroke": 4, "overflow": "hidden", "height": 28, "fontSize": 28, "color": "#8dfff8"}, "nodeParent": 5, "label": "Label(nikeName_1)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 639.5, "x": 1745.5, "visible": false, "var": "info_2", "skin": "public/BG_234.png", "anchorX": 0.5}, "nodeParent": 47, "label": "Image(info_2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 9, "child": [{"x": 45, "type": "Image", "props": {"y": -16, "x": -17, "width": 728, "visible": false, "skin": "public/bg_oneself.png", "name": "self", "height": 160}, "nodeParent": 9, "label": "Image(self)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}, {"x": 45, "type": "Label", "props": {"y": 34, "x": 24, "width": 58, "var": "index_2", "valign": "middle", "text": "3", "height": 56, "fontSize": 48, "color": "#ffffff", "align": "center"}, "nodeParent": 9, "label": "Label(index_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": []}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 57, "x": 230, "var": "nameItem_2"}, "nodeParent": 9, "label": "UIView(nameItem_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 50, "child": []}, {"x": 45, "type": "Label", "props": {"y": 52, "x": 230, "width": 315, "var": "nikeName_2", "valign": "middle", "text": "name", "strokeColor": "#000000", "stroke": 4, "overflow": "hidden", "height": 30, "fontSize": 30, "color": "#8dfff8"}, "nodeParent": 9, "label": "Label(nikeName_2)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 780.5, "x": 1743.5, "visible": false, "var": "info_3", "skin": "public/BG_234.png", "anchorX": 0.5}, "nodeParent": 47, "label": "Image(info_3)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 11, "child": [{"x": 45, "type": "Image", "props": {"y": -16, "x": -17, "width": 728, "visible": false, "skin": "public/bg_oneself.png", "name": "self", "height": 160}, "nodeParent": 11, "label": "Image(self)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 45, "type": "Label", "props": {"y": 34, "x": 24, "width": 58, "var": "index_3", "valign": "middle", "text": "4", "height": 56, "fontSize": 48, "color": "#ffffff", "align": "center"}, "nodeParent": 11, "label": "Label(index_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": []}, {"x": 45, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 57, "x": 230, "var": "nameItem_3"}, "nodeParent": 11, "label": "UIView(nameItem_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 51, "child": []}, {"x": 45, "type": "Label", "props": {"y": 52, "x": 230, "width": 319, "var": "nikeName_3", "valign": "middle", "text": "name", "strokeColor": "#000000", "stroke": 4, "overflow": "hidden", "height": 30, "fontSize": 30, "color": "#8dfff8"}, "nodeParent": 11, "label": "Label(nikeName_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 31, "child": []}]}, {"x": 30, "type": "Label", "props": {"y": 159.5, "width": 553, "var": "result_tx", "valign": "middle", "text": "You Win", "strokeColor": "#a62619", "stroke": 4, "height": 95, "fontSize": 64, "color": "#ffffff", "centerX": 0, "bold": true, "alpha": 0, "align": "center"}, "nodeParent": 47, "label": "Label(result_tx)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 35, "child": [], "$HIDDEN": false}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}