{"x": 0, "type": "View", "selectedBox": 3, "selecteID": 4, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "bg_mask", "top": 0, "skin": "public/alphaBg80.png", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image(bg_mask)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 10, "x": 10, "right": 0, "left": 0, "height": 1027, "centerY": 0}, "nodeParent": 1, "label": "Box", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 1026.5, "width": 296, "var": "back_btn", "skin": "public/btn_G.png", "scaleY": 0, "scaleX": 0, "labelStrokeColor": "#61f2ca", "labelStroke": 2, "labelSize": 46, "labelColors": "#08846b", "labelBold": true, "label": "Back", "height": 114, "centerX": -176, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 3, "label": "Button(back_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"type": "EffectAnimation", "props": {"y": 38, "x": 166, "var": "mode1", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/modeBtn.efc"}, "nodeParent": 6, "label": "EffectAnimation(mode1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}]}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 1026.5, "width": 296, "var": "again_btn", "skin": "public/btn_y.png", "scaleY": 0, "scaleX": 0, "labelStrokeColor": "#fff08b", "labelStroke": 2, "labelSize": 46, "labelColors": "#ab6e0f", "labelBold": true, "label": "Share", "height": 114, "centerX": 176, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 3, "label": "Button(again_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"type": "EffectAnimation", "props": {"var": "mode2", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/modeBtn.efc"}, "nodeParent": 4, "label": "EffectAnimation(mode2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "Box", "props": {"var": "skBox", "top": -50, "right": 0, "left": 0, "height": 445}, "nodeParent": 3, "label": "Box(skBox)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}, {"x": 30, "type": "Image", "props": {"y": 327, "x": 2416, "visible": true, "var": "team_0", "skin": "public/BG_TEAM_1ST.png", "sizeGrid": "15,15,15,15", "anchorX": 0.5}, "nodeParent": 3, "label": "Image(team_0)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 9, "child": [{"x": 45, "type": "Image", "props": {"y": -17, "x": -18, "width": 730, "visible": false, "var": "self_0", "skin": "public/bg_oneself.png", "height": 290}, "nodeParent": 9, "label": "Image(self_0)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 45, "type": "Image", "props": {"y": 87, "x": 14, "skin": "public/1.png"}, "nodeParent": 9, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}, {"x": 45, "type": "Box", "props": {"y": 42, "x": 112, "width": 566, "var": "info_0", "height": 52}, "nodeParent": 9, "label": "Box(info_0)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 32, "child": [{"x": 60, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 10, "x": 110, "visible": false, "var": "nameItem_0"}, "nodeParent": 32, "label": "UIView(nameItem_0)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 64, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Label", "props": {"y": 4, "x": 110, "width": 251, "var": "nikeName_0", "valign": "middle", "text": "name", "strokeColor": "#b1741c", "stroke": 4, "overflow": "hidden", "height": 48, "fontSize": 26, "color": "#ffffff"}, "nodeParent": 32, "label": "Label(nikeName_0)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}]}, {"x": 45, "type": "Box", "props": {"y": 162, "x": 112, "width": 566, "var": "info_1", "height": 52}, "nodeParent": 9, "label": "Box(info_1)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 33, "child": [{"x": 60, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 10, "x": 110, "visible": false, "var": "nameItem_1"}, "nodeParent": 33, "label": "UIView(nameItem_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 65, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Label", "props": {"y": 4, "x": 110, "width": 251, "var": "nikeName_1", "valign": "middle", "text": "name", "strokeColor": "#b1741c", "stroke": 4, "overflow": "hidden", "height": 48, "fontSize": 26, "color": "#ffffff"}, "nodeParent": 33, "label": "Label(nikeName_1)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 34, "child": []}]}]}, {"x": 30, "type": "Image", "props": {"y": 594.5, "x": 2415.5, "visible": true, "var": "team_1", "skin": "public/BG_TEAM_234.png", "anchorX": 0.5}, "nodeParent": 3, "label": "Image(team_1)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 16, "child": [{"x": 45, "type": "Image", "props": {"y": -17, "x": -17, "width": 728, "visible": false, "var": "self_1", "skin": "public/bg_oneself.png", "height": 290}, "nodeParent": 16, "label": "Image(self_1)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 45, "type": "Image", "props": {"y": 88, "x": 14, "skin": "public/2.png"}, "nodeParent": 16, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}, {"x": 45, "type": "Box", "props": {"y": 32, "x": 112, "width": 566, "var": "info_2", "height": 52}, "nodeParent": 16, "label": "Box(info_2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 49, "child": [{"x": 60, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 10, "x": 110, "visible": false, "var": "nameItem_2"}, "nodeParent": 49, "label": "UIView(nameItem_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 66, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Label", "props": {"y": 4, "x": 110, "width": 251, "var": "nikeName_2", "valign": "middle", "text": "name", "strokeColor": "#000000", "stroke": 4, "overflow": "hidden", "height": 48, "fontSize": 26, "color": "#8dfff8"}, "nodeParent": 49, "label": "Label(nikeName_2)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 50, "child": []}]}, {"x": 45, "type": "Box", "props": {"y": 158, "x": 112, "width": 566, "var": "info_3", "height": 52}, "nodeParent": 16, "label": "Box(info_3)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 55, "child": [{"x": 60, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 10, "x": 110, "visible": false, "var": "nameItem_3"}, "nodeParent": 55, "label": "UIView(nameItem_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 67, "child": [], "$HIDDEN": false}, {"x": 60, "type": "Label", "props": {"y": 4, "x": 110, "width": 251, "var": "nikeName_3", "valign": "middle", "text": "name", "strokeColor": "#000000", "stroke": 4, "overflow": "hidden", "height": 48, "fontSize": 26, "color": "#8dfff8"}, "nodeParent": 55, "label": "Label(nikeName_3)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 56, "child": []}]}]}, {"x": 30, "type": "Label", "props": {"y": 163.5, "width": 553, "var": "result_tx", "valign": "middle", "text": "Game Finished", "strokeColor": "#a62619", "stroke": 4, "height": 95, "fontSize": 64, "color": "#ffffff", "centerX": 0, "bold": true, "alpha": 0, "align": "center"}, "nodeParent": 3, "label": "Label(result_tx)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 31, "child": [], "$HIDDEN": false}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}