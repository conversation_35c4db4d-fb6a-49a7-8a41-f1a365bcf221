{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 4, "props": {"width": 60, "sceneColor": "#000000", "height": 60}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 60, "var": "dicePic", "skin": "game/arrow_dice_buffer.png", "height": 60, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(dicePic)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 60, "var": "circlePic", "skin": "game/arrow_dice_circle.png", "scaleY": 0.5, "scaleX": 0.5, "height": 60, "blendMode": "lighter", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(circlePic)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}