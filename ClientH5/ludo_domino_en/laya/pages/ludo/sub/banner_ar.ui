{"x": 0, "type": "View", "selectedBox": 53, "selecteID": 54, "props": {"width": 750, "sceneColor": "#000000", "mouseThrough": true, "height": 220}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"x": 12, "var": "bg", "top": 0, "skin": "game/image_ludo_bg0.png", "sizeGrid": "0,0,20,0", "right": 0, "left": 0, "height": 105}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 52, "child": [], "$HIDDEN": false}, {"x": 15, "type": "HBox", "props": {"var": "head", "right": 0, "mouseThrough": true, "left": 0, "cacheAs": "bitmap"}, "nodeParent": 1, "label": "<PERSON><PERSON><PERSON>(head)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 53, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 10, "x": 10, "visible": false, "var": "store_btn", "stateNum": "1", "skin": "game/btn_store.png", "left": 10, "labelSize": 24, "labelColors": "#ffffff"}, "nodeParent": 53, "label": "Button(store_btn)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 54, "child": []}, {"x": 30, "type": "HBox", "props": {"y": 14, "x": 556, "space": 25, "right": 25}, "nodeParent": 53, "label": "HBox", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 60, "child": [{"type": "<PERSON><PERSON>", "props": {"x": 0, "var": "rank_btn", "stateNum": "1", "skin": "game/btn_reward.png"}, "nodeParent": 60, "label": "Button(rank_btn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 61, "child": []}, {"type": "<PERSON><PERSON>", "props": {"var": "system_btn", "stateNum": "1", "skin": "game/system.png"}, "nodeParent": 60, "label": "Button(system_btn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 62, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"width": 432, "visible": false, "var": "system", "top": 95, "skin": "game/BG_Expression.png", "right": 4, "height": 122, "cacheAs": "bitmap", "anchorX": 1}, "nodeParent": 53, "label": "Image(system)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 63, "child": [{"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 12, "width": 130, "var": "sound_btn", "stateNum": "1", "skin": "game/BG_system.png", "right": 12, "labelSize": 20, "labelPadding": "26,0,0,0", "labelColors": "#f8f8f8", "label": "ON", "height": 98}, "nodeParent": 63, "label": "Button(sound_btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 64, "child": [{"x": 60, "type": "Image", "props": {"width": 40, "skin": "game/voice_on.png", "height": 40, "centerY": -16, "centerX": 0}, "nodeParent": 64, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 65, "child": []}]}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 12, "width": 130, "var": "rule_btn", "stateNum": "1", "skin": "game/BG_system.png", "right": 151, "labelSize": 20, "labelPadding": "26,0,0,0", "labelColors": "#f8f8f8", "label": "Rules", "height": 98}, "nodeParent": 63, "label": "Button(rule_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 66, "child": [{"type": "<PERSON><PERSON>", "props": {"width": 40, "stateNum": "1", "skin": "game/rule.png", "height": 40, "centerY": -16, "centerX": 0}, "nodeParent": 66, "label": "<PERSON><PERSON>", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 67, "child": []}]}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 12, "width": 130, "visible": false, "var": "magic", "stateNum": "1", "skin": "game/BG_system.png", "right": 290, "labelSize": 20, "labelPadding": "26,0,0,0", "labelColors": "#f8f8f8", "label": "Magic", "height": 98}, "nodeParent": 63, "label": "<PERSON><PERSON>(magic)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 68, "child": [{"type": "<PERSON><PERSON>", "props": {"width": 40, "stateNum": "1", "skin": "game/prop.png", "height": 40, "centerY": -16, "centerX": 0}, "nodeParent": 68, "label": "<PERSON><PERSON>", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 69, "child": []}], "$HIDDEN": true}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 12, "width": 130, "visible": false, "var": "muteSpectator_btn", "stateNum": "1", "skin": "game/BG_system.png", "right": 290, "height": 98}, "nodeParent": 63, "label": "Button(muteSpectator_btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 70, "child": [{"x": 60, "type": "Image", "props": {"y": 16, "skin": "game/ChatON.png", "centerY": -16, "centerX": 0}, "nodeParent": 70, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 71, "child": []}, {"x": 60, "type": "Label", "props": {"y": 65, "x": -16, "width": 163, "var": "muteSpectator_lb", "text": "Chat ON", "height": 23, "fontSize": 20, "color": "#f8f8f8", "align": "center"}, "nodeParent": 70, "label": "Label(muteSpectator_lb)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 101, "child": []}], "$HIDDEN": false}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 12, "width": 130, "var": "exit_btn", "stateNum": "1", "skin": "game/BG_system.png", "right": 291, "labelSize": 20, "labelPadding": "26,0,0,0", "labelColors": "#f8f8f8", "label": "Exit", "height": 98}, "nodeParent": 63, "label": "Button(exit_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 72, "child": [{"type": "<PERSON><PERSON>", "props": {"width": 40, "stateNum": "1", "skin": "game/exit.png", "height": 40, "centerY": -16, "centerX": 0}, "nodeParent": 72, "label": "<PERSON><PERSON>", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 73, "child": []}], "$HIDDEN": true}, {"x": 45, "type": "Image", "props": {"y": 2, "skin": "game/BG_Expression_V.png", "rotation": 180, "right": 7}, "nodeParent": 63, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 74, "child": []}], "$LOCKED": false, "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"width": 460, "visible": false, "var": "eventProp_info", "top": 95, "skin": "game/BG_Expression.png", "height": 221, "centerX": 72, "cacheAs": "bitmap"}, "nodeParent": 53, "label": "Image(eventProp_info)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 75, "child": [{"type": "Image", "props": {"y": 3, "x": 91, "skin": "game/BG_Expression_V.png", "rotation": 180}, "nodeParent": 75, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 77, "child": []}, {"type": "Image", "props": {"y": 17, "width": 430, "skin": "game/table_ROOM.png", "height": 53, "centerX": 1}, "nodeParent": 75, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 78, "child": [{"type": "Label", "props": {"y": 0, "x": 134, "width": 284, "valign": "middle", "text": "Obtain on the board", "height": 53, "fontSize": 24, "color": "#ffffff", "bold": true, "align": "right"}, "nodeParent": 78, "label": "Label", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 79, "child": []}, {"type": "Label", "props": {"y": 0, "x": 14, "width": 112, "var": "gameMapCount", "valign": "middle", "height": 53, "fontSize": 24, "color": "#FFF95B", "bold": true, "align": "left"}, "nodeParent": 78, "label": "Label(gameMapCount)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 80, "child": []}]}, {"type": "Image", "props": {"y": 82, "width": 430, "skin": "game/table_ROOM.png", "height": 53, "centerX": 1}, "nodeParent": 75, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 92, "child": [{"type": "Label", "props": {"y": 0, "x": 134, "width": 284, "valign": "middle", "text": "Obtain by arriving", "height": 53, "fontSize": 24, "color": "#ffffff", "bold": true, "align": "right"}, "nodeParent": 92, "label": "Label", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 93, "child": []}, {"type": "Label", "props": {"y": 0, "x": 14, "width": 112, "var": "arriveCount", "valign": "middle", "height": 53, "fontSize": 24, "color": "#FFF95B", "bold": true, "align": "left"}, "nodeParent": 92, "label": "Label(arriveCount)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 94, "child": []}]}, {"type": "Image", "props": {"y": 147, "width": 430, "skin": "game/table_ROOM.png", "height": 53, "centerX": 1}, "nodeParent": 75, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 95, "child": [{"type": "Label", "props": {"y": 0, "x": 134, "width": 284, "valign": "middle", "text": "Obtain by being No.1", "height": 53, "fontSize": 24, "color": "#ffffff", "bold": true, "align": "right"}, "nodeParent": 95, "label": "Label", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 96, "child": []}, {"type": "Label", "props": {"y": 0, "x": 14, "width": 112, "var": "winCount", "valign": "middle", "height": 53, "fontSize": 24, "color": "#FFF95B", "bold": true, "align": "left"}, "nodeParent": 95, "label": "Label(winCount)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 97, "child": []}]}, {"type": "Image", "props": {"y": 207, "x": 10, "width": 430, "visible": false, "var": "propWin2", "skin": "game/table_ROOM.png", "height": 53, "centerX": 1}, "nodeParent": 75, "label": "Image(propWin2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 98, "child": [{"type": "Label", "props": {"y": 0, "x": 134, "width": 284, "valign": "middle", "text": "Obtain by being No.2", "height": 53, "fontSize": 24, "color": "#ffffff", "bold": true, "align": "right"}, "nodeParent": 98, "label": "Label", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 99, "child": []}, {"type": "Label", "props": {"y": 0, "x": 14, "width": 112, "var": "winCount2", "valign": "middle", "height": 53, "fontSize": 24, "color": "#FFF95B", "bold": true, "align": "left"}, "nodeParent": 98, "label": "Label(winCount2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 100, "child": []}]}], "$LOCKED": false, "$HIDDEN": false}, {"x": 30, "type": "Box", "props": {"y": 24, "x": 220, "width": 310, "height": 46, "centerX": 0}, "nodeParent": 53, "label": "Box", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 76, "child": [{"x": 45, "type": "<PERSON><PERSON>", "props": {"width": 270, "visible": false, "var": "audience_btn", "stateNum": "1", "skin": "game/image_title_bg.png", "height": 46, "centerY": 0, "centerX": 0}, "nodeParent": 76, "label": "Button(audience_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 81, "child": [{"type": "Image", "props": {"y": 13, "x": 206, "skin": "game/Audience.png"}, "nodeParent": 81, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 82, "child": []}, {"type": "Label", "props": {"y": 14, "x": 90, "width": 90, "valign": "middle", "text": "Spectator", "fontSize": 18, "color": "#ffffff", "align": "center"}, "nodeParent": 81, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 83, "child": []}, {"type": "Label", "props": {"y": 11, "x": 25, "width": 52, "var": "audience_num", "valign": "middle", "text": "0", "fontSize": 24, "color": "#ffffff", "align": "center"}, "nodeParent": 81, "label": "Label(audience_num)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 84, "child": []}], "$HIDDEN": true}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 0, "width": 136, "visible": false, "var": "eventProp_btn", "stateNum": "1", "skin": "game/image_title_bg.png", "height": 48}, "nodeParent": 76, "label": "Button(eventProp_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 85, "child": [{"type": "Image", "props": {"x": 133, "var": "prop_icon", "skin": "public/propIcon.png", "centerY": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 85, "label": "Image(prop_icon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 86, "child": []}, {"type": "Label", "props": {"y": 1, "x": 8, "width": 101, "var": "curCount", "valign": "middle", "text": "0", "height": 46, "fontSize": 24, "color": "#FFF95B", "bold": true, "align": "center"}, "nodeParent": 85, "label": "Label(curCount)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 87, "child": []}, {"type": "Label", "props": {"y": 7, "x": 103, "width": 58, "visible": false, "var": "eventProp_add", "valign": "middle", "text": "+20", "height": 32, "fontSize": 32, "color": "#FFF95B", "bold": true, "align": "center"}, "nodeParent": 85, "label": "Label(eventProp_add)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 91, "child": []}], "$HIDDEN": true}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 174, "width": 136, "visible": false, "var": "audience_btn2", "stateNum": "1", "skin": "game/image_title_bg.png", "height": 48}, "nodeParent": 76, "label": "<PERSON>ton(audience_btn2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 88, "child": [{"x": 60, "type": "Image", "props": {"y": 15, "x": 94, "skin": "game/Audience.png"}, "nodeParent": 88, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 89, "child": []}, {"x": 60, "type": "Label", "props": {"y": 9, "x": 18, "width": 52, "var": "audience_num2", "valign": "middle", "text": "0", "height": 31, "fontSize": 24, "color": "#ffffff", "align": "center"}, "nodeParent": 88, "label": "Label(audience_num2)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 90, "child": []}], "$HIDDEN": false}], "$HIDDEN": false}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}