{"x": 0, "type": "View", "selectedBox": 2, "selecteID": 3, "props": {"width": 750, "sceneColor": "#000000", "mouseThrough": true, "height": 220}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"x": 12, "var": "bg", "top": 0, "skin": "game/image_ludo_bg0.png", "sizeGrid": "0,0,20,0", "right": 0, "left": 0, "height": 105}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 52, "child": [], "$HIDDEN": false}, {"x": 15, "type": "HBox", "props": {"var": "head", "right": 0, "mouseThrough": true, "left": 0, "cacheAs": "bitmap"}, "nodeParent": 1, "label": "<PERSON><PERSON><PERSON>(head)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 10, "visible": false, "var": "store_btn", "stateNum": "1", "skin": "game/btn_store.png", "right": 10, "labelSize": 24, "labelColors": "#ffffff"}, "nodeParent": 2, "label": "Button(store_btn)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Box", "props": {"y": 14, "x": 30, "left": 30}, "nodeParent": 2, "label": "Box", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 5, "child": [{"type": "<PERSON><PERSON>", "props": {"var": "system_btn", "stateNum": "1", "skin": "game/system.png"}, "nodeParent": 5, "label": "Button(system_btn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}, {"type": "<PERSON><PERSON>", "props": {"x": 95, "var": "rank_btn", "stateNum": "1", "skin": "game/btn_reward.png"}, "nodeParent": 5, "label": "Button(rank_btn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"width": 432, "visible": false, "var": "system", "top": 95, "skin": "game/BG_Expression.png", "left": 12, "height": 121, "cacheAs": "bitmap"}, "nodeParent": 2, "label": "Image(system)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 8, "child": [{"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 12, "width": 130, "var": "sound_btn", "stateNum": "1", "skin": "game/BG_system.png", "left": 12, "labelSize": 20, "labelPadding": "26,0,0,0", "labelColors": "#f8f8f8", "label": "ON", "height": 98}, "nodeParent": 8, "label": "Button(sound_btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 9, "child": [{"x": 60, "type": "Image", "props": {"y": 15, "width": 40, "skin": "game/voice_on.png", "height": 40, "centerY": -16, "centerX": 0}, "nodeParent": 9, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}]}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 12, "width": 130, "var": "rule_btn", "stateNum": "1", "skin": "game/BG_system.png", "left": 151, "labelSize": 20, "labelPadding": "26,0,0,0", "labelColors": "#f8f8f8", "label": "Rules", "height": 98}, "nodeParent": 8, "label": "Button(rule_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 11, "child": [{"type": "<PERSON><PERSON>", "props": {"width": 40, "stateNum": "1", "skin": "game/rule.png", "height": 40, "centerY": -16, "centerX": 0}, "nodeParent": 11, "label": "<PERSON><PERSON>", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}]}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 12, "width": 130, "visible": false, "var": "magic", "stateNum": "1", "skin": "game/BG_system.png", "left": 291, "labelSize": 20, "labelPadding": "26,0,0,0", "labelColors": "#f8f8f8", "label": "Magic", "height": 98}, "nodeParent": 8, "label": "<PERSON><PERSON>(magic)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 13, "child": [{"x": 60, "type": "<PERSON><PERSON>", "props": {"width": 40, "stateNum": "1", "skin": "game/prop.png", "height": 40, "centerY": -16, "centerX": 0}, "nodeParent": 13, "label": "<PERSON><PERSON>", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}], "$HIDDEN": true}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 12, "width": 130, "visible": false, "var": "muteSpectator_btn", "stateNum": "1", "skin": "game/BG_system.png", "left": 291, "height": 98}, "nodeParent": 8, "label": "Button(muteSpectator_btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 15, "child": [{"x": 60, "type": "Image", "props": {"skin": "game/ChatON.png", "centerY": -16, "centerX": 0}, "nodeParent": 15, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}, {"x": 60, "type": "Label", "props": {"y": 65, "x": -17, "width": 163, "var": "muteSpectator_lb", "text": "Chat ON", "height": 23, "fontSize": 20, "color": "#f8f8f8", "align": "center"}, "nodeParent": 15, "label": "Label(muteSpectator_lb)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 63, "child": []}], "$HIDDEN": false}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 12, "width": 130, "var": "exit_btn", "stateNum": "1", "skin": "game/BG_system.png", "left": 291, "labelSize": 20, "labelPadding": "26,0,0,0", "labelColors": "#f8f8f8", "label": "Exit", "height": 98}, "nodeParent": 8, "label": "Button(exit_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 17, "child": [{"type": "<PERSON><PERSON>", "props": {"width": 40, "stateNum": "1", "skin": "game/exit.png", "height": 40, "centerY": -16, "centerX": 0}, "nodeParent": 17, "label": "<PERSON><PERSON>", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}], "$HIDDEN": false}, {"x": 45, "type": "Image", "props": {"y": 3, "x": 64, "skin": "game/BG_Expression_V.png", "rotation": 180}, "nodeParent": 8, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 19, "child": []}], "$LOCKED": false, "$HIDDEN": false}, {"x": 30, "type": "Box", "props": {"y": 24, "width": 310, "height": 46, "centerX": 0}, "nodeParent": 2, "label": "Box", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 37, "child": [{"type": "<PERSON><PERSON>", "props": {"width": 270, "visible": false, "var": "audience_btn", "stateNum": "1", "skin": "game/image_title_bg.png", "height": 46, "centerY": 0, "centerX": 0}, "nodeParent": 37, "label": "Button(audience_btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 42, "child": [{"type": "Image", "props": {"y": 13, "x": 34, "skin": "game/Audience.png"}, "nodeParent": 42, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 43, "child": []}, {"type": "Label", "props": {"y": 14, "x": 90, "width": 90, "valign": "middle", "text": "Spectator", "fontSize": 18, "color": "#ffffff", "align": "center"}, "nodeParent": 42, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 44, "child": []}, {"type": "Label", "props": {"y": 11, "x": 193, "width": 52, "var": "audience_num", "valign": "middle", "text": "0", "fontSize": 24, "color": "#ffffff", "align": "center"}, "nodeParent": 42, "label": "Label(audience_num)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 45, "child": []}], "$HIDDEN": true}, {"type": "<PERSON><PERSON>", "props": {"y": 0, "x": 174, "width": 136, "visible": false, "var": "eventProp_btn", "stateNum": "1", "skin": "game/image_title_bg.png", "height": 48}, "nodeParent": 37, "label": "Button(eventProp_btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 46, "child": [{"type": "Image", "props": {"x": 7, "var": "prop_icon", "skin": "public/propIcon.png", "centerY": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 46, "label": "Image(prop_icon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 47, "child": []}, {"type": "Label", "props": {"y": 1, "x": 29, "width": 101, "var": "curCount", "valign": "middle", "text": "0", "height": 46, "fontSize": 24, "color": "#FFF95B", "bold": true, "align": "center"}, "nodeParent": 46, "label": "Label(curCount)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 48, "child": []}, {"type": "Label", "props": {"y": 7, "x": -7, "width": 58, "visible": false, "var": "eventProp_add", "valign": "middle", "text": "+20", "height": 32, "fontSize": 32, "color": "#FFF95B", "bold": true, "align": "center"}, "nodeParent": 46, "label": "Label(eventProp_add)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 53, "child": []}], "$HIDDEN": false}, {"type": "<PERSON><PERSON>", "props": {"y": 0, "x": 0, "width": 136, "visible": false, "var": "audience_btn2", "stateNum": "1", "skin": "game/image_title_bg.png", "height": 48}, "nodeParent": 37, "label": "<PERSON>ton(audience_btn2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 49, "child": [{"type": "Image", "props": {"y": 15, "x": 17, "skin": "game/Audience.png"}, "nodeParent": 49, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 50, "child": []}, {"type": "Label", "props": {"y": 9, "x": 68, "width": 52, "var": "audience_num2", "valign": "middle", "text": "0", "height": 31, "fontSize": 24, "color": "#ffffff", "align": "center"}, "nodeParent": 49, "label": "Label(audience_num2)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 51, "child": []}], "$HIDDEN": true}]}, {"x": 30, "type": "Image", "props": {"width": 460, "visible": false, "var": "eventProp_info", "top": 95, "skin": "game/BG_Expression.png", "height": 215, "centerX": -69, "cacheAs": "bitmap"}, "nodeParent": 2, "label": "Image(eventProp_info)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 36, "child": [{"type": "Image", "props": {"y": 3, "x": 402, "skin": "game/BG_Expression_V.png", "rotation": 180}, "nodeParent": 36, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 41, "child": []}, {"type": "Image", "props": {"y": 17, "x": 16, "width": 430, "skin": "game/table_ROOM.png", "height": 53, "centerX": 1}, "nodeParent": 36, "label": "Image", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 38, "child": [{"type": "Label", "props": {"y": 0, "x": 14, "width": 284, "valign": "middle", "text": "Obtain on the board", "height": 53, "fontSize": 24, "color": "#ffffff", "bold": true}, "nodeParent": 38, "label": "Label", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 39, "child": []}, {"type": "Label", "props": {"y": 0, "x": 301, "width": 112, "var": "gameMapCount", "valign": "middle", "height": 53, "fontSize": 24, "color": "#FFF95B", "bold": true, "align": "right"}, "nodeParent": 38, "label": "Label(gameMapCount)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 40, "child": []}]}, {"type": "Image", "props": {"y": 81, "width": 430, "skin": "game/table_ROOM.png", "height": 53, "centerX": 1}, "nodeParent": 36, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 54, "child": [{"type": "Label", "props": {"y": 0, "x": 14, "width": 284, "valign": "middle", "text": "Obtain by arriving", "height": 53, "fontSize": 24, "color": "#ffffff", "bold": true}, "nodeParent": 54, "label": "Label", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 55, "child": []}, {"type": "Label", "props": {"y": 0, "x": 301, "width": 112, "var": "arriveCount", "valign": "middle", "height": 53, "fontSize": 24, "color": "#FFF95B", "bold": true, "align": "right"}, "nodeParent": 54, "label": "Label(arriveCount)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 56, "child": []}]}, {"type": "Image", "props": {"y": 146, "width": 430, "skin": "game/table_ROOM.png", "height": 53, "centerX": 1}, "nodeParent": 36, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 57, "child": [{"type": "Label", "props": {"y": 0, "x": 14, "width": 309, "valign": "middle", "text": "Obtain by being No.1", "height": 53, "fontSize": 24, "color": "#ffffff", "bold": true}, "nodeParent": 57, "label": "Label", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 58, "child": []}, {"type": "Label", "props": {"y": 0, "x": 301, "width": 112, "var": "winCount", "valign": "middle", "height": 53, "fontSize": 24, "color": "#FFF95B", "bold": true, "align": "right"}, "nodeParent": 57, "label": "Label(winCount)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 59, "child": []}]}, {"type": "Image", "props": {"y": 207, "width": 430, "visible": false, "var": "propWin2", "skin": "game/table_ROOM.png", "height": 53, "centerX": 1}, "nodeParent": 36, "label": "Image(propWin2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 60, "child": [{"type": "Label", "props": {"y": 0, "x": 14, "width": 309, "valign": "middle", "text": "Obtain by being No.2", "height": 53, "fontSize": 24, "color": "#ffffff", "bold": true}, "nodeParent": 60, "label": "Label", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 61, "child": []}, {"type": "Label", "props": {"y": 0, "x": 301, "width": 112, "var": "winCount2", "valign": "middle", "height": 53, "fontSize": 24, "color": "#FFF95B", "bold": true, "align": "right"}, "nodeParent": 60, "label": "Label(winCount2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 62, "child": []}]}], "$LOCKED": false, "$HIDDEN": false}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}