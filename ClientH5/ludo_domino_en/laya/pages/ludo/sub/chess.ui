{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "props": {"width": 80, "sceneWidth": 300, "sceneHeight": 300, "sceneColor": "#000000", "height": 80, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"width": 80, "var": "box", "mouseThrough": true, "height": 80}, "nodeParent": 1, "label": "Box(box)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Box", "props": {"y": 40, "x": 40, "width": 80, "var": "clickArea", "mouseThrough": false, "mouseEnabled": true, "height": 80, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 2, "label": "Box(clickArea)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 30, "type": "Image", "props": {"y": 40, "x": 40, "visible": false, "var": "chessImg", "skin": "game/chess_11000_0.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 2, "label": "Image(chessImg)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}