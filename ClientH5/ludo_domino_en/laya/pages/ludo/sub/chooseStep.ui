{"x": 0, "type": "View", "selectedBox": 11, "selecteID": 5, "props": {"y": 129, "x": 500, "width": 1000, "sceneColor": "#000000", "height": 128, "anchorY": 1, "anchorX": 0.5}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"width": 100, "var": "nums_bg", "skin": "game/Bubble_0.png", "height": 100, "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image(nums_bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"x": 30, "type": "HBox", "props": {"var": "nums_box", "top": 5, "space": 10, "right": 5, "left": 5, "height": 90}, "nodeParent": 3, "label": "HBox(nums_box)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}]}, {"x": 15, "type": "Box", "props": {"y": 65, "x": 500, "width": 26, "var": "arrow", "rotation": 0, "height": 128, "centerY": 0, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(arrow)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 11, "child": [{"x": 30, "type": "Image", "props": {"var": "arrow_skin", "skin": "game/Bubble_0_0.png", "centerX": 0, "bottom": 0}, "nodeParent": 11, "label": "Image(arrow_skin)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}