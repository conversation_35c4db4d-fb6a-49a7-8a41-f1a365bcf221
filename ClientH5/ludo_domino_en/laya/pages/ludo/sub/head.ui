{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 15, "props": {"y": 56, "x": 56, "width": 112, "sceneWidth": 750, "sceneHeight": 1334, "sceneColor": "#000000", "height": 112, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 14, "x": 14, "width": 84, "var": "face", "skin": "game/default_head.png", "name": "face", "height": 84}, "nodeParent": 1, "label": "Image(face)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"x": 30, "type": "Image", "props": {"y": 0, "x": 0, "width": 84, "skin": "game/head_mask.png", "renderType": "mask", "height": 84}, "nodeParent": 3, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": [], "$HIDDEN": false}], "$LOCKED": true, "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 46, "x": 19, "width": 108, "visible": false, "var": "left_img", "skin": "game/LEFT.png", "scaleY": 0.7, "scaleX": 0.7, "height": 35}, "nodeParent": 1, "label": "Image(left_img)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 7, "child": [{"x": 30, "type": "Label", "props": {"y": 6, "width": 80, "var": "status", "valign": "middle", "text": "LEFT", "height": 22, "fontSize": 20, "color": "#adadad", "centerX": 13, "bold": true, "align": "center"}, "nodeParent": 7, "label": "Label(status)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}, {"x": 30, "type": "Image", "props": {"y": 4, "x": 3, "width": 28, "var": "status_icon", "skin": "game/left_man.png", "height": 28}, "nodeParent": 7, "label": "Image(status_icon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}], "$LOCKED": true, "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 112, "var": "bg", "skin": "game/face_bg.png", "height": 112}, "nodeParent": 1, "label": "Image(bg)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": [], "$LOCKED": true, "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 17, "x": 32, "visible": false, "var": "crown_1", "skin": "game/crown_1.png", "scaleY": 0.7, "scaleX": 0.7, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(crown_1)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": [], "$LOCKED": true, "$HIDDEN": true}, {"x": 15, "type": "Image", "props": {"y": 18, "x": 35, "visible": false, "var": "crown_2", "skin": "game/crown_2.png", "scaleY": 0.7, "scaleX": 0.7, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(crown_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": [], "$LOCKED": true, "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 60, "x": 64, "visible": false, "var": "team_icon", "skin": "game/team_A.png"}, "nodeParent": 1, "label": "Image(team_icon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": [], "$HIDDEN": true}, {"x": 15, "type": "Image", "props": {"y": 0, "x": 64, "width": 45, "visible": false, "var": "btn_gift", "skin": "game/interactive_icon_bg.png", "name": "btn_gift", "height": 45}, "nodeParent": 1, "label": "Image(btn_gift)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 18, "child": [{"x": 30, "type": "Image", "props": {"y": 7, "x": 7, "var": "img_gift", "skin": "game/interactive_icon.png", "name": "img_gift"}, "nodeParent": 18, "label": "Image(img_gift)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 19, "child": []}], "$HIDDEN": true}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}