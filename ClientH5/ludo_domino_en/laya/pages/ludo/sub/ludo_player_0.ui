{"x": 0, "type": "View", "selectedBox": 17, "selecteID": 2, "props": {"width": 300, "sceneColor": "#000000", "height": 220, "autoAnimation": "pause"}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 20, "x": 6, "width": 180, "visible": false, "var": "playerBg"}, "nodeParent": 1, "label": "Image(playerBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 23, "child": []}, {"x": 15, "type": "Box", "props": {"y": 38, "x": 96, "width": 180, "var": "name_box", "height": 32, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(name_box)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 19, "child": [{"x": 30, "type": "HBox", "props": {"y": 16, "x": 0, "space": 5, "left": 0, "centerY": 0, "anchorY": 0.5, "align": "middle"}, "nodeParent": 19, "label": "HBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 17, "child": [{"x": 45, "type": "Box", "props": {"y": 2, "width": 40, "visible": false, "var": "flag", "height": 40}, "nodeParent": 17, "label": "Box(flag)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"x": 45, "type": "Box", "props": {"y": 2, "width": 40, "visible": false, "var": "RL", "height": 28}, "nodeParent": 17, "label": "Box(RL)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}, {"x": 45, "type": "Box", "props": {"y": 2, "width": 30, "visible": false, "var": "vip", "height": 28}, "nodeParent": 17, "label": "Box(vip)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"x": 45, "type": "Label", "props": {"y": 0, "var": "nick<PERSON><PERSON>", "valign": "middle", "text": "name", "rotation": 0, "height": 32, "fontSize": 20, "color": "#ffffff", "align": "left"}, "nodeParent": 17, "label": "Label(nickName)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": [], "$LOCKED": false, "$HIDDEN": false}]}]}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 52, "x": 0, "width": 52, "var": "voice_btn", "height": 146}, "nodeParent": 1, "label": "<PERSON><PERSON>(voice_btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"x": 30, "type": "Animation", "props": {"y": 34, "x": 4, "var": "voice_ani", "source": "publics/ani/ani_voice.ani"}, "nodeParent": 6, "label": "Animation(voice_ani)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 120, "x": 96, "width": 90, "var": "head_box", "rotation": 0, "height": 90, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(head_box)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": [], "$HIDDEN": false}, {"x": 15, "type": "HBox", "props": {"y": 43, "x": 230, "var": "buff_box", "space": 5, "height": 40, "anchorY": 0.5, "anchorX": 0.5, "align": "middle"}, "nodeParent": 1, "label": "HBox(buff_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 121, "x": 227, "width": 100, "var": "rank", "height": 68, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(rank)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 9, "child": [{"type": "Image", "props": {"y": -1, "x": 4, "visible": false, "var": "rank2", "skin": "game/icon_2nd.png"}, "nodeParent": 9, "label": "Image(rank2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": [], "$HIDDEN": false}, {"type": "Image", "props": {"y": 0, "x": -3, "visible": false, "var": "rank1", "skin": "game/icon_1st.png"}, "nodeParent": 9, "label": "Image(rank1)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "HBox", "props": {"y": 11, "x": 10, "width": 150, "var": "dice_box", "space": 5, "height": 50, "align": "middle"}, "nodeParent": 1, "label": "HBox(dice_box)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 125, "x": 220, "var": "dice_bg", "skin": "game/cliP_dice.png", "anchorY": 0.5, "anchorX": 0.5, "alpha": 0}, "nodeParent": 1, "label": "Image(dice_bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 30, "type": "Box", "props": {"y": 5, "x": 22, "width": 120, "var": "dice_vessel", "height": 120}, "nodeParent": 4, "label": "Box(dice_vessel)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}, {"x": 30, "type": "Animation", "props": {"y": -14, "x": 80, "width": 1, "visible": false, "var": "throw_jump", "source": "publics/ani/jump.ani", "scaleY": 1.6, "scaleX": 1.6, "height": 1}, "nodeParent": 4, "label": "Animation(throw_jump)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": [], "$HIDDEN": false}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}