{"x": 0, "type": "View", "selectedBox": 20, "selecteID": 25, "props": {"width": 300, "sceneColor": "#000000", "height": 220}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 140, "x": 6, "width": 180, "visible": false, "var": "playerBg"}, "nodeParent": 1, "label": "Image(playerBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 23, "child": []}, {"x": 15, "type": "Box", "props": {"y": 155, "x": 96, "width": 180, "var": "name_box", "height": 32, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(name_box)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 19, "child": [{"x": 30, "type": "HBox", "props": {"space": 5, "left": 0, "centerY": 0, "anchorY": 0.5, "align": "middle"}, "nodeParent": 19, "label": "HBox", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 16, "child": [{"x": 45, "type": "Box", "props": {"width": 40, "visible": false, "var": "flag", "height": 40}, "nodeParent": 16, "label": "Box(flag)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"x": 45, "type": "Box", "props": {"width": 40, "visible": false, "var": "RL", "height": 28}, "nodeParent": 16, "label": "Box(RL)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}, {"x": 45, "type": "Box", "props": {"width": 30, "visible": false, "var": "vip", "height": 28}, "nodeParent": 16, "label": "Box(vip)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"x": 45, "type": "Label", "props": {"y": 0, "var": "nick<PERSON><PERSON>", "valign": "middle", "text": "name", "rotation": 0, "height": 32, "fontSize": 20, "color": "#ffffff", "align": "left"}, "nodeParent": 16, "label": "Label(nickName)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": [], "$LOCKED": false, "$HIDDEN": false}], "$HIDDEN": false}]}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 0, "width": 52, "var": "voice_btn", "height": 136}, "nodeParent": 1, "label": "<PERSON><PERSON>(voice_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"type": "Animation", "props": {"y": 24, "x": 4, "var": "voice_ani", "source": "publics/ani/ani_voice.ani"}, "nodeParent": 6, "label": "Animation(voice_ani)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": [], "$LOCKED": true}]}, {"x": 15, "type": "Box", "props": {"y": 65, "x": 96, "width": 90, "var": "head_box", "height": 90, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(head_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": []}, {"x": 15, "type": "HBox", "props": {"y": 160, "x": 230, "var": "buff_box", "space": 5, "height": 40, "anchorY": 0.5, "anchorX": 0.5, "align": "middle"}, "nodeParent": 1, "label": "HBox(buff_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 15, "type": "Box", "props": {"y": 70, "x": 224, "width": 100, "var": "rank", "height": 68, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(rank)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 9, "child": [{"x": 30, "type": "Image", "props": {"y": -1, "x": 4, "visible": false, "var": "rank2", "skin": "game/icon_2nd.png"}, "nodeParent": 9, "label": "Image(rank2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 0, "x": -3, "visible": false, "var": "rank1", "skin": "game/icon_1st.png"}, "nodeParent": 9, "label": "Image(rank1)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "HBox", "props": {"y": 160, "x": 10, "width": 150, "var": "dice_box", "space": 5, "height": 50, "anchorY": 0.5, "align": "middle"}, "nodeParent": 1, "label": "HBox(dice_box)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}, {"x": 15, "type": "Image", "props": {"y": 68, "x": 220, "var": "dice_bg", "skin": "game/cliP_dice.png", "mouseThrough": true, "anchorY": 0.5, "anchorX": 0.5, "alpha": 0}, "nodeParent": 1, "label": "Image(dice_bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 30, "type": "Box", "props": {"y": 5, "x": 22, "width": 120, "var": "dice_vessel", "height": 120}, "nodeParent": 4, "label": "Box(dice_vessel)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}, {"x": 30, "type": "Animation", "props": {"y": 144, "x": 82, "width": 1, "visible": false, "var": "throw_jump", "source": "publics/ani/jump.ani", "scaleY": 1.6, "scaleX": 1.6, "rotation": 180, "height": 1}, "nodeParent": 4, "label": "Animation(throw_jump)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": [], "$HIDDEN": false}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}