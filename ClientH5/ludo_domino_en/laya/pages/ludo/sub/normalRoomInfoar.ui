{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "props": {"width": 460, "sceneColor": "#000000", "height": 230, "cacheAs": "bitmap"}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"width": 460, "var": "bg", "skin": "game/BG_Expression.png", "height": 230, "cacheAs": "bitmap"}, "nodeParent": 1, "label": "Image(bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 39, "child": [{"x": 30, "type": "Image", "props": {"y": 15, "x": 17, "width": 275, "skin": "game/table_ROOM.png", "height": 40}, "nodeParent": 39, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 40, "child": [{"x": 45, "type": "Label", "props": {"y": 9, "x": 12, "width": 151, "var": "rank_roomId", "valign": "middle", "height": 22, "fontSize": 18, "color": "#f3f3f3", "align": "center"}, "nodeParent": 40, "label": "Label(rank_roomId)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 41, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Label", "props": {"y": 7, "x": 178, "width": 105, "valign": "middle", "text": "ROOM ID:", "height": 26, "fontSize": 18, "color": "#fff690", "bold": true}, "nodeParent": 40, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 42, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "Image", "props": {"y": 15, "x": 303, "width": 142, "skin": "game/table_ROOM.png", "height": 40}, "nodeParent": 39, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 43, "child": [{"x": 45, "type": "Label", "props": {"y": 3, "x": 12, "width": 116, "var": "rank_roomType", "valign": "middle", "text": "Classic", "height": 34, "fontSize": 20, "color": "#f3f3f3", "align": "center"}, "nodeParent": 43, "label": "Label(rank_roomType)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 44, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 78, "width": 430, "var": "rankP1", "skin": "game/table_ROOM.png", "height": 53, "centerX": 0}, "nodeParent": 39, "label": "Image(rankP1)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 45, "child": [{"x": 45, "type": "Image", "props": {"y": -7, "x": 318, "skin": "game/RANK1.png"}, "nodeParent": 45, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 46, "child": []}, {"x": 45, "type": "Label", "props": {"y": 15, "x": 183, "text": "RANK 1", "fontSize": 26, "color": "#fff690", "bold": true}, "nodeParent": 45, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 47, "child": []}, {"x": 45, "type": "Label", "props": {"y": 12, "x": 5, "width": 170, "var": "rank_1", "overflow": "scroll", "height": 32, "fontSize": 32, "color": "#ffffff", "align": "center"}, "nodeParent": 45, "label": "Label(rank_1)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 48, "child": [], "$LOCKED": true}]}, {"x": 30, "type": "Image", "props": {"y": 151, "width": 430, "var": "rankP2", "skin": "game/table_ROOM.png", "height": 53, "centerX": 0}, "nodeParent": 39, "label": "Image(rankP2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 49, "child": [{"x": 45, "type": "Image", "props": {"y": -5, "x": 325, "skin": "game/RANK2.png"}, "nodeParent": 49, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 50, "child": []}, {"x": 45, "type": "Label", "props": {"y": 14, "x": 184, "text": "RANK 2", "fontSize": 26, "color": "#fff690", "bold": true}, "nodeParent": 49, "label": "Label", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 51, "child": []}, {"x": 45, "type": "Label", "props": {"y": 13, "x": 8, "width": 149, "var": "rank_2", "overflow": "scroll", "fontSize": 28, "color": "#ffffff", "align": "center"}, "nodeParent": 49, "label": "Label(rank_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 52, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "Image", "props": {"y": 3, "x": 392, "skin": "game/BG_Expression_V.png", "rotation": 180}, "nodeParent": 39, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 53, "child": []}], "$LOCKED": false, "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}