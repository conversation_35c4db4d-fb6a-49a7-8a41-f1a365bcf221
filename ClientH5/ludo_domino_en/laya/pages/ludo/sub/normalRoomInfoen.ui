{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "props": {"width": 460, "sceneColor": "#000000", "height": 230, "cacheAs": "bitmap"}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"width": 460, "var": "bg", "skin": "game/BG_Expression.png", "height": 230, "cacheAs": "bitmap"}, "nodeParent": 1, "label": "Image(bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 24, "child": [{"x": 30, "type": "Image", "props": {"y": 15, "x": 170, "width": 275, "skin": "game/table_ROOM.png", "height": 40}, "nodeParent": 24, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 25, "child": [{"x": 45, "type": "Label", "props": {"y": 10, "x": 107, "width": 154, "var": "rank_roomId", "valign": "middle", "height": 23, "fontSize": 18, "color": "#f3f3f3", "align": "center"}, "nodeParent": 25, "label": "Label(rank_roomId)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Label", "props": {"y": 9, "x": 10, "width": 93, "valign": "middle", "text": "ROOM ID:", "height": 25, "fontSize": 18, "color": "#fff690", "bold": true, "align": "right"}, "nodeParent": 25, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "Image", "props": {"y": 15, "x": 16, "width": 142, "skin": "game/table_ROOM.png", "height": 40}, "nodeParent": 24, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 28, "child": [{"x": 45, "type": "Label", "props": {"y": 3, "x": 12, "width": 116, "var": "rank_roomType", "valign": "middle", "text": "Classic", "height": 34, "fontSize": 20, "color": "#f3f3f3", "align": "center"}, "nodeParent": 28, "label": "Label(rank_roomType)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 78, "width": 430, "var": "rankP1", "skin": "game/table_ROOM.png", "height": 53, "centerX": 0}, "nodeParent": 24, "label": "Image(rankP1)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 30, "child": [{"x": 45, "type": "Image", "props": {"y": -7, "x": 20, "skin": "game/RANK1.png"}, "nodeParent": 30, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 31, "child": []}, {"x": 45, "type": "Label", "props": {"y": 15, "x": 92, "text": "RANK 1", "fontSize": 26, "color": "#fff690", "bold": true}, "nodeParent": 30, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": []}, {"x": 45, "type": "Label", "props": {"y": 12, "x": 213, "width": 170, "var": "rank_1", "overflow": "scroll", "height": 32, "fontSize": 32, "color": "#ffffff", "align": "center"}, "nodeParent": 30, "label": "Label(rank_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 33, "child": [], "$LOCKED": true}]}, {"x": 30, "type": "Image", "props": {"y": 153, "x": 19, "width": 430, "var": "rankP2", "skin": "game/table_ROOM.png", "height": 53, "centerX": 0}, "nodeParent": 24, "label": "Image(rankP2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 34, "child": [{"x": 45, "type": "Image", "props": {"y": -5, "x": 30, "skin": "game/RANK2.png"}, "nodeParent": 34, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 35, "child": []}, {"x": 45, "type": "Label", "props": {"y": 14, "x": 94, "text": "RANK 2", "fontSize": 26, "color": "#fff690", "bold": true}, "nodeParent": 34, "label": "Label", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 36, "child": []}, {"x": 45, "type": "Label", "props": {"y": 13, "x": 222, "width": 149, "var": "rank_2", "overflow": "scroll", "fontSize": 28, "color": "#ffffff", "align": "center"}, "nodeParent": 34, "label": "Label(rank_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 37, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "Image", "props": {"y": 3, "x": 71, "skin": "game/BG_Expression_V.png", "rotation": 180}, "nodeParent": 24, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 38, "child": []}], "$LOCKED": false, "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}