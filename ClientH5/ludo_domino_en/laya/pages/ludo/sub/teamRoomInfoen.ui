{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "props": {"width": 460, "sceneColor": "#000000", "height": 300, "cacheAs": "bitmap"}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 460, "var": "bg", "skin": "game/BG_Expression.png", "height": 290, "cacheAs": "bitmap"}, "nodeParent": 1, "label": "Image(bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Image", "props": {"y": 15, "x": 170, "width": 275, "skin": "game/table_ROOM.png", "height": 40}, "nodeParent": 2, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 3, "child": [{"x": 45, "type": "Label", "props": {"y": 10, "x": 107, "width": 154, "var": "rank_roomId", "valign": "middle", "height": 23, "fontSize": 18, "color": "#f3f3f3", "align": "center"}, "nodeParent": 3, "label": "Label(rank_roomId)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Label", "props": {"y": 9, "x": 10, "width": 93, "valign": "middle", "text": "ROOM ID:", "height": 25, "fontSize": 18, "color": "#fff690", "bold": true, "align": "right"}, "nodeParent": 3, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "Image", "props": {"y": 15, "x": 16, "width": 142, "skin": "game/table_ROOM.png", "height": 40}, "nodeParent": 2, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"x": 45, "type": "Label", "props": {"y": 3, "x": 12, "width": 116, "var": "rank_roomType", "valign": "middle", "text": "Classic", "height": 34, "fontSize": 20, "color": "#f3f3f3", "align": "center"}, "nodeParent": 6, "label": "Label(rank_roomType)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 78, "width": 430, "skin": "game/table_ROOM.png", "height": 53, "centerX": 0}, "nodeParent": 2, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 8, "child": [{"x": 45, "type": "Image", "props": {"y": -7, "x": 20, "skin": "game/RANK1.png"}, "nodeParent": 8, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 45, "type": "Label", "props": {"y": 15, "x": 92, "text": "RANK 1", "fontSize": 26, "color": "#fff690", "bold": true}, "nodeParent": 8, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 45, "type": "Label", "props": {"y": 12, "x": 213, "width": 170, "var": "rank_1", "overflow": "scroll", "height": 32, "fontSize": 32, "color": "#ffffff", "align": "center"}, "nodeParent": 8, "label": "Label(rank_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": [], "$LOCKED": true}]}, {"x": 30, "type": "Image", "props": {"y": 153, "width": 430, "skin": "game/table_ROOM.png", "height": 124, "centerX": 0}, "nodeParent": 2, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 12, "child": [{"x": 45, "type": "Label", "props": {"y": 16, "x": 21, "width": 178, "valign": "middle", "text": "Team A", "height": 40, "fontSize": 30, "color": "#FFFFFF", "bold": true, "align": "center"}, "nodeParent": 12, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 45, "type": "Label", "props": {"y": 16, "x": 228, "width": 178, "valign": "middle", "text": "Team B", "height": 40, "fontSize": 30, "color": "#FFFFFF", "bold": true, "align": "center"}, "nodeParent": 12, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"x": 45, "type": "Image", "props": {"y": 63, "x": 55, "var": "teamA_img", "skin": "game/Team1.png"}, "nodeParent": 12, "label": "Image(teamA_img)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}, {"x": 45, "type": "Image", "props": {"y": 63, "x": 264, "var": "teamB_img", "skin": "game/Team2.png"}, "nodeParent": 12, "label": "Image(teamB_img)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"x": 45, "type": "Image", "props": {"y": 37, "x": 189, "skin": "game/vs.png"}, "nodeParent": 12, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 23, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 3, "x": 72, "skin": "game/BG_Expression_V.png", "rotation": 180}, "nodeParent": 2, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}], "$LOCKED": false, "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}