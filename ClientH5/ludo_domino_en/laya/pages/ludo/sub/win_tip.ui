{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 7, "props": {"sceneWidth": 750, "sceneHeight": 1334, "sceneColor": "#ffffff", "mouseThrough": false}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": -35, "x": -35, "width": 397, "skin": "game/win_bg.png", "height": 397}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 7, "child": [{"x": 30, "type": "Label", "props": {"y": 129, "x": 72, "wordWrap": true, "width": 246, "var": "tx", "valign": "middle", "text": "You have won the\\ngame and exiting\\nthe game won’t\\naffect the rewards.", "strokeColor": "#2C230B", "stroke": 2, "height": 111, "fontSize": 20, "color": "#FFFFFF", "align": "center"}, "nodeParent": 7, "label": "Label(tx)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}