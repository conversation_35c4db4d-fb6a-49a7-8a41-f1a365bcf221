{"x": 0, "type": "View", "selectedBox": 13, "selecteID": 21, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"top": 0, "skin": "activity/BG.png", "right": 0, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"width": 396, "visible": false, "var": "nextBtn", "skin": "activity/btn_y.png", "labelSize": 40, "labelColors": "#ab6e0f", "label": "Next", "height": 108, "centerX": 0, "bottom": 42}, "nodeParent": 1, "label": "Button(nextBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"var": "topBg", "top": 0, "skin": "activity/Navigation_bottom.png", "right": 0, "left": 0, "height": 98}, "nodeParent": 1, "label": "Image(topBg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 13, "child": [{"x": 30, "type": "Image", "props": {"width": 109, "visible": false, "var": "ruleBtn", "skin": "activity/alphaBg.png", "left": 4, "height": 88, "bottom": -94}, "nodeParent": 13, "label": "Image(ruleBtn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 77, "child": [{"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 12, "x": 26, "stateNum": "1", "skin": "activity/Description.png"}, "nodeParent": 77, "label": "<PERSON><PERSON>", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 78, "child": []}]}, {"x": 30, "type": "Image", "props": {"x": 614, "width": 134, "var": "closeBtn", "skin": "activity/alphaBg.png", "height": 78, "centerY": -2}, "nodeParent": 13, "label": "Image(closeBtn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 81, "child": [{"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 27, "stateNum": "1", "skin": "activity/cross.png"}, "nodeParent": 81, "label": "<PERSON><PERSON>", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 82, "child": []}]}, {"x": 30, "type": "Image", "props": {"width": 286, "var": "imgTitle", "skin": "activity/image_title_bg.png", "sizeGrid": "16,24,16,24", "height": 48, "centerY": -4, "centerX": 0}, "nodeParent": 13, "label": "Image(imgTitle)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 21, "child": [{"x": 45, "type": "Text", "props": {"y": 4, "x": 28, "width": 137, "var": "curTxt", "valign": "middle", "height": 40, "fontSize": 22, "color": "#ffffff"}, "nodeParent": 21, "label": "Text(curTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"x": 45, "type": "Text", "props": {"y": 11, "x": 179, "var": "moneyTxt", "valign": "middle", "text": " ", "fontSize": 26, "color": "#fff95b", "align": "right"}, "nodeParent": 21, "label": "Text(moneyTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 23, "child": []}]}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"visible": false, "var": "quitBtn", "stateNum": "1", "skin": "activity/return.png", "left": 32, "labelSize": 24, "centerY": -2}, "nodeParent": 13, "label": "<PERSON><PERSON>(quitBtn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 18, "child": [{"type": "Label", "props": {"y": 24, "x": 72, "width": 77, "text": "Quit", "height": 33, "fontSize": 24, "color": "#ffffff"}, "nodeParent": 18, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 19, "child": []}]}], "$HIDDEN": true}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"width": 396, "visible": false, "var": "joinBtn", "skin": "activity/btn_y.png", "labelSize": 40, "labelColors": "#ab6e0f", "height": 108, "centerX": 1, "bottom": 42}, "nodeParent": 1, "label": "Button(joinBtn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 62, "child": [{"x": 30, "type": "Box", "props": {"y": 55, "x": 137, "var": "costBox"}, "nodeParent": 62, "label": "Box(costBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 83, "child": [{"x": 45, "type": "Image", "props": {"y": 1, "width": 29, "skin": "activity/coins.png", "height": 30}, "nodeParent": 83, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 64, "child": []}, {"x": 45, "type": "Text", "props": {"y": 0, "x": 35, "var": "costTxt", "valign": "middle", "text": "6000", "strokeColor": "#fff08b", "stroke": 2, "fontSize": 30, "color": "#ab6e0f", "bold": true, "align": "left"}, "nodeParent": 83, "label": "Text(costTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 65, "child": []}]}, {"x": 30, "type": "Text", "props": {"y": 9, "x": 11, "width": 373, "var": "joinTxt", "valign": "middle", "text": "Battle", "strokeColor": "#fff08b", "stroke": 2, "height": 40, "fontSize": 40, "color": "#ab6e0f", "bold": true, "align": "center"}, "nodeParent": 62, "label": "Text(joinTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 63, "child": []}], "$HIDDEN": true}, {"x": 15, "type": "Box", "props": {"width": 669, "var": "mapSp", "height": 988, "centerY": 2, "centerX": 0}, "nodeParent": 1, "label": "Box(mapSp)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 11, "child": [{"x": 30, "type": "Box", "props": {"y": -1, "x": 112, "var": "bannerBox"}, "nodeParent": 11, "label": "Box(bannerBox)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 60, "child": []}, {"x": 30, "type": "Image", "props": {"y": 244, "x": 214, "width": 424, "skin": "activity/arrow.png", "height": 652}, "nodeParent": 11, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$LOCKED": false, "$HIDDEN": false}, {"x": 30, "type": "Box", "props": {"y": 437, "x": 0, "width": 226, "var": "item_5", "height": 250}, "nodeParent": 11, "label": "Box(item_5)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 51, "child": [{"x": 45, "type": "Image", "props": {"y": 0, "x": 115, "width": 230, "var": "img5", "skin": "activity/islands_5.png", "scaleX": 1, "height": 248, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 51, "label": "Image(img5)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 8, "child": [{"type": "EffectAnimation", "props": {"var": "move5", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/levelMove.efc"}, "nodeParent": 8, "label": "EffectAnimation(move5)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 69, "child": []}, {"type": "EffectAnimation", "props": {"var": "scale5", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/levelMovesize.efc"}, "nodeParent": 8, "label": "EffectAnimation(scale5)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 70, "child": []}]}]}, {"x": 30, "type": "Box", "props": {"y": 558, "x": 470, "width": 213, "var": "item_4", "height": 206}, "nodeParent": 11, "label": "Box(item_4)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 54, "child": [{"x": 45, "type": "Image", "props": {"y": 0, "x": 103, "width": 207, "var": "img4", "skin": "activity/islands_4.png", "scaleY": 1, "scaleX": 1, "height": 206, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 54, "label": "Image(img4)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 7, "child": [{"type": "EffectAnimation", "props": {"var": "move4", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/levelMove.efc"}, "nodeParent": 7, "label": "EffectAnimation(move4)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 71, "child": []}, {"type": "EffectAnimation", "props": {"var": "scale4", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/levelMovesize.efc"}, "nodeParent": 7, "label": "EffectAnimation(scale4)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 72, "child": []}]}]}, {"x": 30, "type": "Box", "props": {"y": 692, "x": 103, "width": 191, "var": "item_3", "height": 164}, "nodeParent": 11, "label": "Box(item_3)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 53, "child": [{"type": "Image", "props": {"y": 0, "x": 98, "width": 197, "var": "img3", "skin": "activity/islands_3.png", "scaleY": 1, "scaleX": 1, "height": 169, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 53, "label": "Image(img3)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"type": "EffectAnimation", "props": {"var": "move3", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/levelMove.efc"}, "nodeParent": 6, "label": "EffectAnimation(move3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 73, "child": []}, {"type": "EffectAnimation", "props": {"var": "scale3", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/levelMovesize.efc"}, "nodeParent": 6, "label": "EffectAnimation(scale3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 74, "child": []}]}]}, {"x": 30, "type": "Box", "props": {"y": 786, "x": 413, "width": 177, "var": "item_2", "height": 146}, "nodeParent": 11, "label": "Box(item_2)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 52, "child": [{"type": "Image", "props": {"y": 0, "x": 88, "width": 176, "var": "img2", "skin": "activity/islands_2.png", "height": 145, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 52, "label": "Image(img2)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 5, "child": [{"type": "EffectAnimation", "props": {"var": "scale2", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/levelMovesize.efc"}, "nodeParent": 5, "label": "EffectAnimation(scale2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 75, "child": []}, {"type": "EffectAnimation", "props": {"var": "move2", "skin": "../laya/icons/components/Animation.png", "effect": "publics/ani/levelMove.efc"}, "nodeParent": 5, "label": "EffectAnimation(move2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 76, "child": []}]}]}, {"x": 30, "type": "Image", "props": {"y": 196, "x": 123, "var": "item_6", "skin": "activity/islands.png", "anchorY": 0.6}, "nodeParent": 11, "label": "Image(item_6)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 9, "child": [{"x": 45, "type": "Image", "props": {"y": 234, "x": 0, "skin": "activity/v.png"}, "nodeParent": 9, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 61, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 913, "x": 265, "var": "item_1", "skin": "activity/islands_1.png", "anchorY": 0.5, "anchorX": 0}, "nodeParent": 11, "label": "Image(item_1)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 30, "type": "Box", "props": {"y": 0, "x": 60, "width": 583, "height": 988, "cacheAs": "bitmap"}, "nodeParent": 11, "label": "Box", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 66, "child": [{"type": "Image", "props": {"y": 937, "x": 226, "skin": "activity/Number_of_people.png"}, "nodeParent": 66, "label": "Image", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}, {"type": "Image", "props": {"y": 813, "x": 384, "skin": "activity/Number_of_people.png"}, "nodeParent": 66, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 28, "child": []}, {"type": "Image", "props": {"y": 728, "x": 80, "skin": "activity/Number_of_people.png"}, "nodeParent": 66, "label": "Image", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}, {"type": "Image", "props": {"y": 616, "x": 460, "skin": "activity/Number_of_people.png"}, "nodeParent": 66, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 30, "child": []}, {"type": "Image", "props": {"y": 511, "x": -4, "skin": "activity/Number_of_people.png"}, "nodeParent": 66, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 31, "child": []}, {"type": "Image", "props": {"y": 306, "x": 212, "skin": "activity/Number_of_people.png"}, "nodeParent": 66, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": []}], "$LOCKED": false}, {"x": 30, "type": "Text", "props": {"y": 940, "x": 308, "width": 80, "var": "txt_1", "valign": "middle", "strokeColor": "#000000", "stroke": 1, "height": 24, "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 11, "label": "Text(txt_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 33, "child": []}, {"x": 30, "type": "Text", "props": {"y": 816, "x": 466, "width": 80, "var": "txt_2", "valign": "middle", "strokeColor": "#000000", "stroke": 1, "height": 24, "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 11, "label": "Text(txt_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 34, "child": []}, {"x": 30, "type": "Text", "props": {"y": 730, "x": 162, "width": 80, "var": "txt_3", "valign": "middle", "strokeColor": "#000000", "stroke": 1, "height": 24, "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 11, "label": "Text(txt_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 35, "child": []}, {"x": 30, "type": "Text", "props": {"y": 619, "x": 541, "width": 80, "var": "txt_4", "valign": "middle", "strokeColor": "#000000", "stroke": 1, "height": 24, "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 11, "label": "Text(txt_4)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 36, "child": []}, {"x": 30, "type": "Text", "props": {"y": 514, "x": 78, "width": 80, "var": "txt_5", "valign": "middle", "strokeColor": "#000000", "stroke": 1, "height": 24, "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 11, "label": "Text(txt_5)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 37, "child": []}, {"x": 30, "type": "Text", "props": {"y": 307, "x": 294, "width": 80, "var": "txt_6", "valign": "middle", "strokeColor": "#000000", "stroke": 1, "height": 24, "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 11, "label": "Text(txt_6)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 38, "child": []}, {"x": 30, "type": "Text", "props": {"y": 258, "x": 225, "width": 217, "var": "rewardTxt", "valign": "middle", "strokeColor": "#d02824", "stroke": 2, "height": 36, "fontSize": 30, "color": "#ffeaa5", "align": "center"}, "nodeParent": 11, "label": "Text(rewardTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 39, "child": []}], "$LOCKED": false}, {"x": 15, "type": "Text", "props": {"y": 128, "x": 0, "width": 750, "var": "infoTxt", "valign": "middle", "text": "25% of prize pool for the winner！", "mouseThrough": true, "height": 40, "fontSize": 22, "color": "#167b99", "align": "center"}, "nodeParent": 1, "label": "Text(infoTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": []}, {"x": 15, "type": "Image", "props": {"y": 1142, "x": 525, "visible": false, "var": "prop_icon_bg", "skin": "public/prop_bg_icon.png", "centerX": 195, "bottom": 102}, "nodeParent": 1, "label": "Image(prop_icon_bg)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 84, "child": [{"type": "Image", "props": {"y": 44, "x": 45, "width": 60, "var": "propIcon", "skin": "public/propIcon.png", "height": 60, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 84, "label": "Image(propIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 85, "child": []}, {"type": "Image", "props": {"y": -6, "x": 25, "var": "winIcon", "skin": "public/pic_WIN.png"}, "nodeParent": 84, "label": "Image(winIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 87, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}