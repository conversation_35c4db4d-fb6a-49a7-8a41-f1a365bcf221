{"x": 0, "type": "View", "selectedBox": 2, "selecteID": 8, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"top": 0, "skin": "activity/alphaBg80.png", "sizeGrid": "1,1,1,1", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": []}, {"x": 15, "type": "Box", "props": {"width": 750, "var": "box", "height": 1332, "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Box(box)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 1164, "x": 200, "width": 297, "var": "closeBtn", "skin": "activity/btn_G.png", "labelStrokeColor": "#61f2ca", "labelStroke": 2, "labelSize": 39, "labelColors": "#08846b", "labelAlign": "center", "label": "Close", "height": 115, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 2, "label": "Button(closeBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 30, "type": "Box", "props": {"y": 0, "width": 750, "var": "bannerBox", "height": 1050, "centerX": 0}, "nodeParent": 2, "label": "Box(bannerBox)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}, {"x": 30, "type": "Box", "props": {"y": 462, "width": 427, "var": "contentBox", "height": 465, "centerX": 0, "alpha": 0}, "nodeParent": 2, "label": "Box(contentBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 11, "child": [{"x": 45, "type": "Image", "props": {"y": 296, "width": 190, "skin": "activity/gold.png", "height": 42, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 11, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}, {"x": 45, "type": "Label", "props": {"text": "Congratulations!", "strokeColor": "#8c1728", "stroke": 2, "right": 0, "left": 0, "height": 68, "fontSize": 33, "color": "#ffffff", "align": "center"}, "nodeParent": 11, "label": "Label", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 45, "type": "Label", "props": {"y": 43, "text": "You win the big prize.", "strokeColor": "#8c1728", "stroke": 2, "right": 0, "left": 0, "height": 68, "fontSize": 29, "color": "#ffffff", "align": "center"}, "nodeParent": 11, "label": "Label", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 45, "type": "Label", "props": {"y": 281, "x": 153, "width": 130, "var": "rewardTxt", "height": 32, "fontSize": 32, "color": "#ffe77f", "align": "center"}, "nodeParent": 11, "label": "Label(rewardTxt)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 124, "x": 286, "visible": false, "var": "crown", "skin": "activity/huangguan_19.png"}, "nodeParent": 2, "label": "Image(crown)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 1164, "x": 556, "width": 297, "var": "againBtn", "skin": "activity/btn_y.png", "labelStrokeColor": "#fff08b", "labelStroke": 2, "labelSize": 39, "labelColors": "#ab6e0f", "labelAlign": "center", "height": 115, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 2, "label": "Button(againBtn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 45, "type": "Image", "props": {"y": 64, "x": 94, "width": 29, "skin": "activity/coins.png", "height": 30}, "nodeParent": 4, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}, {"x": 45, "type": "Text", "props": {"y": 17, "x": 3, "width": 291, "valign": "middle", "text": "Play Again", "strokeColor": "#fff08b", "stroke": 2, "height": 40, "fontSize": 40, "color": "#ab6e0f", "align": "center"}, "nodeParent": 4, "label": "Text", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 45, "type": "Text", "props": {"y": 63, "x": 129, "var": "costTxt", "valign": "middle", "text": "6000", "strokeColor": "#fff08b", "stroke": 2, "fontSize": 30, "color": "#ab6e0f", "bold": true, "align": "left"}, "nodeParent": 4, "label": "Text(costTxt)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}]}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}