{"x": 0, "type": "View", "selectedBox": 252, "selecteID": 266, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "mouseThrough": false, "mouseEnabled": true, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "bg", "top": 0, "skin": "activity/alphaBg80.png", "right": 0, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 259, "child": []}, {"x": 15, "type": "Image", "props": {"width": 718, "skin": "activity/image_bg_title.png", "height": 980, "centerY": 0, "centerX": 0, "cacheAsBitmap": true}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 36, "child": [{"x": 30, "type": "Image", "props": {"x": 8, "width": 701, "top": 93, "skin": "activity/image_bg_content.png", "height": 876}, "nodeParent": 36, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 136, "child": []}, {"x": 30, "type": "Image", "props": {"y": 7, "x": 603, "width": 130, "var": "closeBtn", "skin": "activity/alphaBg.png", "height": 82}, "nodeParent": 36, "label": "Image(closeBtn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 257, "child": [{"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 22, "x": 44, "stateNum": "1", "skin": "activity/btn_close.png"}, "nodeParent": 257, "label": "<PERSON><PERSON>", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 258, "child": []}]}, {"x": 30, "type": "Panel", "props": {"y": 107, "x": 21, "width": 679, "var": "panel", "height": 841}, "nodeParent": 36, "label": "Panel(panel)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 251, "child": [{"x": 45, "type": "Image", "props": {"y": 752, "x": 6, "width": 666, "visible": true, "skin": "activity/bg_others.png", "height": 286}, "nodeParent": 251, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 263, "child": []}, {"x": 45, "type": "Image", "props": {"y": 4, "x": 6, "width": 666, "visible": true, "var": "contentBg", "skin": "activity/bg_others.png", "height": 736}, "nodeParent": 251, "label": "Image(contentBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 140, "child": []}, {"x": 45, "type": "VBox", "props": {"y": 16, "x": 17, "width": 0, "var": "ruleList", "space": 19, "height": 0}, "nodeParent": 251, "label": "<PERSON><PERSON><PERSON>(ruleList)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 252, "child": [{"x": 60, "type": "Box", "props": {"y": 746, "x": 4, "width": 639, "height": 256}, "nodeParent": 252, "label": "Box", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 266, "child": [{"x": 75, "type": "Image", "props": {"y": 10, "var": "img_rule", "skin": "activity/img_rule_en.png"}, "nodeParent": 266, "label": "Image(img_rule)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 264, "child": []}]}, {"x": 60, "type": "Box", "props": {"y": 0, "x": 0, "width": 630, "height": 610}, "nodeParent": 252, "label": "Box", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 268, "child": [{"type": "Image", "props": {"y": 26, "x": 38, "width": 122, "skin": "activity/islands_1.png", "height": 98}, "nodeParent": 268, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 269, "child": []}, {"type": "Image", "props": {"y": 76, "x": 187, "width": 36, "skin": "activity/Greenarrow.png", "height": 23}, "nodeParent": 268, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 270, "child": []}, {"type": "Image", "props": {"y": 14, "x": 250, "width": 134, "skin": "activity/islands_2.png", "height": 110}, "nodeParent": 268, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 271, "child": []}, {"type": "Image", "props": {"y": 0, "x": 481, "width": 146, "skin": "activity/islands_3.png", "height": 124}, "nodeParent": 268, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 272, "child": []}, {"type": "Image", "props": {"y": 164, "x": 370, "width": 163, "skin": "activity/islands_4.png", "height": 159}, "nodeParent": 268, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 273, "child": []}, {"type": "Image", "props": {"y": 154, "x": 93, "width": 163, "skin": "activity/islands_5.png", "height": 168}, "nodeParent": 268, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 274, "child": []}, {"type": "Image", "props": {"y": 369, "x": 118, "width": 394, "skin": "activity/6_42.png", "height": 239}, "nodeParent": 268, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 275, "child": []}, {"type": "Image", "props": {"y": 530, "x": 50, "width": 532, "skin": "activity/v.png", "sizeGrid": "0,195,0,195", "height": 66}, "nodeParent": 268, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 276, "child": []}, {"type": "Image", "props": {"y": 76, "x": 416, "width": 36, "skin": "activity/Greenarrow.png", "height": 23}, "nodeParent": 268, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 277, "child": []}, {"type": "Image", "props": {"y": 151, "x": 559, "width": 43, "skin": "activity/Greenarrow.png", "skewY": 1, "rotation": 130, "height": 23}, "nodeParent": 268, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 278, "child": []}, {"type": "Image", "props": {"y": 250, "x": 335, "width": 44, "skin": "activity/Greenarrow.png", "scaleX": -1, "height": 28}, "nodeParent": 268, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 279, "child": []}, {"type": "Image", "props": {"y": 88, "x": 40, "width": 30, "skin": "activity/coins.png", "height": 31}, "nodeParent": 268, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 280, "child": []}, {"type": "Image", "props": {"y": 88, "x": 258, "width": 30, "skin": "activity/coins.png", "height": 31}, "nodeParent": 268, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 282, "child": []}, {"type": "Image", "props": {"y": 88, "x": 494, "width": 30, "skin": "activity/coins.png", "height": 31}, "nodeParent": 268, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 284, "child": []}, {"type": "Image", "props": {"y": 286, "x": 386, "width": 30, "skin": "activity/coins.png", "height": 31}, "nodeParent": 268, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 286, "child": []}, {"type": "Image", "props": {"y": 286, "x": 112, "width": 30, "skin": "activity/coins.png", "height": 31}, "nodeParent": 268, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 288, "child": []}, {"type": "Image", "props": {"y": 553, "x": 149, "width": 31, "skin": "activity/coins.png", "height": 31}, "nodeParent": 268, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 290, "child": []}, {"type": "Image", "props": {"y": 329, "x": 194, "width": 43, "skin": "activity/Greenarrow.png", "skewY": 1, "rotation": 60, "height": 23}, "nodeParent": 268, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 292, "child": []}, {"type": "Image", "props": {"y": 338, "x": 272, "width": 85, "var": "img_win", "skin": "activity/img_win_en.png", "height": 35}, "nodeParent": 268, "label": "Image(img_win)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 293, "child": []}, {"type": "Label", "props": {"y": 91, "x": 74, "width": 100, "var": "txt_1", "text": "1000", "strokeColor": "#000000", "stroke": 2, "height": 26, "fontSize": 22, "color": "#ffffff"}, "nodeParent": 268, "label": "Label(txt_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 281, "child": []}, {"type": "Label", "props": {"y": 91, "x": 292, "width": 100, "var": "txt_2", "text": "1000", "strokeColor": "#000000", "stroke": 2, "height": 26, "fontSize": 22, "color": "#ffffff"}, "nodeParent": 268, "label": "Label(txt_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 283, "child": []}, {"type": "Label", "props": {"y": 91, "x": 528, "width": 100, "var": "txt_3", "text": "1000", "strokeColor": "#000000", "stroke": 2, "height": 26, "fontSize": 22, "color": "#ffffff"}, "nodeParent": 268, "label": "Label(txt_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 285, "child": []}, {"type": "Label", "props": {"y": 289, "x": 420, "width": 100, "var": "txt_4", "text": "1000", "strokeColor": "#000000", "stroke": 2, "height": 26, "fontSize": 22, "color": "#ffffff"}, "nodeParent": 268, "label": "Label(txt_4)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 287, "child": []}, {"type": "Label", "props": {"y": 289, "x": 146, "width": 100, "var": "txt_5", "text": "1000", "strokeColor": "#000000", "stroke": 2, "height": 26, "fontSize": 22, "color": "#ffffff"}, "nodeParent": 268, "label": "Label(txt_5)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 289, "child": []}, {"type": "Label", "props": {"y": 557, "x": 181, "width": 302, "var": "txt_6", "text": "1000", "strokeColor": "#bd3930", "stroke": 2, "height": 26, "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 268, "label": "Label(txt_6)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 291, "child": []}]}, {"x": 60, "type": "Label", "props": {"y": 650, "x": 6, "wordWrap": true, "width": 646, "var": "content_1", "text": "You can share 25% of the prize pool as extra bonus, if you win the final round.", "height": 98, "fontSize": 28, "color": "#548172"}, "nodeParent": 252, "label": "Label(content_1)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 262, "child": []}], "$LOCKED": false, "$HIDDEN": false}]}, {"x": 30, "type": "Label", "props": {"y": 24, "width": 712, "var": "titleTxt", "valign": "middle", "text": "Rules ", "strokeColor": "#30876d", "stroke": 3, "height": 54, "fontSize": 36, "color": "#ffffff", "centerX": 0, "align": "center"}, "nodeParent": 36, "label": "Label(titleTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 73, "child": []}], "$LOCKED": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}