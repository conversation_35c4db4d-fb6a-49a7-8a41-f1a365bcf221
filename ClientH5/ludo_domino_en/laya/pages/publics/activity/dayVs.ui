{"x": 0, "type": "View", "selectedBox": 64, "selecteID": 67, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"top": 0, "skin": "activity/alphaBg80.png", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 51, "var": "btnClose", "stateNum": "1", "skin": "activity/dropout.png", "right": 0}, "nodeParent": 1, "label": "<PERSON><PERSON>(btnClose)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 61, "child": []}, {"x": 15, "type": "Box", "props": {"var": "vsBox"}, "nodeParent": 1, "label": "Box(vsBox)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 62, "child": []}, {"x": 15, "type": "Box", "props": {"y": 286, "visible": false, "var": "titleBox", "height": 68, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(titleBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 64, "child": [{"x": 30, "type": "Image", "props": {"y": 0, "x": 156, "width": 56, "var": "goldIcon", "skin": "activity/coins.png", "height": 61}, "nodeParent": 64, "label": "Image(goldIcon)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 66, "child": []}, {"x": 30, "type": "Text", "props": {"y": 0, "x": 0, "var": "WinTxt", "strokeColor": "#985b00", "stroke": 4, "height": 60, "fontSize": 60, "color": "#ffed72"}, "nodeParent": 64, "label": "Text(WinTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 69, "child": []}, {"x": 30, "type": "Text", "props": {"y": 2, "x": 222, "var": "winGoldTxt", "strokeColor": "#985b00", "stroke": 4, "height": 54, "fontSize": 54, "color": "#ffed72"}, "nodeParent": 64, "label": "Text(winGoldTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 70, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}