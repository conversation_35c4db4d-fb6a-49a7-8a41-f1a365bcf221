{"x": 0, "type": "View", "selectedBox": 6, "selecteID": 11, "props": {"width": 250, "sceneColor": "#000000", "height": 250}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"y": 125, "x": 125, "width": 154, "var": "head", "height": 154, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(head)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 10, "child": [{"x": 30, "type": "Image", "props": {"y": 10, "x": 10, "width": 136, "var": "face", "skin": "activity/default_head.png", "height": 136}, "nodeParent": 10, "label": "Image(face)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 7, "child": [{"x": 45, "type": "Image", "props": {"y": 0, "x": 0, "width": 136, "skin": "activity/head_mask.png", "renderType": "mask", "height": 136}, "nodeParent": 7, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": [], "$HIDDEN": false}], "$LOCKED": false, "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 77, "x": 77, "width": 154, "var": "bg", "skin": "activity/default.png", "height": 154, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 10, "label": "Image(bg)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": [], "$LOCKED": false, "$HIDDEN": false}]}, {"x": 15, "type": "Image", "props": {"y": 225, "width": 140, "visible": false, "var": "costBox", "skin": "activity/gold.png", "height": 31, "centerX": 3, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image(costBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 30, "type": "Label", "props": {"y": 3, "x": 17, "width": 112, "var": "costTxt", "height": 28, "fontSize": 20, "color": "#ffe77f", "align": "center"}, "nodeParent": 4, "label": "Label(costTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}]}, {"x": 15, "type": "Box", "props": {"y": 18, "width": 250, "var": "nameBox", "height": 34, "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(nameBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"x": 30, "type": "Label", "props": {"y": 0, "x": 0, "width": 250, "visible": false, "var": "nameTxt", "height": 34, "fontSize": 24, "color": "#ffffff", "align": "center"}, "nodeParent": 6, "label": "Label(nameTxt)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$LOCKED": true}, {"x": 30, "type": "UIView", "source": "publics/item/nameItem.ui", "props": {"y": 0, "x": 0, "visible": false, "var": "nameItem"}, "nodeParent": 6, "label": "UIView(nameItem)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": [], "$LOCKED": false}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}