{"x": 0, "type": "View", "selectedBox": 10, "selecteID": 7, "props": {"width": 140, "sceneColor": "#000000", "height": 140}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"y": 68, "var": "head", "centerX": 0, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Box(head)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 10, "child": [{"x": 30, "type": "Image", "props": {"y": 12, "x": 12, "width": 110, "var": "face", "skin": "activity/default_head.png", "height": 110}, "nodeParent": 10, "label": "Image(face)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 7, "child": [{"x": 45, "type": "Image", "props": {"y": 0, "x": 0, "width": 110, "skin": "activity/head_mask.png", "renderType": "mask", "height": 110}, "nodeParent": 7, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": [], "$HIDDEN": false}], "$LOCKED": false, "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 0, "x": 0, "var": "bg", "skin": "activity/111.png"}, "nodeParent": 10, "label": "Image(bg)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": [], "$LOCKED": true, "$HIDDEN": false}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}