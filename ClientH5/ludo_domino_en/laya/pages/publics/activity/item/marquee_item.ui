{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 4, "props": {"width": 750, "sceneColor": "#000000", "height": 80}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "skin": "activity/BG_words.png", "centerX": 0}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 15, "type": "Label", "props": {"y": 14, "x": 750, "var": "txt_en", "height": 37, "fontSize": 30, "color": "#ffffff"}, "nodeParent": 1, "label": "Label(txt_en)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 15, "type": "Label", "props": {"y": 14, "x": 0, "var": "txt_ar", "height": 37, "fontSize": 30, "color": "#ffffff", "anchorX": 1, "align": "right"}, "nodeParent": 1, "label": "Label(txt_ar)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}