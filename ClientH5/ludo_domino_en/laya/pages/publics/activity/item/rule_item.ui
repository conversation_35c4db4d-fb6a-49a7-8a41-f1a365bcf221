{"x": 0, "type": "View", "selectedBox": 11, "selecteID": 42, "props": {"width": 630, "sceneColor": "#000000", "height": 630}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"y": 20, "x": 0, "width": 630, "var": "box", "height": 610}, "nodeParent": 1, "label": "Box(box)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 11, "child": [{"x": 30, "type": "Image", "props": {"y": 26, "x": 38, "width": 122, "skin": "activity/islands_1.png", "height": 98}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 30, "type": "Image", "props": {"y": 76, "x": 187, "width": 36, "skin": "activity/Greenarrow.png", "height": 23}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": []}, {"x": 30, "type": "Image", "props": {"y": 14, "x": 250, "width": 134, "skin": "activity/islands_2.png", "height": 110}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}, {"x": 30, "type": "Image", "props": {"y": 0, "x": 481, "width": 146, "skin": "activity/islands_3.png", "height": 124}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": []}, {"x": 30, "type": "Image", "props": {"y": 164, "x": 370, "width": 163, "skin": "activity/islands_4.png", "height": 159}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}, {"x": 30, "type": "Image", "props": {"y": 154, "x": 93, "width": 163, "skin": "activity/islands_5.png", "height": 168}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 30, "type": "Image", "props": {"y": 369, "x": 118, "width": 394, "skin": "activity/6_42.png", "height": 239}, "nodeParent": 11, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}, {"x": 30, "type": "Image", "props": {"y": 530, "x": 50, "width": 532, "skin": "activity/v.png", "sizeGrid": "0,195,0,195", "height": 66}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 42, "child": []}, {"x": 30, "type": "Image", "props": {"y": 76, "x": 416, "width": 36, "skin": "activity/Greenarrow.png", "height": 23}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 19, "child": []}, {"x": 30, "type": "Image", "props": {"y": 151, "x": 559, "width": 43, "skin": "activity/Greenarrow.png", "skewY": 1, "rotation": 130, "height": 23}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"x": 30, "type": "Image", "props": {"y": 250, "x": 335, "width": 44, "skin": "activity/Greenarrow.png", "scaleX": -1, "height": 28}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}, {"x": 30, "type": "Image", "props": {"y": 88, "x": 40, "width": 30, "skin": "activity/coins.png", "height": 31}, "nodeParent": 11, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 23, "child": []}, {"x": 30, "type": "Image", "props": {"y": 88, "x": 258, "width": 30, "skin": "activity/coins.png", "height": 31}, "nodeParent": 11, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": []}, {"x": 30, "type": "Image", "props": {"y": 88, "x": 494, "width": 30, "skin": "activity/coins.png", "height": 31}, "nodeParent": 11, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}, {"x": 30, "type": "Image", "props": {"y": 286, "x": 386, "width": 30, "skin": "activity/coins.png", "height": 31}, "nodeParent": 11, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}, {"x": 30, "type": "Image", "props": {"y": 286, "x": 112, "width": 30, "skin": "activity/coins.png", "height": 31}, "nodeParent": 11, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 31, "child": []}, {"x": 30, "type": "Image", "props": {"y": 553, "x": 149, "width": 31, "skin": "activity/coins.png", "height": 31}, "nodeParent": 11, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 33, "child": []}, {"x": 30, "type": "Image", "props": {"y": 329, "x": 194, "width": 43, "skin": "activity/Greenarrow.png", "skewY": 1, "rotation": 60, "height": 23}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 41, "child": []}, {"x": 30, "type": "Image", "props": {"y": 344, "x": 272, "width": 85, "skin": "activity/win.png", "height": 35}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 43, "child": []}, {"x": 30, "type": "Label", "props": {"y": 91, "x": 74, "width": 100, "var": "txt_1", "text": "1000", "strokeColor": "#000000", "stroke": 2, "height": 26, "fontSize": 22, "color": "#ffffff"}, "nodeParent": 11, "label": "Label(txt_1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": []}, {"x": 30, "type": "Label", "props": {"y": 91, "x": 292, "width": 100, "var": "txt_2", "text": "1000", "strokeColor": "#000000", "stroke": 2, "height": 26, "fontSize": 22, "color": "#ffffff"}, "nodeParent": 11, "label": "Label(txt_2)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": []}, {"x": 30, "type": "Label", "props": {"y": 91, "x": 528, "width": 100, "var": "txt_3", "text": "1000", "strokeColor": "#000000", "stroke": 2, "height": 26, "fontSize": 22, "color": "#ffffff"}, "nodeParent": 11, "label": "Label(txt_3)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 28, "child": []}, {"x": 30, "type": "Label", "props": {"y": 289, "x": 420, "width": 100, "var": "txt_4", "text": "1000", "strokeColor": "#000000", "stroke": 2, "height": 26, "fontSize": 22, "color": "#ffffff"}, "nodeParent": 11, "label": "Label(txt_4)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 30, "child": []}, {"x": 30, "type": "Label", "props": {"y": 289, "x": 146, "width": 100, "var": "txt_5", "text": "1000", "strokeColor": "#000000", "stroke": 2, "height": 26, "fontSize": 22, "color": "#ffffff"}, "nodeParent": 11, "label": "Label(txt_5)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": []}, {"x": 30, "type": "Label", "props": {"y": 557, "x": 181, "width": 302, "var": "txt_6", "text": "1000", "strokeColor": "#bd3930", "stroke": 2, "height": 26, "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 11, "label": "Label(txt_6)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 34, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}