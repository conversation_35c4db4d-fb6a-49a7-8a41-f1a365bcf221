{"type": "View", "selectedBox": 1, "props": {"sceneWidth": 600, "sceneHeight": 400, "sceneColor": "#000000"}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"type": "Image", "props": {"y": 0, "x": 0, "skin": "duel/duel_knife.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}], "animations": [{"nodes": [{"target": 2, "keyframes": {"scaleY": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 0}, {"value": 1.3, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 15}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 20}], "scaleX": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 0}, {"value": 1.3, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 15}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 20}]}}], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}