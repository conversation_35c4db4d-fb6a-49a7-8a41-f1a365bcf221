{"x": 0, "type": "View", "selectedBox": 1, "props": {"sceneWidth": 1334, "sceneHeight": 750, "sceneColor": "#000000"}, "nodeParent": -1, "label": "View", "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"type": "GraphicNode", "props": {}, "nodeParent": 1, "label": "GraphicNode", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}], "animations": [{"nodes": [{"target": 2, "keyframes": {"y": [{"value": 51, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "y", "index": 0}, {"value": 66, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "y", "index": 20}, {"value": 51, "tweenMethod": "linearNone", "tween": true, "target": 2, "label": null, "key": "y", "index": 40}], "x": [{"value": 55, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "x", "index": 0}, {"value": 69, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "x", "index": 20}, {"value": 54, "tweenMethod": "linearNone", "tween": true, "target": 2, "label": null, "key": "x", "index": 40}], "skin": [{"value": "game/finger.png", "tweenMethod": "linearNone", "tween": false, "target": 2, "key": "skin", "index": 0}, {"value": "game/finger.png", "tweenMethod": "linearNone", "tween": false, "target": 2, "label": null, "key": "skin", "index": 40}], "scaleY": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 0}, {"value": 1.2, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 20}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "label": null, "key": "scaleY", "index": 40}], "scaleX": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 0}, {"value": 1.2, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 20}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "label": null, "key": "scaleX", "index": 40}], "rotation": [{"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "rotation", "index": 0}, {"value": 5, "tweenMethod": "linearNone", "tween": true, "target": 2, "label": null, "key": "rotation", "index": 40}], "anchorY": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "anchorY", "index": 0}, {"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "label": null, "key": "anchorY", "index": 40}], "anchorX": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "anchorX", "index": 0}, {"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "label": null, "key": "anchorX", "index": 40}]}}], "name": "play", "id": 1, "frameRate": 24, "action": 0}]}