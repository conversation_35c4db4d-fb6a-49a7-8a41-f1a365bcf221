{"type": "View", "selectedBox": 1, "props": {}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"type": "Image", "props": {"skin": "activity/islands_2.png", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}], "animations": [{"nodes": [{"target": 2, "keyframes": {"scaleY": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 0}, {"value": 1.2, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 10}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 20}], "scaleX": [{"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 0}, {"value": 1.2, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 10}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 20}], "anchorY": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "anchorY", "index": 0}], "anchorX": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "anchorX", "index": 0}]}}], "name": "levelSize", "id": 1, "frameRate": 24, "action": 0}]}