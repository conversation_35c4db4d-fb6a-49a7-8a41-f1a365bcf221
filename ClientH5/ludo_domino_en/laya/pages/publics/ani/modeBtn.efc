{"type": "View", "selectedBox": 1, "selecteID": 2, "props": {"sceneWidth": 1334, "sceneHeight": 750, "sceneColor": "#000000"}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"type": "<PERSON><PERSON>", "props": {"skin": "game/btn_y.png", "label": "label", "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 1, "label": "<PERSON><PERSON>", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}], "animations": [{"nodes": [{"target": 2, "keyframes": {"visible": [{"value": true, "tweenMethod": "linearNone", "tween": false, "target": 2, "key": "visible", "index": 0}], "scaleY": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 0}, {"value": 1.2, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 3}, {"value": 0.8, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 6}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleY", "index": 9}], "scaleX": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 0}, {"value": 1.2, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 3}, {"value": 0.8, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 6}, {"value": 1, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "scaleX", "index": 9}]}}], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}