{"x": 0, "type": "View", "selectedBox": 1, "props": {"sceneWidth": 1334, "sceneHeight": 750, "sceneColor": "#000000"}, "nodeParent": -1, "label": "View", "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"type": "GraphicNode", "props": {}, "nodeParent": 1, "label": "GraphicNode", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}], "animations": [{"nodes": [{"target": 2, "keyframes": {"y": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "y", "index": 0}], "x": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "x", "index": 0}], "skin": [{"value": "public/pic_reconnection.png", "tweenMethod": "linearNone", "tween": false, "target": 2, "key": "skin", "index": 0}], "rotation": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "rotation", "index": 0}, {"value": 360, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "rotation", "index": 30}], "pivotX": [{"value": 0, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "pivotX", "index": 0}], "anchorY": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "anchorY", "index": 0}], "anchorX": [{"value": 0.5, "tweenMethod": "linearNone", "tween": true, "target": 2, "key": "anchorX", "index": 0}]}}], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}