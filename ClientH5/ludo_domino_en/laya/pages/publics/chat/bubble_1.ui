{"x": 0, "type": "View", "selectedBox": 2, "selecteID": 9, "props": {"sceneWidth": 750, "sceneHeight": 1334, "sceneColor": "#000000"}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 37, "x": 0, "var": "arrow", "skin": "public/bubbles_angle_12000.png", "scaleX": -1, "rotation": 180}, "nodeParent": 1, "label": "Image(arrow)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 15, "type": "Image", "props": {"y": 28, "x": -85, "width": 166, "var": "box", "skin": "public/bubbles_12000.png", "sizeGrid": "40,40,40,40", "height": 98, "anchorY": 0, "anchorX": 0}, "nodeParent": 1, "label": "Image(box)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Image", "props": {"var": "bottom_icon", "skin": "public/white.png", "right": -30, "bottom": -30}, "nodeParent": 2, "label": "Image(bottom_icon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}, {"x": 30, "type": "Image", "props": {"var": "top_icon", "top": -30, "skin": "public/white.png", "right": -30}, "nodeParent": 2, "label": "Image(top_icon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}, {"x": 30, "type": "Box", "props": {"var": "sk_box", "top": 20, "right": 20, "left": 20, "bottom": 20}, "nodeParent": 2, "label": "Box(sk_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 30, "type": "Box", "props": {"var": "emoji_box", "top": 20, "right": 20, "left": 20, "bottom": 20}, "nodeParent": 2, "label": "Box(emoji_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": [], "$HIDDEN": true}, {"x": 30, "type": "Label", "props": {"wordWrap": true, "var": "lb", "valign": "middle", "text": "label", "overflow": "hidden", "fontSize": 30, "color": "#343434", "centerY": 0, "centerX": 0}, "nodeParent": 2, "label": "Label(lb)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}