{"x": 0, "type": "View", "selectedBox": 2, "selecteID": 3, "props": {"sceneWidth": 750, "sceneHeight": 1334, "sceneColor": "#000000"}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": -8, "x": -70, "width": 140, "var": "box", "skin": "public/muteBubble.png", "sizeGrid": "40,40,40,40", "height": 60, "anchorY": 1, "anchorX": 0}, "nodeParent": 1, "label": "Image(box)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Image", "props": {"x": 62, "var": "arrow", "top": 58, "skin": "public/muteBubbleArrow.png"}, "nodeParent": 2, "label": "Image(arrow)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"width": 140, "var": "repartBtn", "stateNum": "1", "labelSize": 28, "labelColors": "#ffffff", "label": "Report", "height": 60}, "nodeParent": 2, "label": "Button(repartBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}], "$LOCKED": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}