{"x": 0, "type": "View", "selectedBox": 5, "selecteID": 81, "props": {"width": 750, "top": -300, "sceneColor": "#000", "right": 0, "mouseThrough": true, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "bg", "top": 0, "sizeGrid": "2,2,2,2", "right": 0, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 60, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"width": 750, "var": "box", "top": 0, "right": 0, "mouseThrough": true, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": 1, "label": "Box(box)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 62, "child": [{"x": 30, "type": "Image", "props": {"y": 6, "x": 12, "var": "chatbox", "top": 0, "right": 0, "mouseThrough": false, "left": 0, "bottom": 0}, "nodeParent": 62, "label": "Image(chatbox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 45, "type": "Image", "props": {"var": "chatboxBg", "top": 0, "sizeGrid": "2,4,2,2", "right": 0, "left": 0, "bottom": 0}, "nodeParent": 4, "label": "Image(chatboxBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 66, "child": []}, {"x": 45, "type": "Image", "props": {"var": "banner", "top": -3, "sizeGrid": "3,4,6,5", "right": -6, "left": -6, "height": 70}, "nodeParent": 4, "label": "Image(banner)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 45, "type": "Box", "props": {"visible": false, "var": "channelBannerBox", "top": -3, "right": -6, "left": -6, "height": 70}, "nodeParent": 4, "label": "Box(channelBannerBox)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 69, "child": []}, {"x": 45, "type": "Label", "props": {"y": -1, "wordWrap": true, "width": 750, "var": "chatTitleTxt", "valign": "middle", "text": "Please chat decently", "right": 0, "left": 0, "height": 56, "fontSize": 27, "color": "#ffffff", "align": "center"}, "nodeParent": 4, "label": "Label(chatTitleTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": [], "$LOCKED": true}, {"x": 45, "type": "Panel", "props": {"x": 0, "var": "chatPanel", "top": 75, "right": 0, "left": 0, "bottom": 125}, "nodeParent": 4, "label": "Panel(chatPanel)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 52, "child": [{"x": 60, "type": "VBox", "props": {"var": "chatList", "space": 5, "right": 0, "left": 0}, "nodeParent": 52, "label": "VBox(chatList)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 47, "child": [], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 45, "type": "Panel", "props": {"y": 75, "x": 0, "visible": false, "var": "teamChatPanel", "top": 75, "right": 0, "left": 0, "bottom": 125}, "nodeParent": 4, "label": "Panel(teamChatPanel)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 80, "child": [{"x": 60, "type": "VBox", "props": {"y": 0, "x": 0, "var": "teamChatList"}, "nodeParent": 80, "label": "<PERSON><PERSON><PERSON>(teamChatList)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 79, "child": []}], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"var": "footer", "sizeGrid": "7,10,12,10", "right": -6, "left": -6, "height": 125, "bottom": -15}, "nodeParent": 62, "label": "Image(footer)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 5, "child": [{"x": 45, "type": "Image", "props": {"y": 22, "var": "inputBgImg", "sizeGrid": "16,13,15,15", "right": 256, "left": 21, "height": 74}, "nodeParent": 5, "label": "Image(inputBgImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 81, "child": []}, {"x": 45, "type": "Image", "props": {"y": 110, "x": 100, "var": "arrow", "anchorX": 0.5}, "nodeParent": 5, "label": "Image(arrow)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": -74, "x": 496, "width": 260, "visible": false, "var": "msgHintBtn", "stateNum": "1", "sizeGrid": "0,14,0,57", "labelSize": 26, "labelPadding": "0,-26,2,0", "labelColors": "#45c9a6", "label": "NEW MESSAGE", "height": 66}, "nodeParent": 5, "label": "Button(msgHintBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 63, "child": []}, {"x": 45, "type": "Image", "props": {"y": 22, "width": 225, "var": "sendBtn", "sizeGrid": "23,23,33,23", "right": 20, "name": "send", "height": 88}, "nodeParent": 5, "label": "Image(sendBtn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 65, "child": [{"type": "Label", "props": {"y": 4, "width": 188, "var": "sendTxt", "text": "Send", "strokeColor": "#61f2ca", "stroke": 2, "height": 36, "fontSize": 36, "color": "#08846b", "centerX": 0, "align": "center"}, "nodeParent": 65, "label": "Label(sendTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": [], "$LOCKED": false, "$HIDDEN": false}, {"type": "Label", "props": {"y": 44, "width": 188, "var": "sendNumTxt", "strokeColor": "#61f2ca", "stroke": 2, "height": 28, "fontSize": 24, "color": "#08846b", "centerX": 0, "align": "center"}, "nodeParent": 65, "label": "Label(sendNumTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 64, "child": [], "$LOCKED": false}]}, {"x": 45, "type": "TextInput", "props": {"y": 26, "wordWrap": false, "width": 481, "var": "inputChat", "type": "text", "sizeGrid": "16,13,15,15", "right": 256, "promptColor": "#c8c7c7", "prompt": "Tap here to enter chat content", "padding": "0,20,0,20", "overflow": "scroll", "left": 21, "height": 70, "fontSize": 26, "color": "#000000"}, "nodeParent": 5, "label": "TextInput(inputChat)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}], "$HIDDEN": false}], "$LOCKED": false, "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}