{"x": 0, "type": "View", "selectedBox": 20, "selecteID": 21, "props": {"sceneWidth": 750, "sceneHeight": 1334, "sceneColor": "#ffffff", "right": 0, "left": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "VBox", "props": {"var": "item", "space": 6, "right": 0, "left": 0}, "nodeParent": 1, "label": "VBox(item)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 15, "child": [{"x": 30, "type": "Box", "props": {"var": "bb", "top": 6, "right": 0, "left": 0}, "nodeParent": 15, "label": "Box(bb)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 14, "child": [{"x": 45, "type": "Label", "props": {"y": 6, "x": 89, "var": "nick<PERSON><PERSON>", "text": "label", "height": 25, "fontSize": 22, "color": "#8e8e8e"}, "nodeParent": 14, "label": "Label(nickName)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 45, "type": "Label", "props": {"y": 40, "x": 90, "wordWrap": true, "var": "chat_tx", "text": "----", "fontSize": 24, "color": "#000000"}, "nodeParent": 14, "label": "Label(chat_tx)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 45, "type": "Image", "props": {"y": 10, "x": 10, "width": 60, "var": "head", "skin": "public/default_head.png", "height": 60}, "nodeParent": 14, "label": "Image(head)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 20, "child": [{"x": 60, "type": "Image", "props": {"y": 0, "x": 0, "width": 60, "skin": "public/head_mask.png", "renderType": "mask", "height": 60}, "nodeParent": 20, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": [], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 45, "type": "Image", "props": {"y": 0, "x": 0, "width": 80, "var": "head_frame", "skin": "public/face_bg.png", "height": 80}, "nodeParent": 14, "label": "Image(head_frame)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Image", "props": {"y": 34, "x": 88, "width": 36, "visible": false, "var": "chat_emoji", "skin": "public/icon_emoji_laugh.png", "height": 36}, "nodeParent": 14, "label": "Image(chat_emoji)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": [], "$HIDDEN": false}]}, {"x": 30, "type": "Image", "props": {"var": "line", "skin": "public/chat_line.png", "right": 0, "left": 0, "height": 4, "bottom": -12}, "nodeParent": 15, "label": "Image(line)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}