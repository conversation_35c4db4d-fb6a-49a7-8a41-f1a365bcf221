{"x": 0, "type": "View", "selectedBox": 20, "selecteID": 21, "props": {"sceneWidth": 750, "sceneHeight": 1334, "sceneColor": "#ffffff", "right": 0, "left": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "VBox", "props": {"var": "item", "space": 6, "right": 0, "left": 0}, "nodeParent": 1, "label": "VBox(item)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 15, "child": [{"x": 30, "type": "Box", "props": {"x": 0, "width": 237, "var": "bb", "top": 6, "height": 165}, "nodeParent": 15, "label": "Box(bb)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 14, "child": [{"x": 45, "type": "Image", "props": {"y": 24, "x": 10, "width": 60, "var": "head", "skin": "public/default_head.png", "height": 60}, "nodeParent": 14, "label": "Image(head)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 20, "child": [{"x": 60, "type": "Image", "props": {"y": 0, "x": 0, "width": 60, "skin": "public/head_mask.png", "renderType": "mask", "height": 60}, "nodeParent": 20, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": [], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 45, "type": "Image", "props": {"y": 14, "x": 0, "width": 80, "var": "head_frame", "skin": "public/face_bg.png", "height": 80}, "nodeParent": 14, "label": "Image(head_frame)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Image", "props": {"y": 28, "x": 82, "width": 96, "visible": false, "var": "chat_emoji", "height": 96}, "nodeParent": 14, "label": "Image(chat_emoji)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": [], "$HIDDEN": false}, {"x": 45, "type": "Box", "props": {"y": -2, "x": 102, "width": 40, "visible": false, "var": "RL", "height": 28, "anchorX": 0}, "nodeParent": 14, "label": "Box(RL)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 30, "child": []}, {"x": 45, "type": "Box", "props": {"y": -2, "x": 145, "width": 40, "visible": false, "var": "VIP", "height": 28, "anchorX": 0}, "nodeParent": 14, "label": "Box(VIP)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}, {"x": 45, "type": "Image", "props": {"width": 136, "var": "msgBox", "top": 20, "skin": "public/chat_left.png", "sizeGrid": "68,86,44,42", "left": 86, "height": 114}, "nodeParent": 14, "label": "Image(msgBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 23, "child": [{"x": 60, "type": "HTMLDivElement", "props": {"y": 46, "x": 40, "width": 72, "var": "lb", "height": 28}, "nodeParent": 23, "label": "HTMLDivElement(lb)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 28, "child": [], "$LOCKED": true, "$HIDDEN": false}]}, {"x": 45, "type": "Label", "props": {"y": 0, "x": 102, "var": "nick<PERSON><PERSON>", "text": "label", "height": 25, "fontSize": 24, "color": "#FFFFFF"}, "nodeParent": 14, "label": "Label(nickName)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$LOCKED": true}]}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}