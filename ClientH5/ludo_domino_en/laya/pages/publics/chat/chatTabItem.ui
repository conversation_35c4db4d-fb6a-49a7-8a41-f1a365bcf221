{"x": 0, "type": "<PERSON><PERSON>", "selectedBox": 1, "selecteID": 3, "props": {"y": 0, "x": 0, "width": 375, "var": "btn", "stateNum": "1", "skin": "game/btn_chatBanner_noSelect.png", "sizeGrid": "0,2,0,2", "height": 70}, "nodeParent": -1, "label": "Button(btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "icon", "skin": "game/ico_gameChat_noSelect.png", "centerY": 2, "centerX": -50}, "nodeParent": 1, "label": "Image(icon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 15, "type": "Label", "props": {"x": 179, "width": 97, "var": "txt", "valign": "middle", "text": "All", "strokeColor": "#7676CC", "stroke": 2, "height": 68, "fontSize": 36, "color": "#332E7E", "centerY": 2, "centerX": 40, "bold": true, "align": "left"}, "nodeParent": 1, "label": "Label(txt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}