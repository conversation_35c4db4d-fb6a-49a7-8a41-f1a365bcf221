{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 1, "props": {"y": 675, "x": 0, "width": 680, "sceneColor": "#000000", "height": 674, "cacheAs": "normal", "anchorY": 1}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 679, "var": "bg", "skin": "game/BG_Expression.png", "height": 674}, "nodeParent": 1, "label": "Image(bg)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"type": "Image", "props": {"width": 646, "var": "emojiBg", "skin": "game/table_ROOM.png", "height": 294, "centerX": 0, "bottom": 21}, "nodeParent": 2, "label": "Image(emojiBg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 12, "child": [{"type": "Image", "props": {"y": 0, "x": 0, "width": 646, "var": "tabBg", "skin": "game/biaoqing_tab_bg.png", "height": 80}, "nodeParent": 12, "label": "Image(tabBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}]}]}, {"x": 15, "type": "Image", "props": {"y": 670, "x": 232, "var": "arrow", "skin": "game/BG_Expression_V.png", "anchorX": 0.5}, "nodeParent": 1, "label": "Image(arrow)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 15, "type": "List", "props": {"y": 18, "x": 15, "width": 650, "var": "wordList", "top": 18, "spaceY": 13, "spaceX": 20, "repeatY": 4, "repeatX": 3, "height": 309, "centerX": 0}, "nodeParent": 1, "label": "List(wordList)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"type": "<PERSON><PERSON>", "props": {"width": 204, "stateNum": "1", "skin": "game/table_words.png", "sizeGrid": "16,14,15,15", "renderType": "render", "labelStrokeColor": "#105740", "labelSize": 24, "labelColors": "#ffffff", "labelAlign": "center", "label": "Hi", "height": 68}, "nodeParent": 4, "label": "<PERSON><PERSON>", "isDirectory": false, "isAniNode": false, "hasChild": false, "compId": 11, "child": []}]}, {"x": 15, "type": "List", "props": {"y": 359, "x": 18, "width": 646, "var": "tabList", "spaceY": 0, "spaceX": 0, "repeatX": 5, "height": 80}, "nodeParent": 1, "label": "List(tabList)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 23, "child": [{"type": "UIView", "source": "publics/chat/fastTabItem.ui", "props": {"renderType": "render"}, "nodeParent": 23, "label": "UIView", "isDirectory": false, "isAniNode": false, "hasChild": false, "compId": 24, "child": []}]}, {"x": 15, "type": "List", "props": {"y": 450, "x": 98, "width": 613, "var": "emojiList", "spaceY": 5, "spaceX": 36, "repeatY": 2, "repeatX": 5, "height": 186, "centerX": 0, "bottom": 38}, "nodeParent": 1, "label": "List(emojiList)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"type": "<PERSON><PERSON>", "props": {"width": 93, "stateNum": "1", "skin": "public/icon_emoji_laugh.png", "renderType": "render", "height": 93}, "nodeParent": 6, "label": "<PERSON><PERSON>", "isDirectory": false, "isAniNode": false, "hasChild": false, "compId": 9, "child": []}]}, {"x": 15, "type": "Box", "props": {"y": 357, "x": 17, "width": 646, "visible": true, "var": "bqBox"}, "nodeParent": 1, "label": "Box(bqBox)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 16, "child": [{"type": "Image", "props": {"y": 79, "x": 2, "width": 642, "var": "line", "skin": "game/biaoqing_line.png"}, "nodeParent": 16, "label": "Image(line)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"type": "Image", "props": {"y": 2, "x": -10, "var": "tabLeft", "skin": "game/biaoqing_bg_left.png"}, "nodeParent": 16, "label": "Image(tabLeft)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": []}, {"type": "Image", "props": {"y": 2, "x": 625, "var": "tabRight", "skin": "game/biaoqing_bg_right.png"}, "nodeParent": 16, "label": "Image(tabRight)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": []}], "$LOCKED": false, "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 437, "x": 17, "width": 646, "visible": false, "var": "maskBg", "skin": "game/biaoqing_mask_game.png", "mouseThrough": false, "mouseEnabled": true, "height": 214}, "nodeParent": 1, "label": "Image(maskBg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 21, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 75, "x": 206, "width": 235, "var": "buyBtn", "skin": "public/btn_y.png", "height": 69, "centerX": 0}, "nodeParent": 21, "label": "Button(buyBtn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 32, "child": [{"x": 45, "type": "Image", "props": {"y": 12, "x": 56, "var": "priceIcon", "skin": "public/Diamonds.png", "scaleY": 0.5, "scaleX": 0.5}, "nodeParent": 32, "label": "Image(priceIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 33, "child": []}, {"x": 45, "type": "Text", "props": {"y": 13, "x": 95, "width": 71, "var": "priceTxt", "text": "0", "strokeColor": "#FFF08B", "stroke": 2, "height": 31, "fontSize": 30, "color": "#AB6E0F"}, "nodeParent": 32, "label": "Text(priceTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 34, "child": []}]}, {"x": 30, "type": "Box", "props": {"var": "maskBox", "centerY": 0, "centerX": 0}, "nodeParent": 21, "label": "Box(maskBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 31, "child": [{"x": 45, "type": "Image", "props": {"y": 0, "x": 0, "skin": "public/lack.png"}, "nodeParent": 31, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}, {"x": 45, "type": "Text", "props": {"y": 6, "x": 42, "var": "txt_emoji", "text": "Exclusive for Royal users", "height": 29, "fontSize": 20, "color": "#ffffff", "bold": true}, "nodeParent": 31, "label": "Text(txt_emoji)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 28, "child": []}]}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}