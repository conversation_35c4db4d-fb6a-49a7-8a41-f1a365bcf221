{"x": 0, "type": "<PERSON><PERSON>", "selectedBox": 1, "selecteID": 4, "props": {"y": 0, "x": 0, "width": 120, "stateNum": "2", "skin": "game/interactive_btn.png", "renderType": "render", "mouseThrough": false, "mouseEnabled": true, "labelStrokeColor": "#105740", "labelSize": 24, "labelColors": "#ffffff", "labelAlign": "center", "height": 150}, "nodeParent": -1, "label": "<PERSON><PERSON>", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 110, "x": 14, "width": 25, "var": "needCoinImg", "skin": "game/coin.png", "height": 24}, "nodeParent": 1, "label": "Image(needCoinImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 15, "type": "Image", "props": {"y": 6, "x": 11, "width": 95, "var": "img_icon", "height": 95}, "nodeParent": 1, "label": "Image(img_icon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 15, "type": "Label", "props": {"y": 109, "x": 22, "width": 86, "var": "txt_coin", "height": 24, "fontSize": 24, "color": "#FDF75C", "align": "center"}, "nodeParent": 1, "label": "Label(txt_coin)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}