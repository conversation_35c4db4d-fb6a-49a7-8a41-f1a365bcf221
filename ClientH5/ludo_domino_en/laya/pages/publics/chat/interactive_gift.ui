{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 7, "props": {"width": 516, "sceneColor": "#000000", "height": 380, "cacheAs": "normal"}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "width": 525, "var": "bg", "skin": "game/interactive_bg.png", "sizeGrid": "78,26,24,25", "height": 378}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 15, "type": "Image", "props": {"y": 13, "x": 389, "width": 126, "var": "img_diamond_bg", "skin": "game/interactive_money_bg.png", "sizeGrid": "12,24,14,24", "height": 36}, "nodeParent": 1, "label": "Image(img_diamond_bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 14, "child": [{"x": 30, "type": "Image", "props": {"y": -2, "x": -8, "width": 36, "var": "diamondImg", "skin": "game/Diamonds.png", "height": 40}, "nodeParent": 14, "label": "Image(diamondImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 13, "x": 248, "width": 126, "var": "img_coin_bg", "skin": "game/interactive_money_bg.png", "sizeGrid": "12,24,14,24", "height": 36}, "nodeParent": 1, "label": "Image(img_coin_bg)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 17, "child": [{"x": 30, "type": "Image", "props": {"y": -2, "x": -10, "width": 40, "var": "coinImg", "skin": "game/coin.png", "height": 40}, "nodeParent": 17, "label": "Image(coinImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 160, "x": 3, "var": "img_arrow", "skin": "game/BG_Expression_V.png", "rotation": 90}, "nodeParent": 1, "label": "Image(img_arrow)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": []}, {"x": 15, "type": "CheckBox", "props": {"y": 6, "x": -1, "width": 114, "var": "checkBox_all", "stateNum": "2", "skin": "game/interactive_select.png", "sizeGrid": "-16,0,0,-16", "selected": true, "labelStrokeColor": "#6A6BA1", "labelStroke": 2, "labelSize": 24, "labelPadding": "12,0,0,0", "labelColors": "#FFFFFF,#FFFFFF", "label": "To All", "height": 56}, "nodeParent": 1, "label": "CheckBox(checkBox_all)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}, {"x": 15, "type": "List", "props": {"y": 67, "x": 15, "width": 498, "var": "giftList", "spaceY": 0, "spaceX": 0, "repeatY": 2, "height": 309}, "nodeParent": 1, "label": "List(giftList)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 7, "child": [{"x": 30, "type": "UIView", "source": "publics/chat/interactiveItem.ui", "props": {"renderType": "render"}, "nodeParent": 7, "label": "UIView", "isDirectory": false, "isAniNode": false, "hasChild": false, "compId": 20, "child": []}], "$HIDDEN": false}, {"x": 15, "type": "Label", "props": {"y": 18, "x": 419, "width": 90, "var": "txt_diamond", "height": 24, "fontSize": 24, "color": "#FFF95B", "align": "center"}, "nodeParent": 1, "label": "Label(txt_diamond)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}, {"x": 15, "type": "Label", "props": {"y": 18, "x": 279, "width": 90, "var": "txt_money", "height": 24, "fontSize": 24, "color": "#FFF95B", "align": "center"}, "nodeParent": 1, "label": "Label(txt_money)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 19, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}