{"x": 0, "type": "View", "selectedBox": 15, "selecteID": 42, "referenceLines": null, "props": {"width": 750, "top": 0, "sceneColor": "#ffffff", "right": 0, "mouseThrough": true, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "CheckBox", "props": {"width": 117, "var": "liveChat", "stateNum": 2, "skin": "game/checkbox_liveChat.png", "right": 14, "height": 80, "bottom": 16}, "nodeParent": 1, "label": "CheckBox(liveChat)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 15, "type": "Image", "props": {"skin": "game/chat_input_watch.png", "right": 279, "left": 20, "height": 50, "bottom": 33}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 15, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 2, "var": "showE<PERSON>ji", "stateNum": 1, "skin": "game/gy_smile.png", "right": 1}, "nodeParent": 15, "label": "<PERSON><PERSON>(showEmoji)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": []}, {"x": 30, "type": "Label", "props": {"y": 1, "x": 6, "wordWrap": false, "width": 396, "var": "keyboard_btn", "valign": "middle", "text": "Tap here to chat", "overflow": "hidden", "height": 47, "fontSize": 26, "color": "#c8c7c7"}, "nodeParent": 15, "label": "Label(keyboard_btn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 42, "child": []}]}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"var": "chat_btn", "stateNum": 2, "skin": "game/btn_chat.png", "right": 142, "bottom": 18}, "nodeParent": 1, "label": "Button(chat_btn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 25, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": -4, "x": 86, "visible": false, "var": "unreadTx", "stateNum": 1, "skin": "game/icon_mailpoint.png", "labelSize": 16, "labelPadding": 0, "labelFont": "<PERSON><PERSON>", "labelColors": "#ffffff", "labelBold": true, "labelAlign": "center", "label": "99+"}, "nodeParent": 25, "label": "<PERSON><PERSON>(unreadTx)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 43, "child": []}]}, {"x": 15, "type": "Box", "props": {"width": 750, "visible": false, "var": "chat", "top": 0, "right": 0, "mouseThrough": false, "left": 0, "height": 1066, "bottom": 115}, "nodeParent": 1, "label": "Box(chat)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 34, "child": [{"x": 30, "type": "Image", "props": {"var": "bg_right", "top": 100, "skin": "game/live_chat_bg.png", "right": 0, "left": 0, "bottom": 0}, "nodeParent": 34, "label": "Image(bg_right)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 33, "child": [{"x": 45, "type": "Image", "props": {"skin": "game/live_chat_arrow.png", "right": 180, "bottom": -28}, "nodeParent": 33, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 38, "child": []}, {"x": 45, "type": "Image", "props": {"width": 770, "var": "banner", "skin": "game/BG_basechat.png", "sizeGrid": "0,10,0,10", "right": -10, "left": -10, "height": 61}, "nodeParent": 33, "label": "Image(banner)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 45, "child": []}, {"x": 45, "type": "Label", "props": {"wordWrap": true, "width": 750, "valign": "middle", "text": "Please chat decently", "right": 0, "left": 0, "height": 56, "fontSize": 27, "color": "#ffffff", "align": "center"}, "nodeParent": 33, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 44, "child": []}, {"x": 45, "type": "Panel", "props": {"y": 60, "x": 0, "width": 750, "var": "chatPanel", "vScrollBarSkin": "game/scroll.png", "top": 60, "right": 0, "left": 0, "height": 1134, "bottom": 10}, "nodeParent": 33, "label": "Panel(chatPanel)", "isOpen": null, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 35, "child": [{"type": "VBox", "props": {"var": "chatList", "space": 5, "right": 0, "left": 0}, "nodeParent": 35, "label": "VBox(chatList)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 36, "child": []}]}], "$HIDDEN": false}], "$HIDDEN": false}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"width": 260, "visible": false, "var": "msgHintBtn", "stateNum": 1, "skin": "game/newmessage.png", "sizeGrid": "0,14,0,57", "right": 0, "labelSize": 26, "labelPadding": "0,-26,2,0", "labelColors": "#45c9a6", "label": "NEW MESSAGE", "height": 66, "bottom": 123}, "nodeParent": 1, "label": "Button(msgHintBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 40, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}