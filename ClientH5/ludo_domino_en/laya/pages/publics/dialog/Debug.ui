{"x": 0, "type": "Dialog", "selectedBox": 24, "selecteID": 26, "props": {"y": 0, "x": 0, "width": 750, "sceneColor": "#000000", "height": 1334}, "nodeParent": -1, "label": "Dialog", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"width": 750, "top": 0, "skin": "public/maskBg.png", "left": 0, "height": 1334}, "nodeParent": 1, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 645, "width": 94, "var": "closeBtn", "stateNum": "1", "name": "close", "labelSize": 30, "labelColors": "#FF0000", "label": "close", "height": 60}, "nodeParent": 1, "label": "Button(closeBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 2, "x": 2, "width": 94, "var": "clear", "stateNum": "1", "labelStrokeColor": "#ffffff", "labelSize": 30, "labelColors": "#008800", "label": "clear", "height": 60}, "nodeParent": 1, "label": "<PERSON><PERSON>(clear)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 285, "width": 115, "var": "copyLog", "stateNum": "1", "name": "copyLog", "labelStrokeColor": "#ffffff", "labelSize": 30, "labelColors": "#008800", "label": "copyLog", "height": 60}, "nodeParent": 1, "label": "<PERSON><PERSON>(copyLog)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 23, "child": []}, {"x": 15, "type": "<PERSON><PERSON>", "props": {"y": 0, "x": 410, "width": 110, "var": "searchBtn", "stateNum": "1", "labelStrokeColor": "#ffffff", "labelSize": 30, "labelColors": "#008800", "label": "search", "height": 60}, "nodeParent": 1, "label": "Button(searchBtn)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}, {"x": 15, "type": "List", "props": {"y": 175, "x": 0, "width": 750, "var": "_list", "vScrollBarSkin": "game/scroll.png", "spaceY": 5, "repeatX": 1, "height": 1150}, "nodeParent": 1, "label": "List(_list)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 8, "child": [{"x": 30, "type": "Label", "props": {"y": 0, "x": 0, "wordWrap": true, "width": 750, "text": "label", "renderType": "render", "height": 29, "fontSize": 20, "color": "#ffffff"}, "nodeParent": 8, "label": "Label", "isDirectory": false, "isAniNode": false, "hasChild": false, "compId": 9, "child": []}]}, {"x": 15, "type": "Label", "props": {"y": 62, "wordWrap": true, "width": 750, "visible": false, "var": "<PERSON><PERSON><PERSON><PERSON>", "text": "label", "height": 60, "fontSize": 28, "color": "#008800", "centerX": 0, "bgColor": "#f3e9e6", "align": "left"}, "nodeParent": 1, "label": "Label(specialLabel)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 15, "type": "Box", "props": {"y": 62, "x": 0, "width": 750, "visible": false, "var": "searchBox", "height": 60}, "nodeParent": 1, "label": "Box(searchBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 15, "child": [{"x": 30, "type": "Image", "props": {"y": 0, "x": 0, "width": 750, "var": "searchBg", "height": 60}, "nodeParent": 15, "label": "Image(searchBg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": []}, {"x": 30, "type": "TextInput", "props": {"y": 4, "x": 4, "wordWrap": true, "width": 420, "var": "searchInput", "text": "search...", "sizeGrid": "search...", "height": 50, "fontSize": 28, "color": "#000000", "borderColor": "#6b6666", "bgColor": "#eef4eb"}, "nodeParent": 15, "label": "TextInput(searchInput)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 8, "x": 429, "width": 50, "var": "preBtn", "stateNum": "1", "labelStrokeColor": "#ffffff", "labelSize": 30, "labelColors": "#008800", "label": "<<", "height": 40}, "nodeParent": 15, "label": "Button(preBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 8, "x": 486, "width": 50, "var": "nextBtn", "stateNum": "1", "labelStrokeColor": "#ffffff", "labelSize": 30, "labelColors": "#008800", "label": ">>", "height": 40}, "nodeParent": 15, "label": "Button(nextBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 8, "x": 548, "width": 66, "var": "toStart", "stateNum": "1", "labelStrokeColor": "#ffffff", "labelSize": 20, "labelColors": "#000000", "label": "toTop", "height": 40}, "nodeParent": 15, "label": "Button(toStart)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 19, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 8, "x": 621, "width": 66, "var": "toEnd", "stateNum": "1", "labelStrokeColor": "#ffffff", "labelSize": 20, "labelColors": "#000", "label": "toEnd", "height": 40}, "nodeParent": 15, "label": "Button(toEnd)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 8, "x": 691, "width": 50, "var": "closeSearch", "stateNum": "1", "labelStrokeColor": "#ffffff", "labelSize": 32, "labelColors": "#FF0000", "label": "x", "height": 40}, "nodeParent": 15, "label": "Button(closeSearch)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": []}]}, {"x": 15, "type": "Box", "props": {"y": 122, "x": -2, "width": 750, "height": 60}, "nodeParent": 1, "label": "Box", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 24, "child": [{"x": 30, "type": "Image", "props": {"y": 0, "x": 0, "width": 750, "height": 60}, "nodeParent": 24, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 25, "child": []}, {"x": 30, "type": "TextInput", "props": {"y": 4, "x": 4, "wordWrap": true, "width": 420, "var": "urlInput", "text": "ws://172.20.65.14:3000", "height": 50, "fontSize": 28, "color": "#000000", "borderColor": "#6b6666", "bgColor": "#eef4eb"}, "nodeParent": 24, "label": "TextInput(urlInput)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 8, "x": 429, "width": 115, "var": "connect_btn", "stateNum": "1", "labelStrokeColor": "#ffffff", "labelSize": 30, "labelColors": "#008800", "label": "connect", "height": 40}, "nodeParent": 24, "label": "Button(connect_btn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}