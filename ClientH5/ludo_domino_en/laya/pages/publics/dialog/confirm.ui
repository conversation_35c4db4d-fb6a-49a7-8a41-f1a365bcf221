{"x": 0, "type": "Dialog", "selectedBox": 2, "selecteID": 9, "props": {"width": 1050, "sceneWidth": 750, "sceneHeight": 1334, "sceneColor": "#000000", "height": 1750}, "nodeParent": -1, "label": "Dialog", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"width": 718, "skin": "public/tip_bg.png", "height": 395, "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"width": 270, "visible": false, "var": "_exit", "skin": "public/btn_G.png", "labelStrokeColor": "#61f2ca", "labelStroke": 2, "labelSize": 40, "labelColors": "#08846b", "label": "Exit", "height": 84, "centerX": -152, "bottom": 35}, "nodeParent": 2, "label": "Button(_exit)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"width": 270, "visible": false, "var": "_connect", "stateNum": "1", "skin": "public/btn_y.png", "labelStrokeColor": "#fff08b", "labelStroke": 2, "labelSize": 40, "labelColors": "#ab6e0f", "label": "Connect", "height": 84, "centerX": 152, "bottom": 35}, "nodeParent": 2, "label": "Button(_connect)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}, {"x": 30, "type": "Image", "props": {"width": 329, "top": 6, "skin": "public/tip_title.png", "sizeGrid": "0,87,0,87", "height": 81, "centerX": 0}, "nodeParent": 2, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 9, "child": [{"x": 45, "type": "Label", "props": {"y": 0, "x": 0, "width": 326, "valign": "middle", "text": "Tip", "strokeColor": "#34866e", "stroke": 3, "height": 78, "fontSize": 40, "color": "#ffffff", "bold": true, "align": "center"}, "nodeParent": 9, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 10, "child": []}]}]}, {"x": 15, "type": "Label", "props": {"y": 763, "x": 217, "wordWrap": true, "width": 616, "var": "confrim_tx", "valign": "middle", "text": "--", "height": 180, "fontSize": 30, "color": "#6c6a71", "centerX": 0, "align": "center"}, "nodeParent": 1, "label": "Label(confrim_tx)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}