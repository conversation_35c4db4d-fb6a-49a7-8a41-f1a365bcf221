{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 7, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "mouseThrough": false, "mouseEnabled": true, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"var": "bg", "top": 0, "skin": "public/maskBg.png", "right": 0, "mouseThrough": false, "mouseEnabled": false, "left": 0, "bottom": -300}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"width": 498, "visible": false, "var": "bg_txt", "skin": "public/bg_DL_yanshi.png", "height": 40, "centerY": 52, "centerX": 0}, "nodeParent": 1, "label": "Image(bg_txt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}, {"x": 15, "type": "Box", "props": {"width": 100, "var": "ani_box", "height": 100, "centerY": -76, "centerX": 0}, "nodeParent": 1, "label": "Box(ani_box)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": [], "$LOCKED": true}, {"x": 15, "type": "Label", "props": {"width": 528, "var": "load_tx", "text": "Reconnecting...", "height": 39, "fontSize": 26, "color": "#fff29f", "centerY": 54, "centerX": 0, "align": "center"}, "nodeParent": 1, "label": "Label(load_tx)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 4, "child": [], "$LOCKED": true}, {"x": 15, "type": "Label", "props": {"width": 233, "var": "count", "text": "(30)", "height": 30, "fontSize": 26, "color": "#fff29f", "centerY": 90, "centerX": 0, "align": "center"}, "nodeParent": 1, "label": "Label(count)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": [], "$LOCKED": true}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}