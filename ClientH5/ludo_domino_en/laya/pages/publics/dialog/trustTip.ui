{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 2, "props": {"width": 650, "visible": false, "sceneColor": "#000000", "height": 140, "centerX": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "width": 642, "skin": "public/trust-bg.png", "sizeGrid": "20,20,20,20", "height": 140, "centerX": 0}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Image", "props": {"y": 33, "var": "icon", "skin": "public/trust-mark.png", "left": 30}, "nodeParent": 2, "label": "Image(icon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 30, "type": "Image", "props": {"y": 13, "x": 585, "width": 46, "var": "closeBtn", "skin": "public/alphaBg.png", "height": 67}, "nodeParent": 2, "label": "Image(closeBtn)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"x": 45, "type": "Image", "props": {"y": 0, "x": 6, "skin": "public/X.png"}, "nodeParent": 6, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}]}, {"x": 30, "type": "Label", "props": {"x": 122, "wordWrap": true, "width": 470, "var": "contentTxt", "valign": "middle", "text": "AUTO is turned ON. You obtain no Exp while on AUTO.", "leading": 10, "height": 98, "fontSize": 20, "color": "#777777", "centerY": 5, "bold": true}, "nodeParent": 2, "label": "Label(contentTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}