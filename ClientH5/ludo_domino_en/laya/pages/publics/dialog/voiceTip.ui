{"x": 0, "type": "View", "selectedBox": 1, "selecteID": 17, "props": {"y": 0, "x": 0, "top": 0, "sceneWidth": 750, "sceneHeight": 1334, "right": 0, "mouseThrough": false, "left": 0, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Sprite", "props": {"visible": false, "var": "bg", "alpha": 0.5}, "nodeParent": 1, "label": "Sprite(bg)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 15, "type": "Box", "props": {"y": 348, "x": 141, "width": 75, "var": "tip", "pivotY": 70, "pivotX": 37, "height": 70}, "nodeParent": 1, "label": "Box(tip)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 11, "child": [{"x": 30, "type": "Image", "props": {"y": 28, "x": 36, "width": 59, "var": "tip_bg", "skin": "public/Description_0.png", "sizeGrid": "15,15,15,15", "pivotY": 28, "pivotX": 30, "height": 54}, "nodeParent": 11, "label": "Image(tip_bg)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 30, "type": "Image", "props": {"y": 53, "x": 21, "skin": "public/Description_1.png"}, "nodeParent": 11, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}