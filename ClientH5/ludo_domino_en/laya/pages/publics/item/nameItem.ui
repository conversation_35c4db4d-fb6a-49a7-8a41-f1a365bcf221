{"x": 0, "type": "View", "selectedBox": 6, "selecteID": 7, "props": {"width": 350, "sceneColor": "#000000", "height": 40}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Box", "props": {"y": 0, "x": 0, "var": "nameBox"}, "nodeParent": 1, "label": "Box(nameBox)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "Label", "props": {"y": 0, "x": 0, "var": "nameTxt", "valign": "bottom", "fontSize": 20, "color": "#FFE461", "align": "center"}, "nodeParent": 2, "label": "Label(nameTxt)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Label", "props": {"y": 0, "x": 0, "var": "nameTxt_Down", "valign": "bottom", "fontSize": 20, "color": "#ED7116", "align": "center"}, "nodeParent": 2, "label": "Label(nameTxt_Down)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 45, "type": "Image", "props": {"y": 38, "width": 350, "var": "nameDownMaskSp", "skin": "public/R4叠色@2x.png", "renderType": "mask", "left": 0, "height": 30, "anchorY": 1}, "nodeParent": 4, "label": "Image(nameDownMaskSp)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Label", "props": {"y": 0, "x": 0, "visible": false, "var": "nameTxt_Mask", "valign": "bottom", "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 2, "label": "Label(nameTxt_Mask)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 6, "child": [{"x": 45, "type": "Image", "props": {"y": -40, "x": -50, "width": 60, "var": "nameMaskSp", "skin": "public/R4扫光@2x.png", "renderType": "mask", "height": 100}, "nodeParent": 6, "label": "Image(nameMaskSp)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}], "$HIDDEN": false}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}