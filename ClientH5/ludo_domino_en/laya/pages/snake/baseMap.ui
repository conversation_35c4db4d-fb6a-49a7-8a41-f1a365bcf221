{"x": 0, "type": "View", "selectedBox": 4, "selecteID": 11, "props": {"width": 750, "top": 0, "sceneColor": "#000000", "height": 960, "centerX": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 457, "var": "bg", "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image(bg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 0, "var": "check", "skin": "snake/Checkerboard.png", "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image(check)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"type": "Image", "props": {"y": 890, "x": 0, "skin": "snake/Checkerboard_dec.png"}, "nodeParent": 2, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}], "$LOCKED": false, "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 886, "x": 4, "var": "beginGrid", "skin": "snake/beginGrid.png", "mouseThrough": true}, "nodeParent": 1, "label": "Image(beginGrid)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"width": 720, "var": "element_box", "height": 890, "centerY": -5, "centerX": 0}, "nodeParent": 1, "label": "Box(element_box)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": [], "$LOCKED": true, "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"width": 720, "var": "chess_box", "height": 890, "centerY": -5, "centerX": 0}, "nodeParent": 1, "label": "Box(chess_box)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"x": 30, "type": "Image", "props": {"y": 916, "x": 29, "visible": false, "skin": "snake/chess_11000_0.png", "scaleY": 0.72, "scaleX": 0.72, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 4, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": 916, "x": 72, "visible": false, "skin": "snake/chess_11000_3.png", "scaleY": 0.72, "scaleX": 0.72, "anchorY": 0.5, "anchorX": 0.5}, "nodeParent": 4, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": [], "$HIDDEN": false}], "$LOCKED": true, "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 897, "x": 10, "width": 82, "var": "initBox", "height": 79}, "nodeParent": 1, "label": "Box(initBox)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}, {"x": 15, "type": "Box", "props": {"width": 720, "var": "ani_box", "height": 890, "centerY": -5, "centerX": 0}, "nodeParent": 1, "label": "Box(ani_box)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": [], "$LOCKED": true, "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}