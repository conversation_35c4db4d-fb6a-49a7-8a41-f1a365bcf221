{"x": 0, "type": "View", "selectedBox": 136, "selecteID": 137, "props": {"y": 0, "width": 750, "top": 0, "sceneColor": "#000000", "right": 0, "left": 0, "height": 1334, "bottom": 0}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "var": "bg", "top": 0, "skin": "snake/BG_30000.jpg", "right": 0, "left": 0, "bottom": 0}, "nodeParent": 1, "label": "Image(bg)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 132, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Image", "props": {"y": 667, "x": 375, "var": "furniture", "skin": "snake/furniture.png", "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image(furniture)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 133, "child": []}, {"x": 15, "type": "Image", "props": {"y": 0, "x": 0, "var": "topImg", "top": 0, "skin": "snake/image_ludo_bg0.png", "sizeGrid": "0,0,20,0", "right": 0, "left": 0, "height": 105}, "nodeParent": 1, "label": "Image(topImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 134, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"x": 0, "width": 750, "var": "bodyMc", "top": 110, "right": 0, "mouseThrough": true, "left": 0, "height": 1229, "bottom": 0}, "nodeParent": 1, "label": "Box(bodyMc)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 78, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"y": 790, "x": 0, "width": 750, "var": "players_layer", "right": 0, "mouseThrough": true, "mouseEnabled": true, "left": 0, "height": 414, "bottom": 130}, "nodeParent": 1, "label": "Box(players_layer)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 163, "child": [], "$HIDDEN": false}, {"x": 15, "type": "Box", "props": {"var": "myHander", "mouseThrough": true, "left": 47, "bottom": 130}, "nodeParent": 1, "label": "Box(myHander)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 136, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 143, "x": 266, "var": "reset", "stateNum": "1", "skin": "snake/btn_refresh_on.png"}, "nodeParent": 136, "label": "<PERSON><PERSON>(reset)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 137, "child": [{"x": 45, "type": "Image", "props": {"y": 24, "x": 38, "width": 16, "var": "reset<PERSON>oin", "skin": "snake/Diamonds.png", "height": 19}, "nodeParent": 137, "label": "Image(resetCoin)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 138, "child": []}, {"x": 45, "type": "Label", "props": {"y": 17, "x": 5, "width": 36, "var": "useCoin", "valign": "middle", "text": "-", "height": 28, "fontSize": 20, "color": "#ffffff", "align": "center"}, "nodeParent": 137, "label": "Label(useCoin)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 139, "child": []}]}, {"x": 30, "type": "Image", "props": {"y": 73, "x": -3, "visible": false, "var": "trusteeship", "skin": "snake/auto.png"}, "nodeParent": 136, "label": "Image(trusteeship)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 140, "child": [{"x": 45, "type": "Label", "props": {"y": 7, "x": 8, "width": 68, "text": "AUTO", "strokeColor": "#000000", "stroke": 2, "height": 24, "fontSize": 22, "color": "#ffffff", "bold": true, "align": "center"}, "nodeParent": 140, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 141, "child": []}, {"x": 45, "type": "CheckBox", "props": {"y": 3, "x": 81, "width": 50, "var": "trust", "stateNum": "2", "skin": "snake/check_auto.png", "height": 33}, "nodeParent": 140, "label": "CheckBox(trust)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 142, "child": []}, {"x": 45, "type": "Animation", "props": {"y": 0, "x": 67, "var": "trust_jump", "source": "publics/ani/jump.ani"}, "nodeParent": 140, "label": "Animation(trust_jump)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 143, "child": []}, {"x": 45, "type": "Animation", "props": {"y": 18, "x": 96, "visible": false, "var": "trust_wait", "source": "publics/ani/relogin.ani", "scaleY": 0.3, "scaleX": 0.3}, "nodeParent": 140, "label": "Animation(trust_wait)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 144, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": -213, "x": 183, "width": 490, "visible": false, "var": "reset_info", "skin": "snake/BG_Expression.png", "height": 312}, "nodeParent": 136, "label": "Image(reset_info)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 145, "child": [{"x": 45, "type": "Image", "props": {"y": 58, "x": 22, "width": 448, "skin": "snake/table_words.png", "height": 100}, "nodeParent": 145, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 146, "child": []}, {"x": 45, "type": "Image", "props": {"y": 309, "x": 98, "skin": "snake/BG_Expression_V.png"}, "nodeParent": 145, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 147, "child": []}, {"x": 45, "type": "Image", "props": {"y": 270, "x": 22, "skin": "snake/spot.png"}, "nodeParent": 145, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 158, "child": []}, {"x": 45, "type": "Image", "props": {"y": 224, "x": 22, "skin": "snake/spot.png"}, "nodeParent": 145, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 157, "child": []}, {"x": 45, "type": "Image", "props": {"y": 178, "x": 22, "skin": "snake/spot.png"}, "nodeParent": 145, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 156, "child": []}, {"x": 45, "type": "CheckBox", "props": {"y": 252, "x": 420, "width": 100, "var": "isreset", "stateNum": "2", "skin": "snake/undoon.png", "height": 90}, "nodeParent": 145, "label": "CheckBox(isreset)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 155, "child": []}, {"x": 45, "type": "Label", "props": {"y": 3, "x": 33, "width": 428, "var": "reset_title", "valign": "middle", "text": "Re-roll Dice", "strokeColor": "#575690", "stroke": 2, "height": 52, "fontSize": 35, "color": "#ffffff", "align": "center"}, "nodeParent": 145, "label": "Label(reset_title)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 148, "child": []}, {"x": 45, "type": "Label", "props": {"y": 57, "x": 31, "wordWrap": true, "width": 433, "var": "reset_body", "valign": "middle", "text": "You can re-roll dice within 2 secs.", "height": 101, "fontSize": 25, "color": "#f3f3f3", "align": "center"}, "nodeParent": 145, "label": "Label(reset_body)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 149, "child": []}, {"x": 45, "type": "Label", "props": {"y": 217, "x": 39, "text": "Re-roll Remaining", "height": 32, "fontSize": 23, "color": "#5b7f44 ", "align": "right"}, "nodeParent": 145, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 150, "child": []}, {"x": 45, "type": "Label", "props": {"y": 170, "x": 39, "text": "Re-roll Left for this round", "height": 32, "fontSize": 23, "color": "#5b7f44 ", "align": "right"}, "nodeParent": 145, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 151, "child": []}, {"x": 45, "type": "Label", "props": {"y": 164, "x": 423, "width": 44, "var": "round_count_text", "valign": "middle", "text": "0", "strokeColor": "#575690", "stroke": 3, "height": 44, "fontSize": 39, "color": "#ffffff", "align": "center"}, "nodeParent": 145, "label": "Label(round_count_text)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 152, "child": []}, {"x": 45, "type": "Label", "props": {"y": 211, "x": 423, "width": 44, "var": "count_text", "valign": "middle", "text": "0", "strokeColor": "#575690", "stroke": 3, "height": 44, "fontSize": 39, "color": "#ffffff", "align": "center"}, "nodeParent": 145, "label": "Label(count_text)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 153, "child": []}, {"x": 45, "type": "Label", "props": {"y": 265, "x": 39, "width": 172, "var": "isreset_tx", "text": "Re-roll ON", "height": 32, "fontSize": 23, "color": "#5b7f44 ", "align": "left"}, "nodeParent": 145, "label": "Label(isreset_tx)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 154, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"y": -80, "x": 239, "visible": false, "var": "quick_buy_btn", "skin": "snake/Recharge_label.png"}, "nodeParent": 136, "label": "Image(quick_buy_btn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 159, "child": [], "$HIDDEN": false}, {"x": 30, "type": "Animation", "props": {"y": 82, "x": 190, "visible": false, "var": "throw_jump", "source": "publics/ani/jump.ani", "scaleY": 1.6, "scaleX": 1.6}, "nodeParent": 136, "label": "Animation(throw_jump)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 160, "child": [], "$HIDDEN": false}]}, {"x": 15, "type": "Box", "props": {"y": 13, "var": "topMc", "top": 13, "right": 13, "left": 13}, "nodeParent": 1, "label": "Box(topMc)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 103, "child": []}, {"x": 15, "type": "Box", "props": {"y": 800, "x": 10, "width": 750, "var": "giftAniBox", "right": 0, "mouseThrough": true, "mouseEnabled": true, "left": 0, "height": 414, "bottom": 130}, "nodeParent": 1, "label": "Box(giftAniBox)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 164, "child": [], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}