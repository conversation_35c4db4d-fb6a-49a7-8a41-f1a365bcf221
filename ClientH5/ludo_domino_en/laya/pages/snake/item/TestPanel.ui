{"x": 0, "type": "Dialog", "selectedBox": 10, "selecteID": 19, "props": {"width": 750, "sceneColor": "#000000", "height": 1334}, "nodeParent": -1, "label": "Dialog", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "Image", "props": {"width": 707, "skin": "public/bg_content26.png", "height": 715, "centerY": 0, "centerX": 0}, "nodeParent": 1, "label": "Image", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 10, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 606, "x": 246, "width": 216, "visible": false, "var": "initBtn", "skin": "public/btn_y.png", "labelSize": 24, "label": "棋子重置起点", "height": 117}, "nodeParent": 10, "label": "Button(initBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 2, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 485, "width": 216, "var": "saveBtn", "skin": "public/btn_G.png", "labelSize": 30, "label": "保存", "height": 117, "centerX": 0}, "nodeParent": 10, "label": "Button(saveBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 7, "child": []}, {"x": 30, "type": "TextInput", "props": {"y": 96, "x": 375, "width": 260, "var": "moveInitTimeTxt", "type": "text", "text": "150", "promptColor": "#e5a9a8", "prompt": "走格子速度", "height": 40, "fontSize": 24, "font": "<PERSON><PERSON>", "color": "#282222"}, "nodeParent": 10, "label": "TextInput(moveInitTimeTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 6, "child": []}, {"x": 30, "type": "TextInput", "props": {"y": 132, "x": 375, "width": 260, "var": "moveChangeTimeTxt", "type": "text", "text": "100", "promptColor": "#e5a9a8", "prompt": "走格子速度", "height": 46, "fontSize": 24, "font": "<PERSON><PERSON>", "color": "#282222"}, "nodeParent": 10, "label": "TextInput(moveChangeTimeTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"x": 30, "type": "TextInput", "props": {"y": 213, "x": 375, "width": 260, "var": "kissTimeTxt", "type": "text", "text": "200", "promptColor": "#e5a9a8", "prompt": "蛇身移动单位时间", "height": 48, "fontSize": 24, "font": "<PERSON><PERSON>", "color": "#282222"}, "nodeParent": 10, "label": "TextInput(kissTimeTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 8, "child": []}, {"x": 30, "type": "TextInput", "props": {"y": 248, "x": 374, "width": 260, "var": "kissTimeVTxt", "type": "text", "text": "2", "promptColor": "#e5a9a8", "prompt": "蛇身移动单位时间", "height": 54, "fontSize": 24, "font": "<PERSON><PERSON>", "color": "#282222"}, "nodeParent": 10, "label": "TextInput(kissTimeVTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}, {"x": 30, "type": "TextInput", "props": {"y": 324, "x": 375, "width": 260, "var": "ladderStepTxt", "type": "text", "text": "60", "promptColor": "#e5a9a8", "prompt": "爬梯步长", "height": 54, "fontSize": 24, "font": "<PERSON><PERSON>", "color": "#282222"}, "nodeParent": 10, "label": "TextInput(ladderStepTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 9, "child": []}, {"x": 30, "type": "TextInput", "props": {"y": 361, "x": 374, "width": 260, "var": "ladderStepTimeTxt", "type": "text", "text": "180", "promptColor": "#e5a9a8", "prompt": "爬梯单步时长", "height": 54, "fontSize": 24, "font": "<PERSON><PERSON>", "color": "#282222"}, "nodeParent": 10, "label": "TextInput(ladderStepTimeTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}, {"x": 30, "type": "Text", "props": {"y": 103, "x": 19, "width": 225, "text": "单格移动基础时间（默认150）", "height": 38, "fontSize": 26, "color": "#0f0c0c", "align": "left"}, "nodeParent": 10, "label": "Text", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 11, "child": []}, {"x": 30, "type": "Text", "props": {"y": 376, "x": 19, "width": 286, "text": "爬梯单步时长（默认 180）", "height": 38, "fontSize": 26, "color": "#0f0c0c", "align": "left"}, "nodeParent": 10, "label": "Text", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": []}, {"x": 30, "type": "Text", "props": {"y": 141, "x": 21, "width": 225, "text": "单格移动浮动时间（默认100）", "height": 38, "fontSize": 26, "color": "#0f0c0c", "align": "left"}, "nodeParent": 10, "label": "Text", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 19, "child": []}, {"x": 30, "type": "Text", "props": {"y": 223, "x": 19, "width": 286, "text": "蛇身移动时间（默认 200ms）", "height": 38, "fontSize": 26, "color": "#0f0c0c", "align": "left"}, "nodeParent": 10, "label": "Text", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 12, "child": []}, {"x": 30, "type": "Text", "props": {"y": 339, "x": 19, "width": 286, "text": "爬梯步长（默认 60）", "height": 38, "fontSize": 26, "color": "#0f0c0c", "align": "left"}, "nodeParent": 10, "label": "Text", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": []}, {"x": 30, "type": "Text", "props": {"y": 260, "x": 19, "width": 286, "text": "蛇身移动时间系数（默认 2）", "height": 38, "fontSize": 26, "color": "#0f0c0c", "align": "left"}, "nodeParent": 10, "label": "Text", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 16, "x": 622, "width": 48, "var": "closeBtn", "stateNum": "1", "skin": "public/btn_close.png", "height": 52}, "nodeParent": 10, "label": "Button(closeBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}]}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}