{"x": 0, "type": "View", "selectedBox": 2, "selecteID": 40, "props": {"width": 750, "sceneColor": "#000000", "right": 0, "mouseThrough": true, "left": 0, "height": 200}, "nodeParent": -1, "label": "View", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 1, "child": [{"x": 15, "type": "HBox", "props": {"y": 0, "x": 0, "var": "topMc", "top": 0, "right": 0, "left": 0, "height": 93}, "nodeParent": 1, "label": "HBox(topMc)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 2, "child": [{"x": 30, "type": "<PERSON><PERSON>", "props": {"var": "winBtn", "top": 4, "stateNum": "1", "skin": "snake/btn_reward.png", "left": 110}, "nodeParent": 2, "label": "Button(winBtn)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 3, "child": []}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 18, "width": 156, "visible": false, "var": "store_btn", "stateNum": "1", "skin": "snake/image_title_bg.png", "right": 28, "labelSize": 24, "labelColors": "#ffffff", "label": "------", "height": 46}, "nodeParent": 2, "label": "Button(store_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 40, "child": [{"type": "Image", "props": {"y": -10, "x": -28, "skin": "snake/Diamonds.png"}, "nodeParent": 40, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 42, "child": []}], "$HIDDEN": false}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 19, "width": 290, "visible": false, "var": "audience_btn", "stateNum": "1", "skin": "snake/image_title_bg.png", "height": 46, "centerX": -7}, "nodeParent": 2, "label": "Button(audience_btn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 41, "child": [{"type": "Image", "props": {"y": 15, "x": 52, "skin": "snake/Audience.png"}, "nodeParent": 41, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 43, "child": []}, {"type": "Label", "props": {"y": 15, "x": 91, "width": 90, "valign": "middle", "text": "Spectator", "height": 20, "fontSize": 18, "color": "#ffffff", "align": "center"}, "nodeParent": 41, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 44, "child": []}, {"type": "Label", "props": {"y": 9, "x": 185, "width": 52, "var": "audience_num", "valign": "middle", "text": "0", "height": 31, "fontSize": 24, "color": "#ffffff", "align": "center"}, "nodeParent": 41, "label": "Label(audience_num)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 45, "child": []}]}, {"x": 30, "type": "Image", "props": {"width": 80, "top": 6, "skin": "snake/alphaBg.png", "left": 14, "height": 80}, "nodeParent": 2, "label": "Image", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 4, "child": [{"type": "<PERSON><PERSON>", "props": {"y": 0, "x": 0, "var": "settingBtn", "stateNum": "1", "skin": "snake/system.png"}, "nodeParent": 4, "label": "Button(settingBtn)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 5, "child": []}]}, {"x": 30, "type": "Image", "props": {"width": 472, "visible": false, "var": "rankMc", "top": 90, "skin": "snake/BG_Expression.png", "mouseEnabled": false, "left": 86, "height": 68}, "nodeParent": 2, "label": "Image(rankMc)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 12, "child": [{"type": "Image", "props": {"y": 3, "x": 46, "width": 28, "skin": "snake/BG_Expression_V.png", "scaleY": -1, "height": 15}, "nodeParent": 12, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 13, "child": []}, {"type": "Image", "props": {"y": 18, "x": 157, "width": 306, "skin": "snake/table_words.png", "height": 36}, "nodeParent": 12, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 14, "child": []}, {"type": "Image", "props": {"y": 18, "x": 10, "width": 144, "skin": "snake/table_words.png", "height": 36}, "nodeParent": 12, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 17, "child": []}, {"type": "Image", "props": {"y": 73, "width": 436, "visible": false, "var": "rank1", "skin": "snake/table_words.png", "height": 53, "centerX": 2}, "nodeParent": 12, "label": "Image(rank1)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 19, "child": [{"type": "Image", "props": {"y": -11, "x": 15, "var": "rankImg1", "skin": "snake/RANK1.png"}, "nodeParent": 19, "label": "Image(rankImg1)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 20, "child": []}, {"type": "Label", "props": {"y": 7, "x": 232, "width": 149, "var": "rank_1", "height": 38, "fontSize": 36, "color": "#ffffff", "align": "center"}, "nodeParent": 19, "label": "Label(rank_1)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 21, "child": [], "$LOCKED": false}, {"type": "Label", "props": {"y": 9, "x": 94, "width": 129, "var": "rankImgTxt1", "text": "RANK 1", "height": 38, "fontSize": 31, "color": "#fff690", "bold": true}, "nodeParent": 19, "label": "Label(rankImgTxt1)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 22, "child": [], "$LOCKED": false}], "$HIDDEN": false}, {"type": "Label", "props": {"y": 28, "x": 173, "width": 100, "var": "roomIDImg", "text": "ROOM ID:", "height": 21, "fontSize": 18, "color": "#fff692", "bold": true}, "nodeParent": 12, "label": "Label(roomIDImg)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 16, "child": []}, {"type": "Label", "props": {"y": 27, "x": 273, "width": 180, "var": "rank_roomId", "height": 22, "fontSize": 19, "color": "#f3f3f3", "align": "center"}, "nodeParent": 12, "label": "Label(rank_roomId)", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 15, "child": []}, {"type": "Label", "props": {"y": 25, "x": 12, "width": 138, "var": "typeTxt", "text": "CLASSIC", "height": 29, "fontSize": 20, "color": "#f3f3f3", "align": "center"}, "nodeParent": 12, "label": "Label(typeTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 18, "child": []}], "$LOCKED": false, "$HIDDEN": false}, {"x": 30, "type": "Image", "props": {"width": 599, "visible": false, "var": "set", "top": 93, "skin": "snake/BG_Expression.png", "sizeGrid": "15,15,15,15", "left": 17, "height": 125}, "nodeParent": 2, "label": "Image(set)", "isOpen": true, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 23, "child": [{"x": 45, "type": "Image", "props": {"y": 4, "x": 17, "skin": "snake/BG_Expression_V.png", "scaleY": -1}, "nodeParent": 23, "label": "Image", "isOpen": false, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 24, "child": []}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 15, "x": 20, "width": 132, "var": "soundBtn", "stateNum": "1", "skin": "snake/table_words.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "Button(soundBtn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 31, "child": [{"type": "Image", "props": {"y": 12, "var": "soundIcon", "skin": "snake/voice_on.png", "centerX": 0}, "nodeParent": 31, "label": "Image(soundIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 32, "child": []}, {"type": "Label", "props": {"y": 66, "width": 120, "var": "soundTxt", "text": "ON", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 0, "align": "center"}, "nodeParent": 31, "label": "Label(soundTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 33, "child": []}]}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 15, "x": 163, "width": 132, "var": "ruleBtn", "stateNum": "1", "skin": "snake/table_words.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "<PERSON><PERSON>(ruleBtn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 25, "child": [{"type": "Image", "props": {"y": 12, "skin": "snake/rule.png", "centerX": 0}, "nodeParent": 25, "label": "Image", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 26, "child": []}, {"type": "Label", "props": {"y": 66, "width": 120, "text": "Rules", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 0, "align": "center"}, "nodeParent": 25, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 27, "child": []}]}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 15, "x": 306, "width": 132, "var": "chatSwitchBtn", "stateNum": "1", "skin": "snake/table_words.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "Button(chatSwitchBtn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 34, "child": [{"type": "Image", "props": {"y": 12, "var": "switchIcon", "skin": "snake/ChatON.png", "centerX": 0}, "nodeParent": 34, "label": "Image(switchIcon)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 35, "child": []}, {"type": "Label", "props": {"y": 66, "width": 120, "var": "switchTxt", "text": "Chat ON", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 0, "align": "center"}, "nodeParent": 34, "label": "Label(switchTxt)", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 36, "child": []}]}, {"x": 45, "type": "<PERSON><PERSON>", "props": {"y": 15, "x": 448, "width": 132, "var": "exitBtn", "stateNum": "1", "skin": "snake/table_words.png", "sizeGrid": "12,12,12,12", "height": 98}, "nodeParent": 23, "label": "Button(exitBtn)", "isOpen": false, "isDirectory": true, "isAniNode": true, "hasChild": true, "compId": 28, "child": [{"type": "Image", "props": {"y": 12, "skin": "snake/exit.png", "centerX": 0}, "nodeParent": 28, "label": "Image", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 29, "child": []}, {"type": "Label", "props": {"y": 66, "width": 120, "text": "Exit", "strokeColor": "#105740", "height": 24, "fontSize": 20, "color": "#f8f8f8", "centerX": 0, "align": "center"}, "nodeParent": 28, "label": "Label", "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 30, "child": []}]}], "$LOCKED": false, "$HIDDEN": true}, {"x": 30, "type": "<PERSON><PERSON>", "props": {"y": 3, "visible": false, "var": "btn_shop", "stateNum": "1", "skin": "game/btn_store.png", "right": 12, "labelSize": 24, "labelColors": "#ffffff"}, "nodeParent": 2, "label": "Button(btn_shop)", "isOpen": true, "isDirectory": false, "isAniNode": true, "hasChild": false, "compId": 46, "child": [], "$HIDDEN": false}], "$HIDDEN": false}], "animations": [{"nodes": [], "name": "ani1", "id": 1, "frameRate": 24, "action": 0}]}