rm -rf /Users/<USER>/ludo_domino/ClientH5/release/web/newVer/libs
rm -rf /Users/<USER>/ludo_domino/ClientH5/release/web/newVer/js/*
currentTimeStamp=`date +"%Y%m%d%H%M%S"`
# mv /Users/<USER>/work/ludo_domino/ClientH5/release/web/code.js /Users/<USER>/work/ludo_domino/ClientH5/release/web/js/code$currentTimeStamp.js
# sed -i "" '/script/d' /Users/<USER>/work/ludo_domino/ClientH5/release/web/index.html
# sed -i "" '/!--/d' /Users/<USER>/work/ludo_domino/ClientH5/release/web/index.html
# sed -i "" 's/<body>/<body><script src="js\/code'$currentTimeStamp'.js"><\/script>/g' /Users/<USER>/work/ludo_domino/ClientH5/release/web/index.html
# rm -rf /Users/<USER>/work/ludo_domino/ClientH5/release/web/res/proto/ChessGame.proto
# rm -rf /Users/<USER>/work/ludo_domino/ClientH5/release/web/res/proto/VsGameProtocol.proto
# cp /Users/<USER>/work/ludo_domino/ClientH5/ludo_domino_en/bin/res/proto/ChessGame.proto /Users/<USER>/work/ludo_domino/ClientH5/release/web/res/proto/ChessGame.proto
# cp /Users/<USER>/work/ludo_domino/ClientH5/ludo_domino_en/bin/res/proto/VsGameProtocol.proto ./Users/<USER>/work/ludo_domino/ClientH5/release/web/res/proto/VsGameProtocol.proto


mv /Users/<USER>/ludo_domino/ClientH5/release/web/newVer/code.js /Users/<USER>/ludo_domino/ClientH5/release/web/newVer/js/code$currentTimeStamp.js
sed -i "" '/script/d' /Users/<USER>/ludo_domino/ClientH5/release/web/newVer/index.html
sed -i "" '/!--/d' /Users/<USER>/ludo_domino/ClientH5/release/web/newVer/index.html
sed -i "" 's/<body>/<body><script src="js\/code'$currentTimeStamp'.js"><\/script>/g' /Users/<USER>/ludo_domino/ClientH5/release/web/newVer/index.html
# rm -rf /Users/<USER>/ludo_domino/ClientH5/release/web/newVer/res/proto/ChessGame.proto
# rm -rf /Users/<USER>/ludo_domino/ClientH5/release/web/newVer/res/proto/VsGameProtocol.proto
# cp /Users/<USER>/ludo_domino/ClientH5/ludo_domino_en/bin/res/proto/ChessGame.proto /Users/<USER>/ludo_domino/ClientH5/release/web/newVer/res/proto/ChessGame.proto
# cp /Users/<USER>/ludo_domino/ClientH5/ludo_domino_en/bin/res/proto/VsGameProtocol.proto /Users/<USER>/ludo_domino/ClientH5/release/web/newVer/res/proto/VsGameProtocol.proto
# cp /Users/<USER>/ludo_domino/ClientH5/ludo_domino_en/bin/res/proto/SnakeLadder.proto /Users/<USER>/ludo_domino/ClientH5/release/web/newVer/res/proto/SnakeLadder.proto
# cp /Users/<USER>/ludo_domino/ClientH5/ludo_domino_en/bin/res/proto/DominoWatch.proto /Users/<USER>/ludo_domino/ClientH5/release/web/newVer/res/proto/DominoWatch.proto
