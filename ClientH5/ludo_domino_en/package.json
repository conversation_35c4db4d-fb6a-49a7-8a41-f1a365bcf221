{"name": "dominos", "version": "1.0.0", "description": "--", "repository": "", "main": "Main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "yl", "license": "ISC", "dependencies": {"async": "^2.6.1", "child_process": "^1.0.2"}, "devDependencies": {"cheerio": "^1.0.0-rc.2", "gulp": "^3.9.1", "gulp-concat": "^2.6.1", "gulp-livereload": "^3.8.1", "gulp-merge": "^0.1.1", "gulp-sourcemaps": "^2.6.0", "gulp-tslint": "^8.1.1", "gulp-typedoc": "^2.0.2", "gulp-typescript": "^3.2.4", "gulp-uglify": "^3.0.1", "minimist": "^1.2.0", "strip-json-comments": "^2.0.1", "tslint": "^5.4.3", "typedoc": "^0.7.1", "typescript": "^2.9.2"}}