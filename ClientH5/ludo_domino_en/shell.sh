work_path=$(dirname $(readlink -f $0))
cd $work_path
ls
if [ -f ".././release/web/code.js" ];then
    # echo '当前目录'$work_path
    rm -rf .././release/web/libs
    # echo '删除libs目录'
    rm -rf .././release/web/js/*
    # echo '删除js中文件'
    currentTimeStamp=`date "+%Y%m%d%H%M%S"`
#    timeStamp=`date -d "$current" +%s`
#    currentTimeStamp=$((timeStamp*1000+`date "+%N"`/1000000))
    mv .././release/web/code.js .././release/web/js/code$currentTimeStamp.js
    # echo '移动js并重命名'$currentTimeStamp
    sed -i -e '/script/d' -e '/!--/d' .././release/web/index.html
    # echo '删除index中的js引入'
    sed -i '/<body>/a\<script src="js/code'$currentTimeStamp'.js" type="text/javascript"></script>' .././release/web/index.html
    # echo '重新引入js文件'

    rm -rf .././release/web/res/proto/ChessGame.proto
    rm -rf .././release/web/res/proto/VsGameProtocol.proto
    cp ./bin/res/proto/ChessGame.proto .././release/web/res/proto/ChessGame.proto
    cp ./bin/res/proto/VsGameProtocol.proto .././release/web/res/proto/VsGameProtocol.proto
    # 更新release下的proto文件

    cd ../../
    start .
    echo '--------------成功! 5秒后关闭窗口--------------'
    sleep 5
    # cmd /k
else
    echo 'web目录下没有合并的code.js'
    echo '勾选 是否合并index.html中所有js文件'
    echo '勾选 是否只复制index.html中引用的js文件'
    echo '或者 检查文件路径 尤其注意release是哪里的release'
    cmd /k
fi


