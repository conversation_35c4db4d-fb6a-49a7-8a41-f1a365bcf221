import WebGL = Laya.WebGL;
// 程序入口    https://halldev.yalla.games/ludo_domino/index.html
class GameMain {
    private _changeBtn: Laya.Button;
    private _gameMain: BaseMain;
    private _activityMain: BaseMain;//锦标赛会跟游戏同存
    private _msg: any;
    private _prepare: yalla.prepare.Prepare;
    private _loginTime: number = 0;//上次登录的时间

    constructor() {
        Laya.init(750, 1334, WebGL);
        yalla.Screen.onResize();
        this.adapter();
        this.initEvent();
        this._prepare = new yalla.prepare.Prepare();
        this._prepare.start();
        var lb: Laya.HTMLDivElement = new Laya.HTMLDivElement();
        lb.style.fontSize = 22;
        lb.style.valign = 'center';
        lb.style.wordWrap = true;
        lb.style.width = 540;
        lb.innerHTML = '你好';
        lb.innerHTML = 'فتح الدردشة';


        //𖤍᭄تيــــــــاڪو  

        //♚ ﮼دبلوماسيهᴷᴳ
        // lb.innerHTML = "asdfdfsdfsdfsdds أولا :حساء من [] + - الد جاجة  【fjdlfjald】. ثانيا :أرز وفطيرة ? .ثالثا :أيس كريم .";
        // if (yalla.util.IsWinConch()) yalla.Skin.instance.init();

        // Laya.DebugPanel.init();
        // Laya.Stat.show(0,200);

        // var arr = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]; 
        // var index = 1;    // var str = 'public/default_head.png';
        // var eStr = laya.utils.Browser.window.Base64.encode(str);
        // var dStr = laya.utils.Browser.window.Base64.decode('5byg5LiJ');
        // var eStr = laya.utils.Browser.window.btoa(str);
        // var dStr = laya.utils.Browser.window.atob(eStr);
        // console.log(arr.slice(index - 10, index), arr.slice(index, index + 5));

        // var msgList = [{ event: 1, sendMessageTime: 1000 }, { event: 2, sendMessageTime: 5000 }, { event: 3, sendMessageTime: 7000 },
        // { event: 3, sendMessageTime: 10000 },{event:1,sendMessageTime:12000},{event:2,sendMessageTime:13000}];

        // this._gameEventList = msgList;
        // this.playMsg();

        // var count = 5;
        // Laya.timer.loop(2000, this, () => {
        //     if (count < 0) return;
        //     count -= 1;
        //     if (!this._gameEventList) this._gameEventList = [];
        //     if(count == 3 || count == 1) this._gameEventList.push({ event: 1, sendMessageTime: 0, isTest:count });
        //     else this._gameEventList.push({ event: 1, sendMessageTime: 13000 + (5 - count) * 1000 });
        //     this.playMsg();
        // })
        // console.log("=====查看conch key======");
        // console.log(laya.utils.Browser.window.gc);
        // for (var key in laya.utils.Browser.window) {
        //     console.log("====window.gc====key:"+key);
        // }
        // for (var key in laya.utils.Browser.window.conch) {
        //     console.log("=====conch===key:"+key);
        // }

        // var path = "https://file.yallaludo.com/Avatar/********/Staging/YallaChat/ca3m5htishg4nokpdt8g/5e46518d-dd45-43c3-ae01-67a6f41963bf.png";
        // var path1 = "http://fat-account.jcmcookie.com";
        // var path2 = "wss://ludo.yalla.games//index.html";
        // var path3 = "ws://fat-ludo.yalla.games:9012";
        // var path4 = "https://graph.facebook.com/https://file.yallaludo.com/https://file.yallaludo.com/https://file.yallaludo.com/Avatar/********/Staging/YallaChat/ca3m5htishg4nokpdt8g/5e46518d-dd45-43c3-ae01-67a6f41963bf.png";
        // var url = yalla.util.getHost(path);
        // var url1 = yalla.util.getHost(path1);
        // var url2 = yalla.util.getHost(path2);
        // var url3 = yalla.util.getHost(path3);
        // var url4 = yalla.util.getHost(path4);

        // yalla.Skin.instance.common_file_host = "fat-file.jcmcookie.com";
        // var dUrl = yalla.getFileDomainHeadType(path4);
        // console.log("===lalal啦啦==durl="+dUrl);
        // console.log(url);
        // console.log("url1:" + url1);
        // console.log(url2);
        // console.log(url3);
        // console.log(url4);
    }

    // private _gameEventList = [];
    // private _msgBeginTime = 0;
    // private _isPlay: boolean = false;
    // private playMsg(): void {
    //     if (!this._gameEventList || this._gameEventList.length < 1) return;
    //     if (this._isPlay) return;
    //     var msg = this._gameEventList.shift();
    //         this._isPlay = true;
    //         if (!this._msgBeginTime) this._msgBeginTime = msg.sendMessageTime;
    //         console.log('===playMsg  0===this._msgBeginTime='+this._msgBeginTime);
    //         if (this._msgBeginTime > 0) {
    //             var time = msg.sendMessageTime - this._msgBeginTime;
    //             console.log(msg, '===playMsg 1===time='+time);
    //             if (time > 0) {
    //                 setTimeout((msg) => {
    //                     console.log(yalla.getTimeHMS(), '=======msg:', msg);
    //                     this._msgBeginTime = msg.sendMessageTime;
    //                     this._isPlay = false;
    //                     this.playMsg();
    //                 }, time, msg);
    //             } else {
    //                 console.log(yalla.getTimeHMS(), '====1===msg:', msg);
    //                 this._isPlay = false;
    //                 this.playMsg();
    //             }
    //         }
    //     }

    private initEvent(): void {
        Laya.stage.on(Laya.Event.MOUSE_DOWN, this, this.touchStart);
        Laya.stage.on('click', this, (e: MouseEvent) => {
            if (e.target instanceof Laya.Button) {
                if (this._activityMain && this._activityMain.gameView && this._activityMain.gameView.visible) {
                    yalla.Sound.playChampionSound('click');
                } else {
                    yalla.Sound.playSound('click');
                }
            }
            yalla.event.YallaEvent.instance.event('click');
            yalla.common.BubbleReport.instance.hide();
        });
        yalla.NativeWebSocket.instance.init();
        yalla.NativeWebSocket.instance.on(Laya.Event.OPEN, this, this.onWebSocketOpen);
        // yalla.NativeWebSocket.instance.on('onNetConnecting', this, this.onWebSocketConnecting);
        yalla.Native.instance.on(yalla.Native.instance.Event.BLUR, this, this.onBlur);
        yalla.Native.instance.on(yalla.Native.instance.Event.FOUCE, this, this.onForce);
        yalla.Native.instance.on(yalla.Native.instance.Event.QUITGAME, this, this.onQuitGame);
        yalla.Native.instance.on(yalla.Native.instance.Event.EXITGAMEWATCHFORLEAGUE, this, this.onQuitGame);
        yalla.common.connect.ReconnectControl.instance.on(yalla.common.connect.ReconnectControl.instance.Event.RECONECT, this, this.onReconnect);
        yalla.common.connect.ReconnectControl.instance.on(yalla.common.connect.ReconnectControl.instance.Event.TIMEOVER, this, this.onReTimeOver);
        yalla.Native.instance.on(yalla.Native.instance.Event.ACCOUNT, this, this.onAccoutLogin);
        yalla.Native.instance.on(yalla.Native.instance.Event.OFFGAME, this, this.onOffGame);
        yalla.Native.instance.on(yalla.Native.instance.Event.NOVICEGAME, this, this.onNovice);
        yalla.Native.instance.on(yalla.Native.instance.Event.JUNGLRNOVICE, this, this.onJungleNovices);
        yalla.Native.instance.on(yalla.Native.instance.Event.JACKAROONOVICE, this, this.onJackaroNovice);
        yalla.Native.instance.on(yalla.Native.instance.Event.HASBACKHALL, this, this.onHasBackHall);
        yalla.Native.instance.on(yalla.Native.instance.Event.KICKEDOUT, this, this.onKickedOut);
        yalla.Native.instance.on(yalla.Native.instance.Event.CHAMPIONTOGAME, this, this.championToGame);
        yalla.Native.instance.on(yalla.Native.instance.Event.BACKCHAMPION, this, this.backChampion);
        yalla.Native.instance.on(yalla.Native.instance.Event.UPDATEGOLDS, this, this.updateGolds);
        yalla.Native.instance.on(yalla.Native.instance.Event.CONNECTSTATECHANGE, this, this.connectStateChange);
        yalla.Native.instance.on(yalla.Native.instance.Event.HIDECHAMPION, this, this.hideChampion);
        yalla.Native.instance.on(yalla.Native.instance.Event.SETMUTE, this, this.setMute);
        yalla.Native.instance.on(yalla.Native.instance.Event.ONBACKPRESSED, this, this.onBackPressed);

        yalla.Native.instance.on(yalla.Native.instance.Event.RESIZEVIEW, this, this.resizeView);
        yalla.Native.instance.on(yalla.Native.instance.Event.UPDATEMONEY, this, this.updateMoney);
    }
    private touchStart(touch: Laya.Event) {
        console.log("touch Game Stage：" + touch.stageX + ":" + touch.stageY);
    }
    private addNetStateEvent() {
        // if (!yalla.Native.instance.hasListener(yalla.Native.instance.Event.NETSTATECHANGE))
        //     yalla.Native.instance.on(yalla.Native.instance.Event.NETSTATECHANGE, this, this.netStateChange);
    }
    private removeNetStateEvent() {
        // yalla.Native.instance.off(yalla.Native.instance.Event.NETSTATECHANGE, this, this.netStateChange);
    }
    private onReTimeOver() {
        this._gameMain && this._gameMain.onReTimeOver();
    }
    private onBlur() {
        yalla.Debug.log(' onPause- ');
        this._gameMain && this._gameMain.onBlur();
        this._activityMain && this._activityMain.onBlur();
    }

    private onForce() {
        yalla.Debug.log(' onResume- ');
        this._gameMain && this._gameMain.onForce();
        this._activityMain && this._activityMain.onForce();
    }
    private onReconnect() {
        yalla.Debug.log(" :main  onReconnect");
        this._gameMain && this._gameMain.onReconnect();
    }
    private onQuitGame(msg: any) {
        yalla.Debug.log("onQuitGame");
        yalla.Native.instance.closeKeyBoard();
        this._gameMain && this._gameMain.onQuitGame(msg);
    }

    private onKickedOut() {
        yalla.Debug.log('onKickedOut');
        this._gameMain && this._gameMain.onKickedOut();
        this._activityMain && this._activityMain.onKickedOut();
    }
    /**
     * @param json {
     *  {"code":-1|1} 1 与服务器连接上，-1与服务器断开 锦标赛需要
     */
    private connectStateChange(json) {
        if (this._gameMain) return;
        this._activityMain && this._activityMain.connectStateChange(json);
    }
    /**
     * @param time 当前时间
     * 限制登录时间间隔 返回是否连续调用
     */
    private limitLogin(time, limitTime: number = 3000) {
        return time - this._loginTime < limitTime;
    }

    /**
     * 登陆游戏
     * TODO android 如果不执行onInit
     * @param msg 
     */
    private onAccoutLogin(msg: any): void {
        this.clear();
        Laya.timer.clearAll(this);

        // this.addNetStateEvent();
        yalla.Debug.log(Laya.stage.frameRate + ' : 1.4.5.0-0807-19：20- ' + yalla.Global.LoadingChampionState, "blue");
        if (!msg) return;

        yalla.Native.instance.getDomainType(() => {
            yalla.DomainUtil.instance.initDomainData();
            // yalla.Skin.instance.setDomainData();
            yalla.Skin.instance.updateSkinDataRootUrl();
        });

        yalla.Global.onAccountLoginMsg = msg;
        ludo.muteSpectator.init(msg.idx);
        yalla.Sound.init();

        // yalla.Global.isFouce = true;//1.3.4add ios和chat小程序顶号的时候 导致ios退出到登录界面，再次进入游戏的时候此值仍然为false
        if (yalla.util.IsWinConch()) {
            yalla.Font.init(Laya.Handler.create(this, this.initGameView, [msg]));
        } else {
            this.initGameView(msg);
        }
    }
    private initGameView(msg): void {
        if (msg.isChampionship) {
            if (yalla.Global.LoadingChampionState == 1) return;
            yalla.Global.gameType = 1;
            if (this._activityMain) {
                this._activityMain.clear();
                this._activityMain = null;
            }
            Laya.stage.removeChildByName("activityView");
            yalla.Global.LoadingChampionState = 1;
            this._activityMain = new ActivityMain();
            this._activityMain.onLogin(msg);

        } else {
            if (this._activityMain && this._activityMain.gameView) {
                this._gameMain && (this._gameMain.gameView.visible = false);
                Laya.timer.once(5000, this, this.hideChampionTime);
            }

            var gameId = msg.gameid ? msg.gameid : msg.gameId;
            var gameType = gameId ? gameId : msg.gameType;
            if (!gameType) gameType = yalla.Global.gameType

            yalla.Global.gameType = gameType;
            yalla.Debug.log(yalla.Global.Account);
            if (yalla.Global.gameType == GameType.LUDO) {//切入ludo
                this._gameMain = new LudoMain(0);

            } else if (yalla.Global.gameType == GameType.DOMINO) {//切入domino
                // yalla.Global.Account.leagueWatch = true;
                if (yalla.Global.Account.leagueWatch) {
                    this._gameMain = new WatchMain(0);
                } else {
                    this._gameMain = new DominoMain(0);
                }

            } else if (yalla.Global.gameType == GameType.SNAKEANDLADER) {//切入snake
                this._gameMain = new SnakeMain(0);
            } else if (yalla.Global.gameType == GameType.JACKARO) {//切入jackaro
                this._gameMain = new JackaroMain(0);
            }

            this._gameMain && this._gameMain.onLogin(msg);
            if (!yalla.Global.Account.leagueWatch) {
                // //现在改为主动调起原生soket连接（因锦标赛）
                yalla.NativeWebSocket.instance.connectSocket();
                if (yalla.util.netState() == 0) yalla.common.connect.ReconnectControl.instance.connect(true);
            }
        }
    }

    /**
     * 离线模式
     * @param msg 
     */
    private onOffGame(msg) {
        var time = new Date().getTime();
        if (this.limitLogin(time)) {
            return;
        }
        this._loginTime = time;
        this.clear();
        yalla.Sound.init();
        if (yalla.util.IsWinConch()) {
            yalla.Font.init(Laya.Handler.create(this, () => {
                this._gameMain = new LudoMain(1);
                this._gameMain.onOffGame(msg);
            }));
        } else {
            this._gameMain = new LudoMain(1);
            this._gameMain.onOffGame(msg);
        }
        // this.onJungleNovices(10019, "https://img2.baidu.com/it/u=**********,3953168763&fm=26&fmt=auto&gp=0.jpg", "yalla", "1", "1");
    }
    private onJungleNovices(a, b, c, d, f) {
        var time = new Date().getTime();
        if (this.limitLogin(time)) {
            return;
        }
        this._loginTime = time;
        this.clear();
        yalla.Sound.init();
        if (yalla.util.IsWinConch()) {
            yalla.Font.init(Laya.Handler.create(this, () => {
                this._gameMain = new LudoMain(3);
                this._gameMain.onNoviceGame(a, b, c, d, f);
            }, [a, b, c]));
        } else {
            this._gameMain = new LudoMain(3);
            this._gameMain.onNoviceGame(a, b, c, d, f);
        }
    }
    private noviceLoadedState: number = 0;
    private onJackaroNovice(a, b, c, d, f) {
        if (this.noviceLoadedState == 1) return;
        var time = new Date().getTime();
        if (this.limitLogin(time)) { return; }
        this._loginTime = time;
        yalla.Global.gameType = GameType.JACKARO;
        this.clear();
        yalla.Sound.init();
        yalla.PhoneVibrate.init();
        if (yalla.util.IsWinConch()) {
            yalla.Font.init(Laya.Handler.create(this, () => {
                this.reloadJackarooNovice(a, b, c, d, f);
            }, [a, b, c]));
        } else {
            this.reloadJackarooNovice(a, b, c, d, f);
        }
    }
    private reloadJackarooNovice(a, b, c, d, f) {
        this.noviceLoadedState = 1;
        this._gameMain = new JackaroMain(1);
        if (this.noviceLoadedState == 2) {
            this._gameMain.onNoviceGame(a, b, c, d, f);
        } else {
            Laya.loader.load([
                { url: 'res/atlas/jackaro/novice.png', type: Laya.Loader.IMAGE },
                { url: 'res/atlas/jackaro/novice.atlas', type: Laya.Loader.ATLAS },
                // { url: "res/json/jackarooNoviceData.json", type: Laya.Loader.JSON },
                { url: "res/sk/novice/xinshou.png", type: Laya.Loader.IMAGE },
                { url: "res/sk/novice/xinshou.sk", type: Laya.Loader.BUFFER },
                { url: "res/sk/novice/jiaoHuan01.png", type: Laya.Loader.IMAGE },
                { url: "res/sk/novice/jiaoHuan01.sk", type: Laya.Loader.BUFFER },
                { url: "jackaro/novice/finger.ani", type: Laya.Loader.JSON },
                { url: "jackaro/novice/nextTurn.ani", type: Laya.Loader.JSON }
            ], Laya.Handler.create(this, () => {
                this.noviceLoadedState = 2;
                yalla.Global.gameType == GameType.JACKARO && this._gameMain && !yalla.Global.onlineMode && this._gameMain.onNoviceGame(a, b, c, d, f);
            }), null, null, 1, true, "jackaroo_novice_res");

        }
    }
    /**
     * 新手引导
     */
    private onNovice(a, b, c, d, f) {
        var time = new Date().getTime();
        if (this.limitLogin(time)) {
            return;
        }
        this._loginTime = time;
        this.clear();
        yalla.Sound.init();
        if (yalla.util.IsWinConch()) {
            yalla.Font.init(Laya.Handler.create(this, () => {
                this._gameMain = new LudoMain(2);
                this._gameMain.onNoviceGame(a, b, c, d, f);
            }, [a, b, c]));
        } else {
            this._gameMain = new LudoMain(2);
            this._gameMain.onNoviceGame(a, b, c, d, f);
        }
    }

    /**
     * 返回大厅已经GameUI
     * 如果游戏再断线中返回大厅，需要把重连ui关闭
     */
    private onHasBackHall(removeView: Boolean = false): void {
        yalla.Global.onAccountLoginMsg = null;
        yalla.common.connect.ReconnectControl.instance.hideConnect();
        yalla.Debug.log('==Main.onHasBackHall001===' + removeView);
        if (this._gameMain) {
            if (removeView) {
                this._gameMain.clear();
                this._gameMain = null;
            } else {
                this._gameMain.backHallClear();
            }
        }
        if (this._activityMain) {
            if (yalla.Global.LoadingChampionState != 2) return;
            this._activityMain.clear();
            this._activityMain = null;
        }
        Laya.stage.removeChildByName("resultView");
        if (laya.utils.Browser.window.hasOwnProperty('gc')) laya.utils.Browser.window.gc();
    }

    /**
     * 锦标赛进入游戏
     */
    private championToGame(d: any): void {
        var version = yalla.Global.Account.version;
        var sameSkinBuySwitch = yalla.Global.Account.sameSkinBuySwitch;
        yalla.Native.instance.isBack = true;
        d.version = version;
        yalla.Global.Account = d;
        yalla.Global.Account.sameSkinBuySwitch = sameSkinBuySwitch;
        yalla.DomainUtil.instance.initSocketUrl(d.socketUrlList);
        this.onAccoutLogin(d);
    }

    /**
     * 游戏到锦标赛 
     * d {winnerId:*}
     */
    private backChampion(d: any): void {
        if (this._gameMain) {
            yalla.Native.instance.backChampion();
            this._gameMain.clear();
            this._gameMain = null;
        }

        //打开应用，再次未结束的游戏，，退出或者结算后，返回锦标赛（界面初始化）
        if (!this._activityMain) this._activityMain = new ActivityMain();
        if (this._activityMain && this._activityMain.gameView) {
            this._activityMain.backChampion(d);
        }
    }

    /**
     * 进入游戏，等待removeMatchView后隐藏锦标赛界面
     */
    private hideChampionTime(): void {
        if (this._activityMain && this._activityMain.gameView) {
            this._gameMain && (this._gameMain.gameView.visible = true);
            this._activityMain.changeToGame();
            this._activityMain = null;
        }
    }
    private hideChampion() {
        Laya.timer.clear(this, this.hideChampionTime);
        this.hideChampionTime();
    }

    /**
     * 商城购买了金币，游戏内更新
     * @param d 
     */
    private updateGolds(d: any) {
        if (this._activityMain && this._activityMain.gameView) {
            yalla.data.ActivityService.instance.updateGolds(d);
        }
    }

    /**
     * 更新本地屏蔽玩家数据
     * @param d 
     */
    private setMute(d: any) {
        var isMute = yalla.Mute.muted(d.idx);
        yalla.Debug.log("====开始举报===isMute=" + isMute);
        var gameType = yalla.Global.gameType;
        if (isMute) {
            yalla.Mute.remove(d.idx);
            if (gameType == GameType.LUDO) {
                if (!ludo.Global.inSit()) {//观战操作
                    yalla.Native.instance.mobClickEvent(buryPoint.GAME_SPECTATER_UNMUTE);//埋点 观战者点击取消屏蔽
                } else {//玩家操作
                    var playerInfo = ludoRoom.instance.getPlayerByIdx(d.idx);

                    if (playerInfo) {
                        if (playerInfo.sitNum < 0) {//对观战
                            yalla.Native.instance.mobClickEvent(buryPoint.GAME_UMUTESPECTATER);
                        } else {//对玩家
                            yalla.Native.instance.mobClickEvent(buryPoint.GAME_USER_UNMUTE);
                        }
                    }
                }
            } else if (gameType == GameType.DOMINO) {
                yalla.Native.instance.mobClickEvent(buryPoint.DGAME_USER_UNMUTE);//埋点 点击取消屏蔽
            } else if (gameType == GameType.SNAKEANDLADER) {

            }
        } else {
            yalla.Mute.add(d.idx);
            if (gameType == GameType.LUDO) {
                if (!ludo.Global.inSit()) {//观战操作
                    yalla.Native.instance.mobClickEvent(buryPoint.GAME_SPECTATER_MUTE);//埋点 观战者点击屏蔽
                } else {//玩家操作
                    var playerInfo = ludoRoom.instance.getPlayerByIdx(d.idx);
                    if (playerInfo) {
                        if (playerInfo.sitNum < 0) {//对观战
                            yalla.Native.instance.mobClickEvent(buryPoint.GAME_MUTESPECTATER);
                        } else {//对玩家
                            yalla.Native.instance.mobClickEvent(buryPoint.GAME_USER_MUTE);
                        }
                    }
                }
            } else if (gameType == GameType.DOMINO) {
                yalla.Native.instance.mobClickEvent(buryPoint.DGAME_USER_MUTE);//埋点 点击取消屏蔽
            } else if (gameType == GameType.SNAKEANDLADER) {

            }
        }
    }

    /**
     * 正常非托管 非打开聊天面板 非打开互动表情 非小局结算弹窗 时，支持返回键点击弹出退出游戏弹窗
     * 非正常状态时，点击返回无处理
     */
    private onBackPressed(): void {
        yalla.Debug.log(yalla.common.Confirm.instance.isExit + "=======Main.onBackPressed=======" + yalla.common.connect.ReconnectControl.instance.isConnectView);
        if (yalla.common.connect.ReconnectControl.instance.isConnectView) return;
        if (this._gameMain) {
            if (yalla.util.isBackPressed()) {
                if (yalla.common.Confirm.instance.isExit) {//有确认退出弹框，再次点击返回键 需要关闭弹窗
                    yalla.util.closeAllDialog();
                    return;
                }
                if (yalla.Global.IsGameOver) return;
                switch (yalla.Global.gameType) {
                    case GameType.DOMINO:
                        yalla.event.YallaEvent.instance.event(yalla.data.EnumCustomizeCmd.Dominose_Back);
                        break;
                    case GameType.SNAKEANDLADER:
                        yalla.event.YallaEvent.instance.event(yalla.data.snake.EnumCustomizeCmd.Exit_Back);
                        break;
                    default:
                        this._gameMain.gameView && this._gameMain.gameView.onBackPressed && this._gameMain.gameView.onBackPressed();
                        break;
                }
            }
        } else if (this._activityMain) {
            if (yalla.common.Confirm.instance.isExit) {//有确认退出弹框，再次点击返回键 需要关闭弹窗
                yalla.util.closeAllDialog();
                return;
            }
            this._activityMain.gameView && this._activityMain.gameView.onBackPressed();
        }
    }

    private reScreenTimer = 0;
    private resizeView(msg: any): void {
        Laya.timer.clear(this, this.updateView);
        Laya.timer.once(1000, this, this.updateView);

        Laya.timer.clear(this, this.resizeScreen);
        this.reScreenTimer = 0;
        this.resizeScreen()
        // Laya.timer.once(2000, this, this.resizeScreen);
    }
    private updateView(): void {
        // this.resizeScreen();//部分设备需要延迟再执行yalla.Screen.onResize才能正确适配
        if (yalla.Global.onAccountLoginMsg) {
            this.onAccoutLogin(yalla.Global.onAccountLoginMsg);
        }
    }
    private resizeScreen() {
        yalla.Screen.onResize(true);
        if (this._gameMain) {
            this._gameMain.onResize();
        }
        this.reScreenTimer++;
        if (this.reScreenTimer <= 5) Laya.timer.once(500, this, this.resizeScreen);
    }

    private updateMoney() {
        if (this._gameMain) {
            this._gameMain.updateMoney();
        }
    }

    private onWebSocketOpen() {
        yalla.Debug.log('-----onWebSocketOpen---11 ' + yalla.Global.IsGameOver);
        yalla.Global.isconnect = true;
        yalla.data.ChatDataPool.Instance.sendEmojiResponse();
        this._gameMain && this._gameMain.onWebSocketOpen();
    }

    // private onWebSocketConnecting() {
    //     this._gameMain && this._gameMain.onWebSocketConnecting();
    // }

    /**
     * 适配
     */
    private adapter(): void {
        UIConfig.popupBgAlpha = 0.5;
        Laya.Text.CharacterCache = false;
        if (yalla.Native.instance.deviceType == DeviceType.IOS)
            Laya.stage.screenMode = "vertical";
        Laya.ResourceVersion.type = Laya.ResourceVersion.FILENAME_VERSION;
        Laya.stage.alignV = Laya.Stage.ALIGN_MIDDLE;
        Laya.stage.alignH = Laya.Stage.ALIGN_CENTER;

        Laya.stage.bgColor = "#312445";
    }

    private clear(): void {
        yalla.Native.instance.clear();//TODO::backhall方法支持延迟调用，这可能会出现再原生发放调用成功后执行；再次执行onLogin，之前的backHall延迟时间又会被执行，so这里清理
        Laya.stage.removeChildByName("resultView");
        if (this._gameMain) {
            this._gameMain.clear();
            this._gameMain = null;
        }
        Laya.timer.clear(this, this.resizeScreen);
        Laya.stage.removeChildByName("gameView");
        Laya.stage.removeChildByName("TrustTip");
        yalla.File.clearCache();
        Laya.stage.y = 0;
        // yalla.Global.isconnect = false; //TODO::1.3.0及以前加入则不能重连
    }
}
new GameMain();