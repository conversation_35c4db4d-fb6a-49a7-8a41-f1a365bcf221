/*
* name;
*/
module ArabicFixerTool {
    export var showTashkeel: boolean = true;
    export var combineTashkeel: boolean = true;
    export var useHinduNumbers: boolean = false;

    export function ToCharArray(str: string): number[] {
        var result: number[] = [];
        for (var i: number = 0; i < str.length; i++) {
            result[i] = str.charCodeAt(i);
        }
        return result;
    }

    export function RemoveTashkeel(str: string, tashkeelLocation: TashkeelLocation[]): string  {
        //tashkeelLocation = new List<TashkeelLocation>();
        var letters: number[] = ToCharArray(str);

        var index: number = 0;
        for (var i: number = 0; i < letters.length; i++) {
            if (letters[i] == 0x064B) { // Tanween Fatha
                tashkeelLocation.push(new TashkeelLocation(0x064B, i));
                index++;
            }
            else if (letters[i] == 0x064C) { // Tanween Damma
                tashkeelLocation.push(new TashkeelLocation(0x064C, i));
                index++;
            }
            else if (letters[i] == 0x064D) { // Tanween Kasra
                tashkeelLocation.push(new TashkeelLocation(0x064D, i));
                index++;
            }
            else if (letters[i] == 0x064E) { // Fatha
                if (index > 0 && combineTashkeel)  {
                    if (tashkeelLocation[index - 1].tashkeel == 0x0651) // Shadda
                    {
                        tashkeelLocation[index - 1].tashkeel = 0xFC60; // Shadda With Fatha
                        continue;
                    }
                }

                tashkeelLocation.push(new TashkeelLocation(0x064E, i));
                index++;
            }
            else if (letters[i] == 0x064F) { // DAMMA
                if (index > 0 && combineTashkeel) {
                    if (tashkeelLocation[index - 1].tashkeel == 0x0651) { // SHADDA
                        tashkeelLocation[index - 1].tashkeel = 0xFC61; // Shadda With DAMMA
                        continue;
                    }
                }
                tashkeelLocation.push(new TashkeelLocation(0x064F, i));
                index++;
            }
            else if (letters[i] == 0x0650) { // KASRA
                if (index > 0 && combineTashkeel) {
                    if (tashkeelLocation[index - 1].tashkeel == 0x0651) { // SHADDA
                        tashkeelLocation[index - 1].tashkeel = 0xFC62; // Shadda With KASRA
                        continue;
                    }
                }
                tashkeelLocation.push(new TashkeelLocation(0x0650, i));
                index++;
            }
            else if (letters[i] == 0x0651) { // SHADDA
                if (index > 0 && combineTashkeel)  {
                    if (tashkeelLocation[index - 1].tashkeel == 0x064E) // FATHA
                    {
                        tashkeelLocation[index - 1].tashkeel = 0xFC60; // Shadda With Fatha
                        continue;
                    }

                    if (tashkeelLocation[index - 1].tashkeel == 0x064F) // DAMMA
                    {
                        tashkeelLocation[index - 1].tashkeel = 0xFC61; // Shadda With DAMMA
                        continue;
                    }

                    if (tashkeelLocation[index - 1].tashkeel == 0x0650) // KASRA
                    {
                        tashkeelLocation[index - 1].tashkeel = 0xFC62; // Shadda With KASRA
                        continue;
                    }
                }

                tashkeelLocation.push(new TashkeelLocation(0x0651, i));
                index++;
            }
            else if (letters[i] == 0x0652) { // SUKUN
                tashkeelLocation.push(new TashkeelLocation(0x0652, i));
                index++;
            }
            else if (letters[i] == 0x0653) { // MADDAH ABOVE
                tashkeelLocation.push(new TashkeelLocation(0x0653, i));
                index++;
            }
        }

        var split: string[] = str.split(/[\u064B\u064C\u064D\u064E\u064F\u0650\u0651\u0652\u0653\uFC60\uFC61\uFC62]/);
        str = "";

        for (var s = 0; s < split.length; s++)  {
            str += split[s];
        }

        return str;
    }

    export function ReturnTashkeel(letters: number[], tashkeelLocation: TashkeelLocation[]): number[]  {
        var lettersWithTashkeel: number[] = [];

        var letterWithTashkeelTracker: number = 0;
        for (var i = 0; i < letters.length; i++)  {
            lettersWithTashkeel[letterWithTashkeelTracker] = letters[i];
            letterWithTashkeelTracker++;
            tashkeelLocation.forEach(function (hLocation: TashkeelLocation) {
                if (hLocation.position == letterWithTashkeelTracker)  {
                    lettersWithTashkeel[letterWithTashkeelTracker] = hLocation.tashkeel;
                    letterWithTashkeelTracker++;
                }
            })
        }

        return lettersWithTashkeel;
    }

    /// <summary>
    /// Converts a string to a form in which the sting will be displayed correctly for arabic text.
    /// </summary>
    /// <param name="str">String to be converted. Example: "Aaa"</param>
    /// <returns>Converted string. Example: "aa aaa A" without the spaces.</returns>
    export function FixLine(str: string): string  {
        var test: string = "";

        var tashkeelLocation: TashkeelLocation[] = [];

        var originString: string = RemoveTashkeel(str, tashkeelLocation);

        var lettersOrigin: number[] = ToCharArray(originString);
        var lettersFinal: number[] = ToCharArray(originString);



        for (var i = 0; i < lettersOrigin.length; i++)  {
            lettersOrigin[i] = ArabicTable.Convert(lettersOrigin[i]);
        }

        for (var i = 0; i < lettersOrigin.length; i++)  {
            var skip: boolean = false;
            //lettersOrigin[i] = (char)ArabicTable.ArabicMapper.Convert(lettersOrigin[i]);
            // For special Lam Letter connections.
            if (lettersOrigin[i] == IsolatedArabicLetters.Lam)  {

                if (i < lettersOrigin.length - 1)  {
                    //lettersOrigin[i + 1] = (char)ArabicTable.ArabicMapper.Convert(lettersOrigin[i + 1]);
                    if ((lettersOrigin[i + 1] == IsolatedArabicLetters.AlefMaksoor))  {
                        lettersOrigin[i] = 0xFEF7;
                        lettersFinal[i + 1] = 0xFFFF;
                        skip = true;
                    }
                    else if ((lettersOrigin[i + 1] == IsolatedArabicLetters.Alef))  {
                        lettersOrigin[i] = 0xFEF9;
                        lettersFinal[i + 1] = 0xFFFF;
                        skip = true;
                    }
                    else if ((lettersOrigin[i + 1] == IsolatedArabicLetters.AlefHamza))  {
                        lettersOrigin[i] = 0xFEF5;
                        lettersFinal[i + 1] = 0xFFFF;
                        skip = true;
                    }
                    else if ((lettersOrigin[i + 1] == IsolatedArabicLetters.AlefMad))  {
                        lettersOrigin[i] = 0xFEF3;
                        lettersFinal[i + 1] = 0xFFFF;
                        skip = true;
                    }
                }

            }


            if (!IsIgnoredCharacter(lettersOrigin[i]))  {
                if (IsMiddleLetter(lettersOrigin, i))
                    lettersFinal[i] = (lettersOrigin[i] + 3);
                else if (IsFinishingLetter(lettersOrigin, i))
                    lettersFinal[i] = (lettersOrigin[i] + 1);
                else if (IsLeadingLetter(lettersOrigin, i))
                    lettersFinal[i] = (lettersOrigin[i] + 2);
            }

            //string strOut = String.Format(@"\x{0:x4}", (ushort)lettersOrigin[i]);
            //UnityEngine.Debug.Log(strOut);

            //strOut = String.Format(@"\x{0:x4}", (ushort)lettersFinal[i]);
            //UnityEngine.Debug.Log(strOut);

            test += String.fromCharCode(lettersOrigin[i]) + " ";
            if (skip)
                i++;


            //chaning numbers to hindu
            if (useHinduNumbers) {
                if (lettersOrigin[i] == 0x0030)
                    lettersFinal[i] = 0x0660;
                else if (lettersOrigin[i] == 0x0031)
                    lettersFinal[i] = 0x0661;
                else if (lettersOrigin[i] == 0x0032)
                    lettersFinal[i] = 0x0662;
                else if (lettersOrigin[i] == 0x0033)
                    lettersFinal[i] = 0x0663;
                else if (lettersOrigin[i] == 0x0034)
                    lettersFinal[i] = 0x0664;
                else if (lettersOrigin[i] == 0x0035)
                    lettersFinal[i] = 0x0665;
                else if (lettersOrigin[i] == 0x0036)
                    lettersFinal[i] = 0x0666;
                else if (lettersOrigin[i] == 0x0037)
                    lettersFinal[i] = 0x0667;
                else if (lettersOrigin[i] == 0x0038)
                    lettersFinal[i] = 0x0668;
                else if (lettersOrigin[i] == 0x0039)
                    lettersFinal[i] = 0x0669;
            }

        }



        //Return the Tashkeel to their places.
        if (showTashkeel)
            lettersFinal = ReturnTashkeel(lettersFinal, tashkeelLocation);


        var list: number[] = [];

        var numberList: number[] = [];

        for (var i = lettersFinal.length - 1; i >= 0; i--)  {
            //				if (lettersFinal[i] == '(')
            //						numberList.Add(')');
            //				else if (lettersFinal[i] == ')')
            //					numberList.Add('(');
            //				else if (lettersFinal[i] == '<')
            //					numberList.Add('>');
            //				else if (lettersFinal[i] == '>')
            //					numberList.Add('<');
            //				else 
            if (Char.IsPunctuation(lettersFinal[i]) && i > 0 && i < lettersFinal.length - 1 &&
                (Char.IsPunctuation(lettersFinal[i - 1]) || Char.IsPunctuation(lettersFinal[i + 1])))  {
                if (lettersFinal[i] == '('.charCodeAt(0))
                    list.push(')'.charCodeAt(0));
                else if (lettersFinal[i] == ')'.charCodeAt(0))
                    list.push('('.charCodeAt(0));
                else if (lettersFinal[i] == '<'.charCodeAt(0))
                    list.push('>'.charCodeAt(0));
                else if (lettersFinal[i] == '>'.charCodeAt(0))
                    list.push('<'.charCodeAt(0));
                else if (lettersFinal[i] == '['.charCodeAt(0))
                    list.push(']'.charCodeAt(0));
                else if (lettersFinal[i] == ']'.charCodeAt(0))
                    list.push('['.charCodeAt(0));
                else if (lettersFinal[i] != 0xFFFF)
                    list.push(lettersFinal[i]);
            }
            // For cases where english words and arabic are mixed. This allows for using arabic, english and numbers in one sentence.
            else if (lettersFinal[i] == ' '.charCodeAt(0) && i > 0 && i < lettersFinal.length - 1 &&
                (Char.IsLower(lettersFinal[i - 1]) || Char.IsUpper(lettersFinal[i - 1]) || Char.IsNumber(lettersFinal[i - 1])) &&
                (Char.IsLower(lettersFinal[i + 1]) || Char.IsUpper(lettersFinal[i + 1]) || Char.IsNumber(lettersFinal[i + 1])))  {
                numberList.push(lettersFinal[i]);
            }

            else if (Char.IsNumber(lettersFinal[i]) || Char.IsLower(lettersFinal[i]) ||
                Char.IsUpper(lettersFinal[i]) || Char.IsSymbol(lettersFinal[i]) ||
                Char.IsPunctuation(lettersFinal[i]))// || lettersFinal[i] == '^') //)
            {

                if (lettersFinal[i] == '('.charCodeAt(0))
                    numberList.push(')'.charCodeAt(0));
                else if (lettersFinal[i] == ')'.charCodeAt(0))
                    numberList.push('('.charCodeAt(0));
                else if (lettersFinal[i] == '<'.charCodeAt(0))
                    numberList.push('>'.charCodeAt(0));
                else if (lettersFinal[i] == '>'.charCodeAt(0))
                    numberList.push('<'.charCodeAt(0));
                else if (lettersFinal[i] == '['.charCodeAt(0))
                    list.push(']'.charCodeAt(0));
                else if (lettersFinal[i] == ']'.charCodeAt(0))
                    list.push('['.charCodeAt(0));
                else
                    numberList.push(lettersFinal[i]);
            }
            else if ((lettersFinal[i] >= 0xD800 && lettersFinal[i] <= 0xDBFF) ||
                (lettersFinal[i] >= 0xDC00 && lettersFinal[i] <= 0xDFFF))  {
                numberList.push(lettersFinal[i]);
            }
            else  {
                if (numberList.length > 0)  {
                    for (var j: number = 0; j < numberList.length; j++)
                        list.push(numberList[numberList.length - 1 - j]);
                    numberList = [];
                }
                if (lettersFinal[i] != 0xFFFF)
                    list.push(lettersFinal[i]);

            }
        }
        if (numberList.length > 0)  {
            for (var j: number = 0; j < numberList.length; j++)
                list.push(numberList[numberList.length - 1 - j]);
            numberList = [];
        }

        // Moving letters from a list to an array.
        lettersFinal = [];
        for (var i = 0; i < list.length; i++)
            lettersFinal[i] = list[i];


        str = Char.toString(lettersFinal);
        return str;
    }



    /// <summary>
    /// English letters, numbers and punctuation characters are ignored. This checks if the ch is an ignored character.
    /// </summary>
    /// <param name="ch">The character to be checked for skipping</param>
    /// <returns>True if the character should be ignored, false if it should not be ignored.</returns>
    export function IsIgnoredCharacter(ch: number): boolean  {
        var isPunctuation: boolean = Char.IsPunctuation(ch);
        var isNumber: boolean = Char.IsNumber(ch);
        var isLower: boolean = Char.IsLower(ch);
        var isUpper: boolean = Char.IsUpper(ch);
        var isSymbol: boolean = Char.IsSymbol(ch);
        var isPersianCharacter: boolean = ch == 0xFB56 || ch == 0xFB7A || ch == 0xFB8A || ch == 0xFB92 || ch == 0xFB8E;
        var isPresentationFormB: boolean = (ch <= 0xFEFF && ch >= 0xFE70);
        var isAcceptableCharacter: boolean = isPresentationFormB || isPersianCharacter || ch == 0xFBFC;



        return isPunctuation ||
            isNumber ||
            isLower ||
            isUpper ||
            isSymbol ||
            !isAcceptableCharacter ||
            ch == 'a'.charCodeAt(0) || ch == '>'.charCodeAt(0) || ch == '<'.charCodeAt(0) || ch == 0x061B;

        //            return char.IsPunctuation(ch) || char.IsNumber(ch) || ch == 'a' || ch == '>' || ch == '<' ||
        //                    char.IsLower(ch) || char.IsUpper(ch) || ch == (char)0x061B || char.IsSymbol(ch)
        //					|| !(ch <= (char)0xFEFF && ch >= (char)0xFE70) // Presentation Form B
        //					|| ch == (char)0xFB56 || ch == (char)0xFB7A || ch == (char)0xFB8A || ch == (char)0xFB92; // Persian Characters

        //					PersianPe = 0xFB56,
        //		PersianChe = 0xFB7A,
        //		PersianZe = 0xFB8A,
        //		PersianGaf = 0xFB92
        //lettersOrigin[i] <= (char)0xFEFF && lettersOrigin[i] >= (char)0xFE70
    }

    /// <summary>
    /// Checks if the letter at index value is a leading character in Arabic or not.
    /// </summary>
    /// <param name="letters">The whole word that contains the character to be checked</param>
    /// <param name="index">The index of the character to be checked</param>
    /// <returns>True if the character at index is a leading character, else, returns false</returns>
    export function IsLeadingLetter(letters: number[], index: number): boolean  {

        var lettersThatCannotBeBeforeALeadingLetter: boolean = index == 0
            || letters[index - 1] == ' '.charCodeAt(0)
            || letters[index - 1] == '*'.charCodeAt(0)  // ??? Remove?
            || letters[index - 1] == 'A'.charCodeAt(0)  // ??? Remove?
            || Char.IsPunctuation(letters[index - 1])
            || letters[index - 1] == '>'.charCodeAt(0)
            || letters[index - 1] == '<'.charCodeAt(0)
            || letters[index - 1] == IsolatedArabicLetters.Alef
            || letters[index - 1] == IsolatedArabicLetters.Dal
            || letters[index - 1] == IsolatedArabicLetters.Thal
            || letters[index - 1] == IsolatedArabicLetters.Ra2
            || letters[index - 1] == IsolatedArabicLetters.Zeen
            || letters[index - 1] == IsolatedArabicLetters.PersianZe
            //|| letters[index - 1] == (int)IsolatedArabicLetters.AlefMaksora 
            || letters[index - 1] == IsolatedArabicLetters.Waw
            || letters[index - 1] == IsolatedArabicLetters.AlefMad
            || letters[index - 1] == IsolatedArabicLetters.AlefHamza
            || letters[index - 1] == IsolatedArabicLetters.Hamza
            || letters[index - 1] == IsolatedArabicLetters.AlefMaksoor
            || letters[index - 1] == IsolatedArabicLetters.WawHamza
            || letters[index - 1] == IsolatedArabicLetters.Ae
            || letters[index - 1] == IsolatedArabicLetters.U
            || letters[index - 1] == IsolatedArabicLetters.Yu
            || letters[index - 1] == IsolatedArabicLetters.Oe
            || letters[index - 1] == IsolatedArabicLetters.Ve;

        var lettersThatCannotBeALeadingLetter: boolean = letters[index] != ' '.charCodeAt(0)
            && letters[index] != IsolatedArabicLetters.Dal
            && letters[index] != IsolatedArabicLetters.Thal
            && letters[index] != IsolatedArabicLetters.Ra2
            && letters[index] != IsolatedArabicLetters.Zeen
            && letters[index] != IsolatedArabicLetters.PersianZe
            && letters[index] != IsolatedArabicLetters.Alef
            && letters[index] != IsolatedArabicLetters.AlefHamza
            && letters[index] != IsolatedArabicLetters.AlefMaksoor
            && letters[index] != IsolatedArabicLetters.AlefMad
            && letters[index] != IsolatedArabicLetters.WawHamza
            && letters[index] != IsolatedArabicLetters.Waw
            && letters[index] != IsolatedArabicLetters.Hamza
            && letters[index] != IsolatedArabicLetters.Ae
            && letters[index] != IsolatedArabicLetters.U
            && letters[index] != IsolatedArabicLetters.Yu
            && letters[index] != IsolatedArabicLetters.Oe
            && letters[index] != IsolatedArabicLetters.Ve;

        var lettersThatCannotBeAfterLeadingLetter: boolean = index < letters.length - 1
            && letters[index + 1] != ' '.charCodeAt(0)
            && !Char.IsPunctuation(letters[index + 1])
            && !Char.IsNumber(letters[index + 1])
            && !Char.IsSymbol(letters[index + 1])
            && !Char.IsLower(letters[index + 1])
            && !Char.IsUpper(letters[index + 1])
            && letters[index + 1] != IsolatedArabicLetters.Hamza;

        if (lettersThatCannotBeBeforeALeadingLetter && lettersThatCannotBeALeadingLetter && lettersThatCannotBeAfterLeadingLetter)

        //		if ((index == 0 || letters[index - 1] == ' ' || letters[index - 1] == '*' || letters[index - 1] == 'A' || char.IsPunctuation(letters[index - 1])
        //		     || letters[index - 1] == '>' || letters[index - 1] == '<' 
        //		     || letters[index - 1] == (int)IsolatedArabicLetters.Alef
        //		     || letters[index - 1] == (int)IsolatedArabicLetters.Dal || letters[index - 1] == (int)IsolatedArabicLetters.Thal
        //		     || letters[index - 1] == (int)IsolatedArabicLetters.Ra2 
        //		     || letters[index - 1] == (int)IsolatedArabicLetters.Zeen || letters[index - 1] == (int)IsolatedArabicLetters.PersianZe
        //		     || letters[index - 1] == (int)IsolatedArabicLetters.AlefMaksora || letters[index - 1] == (int)IsolatedArabicLetters.Waw
        //		     || letters[index - 1] == (int)IsolatedArabicLetters.AlefMad || letters[index - 1] == (int)IsolatedArabicLetters.AlefHamza
        //		     || letters[index - 1] == (int)IsolatedArabicLetters.AlefMaksoor || letters[index - 1] == (int)IsolatedArabicLetters.WawHamza) 
        //		    && letters[index] != ' ' && letters[index] != (int)IsolatedArabicLetters.Dal
        //		    && letters[index] != (int)IsolatedArabicLetters.Thal
        //		    && letters[index] != (int)IsolatedArabicLetters.Ra2 
        //		    && letters[index] != (int)IsolatedArabicLetters.Zeen && letters[index] != (int)IsolatedArabicLetters.PersianZe
        //		    && letters[index] != (int)IsolatedArabicLetters.Alef && letters[index] != (int)IsolatedArabicLetters.AlefHamza
        //		    && letters[index] != (int)IsolatedArabicLetters.AlefMaksoor
        //		    && letters[index] != (int)IsolatedArabicLetters.AlefMad
        //		    && letters[index] != (int)IsolatedArabicLetters.WawHamza
        //		    && letters[index] != (int)IsolatedArabicLetters.Waw
        //		    && letters[index] != (int)IsolatedArabicLetters.Hamza
        //		    && index < letters.Length - 1 && letters[index + 1] != ' ' && !char.IsPunctuation(letters[index + 1] ) && !char.IsNumber(letters[index + 1])
        //		    && letters[index + 1] != (int)IsolatedArabicLetters.Hamza )
        {
            return true;
        }
        else
            return false;
    }

    /// <summary>
    /// Checks if the letter at index value is a finishing character in Arabic or not.
    /// </summary>
    /// <param name="letters">The whole word that contains the character to be checked</param>
    /// <param name="index">The index of the character to be checked</param>
    /// <returns>True if the character at index is a finishing character, else, returns false</returns>
    export function IsFinishingLetter(letters: number[], index: number): boolean  {
        var indexZero: boolean = index != 0;
        var lettersThatCannotBeBeforeAFinishingLetter: boolean = (index == 0) ? false :
            letters[index - 1] != ' '.charCodeAt(0)
            //				&& char.IsDigit(letters[index-1])
            //				&& char.IsLower(letters[index-1])
            //				&& char.IsUpper(letters[index-1])
            //				&& char.IsNumber(letters[index-1])
            //				&& char.IsWhiteSpace(letters[index-1])
            //				&& char.IsPunctuation(letters[index-1])
            //				&& char.IsSymbol(letters[index-1])

            && letters[index - 1] != IsolatedArabicLetters.Dal
            && letters[index - 1] != IsolatedArabicLetters.Thal
            && letters[index - 1] != IsolatedArabicLetters.Ra2
            && letters[index - 1] != IsolatedArabicLetters.Zeen
            && letters[index - 1] != IsolatedArabicLetters.PersianZe
            //&& letters[index - 1] != (int)IsolatedArabicLetters.AlefMaksora 
            && letters[index - 1] != IsolatedArabicLetters.Waw
            && letters[index - 1] != IsolatedArabicLetters.Alef
            && letters[index - 1] != IsolatedArabicLetters.AlefMad
            && letters[index - 1] != IsolatedArabicLetters.AlefHamza
            && letters[index - 1] != IsolatedArabicLetters.AlefMaksoor
            && letters[index - 1] != IsolatedArabicLetters.WawHamza
            && letters[index - 1] != IsolatedArabicLetters.Hamza
            && letters[index - 1] != IsolatedArabicLetters.Ae
            && letters[index - 1] != IsolatedArabicLetters.U
            && letters[index - 1] != IsolatedArabicLetters.Yu
            && letters[index - 1] != IsolatedArabicLetters.Oe
            && letters[index - 1] != IsolatedArabicLetters.Ve


            && !Char.IsPunctuation(letters[index - 1])
            && !Char.IsSymbol(letters[index - 1])
            && letters[index - 1] != '>'.charCodeAt(0)
            && letters[index - 1] != '<'.charCodeAt(0);


        var lettersThatCannotBeFinishingLetters: boolean = letters[index] != ' '.charCodeAt(0) && letters[index] != IsolatedArabicLetters.Hamza;




        if (lettersThatCannotBeBeforeAFinishingLetter && lettersThatCannotBeFinishingLetters)

        //		if (index != 0 && letters[index - 1] != ' ' && letters[index - 1] != '*' && letters[index - 1] != 'A'
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.Dal && letters[index - 1] != (int)IsolatedArabicLetters.Thal
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.Ra2 
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.Zeen && letters[index - 1] != (int)IsolatedArabicLetters.PersianZe
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.AlefMaksora && letters[index - 1] != (int)IsolatedArabicLetters.Waw
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.Alef && letters[index - 1] != (int)IsolatedArabicLetters.AlefMad
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.AlefHamza && letters[index - 1] != (int)IsolatedArabicLetters.AlefMaksoor
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.WawHamza && letters[index - 1] != (int)IsolatedArabicLetters.Hamza 
        //		    && !char.IsPunctuation(letters[index - 1]) && letters[index - 1] != '>' && letters[index - 1] != '<' 
        //		    && letters[index] != ' ' && index < letters.Length
        //		    && letters[index] != (int)IsolatedArabicLetters.Hamza)
        {
            //try
            //{
            //    if (char.IsPunctuation(letters[index + 1]))
            //        return true;
            //    else
            //        return false;
            //}
            //catch (Exception e)
            //{
            //    return false;
            //}

            return true;
        }
        //return true;
        else
            return false;
    }

    /// <summary>
    /// Checks if the letter at index value is a middle character in Arabic or not.
    /// </summary>
    /// <param name="letters">The whole word that contains the character to be checked</param>
    /// <param name="index">The index of the character to be checked</param>
    /// <returns>True if the character at index is a middle character, else, returns false</returns>
    export function IsMiddleLetter(letters: number[], index: number): boolean  {
        var lettersThatCannotBeMiddleLetters: boolean = (index == 0) ? false :
            letters[index] != IsolatedArabicLetters.Alef
            && letters[index] != IsolatedArabicLetters.Dal
            && letters[index] != IsolatedArabicLetters.Thal
            && letters[index] != IsolatedArabicLetters.Ra2
            && letters[index] != IsolatedArabicLetters.Zeen
            && letters[index] != IsolatedArabicLetters.PersianZe
            //&& letters[index] != (int)IsolatedArabicLetters.AlefMaksora
            && letters[index] != IsolatedArabicLetters.Waw
            && letters[index] != IsolatedArabicLetters.AlefMad
            && letters[index] != IsolatedArabicLetters.AlefHamza
            && letters[index] != IsolatedArabicLetters.AlefMaksoor
            && letters[index] != IsolatedArabicLetters.WawHamza
            && letters[index] != IsolatedArabicLetters.Hamza
            && letters[index] != IsolatedArabicLetters.Ae
            && letters[index] != IsolatedArabicLetters.U
            && letters[index] != IsolatedArabicLetters.Yu
            && letters[index] != IsolatedArabicLetters.Oe
            && letters[index] != IsolatedArabicLetters.Ve;;

        var lettersThatCannotBeBeforeMiddleCharacters: boolean = (index == 0) ? false :
            letters[index - 1] != IsolatedArabicLetters.Alef
            && letters[index - 1] != IsolatedArabicLetters.Dal
            && letters[index - 1] != IsolatedArabicLetters.Thal
            && letters[index - 1] != IsolatedArabicLetters.Ra2
            && letters[index - 1] != IsolatedArabicLetters.Zeen
            && letters[index - 1] != IsolatedArabicLetters.PersianZe
            //&& letters[index - 1] != (int)IsolatedArabicLetters.AlefMaksora
            && letters[index - 1] != IsolatedArabicLetters.Waw
            && letters[index - 1] != IsolatedArabicLetters.AlefMad
            && letters[index - 1] != IsolatedArabicLetters.AlefHamza
            && letters[index - 1] != IsolatedArabicLetters.AlefMaksoor
            && letters[index - 1] != IsolatedArabicLetters.WawHamza
            && letters[index - 1] != IsolatedArabicLetters.Hamza
            && letters[index - 1] != IsolatedArabicLetters.Ae
            && letters[index - 1] != IsolatedArabicLetters.U
            && letters[index - 1] != IsolatedArabicLetters.Yu
            && letters[index - 1] != IsolatedArabicLetters.Oe
            && letters[index - 1] != IsolatedArabicLetters.Ve
            && !Char.IsPunctuation(letters[index - 1])
            && letters[index - 1] != '>'.charCodeAt(0)
            && letters[index - 1] != '<'.charCodeAt(0)
            && letters[index - 1] != ' '.charCodeAt(0)
            && letters[index - 1] != '*'.charCodeAt(0);

        var lettersThatCannotBeAfterMiddleCharacters: boolean = (index >= letters.length - 1) ? false :
            letters[index + 1] != ' '.charCodeAt(0)
            && letters[index + 1] != '\r'.charCodeAt(0)
            && letters[index + 1] != IsolatedArabicLetters.Hamza
            && !Char.IsNumber(letters[index + 1])
            && !Char.IsSymbol(letters[index + 1])
            && !Char.IsPunctuation(letters[index + 1]);
        if (lettersThatCannotBeAfterMiddleCharacters && lettersThatCannotBeBeforeMiddleCharacters && lettersThatCannotBeMiddleLetters)

        //		if (index != 0 && letters[index] != ' '
        //		    && letters[index] != (int)IsolatedArabicLetters.Alef && letters[index] != (int)IsolatedArabicLetters.Dal
        //		    && letters[index] != (int)IsolatedArabicLetters.Thal && letters[index] != (int)IsolatedArabicLetters.Ra2
        //		    && letters[index] != (int)IsolatedArabicLetters.Zeen && letters[index] != (int)IsolatedArabicLetters.PersianZe 
        //		    && letters[index] != (int)IsolatedArabicLetters.AlefMaksora
        //		    && letters[index] != (int)IsolatedArabicLetters.Waw && letters[index] != (int)IsolatedArabicLetters.AlefMad
        //		    && letters[index] != (int)IsolatedArabicLetters.AlefHamza && letters[index] != (int)IsolatedArabicLetters.AlefMaksoor
        //		    && letters[index] != (int)IsolatedArabicLetters.WawHamza && letters[index] != (int)IsolatedArabicLetters.Hamza
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.Alef && letters[index - 1] != (int)IsolatedArabicLetters.Dal
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.Thal && letters[index - 1] != (int)IsolatedArabicLetters.Ra2
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.Zeen && letters[index - 1] != (int)IsolatedArabicLetters.PersianZe 
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.AlefMaksora
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.Waw && letters[index - 1] != (int)IsolatedArabicLetters.AlefMad
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.AlefHamza && letters[index - 1] != (int)IsolatedArabicLetters.AlefMaksoor
        //		    && letters[index - 1] != (int)IsolatedArabicLetters.WawHamza && letters[index - 1] != (int)IsolatedArabicLetters.Hamza 
        //		    && letters[index - 1] != '>' && letters[index - 1] != '<' 
        //		    && letters[index - 1] != ' ' && letters[index - 1] != '*' && !char.IsPunctuation(letters[index - 1])
        //		    && index < letters.Length - 1 && letters[index + 1] != ' ' && letters[index + 1] != '\r' && letters[index + 1] != 'A' 
        //		    && letters[index + 1] != '>' && letters[index + 1] != '>' && letters[index + 1] != (int)IsolatedArabicLetters.Hamza
        //		    )
        {
            try  {
                if (Char.IsPunctuation(letters[index + 1]))
                    return false;
                else
                    return true;
            }
            catch (e)  {
                return false;
            }
            //return true;
        }
        else
            return false;
    }
}