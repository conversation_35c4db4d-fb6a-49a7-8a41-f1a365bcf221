/*
* name;
*/
module ArabicSupport{
        export function FixDefault(str:string):string 
		{
			return Fix(str, false, false);
		}
		
		export function FixRL(str:string , rtl:boolean):string 
		{
			if(rtl)
				
			{
				return FixDefault(str);
			}
			else
			{
				var words:string[] = str.split(' ');
				var result:string  = "";
				var  arabicToIgnore:string = "";
				for(var i=0;i<words.length;i++)
				{
                    var word:string=words[i];
					if(IsLower(word.toLowerCase()[Math.floor(word.length/2)]))
					{
						result += FixDefault(arabicToIgnore) + word + " ";
						arabicToIgnore = "";
					}
					else
					{
						arabicToIgnore += word + " ";
						
					}
				}
				if(arabicToIgnore != "")
					result += FixDefault(arabicToIgnore);
				
				return result;
			}
		}
		
		/// <summary>
		/// Fix the specified string with customization options.
		/// </summary>
		/// <param name='str'>
		/// String to be fixed.
		/// </param>
		/// <param name='showTashkeel'>
		/// Show tashkeel.
		/// </param>
		/// <param name='useHinduNumbers'>
		/// Use hindu numbers.
		/// </param>
		export function  Fix(str:string , showTashkeel:boolean, useHinduNumbers:boolean ):string
		{
			ArabicFixerTool.showTashkeel = showTashkeel;
			ArabicFixerTool.useHinduNumbers = useHinduNumbers;
			
			if (!str) return '';//存在str  undefined情况
			//str.indexOf
			if(str.indexOf("\n")>=0)
				str = str.replace("\n", "\n");
			
			if(str.indexOf("\n")>=0)
			{
				//var  stringSeparators:string[] = new string[] {Environment.NewLine};
				var strSplit:string[]= str.split(/\n/g);
				
				if(strSplit.length == 0)
					return ArabicFixerTool.FixLine(str);
				else if(strSplit.length == 1)
					return ArabicFixerTool.FixLine(str);
				else
				{
					var outputString:string = ArabicFixerTool.FixLine(strSplit[0]);
					var iteration:number= 1;
					if(strSplit.length > 1)
					{
						while(iteration < strSplit.length)
						{
							outputString += "\n" + ArabicFixerTool.FixLine(strSplit[iteration]);
							iteration++;
						}
					}				
					return outputString;
				}	
			}
			else
			{
				return ArabicFixerTool.FixLine(str);
			}
			
		}

        export function  Fix3(str:string , showTashkeel:boolean, combineTashkeel:boolean, useHinduNumbers:boolean):string
        {
            ArabicFixerTool.combineTashkeel = combineTashkeel;
            return Fix(str, showTashkeel, useHinduNumbers);
        }

        export function IsUpper(code:string) {
            return code === code.toUpperCase()
        }
        export function IsLower(code:string) {
            return code === code.toLowerCase();
        }





      
}