/*
* name;
*/
module ArabicTable{
   






    export var mapList:ArabicMapping[]=null;
	//ArabicTable();
	/// <summary>
	/// Setting up the conversion table
	/// </summary>
	export function ArabicTable()
	{
		mapList = [];
		
		mapList.push(new ArabicMapping(GeneralArabicLetters.Hamza,IsolatedArabicLetters.Hamza));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Alef, IsolatedArabicLetters.Alef));
		mapList.push(new ArabicMapping(GeneralArabicLetters.AlefHamza, IsolatedArabicLetters.AlefHamza));
		mapList.push(new ArabicMapping(GeneralArabicLetters.WawHamza, IsolatedArabicLetters.WawHamza));
		mapList.push(new ArabicMapping(GeneralArabicLetters.AlefMaksoor, IsolatedArabicLetters.AlefMaksoor));
		mapList.push(new ArabicMapping(GeneralArabicLetters.AlefMagsora, IsolatedArabicLetters.AlefMagsora));
		mapList.push(new ArabicMapping(GeneralArabicLetters.HamzaNabera, IsolatedArabicLetters.HamzaNabera));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Ba, IsolatedArabicLetters.Ba));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Ta, IsolatedArabicLetters.Ta));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Tha2, IsolatedArabicLetters.Tha2));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Jeem, IsolatedArabicLetters.Jeem));
		mapList.push(new ArabicMapping(GeneralArabicLetters.H7aa, IsolatedArabicLetters.H7aa));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Khaa2, IsolatedArabicLetters.Khaa2));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Dal, IsolatedArabicLetters.Dal));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Thal, IsolatedArabicLetters.Thal));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Ra2, IsolatedArabicLetters.Ra2));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Zeen, IsolatedArabicLetters.Zeen));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Seen, IsolatedArabicLetters.Seen));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Sheen, IsolatedArabicLetters.Sheen));
		mapList.push(new ArabicMapping(GeneralArabicLetters.S9a, IsolatedArabicLetters.S9a));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Dha, IsolatedArabicLetters.Dha));
		mapList.push(new ArabicMapping(GeneralArabicLetters.T6a, IsolatedArabicLetters.T6a));
		mapList.push(new ArabicMapping(GeneralArabicLetters.T6ha, IsolatedArabicLetters.T6ha));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Ain, IsolatedArabicLetters.Ain));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Gain, IsolatedArabicLetters.Gain));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Fa, IsolatedArabicLetters.Fa));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Gaf, IsolatedArabicLetters.Gaf));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Kaf, IsolatedArabicLetters.Kaf));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Lam, IsolatedArabicLetters.Lam));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Meem, IsolatedArabicLetters.Meem));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Noon, IsolatedArabicLetters.Noon));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Ha, IsolatedArabicLetters.Ha));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Waw, IsolatedArabicLetters.Waw));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Ya, IsolatedArabicLetters.Ya));
		mapList.push(new ArabicMapping(GeneralArabicLetters.AlefMad, IsolatedArabicLetters.AlefMad));
		mapList.push(new ArabicMapping(GeneralArabicLetters.TaMarboota, IsolatedArabicLetters.TaMarboota));		
		mapList.push(new ArabicMapping(GeneralArabicLetters.PersianPe, IsolatedArabicLetters.PersianPe)); 		// Persian Letters;
		mapList.push(new ArabicMapping(GeneralArabicLetters.PersianChe, IsolatedArabicLetters.PersianChe));
		mapList.push(new ArabicMapping(GeneralArabicLetters.PersianZe, IsolatedArabicLetters.PersianZe));
		mapList.push(new ArabicMapping(GeneralArabicLetters.PersianGaf, IsolatedArabicLetters.PersianGaf));
		mapList.push(new ArabicMapping(GeneralArabicLetters.PersianGaf2, IsolatedArabicLetters.PersianGaf2));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Yu, IsolatedArabicLetters.Yu));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Oe, IsolatedArabicLetters.Oe));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Ve, IsolatedArabicLetters.Ve));
		mapList.push(new ArabicMapping(GeneralArabicLetters.Ae, IsolatedArabicLetters.Ae));
		mapList.push(new ArabicMapping(GeneralArabicLetters.U, IsolatedArabicLetters.U));
		
		mapList.push(new ArabicMapping(GeneralArabicLetters.Ya1, IsolatedArabicLetters.Ya1));
		
		//for (int i = 0; i < generalArabic.Length; i++)
		//    mapList.Add(new ArabicMapping(generalArabic.GetValue(i), isolatedArabic.GetValue(i)));    // I
		
		
	}
	
	
	
	export function Convert(toBeConverted:number):number
	{
		if(mapList==null){
			ArabicTable();
		}
		
		for (var i=0;i< mapList.length;i++)
        if (mapList[i].from == toBeConverted)
		{
			// console.log(i + "==="+ mapList[i].from+"：from====阿语Convert====toBeConverted："+toBeConverted + '==to:'+mapList[i].to);
            return mapList[i].to;
        }
		return toBeConverted;
	}
	
}