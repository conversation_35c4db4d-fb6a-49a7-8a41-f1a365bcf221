/*
* name;
*/
 
  enum UnicodeCategory {
 
        UppercaseLetter = 0,
 
        LowercaseLetter = 1,
 
        TitlecaseLetter = 2,
 
        ModifierLetter = 3,
 
        OtherLetter = 4,
 
        NonSpacingMark = 5,
 
        SpacingCombiningMark = 6,
 
        EnclosingMark = 7,
 
        DecimalDigitNumber = 8,
 
        LetterNumber = 9,
 
        OtherNumber = 10,
 
        SpaceSeparator = 11,
 
        LineSeparator = 12,
 
        ParagraphSeparator = 13,
 
        Control = 14,
 
        Format = 15,
 
        Surrogate = 16,
 
        PrivateUse = 17,
 
        ConnectorPunctuation = 18,
 
        DashPunctuation = 19,
 
        OpenPunctuation = 20,
 
        ClosePunctuation = 21,
 
        InitialQuotePunctuation = 22,
 
        FinalQuotePunctuation = 23,
 
        OtherPunctuation = 24,
 
        MathSymbol = 25,
 
        CurrencySymbol = 26,
 
        ModifierSymbol = 27,
 
        OtherSymbol = 28,
 
        OtherNotAssigned = 29,
    }


module Char{

    export var  categoryForLatin1:any = [
            UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control,    // 0000 - 0007
            UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control,    // 0008 - 000F
            UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control,    // 0010 - 0017
            UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control,    // 0018 - 001F
            UnicodeCategory.SpaceSeparator, UnicodeCategory.OtherPunctuation, UnicodeCategory.OtherPunctuation, UnicodeCategory.OtherPunctuation, UnicodeCategory.CurrencySymbol, UnicodeCategory.OtherPunctuation, UnicodeCategory.OtherPunctuation, UnicodeCategory.OtherPunctuation,    // 0020 - 0027
            UnicodeCategory.OpenPunctuation, UnicodeCategory.ClosePunctuation, UnicodeCategory.OtherPunctuation, UnicodeCategory.MathSymbol, UnicodeCategory.OtherPunctuation, UnicodeCategory.DashPunctuation, UnicodeCategory.OtherPunctuation, UnicodeCategory.OtherPunctuation,    // 0028 - 002F
            UnicodeCategory.DecimalDigitNumber, UnicodeCategory.DecimalDigitNumber, UnicodeCategory.DecimalDigitNumber, UnicodeCategory.DecimalDigitNumber, UnicodeCategory.DecimalDigitNumber, UnicodeCategory.DecimalDigitNumber, UnicodeCategory.DecimalDigitNumber, UnicodeCategory.DecimalDigitNumber,    // 0030 - 0037
            UnicodeCategory.DecimalDigitNumber, UnicodeCategory.DecimalDigitNumber, UnicodeCategory.OtherPunctuation, UnicodeCategory.OtherPunctuation, UnicodeCategory.MathSymbol, UnicodeCategory.MathSymbol, UnicodeCategory.MathSymbol, UnicodeCategory.OtherPunctuation,    // 0038 - 003F
            UnicodeCategory.OtherPunctuation, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter,    // 0040 - 0047
            UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter,    // 0048 - 004F
            UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter,    // 0050 - 0057
            UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.OpenPunctuation, UnicodeCategory.OtherPunctuation, UnicodeCategory.ClosePunctuation, UnicodeCategory.ModifierSymbol, UnicodeCategory.ConnectorPunctuation,    // 0058 - 005F
            UnicodeCategory.ModifierSymbol, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter,    // 0060 - 0067
            UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter,    // 0068 - 006F
            UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter,    // 0070 - 0077
            UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.OpenPunctuation, UnicodeCategory.MathSymbol, UnicodeCategory.ClosePunctuation, UnicodeCategory.MathSymbol, UnicodeCategory.Control,    // 0078 - 007F
            UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control,    // 0080 - 0087
            UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control,    // 0088 - 008F
            UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control,    // 0090 - 0097
            UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control, UnicodeCategory.Control,    // 0098 - 009F
            UnicodeCategory.SpaceSeparator, UnicodeCategory.OtherPunctuation, UnicodeCategory.CurrencySymbol, UnicodeCategory.CurrencySymbol, UnicodeCategory.CurrencySymbol, UnicodeCategory.CurrencySymbol, UnicodeCategory.OtherSymbol, UnicodeCategory.OtherSymbol,    // 00A0 - 00A7
            UnicodeCategory.ModifierSymbol, UnicodeCategory.OtherSymbol, UnicodeCategory.LowercaseLetter, UnicodeCategory.InitialQuotePunctuation, UnicodeCategory.MathSymbol, UnicodeCategory.DashPunctuation, UnicodeCategory.OtherSymbol, UnicodeCategory.ModifierSymbol,    // 00A8 - 00AF
            UnicodeCategory.OtherSymbol, UnicodeCategory.MathSymbol, UnicodeCategory.OtherNumber, UnicodeCategory.OtherNumber, UnicodeCategory.ModifierSymbol, UnicodeCategory.LowercaseLetter, UnicodeCategory.OtherSymbol, UnicodeCategory.OtherPunctuation,    // 00B0 - 00B7
            UnicodeCategory.ModifierSymbol, UnicodeCategory.OtherNumber, UnicodeCategory.LowercaseLetter, UnicodeCategory.FinalQuotePunctuation, UnicodeCategory.OtherNumber, UnicodeCategory.OtherNumber, UnicodeCategory.OtherNumber, UnicodeCategory.OtherPunctuation,    // 00B8 - 00BF
            UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter,    // 00C0 - 00C7
            UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter,    // 00C8 - 00CF
            UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.MathSymbol,    // 00D0 - 00D7
            UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.UppercaseLetter, UnicodeCategory.LowercaseLetter,    // 00D8 - 00DF
            UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter,    // 00E0 - 00E7
            UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter,    // 00E8 - 00EF
            UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.MathSymbol,    // 00F0 - 00F7
            UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter, UnicodeCategory.LowercaseLetter,    // 00F8 - 00FF
        ];

    export var Punctuation:string="!\"#$%&'()*+,-./:;<=>?@[\]^_`{|}~";
    export var NumberString:string="0123456789٠٩٨٧٦٥٤٣٢١";
    // export var NumberString:string="0123456789";
    export var SymbolString:string="±×÷∶∧∨∑∏∪∩∈∷√⊥∥∠⌒⊙∫∮≡≌≈∽∝≠≮≯≤≥∞∵∴、。·ˉˇ¨〃々—～‖…‘’“”〔〕〈〉《》「」『』〖〗【】！＂＇（），－．：；＜＝＞？［］｛｜｝｀﹉﹊﹋﹌﹍﹎﹏﹐﹑﹒﹔﹕﹖﹗﹙﹚﹛﹜﹝﹞︵︶︹︺︿﹀︽︾﹁﹂﹃﹄︻︼︷︸︱︳︴︵︶︹︺︿﹀︽︾﹁﹂﹃﹄︻︼︷︸︱︳︴∟∣≒≦≧⊿═"
    export function IsPunctuation(ch:number):boolean{
        var char:string =String.fromCharCode(ch);
        if(Punctuation.indexOf(char)>=0){
            return true;
        }
        return false;
    }
    export function IsNumber(ch:number):boolean{
        var char:string =String.fromCharCode(ch);
        if(NumberString.indexOf(char)>=0){
            return true;
        }
        return false;
    }


     export function IsLatin1(ch:number):boolean {
            return (ch <= 0x00ff);
        }
 
        // Return true for all characters below or equal U+007f, which is ASCII.
     export function IsAscii(ch:number):boolean {
            return (ch <= 0x007f);
        }
    export function GetLatin1UnicodeCategory(ch:number):number {
           // Contract.Assert(IsLatin1(ch), "Char.GetLatin1UnicodeCategory(): ch should be <= 007f");
            return categoryForLatin1[ch];
        }
        // Return the Unicode category for Unicode character <= 0x00ff.      
    export function IsLower(c:number):boolean {
            if (IsLatin1(c)) {
                if (IsAscii(c)) {
                    return (c >= 'a'.charCodeAt(0) && c <= 'z'.charCodeAt(0));
                }
                return GetLatin1UnicodeCategory(c)== UnicodeCategory.LowercaseLetter;
            }
            return  GetLatin1UnicodeCategory(c) == UnicodeCategory.LowercaseLetter;          
        }

    export function IsUpper(ch:number):boolean {
       if (IsLatin1(ch)) {
            if (IsAscii(ch)) {
                return (ch >= 'A'.charCodeAt(0) && ch <= 'Z'.charCodeAt(0));
            }
            return  GetLatin1UnicodeCategory(ch)== UnicodeCategory.UppercaseLetter;
        }
        return  GetLatin1UnicodeCategory(ch)== UnicodeCategory.UppercaseLetter;       
    }
   
     export function IsSymbol(ch:number):boolean {
        var char:string =String.fromCharCode(ch);
        if(SymbolString.indexOf(char)>=0){
            return true;
        }
        return false;
     }
     export function toString(ch:number[]):string {
         var result:string="";
         for(var i:number=0;i<ch.length;i++){
             result+=String.fromCharCode(ch[i]);
         }
         return result;
     }
}