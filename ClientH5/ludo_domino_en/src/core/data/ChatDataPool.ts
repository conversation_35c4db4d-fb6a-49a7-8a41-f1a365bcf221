module yalla.data {
    export class ChatDataPool {

        public static _instance: ChatDataPool;
        private _emojiThemeHash: Object;
        private _emojiHash: Object;
        public emojiThemeList: Array<EmojiThemeConf> = [];
        /**表情主题一页最大数量 */
        public pageCount = 5;
        public canSortEmoji: boolean = false;           //是否成功购买表情，购买后 置为true，在此打开快捷表情面包，需要重新排序置为false
        public isNewEmojiList: boolean = true;          //表情列表是否最新

        public emojiThemeIcon = { 0: "icon_bq_horse", 1: "icon_bq_Arab_male", 2: "icon_bq_Arab_female", 3: "icon_bq_RLemj", 4: "icon_bq_Tiger" };
        public defaultTheme:any = {id: 0, engName: "Horse", arbicName: "Horse", sort: 1, url: "", emojiConfig: [{}],needRoyalLevel:0, isOwn:true };
        public testDataList: Array<any> = [
            {
                id: 1, engName: "Arab_male", arbicName: "Arab_male", sort: 1, url: "",
                emojiConfig: [
                    { uniqueID: 10101, url: "" }, { uniqueID: 10102, url: "" }, { uniqueID: 10103, url: "" },
                    { uniqueID: 10104, url: "" }, { uniqueID: 10105, url: "" }, { uniqueID: 10106, url: "" },
                    { uniqueID: 10107, url: "" }, { uniqueID: 10108, url: "" }, { uniqueID: 10109, url: "" }, { uniqueID: 10110, url: "" }
                ],
                needRoyalLevel: 5, isOwn: false
            },
            {
                id: 2, engName: "Arab_female", arbicName: "Arab_female", sort: 2, url: "",
                emojiConfig: [
                    { uniqueID: 10001, url: "" }, { uniqueID: 10002, url: "" }, { uniqueID: 10003, url: "" },
                    { uniqueID: 10004, url: "" }, { uniqueID: 10005, url: "" }, { uniqueID: 10006, url: "" },
                    { uniqueID: 10007, url: "" }, { uniqueID: 10008, url: "" }, { uniqueID: 10009, url: "" }, { uniqueID: 10010, url: "" }
                ],
                needRoyalLevel: 2,isOwn:true
            },
            {
                id: 3, engName: "RLemj", arbicName: "RLemj", sort: 3, url: "",
                emojiConfig: [
                    { uniqueID: 10201, url: "" }, { uniqueID: 10202, url: "" }, { uniqueID: 10203, url: "" },
                    { uniqueID: 10204, url: "" }, { uniqueID: 10205, url: "" }, { uniqueID: 10206, url: "" },
                    { uniqueID: 10207, url: "" }, { uniqueID: 10208, url: "" }
                ],
                needRoyalLevel: 0,isOwn:true
            },
            {
                id: 4, engName: "Tiger", arbicName: "Tiger", sort: 4, url: "",
                emojiConfig: [
                    { uniqueID: 10301, url: "" }, { uniqueID: 10302, url: "" }, { uniqueID: 10303, url: "" },
                    { uniqueID: 10304, url: "" }, { uniqueID: 10305, url: "" }, { uniqueID: 10306, url: "" },
                    { uniqueID: 10307, url: "" }, { uniqueID: 10308, url: "" }
                ],
                needRoyalLevel: 0,isOwn:true
            },
        ];

        // 表情icon emoji_     动效 emojiSk_
    //     public testEmojiRes = {"emoji":{
    //         "sk": [
    //             "emojiSk_10001.sk","emojiSk_10002.sk","emojiSk_10003.sk","emojiSk_10004.sk","emojiSk_10005.sk",
    //             "emojiSk_10006.sk","emojiSk_10007.sk","emojiSk_10008.sk","emojiSk_10009.sk","emojiSk_10010.sk",
    //             "emojiSk_10101.sk","emojiSk_10102.sk","emojiSk_10103.sk","emojiSk_10104.sk","emojiSk_10105.sk",
    //             "emojiSk_10106.sk","emojiSk_10107.sk","emojiSk_10108.sk","emojiSk_10109.sk","emojiSk_10110.sk",
    //             "emojiSk_10201.sk","emojiSk_10202.sk","emojiSk_10203.sk","emojiSk_10204.sk","emojiSk_10205.sk",
    //             "emojiSk_10206.sk","emojiSk_10207.sk","emojiSk_10208.sk"
    //             ],
    //         "images": [
    //             "emojiSk_10001.png","emojiSk_10002.png","emojiSk_10003.png","emojiSk_10004.png","emojiSk_10005.png",
    //             "emojiSk_10006.png","emojiSk_10007.png","emojiSk_10008.png","emojiSk_10009.png","emojiSk_10010.png",
    //             "emojiSk_10101.png","emojiSk_10102.png","emojiSk_10103.png","emojiSk_10104.png","emojiSk_10105.png",
    //             "emojiSk_10106.png","emojiSk_10107.png","emojiSk_10108.png","emojiSk_10109.png","emojiSk_10110.png",
    //             "emojiSk_10201.png","emojiSk_10202.png","emojiSk_10203.png","emojiSk_10204.png","emojiSk_10205.png",
    //             "emojiSk_10206.png","emojiSk_10207.png","emojiSk_10208.png",
    //             "emoji_10001.png","emoji_10002.png","emoji_10003.png","emoji_10004.png","emoji_10005.png",
    //             "emoji_10006.png","emoji_10007.png","emoji_10008.png","emoji_10009.png","emoji_10010.png",
    //             "emoji_10101.png","emoji_10102.png","emoji_10103.png","emoji_10104.png","emoji_10105.png",
    //             "emoji_10106.png","emoji_10107.png","emoji_10108.png","emoji_10109.png","emoji_10110.png",
    //             "emoji_10201.png","emoji_10202.png","emoji_10203.png","emoji_10204.png","emoji_10205.png",
    //             "emoji_10206.png","emoji_10207.png","emoji_10208.png"
    //         ]
    // }}
        constructor() {
        }

        static get Instance(): ChatDataPool{
           return ChatDataPool._instance || (ChatDataPool._instance = new ChatDataPool());
        }
        /**
         * 从原生 获取表情列表
         * @param callBack 
         */
        public sendEmojiResponse(callBack: Function = null): void{
            if (!this.emojiThemeList || this.emojiThemeList.length < 1 || !this.isNewEmojiList) {
                this.emojiThemeList = [this.defaultTheme];//TODO::如果接口不回掉，也需要有默认表情
                yalla.Native.instance.emojiResponse((data) => {
                    this.isNewEmojiList = true;
                    this.initEmojiData(data);
                    callBack && callBack();
                }, this.isNewEmojiList ? 0 : 1);
            } else {
                callBack && callBack();
            }    
        }

        /**
         * 拉去列表时，已经是最新的顺序（原生处理了）
         */
        public initEmojiData(d: any) {
            // d = [{ "arbicName": "", "currencyPrice": 0, "diamondPrice": 0, "emojiConfig": [{ "animalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/32.gif", "arbicName": "قبلة في الهواء", "engName": "Throw kiss", "id": 0, "preAnimalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/32-32.gif", "sort": 32, "uniqueID": 0, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/32.png" }, { "animalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/31.gif", "arbicName": "حب", "engName": "Love", "id": 0, "preAnimalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/31-31.gif", "sort": 31, "uniqueID": 0, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/31.png" }, { "animalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/30.gif", "arbicName": "غير راض", "engName": "Humph", "id": 0, "preAnimalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/30-30.gif", "sort": 30, "uniqueID": 0, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/30.png" }, { "animalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/29.gif", "arbicName": "هيا", "engName": "Cheering", "id": 0, "preAnimalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/29-29.gif", "sort": 29, "uniqueID": 0, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/29.png" }, { "animalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/28.gif", "arbicName": "مصدوم", "engName": "Shocked", "id": 0, "preAnimalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/28-28.gif", "sort": 28, "uniqueID": 0, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/28.png" }, { "animalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/27.gif", "arbicName": "وجه عابس", "engName": "Grimace", "id": 0, "preAnimalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/27-27.gif", "sort": 27, "uniqueID": 0, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/27.png" }, { "animalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/26.gif", "arbicName": "باكي", "engName": "Cry", "id": 0, "preAnimalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/26-26.gif", "sort": 26, "uniqueID": 0, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/26.png" }, { "animalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/25.gif", "arbicName": "مبتسم", "engName": "Grin", "id": 0, "preAnimalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/25-25.gif", "sort": 25, "uniqueID": 0, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/25.png" }, { "animalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/24.gif", "arbicName": "أريد هدية", "engName": "Gift me", "id": 0, "preAnimalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/24-24.gif", "sort": 24, "uniqueID": 0, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/24.png" }, { "animalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/23.gif", "arbicName": "مرحبا", "engName": "Hi", "id": 0, "preAnimalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/23-23.gif", "sort": 23, "uniqueID": 0, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/23.png" }], "engName": "c", "id": 3, "isCanBuy": true, "isDisplayMall": false, "isOwn": true, "needRoyalLevel": 0, "showPlaceType": [3, 2], "sort": 3, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/icon/c.png" }, { "arbicName": "阿阿阿", "currencyPrice": 200, "diamondPrice": 0, "emojiConfig": [{ "animalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/2.gif", "arbicName": "ممتاز", "engName": "ThumbsUp", "id": 0, "preAnimalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/2-2.gif", "sort": 1, "uniqueID": 0, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/2.png" }, { "animalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/1.gif", "arbicName": "مندهش", "engName": "Astonished", "id": 0, "preAnimalUrl": "https://file.yallaludo.com/Skin/H5/room/emoji/1-1.gif", "sort": 1, "uniqueID": 0, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/1.png" }], "engName": "a", "id": 4, "isCanBuy": true, "isDisplayMall": true, "isOwn": true, "needRoyalLevel": 0, "showPlaceType": [3, 1, 2], "sort": 4, "url": "https://file.yallaludo.com/Skin/H5/room/emoji/icon/a.png" }, { "arbicName": "阿一哦", "currencyPrice": 0, "diamondPrice": 200, "engName": "b", "id": 5, "isCanBuy": true, "isDisplayMall": true, "isOwn": false, "needRoyalLevel": 0, "showPlaceType": [3, 2, 1, 1], "sort": 2 }];
            if (!d) return;
            if (!this._emojiThemeHash) this._emojiThemeHash = {};
            this.emojiThemeList = d ? d : [];
            if (yalla.util.IsBrowser()) this.emojiThemeList = this.testDataList;
            
            //TODO::跟服务端（万泽鹏）确认过，默认的小马表情不会返回，这里就客户端自己添加默认
            var hasDefaultHorse = false;
            this.emojiThemeList.forEach((value: EmojiThemeConf, index: number, arr: Array<EmojiThemeConf>) => {
                if(value.emojiConfig) yalla.util.sortListAsc(value.emojiConfig, 'sort');
                if (value.id == 0) hasDefaultHorse = true;
                this._emojiThemeHash[value.id] = value;

                value.url = yalla.DomainUtil.instance.getFileDomainType_flexible(value.url);
            });
            if(!hasDefaultHorse) this.emojiThemeList.unshift(this.defaultTheme);
            this.addEmojiHash();
        }

        public addEmojiHash(): void{
            var v;
            if (!this._emojiHash) this._emojiHash = {};
            this.emojiThemeList.forEach((value: EmojiThemeConf, index: number, arr: Array<EmojiThemeConf>) => {
                if (value.emojiConfig) {
                    value.emojiConfig.forEach((vv: EmojiConf, vi: number, vArr: Array<EmojiThemeConf>) => {
                        this._emojiHash[vv.uniqueID] = vv;
                        
                        vv.url = yalla.DomainUtil.instance.getFileDomainType_flexible(vv.url);
                        // console.log("=======chatDataPoo.addEmojiHash======uniqueID="+vv.uniqueID);
                    });
                }
            })
        }

        /**
         * 游戏内购买表情后，自己排序（因数据自己维护，不重新拉去列表）
         */
        public sortEmoji() {
            this.emojiThemeList.sort((a: EmojiThemeConf, b: EmojiThemeConf) => {
                var av = Number(a.isOwn);
                var bv = Number(b.isOwn);
                if (av < bv) {
                    return 1;
                } else if (av > bv) {
                    return -1;
                } else {
                    return 0;
                }
            });
        }

        public emojiThemeById(themeId:number): EmojiThemeConf{
            if (!this._emojiThemeHash) return;
            return this._emojiThemeHash[themeId];
        }

        public emojiById(id:number): EmojiConf{
            if (!this._emojiHash) return;
            return this._emojiHash[id];
        }

        /**购买表情失败提示 */
        public checkBuyEmojiError(code:number): string{
            var str = 'Emoji purchase failed';
            var lan = yalla.Font.lan;
            if (code == 401) {
                str = lan == 'ar' ? 'لقطع الذهبية غير كافية' : 'Insufficient Golds';
            } else if (code == 402) {
                // str = lan == 'ar' ? 'الماسات غير كافية' : 'Insufficient Diamonds';
                str = "";//TODO::1.4.4 原生购买有推荐档位，不需要toast提示
            } else if (code == 403) {
                str = lan == 'ar' ? 'تم تجميد حسابك' : 'Your account has been frozen';
            } else {
                str = lan == 'ar' ? 'فشل شراء الإيموجي' : 'Emoji purchase failed';
            }
            return str;
        }

        public clear() {
            this.isNewEmojiList = true;
            this.canSortEmoji = false;
            this._emojiHash = null;
            this._emojiThemeHash = null;
            this.emojiThemeList = null;
        } 
    }
}    
interface EmojiThemeConf {
    id:number, //表情主题 id
    name:string,//主题包名称
    url: string,//url
    engName: string,//英文名称
    arbicName:string,//阿语名称
    sort:number,//排序序号
    diamondPrice:number,//钻石价格
    currencyPrice:number,//金币价格
    isCanBuy:boolean // 是否可购买
    needRoyalLevel:number //可购买需要的皇室等级,default=0
    isOwn:boolean, // 是否已拥有
    emojiConfig?: Array<any>,
    nameDesc: string,//表情包英语解锁描述
    arbicNameDesc:string,//表情包阿语解锁描述
}

interface EmojiConf {
    id:number, //表情主题 id
    url:string,//表情包url
    engName: string,//英文名称
    arbicName:string,//阿语名称
    sort:number,//排序序号
    uniqueID:number//唯一id
}
