module yalla {
    export var res = [
        { url: yalla.getProto('VsGameProtocol'), type: <PERSON>a.Loader.BUFFER },
        { url: yalla.getProto('DominoNew'), type: Laya.Loader.BUFFER },
        { url: yalla.getProto('DominoWatch'), type: Laya.Loader.BUFFER },
        { url: yalla.getProto('ChessGame'), type: Laya.Loader.BUFFER },
        { url: yalla.getProto('SnakeLadder'), type: Laya.Loader.BUFFER },
        { url: yalla.getProto('jackarooGame'), type: Laya.Loader.BUFFER },
        { url: yalla.getLang('ar'), type: Laya.Loader.JSON },

        { url: yalla.getUI('public.atlas'), type: Laya.Loader.ATLAS },
        { url: yalla.getUI('domino.atlas'), type: <PERSON><PERSON><PERSON><PERSON>ader.ATLAS },
        { url: yalla.getUI('36x36.atlas'), type: Laya.Loader.ATLAS },
        { url: yalla.getUI('game.atlas'), type: Laya.Loader.ATLAS },
        { url: yalla.getUI('jackaro.atlas'), type: Laya.Loader.ATLAS },
        { url: yalla.getUI('jackaroCard.atlas'), type: Laya.Loader.ATLAS },
        { url: yalla.getUI('activity.atlas'), type: Laya.Loader.ATLAS },
        { url: yalla.getUI('curtain.atlas'), type: Laya.Loader.ATLAS },
        { url: yalla.getUI('duel.atlas'), type: Laya.Loader.ATLAS },
        { url: yalla.getUI('public.png'), type: Laya.Loader.IMAGE },
        { url: yalla.getUI('domino.png'), type: Laya.Loader.IMAGE },
        { url: yalla.getUI('36x36.png'), type: Laya.Loader.IMAGE },
        { url: yalla.getUI('game.png'), type: Laya.Loader.IMAGE },
        { url: yalla.getUI('jackaro.png'), type: Laya.Loader.IMAGE },
        { url: yalla.getUI('jackaroCard.png'), type: Laya.Loader.IMAGE },
        { url: yalla.getUI('jackaro/pokerRule.png'), type: Laya.Loader.IMAGE },
        { url: yalla.getUI('jackaro/pokerRule.atlas'), type: Laya.Loader.ATLAS },
        { url: yalla.getUI('activity.png'), type: Laya.Loader.IMAGE },
        { url: yalla.getUI('curtain.png'), type: Laya.Loader.IMAGE },
        { url: yalla.getUI('duel.png'), type: Laya.Loader.IMAGE },

        { url: yalla.getUI('snake.atlas'), type: Laya.Loader.ATLAS },
        { url: yalla.getUI('snake.png'), type: Laya.Loader.IMAGE },

        { url: yalla.getUI('ylemoji.atlas'), type: Laya.Loader.ATLAS },
        { url: yalla.getUI('ylemoji.png'), type: Laya.Loader.IMAGE },

        { url: "publics/ani/safe_circle.ani", type: Laya.Loader.JSON },
        { url: "publics/ani/ani_voice.ani", type: Laya.Loader.JSON },
        { url: "publics/ani/ani_voice_domino.ani", type: Laya.Loader.JSON },
        { url: "publics/ani/ani_voice_me.ani", type: Laya.Loader.JSON },
        { url: "publics/ani/finger.ani", type: Laya.Loader.JSON },
        { url: "publics/ani/jump.ani", type: Laya.Loader.JSON },
        { url: "publics/ani/relogin.ani", type: Laya.Loader.JSON },
        // { url: "ludo/jungleMode/tigerClaw.ani", type: Laya.Loader.JSON },

        { url: yalla.getSkeletonPng('dice/dice_10000'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton('dice/dice_10000'), type: Laya.Loader.BUFFER },

        { url: yalla.getSkeletonPng('night/heiye'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton('night/heiye'), type: Laya.Loader.BUFFER },

        { url: yalla.getSkeletonPng('light/deng'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton('light/deng'), type: Laya.Loader.BUFFER },

        { url: yalla.getSkeletonPng('roadblock/luzhang1'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton('roadblock/luzhang1'), type: Laya.Loader.BUFFER },

        { url: 'game/Base_13000.png', type: Laya.Loader.IMAGE },
        { url: 'game/BG_13000.png', type: Laya.Loader.IMAGE },
        { url: 'game/Checkerboard_13000.png', type: Laya.Loader.IMAGE },
        { url: 'duel/Checkerboard_duel.png', type: Laya.Loader.IMAGE },
        { url: 'duel/Base_duel.png', type: Laya.Loader.IMAGE },

        { url: 'jackaro/Base_52000.png', type: Laya.Loader.IMAGE },
        { url: 'jackaro/BG_52000.jpg', type: Laya.Loader.IMAGE },
        { url: 'jackaro/Checkerboard_52000.png', type: Laya.Loader.IMAGE },
        { url: 'jackaro/Checkerboard_2_52000.png', type: Laya.Loader.IMAGE },
        { url: 'jackaro/bg_card_intro.png', type: Laya.Loader.IMAGE },
        { url: 'jackaro/bg_select_dot.png', type: Laya.Loader.IMAGE },
        { url: 'jackaro/bg_discard_self.png', type: Laya.Loader.IMAGE },
        { url: 'jackaro/bg_discard_all.png', type: Laya.Loader.IMAGE },
        { url: 'jackaro/bg_tips_discard.png', type: Laya.Loader.IMAGE },

        // { url: `res/test/faceframe_200008.sk`, type: Laya.Loader.BUFFER },
        // { url: `res/test/faceframe_200008.png`, type: Laya.Loader.IMAGE },
        // { url: `res/test/dice_10045/dice_100452.png`, type: Laya.Loader.IMAGE },
        //  { url: `res/test/BG_21007.jpg`, type: Laya.Loader.IMAGE },

        { url: yalla.getSkeleton("props/circleaureole/skeleton"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('props/circleaureole/skeleton'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("props/rocket/huojian"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng("props/rocket/huojian"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton('dayChampion/skeleton'), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('dayChampion/skeleton'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton('dayChampionResult/banner/skeleton'), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('dayChampionResult/banner/skeleton'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton('dayChampionResult/baodian/skeleton'), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('dayChampionResult/baodian/skeleton'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton('dayChampionVS/skeleton'), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('dayChampionVS/skeleton'), type: Laya.Loader.IMAGE },

        //优先加载jackaro 棋子选择
        { url: yalla.getSkeleton("jackaro/choose/Choose_red/Choose_red"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/choose/Choose_red/Choose_red'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/choose/Choose_blue/Choose_blue"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/choose/Choose_blue/Choose_blue'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/choose/Choose_green/Choose_green"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/choose/Choose_green/Choose_green'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/choose/Choose_yellow/Choose_yellow"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/choose/Choose_yellow/Choose_yellow'), type: Laya.Loader.IMAGE },

        { url: yalla.getSkeleton("jackaro/guiji/huang/huang"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng("jackaro/guiji/huang/huang"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/guiji/hong/hong"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng("jackaro/guiji/hong/hong"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/guiji/lan/lan"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng("jackaro/guiji/lan/lan"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/guiji/lv/lv"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng("jackaro/guiji/lv/lv"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/chizi/chizi/chizi"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng("jackaro/chizi/chizi/chizi"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/tishi/tishi"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng("jackaro/tishi/tishi"), type: Laya.Loader.IMAGE },
        //发牌流光
        { url: yalla.getSkeleton('jackaro/fapai_sweep/fapai_sweep'), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/fapai_sweep/fapai_sweep'), type: Laya.Loader.IMAGE },

        { url: 'bmpfont/number.fnt', type: Laya.Loader.XML },
        { url: 'bmpfont/roundTimeBmfont.fnt', type: Laya.Loader.XML },
        // { url: yalla.getTTF('layabox'), type: Laya.Loader.BUFFER },
    ];

    export var backLoadRes = [
        { url: yalla.getSkeletonPng('result/win'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeletonPng('result/lose'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeletonPng('fastemoji/zong'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeletonPng("loading/loading"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeletonPng("voice_ludo/skeleton"), type: Laya.Loader.IMAGE },

        { url: yalla.getSkeletonPng("vip/xunzhang"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeletonPng("royallevel/huangguan"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("vip/xunzhang"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeleton("royallevel/huangguan"), type: Laya.Loader.BUFFER },


        { url: yalla.getSkeletonPng("duel_clock/duel_clock"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton('duel_clock/duel_clock'), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng("duel_fight/duel_fight"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton('duel_fight/duel_fight'), type: Laya.Loader.BUFFER },

        { url: yalla.getSkeleton('result/win'), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeleton('result/lose'), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeleton('fastemoji/zong'), type: Laya.Loader.BUFFER },

        { url: yalla.getSkeleton("voicebtn/anniusaoguang"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeleton("endpoint/skeleton"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeleton("props/gold_dice/skeleton"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeleton("loading/loading"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeleton("voice_ludo/skeleton"), type: Laya.Loader.BUFFER },

        // { url: 'res/test/gift_1009.sk', type: Laya.Loader.BUFFER },
        // { url: 'res/test/gift_1009.png', type: Laya.Loader.IMAGE },
        // { url: 'res/test/gift_1012.sk', type: Laya.Loader.BUFFER },
        // { url: 'res/test/gift_1012.png', type: Laya.Loader.IMAGE },

        { url: 'public/BG_1ST.png', type: Laya.Loader.IMAGE },
        { url: 'public/BG_234.png', type: Laya.Loader.IMAGE },
        { url: "public/BG_words.png", type: Laya.Loader.IMAGE },
        { url: "activity/BG.png", type: Laya.Loader.IMAGE },
        { url: "activity/arrow.png", type: Laya.Loader.IMAGE },
        { url: "activity/Navigation_bottom.png", type: Laya.Loader.IMAGE },
        { url: "activity/img_rule_ar.png", type: Laya.Loader.IMAGE },
        { url: "activity/img_rule_en.png", type: Laya.Loader.IMAGE },
        { url: 'public/BG_TEAM_1ST.png', type: Laya.Loader.IMAGE },
        { url: 'public/BG_TEAM_234.png', type: Laya.Loader.IMAGE },
        { url: 'public/top-bg.png', type: Laya.Loader.IMAGE },

        { url: yalla.getSkeleton("chooseChess_snake/skeleton"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('chooseChess_snake/skeleton'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeletonPng("snake/sheqizhongdian"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("snake/sheqizhongdian"), type: Laya.Loader.BUFFER },
        { url: 'snake/BG_30000.jpg', type: Laya.Loader.IMAGE },
        { url: 'snake/Checkerboard.png', type: Laya.Loader.IMAGE },
        { url: 'snake/Checkerboard_dec.png', type: Laya.Loader.IMAGE },
        { url: 'snake/image_ludo_bg0.png', type: Laya.Loader.IMAGE },
        { url: "snake/1000_1x6_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/1000_2x7_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/1000_3x6_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/1000_3x8_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/1000_4x7_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/1000_5x6_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/1000_5x9_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/1000_6x7_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/1000_7x6_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/2000_2x6_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/2000_3x7_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/2000_4x6_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/2000_5x9_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/2000_7x7_1001.png", type: Laya.Loader.IMAGE },
        { url: "snake/2000_10x7_1001.png", type: Laya.Loader.IMAGE },

        //丛林模式资源        
        { url: 'game/furniture_jungle.png', type: Laya.Loader.IMAGE },
        { url: 'game/Base_jungle.png', type: Laya.Loader.IMAGE },
        { url: 'game/Checkerboard_jungle.png', type: Laya.Loader.IMAGE },
        { url: 'jungleNovice/jungle_net_error.png', type: Laya.Loader.IMAGE },
        { url: 'jungleNovice/jungle_result_bg.png', type: Laya.Loader.IMAGE },

        { url: 'domino/BG_21000.jpg', type: Laya.Loader.IMAGE },

        { url: yalla.getSkeleton("props/snail/woniu"), type: Laya.Loader.BUFFER },//蜗牛
        { url: yalla.getSkeletonPng("props/snail/woniu"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("props/dice/shaizi"), type: Laya.Loader.BUFFER },//骰子道具
        { url: yalla.getSkeletonPng("props/dice/shaizi"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("props/shield/dunpai"), type: Laya.Loader.BUFFER },//盾牌
        { url: yalla.getSkeletonPng("props/shield/dunpai"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("props/aureole/guangkuang"), type: Laya.Loader.BUFFER },//方形光环
        { url: yalla.getSkeletonPng("props/aureole/guangkuang"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("props/tornado/xuanfeng"), type: Laya.Loader.BUFFER },//龙卷风
        { url: yalla.getSkeletonPng("props/tornado/xuanfeng"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("props/cloud/yun"), type: Laya.Loader.BUFFER },//乌云
        { url: yalla.getSkeletonPng("props/cloud/yun"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeletonPng("props/lockdown/mabi"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("props/lockdown/mabi"), type: Laya.Loader.BUFFER },//棋子麻痹
        { url: yalla.getSkeleton("props/lightning/shandianyujing"), type: Laya.Loader.BUFFER },//闪电预警
        { url: yalla.getSkeletonPng("props/lightning/shandianyujing"), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("props/lightning/leidian"), type: Laya.Loader.BUFFER },//格子霹下来的雷电
        { url: yalla.getSkeletonPng("props/lightning/leidian"), type: Laya.Loader.IMAGE },
        { url: yalla.getUI('jungleNovice.atlas'), type: Laya.Loader.ATLAS },
        { url: yalla.getUI('jungleNovice.png'), type: Laya.Loader.IMAGE },

        //jackaro spine  动画
        { url: yalla.getSkeleton("jackaro/xipai/xipai"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/xipai/xipai'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/fly/fly_1"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/fly/fly_1'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/finish/Finish"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/finish/Finish'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/jinzhongdian/jinzhongdian"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/jinzhongdian/jinzhongdian'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/exchange/Exchange"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/exchange/Exchange'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/xing/xingxing"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/xing/xingxing'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/chizi/chiZi01/chiZi01"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/chizi/chiZi01/chiZi01'), type: Laya.Loader.IMAGE },

        { url: yalla.getSkeleton("jackaro/choose/mini/Choose_red/Choose_red"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/choose/mini/Choose_red/Choose_red'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/choose/mini/Choose_blue/Choose_blue"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/choose/mini/Choose_blue/Choose_blue'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/choose/mini/Choose_green/Choose_green"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/choose/mini/Choose_green/Choose_green'), type: Laya.Loader.IMAGE },
        { url: yalla.getSkeleton("jackaro/choose/mini/Choose_yellow/Choose_yellow"), type: Laya.Loader.BUFFER },
        { url: yalla.getSkeletonPng('jackaro/choose/mini/Choose_yellow/Choose_yellow'), type: Laya.Loader.IMAGE },
    ];
    /**
     * 快捷短语
     */
    export const fastChat = {
        emojiSkin: [
            "icon_emoji_laugh.png",
            "icon_emoji_scared.png",
            "icon_emoji_cry.png",
            "icon_emoji_love.png",
            "icon_emoji_angry.png",
            "icon_emoji_cool.png",
            "icon_emoji_like.png",
            "icon_emoji_unlike.png",
            "icon_emoji_fist.png",
            "icon_emoji_coma.png"
        ],
        ludoWords: [
            "Hello",
            "Hehehe",
            "Oops",
            "Good luck",
            "Nice move",
            "Play fast",
            "Bravo",
            "I will eat you",
            "Welcome",
            "Good game",
            "Sorry mate!",
            "Thanks"
        ],
        dominoWords: [
            "Hello",
            "Hehehe",
            "Oops",
            "Good luck",
            "Why! Why!",
            "Play fast",
            "Bravo",
            "Unlucky",
            "Bye! Bye!",
            "Good game",
            "Sorry mate!",
            "Thanks"
        ],
        snakeWords: [
            "Hello",
            "Hehehe",
            "Oops",
            "Bad luck",
            "Nice move",
            "Play fast",
            "I love ladders",
            "Welcome",
            "Bye",
            "Watch out",
            "Yeah",
            "You’ll be bitten"
        ], jackaroWords: [
            "Hello",
            "Hehehe",
            "Oops",
            "Good luck",
            "Nice move",
            "Play fast",
            "Bravo",
            "I will eat you",
            "Welcome",
            "Good game",
            "Sorry mate!",
            "Thanks"
        ]
    };

    /**
     * 快捷短语埋点事件
     */
    export const fastChat_Points = {
        ludo: [
            "message_ludo_hello",
            "message_ludo_hehehe",
            "message_ludo_ohmygod",
            "message_ludo_todayisyourday",
            "message_ludo_nicemove",
            "message_ludo_playfast",
            "message_ludo_todayismyday",
            "message_ludo_iwilleatyou",
            "message_ludo_pleasedontkill",
            "message_ludo_goodgame",
            "message_ludo_sorrymate",
            "message_ludo_thanks"
        ],
        domino: [
            "message_domino_hello",
            "message_domino_hehehe",
            "message_domino_ohmygod",
            "message_domino_todayisyourday",
            "message_domino_whywhy",
            "message_domino_whywhy",
            "message_domino_todayismyday",
            "message_domino_unlucky",
            "message_domino_byebye",
            "message_domino_goodgame",
            "message_domino_sorrymate",
            "message_domino_thanks"
        ],
        snake: [],
        jackaro: []
    }

    //非逃跑的退出，游戏房间类型对应返回去向    
    export const backHall_direction = {
        0: "public",
        1: "private",
        3: "championship",
        4: "vip",
        5: "team",
        6: "night",
        7: "teamnight",
        8: "league",
        9: "duel",
        10: "jungle"
    }


    export const nationalList = [
        {
            "id": 1,
            "areaCode": "966",
            "name": "KSA",
            "shortName": "SA",
            "flagUrl": "https://account.yalla.games/NationlityIcon/1.png"
        },
        {
            "id": 3,
            "areaCode": "213",
            "name": "Algeria",
            "shortName": "DZ",
            "flagUrl": "https://account.yalla.games/NationlityIcon/3.png"
        },
        {
            "id": 64,
            "areaCode": "61",
            "name": "Australia",
            "shortName": "AU",
            "flagUrl": "https://account.yalla.games/NationlityIcon/64.png"
        },
        {
            "id": 104,
            "areaCode": "43",
            "name": "Austria",
            "shortName": "AT",
            "flagUrl": "https://account.yalla.games/NationlityIcon/104.png"
        },
        {
            "id": 9,
            "areaCode": "973",
            "name": "Bahrain",
            "shortName": "BH",
            "flagUrl": "https://account.yalla.games/NationlityIcon/9.png"
        },
        {
            "id": 35,
            "areaCode": "880",
            "name": "Bangladesh",
            "shortName": "BD",
            "flagUrl": "https://account.yalla.games/NationlityIcon/35.jpg"
        },
        {
            "id": 32,
            "areaCode": "1",
            "name": "Canada",
            "shortName": "CA",
            "flagUrl": "https://account.yalla.games/NationlityIcon/32.png"
        },
        {
            "id": 19,
            "areaCode": "86",
            "name": "China",
            "shortName": "CN",
            "flagUrl": "https://account.yalla.games/NationlityIcon/19.png"
        },
        {
            "id": 2,
            "areaCode": "20",
            "name": "Egypt",
            "shortName": "EG",
            "flagUrl": "https://account.yalla.games/NationlityIcon/2.png"
        },
        {
            "id": 30,
            "areaCode": "251",
            "name": "Ethiopia",
            "shortName": "ET",
            "flagUrl": "https://account.yalla.games/NationlityIcon/30.png"
        },
        {
            "id": 21,
            "areaCode": "33",
            "name": "France",
            "shortName": "FR",
            "flagUrl": "https://account.yalla.games/NationlityIcon/21.png"
        },
        {
            "id": 23,
            "areaCode": "49",
            "name": "Germany",
            "shortName": "DE",
            "flagUrl": "https://account.yalla.games/NationlityIcon/23.png"
        },
        {
            "id": 17,
            "areaCode": "91",
            "name": "India",
            "shortName": "IN",
            "flagUrl": "https://account.yalla.games/NationlityIcon/17.png"
        },
        {
            "id": 18,
            "areaCode": "62",
            "name": "Indonesia",
            "shortName": "ID",
            "flagUrl": "https://account.yalla.games/NationlityIcon/18.png"
        },
        {
            "id": 8,
            "areaCode": "964",
            "name": "Iraq",
            "shortName": "IO",
            "flagUrl": "https://account.yalla.games/NationlityIcon/8.png"
        },
        {
            "id": 25,
            "areaCode": "39",
            "name": "Italy",
            "shortName": "IT",
            "flagUrl": "https://account.yalla.games/NationlityIcon/25.png"
        },
        {
            "id": 5,
            "areaCode": "962",
            "name": "Jordan",
            "shortName": "JO",
            "flagUrl": "https://account.yalla.games/NationlityIcon/5.png"
        },
        {
            "id": 7,
            "areaCode": "965",
            "name": "Kuwait",
            "shortName": "KW",
            "flagUrl": "https://account.yalla.games/NationlityIcon/7.png"
        },
        {
            "id": 13,
            "areaCode": "961",
            "name": "Lebanon",
            "shortName": "LB",
            "flagUrl": "https://account.yalla.games/NationlityIcon/13.png"
        },
        {
            "id": 27,
            "areaCode": "218",
            "name": "Libya",
            "shortName": "LY",
            "flagUrl": "https://account.yalla.games/NationlityIcon/27.png"
        },
        {
            "id": 4,
            "areaCode": "212",
            "name": "Morocco",
            "shortName": "MA",
            "flagUrl": "https://account.yalla.games/NationlityIcon/4.png"
        },
        {
            "id": 58,
            "areaCode": "977",
            "name": "Nepal",
            "shortName": "NP",
            "flagUrl": "https://account.yalla.games/NationlityIcon/nepal_flag.png"
        },
        {
            "id": 76,
            "areaCode": "31",
            "name": "Netherlands",
            "shortName": "NL",
            "flagUrl": "https://account.yalla.games/NationlityIcon/76.png"
        },
        {
            "id": 14,
            "areaCode": "968",
            "name": "Oman",
            "shortName": "OM",
            "flagUrl": "https://account.yalla.games/NationlityIcon/14.jpg"
        },
        {
            "id": 16,
            "areaCode": "92",
            "name": "Pakistan",
            "shortName": "PK",
            "flagUrl": "https://account.yalla.games/NationlityIcon/16.png"
        },
        {
            "id": 15,
            "areaCode": "970",
            "name": "Palestine",
            "shortName": "BL",
            "flagUrl": "https://account.yalla.games/NationlityIcon/15.png"
        },
        {
            "id": 38,
            "areaCode": "63",
            "name": "Philippines",
            "shortName": "PH",
            "flagUrl": "https://account.yalla.games/NationlityIcon/38.png"
        },
        {
            "id": 11,
            "areaCode": "974",
            "name": "Qatar",
            "shortName": "QA",
            "flagUrl": "https://account.yalla.games/NationlityIcon/11.png"
        },
        {
            "id": 26,
            "areaCode": "7",
            "name": "Russia",
            "shortName": "RU",
            "flagUrl": "https://account.yalla.games/NationlityIcon/26.png"
        },
        {
            "id": 24,
            "areaCode": "34",
            "name": "Spain",
            "shortName": "ES",
            "flagUrl": "https://account.yalla.games/NationlityIcon/24.png"
        },
        {
            "id": 29,
            "areaCode": "249",
            "name": "Sudan",
            "shortName": "SD",
            "flagUrl": "https://account.yalla.games/NationlityIcon/29.png"
        },
        {
            "id": 28,
            "areaCode": "216",
            "name": "Tunisia",
            "shortName": "TN",
            "flagUrl": "https://account.yalla.games/NationlityIcon/28.png"
        },
        {
            "id": 20,
            "areaCode": "90",
            "name": "Turkey",
            "shortName": "TR",
            "flagUrl": "https://account.yalla.games/NationlityIcon/20.png"
        },
        {
            "id": 6,
            "areaCode": "971",
            "name": "UAE",
            "shortName": "AE",
            "flagUrl": "https://account.yalla.games/NationlityIcon/6.png"
        },
        {
            "id": 22,
            "areaCode": "44",
            "name": "UK",
            "shortName": "UK",
            "flagUrl": "https://account.yalla.games/NationlityIcon/22.png"
        },
        {
            "id": 49,
            "areaCode": "380",
            "name": "Ukraine",
            "shortName": "UA",
            "flagUrl": "https://account.yalla.games/NationlityIcon/49.png"
        },
        {
            "id": 31,
            "areaCode": "1",
            "name": "USA",
            "shortName": "USA",
            "flagUrl": "https://account.yalla.games/NationlityIcon/31.png"
        },
        {
            "id": 12,
            "areaCode": "967",
            "name": "Yemen",
            "shortName": "YE",
            "flagUrl": "https://account.yalla.games/NationlityIcon/12.png"
        }
    ];

    // export const faceBoxAni = [110001, 110002, 110003, 120009, 121003, 121004, 121005, 121006, 121007, 121008];
}