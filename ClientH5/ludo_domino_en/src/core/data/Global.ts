module yalla {
	export class Global {
		// public static rootUrl: string = "https://account.yalla.games";
		public static gameType: number = 10019;
		public static faceId = 121036;
		// 资源
		public static domain: string = '';
		// 版本号
		// public static version = '1020201';
		//语种
		public static arb: boolean = false;
		//appid
		public static appid: string = 'ludo';
		//host（开发环境）
		public static host: string = 'ws://fat-domino.yalla.games'; //domino 开发 'ws://dev-domino.yalla.games'; //测试服 'ws://fat-domino.yalla.games'  于洋(domino) ***********  9040   德建(蛇其) **************  9021   开发服************ 9036
		// public static host: string = 'wss://dev-tyr.yalla.games'; //蛇棋
		public static port: string = '9037'//'9091';//'9037'
		public static token: string = 'asdf';
		public static gateWay: GateWayModel = {
			sign: '',
			deviceId: '',
			channelId: 0,
			version: '',
			timestamp: 0
		};//网关相关参数 gateWay:{sign:'',deviceId:'',channelId:0,version:'',timestamp:111}

		public static Account: AccountModel = {
			idx: null,
			royalty: 0,
			token: '',
			ip: '',
			url: '',
			socketUrlList: [],
			standby: '',
			rankPercentList: [],
			championshipId: 0,
			isPrivate: 0,
			banTalkData: "",
			version: "",
			voiceType: VoiceType.Agora,
			league: {
				watch: false,
				round: 1,
				groupNo: 0
			},
			leagueWatch: false, //是否联赛观战
			propImgUrl: "",//道具地址（根据地址是否为空，决定显示与否）
			connectType: 0,
			sameSkinBuySwitch: 0
		};			//角色账户信息
		public static heart_interval = 5000;			//心跳包间隔时间
		public static loseNet_interval = 6000;			//断线6s
		public static reconnect_interval = 10000;		//如果通知原生连socket10s没返回则重新reconnect
		public static MAX_CHARS = 200;					//聊天得最大字符

		public static ProfileInfo: ProfileInfo = {
			isDebug: false,
			hasHair: false
		};			//游戏信息
		public static game_state = '';
		public static isFouce: boolean = true;			//获得焦点 true 失去焦点 false
		public static isconnect: boolean = true;

		public static IsGameOver: boolean = false;		//游戏是否结束
		public static onlineMode: boolean = false;		//游戏是否结束

		public static tipContent: String = '';			//飘字提示内容
		public static ProtocolMsgIndex: number = 0;		//当前的协议索引
		public static ProtoEnterRoomMsgIndex: number = 0; //断线重连后，记录当前最新的消息索引
		public static onLoseNextTime: number = 0;      	//socket断开时间

		public static reconnectTimes: number = 0;		//断线重连次数
		public static LoadingChampionState: number = 0;  //加载锦标赛状态 1 加载完成2
		public static HallNetState: number = 0;			//-1断网   1连接成功
		public static ChatMaxLen: number = 100;			//最大聊天数
		public static ChatIndex: number = 0;			//聊天消息累计索引
		public static ChatMsgList: Array<any> = [];		//聊天信息 [{"AccountId":1,"MsgContent":"123","IsReport":1,"Type":1}] IsReport:1被举报的聊天 0:非举报聊天 Type 1:文字 2:URL
		public static ChatLastMsg: string = "";			//普通游戏聊天面板，保存上一条自己发送的内容
		public static nativeIputMsg: string = ""; 		//观战 上次输入的聊天内容
		/**是否需要更新zego token */
		public static IsUpdateZegoToken: boolean = false;
		public static testGiftId: number = 1017;

		public static backStatusHash: any = null;	//1点击android返回键有效，trustShip interactiveGift chat roundResult fastChat 非托管 非打开聊天面板 非打开互动表情 非小局结算弹窗 时，支持返回键点击弹出退出游戏弹窗
		public static onAccountLoginMsg: any = null;
		public static dominoNew: boolean = true;
		public static googleInfo: GooglePlayInfo = {	//返回google信息
			isCanDownload: false
		}
		public static DomainTypeInfo: any = null;
		/** 是否为低性能设备 */
		public static isLowPerformanceDevice: boolean = false;
	}
}