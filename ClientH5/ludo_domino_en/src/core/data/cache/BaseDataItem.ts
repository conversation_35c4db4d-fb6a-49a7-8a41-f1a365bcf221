module yalla.data {
	/**数据对象基类*/
	export class BaseDataItem extends Laya.EventDispatcher {

		constructor(d: Object=null) {
			super();
			if (!d)return;
			for (const key in d) {
				this.setPropRead(key, d);
			}
		}

		setPropRead(key: string, d: Object): void {
			/**
			 * value:d[key]
			 * writable:true		如果为false，属性的值就不能被重写,只能为只读了
			 * enumerable:false		是否能在for...in循环中遍历出来或在Object.keys中列举出来。
			 * configurable:true	总开关，一旦为false，就不能再设置他的（value，writable，configurable）
			 */
			Object.defineProperty(this, key, { value: d[key], writable: true, enumerable: false, configurable: true });
		}

		public update(d:Object):void {
			if(!d)return;
			for(const key in d) {
				if (key in this) this[key] = d[key];
				else this.setPropRead(key,d);
			}
		}

		public dispose():void{
			
		}
	}
}