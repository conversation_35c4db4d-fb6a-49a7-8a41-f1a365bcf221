module yalla.data {
	/**
     * 动态数据类
     */
	export class DataItem extends BaseDataItem {

		constructor(d:Object=null) {
			super(d);
		}

		public updataByMode(d:Object,p:Array<string>=[]): void {
			if(!d)return;
			for(const key in d) {
				if (p.length == 0 || p.indexOf(key) == -1) continue;
				switch(d['Mode']) {
					case 'Sub':
						if (key in this) this[key] -= d[key];	
						break;
					case 'Add':
						if (key in this) this[key] += d[key];
						break;
				}
			}
		}
	}
}