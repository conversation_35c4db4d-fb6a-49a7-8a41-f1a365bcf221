module yalla {
    import Handler = Laya.Handler;
    import EventDispatcher = Laya.EventDispatcher;

    export class Client extends EventDispatcher {
        static _instance: Client;

        public _gateWay: GateWay;
        private _preparedHandler: Handler;

        public msgHash: Object = null;
        public msgIndex: number = 0;
        public firstLogin: boolean = true;
        private _heartTime: number = 0;
        private _heartIntervalTime: number = 5000;//10s

        static get instance(): Client {
            return Client._instance || (Client._instance = new Client());
        }

        public init(preparedHandler: Handler): void {
            this._heartIntervalTime = yalla.Global.heart_interval;
            this.initEvent();
            this._preparedHandler = preparedHandler;
            yalla.Debug.log('======Client.init=====' + (!this._gateWay));
            if (!this._gateWay) {
                this._gateWay = new GateWay(ludo.Proto.instance.protoRootNew);
            }
            this._preparedHandler && this._preparedHandler.run();

            //TODO::这里不需要游戏内执行网关登入，由原生执行
            // let authParams: any = {
            //     appId: Global.appid,
            //     userId: Global.Account.idx
            // };
            // if (Global.gateWay) {
            //     authParams.sign = Global.gateWay.sign;
            //     authParams.deviceId = Global.gateWay.deviceId;
            //     authParams.channelId = Global.gateWay.channelId;
            //     authParams.version = Global.gateWay.version;
            //     authParams.timestamp = Global.gateWay.timestamp;
            // }
            // yalla.Debug.log('=====Client.init AuthRequest 111=======');
            // this.sendBaseMsg(this._gateWay.requestEnter('AuthRequest', authParams));

            this.sendHeart();
            this.onheart();

        }

        private initEvent() {
            yalla.NativeWebSocket.instance.on(Laya.Event.OPEN, this, this.onOpen, null);
            yalla.NativeWebSocket.instance.on(Laya.Event.MESSAGE, this, this.onMessage, null);
            yalla.NativeWebSocket.instance.on(Laya.Event.CLOSE, this, this.onClose, null);
            yalla.NativeWebSocket.instance.on(Laya.Event.ERROR, this, this.onError, null);

            yalla.NativeWebSocket.instance.on('Message_onNetMsg', this, this.onMessage_onNetMsg, null);
            yalla.NativeWebSocket.instance.on('Message_onNetResponseMsg', this, this.onMessage_netResponseMsg, null);
        }

        /**业务逻辑发送 */
        public sendMsg(reqName, gameCmd, bodyData?, extraParams?, businessOrder = 1): boolean {
            if (!this._gateWay) return;
            if (!yalla.util.IsWinConch() && gameCmd) {
                yalla.Debug.log(`[${yalla.getTimeHMS()}][Client send:][${reqName}_${gameCmd}]`);
                yalla.Debug.log(bodyData);
                yalla.Debug.log(extraParams);
            }
            let binary = this._gateWay.requestGame(reqName, bodyData, extraParams, businessOrder);
            this.sendBaseMsg(binary);
            return true;
        }
        /**断流 */
        public sendCleanStream(gameCmd): boolean {
            if (!this._gateWay) return;
            if (!yalla.util.IsWinConch() && gameCmd) {
                yalla.Debug.log(`[${yalla.getTimeHMS()}][Client send:][clean_stream_${gameCmd}]`);
            }
            let binary = this._gateWay.requestCleanStream(gameCmd);
            this.sendBaseMsg(binary);
            return true;
        }

        /**
         * @param name 消息名称
         * @param cmd command
         * @param obj 参数
         */
        public sendBaseMsg(binary: any): boolean {
            yalla.NativeWebSocket.instance.sendNetMsg(binary);
            return true;
        }


        /**socket 连接以后，向网关发送用户信息 这里的token是加密过的 */
        public onOpen(e: any = null): void {
            yalla.Debug.log('===socket 连接成功====', JSON.stringify(e));
            // if (this._socket) {
            //     this._socket.gateWay.requestEnter(Global.appid, Global.Account.idx, Global.Account.encryptToken);
            // }
        }

        /**
         * 协议返回 事件抛出 { gateWayCmd, gameCmd, code, res }
         * @param msg 
         */
        private onMessage(msg: any): void {
            // yalla.Debug.logMore("Client onMessage: this._gateWay" + this._gateWay);
            if (!this._gateWay) {
                return;
            }
            var result = this._gateWay.responseMsgHandle(msg);
            // yalla.Debug.logMore("Client onMessage: result" + result + "msg:" + msg);
            if (!result || !msg) return;

            var gateWayCmd = result.gateWayCmd;
            var businessOrder = result.businessOrder;
            var code = result.code;
            // yalla.Debug.log(gateWayCmd + '==Client.onMessage==' + businessOrder + '-code:' + code);
            // yalla.Debug.log(msg);
            // yalla.Debug.log(result);
            if (!yalla.util.IsWinConch() && gateWayCmd < 4) {
                yalla.Debug.log(`[${yalla.getTimeHMS()} ][response][gateWayCmd-gameCmd:${gateWayCmd}:${businessOrder}-code:${code}]`);
            }
            if (code > 0) {
                yalla.Debug.log(result);
                this.event('Game_GateWay_Error', [code]);
                return;
            }
            switch (gateWayCmd) {
                case 1://心跳响应
                    // yalla.Debug.log('===心跳响应===');
                    this._heartTime = yalla.System.getNowTime();
                    break;
                // case 2://网关登入成功，发起游戏内票据登录 // TODO::原生执行登入后回调onOpen
                //     this._preparedHandler && this._preparedHandler.run();
                // break;
                case 4://业务逻辑 { gateWayCmd, businessOrder, code, res }; 业务指令原先默认是1，现在扩展14-19
                    var code = result.code;
                    var nUint8Array = result.potobufSetByteArray;
                    // this.event('Game_' + gateWayCmd + '_' + result.businessOrder, [code, nUint8Array,result.command]);
                    this.event('Game_' + gateWayCmd, [code, nUint8Array, result.command, businessOrder]);
                    break;
                case 6:
                    yalla.Debug.log('====通知网关断流====');
                    break;
            }
        }

        protected onClose(e: any = null): void {
            yalla.Debug.log(this.firstLogin + ':firstLogin--Client.onClose--isconnect:' + yalla.Global.isconnect);
            if (yalla.Global.isconnect) {

                yalla.Global.isconnect = false;
                Laya.timer.clear(this, this.checkConnect);
                Laya.timer.clear(this, this.onheart);
                yalla.Global.onLoseNextTime = yalla.System.getNowTime();
            }

            yalla.Global.reconnectTimes++;
            this.event('Game_SocketClose');
        }

        protected onError(e: any): void {
            this.onClose(e);
            yalla.common.connect.ReconnectControl.instance.connect(yalla.data.snake.UserService.instance.ImmediateConnect);
        }

        public decodeMsg(bytearr: any, name: string): any {
            // try {
            let msg: any = {};
            let root = ludo.Proto.instance.protoRootNew;
            if (root) {
                let loginMessage = root.lookupType(name);
                msg = loginMessage.decode(bytearr);

                if (msg && msg.msgindex) {
                    if (!this.msgHash) this.msgHash = {};
                    this.msgHash[msg.msgindex] = msg;
                    this.msgIndex = msg.msgindex;
                }
                if (!yalla.util.IsWinConch()) yalla.Debug.log(msg);
            }
            return msg;
            // } catch (error) {
            //     this._socket.close();
            // }
        }

        /**
         * 从后台回来重新发送心跳
         */
        public resumeHeart(): void {
            if (yalla.Global.onlineMode) {
                this.sendHeart();
                if (this.firstLogin) {
                    Laya.timer.clear(this, this.checkConnect);
                    Laya.timer.once(5000, this, this.checkConnect);
                }
            }
        }

        /**
         * 心跳包
         */
        private sendHeart(): void {
            Laya.timer.clear(this, this.onheart);
            Laya.timer.clear(this, this.checkConnect);

            this._heartTime = yalla.System.getNowTime();
            Laya.timer.loop(this._heartIntervalTime, this, this.onheart, null, true, true);
        }
        private onheart(): void {
            var subTime = yalla.System.getNowTime() - this._heartTime;
            yalla.Debug.log((subTime >= this._heartIntervalTime + 3000) + '-' + this._heartTime + '===Client.onheart-closeSocket====subTime:' + subTime);
            if (this._heartTime > 0 && subTime >= this._heartIntervalTime + 3000) {
                this.closeSocket();
                this.onClose();

                yalla.common.connect.ReconnectControl.instance.connect(yalla.data.snake.UserService.instance.ImmediateConnect);
                return;
            }
            if (this._gateWay) {
                var binary = this._gateWay.requestPing();
                // yalla.Debug.log('======发送心跳======');
                // yalla.Debug.log(binary);
                this.sendBaseMsg(binary);
            }
        }

        public checkConnect(): void {//TODO::4s内还未连上则断线重连
            if (!yalla.Global.isconnect) {
                yalla.Debug.log('====Client.checkConnect-closeSocket====firstLogin:' + this.firstLogin);
                this.closeSocket();
                this.onClose();
                this.firstLogin = false;
                yalla.common.connect.ReconnectControl.instance.connect(yalla.data.snake.UserService.instance.ImmediateConnect);
            }
        }

        public resetMsg(): void {
            this.msgIndex = 0;
            this.msgHash = null;
        }

        public closeSocket() {
            //TODO::这里之所以加判定，因socket断多次，服务端检测只登录一次，导致后续消息不发送
            if (yalla.Global.isconnect || this.firstLogin) {
                yalla.Debug.log('===Client.closeSocket==sendNetClose==' + this.firstLogin);
                yalla.NativeWebSocket.instance.sendNetClose();
            }
        }

        //=============================联赛观战============================
        // private arr = [];
        public onMessage_onNetMsg(res: any): any {
            // this.arr.push(res);
            // Laya.LocalStorage.setJSON("watchData", this.arr)

            if (!this._gateWay) {
                return;
            }
            var result = this._gateWay.onMessage_onNetMsg(res);
            if (!result || !res) return;

            var cmd = result.cmd;
            var content = result.potobufSetByteArray;
            //走老的协议
            this.event('Message_onNetMsg', [content]);
        }

        public onMessage_netResponseMsg(res: any): any {
            if (!this._gateWay) {
                return;
            }
            var result = this._gateWay.onMessage_netResponseMsg(res);
            if (!result || !res) return;

            var cmd = result.cmd;
            var content = result.potobufSetByteArray;
            this.event('Message_onNetResponseMsg', [cmd, content]);
        }

        public decodeMsg_watch(bt: any, name: string): any {
            if (!this._gateWay) return;

            var result = this._gateWay.onMessage_watch_new(bt);
            yalla.Debug.log(result);
            yalla.Debug.log('=====client===decodeMsg_watch=========');
            if (!result) return;
            var bytearr = result[1];
            let msg: any = {};

            var root = ludo.Proto.instance.root_domino_new;
            if (yalla.Global.Account.leagueWatch) root = ludo.Proto.instance.root_watch;

            if (root) {
                let loginMessage = root.lookupType(name);
                msg = loginMessage.decode(bytearr);

                if (msg && msg.msgindex) {
                    if (!this.msgHash) this.msgHash = {};
                    this.msgHash[msg.msgindex] = msg;
                    this.msgIndex = msg.msgindex;
                }
            }
            return msg;
        }

        //================================================================

        public removeEvent(): void {
            yalla.NativeWebSocket.instance.off(Laya.Event.OPEN, this, this.onOpen, null);
            yalla.NativeWebSocket.instance.off(Laya.Event.MESSAGE, this, this.onMessage, null);
            yalla.NativeWebSocket.instance.off(Laya.Event.CLOSE, this, this.onClose, null);
            yalla.NativeWebSocket.instance.off(Laya.Event.ERROR, this, this.onError, null);

            yalla.NativeWebSocket.instance.off('Message_onNetMsg', this, this.onMessage_onNetMsg, null);
            yalla.NativeWebSocket.instance.off('Message_onNetResponseMsg', this, this.onMessage_netResponseMsg, null);
        }

        public clear(): void {
            yalla.Debug.log('===Client.clear===');
            if (this._gateWay) this._gateWay.clear();
            this.closeSocket();
            this.removeEvent();
            yalla.Global.isconnect = false;
            Laya.timer.clear(this, this.onheart);
            Laya.timer.clear(this, this.checkConnect);
            Laya.timer.clearAll(this);
            this._gateWay = null;
        }
    }
}