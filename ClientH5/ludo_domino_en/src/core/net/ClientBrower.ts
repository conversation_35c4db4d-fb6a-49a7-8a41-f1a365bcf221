module yalla {
    import Handler = Laya.Handler;
    import EventDispatcher = Laya.EventDispatcher;

    export class <PERSON>lient<PERSON>rower extends EventDispatcher {
        static _instance: ClientBrower;

        private _socket: Laya.Socket;
        public _gateWay: GateWay;
        private _preparedHandler: Handler;

        private _heartTime: number = 0;
        public msgHash: Object = null;
        public msgIndex: number = 0;
        public firstLogin: boolean = true;

        static get instance(): ClientBrower {
            return ClientBrower._instance || (ClientBrower._instance = new ClientBrower());
        }

        public init(preparedHandler: Handler): void {
            this._preparedHandler = preparedHandler;
            // var url = `${yalla.Global.Account.host}:${yalla.Global.Account.port}`;
            var url = `${yalla.Global.Account.host}?appId=ludo&userId=${yalla.Global.Account.idx}`;
            console.log('=====game connect socket =====url:' + url);
            if (this._socket) this.closeSocket();
            if (!this._socket) {
                this._socket = new Laya.Socket();
                this.initEvent();
            }
            this._socket.connectByUrl(url);

            if (!this._gateWay) {
                console.log("===新的proto====");
                console.log(ludo.Proto.instance.protoRootNew);
                this._gateWay = new GateWay(ludo.Proto.instance.protoRootNew);
            }
        }

        private initEvent() {
            this._socket.on(Laya.Event.OPEN, this, this.onOpen, null);
            this._socket.on(Laya.Event.MESSAGE, this, this.onMessage, null);
            this._socket.once(Laya.Event.CLOSE, this, this.onClose, null);
            this._socket.once(Laya.Event.ERROR, this, this.onError, null);
            // yalla.NativeWebSocket.instance.on(Laya.Event.OPEN, this, this.onOpen, null);
            // yalla.NativeWebSocket.instance.on(Laya.Event.MESSAGE, this, this.onMessage, null);
            // yalla.NativeWebSocket.instance.on(Laya.Event.CLOSE, this, this.onClose, null);
            // yalla.NativeWebSocket.instance.on(Laya.Event.ERROR, this, this.onError, null);
        }

        /**业务逻辑发送 */
        public sendMsg(reqName, gameCmd, bodyData?, extraParams?, businessOrder = 1): boolean {
            // if (bodyData) {
            //     bodyData.roomId = Global.Account.roomid;
            //     bodyData.command = gameCmd;
            // }
            if (!yalla.util.IsWinConch() && gameCmd) {
                yalla.Debug.log(`[${yalla.getTimeHMS()}][ClientBrower send:][${reqName}_${gameCmd}]`);
                // yalla.Debug.log(bodyData);
                // yalla.Debug.log(extraParams);
            }
            let binary = this._gateWay.requestGame(reqName, bodyData, extraParams, businessOrder);
            this.sendBaseMsg(binary);
            return true;
        }
        /**断流 */
        public sendCleanStream(gameCmd): boolean {
            if (!this._gateWay) return;
            if (!yalla.util.IsWinConch() && gameCmd) {
                yalla.Debug.log(`[${yalla.getTimeHMS()}][ClientBrower send:][clean_stream_${gameCmd}]`);
            }
            let binary = this._gateWay.requestCleanStream();
            this.sendBaseMsg(binary);
            return true;
        }

        /**
         * @param name 消息名称
         * @param cmd command
         * @param obj 参数
         */
        public sendBaseMsg(binary: any): boolean {
            this._socket.send(binary);
            return true;
            // let loginMessage = this._root.lookupType(name);
            // let message = loginMessage.create(obj);
            // let byteArray = loginMessage.encode(message).finish();
            // if (byteArray && this.byte) {
            // 	this.byte.clear();
            // 	this.byte.writeInt16(byteArray.byteLength);
            // 	this.byte.writeByte(0);
            // 	this.byte.writeByte(cmd);
            // 	this.byte.writeArrayBuffer(byteArray);

            // 	var arr: Array<number>;
            // 	if (yalla.Native.instance.deviceType == DeviceType.IOS) {
            // 		arr = Array.prototype.slice.call(new Uint8Array(this.byte.buffer));
            // 	} else {
            // 		arr = Array.prototype.slice.call(new Int8Array(this.byte.buffer));
            // 	}
            // 	yalla.NativeWebSocket.instance.sendNetMsg(arr);
            // }
        }


        /**socket 连接以后，向网关发送用户信息 这里的token是加密过的 */
        public onOpen(e: any = null): void {

            console.log('===socket 连接成功 0====');
            let authParams: any = {
                appId: Global.appid,
                userId: Global.Account.idx
            };
            if (Global.gateWay) {
                authParams.sign = Global.gateWay.sign;
                authParams.deviceId = Global.gateWay.deviceId;
                authParams.channelId = Global.gateWay.channelId;
                authParams.version = Global.gateWay.version;
                authParams.timestamp = Global.gateWay.timestamp;
            }

            this.sendBaseMsg(this._gateWay.requestEnter('AuthRequest', authParams));
            // this.sendBaseMsg(this._gateWay.requestEnter('AuthRequest', { appId: Global.appid, userId: Global.Account.idx }));
            this.sendHeart();
            this.onheart();//计时器从0开始时先执行一次
            this._preparedHandler && this._preparedHandler.run();
            // NativeWebSocket.instance.event(Laya.Event.OPEN);
        }

        /**
         * 协议返回 事件抛出 { gateWayCmd, gameCmd, code, res }
         * @param msg 
         */
        private onMessage(msg: any): void {
            var result = this._gateWay.responseMsgBrowserHandle(msg);
            if (!result || !msg) return;
            var gateWayCmd = result.gateWayCmd;
            var businessOrder = result.businessOrder;
            var code = result.code;
            // console.log(gateWayCmd + '==Client.onMessage==' + gateWayCmd + '-code:' + code);
            // console.log(msg);
            // console.log(result);
            if (!yalla.util.IsWinConch() && gateWayCmd < 4) {
                // console.log(`[${yalla.getTimeHMS()} ][response][gateWayCmd-gameCmd:${gateWayCmd}:${businessOrder}-code:${code}]`);
            }
            if (code > 0) {
                console.log(result);
                this.event('Game_GateWay_Error', [code]);
                return;
            }
            switch (gateWayCmd) {
                case 1://心跳响应
                    this._heartTime = yalla.System.getNowTime();
                    break;
                // case 2://网关登入成功，发起游戏内票据登录
                // this._preparedHandler && this._preparedHandler.run();
                // break;    
                case 4://业务逻辑 { gateWayCmd, businessOrder, code, res }; 业务指令原先默认是1，现在扩展14-19
                    var code = result.code;
                    var nUint8Array = result.potobufSetByteArray;
                    // this.event('Game_' + gateWayCmd + '_' +‘ businessOrder, [code, nUint8Array,result.command]);
                    // console.log('ClientBrower.onMessage ======业务指令===esult.businessOrder=', businessOrder, 'result.command=', result.command, 'code=', code);
                    this.event('Game_' + gateWayCmd, [code, nUint8Array, result.command, businessOrder]);
                    break;
            }
        }

        protected onClose(e: any): void {
            console.log(yalla.Global.isconnect, '====client.onClose====', e);
            if (yalla.Global.isconnect) {
                // if (yalla.Global.isconnect) {
                // yalla.data.UserService.instance.recordPointQuit(yalla.data.ExitType.SOCKETFAILED_QUIT);
                // //TODO::原先之所以加下面这个逻辑，短时间断线后不发起重连，表现更友好
                // if (yalla.data.RoomService.instance.msgQueueList.length < 1) {
                //     yalla.data.RoomService.instance.addMsgInManunal();
                // }
                // }
                yalla.Global.isconnect = false;
                Laya.timer.clear(this, this.checkConnect);
                Laya.timer.clear(this, this.onheart);
                yalla.Global.onLoseNextTime = yalla.System.getNowTime();
                yalla.common.connect.ReconnectControl.instance.connect(yalla.data.snake.UserService.instance.ImmediateConnect);
            }

            yalla.Global.reconnectTimes++;
        }

        protected onError(e: any): void {
            console.log('====client.onError====', e);
            this.onClose(e);
        }

        public decodeMsg(bytearr: any, name: string): any {
            // try {
            let msg: any = {};
            let root = ludo.Proto.instance.protoRootNew;
            if (root) {
                try {
                    let loginMessage = root.lookupType(name);
                    // console.log(name, '=====client.decodeMsg=====', loginMessage);
                    // console.log(bytearr);
                    msg = loginMessage.decode(bytearr);

                    if (msg && msg.msgindex) {
                        if (!this.msgHash) this.msgHash = {};
                        this.msgHash[msg.msgindex] = msg;
                        this.msgIndex = msg.msgindex;
                    }
                    // if (!yalla.util.IsWinConch()) yalla.Debug.log(msg);
                } catch (error) {
                    // console.warn('===decodeMsg 错误===', error, "name:", name);
                }
                return msg;
                // } catch (error) {
                //     this._socket.close();
                // }
            }
        }

        /**
         * 从后台回来重新发送心跳
         */
        public resumeHeart(): void {
            this.sendHeart();
            if (this.firstLogin) {
                Laya.timer.clear(this, this.checkConnect);
                Laya.timer.once(4000, this, this.checkConnect);
            }
        }

        /**
         * 心跳包
         */
        private sendHeart(): void {
            Laya.timer.clear(this, this.onheart);
            Laya.timer.clear(this, this.checkConnect);

            this._heartTime = yalla.System.getNowTime();
            Laya.timer.loop(yalla.Global.heart_interval, this, this.onheart, null, true, true);
        }
        private onheart(): void {
            // console.log("====发送心跳===");
            // var subTime = yalla.System.getNowTime() - this._heartTime;
            // if (this._heartTime > 0 && subTime >= yalla.Global.heart_interval + 3000) {
            //     this.closeSocket();
            //     this.onError(true);
            //     return;
            // }
            this.sendBaseMsg(this._gateWay.requestPing());
        }

        public checkConnect(): void {//TODO::4s内还未连上则断线重连
            if (!yalla.Global.isconnect) {
                this.firstLogin = false;
                console.log('===closeSocket 0===');
                this.closeSocket();
                this.onError(true);
            }
        }

        public resetMsg(): void {
            this.msgIndex = 0;
            this.msgHash = null;
        }

        public closeSocket() {
            console.log('===closeSocket===');
            if (this._socket) {
                this._socket.close();
            }
            yalla.NativeWebSocket.instance.sendNetClose();
        }

        private removeEvent(): void {
            yalla.NativeWebSocket.instance.off(Laya.Event.OPEN, this, this.onOpen, null);
            yalla.NativeWebSocket.instance.off(Laya.Event.MESSAGE, this, this.onMessage, null);
            yalla.NativeWebSocket.instance.off(Laya.Event.CLOSE, this, this.onClose, null);
            yalla.NativeWebSocket.instance.off(Laya.Event.ERROR, this, this.onError, null);
        }

        public clear(): void {
            console.log("===测试socket 000=====");
            // console.trace();
            this.closeSocket();
            yalla.Global.isconnect = false;
            Laya.timer.clear(this, this.onheart);
            Laya.timer.clear(this, this.checkConnect);
            Laya.timer.clearAll(this);
        }
    }
}