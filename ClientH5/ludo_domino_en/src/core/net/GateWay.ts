module yalla {
    export enum GateWayCmd { ping = 0, pong, enter, exit, message, envelop, clean_stream };
    /**
     *  msgIndex: 0x1A,     // 统一为0x1A
        len: 0,             //下列字段的总长度
        editionIndex: 1,    //版本
        mainCmd: 0,         //类型（0：ping、1：pong、2：enter(登入)、3：exit(登出)、4：message(业务消息)）、5：信封envolop、6：clean_stream 清理stream
        gameCmd: 0,          //指令
        code:  0,           // 响应码
        msg: null,          //业务数据
    蛇棋业务指令码是 1
     */
    export class GateWay {
        private byte: Laya.Byte;
        private _root;
        //--------    请求------------
        public msgIndex = 0x1A; // 统一为0x1A
        // len: 0, //下列字段的总长度
        // headLen: 0, //请求帧的头长度(版本、类型、指令、扩展参数的长度)
        public editionIndex = 1; //版本
        // mainCmd: 0,//类型（0：ping、1：pong、2：enter(登入)、3：exit(登出)、4：message(业务消息)）
        // subCmd: 0, //指令
        //--------------------- 以上为head----------------------
        public extendIndex = null; // 扩展参数，建议为kv集合 1个字节
        // msg: null,//业务数据

        constructor(root) {
            this.byte = new Laya.Byte();
            this._root = root;
        }

        public createRequest(gateWayCmd, businessOrder = 0, reqName?, bodyData?, extraParams?) {
            // console.log("==发起请求===gateWayCmd："+gateWayCmd+" businessOrder:"+businessOrder+" reqName:"+reqName);
            let buffer, extraBuffer;
            let gateWayCmdInt = parseInt(gateWayCmd);
            let len = 10 + 5;// 数据包总长

            let extraLen = 0;
            if (extraParams) {
                extraBuffer = this.encodeMsg('TyrOption', extraParams);
                extraLen = extraBuffer.byteLength;
                len += extraLen;
            } else {
                // console.warn("==111111111111  createRequest===extraParams:为空");
            }
            // 整合数据包头 信息
            if (gateWayCmdInt >= 2) {
                if (bodyData) {
                    buffer = this.encodeMsg(reqName, bodyData)
                    len += buffer.byteLength;  // 15 个字节长度的 head  1 是扩展参数
                }
            }

            if (this.byte) {
                this.byte.clear();

                let byte = this.byte;
                byte.length = len;

                byte.pos = 0;
                byte.writeUint8(this.msgIndex);
                byte.pos = 4;
                byte.writeUint32(len - 5);
                byte.pos = 8;
                byte.writeUint32(6 + extraLen);

                byte.pos = 9;
                byte.writeUint8(this.editionIndex);
                byte.pos = 10;
                byte.writeUint8(gateWayCmdInt);
                byte.pos = 14;
                byte.writeUint32(businessOrder);

                var sendByteArray;
                if (yalla.Native.instance.deviceType == DeviceType.Android) {
                    sendByteArray = new Int8Array(byte.buffer);
                } else {
                    sendByteArray = new Uint8Array(byte.buffer);
                }
            }

            let position = 15;
            if (extraBuffer) {
                this.addToByteArray(sendByteArray, extraBuffer, position);
                position += extraBuffer.byteLength;
            }
            if (buffer) {
                this.addToByteArray(sendByteArray, buffer, position);
            }
            // yalla.Debug.log('========GateWay.createRequest========');
            // yalla.Debug.log(buffer);
            // yalla.Debug.log(extraBuffer);

            if (yalla.util.IsBrowser()) {
                return sendByteArray;
            }
            var arr: Array<number> = Array.prototype.slice.call(sendByteArray);
            return arr;
        }
        private addToByteArray(sendByteArray, buffer, position) {
            if (buffer) {
                let msgByteArray;
                if (yalla.Native.instance.deviceType == DeviceType.Android) {
                    msgByteArray = new Int8Array(buffer)
                } else {
                    msgByteArray = new Uint8Array(buffer)
                }
                for (let index = 0; index < msgByteArray.byteLength; index++) { // head + msg 合并
                    sendByteArray[position] = msgByteArray[index];
                    position++;
                }
            }
        }

        responseMsgHandle(buffer) {
            if (this.byte) {
                this.byte.clear();
                if (buffer) this.byte.writeArrayBuffer(new Uint8Array(buffer));
                if (this.byte.length < 15) return;

                this.byte.pos = 6;
                let gateWayCmd = this.byte.readByte();

                this.byte.pos = 10;
                let businessOrder = this.byte.getUint8();

                this.byte.pos = 14;
                let code = this.byte.readByte();// 响应码
                var potobufSetByteArray;
                let command = 0;
                if (gateWayCmd > 3) {
                    if (this.byte.length <= 15) return; //TODO::服务端双向流可能返回空
                    this.byte.pos = 16;
                    command = this.byte.readByte();
                    potobufSetByteArray = this.byte.getUint8Array(15, this.byte.length - 15);
                }
                return { gateWayCmd, businessOrder, command, code, potobufSetByteArray };
            }
        }

        responseMsgBrowserHandle(buffer) {
            if (this.byte) {
                this.byte.clear();
                // console.log(buffer)
                if (buffer) this.byte.writeArrayBuffer(new Uint8Array(buffer));
                // console.log(this.byte.getUint8Array(10, 1), "------------");
                if (this.byte.length < 15) return;

                this.byte.pos = 6;
                let gateWayCmd = this.byte.readByte();

                this.byte.pos = 10;
                var businessOrder = this.byte.getUint8();

                this.byte.pos = 14;
                let code = this.byte.readByte();// 响应码
                var potobufSetByteArray;
                let command = 0;
                if (gateWayCmd > 3) {
                    if (this.byte.length <= 15) return;
                    this.byte.pos = 16;
                    command = this.byte.readByte();
                    potobufSetByteArray = this.byte.getUint8Array(15, this.byte.length - 15);
                }

                return { gateWayCmd, businessOrder, command, code, potobufSetByteArray };
            }
        }

        /**发起登入 */
        public requestEnter(reqName, identity, token?) {
            return this.createRequest(GateWayCmd.enter, 2, reqName, identity);
        }
        /**发起业务逻辑 */
        public requestGame(reqName, bodyData?, extraParams?, businessOrder = 1) {
            return this.createRequest(GateWayCmd.message, businessOrder, reqName, bodyData, extraParams);
        }
        /**发起登出 */
        public requestExit() {
            return this.createRequest(GateWayCmd.exit);
        }
        /**发起心跳 */
        public requestPing() {
            return this.createRequest(GateWayCmd.ping);
        }
        /**清理stream 1是蛇棋 */
        public requestCleanStream(gameCmd = 1) {
            return this.createRequest(GateWayCmd.clean_stream, gameCmd);
        }

        public encodeMsg(name, msg) {//序列化消息
            // var type = this._root.lookupType[name];
            // var request = type.create(msg);
            var type = this._root.lookupType(name);
            // console.log(type+'===encodeMsg 0==');
            var request = type.create(msg);
            // console.log(request,'===encodeMsg 1==',msg); 
            var binary = type.encode(request).finish();
            // console.log(binary,'===encodeMsg 2==');
            return binary;
        }

        public decodeMsg(name, binary) {//反序列化消息
            var type = this._root.lookupType[name];
            var msg = type.decode(binary);
            return msg;
        }

        /**
         * @param name 消息名称
         * @param cmd command
         * @param obj 参数
         */
        public sendMsg(binary: Uint8Array) {

        }

        //=======================联赛观战==========================
        // private arr = [];
        public onMessage_onNetMsg(res: any): any {
            // this.arr.push(res);
            // Laya.LocalStorage.setJSON("watchData", this.arr)

            if (this.byte) {
                let cmd = -10000;
                this.byte.clear();
                if (res) this.byte.writeArrayBuffer(new Uint8Array(res));
                var potobufSetByteArray = this.byte.getUint8Array(0, this.byte.length);
                return { cmd, potobufSetByteArray };
            }
        }
        public onMessage_netResponseMsg(res: any): any {
            if (this.byte) {
                this.byte.clear();
                if (res) this.byte.writeArrayBuffer(new Uint8Array(res));
                if (this.byte.length < 15) return;

                this.byte.pos = 6;
                let gateWayCmd = this.byte.readByte();

                this.byte.pos = 10;
                var cmd = this.byte.readByte();

                this.byte.pos = 14;
                let code = this.byte.readByte();// 响应码

                yalla.Debug.log(code + '-' + gateWayCmd + '===新==gateWay.onMessage_netResponseMsg=====cmd==' + cmd);
                var potobufSetByteArray = this.byte.getUint8Array(15, this.byte.length - 15);
                return { cmd, potobufSetByteArray };
            }
        }

        /**这是需要返回的*/
        public onMessage_watch_new(res: any): any {
            // yalla.Debug.log(res)
            // this.arr.push(res);
            // Laya.LocalStorage.setJSON("watchData", this.arr)
            if (this.byte) {
                this.byte.clear();
                if (res) this.byte.writeArrayBuffer(new Uint8Array(res));
                if (this.byte.length < 15) return;

                this.byte.pos = 6;
                let gateWayCmd = this.byte.readByte();

                this.byte.pos = 10;
                var cmd = this.byte.readByte();

                this.byte.pos = 14;
                let code = this.byte.readByte();// 响应码

                yalla.Debug.log(code + '-' + gateWayCmd + '===老==onMessage_watch=====cmd==' + cmd);
                // var potobufSetByteArray = this.byte.getUint8Array(0, this.byte.length);
                var potobufSetByteArray = this.byte.getUint8Array(15, this.byte.length - 15);
                return [cmd, potobufSetByteArray];
                // if (this._messageHandler) {
                // 	this._messageHandler.runWith([cmd, potobufSetByteArray]);
                // }
            }
        }
        //========================================================

        public clear() {
            if (this.byte) {
                this.byte.clear();
            }
        }
    }
}