/**
 * 网络消息缓存，根据每帧循环分发,目前游戏每秒60帧，每帧最多分发60条消息，如果消息过多，存在分发延迟问题
 */
class NetMessageCache {
    private static instance: NetMessageCache;
    private messageQueue: Array<{ name: string, func: Function, data: any }>;
    private isDispatching: boolean;
    private defaultDispatchFunction: Function;

    private constructor() {
        this.messageQueue = [];
        this.isDispatching = false;
        this.defaultDispatchFunction = (data: any) => {
            console.log("Default dispatch function called with data:", data);
        };
    }

    public static getInstance(): NetMessageCache {
        if (!NetMessageCache.instance) {
            NetMessageCache.instance = new NetMessageCache();
        }
        return NetMessageCache.instance;
    }

    public addMessage(messageName: string, callback?: Function, data?: any): void {
        const dispatchFunction = callback || ((data: any) => this.defaultDispatchFunction(data));
        this.messageQueue.push({ name: messageName, func: dispatchFunction, data: data });

        if (!this.isDispatching) {
            this.startDispatching();
        }
    }

    public setDefaultDispatchFunction(func: Function): void {
        this.defaultDispatchFunction = func;
    }

    private startDispatching(): void {
        this.isDispatching = true;
        Laya.timer.frameLoop(1, this, this.dispatchMessages);
    }

    private stopDispatching(): void {
        this.isDispatching = false;
        Laya.timer.clear(this, this.dispatchMessages);
    }

    private dispatchMessages(): void {
        if (this.messageQueue.length === 0) {
            this.stopDispatching();
            return;
        }

        const message = this.messageQueue.shift()!;
        message.func(message.data);
    }

    public clearCache(): void {
        this.messageQueue = [];
        this.stopDispatching();
        yalla.Debug.log('******* clearCache *******');

    }
}