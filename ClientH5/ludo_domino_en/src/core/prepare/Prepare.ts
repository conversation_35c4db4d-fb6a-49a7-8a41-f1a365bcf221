module yalla.prepare {
	export class Prepare {
		private _items: Array<PrepareItem>;
		private _started: Array<PrepareItem>;
		private _total: number;
		private _progress: number;
		constructor() { }
		private prepareInit(): void {
			yalla.Str.init();
			yalla.Native.instance.initGames((datas) => {
				if (!!datas) laya.net.LocalStorage.setItem("filePath", datas);
			}, true)
			if (!Laya.LocalStorage.getJSON("voice_open")) {
				Laya.LocalStorage.setJSON("voice_open", { isOpen: true });
			}
		}

		public start(): void {
			yalla.Global.game_state = yalla.data.GameState.State_Loading;
			this.reset();
			this.prepareInit();

			yalla.Native.instance.gameLoadingProgress(1);
			this.add(new PrepareRes());
			this.add(new PrepareData());
			this.showProgress();
		}

		private reset(): void {
			this._total = 0;
			this._progress = 0;
			this._items = [];
			this._started = [];
		}

		protected begin(): void {
			if (this._items.length == 0) {
				return;
			}
			var item: PrepareItem = this._items.shift();
			this._started.push(item);
			item.init();
			item.start();
		}

		/**
		 * 增加一个流程项目到流程的尾部
		 */
		private add(item: PrepareItem): void {
			this._items.push(item);
			item.completeHandler = Laya.Handler.create(this, this.onPrepareItemComplete, [item]);
			item.progressHandler = Laya.Handler.create(this, this.onPrepareItemProgress, [item], false);
			item.errorHandler = Laya.Handler.create(this, this.onPrepareItemError, [item], false);
			this._total += item.progress;
		}

		private onPrepareItemComplete(item: PrepareItem): void {
			this.updateProgress(item.progress);
			if (this._items.length == 0) {
				this.doFinished();
			} else {
				item.dispose();
				this.begin();
			}
		}
		private onPrepareItemProgress(item: PrepareItem, txt: string): void { }
		private onPrepareItemError(item: PrepareItem, txtError: string): void {
			if (yalla.Global.game_state == yalla.data.GameState.State_Loading) {
			}
		}
		private updateProgress(progress: number): void {
			this._progress += progress;
			yalla.Native.instance.gameLoadingProgress(Math.floor(this._progress / this._total * 100));
		}
		private doFinished(): void {
			if (!yalla.util.IsWinConch()) yalla.Debug.log(`[info]prepare completed on timer ${yalla.System.getServerTime()}.`);
			yalla.Debug.log("doFinished");
			yalla.Global.game_state = yalla.data.GameState.State_Playing;
			this.clear();
			// new yalla.BlockWord();
			yalla.Native.instance.isLowPerformanceDevice((result) => {
				if (result) {
					yalla.Global.isLowPerformanceDevice = result.result;
				}
				yalla.Debug.log('****isLowPerformanceDevice00****' + result + "yalla.Global.isLowPerformanceDevice:" + yalla.Global.isLowPerformanceDevice);
			})
			if (yalla.Native.instance.deviceType == DeviceType.IOS) {

				yalla.Native.instance.getDomainType(() => {
					yalla.Skin.instance.init(() => {
						yalla.Native.instance.gameLoadingProgress(200);
					});
				})


				// yalla.Native.instance.getDomainType(() => {} )
				// yalla.Skin.instance.init(() => {
				// 	yalla.Native.instance.gameLoadingProgress(200);
				// });

				if (!yalla.Global.ProfileInfo.isDebug) {
					laya.utils.Browser.window.onerror = function (msg, url, line, column, detail) {
						yalla.Native.instance.writeLog(msg);
						return true;
					}
					laya.utils.Browser.window.alert = function (msg, url, line, column, detail) {
						yalla.Native.instance.writeLog(msg);
						return true;
					}
				}
			} else {
				yalla.Native.instance.getGooglePlayInfo((res) => {
					yalla.Global.googleInfo = res;
					yalla.Debug.log('****getGooglePlayInfo00****' + JSON.stringify(res));
				})
				yalla.Native.instance.gameLoadingProgress(200);
				// yalla.Native.instance.getDomainType(() => {
				// 	yalla.Skin.instance.init(null);
				// } )
				yalla.Native.instance.getDomainType(() => {
					yalla.Skin.instance.init(() => { });
				})
			}
		}

		/**
		 * progress
		 */
		private showProgress(): void {
			Laya.ResourceVersion.enable("version.json", Laya.Handler.create(this, () => {
				this.onLoadingPrepared();
			}));
		}

		private onLoadingPrepared(): void {
			this.begin();
		}
		private onLoadingReload(): void {
			this.start();
		}

		private clear(): void {
			this._items = null;
			this._started = null;
		}

		// 处理中途断线情况
		private handleClientClosed(data: any): void {
		}

		private handleClientError(data: any): void {
		}

		private handleErrorMsg(code: number): void {
		}

		private reconnect(interval: number = 5000): void {

		}
	}
}