module yalla.prepare {

	export class PrepareData extends PrepareItem {

		private _counter: number;

		constructor() {
			super();
		}

		start(): void {
			this.progressHandler.runWith('加载游戏数据');
			//获取游戏基础数据
			this.loadData();
		}

		protected onInit(): void {
			super.onInit();
			yalla.data.UserService.instance.register();
			yalla.data.RoomService.instance.register();
			// yalla.data.VoiceService.instance.register();
			yalla.data.snake.UserService.instance.register();
		}

		get progress(): number { return 50; }

		private loadData(data: any = null) {
			ludo.Proto.instance.on(Laya.Event.COMPLETE, this, this.complete);
		}
	}
}