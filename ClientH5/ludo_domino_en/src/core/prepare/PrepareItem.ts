module yalla.prepare {

	export class PrepareItem {

		protected _resList: Array<any>;
		protected _complete: boolean;
		public completeHandler: <PERSON><PERSON>.Handler;
		public progressHandler: <PERSON><PERSON>.Handler;
		public errorHandler: <PERSON><PERSON>.Handler;
		private _name: string;

		constructor(name:string='') {
			this._resList = [];
			this._name = name;
		}

		protected onInit(): void {
		}
 
		protected complete(): void {
			this._complete = true;
			this.completeHandler.run();
		}

		init(): void {
			this.onInit();
		}

		start(): void {
			throw Error('must be overrided')
		}

		dispose(): void {
			
		}

		/**
		 * 进度值
		 * @return
		 */
		get progress(): number { return 1; }

		// 资源列表
		get resList(): Array<any> { return this._resList; }

		// 命令列表
		get cmdList(): Array<any> { return null; }
	}
}