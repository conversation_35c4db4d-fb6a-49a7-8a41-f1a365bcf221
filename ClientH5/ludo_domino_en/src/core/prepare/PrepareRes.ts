module yalla.prepare {

	import Loader = Laya.Loader;
	import Handler = Laya.Handler;

	export class PrepareRes extends PrepareItem {
		private _counter: number;

		constructor() {
			super();
		}

		protected onInit(): void {
			super.onInit();

			this._counter = 0;
			this._resList = yalla.res;
		}

		start(): void {
			this.progressHandler.runWith('加载游戏资源');
			Laya.ResourceVersion.enable("version.json", Laya.Handler.create(this, () => {
				Laya.loader.load(this._resList, Handler.create(this, this.onLoadComplete), Handler.create(this, this.onLoadProgress, null, false));
			}));
			yalla.Native.instance.loadProfileInfo((msg: ProfileInfo) => {
				yalla.Global.ProfileInfo = msg;
				var isDebug = yalla.Global.ProfileInfo.isDebug;
				if (isDebug) {
					yalla.DomainUtil.instance.game_account_url = "https://fat-account.yalla.games";
					yalla.DomainUtil.instance.game_activity_url = "https://fat-activity.yalla.games";
				}
				// yalla.Global.rootUrl = isDebug ? 'https://fat-account.yalla.games' : 'https://account.yalla.games';
				// new yalla.BlockWord();
			});
			this.loadBackend();
		}

		dispose(): void {
			super.dispose();
		}

		get progress(): number { return 50; }

		private onLoadComplete(data: any): void {
			if (data) {
				yalla.Font.initBmpFont(Laya.Handler.create(this, this.complete));
			} else {
				yalla.Native.instance.initEngineFailed();
				this.errorHandler.runWith(`加载游戏资源失败`);
			}
		}

		private onLoadProgress(progress: number): void {
			// if (progress === 1) {
			// 	this.loadBackend();
			// }
		}

		/**
		 * 登录时非必须的资源、数据可以放后台预加载
		 * 
		 */
		private loadBackend(): void {
			let resList = yalla.backLoadRes;
			// if (yalla.Native.instance.deviceType != DeviceType.IOS) {
			// 	resList = resList.concat(yalla.backLoadSoundRes);
			// }
			Laya.loader.load(resList, Laya.Handler.create(this, (data: any) => {
				if (!data) {
					yalla.Native.instance.initEngineFailed();
				}
			}));
		}
	}
}