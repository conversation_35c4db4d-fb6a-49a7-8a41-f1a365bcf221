module yalla {
    export class CLogDBA {
        static _instance: CLogDBA;
        private recodHash: any = {};
        constructor() { }

        static get instance(): CLogDBA {
            return CLogDBA._instance || (CLogDBA._instance = new CLogDBA());
        }

        /** 请求记录 */
        record_request(eventId) {
            if (!this.recodHash) this.recodHash = {};
            this.recodHash[eventId] = System.getNowTime();
        }
        /**响应后上报 */
        record_response(eventId) {
            if (this.recodHash && this.recodHash[eventId]) {
                var startTime = this.recodHash[eventId];
                var durNum = System.getNowTime() - startTime;
                Native.instance.getProxyInfo(yalla.Global.Account.url, (data) => {
                    var isProxy = Boolean(data.state);
                    yalla.util.clogDBAmsg(eventId, JSON.stringify({ durNum, isProxy }));
                    delete this.recodHash[eventId];
                })
                delete this.recodHash[eventId];
            }
        }
        /**
         * 断线重连、清理都需要执行clear
         */
        clear() {
            this.recodHash = {};
        }
    }
    export enum CLogDBA_EventID {
        Ludo_DiceNums_Resp = 600004,//Ludo游戏内骰子点数返回响应
        Ludo_ChessMove_Resp = 600005,//Ludo游戏内棋子移动返回响应
        Ludo_Undo_DiceNums_Resp = 600008,//Ludo游戏内Undo骰子点数返回响应
        Domino_PlayCard_Resp = 600006,//Domino出牌响应
        // Domino_PlayCard_End = 600007,//Domino牌到位后响应
        Domino_Roundtime_Resp = 600009//Domino延时响应
    }
}    




