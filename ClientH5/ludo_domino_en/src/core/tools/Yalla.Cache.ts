module yalla {
    export class Cache extends Laya.EventDispatcher {
        private static _countryList: Object;
        private static _avatarList: Object;
        private static _levelList: Object;
        constructor() {
            super();
        }
        static get countryList(): Object {
            if (!this._countryList) {
                var obj: Object = new Object();
                yalla.nationalList.forEach((value: countryItem) => {
                    obj[value.id] = value;
                })
                this._countryList = obj;
            }
            return this._countryList;
        }
        /**
         * 获取国家信息
         * @param id 国家id
         */
        static getCountryRES(id: number): countryItem {
            if (!!this.countryList[id]) {
                return this.countryList[id];
            } else {
                return {
                    areaCode: "unknown",
                    flagUrl: "https://account.yalla.games/NationlityIcon/1.png",
                    id: 1,
                    name: "unknown",
                    shortName: "unknown",
                }
            }
        }
    }
}