module yalla {
    export class Debug extends Laya.Box {
        private ui: ui.publics.dialog.DebugUI;
        private btn: Laya.Sprite;
        private clearBtn: Laya.Sprite;
        private index: number = 0;
        private itemList = [];
        private arr = [];
        private logClient: any;
        private serverUrl: string;
        constructor() {
            super();
            this.size(Laya.stage.width, Laya.stage.height);
            this.mouseThrough = true;
            this.btn = new Laya.Sprite();
            this.btn.size(60, 60);
            this.btn.graphics.drawCircle(30, 30, 30, "#ffffff", "#000000", 2);
            this.btn.alpha = 0.5;
            this.addChild(this.btn);
            this.btn.pos(0, 300);
            this.ui = new ui.publics.dialog.DebugUI();
            this.btn.on("click", this, this.showLog);
            this.ui.clear.on("click", this, this.clear);
            this.initSearchEvent();
            this.ui._list.renderHandler = new Laya.Handler(this, (cell: Laya.Label, index) => {
                var data = cell.dataSource;
                cell.text = data.msg;
            });
            // var sUrl = Laya.LocalStorage.getItem("serverUrl");
            // if (sUrl) {
            //     this.serverUrl = sUrl;
            //     this.ui.urlInput.text = sUrl;
            // }
        }
        connect(serverUrl: string) {
            // if (this.logClient) {
            //     this.logClient.disconnect();
            // }
            // var LogClient = window["LogClient"];

            // this.logClient = new LogClient({
            //     serverUrl,//: "ws://172.20.65.14:3000",
            //     reconnectDelay: 3000,
            //     overrideConsole: true,
            //     deviceCode: Date.now().toString()
            // });

            // // // 监听连接成功事件
            // this.logClient.on('connect', (data) => {
            //     console.log('info', `已连接到服务器: ${data.serverUrl}`);
            // });

            // // 监听接收消息事件
            // this.logClient.on('message', (logData) => {
            //     console.log(logData.type, `收到日志: ${logData.message}`);
            // });

            // // 监听断开连接事件
            // this.logClient.on('disconnect', () => {
            //     console.log('info', '已断开与服务器的连接');
            // });

            // // 监听错误事件
            // this.logClient.on('error', (error) => {
            //     console.log('error', `错误: ${error.error}`);

            // });
            // // 连接到服务器
            // this.logClient.connect();
        }
        public clear() {
            this.index = 0;
            this.arr = [];
            this.itemList = [];
            this.currentIndex = 0;
            this.ui._list.array = this.arr;
            this.closeSearch();
        }

        static _instance: yalla.Debug = null;
        static init() {
            this._instance = new yalla.Debug();
            Laya.stage.addChild(this._instance);
            this._instance.zOrder = 10000;
        }

        private showLog() {
            this.ui._list.array = this.arr;
            this.ui.popup();
            this.ui.clear.skin = 'public/trust-btn.png';
            this.ui.closeBtn.skin = 'public/trust-btn.png';
            this.ui.searchBtn.skin = 'public/trust-btn.png';
        }
        static filterLogMore(message: string, ...params: any[]) {
            // if (params.length > 0) {
            //     let concatenatedMessage = message;
            //     params.forEach(param => {
            //         try {
            //             concatenatedMessage += ' ' + (typeof param === 'object' ? JSON.stringify(param) : param);
            //         } catch (error) {
            //             concatenatedMessage += ' 日志无法序列化 ';
            //         }
            //     });
            //     this.log(concatenatedMessage);
            // } else {
            //     this.log(message);
            // }
        }
        static logMore(message: string, ...params: any[]) {
            // if (params.length > 0) {
            //     let concatenatedMessage = message;
            //     params.forEach(param => {
            //         try {
            //             concatenatedMessage += ' ' + (typeof param === 'object' ? JSON.stringify(param) : param);
            //         } catch (error) {
            //             concatenatedMessage += ' 日志无法序列化 ';
            //         }
            //     });
            //     this.log(concatenatedMessage);
            // } else {
            //     this.log(message);
            // }
        }
        static yum(message: string, ...params: any[]) {
            // if (params.length > 0) {
            //     let concatenatedMessage = message;
            //     params.forEach(param => {
            //         try {
            //             concatenatedMessage += ' ' + (typeof param === 'object' ? JSON.stringify(param) : param);
            //         } catch (error) {
            //             concatenatedMessage += ' 日志无法序列化 ';
            //         }
            //     });
            //     this.log(concatenatedMessage);
            // } else {
            //     this.log(message);
            // }
        }
        //TODO 出牌相关的日志屏蔽
        static closeJackaroCardLog() {
            JackaroCardLogger.instance.setLogEnabled('global', false);
        }
        //TODO ！！！ LogClient 需要删除引用，发正式版本
        static zzf(msg: any, color = "red") {
            // console.log(msg);
            // // if (!yalla.Global.ProfileInfo.isDebug) return;
            // if (!this._instance) this.init();
            // this._instance.index++;
            // var str: string = `${this._instance.index}:(${yalla.getTimeHMS()})` + JSON.stringify(msg);
            // var i = Math.ceil(str.length / 60);
            // for (var j = 0; j < i; j++) {
            //     var t = j == 0 ? "" : "     ";
            //     this._instance.arr.push({ msg: t + str.slice(j * 60, (j + 1) * 60) });
            // }

            // let len = this._instance.itemList.length;
            // let height = len < 1 ? 0 : this._instance.itemList[len - 1].height;
            // height += (i * (29 + 5));
            // // console.log(height, i, this._instance.index,"======添加日志对应高度======", str);
            // this._instance.itemList.push({ msg: str, height: height });
        }
        static log(msg: any, color = "red") {
            // // if (!yalla.Global.ProfileInfo.isDebug) return;
            // if (!this._instance) this.init();
            // this._instance.logClient && this._instance.logClient.sendConsoleLog("debug", msg);
            // this._instance.index++;
            // var str: string = `${this._instance.index}:(${yalla.getTimeHMS()})` + JSON.stringify(msg);
            // var i = Math.ceil(str.length / 60);
            // for (var j = 0; j < i; j++) {
            //     var t = j == 0 ? "" : "     ";
            //     this._instance.arr.push({ msg: t + str.slice(j * 60, (j + 1) * 60) });
            // }

            // let len = this._instance.itemList.length;
            // let height = len < 1 ? 0 : this._instance.itemList[len - 1].height;
            // height += (i * (29 + 5));
            // // console.log(height, i, this._instance.index,"======添加日志对应高度======", str);
            // this._instance.itemList.push({ msg: str, height: height });

            // console.log(str);
        }
        // static specialLog(msg: any) {
        // console.log("-走马灯消息-", msg);
        // // if (!yalla.Global.ProfileInfo.isDebug) return;
        // this._instance.ui.specialLabel.visible = msg;
        // if (!msg) {
        //     this._instance.ui.specialLabel.text = '';
        //     return;
        // }
        // let str: string = ((typeof msg == 'string') || (typeof msg == 'number')) ? String(msg) : JSON.stringify(msg);
        // this._instance.ui.specialLabel.text = `(${yalla.getTimeHMS()})：` + str;
        // }

        //-----------------------------日志查询----------------------------------------
        /** 当前消息索引 */
        private currentIndex = 0;

        private initSearchUI(): void {
            this.ui.searchBg.skin = 'public/chat_line.png';
            this.ui.preBtn.skin = 'public/maskBg.png';
            this.ui.nextBtn.skin = 'public/maskBg.png';
            this.ui.toStart.skin = 'public/maskBg.png';
            this.ui.toEnd.skin = 'public/maskBg.png';
            this.ui.closeSearch.skin = 'public/maskBg.png';
        }

        private initSearchEvent(): void {
            this.ui.searchBtn.on('click', this, this.searchLog);
            this.ui.closeSearch.on('click', this, this.closeSearch);
            this.ui.preBtn.on('click', this, this.searchPrev);
            this.ui.nextBtn.on('click', this, this.searchNext);
            this.ui.toStart.on('click', this, this.toStart);
            this.ui.toEnd.on('click', this, this.toEnd);
            this.ui.copyLog.on('click', this, Debug.toCopyLog);
            this.ui.connect_btn.on('click', this, this.clickConnect);
        }
        private clickConnect() {
            let serverUrl = this.ui.urlInput.text;
            if (serverUrl) {
                this.serverUrl = serverUrl;
                this.connect(serverUrl);
                Laya.LocalStorage.setItem("serverUrl", serverUrl);
            }
        }

        /**
         * 查询日志
         */
        private searchLog() {
            this.initSearchUI();
            this.ui.searchBox.visible = true;
            this.ui.searchInput.text = '';
        }

        private toStart() {
            this.currentIndex = 0;
            this.ui._list.scrollBar.value = 0;
        }
        private toEnd() {
            this.currentIndex = this.itemList.length;
            this.ui._list.scrollBar.value = this.ui._list.scrollBar.max;
        }
        private static toCopyLog() {
            if (this._instance && this._instance.arr) {
                let logStr = this._instance.arr.join("\n");
                yalla.Native.instance.copyLog(logStr);
            }
        }

        private searchPrev() {
            let searchStr: string = this.ui.searchInput.text.toLowerCase();
            if (!searchStr || searchStr.length < 1) return;
            let i;
            for (i = this.currentIndex; i > 0; i--) {
                if (!this.itemList[i]) return;
                var itemStr: string = this.itemList[i].msg.toLowerCase();
                if (itemStr.indexOf(searchStr) > -1) {
                    // console.log(msgLen,"=========prev:",this.itemList[i].height, i, itemStr);
                    this.currentIndex = Math.max(i - 1, 0);
                    this.ui._list.scrollBar.value = this.itemList[i].height - 80;
                    return;
                }
            }
        }

        private searchNext() {
            let searchStr: string = this.ui.searchInput.text.toLowerCase();
            if (!searchStr || searchStr.length < 1) return;
            let i;
            let msgLen = this.itemList.length;
            for (i = this.currentIndex; i < msgLen; i++) {
                if (!this.itemList[i]) return;
                var itemStr: string = (this.itemList[i].msg).toLowerCase();
                if (itemStr.indexOf(searchStr) > -1) {
                    // console.log(msgLen,"=========next:",this.itemList[i].height, i, itemStr);
                    this.ui._list.scrollBar.value = this.itemList[i].height - 80;
                    this.currentIndex = Math.min(i + 1, msgLen - 1);
                    return;
                }
            }
        }

        private closeSearch() {
            this.ui.searchBox.visible = false;
            this.ui.searchInput.text = '';
        }
    }
}