module yalla {
    /**
     * *******
     *  common_file = 'https://file.yallaludo.com';
        common_activity_fat = 'https://fat-activity.yalla.games';
        common_activity = 'https://activity.yalla.games';
     * 皮肤：
     *  https://activity.yalla.games/YallaGame/Skin/SkinVersionInfo
     *  https://account.yalla.games/YallaGame/SundryInfo/ExitRecordAdd
     *  https://account.yalla.games/YallaGame/SundryInfo/VoiceStatistic   Skin.getVersion
     *1. 服务器状态错误码 >=500 (后续讨论，统一只要失败就换域名)   (开麦、闭麦、资源下载、android5.0 、各种退出埋点)

      2.COMMON_FILE file.yallaludo.com  替换域名直接放到原声去处理
      
      socket连接，服务端增加新字段（拼好的一组url）不考虑android5.0以下情况， 原先规则：蛇棋、丛林、决斗直接用url；ludo、domino用host+port
     */
    export class DomainUtil {
        /** {"bizType":1004,"countryCode":"HK","hostUrl":"https://fat-activity.jcmcookie.com","type":2,"version":2} */
        public domainHash = {};

        public game_account_url = 'https://account.yalla.games'; 
        public game_activity_url = 'https://activity.yalla.games';
        public game_common_url = 'https://file.yallaludo.com';
        public common_file_host = ''; //host   file.yallaludo.com

        /** 匹配成功后，onLogin收到连接socket的 url组合，port只有一组 */
        public socket_urlList = [];

        static _instance: yalla.DomainUtil = null;
        constructor() { }
        static get instance() {
            if (!this._instance) this._instance = new yalla.DomainUtil();
            return this._instance;
        }
        /**
         *isInitUrl = true, 取数组第一个
         * 遍历socket host
         * onLogin 需要重新赋值Global.Account.url host
         */
        initSocketUrl(urls) {
            yalla.Debug.log("===初始化备用域名socket url==");
            yalla.Debug.log(urls);
            // if (typeof urls == 'string') this.socket_urlList = urls.split(',');
            this.socket_urlList = urls;
            if (this.socket_urlList && this.socket_urlList.length > 0) yalla.Global.Account.url = this.socket_urlList[0];
        }
        /**
         * 更换socket url
         * tyr网管 onError等情况不返回失败url（原生直连）
         */
        changeSocketUrl(res): string {
            yalla.Debug.log(!!res + "==切换下一个socket url==" + (!!this.socket_urlList));
            if(this.socket_urlList) yalla.Debug.log("==域名长度==" + this.socket_urlList.length);
            if (!res || !this.socket_urlList || this.socket_urlList.length < 1) return;

            var len = this.socket_urlList.length,
                url = yalla.Global.Account.url;
            if (typeof res == "string") res = eval(res);
            res.url && (url = res.url);
            
            var cIndex = this.socket_urlList.indexOf(url);
            yalla.Debug.log(cIndex + "=索引===0==前一个url:" + yalla.Global.Account.url );
            if (cIndex < 0 || (cIndex + 1) >= len) cIndex = 0;
            else cIndex += 1;

            if(this.socket_urlList[cIndex]) yalla.Global.Account.url = this.socket_urlList[cIndex];
            yalla.Debug.log(cIndex + "=索引===1==下一个Url:" + yalla.Global.Account.url);
            
            return yalla.Global.Account.url;
        }

        /**
         * 根据类型获取备用域名
         */
        initDomainData() {
            if (yalla.Global.DomainTypeInfo) {
                this.domainHash = {};
                yalla.Global.DomainTypeInfo.forEach(element => {
                    if (element) {
                        var bizType = element.bizType,
                            hostUrl = element.hostUrl;
                        
                        if (hostUrl) element.hostUrl = hostUrl.split(',');
                        this.domainHash[bizType] = element;

                        //首次赋值
                        var firstUrl = element.hostUrl[0];
                        if (firstUrl) {
                            if (bizType == DomainType.COMMON_FILE) {
                                this.common_file_host = yalla.util.getHost(firstUrl);
                                this.game_common_url = firstUrl;
                            }
                            else if (bizType == DomainType.GAME_ACCOUNT) this.game_account_url = firstUrl;
                            else if (bizType == DomainType.GAME_ACTIVITY) this.game_activity_url = firstUrl;
                        }
                    }
                });
            }
        }
        
        /** 头像 替换host 但排除fb tw
         *  ******* TODO:: 原生其实也不用游戏端发送的url去下载，他们内部解析，更换域名下载
        */
        getFileDomainHeadType(url: string): string {
            if (!url) return '';
            if (this.common_file_host) {
                try {
                    let fb = "graph.facebook.com";
                    let tw = "pbs.twimg.com";
                    let host = yalla.util.getHost(url);
                    if (host == fb || host == tw) {
                        // GameUtil.log(host + "==头像不用替换 getFileDomainHeadType==" + url);
                        return url;
                    }
                    let path = String(url).replace(host, this.common_file_host);
                    // GameUtil.log(host + "==所有替换的体制 getFileDomainType==path=" + path);
                    return path;

                } catch (error) {
                    return url;
                }
            }
            return url;
        }

        /** 表情系统 互动礼物等host 提取 替换
         * ******* TODO:: 原生其实也不用游戏端发送的url去下载，他们内部解析，更换域名下载
        */
        getFileDomainType_flexible(url: string): string {
            if (!url) return '';
            if (this.common_file_host) {
                try {
                    let host = yalla.util.getHost(url);
                    // Debug.log(host + ":host=====url:" + url);
                    let path = String(url).replace(host, this.common_file_host);
                    // Debug.log(path)
                    return path;
                } catch (error) {
                    return url;
                }
            }
            return url;
        }

        /**
         * 记录当前的url
         * @param bizType 
         */
        getNextUrlByType(bizType) {
            var nextUrl = '';
            // this.domainHash = { 3000: { hostUrl: ["https://file.yallaludo.com","https://file.yallaludo2.com","https://file.yallaludo3.com"] }, 1000: {hostUrl:["https://account.yalla.games","https://account.yalla2.games","https://account.yalla3.games"]}};
            var element = this.domainHash[bizType];
            if (!element || !element.hostUrl) return nextUrl;
            
            switch (bizType) {
                case DomainType.COMMON_FILE:
                    nextUrl = this.searchNextUrl(this.game_common_url, element.hostUrl);
                    yalla.Debug.log("=备用域名COMMON_FILE==nextUrl:"+nextUrl);
                    if (nextUrl) {
                        this.common_file_host = yalla.util.getHost(nextUrl);
                        this.game_common_url = nextUrl;
                    }
                    break;   
                case DomainType.GAME_ACCOUNT:
                    nextUrl = this.searchNextUrl(this.game_account_url, element.hostUrl);
                    yalla.Debug.log("=备用域名GAME_ACCOUNT==nextUrl:"+nextUrl);
                    if (nextUrl) this.game_account_url = nextUrl;    
                    break; 
                case DomainType.GAME_ACTIVITY:
                    nextUrl = this.searchNextUrl(this.game_activity_url, element.hostUrl);
                    yalla.Debug.log("=备用域名GAME_ACTIVITY==nextUrl:"+nextUrl);
                    if (nextUrl) this.game_activity_url = nextUrl;    
                    break; 
            }
            return nextUrl;
        }

        /**
         * 获取下一个url
         * @param cUrl 
         * @param urlList 
         */
        searchNextUrl(cUrl, urlList = []) {
            var len = urlList.length;
            var cIndex = urlList.indexOf(cUrl);
            if (cIndex < 0 || (cIndex + 1) >= len) return;
            return urlList[cIndex + 1];
        }
        /**
         * 如果开启代理情况下，游戏内不更换域名
         */
        checkProxy_changeSocketUrl(res) {
            Native.instance.getProxyInfo(yalla.Global.Account.url, (data) => {
                var proxy = data.state;
                yalla.Debug.log(yalla.Global.Account.url+":url===重连前获取代理状态==proxy=" + proxy);
                if (!proxy) {
                    this.changeSocketUrl(res);
                }
            });
        }

        // checkProxy_socket() {
        //     Native.instance.getProxyInfo(yalla.Global.Account.url, (data) => {
        //         var proxy = data.state;
        //         yalla.Debug.log("===重连前获取代理状态==proxy=" + proxy);
        //         if (proxy) {
        //             yalla.Global.heart_interval = 9000;
        //             yalla.Global.loseNet_interval = 10000;
        //         } else {
        //             yalla.Global.heart_interval = 5000;
        //             yalla.Global.loseNet_interval = 6000;
        //         }
        //     });
        // }
    }
}