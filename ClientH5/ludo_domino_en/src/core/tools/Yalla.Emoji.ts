module yalla {
    export class Emoji {
        static re = /(?:\ud83d\udc68\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc68\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc68\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc68\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc68\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffe]|\ud83e\uddd1\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\u200d\ud83e\udd1d\u200d\ud83e\uddd1|\ud83d\udc6b\ud83c[\udffb-\udfff]|\ud83d\udc6c\ud83c[\udffb-\udfff]|\ud83d\udc6d\ud83c[\udffb-\udfff]|\ud83d[\udc6b-\udc6d])|(?:\ud83d[\udc68\udc69]|\ud83e\uddd1)(?:\ud83c[\udffb-\udfff])?\u200d(?:\u2695\ufe0f|\u2696\ufe0f|\u2708\ufe0f|\ud83c[\udf3e\udf73\udf7c\udf84\udf93\udfa4\udfa8\udfeb\udfed]|\ud83d[\udcbb\udcbc\udd27\udd2c\ude80\ude92]|\ud83e[\uddaf-\uddb3\uddbc\uddbd])|(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75]|\u26f9)((?:\ud83c[\udffb-\udfff]|\ufe0f)\u200d[\u2640\u2642]\ufe0f)|(?:\ud83c[\udfc3\udfc4\udfca]|\ud83d[\udc6e\udc70\udc71\udc73\udc77\udc81\udc82\udc86\udc87\ude45-\ude47\ude4b\ude4d\ude4e\udea3\udeb4-\udeb6]|\ud83e[\udd26\udd35\udd37-\udd39\udd3d\udd3e\uddb8\uddb9\uddcd-\uddcf\uddd6-\udddd])(?:\ud83c[\udffb-\udfff])?\u200d[\u2640\u2642]\ufe0f|(?:\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d[\udc68\udc69]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc68|\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d[\udc68\udc69]|\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f|\ud83c\udff3\ufe0f\u200d\ud83c\udf08|\ud83c\udff4\u200d\u2620\ufe0f|\ud83d\udc15\u200d\ud83e\uddba|\ud83d\udc3b\u200d\u2744\ufe0f|\ud83d\udc41\u200d\ud83d\udde8|\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc6f\u200d\u2640\ufe0f|\ud83d\udc6f\u200d\u2642\ufe0f|\ud83e\udd3c\u200d\u2640\ufe0f|\ud83e\udd3c\u200d\u2642\ufe0f|\ud83e\uddde\u200d\u2640\ufe0f|\ud83e\uddde\u200d\u2642\ufe0f|\ud83e\udddf\u200d\u2640\ufe0f|\ud83e\udddf\u200d\u2642\ufe0f|\ud83d\udc08\u200d\u2b1b)|[#*0-9]\ufe0f?\u20e3|(?:[©®\u2122\u265f]\ufe0f)|(?:\ud83c[\udc04\udd70\udd71\udd7e\udd7f\ude02\ude1a\ude2f\ude37\udf21\udf24-\udf2c\udf36\udf7d\udf96\udf97\udf99-\udf9b\udf9e\udf9f\udfcd\udfce\udfd4-\udfdf\udff3\udff5\udff7]|\ud83d[\udc3f\udc41\udcfd\udd49\udd4a\udd6f\udd70\udd73\udd76-\udd79\udd87\udd8a-\udd8d\udda5\udda8\uddb1\uddb2\uddbc\uddc2-\uddc4\uddd1-\uddd3\udddc-\uddde\udde1\udde3\udde8\uddef\uddf3\uddfa\udecb\udecd-\udecf\udee0-\udee5\udee9\udef0\udef3]|[\u203c\u2049\u2139\u2194-\u2199\u21a9\u21aa\u231a\u231b\u2328\u23cf\u23ed-\u23ef\u23f1\u23f2\u23f8-\u23fa\u24c2\u25aa\u25ab\u25b6\u25c0\u25fb-\u25fe\u2600-\u2604\u260e\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262a\u262e\u262f\u2638-\u263a\u2640\u2642\u2648-\u2653\u2660\u2663\u2665\u2666\u2668\u267b\u267f\u2692-\u2697\u2699\u269b\u269c\u26a0\u26a1\u26a7\u26aa\u26ab\u26b0\u26b1\u26bd\u26be\u26c4\u26c5\u26c8\u26cf\u26d1\u26d3\u26d4\u26e9\u26ea\u26f0-\u26f5\u26f8\u26fa\u26fd\u2702\u2708\u2709\u270f\u2712\u2714\u2716\u271d\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u2764\u27a1\u2934\u2935\u2b05-\u2b07\u2b1b\u2b1c\u2b50\u2b55\u3030\u303d\u3297\u3299])(?:\ufe0f|(?!\ufe0e))|(?:(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75\udd90]|[\u261d\u26f7\u26f9\u270c\u270d])(?:\ufe0f|(?!\ufe0e))|(?:\ud83c[\udf85\udfc2-\udfc4\udfc7\udfca]|\ud83d[\udc42\udc43\udc46-\udc50\udc66-\udc69\udc6e\udc70-\udc78\udc7c\udc81-\udc83\udc85-\udc87\udcaa\udd7a\udd95\udd96\ude45-\ude47\ude4b-\ude4f\udea3\udeb4-\udeb6\udec0\udecc]|\ud83e[\udd0c\udd0f\udd18-\udd1c\udd1e\udd1f\udd26\udd30-\udd39\udd3d\udd3e\udd77\uddb5\uddb6\uddb8\uddb9\uddbb\uddcd-\uddcf\uddd1-\udddd]|[\u270a\u270b]))(?:\ud83c[\udffb-\udfff])?|(?:\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc73\udb40\udc63\udb40\udc74\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc77\udb40\udc6c\udb40\udc73\udb40\udc7f|\ud83c\udde6\ud83c[\udde8-\uddec\uddee\uddf1\uddf2\uddf4\uddf6-\uddfa\uddfc\uddfd\uddff]|\ud83c\udde7\ud83c[\udde6\udde7\udde9-\uddef\uddf1-\uddf4\uddf6-\uddf9\uddfb\uddfc\uddfe\uddff]|\ud83c\udde8\ud83c[\udde6\udde8\udde9\uddeb-\uddee\uddf0-\uddf5\uddf7\uddfa-\uddff]|\ud83c\udde9\ud83c[\uddea\uddec\uddef\uddf0\uddf2\uddf4\uddff]|\ud83c\uddea\ud83c[\udde6\udde8\uddea\uddec\udded\uddf7-\uddfa]|\ud83c\uddeb\ud83c[\uddee-\uddf0\uddf2\uddf4\uddf7]|\ud83c\uddec\ud83c[\udde6\udde7\udde9-\uddee\uddf1-\uddf3\uddf5-\uddfa\uddfc\uddfe]|\ud83c\udded\ud83c[\uddf0\uddf2\uddf3\uddf7\uddf9\uddfa]|\ud83c\uddee\ud83c[\udde8-\uddea\uddf1-\uddf4\uddf6-\uddf9]|\ud83c\uddef\ud83c[\uddea\uddf2\uddf4\uddf5]|\ud83c\uddf0\ud83c[\uddea\uddec-\uddee\uddf2\uddf3\uddf5\uddf7\uddfc\uddfe\uddff]|\ud83c\uddf1\ud83c[\udde6-\udde8\uddee\uddf0\uddf7-\uddfb\uddfe]|\ud83c\uddf2\ud83c[\udde6\udde8-\udded\uddf0-\uddff]|\ud83c\uddf3\ud83c[\udde6\udde8\uddea-\uddec\uddee\uddf1\uddf4\uddf5\uddf7\uddfa\uddff]|\ud83c\uddf4\ud83c\uddf2|\ud83c\uddf5\ud83c[\udde6\uddea-\udded\uddf0-\uddf3\uddf7-\uddf9\uddfc\uddfe]|\ud83c\uddf6\ud83c\udde6|\ud83c\uddf7\ud83c[\uddea\uddf4\uddf8\uddfa\uddfc]|\ud83c\uddf8\ud83c[\udde6-\uddea\uddec-\uddf4\uddf7-\uddf9\uddfb\uddfd-\uddff]|\ud83c\uddf9\ud83c[\udde6\udde8\udde9\uddeb-\udded\uddef-\uddf4\uddf7\uddf9\uddfb\uddfc\uddff]|\ud83c\uddfa\ud83c[\udde6\uddec\uddf2\uddf3\uddf8\uddfe\uddff]|\ud83c\uddfb\ud83c[\udde6\udde8\uddea\uddec\uddee\uddf3\uddfa]|\ud83c\uddfc\ud83c[\uddeb\uddf8]|\ud83c\uddfd\ud83c\uddf0|\ud83c\uddfe\ud83c[\uddea\uddf9]|\ud83c\uddff\ud83c[\udde6\uddf2\uddfc]|\ud83c[\udccf\udd8e\udd91-\udd9a\udde6-\uddff\ude01\ude32-\ude36\ude38-\ude3a\ude50\ude51\udf00-\udf20\udf2d-\udf35\udf37-\udf7c\udf7e-\udf84\udf86-\udf93\udfa0-\udfc1\udfc5\udfc6\udfc8\udfc9\udfcf-\udfd3\udfe0-\udff0\udff4\udff8-\udfff]|\ud83d[\udc00-\udc3e\udc40\udc44\udc45\udc51-\udc65\udc6a\udc6f\udc79-\udc7b\udc7d-\udc80\udc84\udc88-\udca9\udcab-\udcfc\udcff-\udd3d\udd4b-\udd4e\udd50-\udd67\udda4\uddfb-\ude44\ude48-\ude4a\ude80-\udea2\udea4-\udeb3\udeb7-\udebf\udec1-\udec5\uded0-\uded2\uded5-\uded7\udeeb\udeec\udef4-\udefc\udfe0-\udfeb]|\ud83e[\udd0d\udd0e\udd10-\udd17\udd1d\udd20-\udd25\udd27-\udd2f\udd3a\udd3c\udd3f-\udd45\udd47-\udd76\udd78\udd7a-\uddb4\uddb7\uddba\uddbc-\uddcb\uddd0\uddde-\uddff\ude70-\ude74\ude78-\ude7a\ude80-\ude86\ude90-\udea8\udeb0-\udeb6\udec0-\udec2\uded0-\uded6]|[\u23e9-\u23ec\u23f0\u23f3\u267e\u26ce\u2705\u2728\u274c\u274e\u2753-\u2755\u2795-\u2797\u27b0\u27bf\ue50a])|\ufe0f/g;

        static emojiHash = { "2049": 1, "2122": 1, "2139": 1, "2194": 1, "2195": 1, "2196": 1, "2197": 1, "2198": 1, "2199": 1, "2600": 1, "2601": 1, "2611": 1, "2614": 1, "2615": 1, "2648": 1, "2649": 1, "2650": 1, "2651": 1, "2652": 1, "2653": 1, "2660": 1, "2663": 1, "2665": 1, "2666": 1, "2668": 1, "2693": 1, "2702": 1, "2705": 1, "2708": 1, "2709": 1, "2712": 1, "2714": 1, "2716": 1, "2728": 1, "2733": 1, "2734": 1, "2744": 1, "2747": 1, "2753": 1, "2754": 1, "2755": 1, "2757": 1, "2764": 1, "2795": 1, "2796": 1, "2797": 1, "2934": 1, "2935": 1, "3030": 1, "3297": 1, "3299": 1, "1f004": 1, "1f0cf": 1, "1f170": 1, "1f171": 1, "1f17e": 1, "1f17f": 1, "1f18e": 1, "1f191": 1, "1f192": 1, "1f193": 1, "1f194": 1, "1f195": 1, "1f196": 1, "1f197": 1, "1f198": 1, "1f199": 1, "1f19a": 1, "1f1e6": 1, "1f1e7": 1, "1f1e8-1f1f3": 1, "1f1e8": 1, "1f1e9-1f1ea": 1, "1f1e9": 1, "1f1ea-1f1f8": 1, "1f1ea": 1, "1f1eb-1f1f7": 1, "1f1eb": 1, "1f1ec-1f1e7": 1, "1f1ec": 1, "1f1ed": 1, "1f1ee-1f1f9": 1, "1f1ee": 1, "1f1ef-1f1f5": 1, "1f1ef": 1, "1f1f0-1f1f7": 1, "1f1f0": 1, "1f1f1": 1, "1f1f2": 1, "1f1f3": 1, "1f1f4": 1, "1f1f5": 1, "1f1f6": 1, "1f1f7-1f1fa": 1, "1f1f7": 1, "1f1f8": 1, "1f1f9": 1, "1f1fa-1f1f8": 1, "1f1fa": 1, "1f1fb": 1, "1f1fc": 1, "1f1fd": 1, "1f1fe": 1, "1f1ff": 1, "1f201": 1, "1f202": 1, "1f21a": 1, "1f22f": 1, "1f232": 1, "1f233": 1, "1f234": 1, "1f235": 1, "1f236": 1, "1f237": 1, "1f238": 1, "1f239": 1, "1f23a": 1, "1f250": 1, "1f251": 1, "1f300": 1, "1f301": 1, "1f302": 1, "1f303": 1, "1f304": 1, "1f305": 1, "1f306": 1, "1f307": 1, "1f308": 1, "1f309": 1, "1f30a": 1, "1f30b": 1, "1f30c": 1, "1f30d": 1, "1f30e": 1, "1f30f": 1, "1f310": 1, "1f311": 1, "1f312": 1, "1f313": 1, "1f314": 1, "1f315": 1, "1f316": 1, "1f317": 1, "1f318": 1, "1f319": 1, "1f31a": 1, "1f31b": 1, "1f31c": 1, "1f31d": 1, "1f31e": 1, "1f31f": 1, "1f320": 1, "1f330": 1, "1f331": 1, "1f332": 1, "1f333": 1, "1f334": 1, "1f335": 1, "1f337": 1, "1f338": 1, "1f339": 1, "1f33a": 1, "1f33b": 1, "1f33c": 1, "1f33d": 1, "1f33e": 1, "1f33f": 1, "1f340": 1, "1f341": 1, "1f342": 1, "1f343": 1, "1f344": 1, "1f345": 1, "1f346": 1, "1f347": 1, "1f348": 1, "1f349": 1, "1f34a": 1, "1f34b": 1, "1f34c": 1, "1f34d": 1, "1f34e": 1, "1f34f": 1, "1f350": 1, "1f351": 1, "1f352": 1, "1f353": 1, "1f354": 1, "1f355": 1, "1f356": 1, "1f357": 1, "1f358": 1, "1f359": 1, "1f35a": 1, "1f35b": 1, "1f35c": 1, "1f35d": 1, "1f35e": 1, "1f35f": 1, "1f360": 1, "1f361": 1, "1f362": 1, "1f363": 1, "1f364": 1, "1f365": 1, "1f366": 1, "1f367": 1, "1f368": 1, "1f369": 1, "1f36a": 1, "1f36b": 1, "1f36c": 1, "1f36d": 1, "1f36e": 1, "1f36f": 1, "1f370": 1, "1f371": 1, "1f372": 1, "1f373": 1, "1f374": 1, "1f375": 1, "1f376": 1, "1f377": 1, "1f378": 1, "1f379": 1, "1f37a": 1, "1f37b": 1, "1f37c": 1, "1f380": 1, "1f381": 1, "1f382": 1, "1f383": 1, "1f384": 1, "1f385": 1, "1f386": 1, "1f387": 1, "1f388": 1, "1f389": 1, "1f38a": 1, "1f38b": 1, "1f38c": 1, "1f38d": 1, "1f38e": 1, "1f38f": 1, "1f390": 1, "1f391": 1, "1f392": 1, "1f393": 1, "1f3a0": 1, "1f3a1": 1, "1f3a2": 1, "1f3a3": 1, "1f3a4": 1, "1f3a5": 1, "1f3a6": 1, "1f3a7": 1, "1f3a8": 1, "1f3a9": 1, "1f3aa": 1, "1f3ab": 1, "1f3ac": 1, "1f3ad": 1, "1f3ae": 1, "1f3af": 1, "1f3b0": 1, "1f3b1": 1, "1f3b2": 1, "1f3b3": 1, "1f3b4": 1, "1f3b5": 1, "1f3b6": 1, "1f3b7": 1, "1f3b8": 1, "1f3b9": 1, "1f3ba": 1, "1f3bb": 1, "1f3bc": 1, "1f3bd": 1, "1f3be": 1, "1f3bf": 1, "1f3c0": 1, "1f3c1": 1, "1f3c2": 1, "1f3c3": 1, "1f3c4": 1, "1f3c6": 1, "1f3c7": 1, "1f3c8": 1, "1f3c9": 1, "1f3ca": 1, "1f3e0": 1, "1f3e1": 1, "1f3e2": 1, "1f3e3": 1, "1f3e4": 1, "1f3e5": 1, "1f3e6": 1, "1f3e7": 1, "1f3e8": 1, "1f3e9": 1, "1f3ea": 1, "1f3eb": 1, "1f3ec": 1, "1f3ed": 1, "1f3ee": 1, "1f3ef": 1, "1f3f0": 1, "1f400": 1, "1f401": 1, "1f402": 1, "1f403": 1, "1f404": 1, "1f405": 1, "1f406": 1, "1f407": 1, "1f408": 1, "1f409": 1, "1f40a": 1, "1f40b": 1, "1f40c": 1, "1f40d": 1, "1f40e": 1, "1f40f": 1, "1f410": 1, "1f411": 1, "1f412": 1, "1f413": 1, "1f414": 1, "1f415": 1, "1f416": 1, "1f417": 1, "1f418": 1, "1f419": 1, "1f41a": 1, "1f41b": 1, "1f41c": 1, "1f41d": 1, "1f41e": 1, "1f41f": 1, "1f420": 1, "1f421": 1, "1f422": 1, "1f423": 1, "1f424": 1, "1f425": 1, "1f426": 1, "1f427": 1, "1f428": 1, "1f429": 1, "1f42a": 1, "1f42b": 1, "1f42c": 1, "1f42d": 1, "1f42e": 1, "1f42f": 1, "1f430": 1, "1f431": 1, "1f432": 1, "1f433": 1, "1f434": 1, "1f435": 1, "1f436": 1, "1f437": 1, "1f438": 1, "1f439": 1, "1f43a": 1, "1f43b": 1, "1f43c": 1, "1f43d": 1, "1f43e": 1, "1f440": 1, "1f442": 1, "1f443": 1, "1f444": 1, "1f445": 1, "1f446": 1, "1f447": 1, "1f448": 1, "1f449": 1, "1f44a": 1, "1f44b": 1, "1f44c": 1, "1f44d": 1, "1f44e": 1, "1f44f": 1, "1f450": 1, "1f451": 1, "1f452": 1, "1f453": 1, "1f454": 1, "1f455": 1, "1f456": 1, "1f457": 1, "1f458": 1, "1f459": 1, "1f45a": 1, "1f45b": 1, "1f45c": 1, "1f45d": 1, "1f45e": 1, "1f45f": 1, "1f460": 1, "1f461": 1, "1f462": 1, "1f463": 1, "1f464": 1, "1f465": 1, "1f466": 1, "1f467": 1, "1f468": 1, "1f469": 1, "1f46a": 1, "1f46b": 1, "1f46c": 1, "1f46d": 1, "1f46e": 1, "1f46f": 1, "1f470": 1, "1f471": 1, "1f472": 1, "1f473": 1, "1f474": 1, "1f475": 1, "1f476": 1, "1f477": 1, "1f478": 1, "1f479": 1, "1f47a": 1, "1f47b": 1, "1f47c": 1, "1f47d": 1, "1f47e": 1, "1f47f": 1, "1f480": 1, "1f481": 1, "1f482": 1, "1f483": 1, "1f484": 1, "1f485": 1, "1f486": 1, "1f487": 1, "1f488": 1, "1f489": 1, "1f48a": 1, "1f48b": 1, "1f48c": 1, "1f48d": 1, "1f48e": 1, "1f48f": 1, "1f490": 1, "1f491": 1, "1f492": 1, "1f493": 1, "1f494": 1, "1f495": 1, "1f496": 1, "1f497": 1, "1f498": 1, "1f499": 1, "1f49a": 1, "1f49b": 1, "1f49c": 1, "1f49d": 1, "1f49e": 1, "1f49f": 1, "1f4a0": 1, "1f4a1": 1, "1f4a2": 1, "1f4a3": 1, "1f4a4": 1, "1f4a5": 1, "1f4a6": 1, "1f4a7": 1, "1f4a8": 1, "1f4a9": 1, "1f4aa": 1, "1f4ab": 1, "1f4ac": 1, "1f4ad": 1, "1f4ae": 1, "1f4af": 1, "1f4b0": 1, "1f4b1": 1, "1f4b2": 1, "1f4b3": 1, "1f4b4": 1, "1f4b5": 1, "1f4b6": 1, "1f4b7": 1, "1f4b8": 1, "1f4b9": 1, "1f4ba": 1, "1f4bb": 1, "1f4bc": 1, "1f4bd": 1, "1f4be": 1, "1f4bf": 1, "1f4c0": 1, "1f4c1": 1, "1f4c2": 1, "1f4c3": 1, "1f4c4": 1, "1f4c5": 1, "1f4c6": 1, "1f4c7": 1, "1f4c8": 1, "1f4c9": 1, "1f4ca": 1, "1f4cb": 1, "1f4cc": 1, "1f4cd": 1, "1f4ce": 1, "1f4cf": 1, "1f4d0": 1, "1f4d1": 1, "1f4d2": 1, "1f4d3": 1, "1f4d4": 1, "1f4d5": 1, "1f4d6": 1, "1f4d7": 1, "1f4d8": 1, "1f4d9": 1, "1f4da": 1, "1f4db": 1, "1f4dc": 1, "1f4dd": 1, "1f4de": 1, "1f4df": 1, "1f4e0": 1, "1f4e1": 1, "1f4e2": 1, "1f4e3": 1, "1f4e4": 1, "1f4e5": 1, "1f4e6": 1, "1f4e7": 1, "1f4e8": 1, "1f4e9": 1, "1f4ea": 1, "1f4eb": 1, "1f4ec": 1, "1f4ed": 1, "1f4ee": 1, "1f4ef": 1, "1f4f0": 1, "1f4f1": 1, "1f4f2": 1, "1f4f3": 1, "1f4f4": 1, "1f4f5": 1, "1f4f6": 1, "1f4f7": 1, "1f4f9": 1, "1f4fa": 1, "1f4fb": 1, "1f4fc": 1, "1f500": 1, "1f501": 1, "1f502": 1, "1f503": 1, "1f504": 1, "1f505": 1, "1f506": 1, "1f507": 1, "1f508": 1, "1f509": 1, "1f50a": 1, "1f50b": 1, "1f50c": 1, "1f50d": 1, "1f50e": 1, "1f50f": 1, "1f510": 1, "1f511": 1, "1f512": 1, "1f513": 1, "1f514": 1, "1f515": 1, "1f516": 1, "1f517": 1, "1f518": 1, "1f519": 1, "1f51a": 1, "1f51b": 1, "1f51c": 1, "1f51d": 1, "1f51e": 1, "1f51f": 1, "1f520": 1, "1f521": 1, "1f522": 1, "1f523": 1, "1f524": 1, "1f525": 1, "1f526": 1, "1f527": 1, "1f528": 1, "1f529": 1, "1f52a": 1, "1f52b": 1, "1f52c": 1, "1f52d": 1, "1f52e": 1, "1f52f": 1, "1f530": 1, "1f531": 1, "1f532": 1, "1f533": 1, "1f534": 1, "1f535": 1, "1f536": 1, "1f537": 1, "1f538": 1, "1f539": 1, "1f53a": 1, "1f53b": 1, "1f53c": 1, "1f53d": 1, "1f550": 1, "1f551": 1, "1f552": 1, "1f553": 1, "1f554": 1, "1f555": 1, "1f556": 1, "1f557": 1, "1f558": 1, "1f559": 1, "1f55a": 1, "1f55b": 1, "1f55c": 1, "1f55d": 1, "1f55e": 1, "1f55f": 1, "1f560": 1, "1f561": 1, "1f562": 1, "1f563": 1, "1f564": 1, "1f565": 1, "1f566": 1, "1f567": 1, "1f5fb": 1, "1f5fc": 1, "1f5fd": 1, "1f5fe": 1, "1f5ff": 1, "1f600": 1, "1f601": 1, "1f602": 1, "1f603": 1, "1f604": 1, "1f605": 1, "1f606": 1, "1f607": 1, "1f608": 1, "1f609": 1, "1f60a": 1, "1f60b": 1, "1f60c": 1, "1f60d": 1, "1f60e": 1, "1f60f": 1, "1f610": 1, "1f611": 1, "1f612": 1, "1f613": 1, "1f614": 1, "1f615": 1, "1f616": 1, "1f617": 1, "1f618": 1, "1f619": 1, "1f61a": 1, "1f61b": 1, "1f61c": 1, "1f61d": 1, "1f61e": 1, "1f61f": 1, "1f620": 1, "1f621": 1, "1f622": 1, "1f623": 1, "1f624": 1, "1f625": 1, "1f626": 1, "1f627": 1, "1f628": 1, "1f629": 1, "1f62a": 1, "1f62b": 1, "1f62c": 1, "1f62d": 1, "1f62e": 1, "1f62f": 1, "1f630": 1, "1f631": 1, "1f632": 1, "1f633": 1, "1f634": 1, "1f635": 1, "1f636": 1, "1f637": 1, "1f638": 1, "1f639": 1, "1f63a": 1, "1f63b": 1, "1f63c": 1, "1f63d": 1, "1f63e": 1, "1f63f": 1, "1f640": 1, "1f645": 1, "1f646": 1, "1f647": 1, "1f648": 1, "1f649": 1, "1f64a": 1, "1f64b": 1, "1f64c": 1, "1f64d": 1, "1f64e": 1, "1f64f": 1, "1f680": 1, "1f681": 1, "1f682": 1, "1f683": 1, "1f684": 1, "1f685": 1, "1f686": 1, "1f687": 1, "1f688": 1, "1f689": 1, "1f68a": 1, "1f68b": 1, "1f68c": 1, "1f68d": 1, "1f68e": 1, "1f68f": 1, "1f690": 1, "1f691": 1, "1f692": 1, "1f693": 1, "1f694": 1, "1f695": 1, "1f696": 1, "1f697": 1, "1f698": 1, "1f699": 1, "1f69a": 1, "1f69b": 1, "1f69c": 1, "1f69d": 1, "1f69e": 1, "1f69f": 1, "1f6a0": 1, "1f6a1": 1, "1f6a2": 1, "1f6a3": 1, "1f6a4": 1, "1f6a5": 1, "1f6a6": 1, "1f6a7": 1, "1f6a8": 1, "1f6a9": 1, "1f6aa": 1, "1f6ab": 1, "1f6ac": 1, "1f6ad": 1, "1f6ae": 1, "1f6af": 1, "1f6b0": 1, "1f6b1": 1, "1f6b2": 1, "1f6b3": 1, "1f6b4": 1, "1f6b5": 1, "1f6b6": 1, "1f6b7": 1, "1f6b8": 1, "1f6b9": 1, "1f6ba": 1, "1f6bb": 1, "1f6bc": 1, "1f6bd": 1, "1f6be": 1, "1f6bf": 1, "1f6c0": 1, "1f6c1": 1, "1f6c2": 1, "1f6c3": 1, "1f6c4": 1, "1f6c5": 1, "203c": 1, "21a9": 1, "21aa": 1, "23-20e3": 1, "231a": 1, "231b": 1, "23e9": 1, "23ea": 1, "23eb": 1, "23ec": 1, "23f0": 1, "23f3": 1, "24c2": 1, "25aa": 1, "25ab": 1, "25b6": 1, "25c0": 1, "25fb": 1, "25fc": 1, "25fd": 1, "25fe": 1, "260e": 1, "261d": 1, "263a": 1, "264a": 1, "264b": 1, "264c": 1, "264d": 1, "264e": 1, "264f": 1, "267b": 1, "267f": 1, "26a0": 1, "26a1": 1, "26aa": 1, "26ab": 1, "26bd": 1, "26be": 1, "26c4": 1, "26c5": 1, "26ce": 1, "26d4": 1, "26ea": 1, "26f2": 1, "26f3": 1, "26f5": 1, "26fa": 1, "26fd": 1, "270a": 1, "270b": 1, "270c": 1, "270f": 1, "274c": 1, "274e": 1, "27a1": 1, "27b0": 1, "27bf": 1, "2b05": 1, "2b06": 1, "2b07": 1, "2b1b": 1, "2b1c": 1, "2b50": 1, "2b55": 1, "30-20e3": 1, "303d": 1, "31-20e3": 1, "32-20e3": 1, "33-20e3": 1, "34-20e3": 1, "35-20e3": 1, "36-20e3": 1, "37-20e3": 1, "38-20e3": 1, "39-20e3": 1, "a9": 1, "ae": 1, "e50a": 1 };
        static U200D = String.fromCharCode(0x200D);//不可见字符 零宽度连接符 用于了解多个emoji组合的新的emoji
        static UFE0Fg = /\uFE0F/g;//ariation Selector，「变体选择符」，或「异体选择符」能把某些文字（如数字、骷髅符号、雌/雄符号 等）变成绘文字
        static fastChatHash = {
            img1: "icon_emoji_scared.png",
            img2: "icon_emoji_like.png",
            img3: "icon_emoji_laugh.png",
            img4: "icon_emoji_love.png",
            img5: "icon_emoji_unlike.png",
            img6: "icon_emoji_coma.png",
            img7: "icon_emoji_angry.png",
            img8: "icon_emoji_cry.png",
            img9: "icon_emoji_cool.png",
            img10: "icon_emoji_fist.png"
        }
    
        static replace(text, size: number = null): string {//
            var self = this;
            return String(text).replace(this.re, function (rawText) {
                var iconId = self.grabTheRightIcon(rawText);
                if (self.emojiHash[iconId]) {
                    if (size) return `<img style="height:${size};width:${size};" src="emoji/36x36/${iconId}.png"/>`;
                    return `<img src="emoji/36x36/${iconId}.png"/>`;
                }
                return "□";
                // return "⍰";
            });
        }
        static toCodePoint(emojiUnicode: string, sep: string = "-"): string {//将emoji拆分为Unicode 16进制编码组成的文件名 sep分隔符默认"-"
            var
                r = [],
                c = 0,
                p = 0,
                i = 0;
            while (i < emojiUnicode.length) {
                c = emojiUnicode.charCodeAt(i++);//返回指定位置的unicode编码 
                if (p) {
                    r.push((0x10000 + ((p - 0xD800) << 10) + (c - 0xDC00)).toString(16));
                    p = 0;
                } else if (0xD800 <= c && c <= 0xDBFF) {
                    p = c;
                } else {
                    r.push(c.toString(16));
                }
            }
            return r.join(sep);
        }
        static grabTheRightIcon(rawText) {//没有等宽连接符的时候 去除多余的变体选择符
            return this.toCodePoint(rawText.indexOf(this.U200D) < 0 ?
                rawText.replace(this.UFE0Fg, '') :
                rawText
            );
        }
        static onverterFastChat(str: string) {//兼容原生的快捷表情 替换为游戏中可以识别的内容
            return String(str).replace(/(^img)(\d+$)/gm, (s) => {
                return this.fastChatHash[s] || ""
            })
        }
        //游戏内的快捷短语表情名字转成原生需要的名字
        static reConverterFastChat(str: string) {
            var resultList = yalla.util.findKey(str, yalla.Emoji.fastChatHash);
            if (resultList && resultList.length > 0) return resultList[0];
            return str;
        }

        static replaceYlEmoji(str: string, size: number = null) {
            var re = /\[(A.\d{2})\]/gm;
            return String(str).replace(re, function (nm) {
                if (size) {
                    return ` <img style="height:${size};width:${size};" src="ylemoji/${nm}.png"/> `
                }
                return ` <img src="ylemoji/${nm}.png"/> `;
            })
        }
        static charToUnicode(str: string) {
            return str.codePointAt(0).toString(16);
        }
        // static replace(text, callback) {
        //     return String(text).replace(this.re, callback);
        // }
        static createHtmlString(str: string, size: number = null): string {
            if (str && typeof str == "string")
                return this.replaceYlEmoji(this.replace(str, size), size);
            return "";
        }

        static filterStr(str: string): Array<string> {
            var words = [];
            var texLen = str.length;
            var t: string = '';
            for (var i = 0; i < texLen; i++) {
                if (str[i].search(yalla.Emoji.re) >= 0) {
                    if (!!t) {
                        words.push(t);
                        t = '';
                    }
                    words.push(str[i]);
                    continue;
                }
                var character = str.charAt(i);
                var code = str.charCodeAt(i);
                if (code >= 0xD800 && code <= 0xDBFF) {
                    var char = String(character + str.charAt(++i));
                    if (char.search(this.re) >= 0) {
                        if (t.length > 0) {
                            words.push(t);
                            t = '';
                        }
                        var ucode = yalla.Emoji.charToUnicode(char);
                        if (this.emojiHash[ucode])
                            words.push(char);
                        else words.push("□")
                    } else {
                        t += char;
                    }
                } else {
                    t += character;
                }
            }
            if (t.length > 0) {
                words.push(t);
            }
            return words;
        }
        static lateLable(str: string, lb: Laya.Label, lbProp: Array<string> = ['color', 'fontSize'], sameWidth: boolean = true) {
            lb.removeChildByName('late');
            if (str.search(yalla.Emoji.re) < 0) {
                if(lb['_tf']) lb['_tf']['isLangPacks'] = false;
                lb.text = str;
                return null;
            }
            var sp: Laya.Sprite = new Laya.Sprite();
            var spWid: number = 0;
            sp.name = "late";
            lb.text = '';
            var [x, y] = [0, 0];
            var allStoke: number = 0;
            var strNums = 0;
            this.filterStr(str).forEach((val: string, index: number) => {
                if (val.search(yalla.Emoji.re) >= 0) {
                    var ucode = yalla.Emoji.charToUnicode(val);
                    var src = "emoji/36x36/" + ucode + ".png";
                    var img: Laya.Image = new Laya.Image(src);
                    var fontsize: number = lb.fontSize;
                    img.size(fontsize, fontsize);
                    sp.addChild(img);
                    img.pos(x + allStoke, y);
                    x += img.width + allStoke;
                    spWid += img.width;
                    allStoke = 0;
                    strNums = 0;
                } else {
                    var lbc: Laya.Label = new Laya.Label();
                    for (var i = 0; i < lbProp.length; i++) {
                        lbc[lbProp[i]] = lb[lbProp[i]];
                    }
                    strNums = val.length;
                    if (val.isArabic(0)) {
                        if (!lb.stroke) strNums *= -1;
                        else strNums = 0;
                    }
                    lbc.text = val;
                    sp.addChild(lbc);
                    lbc.pos(x, y);
                    var width = lbc.width/* = this.getTextWidth(val)*/;
                    x += width;
                    spWid += width;
                    if (!lb.stroke) {
                        allStoke = strNums;
                    } else {
                        allStoke = lb.stroke * strNums;
                    }
                }
            });
            sp.width = spWid;
            if (lb.align == 'center') {
                sp.x = (lb.width - sp.width) / 2;
            } else if (lb.align == 'right') {
                sp.x = lb.width - sp.width;
            }
            lb.addChild(sp);
            if (sameWidth) lb.width = sp.width;
            return [lb, sp];
        }

        static getTextWidth(text) {
            return Laya.Browser.context.measureText(text).width - Laya.Browser.context.measureText(text.removeNonArabic()).width / 2;
        }
    }
}