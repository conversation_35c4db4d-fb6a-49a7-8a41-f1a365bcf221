module yalla {
    export class File extends Laya.EventDispatcher {
        static success: boolean = false;
        static folder: string = "";//系统储存根目录
        static relativePath: string = "";//自定义创建的文件夹
        static imgFloder = "imgCache";//
        static cachePath: string = "";//完整路径
        static window = laya.utils.Browser.window;
        static md5 = new laya.utils.Browser.window.md5();
        static loadedHash = {};
        static getFileByNativeHash = {};
        static loadStack = [];
        static filePath = "";
        static init() {
            if (this.window.conch) {
                this.folder = this.window.conch.getCachePath() + "/";
                this.relativePath = Laya.LocalStorage.getItem("relativePath") || "relativePath11";
                this.cachePath = this.folder + this.relativePath;
                this.filePath = "file://" + (yalla.File.cachePath + "/").replace(new RegExp('//', 'g'), "/");
                if (!this.existed(this.folder + this.imgFloder)) {
                    this.fs_mkdir(this.imgFloder);
                }
            }
            yalla.Native.instance.on("downloadFileByUrl", this, this.getFileByNativeCallBack);
        }
        static downloadImgByUrl(headUrl: string, cb: Function, defaultUrl: string = "public/default_head.png") {
            // if (url.indexOf("file.yalla.games") > -1) {
            //     cb && cb(url);
            //     return;
            // }
            var url = yalla.DomainUtil.instance.getFileDomainHeadType(headUrl);
            // yalla.Debug.log(yalla.DomainUtil.instance.game_common_url+"===头像资源下载0=downloadImgByUrl===url=" + url);

            var fileName = this.md5.hex_md5(url).toUpperCase() + ".png";
            var path = this.folder + this.imgFloder + "/" + fileName;
            if (this.existed(path)) {
                var localPath = "file://" + path.replace(new RegExp('//', 'g'), "/");
                cb && cb(localPath);
                yalla.Debug.log("本地已下载的文件"+localPath)
                yalla.Debug.log("url"+url)
                return;
            }
            var isLoading = false;
            this.loadStack.forEach(l => {
                if (l.fileName == fileName) {
                    isLoading = true;
                }
            })
            this.loadStack.push({ fileName: fileName, callback: cb });
            if (!isLoading) {
                yalla.Native.instance.downloadImgByUrl(this.imgFloder, url, (data) => {
                    if (data && data.success) {
                        var fN = this.md5.hex_md5(data.url).toUpperCase() + ".png";
                        var loades = 0;
                        this.loadStack.forEach((val, index) => {
                            if (val.fileName == fN) {
                                var path = this.folder + this.imgFloder + "/" + fN;
                                var localPath = "file://" + path.replace(new RegExp('//', 'g'), "/");
                                val.callback && val.callback(localPath);
                                val.callback = null;
                            }
                            if (!val.callback) {
                                loades++;
                            }
                        })
                        if (loades == this.loadStack.length) {
                            this.loadStack = [];
                        }
                    } else {
                        cb && cb(defaultUrl);
                    }
                });
            }
        }
        static groupLoad(url: string, hander: Laya.Handler, group: string = "PLAYER_HEAD") {
            Laya.loader.load(url, hander, null, Laya.Loader.IMAGE, 1, true, group);
        }
        static getFileByNative(fileName: string) {//下载游戏资源 通过事件downloadFileByUrl 返回在getFileByNativeCallBack中装载,后再通过文件名抛出事件
            var path = this.cachePath + "/" + fileName;
            if (this.existed(path)) {
                var localPath = "file://" + path.replace(new RegExp('//', 'g'), "/");
                // yalla.Debug.log(localPath+'-----Yalla.File getFileByNative 00------fileName: '+ fileName);
                this.loadRes(localPath, fileName, () => {
                    yalla.event.YallaEvent.instance.event(fileName, { success: true, localPath: localPath });
                });
                return;
            } else if (this.getFileByNativeHash[fileName]) { return; }
            this.getFileByNativeHash[fileName] = true;
            // yalla.Debug.log('-----Yalla.File getFileByNative 11------fileName: '+ fileName);
            yalla.Native.instance.downloadFileByFileName(this.relativePath, yalla.Skin.instance.getSkinUrlByName(fileName), null);
        }
        static getFileByNativeCallBack(data) {
            if (data && data.success) {
                var fN = (data.url as string).slice(data.url.lastIndexOf("/") + 1);

                var localPath = "file://" + (this.cachePath + "/" + fN).replace(new RegExp('//', 'g'), "/");
                //TODO::2023-12.27 备用域名
                if (fN.indexOf('skin') >= 0 && fN.indexOf('.json') >= 0) localPath = "file://" + (`${this.folder}skinCache/${fN}`).replace(new RegExp('//', 'g'), "/");

                yalla.Debug.log("File 下载成功回调:" + fN + ' localPath:'+localPath);
                this.loadRes(localPath, fN, () => {
                    yalla.Debug.log("event:" + localPath);
                    yalla.event.YallaEvent.instance.event(fN, { success: true, localPath: localPath });
                    this.getFileByNativeHash[fN] = false;
                    delete this.getFileByNativeHash[fN];
                });
            } /*else {
                try {
                    yalla.Debug.log("下载失败回调:" + JSON.stringify(data));
                } catch (error) {
                }
            }*/
        }
        static loadRes(localPath: string, filename: string, cb = null) {
            if (this.loaded(filename) || filename.indexOf(".sk") >= 0) {
                !!cb && cb(localPath);
                return;
            }
            Laya.loader.load(localPath, Laya.Handler.create(this, () => {
                this.loadedHash[filename] = true;
                !!cb && cb(localPath);
            }), null, null, 1, true, "ludo_file");
        }
        static loaded(filename: string): boolean {
            return !!this.loadedHash[filename];
        }
        /**
         * 文件或文件夹是否存在
         * @param cachepath 绝对路径
         */
        static existed(cachepath: string): boolean {
            // yalla.Debug.log('----existed------cachepath: ' + cachepath);
            return !!this.window.fs_exists ? this.window.fs_exists(cachepath) : false;
        }
        /**
         * 创建文件夹
         * @param folderName 文件夹名称
         * @param path 路径(默认储存根目录)
         */
        static fs_mkdir(folderName: string, path: string = this.folder) {
            // yalla.Debug.log('-----Yalla.File.fs_mkdir----' + folderName+': '+path);
            this.window.fs_mkdir && this.window.fs_mkdir(path + folderName);
        }
        static fs_rmDir(folderName: string, path: string = this.folder) {
            this.window.fs_rmDir && this.window.fs_rmDirSync(path + folderName);
        }
        static loadFile(url: string, path: string, onComplete: Laya.Handler, onError: Laya.Handler = null) {
            var f = new this.window.conch_File(url);
            var fr = new this.window.conch_FileReader();
            fr.setIgnoreError(true);
            fr.onload = () => {
                if (fr.result) {
                    this.window.fs_writeFileSync(path, fr.result);
                    onComplete && onComplete.runWith("file://" + path.replace(new RegExp('//', 'g'), "/"));
                }
                else {
                    onError && onError.runWith("no data");
                }
            };
            // fr.onprogress = onprog;
            fr.onerror = function (e): void {
                onError && onError.runWith(e);
            };
            fr.readAsArrayBuffer(f);
        }
        static clearCache() {
            this.getFileByNativeHash = {};
            this.loadedHash = {};
            this.loadStack = [];
            // yalla.Debug.log(Laya.Loader.groupMap);
            Laya.Loader.clearResByGroup("ludo_file");
            Laya.Loader.clearResByGroup("PLAYER_HEAD");
            // yalla.Debug.log("释放yalla File中加载的纹理");
            // yalla.Debug.log(Laya.Loader.groupMap);
        }
        // static getFile(fileName: string, onComplete: Laya.Handler, onError: Laya.Handler = null): boolean {
        //     var path = this.cachePath + "/" + fileName;
        //     var had: boolean = false;
        //     if (this.window.conch) {
        //         had = this.existed(path)
        //         if (had) {
        //             var localPath = "file://" + path.replace(new RegExp('//', 'g'), "/");
        //             this.loadRes(localPath, fileName, () => {
        //                 onComplete && onComplete.runWith(localPath);
        //             });
        //         } else {
        //             this.loadFile(yalla.getNetRes(fileName), path, Laya.Handler.create(this, (localPath) => {
        //                 this.loadRes(localPath, fileName, () => {
        //                     onComplete && onComplete.runWith(localPath);
        //                 });
        //             }));
        //         }
        //     } else {
        //         this.loadRes(yalla.getNetRes(fileName), fileName, () => {
        //             onComplete && onComplete.runWith(localPath);
        //         });
        //     }
        //     return had;
        // }
    }
}