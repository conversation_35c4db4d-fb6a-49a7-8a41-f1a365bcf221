module yalla {
    export class Font {
        static _lan: string = "en";
        static lanIndex: number = 1;
        static numberBmpFont: Laya.BitmapFont = null;

        static init(callBack: Laya.Handler = null) {
            yalla.Native.instance.currentLanguageType((lan: number) => {
                this.lanIndex = lan;
                yalla.Font.lan = ["en", "ar", "indy", "urdu"][lan - 1];
                callBack && callBack.run();
            });

            // if (yalla.Native.instance.deviceType == DeviceType.IOS) {
            //     yalla.Debug.log('===加载游戏包内的字体文件===');
            //     var layattf: ArrayBuffer = Laya.loader.getRes(yalla.getTTF("layabox"));
            //     if (layattf && laya.utils.Browser.window.conch) {
            //         laya.utils.Browser.window.conch.setFontFaceFromBuffer("Montserrat", layattf);
            //         Laya.Font.defaultFamily = "Montserrat";
            //         Laya.Font.defaultFont = "12px Montserrat";

            //         // if (yalla.Font.lan == 'ar') {
            //         //     var lb: Laya.HTMLDivElement = new Laya.HTMLDivElement();
            //         //     lb.style.fontSize = 22;
            //         //     lb.style.valign = 'center';
            //         //     lb.style.wordWrap = true;
            //         //     lb.style.width = 540;
            //         //     lb.innerHTML = '你好';
            //         //     lb.innerHTML = 'فتح الدردشة';
            //         // }
            //     }
            // }
            // yalla.Debug.log(Laya.Font.defaultFont + "=====0游戏内默认字体0========" + Laya.Font.defaultFamily);
        }

        static set lan(b: string) {
            this._lan = b;
            if (b != "en") {
                laya.display.Text.langPacks = Laya.loader.getRes(yalla.getLang(b));
            } else {
                laya.display.Text.langPacks = null;
            }
        }
        static get lan(): string {
            return this._lan;
        }
        static isRight(): boolean {
            return this.lan == "ar" || this.lan == "urdu";
        }

        static initBmpFont(callBack: Laya.Handler = null): void {
            var mBitmapFont = new Laya.BitmapFont();
            // 加载字体资源, 如果是热更资源
            mBitmapFont.loadFont('bmpfont/number.fnt', new Laya.Handler(this, (font) => {
                // 创建并注册字体
                mBitmapFont.setSpaceWidth(10);
                yalla.Font.numberBmpFont = mBitmapFont;
                // Laya.Text.registerBitmapFont(fontName, mBitmapFont);
                callBack && callBack.run();
            }));
            // //TODO 因为jackaro 和 domino 都需要加载使用 bmfont，这里不再统一加载， 放在每个游戏的main 中加载
            // callBack && callBack.run();
        }


        // /**资源加载与字体注册*/
        // static loadBitmapFont(fontName:string = "", fontUrl = "", callBack = null): void {
        //     var mBitmapFont = new Laya.BitmapFont();
        //     // 加载字体资源
        //     mBitmapFont.loadFont(fontUrl, new Laya.Handler(this, (font) => {
        //         // 创建并注册字体
        //         mBitmapFont.setSpaceWidth(10);
        //         yalla.Font.numberBmpFont = mBitmapFont;
        //         // Laya.Text.registerBitmapFont(fontName, mBitmapFont);
        //         callBack && callBack();
        //     }));
        // }
    }
}