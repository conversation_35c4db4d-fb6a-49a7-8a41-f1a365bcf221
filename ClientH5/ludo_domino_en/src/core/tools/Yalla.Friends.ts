module yalla {
    export class Friends {
        /**
         * 好友缓存
         */
        private static _friends_cache:Array<FPlayerShowInfo> = [];
        public static isGetFriendList:boolean = false;              //是否已经获取好友列表
        public static hasSendFriendHash:Object = {};                //发送好友请求的记录

        static get friends_cache(): Array<FPlayerShowInfo> {
            return this._friends_cache;
        }
        static set friends_cache(value: Array<FPlayerShowInfo>) {
            this.isGetFriendList = true;
            this._friends_cache = value;
        }
        static get online(): Array<FPlayerShowInfo> {
            var result: Array<FPlayerShowInfo> = [];
            for (let i = 0; i < this._friends_cache.length; i++) {
                if (this._friends_cache[i].placeId == 0) {
                    result.push(this._friends_cache[i]);
                }
            }
            return result;
        }
        static isFriend(idx: number): boolean {
            if (!!this._friends_cache) {
                for (let i = 0; i < this._friends_cache.length; i++) {
                    if (this._friends_cache[i].idx == idx) {
                        return true;
                    }
                }
                return false;
            } else {
                return false;
            }
        }
    }
}