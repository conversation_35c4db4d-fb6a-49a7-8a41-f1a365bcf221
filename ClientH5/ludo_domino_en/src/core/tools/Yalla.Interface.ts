interface Param {
    progress?: Function,
    error?: Function,
    success?: Function,
    url: string,
    data?: Object,
    method?: string,
    responseType?: string
}
interface countryItem {
    areaCode: string,
    flagUrl: string,
    id: number,
    name: string,
    shortName: string,
}
interface avatarItem {
    avatar: string,
    id: number
}
interface levelItem {
    id: number,
    name: string,
    minExperience: number,
    maxExperience: number,
    icon: any,
    isMax: boolean
}
interface String {
    howArabic(): number,
    howNotArabic(): number,
    isArabic(threshold): boolean,
    hasArabic(): boolean,
    removeTashkel(): string,
    removeNonArabic(): string,
    removeArabic(): string,
    codePointAt(e): number,
    hasHindi(): boolean,
    howHindi(): number,
    removeNonHindi(): string,
    matchArabic(): Array<string>,
    getArabicWidth(): number,
    matchRectifyWord(): Array<string>,
    rectifyWidth(): number,
    rectifyWidthHtml(): number,
    hasBlockWord(): boolean,
    getByteLen(): number,
    subYlEmoji(n?, byteLen?): string,
    sliceByByte(n?): string
}
enum DeviceType {
    Android = 0,
    IOS = 1,
    Browser = 2
}
interface playerShowInfo {
    fPlayerInfo: FPlayerShowInfo,
    score: number,
    isSignUp: number,
    sitNum: number,
    isLeft: number,
    winIndex: number,
    diceNum?: Array<any>,
    buff?: Array<any>,
    isFighted: number,
    isInSysTrust: number,
    isVoice?: boolean,
    reThrowNum?: number,
    reThrowCost?: number,
    color?: number,
    diceSkinId?: number,//骰子皮肤
    talkSkinId?: number,//气泡皮肤
    chessSkinId?: number,//棋子皮肤
    segId?: number,//段位 
    starNum?: number,//传奇之星
    prettyId: number,//显示id
    isInChatOff?: number,//0不屏蔽  1屏蔽
    openChatOff?: number,//0不屏蔽  1屏蔽
    isQuit?: number,//0未主动离开  1主动离开
    roylevel: number,
    royVisibility?: boolean,//皇室等级可见性,false:所有人可见，true：仅自己可见
    giftId?: number,// 别人赠送的互动礼物ID
    playColor?: number,//玩家颜色 0 红 3：蓝色
    cardSkinId?: number,//卡牌皮肤
    sceneId?: number,//背景皮肤
    fightNum?: number,//击杀数量
    fightLastTime?: number,//最后一次击杀的时间
    winMoney?: number,//ludo结算奖励
    propNum?: number//结算道具
    realRoyLevel?: number,// 真实的玩家皇室等级
    royalLevelNameAnimation?: boolean // rl扫光开关 1.4.0添加
}
interface FPlayerShowInfo {
    country: number,
    faceId: number,
    faceUrl: string,
    ftype: number,
    idx: number,
    level: number,
    nikeName: string,
    placeId: number,
    viplevel: number,
    winCount: number,
    totalCount: number
}
interface palyerInfo {
    playerShowInfo: playerShowInfo,
    gold: number,
    money: number,
    isRecharged: boolean,//是否首充
    exp: number
}
interface AccountModel {
    idx?: number,//账号
    token?: string,
    encryptToken?: string,//加密token
    name?: string,//名称
    avatar?: string,//头像
    level?: number,//等级
    vipLevel?: number,//VIP等级
    roylevel?: number,//royal等级
    realRoyLevel?: number,// 真实的玩家皇室等级
    autoAddExp?: number,// 托管自动加经验 托管加经验 0 可以加  1 不可以加
    language?: number,//语言
    currentExperience?: number,//当前经验
    levelMaxExperience?: number,//当前经验最大值
    currentGoldNum?: number,//金币
    currentDiamondNum?: number,//钻石
    nationalName?: string,//国家名称
    nationalIcon?: string,//国家国旗地址
    host?: string,
    port?: string,
    ip?: string,
    url?: string,
    socketUrlList?: Array<string>,
    standby?: string,      //备用服务器地址 废弃
    onlineNum?: number,
    type?: number,
    roomid?: number,
    gameId?: number,
    isOffGame?: boolean,
    royalty?: number,
    novice?: number,
    rankPercentList?: Array<Object>,
    isPrivate?: number,                      /**0公开  1私密  3锦标赛 4Vip房间 5组队 6黑夜模式 7黑夜组队  8联赛  9duel模式 10丛林模式*/
    championshipId?: number,                 //锦标赛id
    isLimitVisitor?: boolean,                  //是否限制游客
    banTalkData?: string,            // 禁言数据,没禁言时为空串
    needBackHall?: boolean,
    version?: string,//客户端版本号
    voiceType?: VoiceType,//语音房间类型
    androidVersion?: number,//android 版本
    leagueWatch?: boolean,//是否联赛观战
    league?: Object,//联赛相关
    propImgUrl?: string,//道具地址
    connectType?: number,//新协议类型，2是domino新协议
    fixedRouteKey?: string,//固定路由key
    pokerConfigId?: string,//游戏后台指定特定牌配置
    chessPanelConfigId?: number,//游戏后台指定特定棋盘配置
    devGameType?: number,//需要的模式,只在开发环境有效
    sameSkinBuySwitch: number
}
interface GateWayModel {
    sign?: string,
    deviceId?: string,
    channelId?: number,
    version?: string,
    timestamp?: number
}
interface chessEventMsg {
    type: ChessMoveType,
    chessId: number,
    chessLinkId?: number;
}
enum chessEvent {
    None = 0,//正常
    GetLink = 1,//产生叠子
    Fight = 2,//发生战斗
    Win = 3,//到达终点
    TRANSLOCATION = 4,//位移
    GET_BUFF = 5,//获得buff
    USE_BUFF = 6,//获得buff
    ReleaseLink = 7//取消叠子
}

enum PlayerTurnStatus {
    THROW_START = 0,//开始投掷
    THROW_END = 1,//投掷结束
    RETHROW_START = 2,//是否重置开始
    RETHROW_END = 3,//是否重置结束
    CHOOSECHESS_START = 4,//选择数字和棋子开始
    CHOOSECHESS_END = 5,//选择数字和棋子结束
    CHESSMOVE_START = 6,//棋子移动开始
    CHESSMOVE_END = 7,//棋子移动结束
    PLAYER_OUT = 8,//玩家退出
    PLAYER_WIN = 9,//玩家胜利
    GAME_OVER = 10//游戏结束
}

//棋子位移类型
enum ChessMoveType {
    MOVE_NORMAL = 0,//骰子点数移动
    MOVE_BE_HIT = 1,//撞击移动
    MOVE_WIND = 2,//龙卷风移动
    MOVE_RAINBOW = 3,//向后龙卷风移动
    MOVE_ROCKET = 4,//火箭移动
    MOVE_ARROW = 5,//箭头移动
    MOVE_TIGER = 6,//老虎移动
    MOVE_FORWARD_WIND = 7,//向前的风,移动
    MOVE_BACKWARD_WIND = 8, //向后的风,移动
    MOVE_TIGER_HIT = 9,//老虎撞击移动
    MOVE_HOME = 10,//前端玩家被离开时移动类型
}
//服务中触发的事件
class SERVICE_EVENT {
    static GAME = "game";//游戏的状态
    static PLAYER = "player";//游戏玩家的状态 PlayerTurnStatus
    static CHESS = "chess";//棋子在地图中发生的事件（正常、叠字、取消叠字、战斗等）chessEvent
    static MOVE = "move";//棋子移动的消息事件（正常移动、撞击移动）ChessMoveType
    static FLUSH_PROP = "flush_prop";//刷新道具
}

enum Buff {
    BUFF_NONE = 0,//无
    SHIELD = 1,//护盾
    DOUBLLE_DICE = 2,//两次骰子
    LUCK_DICE = 3,//幸运骰子
    SWOON = 4,//休眠一回合
    FLARE = 5,//小照明弹buff
    EXTRA_DICE = 6,//额外骰子
    SNAIL_DICE = 7,//蜗牛骰子
    GRID_SHIELD = 8,//丛林格子护盾
}

enum Props {
    PROP_WIND = 0,//龙卷风
    PROP_RAINBOW = 1,//彩虹
    PROP_THUNDERSTORM = 2,//雷雨
    PROP_SHIELD = 3,//护盾
    PROP_DOUBLLE_DICE = 4,//两次骰子
    PROP_LUCK_DICE = 5, //幸运骰子
    PROP_ROCKET = 6,//火箭
    PROP_FLARE = 7,//小照明弹道具
    PROP_LIGHTNING = 8,//闪电 1.3.9 丛林玩法
    PROP_TIGER = 9,//老虎 1.3.9 丛林玩法
    PROP_EXTRA_DICE = 10,//额外骰子 1.3.9 丛林玩法
    PROP_GRID_SHIELD = 11, //格子护盾(棋子移动后消失) 1.3.9 丛林玩法
    PROP_SNAIL = 12,//蜗牛 1.3.9 丛林玩法
    PROP_WIND_DIRECTION = 13,//风向  1.3.9 丛林玩法
}

enum GameType {
    LUDO = 10019, //ludo
    DOMINO = 10021, //多米诺
    SNAKEANDLADER = 10020, //蛇棋
    SHEEP = 10022, //顶🐏
    JACKARO = 10023, //杰克罗
}
interface playAgainArg {
    gamePay: number,//房间赔率
    gameId: number,//游戏ID
    gameType: number,//游戏模式
    playerNum: number,//玩家数量
    roomId?: number,//房间号
    gameGroup?: number//游戏类型 仅ludo 0普通 1道具
    isPrivate?: number
}
class buryPoint {
    static DOMINO_PRIVATE_PLAYING = "Domino_Private_Playing";
    static DOMINO_PUBLIC_PLAYING = "Domino_Public_Playing";
    static DOMINO_SETTLE = "Domino_Settle";
    static LUDO_PRIVATE_PLAYING = "Ludo_Private_Playing";
    static LUDO_PUBLIC_PLAYING = "Ludo_Public_Playing";
    static LUDOSETTLE = "Ludo_Settle";
    //1.1.8新增
    static GAME_USER = "game_user";//点击参与游戏的用户头像
    static GAME_USER_ADD = "game_user_add";//用户头像_点击添加好友
    static GAME_USER_MUTE = "game_user_mute";//用户头像_点击mute
    static GAME_USER_UNMUTE = "game_user_unmute";//用户头像_点击unmute
    static GAME_USER_QUIETMIC = "game_user_Quietmic";//点击静对方麦
    static GAME_USER_OPENMIC = "game_user_openmic";//点击开对方的麦
    static GAME_EXPRESSION = "game_expression";//点击表情
    static GAME_MESSAGE = "game_message";//点击消息
    static GAME_EXIT_CONFIRM = "game_exit_confirm";//中途退出 点击confirm
    static GAME_EXIT_CANCEL = "game_exit_cancel";//中途退出 点击cancle
    static GAME_REWARD = "game_reward";//点击reward
    static GAME_SETTING = "game_Setting";//点击设置
    static GAME_SETTING_OPENSOUND = "game_Setting_OpenSound"//设置_打开音效
    static GAME_SETTING_CLOSESOUND = "game_Setting_CloseSound";//设置_关闭音效
    static GAME_SETTING_RULES = "game_Setting_Rules";//设置_查看规则
    static GAME_BACK = "game_Back"//点击结果弹窗上的back

    static GAME_SPECTATER = "game_Spectater";//点击围观
    static GAME_SPECTATER_USER = "game_Spectater_User";//点击围观的用户头像
    static GAME_SPECTATER_QUIEMIC = "game_Spectater_QuieMic";//围观静音玩家
    static GAME_SPECTATER_OPENEMIC = "game_Spectater_OpeneMic";//围观开启玩家声音
    static GAME_SPECTATER_MUTE = "game_Spectater_Mute"//围观屏蔽玩家
    static GAME_SPECTATER_UNMUTE = "game_Spectater_Unmute";//围观取消屏蔽玩家
    static GAME_SPECTATER_EMOJI = "game_Spectater_Emoji";//围观发送表情
    static GAME_SPECTATER_MESSAGE = "game_Spectater_message";//围观发送聊天
    static GAME_MUTESPECTATER = "game_MuteSpectater";//玩家_mute围观者
    static GAME_UMUTESPECTATER = "game_UmuteSpectater";//玩家_unmute围观者

    static GAME_BREAK = "game_break";//服务端返回的断线提示的消息数
    static GAME_BREAK_CONNECT = "game_break_connect";//用户点击重新连接的点击次数
    static GAME_BREAK_EXIT = "game_break_exit";//断线后用户放弃重新连接的点击次数
    static GAME_BREAK_RECONNECTSUCCESS = "game_break_ReconnectSuccess";//服务端返回的重连成功的消息数
    static GAME_BREAK_CONNECTFAILURE = "game_break_ConnectFailure";//服务端返回的重连失败的消息数
    static GAME_BREAK_GAMEOVER = "game_break_GameOver"//服务端返回提示游戏结束的消息数，只记录断线情况下的提示

    //..................1.2.0新增ludo....................
    static LUDO_GAME_UNDO_LACK = "Ludo_game_undo_lack";//游戏_点击undo
    static LUDO_GAME_UNDO_LACK_BUY = "Ludo_game_undo_lack_buy";//游戏_点击undo_点击购买
    static TUTORIAL_SKIP_TUTORIAL = "Tutorial_Skip_Tutorial";//新手引导_点击跳过

    //--------------------domino--------------------------
    static DGAME_USER = "DGame_User";//点击参与游戏的用户头像
    static DGAME_USER_ADD = "DGame_User_Add";//用户头像_点击添加好友
    static DGAME_USER_MUTE = "DGame_User_Mute";//用户头像_点击mute
    static DGAME_USER_UNMUTE = "DGame_User_Unmute";//用户头像_点击unmute
    static DGAME_USER_QUIETMIC = "DGame_User_Quietmic";//点击静对方麦
    static DGAME_USER_OPENMIC = "DGame_User_Openmic";//点击开对方的麦
    static DGAME_EXPRESSION = "DGame_Expression";//点击表情
    static DGAME_MESSAGE = "DGame_Message";//点击消息
    static DGAME_EXIT_CONFIRM = "DGame_Exit_Confirm";//中途退出 点击confirm
    static DGAME_EXIT_CANCEL = "DGame_Exit_Cancel";//中途退出 点击cancle
    static DGAME_REWARD = "DGame_Reward";//点击reward
    static DGAME_SETTING = "DGame_Setting";//点击设置
    static DGAME_SETTING_OPENSOUND = "DGame_Setting_OpenSound"//设置_打开音效
    static DGAME_SETTING_CLOSESOUND = "DGame_Setting_CloseSound";//设置_关闭音效
    static DGAME_SETTING_RULES = "DGame_Setting_Rules";//设置_查看规则
    static DGAME_BACK = "DGame_Back"//点击结果弹窗上的back

    static DGAME_BREAK = "DGame_Break";//服务端返回的断线提示的消息数
    static DGAME_BREAK_CONNECT = "DGame_Break_Connect";//用户点击重新连接的点击次数
    static DGAME_BREAK_EXIT = "DGame_Break_Exit";//断线后用户放弃重新连接的点击次数
    static DGAME_BREAK_RECONNECTSUCCESS = "DGame_Break_ReconnectSuccess";//服务端返回的重连成功的消息数
    static DGAME_BREAK_CONNECTFAILURE = "DGame_Break_ConnectFailure";//服务端返回的重连失败的消息数
    static DGAME_BREAK_GAMEOVER = "DGame_Break_GameOver"//服务端返回提示游戏结束的消息数，只记录断线情况下的提示

    //------------------------champion------------------
    static Arena_Quit = 'Arena_Quit';//锦标赛 点击放弃
    static Arena_ConfirmSign = 'Arena_ConfirmSign';//锦标赛 确认报名
    static Arena_MatchFailure = 'Arena_MatchFailure';//锦标赛 匹配失败
    static Arena_SignAgain = 'Arena_SignAgain';//锦标赛 大奖再次报名
    static Arena_OK = 'Arena_OK';//锦标赛 大奖ok
    static Arena_MatchSuccess = 'Arena_MatchSuccess';//锦标赛 匹配成功
    static Arena_Rules = 'Arena_Rules';//锦标赛 查看规则
    static Arena_ExitMatch = 'Arena_ExitMatch';//锦标赛 退出匹配
}
interface showRule {
    gameType: number,
    mode: number,
    ruleType: number
}
interface ProfileInfo {
    isDebug?: boolean,
    hasHair?: boolean,        //手机是否是刘海
    hairHeight?: number       //刘海平高度（px）
}
interface AccountChampion {//锦标赛玩家账户
    idx?: number,//账号
    headUrl?: string,
    name?: string,//名称
    money?: number,//玩家在锦标赛中剩余金币
    curLevel?: number,//所在关卡
}

interface GooglePlayInfo {
    isCanDownload: boolean
}

// interface Champion {//锦标赛玩家账户
//     champoinId?: number,
//     gameId?:number,     //ludo or domino
//     signCost?:number,   //报名费
//     totalMoney?: number,//奖池内金币（每次进入锦标赛刷新）
//     levelCosts?: Array<number>,//每一关消耗[100,200,300,...]
//     levelRoles?: Array<number>,//每一关卡的游戏人数
//     royalty?: number,   //终极关卡胜者分成
//     signUp?: number     //是否报名
// }

interface ChampionConfig {//锦标赛静态数据
    id?: number,
    gameId?: number,             //ludo or domino
    payMoney?: number,           //报名费
    type?: number,               //0 日赛 1 周赛
    gameGroup?: number,          //游戏分组  (ludo)0竞技 1道具               (domino)0
    gameType?: number,           //游戏类型  (ludo)0正常 1大师 2快速 3火拼    (domino)0接龙 1五倍场
    bonusRatio?: number,         //通关获取奖金比例
    levelConfigList?: Array<any>,//等级奖励配置 {level,winMoney}
    minLevel?: number,           //最小等级
}

interface ChampionInfo {//锦标赛玩家账户
    op?: number,                 //操作类型0获取竞标赛列表  1获取锦标赛信息 2报名 3领奖 4匹配 5取消匹配      9胜负通知
    championshipId?: number,    //锦标赛id(0获取所有)
    gameId?: number,             //ludo or domino
    result?: any,                //操作结果 {success failed}
    matchInfo?: any,             //比赛信息
    bonusPool?: number,          //奖池
    levelInfoList?: Array<any>   //每一关的人数 {level, num}
}

interface ChampionshipPlayerInfo {
    idx?: number,
    headUrl?: string,            //头像
    name?: string,               //姓名
    headId?: number,             //头像框
    faceId?: number,              //头像边框
    status?: number,             //状态 0未报名 1可匹配 2比赛中 3游戏中  4通关可领奖
    level?: number,              //锦标赛级别
    money?: number,              //锦标赛报名金币
    moneyLeft?: number,          //锦标赛金币余额
    exitCurrency?: number,        //退出能获得金币  & 通关能获得的钱
    winMoney?: number,            //获胜奖金
    gameRoleLevel?: number,        //游戏中角色等级
    gameGold?: number,             //游戏中的金币
    royalLevel?: number             //皇室等级（原生传入的是这个驼峰写法字段）
    royVisibility?: boolean,          //皇室等级可见性,false:所有人可见，true：仅自己可见
    realRoyLevel?: number,           // 真实的玩家皇室等级
}

enum GameGiftType {
    EMOJ = 1 //表情
}

enum ChatChannel {
    /** 组队聊天 */
    TEAM = 1,
}
enum BusinessOrderCode {/**业务指令码 */
    //snakeLadder
    snake_call = 1,
    snake_message = 14,
    snake_gameOperate = 15,
    snake_playerChatToAll = 16,
    snake_getAgoraToken = 17,
    snake_getZegoToken = 18,
    snake_flushCurrency = 19,
    snake_quitRoom = 20,
    snake_gameGiftCnfList = 21,
    snake_gameGiftSend = 22,
    //domino
    domino_callGame = 35,//双向流
    domino_getZegoToken = 30,
    domino_updataCoin = 31,
    domino_getAgoraToken = 33,
    domino_playerChatToAll = 36,
    domino_quitRoom = 37,
    domino_gameGiftCnfList = 32,
    domino_gameGiftSend = 38,
    domino_gameOperate = 39,
    //gamewatch
    watch_addWatchRoom = 3,
    watch_chatMessage = 5,
    watch_quitWatchRoom = 6,
    //jackaro
    jackaro_call = 1,
    jackaro_message = 14,
    jackaro_gameOperate = 15,
    jackaro_getAgoraToken = 17,
    jackaro_getZegoToken = 18,
    jackaro_flushCurrency = 19,
    jackaro_ticketLogin = 200,
    jackaro_selectPoker = 201,
    jackaro_playPoker = 202,
    jackaro_moveChess = 203,
    jackaro_exchangeChess = 204,
    jackaro_extendTime = 205,
    jackaro_discardPoker = 206,
    jackaro_quitRoom = 207,
    jackaro_flushCurrencyReq = 208,
    jackaro_syncGameData = 209,
    jackaro_cancelTrust = 210,
    jackaro_voiceToken = 211,
    jackaro_gameGiftCnfList = 212,
    jackaro_gameGiftSend = 213,
    jackaro_playerChatToAll = 214,
    jackaro_assignNextPlayerDiscard = 215,
    jackaro_drawNextPlayerDiscard = 216,
    jackaro_set_chat = 217,
    jackaro_testExchangePoker = 218,
    jackaro_select_chess = 219,
    jackaro_select_item = 220,
    jackaro_syncTime = 221,
}
enum GateWayCode {
    SUCCESS = 0,
    /**
     * 网关异常
     */
    GATEWAY_ERROR = 1,
    /**
     * 下游服务异常
     */
    SERVICE_ERROR = 2,
    /**
     * 客户端错误：通常为请求方式错误、参数错误
     */
    CLIENT_ERROR = 3,
    /**
     * 参数错误
     */
    INVALID_PARAMS = 4,
    /**
     * 接口超时
     */
    SERVICE_TIMEOUT = 5,
    /**
     * 服务未发现(只暴露code,具体错误信息不对外暴露)
     */
    SERVICE_NOT_FOUND = 6,
    /**
     * 身份认证失败
     */
    AUTH_ERROR = 7,

    /**
     * 安全检查失败
     */
    SECURITY_ERROR = 8,
    /**
     * 操作被取消
     * 这种情况原因较多,需根据日志或监控排查,对外显示为服务繁忙
     */
    SERVER_BUSY = 9,
    /**
     * 非法应用
     */
    INVALID_APPLICATION = 10,

    /**
     * 指令已停用
     */
    COMMAND_OFFLINE = 11,

    /**
     * 指令基础信息不存在
     */
    COMMAND_INFO_DOSE_NOT_EXIST = 12,

    /**
     * APP不存在该指令
     */
    COMMAND_NOT_EXIST_IN_APP = 13,

    /**
     * 无效指令
     */
    INVALID_COMMAND = 14,

    /**
     * 同时登入
     */
    ILLEGAL_ENTER = 15
}

enum DomainType {/** 备用域名 */
    // https://fat-account.yalla.games
    GAME_ACCOUNT = 1000,//账号
    // https://fat-pay.yalla.games
    GAME_PAY = 1001,//支付
    // https://fat-mail.yalla.games 邮件服务
    GAME_MAIL = 1002,//邮件
    // https://fat-clog.yalla.games 日志打点服务
    GAME_CLOG = 1003,//日志
    // https://fat-activity.yalla.games 红点,活动列表,帮助中心服务
    GAME_ACTIVITY = 1004,//活动接口
    // https://fat-usuallyactivity.yalla.games 网页活动域名
    GAME_USUALLY_ACTIVITY = 1005,//活动网页
    // https://fat-httpgateway.yalla.games
    GAME_GATE_WAY_HTTP = 1006,//http 网关
    // wss://fat-tyr.yalla.games
    GAME_GATE_WAY_SOCKET = 1007,//socket 网关
    // wss://fat-hall.yalla.games 主域名  wss://fat-hall.yallaludo.com 备用域名
    GAME_HALL_SOCKET = 1008,//大厅socket
    // https://file.yallaludo.com	文件地址统一域名（图片、动效等）
    COMMON_FILE = 3000,//文件
    // https://fat-dtchat.yalla.games 私聊域名
    COMMON_CHAT = 3001,//私聊
    // https://fat-activity.yalla.games
    COMMON_RULES = 3002,//规则
    // https://fat-roomapi.yalla.games
    ROOM_API = 2001,//房间api
    //  https://fat-roomclog.yalla.games
    ROOM_CLOG = 2002,//房间日志
    // https://fat-roommoment.yalla.games	圈子，活动，短链等域名
    ROOM_MOMENT = 2003,//房间圈子
    // https://www.yallaludo.com
    ROOM_ACTIVITY_SHARE = 2004,//房间活动分享
    // https://file.yalla.Live
    ROOM_MUSIC_UPLOAD = 2005,//房间音乐上传
    /*
    https://account.yalla.games 账号服务
https://pay.yalla.games 支付
https://httpgateway.yalla.games 游戏网关
https://nitrogen.yalla.games	网关域名
https://nitrogen.yallaludo.com	网关备用域名
https://roomapi.yalla.games	业务接口功能域名
https://roomclog.yalla.games	埋点域名
https://roommoment.yalla.games	圈子，活动，短链等域名
    H5各种规则说明页的域名
https://www.yallaludo.com	分享房间活动的 H5页面
https://file.yalla.Live	iOS音乐上传
     */
}