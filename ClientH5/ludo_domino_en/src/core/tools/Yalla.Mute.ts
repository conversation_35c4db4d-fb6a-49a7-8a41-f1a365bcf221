/**
 * 举报功能
 */
module yalla {
    export class Mute {
        //已举报玩家列表
        static MUTE: string = "mute";
        static UNMUTE: string = "unmute";
        static event: Laya.EventDispatcher = new Laya.EventDispatcher();
        static get mute_cache(): Object {
            if (!!Laya.LocalStorage.getJSON("mute_cache_" + yalla.Global.Account.idx)) {
                return Laya.LocalStorage.getJSON("mute_cache_" + yalla.Global.Account.idx);
            } else {
                return this.mute_cache = { "data": [] };
            }
        }
        static set mute_cache(value: Object) {
            Laya.LocalStorage.setJSON("mute_cache_" + yalla.Global.Account.idx, value);
        }
        /**
         * 移除已举报玩家
         * @param idx 
         */
        static remove(idx: number, cb: Function = null) {
            var index: number = this.mute_cache['data'].indexOf(idx);
            if (index >= 0) {
                this.event.event(this.UNMUTE, [idx]);
                cb && cb();
                var arr = this.mute_cache['data'];
                arr.splice(index, 1);
                this.mute_cache = {
                    "data": arr
                };
            }
        }
        /**
         * 添加未举报玩家
         * @param idx 
         */
        static add(idx: number, cb: Function = null) {
            if (!this.muted(idx)) {
                this.event.event(this.MUTE, [idx]);
                cb && cb();
                var arr = [idx].concat(this.mute_cache['data']);
                yalla.Debug.log("===Yalla.Muted===idx="+idx);
                yalla.Debug.log(arr);
                this.mute_cache = {
                    "data": arr
                };
            }
        }
        /**
         * 是否举报过
         * @param idx 
         */
        static muted(idx: number): boolean {
            if (this.mute_cache['data'].indexOf(idx) >= 0) {
                return true;
            } else {
                return false;
            }
        }

        /**
         * 如果是限制游客，是否显示聊天消息
         * @param gameId 
         */
        static isShowMsgByLimit(msgResult: any): boolean{
            var msg = '';
            var extension;
            var messageType;
            if (msgResult) {
                msg = msgResult.msg;
                extension = msgResult.extension;
                messageType = msgResult.messageType;
            }
            var isShowMsg = true;
            if (yalla.Global.Account.isLimitVisitor) {
                if ((extension && !msg) || messageType == yalla.data.jackaro.GameChatMessageType.CHAT_MESSAGE_EMOJI) return true;
                var isFastWord = false;
                var fastList = this.fastWords;
                fastList = fastList.concat(yalla.fastChat.emojiSkin);
                for (var i = 0; i < fastList.length; i++){
                    var wordStr = fastList[i].replace('\n', ' ');
                    if (wordStr == msg) {
                        isFastWord = true;
                        break;
                    }
                }
                isShowMsg = isFastWord;

            }
            return isShowMsg;
        }

        /**
         * 后台禁言
         *{ExpireTime:,FEnglishStatusReason,FArabicStatusReason}
         **/
        static showBanTalkDialog() {
            if (yalla.Mute.isBanTalk) {
                var data = yalla.util.parse(yalla.Global.Account.banTalkData);
                if (yalla.Font.lan == 'en' && data.FEnglishStatusReason) yalla.common.Tip.instance.showTip(data.FEnglishStatusReason);
                else if(yalla.Font.lan=='ar' && data.FArabicStatusReason) yalla.common.Tip.instance.showTip(data.FArabicStatusReason);
            }
        }

        static get isBanTalk():boolean {
            return yalla.Global.Account.banTalkData && yalla.Global.Account.banTalkData.length > 0;
        }

        static get fastWords() {
            var fastList;
            switch (yalla.Global.gameType) {
                case GameType.DOMINO:
                    fastList = yalla.fastChat.dominoWords;  
                    break;    
                case GameType.SNAKEANDLADER:
                    fastList = yalla.fastChat.snakeWords;     
                    break;   
                default:
                    fastList = yalla.fastChat.ludoWords;    
                    break;    
                    
            }
            return fastList;
        }
    }
}