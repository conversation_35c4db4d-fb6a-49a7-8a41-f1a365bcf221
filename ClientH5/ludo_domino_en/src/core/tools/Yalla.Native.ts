module yalla {
    export class Native extends Laya.EventDispatcher {
        Event = {
            SHARE: "share",//分享
            BACK: "back",
            FOUCE: "fouce",
            BLUR: "blur",
            ACCOUNT: "account",
            SPEAKER: "speaker",
            PRESSENTER: "pressenter",
            OFFGAME: "offgame",
            QUITGAME: "quitGame",
            HASBACKHALL: "hasbackhall",
            NETSTATECHANGE: "netStateChange",
            INITGAME: "initgame",
            KICKEDOUT: "kicketOut",
            NOVICEGAME: "noviceGame",
            CHAMPIONTOGAME: "ChampionToGame",//锦标赛进入游戏
            BACKCHAMPION: "BackChampion",    //游戏返回锦标赛
            HIDECHAMPION: "HideChampion",    //隐藏锦标赛
            UPDATEGOLDS: "UpdateGolds",      //更新金币
            CONNECTSTATECHANGE: "connectStateChange",   //大厅socket链接情况
            SETMUTE: "setMute",                          //是否屏蔽他人
            SHOWKEYBOARD: "showKeyboard",//调起键盘
            NATIVE_INPUT: "onNativeInput",//键盘输入
            GETZEGOTOKEN: "GetZegoTokenRequest",//zego token
            EXITGAMEWATCHFORLEAGUE: "exitGameWatchForLeague",//联赛观战中，如果收到参加联赛通知，则原生会调用此方法
            ONBACKPRESSED: "onBackPressed",//android返回键功能
            READMSGNUM: "readMsgNum",//消息红点
            // CLOSEFRIENDCHAT: "closeFriendChat",//关闭好友聊天
            RESIZEVIEW: "resizeView",//渠道包才会调用的方法，屏幕适配
            UPDATEMONEY: "updatemoney",//通知各个游戏更新钻石或者金币
            JUNGLRNOVICE: "jungleNovice",//丛林模式新手引导
            JACKAROONOVICE: "onJackaroNovice",//Jackaroo新手引导
            ONASSETSCHANGE: "onAssetsChange",//Jackaroo新手引导
            OPEN_ASSETSVIEW: "openAssetsView",//触发资产view，打开原生购买界面
        }
        public isBack: boolean = true;
        public isBackToLeague: boolean = false;
        /** 离开房间(1.4.5 增加条件判定，多次离开语音房被多次回掉，需要有一个状态判定，这个状态在onLogin 和 第一次离开语音房回掉 )*/
        public hasLeaveVoiceRoom: boolean = false;

        constructor() {
            super();
            var u = navigator.userAgent;
            if (laya.utils.Browser.window.conch) {
                if (u.indexOf('Android') > -1 || u.indexOf('Linux') > -1) {//安卓手机
                    this.deviceType = DeviceType.Android;
                } else if (u.indexOf('iPhone') > -1 || u.indexOf('iPad') > -1) {//苹果手机
                    this.deviceType = DeviceType.IOS;
                }
            } else {
                this.deviceType = DeviceType.Browser;
            }
            window['shareResponse'] = (boo: boolean) => {//分享成功的回调
                this.event(this.Event.SHARE, [boo]);
            }
            window['onPause'] = () => {//后台
                this.event(this.Event.BLUR);
                yalla.Global.isFouce = false;
            }
            window['onResume'] = () => {//前台
                // if (yalla.Global.isFouce) return;
                this.event(this.Event.FOUCE);
                yalla.Global.isFouce = true;
            }
            window['pressEnter'] = () => {//键盘回车键
                this.event(this.Event.PRESSENTER);
            }
            window['onLogin'] = (msg: any) => {//外部登录成功(更新user room 数据)main
                this.hasLeaveVoiceRoom = false;
                if (typeof msg == 'string') msg = yalla.util.parse(msg);
                if (msg) {
                    this.isBackToLeague = false;
                    if (msg) {
                        yalla.Debug.log("===onLogin信息===" + JSON.stringify(msg));
                    } else {
                        yalla.Debug.log("===onLogin信息=== msg is null");
                    }
                    if (msg.league && msg.league.watch) {
                        if (!!yalla.Global.Account.roomid && yalla.Global.Account.roomid == msg.roomid) return;
                    } else {
                        if (!!yalla.Global.Account.token && yalla.Global.Account.token == msg.token) return;
                        if (yalla.Global.Account.roomid && yalla.Global.Account.idx && msg.roomid && yalla.Global.Account.roomid != msg.roomid && yalla.Global.Account.idx == msg.idx) {
                            return;
                        }
                    }
                    // if (msg.championshipId && yalla.Global.Account.championshipId == msg.championshipId) return;//TODO::注释原因：ios同时在列表 点击进入和关闭按钮，第二次再点击进入没反应  被这个return了
                    yalla.Global.Account = msg;
                    yalla.Global.Account.fixedRouteKey = msg.serverId;
                    yalla.Global.gateWay = msg.gateWay;
                    yalla.DomainUtil.instance.initSocketUrl(msg.socketUrlList);

                    // var idx = yalla.Global.Account.idx;
                    // if (idx == ******** || idx == ******** || idx == ********) yalla.Global.Account.isLimitVisitor = true;
                    // if (idx == ********* || idx == *********) yalla.Global.Account.isLimitVisitor = true;

                    if (msg.league) yalla.Global.Account.leagueWatch = msg.league.watch;
                    if (msg.gameid) yalla.Global.Account.gameId = msg.gameid;
                    yalla.Debug.log(yalla.Global.Account.leagueWatch + '----onLogin 2--------');
                    yalla.Global.onlineMode = true;
                    this.event(this.Event.ACCOUNT, [yalla.Global.Account]);
                }
                this.isBack = true;
            }
            window["onOffGame"] = (msg: any) => {//离线游戏
                yalla.Global.onlineMode = false;
                if (msg) {
                    this.event(this.Event.OFFGAME, [yalla.util.parse(msg)]);
                }
                this.isBack = true;
            }
            window["onNoviceGame"] = (gameId: string, faceUrl: string, name: string) => {
                yalla.Global.onlineMode = false;
                this.isBack = true;
                faceUrl = laya.utils.Browser.window.Base64.decode(faceUrl);
                name = laya.utils.Browser.window.Base64.decode(name);
                this.event(this.Event.NOVICEGAME, [gameId, faceUrl, name]);
            }
            window["onJungleNovice"] = (gameId: string, faceUrl: string, name: string, realRoyLevel: string, viplevel: string) => {
                yalla.Global.onlineMode = false;
                faceUrl = laya.utils.Browser.window.Base64.decode(faceUrl);
                name = laya.utils.Browser.window.Base64.decode(name);
                this.isBack = true;
                this.event(this.Event.JUNGLRNOVICE, [gameId, faceUrl, name, realRoyLevel, viplevel]);
            }
            window["onJackarooNovice"] = (gameId: string, faceUrl: string, name: string, realRoyLevel: string, viplevel: string) => {
                yalla.Debug.log("~~~~~~~~~~~onJackarooNovice rl:" + realRoyLevel + "vip" + viplevel)
                yalla.Global.onlineMode = false;
                faceUrl = laya.utils.Browser.window.Base64.decode(faceUrl);
                name = laya.utils.Browser.window.Base64.decode(name);
                this.isBack = true;
                this.event(this.Event.JACKAROONOVICE, [gameId, faceUrl, name, realRoyLevel, viplevel]);
            }
            window['getSpeaker'] = (data: Array<number>) => {
                if (data.length > 0) {
                    this.event(this.Event.SPEAKER, [data]);
                }
            }
            window['quitGame'] = (msg) => {//退出游戏(接受其它玩家邀请)  现在游戏中不弹出邀请
                if (typeof msg == 'string') msg = JSON.parse(msg);
                this.isBack = !!msg['isBack'];
                yalla.util.sendExitType(ExitType.JOIN_OTHER_GAME_QUIT)
                this.event(this.Event.QUITGAME, msg);
            }
            window["netStateChange"] = (json) => {//-1 unknow  0断网
                // var d = typeof json == "string" ? JSON.parse(json) : json;
                // this.event(this.Event.NETSTATECHANGE, [d]);
            }
            window["initGame"] = (json) => {
                // this.isBack = true;
                // var d = typeof json == "string" ? JSON.parse(json) : json;
                // this.event(this.Event.INITGAME, [d]);
            }
            window["onKickedOut"] = () => {
                yalla.Global.Account.idx = null;
                yalla.util.resetAccount();

                this.event(this.Event.KICKEDOUT);
            }

            window['onBackHallCompleted'] = (msg) => {//ios 返回大厅回调方法（游戏处激活，程序可被执行）
                if (this.deviceType == DeviceType.IOS) {//切回大厅效果，需要等ios返回，再移除界面
                    var account = yalla.Global.Account;
                    yalla.Debug.log(account.roomid + "=====Native.onBackHallCompleted====0===account.league==" + (!!account.league));
                    if (typeof msg == 'string') yalla.Debug.log(msg);
                    else yalla.Debug.log(JSON.stringify(msg));

                    if (account.league) {
                        /**TODO::联赛中上一局因为断线重连，异常操作，虽然送入游戏弹框点击了加入，但是ios未进入游戏；
                         * 第二次匹配后，出现上一局的游戏结束提示，点击确定退回联赛，又一次弹出进入游戏提示，选择加入，进入游戏瞬间已经触发socket连接 onNetOpen；
                         * ios又回掉onBackHallCompleted，导致游戏绿屏。
                         * 解决方法：根据上一次backHall传入的roomid*/
                        if (typeof msg == 'string') msg = JSON.parse(msg);
                        yalla.Debug.log("=====Native.onBackHallCompleted=======account.roomid==" + account.roomid);
                        yalla.Debug.log(msg);
                        if (msg && msg.roomid && account.roomid && msg.roomid != account.roomid) return;
                    }
                    this.event(this.Event.HASBACKHALL, [true]);
                }
            }

            window['onClearGame'] = () => {
                yalla.Debug.log("=====顶号后做清理逻辑onClearGame===");
                //a b匹配进入游戏，a断线，c登陆a的账号进入游戏，a重连回来，会退回登录页，登陆其他号进入游戏，再匹配进入游戏，会出现 之前页面重连进入上一局 把c顶下来，卡住游戏（原生退回登录页没有通知游戏清理）
                //现在定义新方法，只要原生退出登录页，就通知游戏做清理工作
                yalla.Global.Account.idx = null;
                yalla.util.resetAccount();
                this.event(this.Event.KICKEDOUT);
            }

            window['updateGold'] = (msg) => {//购买金币后，需要通知游戏或者锦标赛，当前金币更新
                if (typeof msg == 'string') msg = JSON.parse(msg);
                this.event(this.Event.UPDATEGOLDS, [msg]);
            }
            window['connectStateChange'] = (json) => {//大厅soket 链接情况 * {"code":-1|1} 1 与服务器连接上，-1与服务器断开
                var d = typeof json == "string" ? JSON.parse(json) : json;
                if (yalla.Global.HallNetState == d.code) return;
                yalla.Global.HallNetState = d.code;
                yalla.Debug.log(yalla.Global.HallNetState + '==connectStateChange==');
                this.event(this.Event.CONNECTSTATECHANGE, [d]);
            }
            window['setMute'] = (msg) => {//设置是否举报 {idx}
                if (typeof msg == 'string') msg = JSON.parse(msg);
                this.event(this.Event.SETMUTE, msg);
            }

            /**
             * json中的三个字段 {type:number,msg:string,isSend:boolean}
             * type:1表示消息,2表示小马动画(快捷表情),3新增表情系统;
             * msg:内容;
             * isSend:是否立即发送
             */
            window['onNativeInput'] = (json) => {// 线上报错 SyntaxError: Unexpected token ILLEGAL onNativeInput({"type":1,"msg":"﮼^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                try {
                    this.event(this.Event.NATIVE_INPUT, json);
                } catch (error) {
                    yalla.Native.instance.writeLog(json);
                }
            }

            window["keyBoardUp"] = (n) => {//IOS改用runJs 线上崩溃:初始化游戏的时候callbackwithjs还没有被注册就执行了
                this.event(this.Event.SHOWKEYBOARD, n);
            }
            /**zego token即将过期通知游戏内重新获取，再返回给原生 */
            window["GetZegoTokenRequest"] = (n) => {
                if (yalla.Global.Account && yalla.Global.Account.voiceType == VoiceType.Zego) {
                    yalla.Global.IsUpdateZegoToken = true;
                    var gameType = yalla.Global.gameType;
                    if (gameType == GameType.LUDO) {
                        ludo.GameSocket.instance.getZegoToken();
                    } else if (gameType == GameType.DOMINO) {
                        yalla.data.RoomService.instance.getToken();
                    } else if (gameType == GameType.SNAKEANDLADER) {
                        yalla.data.snake.UserService.instance.getToken();
                    } else if (gameType == GameType.JACKARO) {
                        yalla.data.jackaro.JackaroUserService.instance.getToken();
                    }
                }
            }
            window['onGameChatClose'] = function () { }//因为cocos有此方法原生会调用 放一个空方法避免报错

            /**其他游戏中收到联赛邀请，这时需要backHall to  leagueList */
            window['exitGameWatchForLeague'] = (n) => {
                this.isBackToLeague = true;
                this.event(this.Event.EXITGAMEWATCHFORLEAGUE, n);
            }

            window["onBackPressed"] = (msg) => {//1.3.6添加此方法，android返回键功能
                this.event(this.Event.ONBACKPRESSED);
            }

            window['readMsg'] = (msg) => {//1.3.7好友聊天消息红点数量更新 {num:1} 先保存数据，如果界面未初始化好
                if (typeof msg == 'string') msg = JSON.parse(msg);
                this.event(this.Event.READMSGNUM, msg);
            }


            /** 渠道包 折叠屏适配 */
            window['resizeView'] = (msg) => {
                if (typeof msg == 'string') msg = JSON.parse(msg);
                this.event(this.Event.RESIZEVIEW, msg);
            }
            //游戏资产发生变化
            window["onAssetsChange"] = data => {
                if (typeof data == 'string') data = JSON.parse(data);
                data && this.event(this.Event.ONASSETSCHANGE, data);
            }
        }
        clear(): void {
            Laya.timer.clearAll(this);
        }
        private static _instance: yalla.Native;
        public deviceType: DeviceType;
        private _appClass: any;
        static get instance(): yalla.Native {
            if (!this._instance)
                this._instance = new yalla.Native();
            return this._instance;
        }
        get appClass(): any {
            switch (this.deviceType) {
                case DeviceType.Android:
                    if (!this._appClass) this._appClass = Laya.PlatformClass.createClass("com.yalla.yallagames.YallaGame").newObject();
                    // this._appClass = Laya.PlatformClass.createClass("com.yalla.yallagames.YallaGame").call('getInstance');
                    break;
                case DeviceType.IOS:
                    if (!this._appClass) this._appClass = Laya.PlatformClass.createClass("YallaGameInterface").newObject();
                    break;
                default:
                    break;
            }
            return this._appClass;
        }

        reportUser(idx: number) {
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("reportUser", JSON.stringify({ idx: idx }));
                    break;
                case DeviceType.IOS:
                    this.appClass.call("reportUser:", JSON.stringify({ idx: idx }));
                    break;
                default:
                    break;
            }
        }
        /**
         * @param obj {
         *  gamePay
         *  gameId
         *  gameType
         *  playerNum
         *  gameGroup
         * }
         */
        playAgain(obj: playAgainArg) {
            if (this.deviceType == DeviceType.Browser) return;
            if (obj && obj.roomId) {
                var json = JSON.stringify(obj);
                switch (this.deviceType) {
                    case DeviceType.Android:
                        this.appClass.call("playAgain", json);
                        break;
                    case DeviceType.IOS:
                        this.appClass.call("playAgain:", json);
                        break;
                    default:
                        break;
                }
            } else {
                this.backHall();
            }
            //TODO::Native onLogin 有处理roomid不同，屏蔽后续的对局，so 再来一局需要把roomid清理
            yalla.Global.Account = {
                roomid: null,
                sameSkinBuySwitch: 0
            }
        }

        /** 新手引导后 快速开始游戏，自动发起匹配 500场 经典 2人模式 
         * @param obj {
         *  gamePay
         *  gameId
         *  gameType
         *  playerNum
         *  gameGroup
         * }
         */
        quickGame(obj: playAgainArg) {
            var json = JSON.stringify(obj);
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("quickGame", json);
                    break;
                case DeviceType.IOS:
                    this.appClass.call("quickGame:", json);
                    break;
                default:
                    break;
            }
        }

        /**
         * 进度
         * @param num 
         */
        gameLoadingProgress(num) {
            // if (num < 100) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack(() => { }, "gameLoadingProgress", num);
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack(() => { }, "gameLoadingProgress:", num);
                    break;
                case DeviceType.Browser:
                    if (num == 200) {
                        if (!!(location.hash)) {
                            yalla.Global.Account.idx = parseInt(location.hash.slice(1));
                        } else if (!!location.search) {
                            var search: Array<string> = location.search.slice(1).split("&");
                            search.forEach(str => {
                                var arr = str.split("=");
                                yalla.Global.Account[arr[0]] = parseInt(arr[1]);
                            })
                            if (!!yalla.Global.Account["gameId"]) {
                                yalla.Global.gameType = yalla.Global.Account["gameId"];
                            }
                        }
                        console.log("gameLoadingProgress yalla.Global.Account.fixedRouteKey=", yalla.Global.Account.fixedRouteKey);
                        if (!!yalla.Global.Account.idx) {
                            if (yalla.Global.gameType == GameType.LUDO) {
                                this.loginLudoBrowser();
                            } else if (yalla.Global.gameType == GameType.DOMINO) {
                                this.loginDominoBrowser(GameType.DOMINO);
                            } else if (yalla.Global.gameType == GameType.SNAKEANDLADER) {
                                this.loginDominoBrowser(GameType.SNAKEANDLADER);
                            } else if (yalla.Global.gameType == GameType.JACKARO) {
                                this.loginJackaroBrowser();
                            } else if (yalla.Global.gameType == 1) {
                                yalla.Global.Account = {
                                    gameId: 1,
                                    sameSkinBuySwitch: 0
                                }
                            }
                            this.event(this.Event.ACCOUNT, [yalla.Global.Account]);
                        } else {
                            // this.loginLudoBrowser();
                            // this.event(this.Event.NOVICEGAME, [10019, "public/default_head.png", "احم✌احمد"]);
                            // this.event(this.Event.INITGAME, [yalla.Global.Account]);
                            // Laya.loader.load(`res/json/local.json`, Laya.Handler.create(this, (e) => {
                            //     !!e && this.event(this.Event.OFFGAME, [e]);
                            // }))
                            this.event(this.Event.JACKAROONOVICE, [GameType.JACKARO, "https://file.yallaludo.com/FAT/Skin/H5/ludoCache11905/chess_11136.png", "yalla", "5", "2"]);
                        }
                    }
                    break;
                default:
                    break;
            }
        }

        loginLudoBrowser() {
            if (!!yalla.Global.Account.idx) {
                yalla.Global.Account = {
                    idx: yalla.Global.Account.idx,
                    roomid: yalla.Global.Account.roomid || 12032,
                    gameId: GameType.LUDO,
                    token: yalla.Global.token,
                    // port: '9013',

                    port: '9012',
                    // ip: "ws://dev-ludo.yalla.games",
                    // host: "ws://dev-ludo.yalla.games",
                    ip: "ws://fat-ludo.yalla.games",
                    host: "ws://fat-ludo.yalla.games",
                    // ip: "ws://dev-jungleludo.yalla.games/0",
                    // host: "ws://dev-jungleludo.yalla.games/0",
                    // ip: "ws://dev-ludo.yalla.games",
                    // host: "ws://dev-ludo.yalla.games",
                    // ip: "ws://*************",
                    // host: "ws://*************",
                    royalty: 0,
                    sameSkinBuySwitch: 0
                }
                if (yalla.Global.Account.idx >= 10115 && yalla.Global.Account.idx <= 10124) {//决斗模式调试idx区间 10115 - 10119 2人局  10121-10124 4人局
                    // yalla.Global.Account.host = yalla.Global.Account.ip = "ws://dev-duelludo.yalla.games/1"//"ws://dev-duelludo.yalla.games/0"两个都可以
                    // yalla.Global.Account.host = yalla.Global.Account.ip = "ws://fat-duelludo.yalla.games/0"
                    // yalla.Global.Account.port = null
                    //  yalla.Global.Account.host = yalla.Global.Account.ip = "ws:************"//"ws://dev-duelludo.yalla.games/1"两个都可以
                }
            } else {
                yalla.Global.Account = {
                    royalty: 0,
                    gameId: 10019,
                    isOffGame: true,
                    sameSkinBuySwitch: 0
                }
            }
        }
        loginDominoBrowser(gameId) {
            // var idx: number;
            // var roomId: number = 1;
            // if (!!(location.hash)) {
            //     idx = parseInt(location.hash.slice(1));
            //     roomId = parseInt(location.hash.slice(7));
            //     roomId = roomId ? roomId : Math.round(Math.random() * 1000 + 1);
            // } else if (!!(laya.net.LocalStorage.getItem("idx"))) {
            //     idx = parseInt(laya.net.LocalStorage.getItem("idx"));
            // } else {
            //     idx = 10082;//10050
            // }

            // yalla.Global.gateWay = {
            //     sign: '',
            //     deviceId: '',
            //     channelId: 0,
            //     version: '',
            //     timestamp: 0
            // };

            yalla.Global.Account = {
                // idx: idx,
                // roomid: roomId,
                idx: yalla.Global.Account.idx,
                roomid: yalla.Global.Account.roomid || 12032,
                gameId: gameId,
                token: yalla.Global.token,
                port: yalla.Global.port,
                host: yalla.Global.host,
                ip: yalla.Global.host,
                royalty: 5,
                leagueWatch: false,
                league: { groupNo: 1, round: 1, watch: false },
                sameSkinBuySwitch: 0
            }
        }
        loginJackaroBrowser() {
            yalla.Global.onlineMode = true;
            let fixedRouteKey = yalla.Global.Account.fixedRouteKey;
            let pokerConfigId = yalla.Global.Account.pokerConfigId;
            let chessPanelConfigId = yalla.Global.Account.chessPanelConfigId;
            let gameType = yalla.Global.Account.devGameType;
            yalla.Global.Account = {
                // idx: idx,
                // roomid: roomId,
                idx: yalla.Global.Account.idx,
                roomid: yalla.Global.Account.roomid || 12032,
                gameId: GameType.JACKARO,
                token: yalla.Global.token,
                port: '9012',
                ip: "wss://dev-tyr.yalla.games",
                host: "wss://dev-tyr.yalla.games",
                royalty: 5,
                leagueWatch: false,
                league: { groupNo: 1, round: 1, watch: false },
                fixedRouteKey: fixedRouteKey,
                pokerConfigId: pokerConfigId,
                chessPanelConfigId: chessPanelConfigId,
                devGameType: gameType,
                sameSkinBuySwitch: 0
            }
        }
        getJungleEventData(cb: Function = () => { }) {
            switch (this.deviceType) {
                case DeviceType.Android:
                case DeviceType.IOS:
                    this.appClass.callWithBack(cb, "getJungleEventData");
                    break;
                case DeviceType.Browser:
                    cb(`{"data":[{"event":1,"open":true},{"event":2,"open":true},{"event":3,"open":true},{"event":4,"open":true},{"event":5,"open":true},{"event":6,"open":true}]}`)
                    break;
            }
        }
        /**
         * 保存离线游戏
         */
        updateLocalGameData(json: string) {
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack(() => { }, "updateLocalGameData", json);
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack(() => { }, "updateLocalGameData:", json);
                    break;
                case DeviceType.Browser:
                    laya.net.LocalStorage.setJSON("ludo_offGame", { data: [JSON.parse(json)] });
                    break;
                default:
                    break;
            }
        }
        /**
         * 移除原生视图
         */
        removeMatchView(isDiaPatch: boolean = true) {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.callWithBack(() => { }, "removeMatchView");
            if (isDiaPatch) this.event(this.Event.HIDECHAMPION);
        }
        /**
         * 返回系统语言 1:en 2:arb
         * @param cb?
         */
        currentLanguageType(cb: Function) {
            if (this.deviceType == DeviceType.Browser) {
                cb(1);
            } else {
                this.appClass.callWithBack((value: number) => {
                    cb(value);
                }, "currentLanguageType");
            }
        }
        /**
         * 设置系统语言
         * @param lan 1:en 2:arb
         */
        changeCurrentLanguage(lan: number) {
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack(() => { }, "currentLanguageType", lan);
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack(() => { }, "currentLanguageType:", lan);
                    break;
                default:
                    break;
            }
        }

        /**
         * 返回游戏大厅
         * json {to:''}
         * delayTime 主要针对锦标赛退回列表界面，Android在backhall之后直接抛出事件，会导致转场时候出现蓝屏（ps：android因为某些原因不能回调）
         * isChampion 是否锦标赛返回大厅
         * PS:带有日志的ios包（游戏内日志、原生日志），会导致退出游戏返回大厅卡顿
         */
        backHall(back: boolean = this.isBack, json: any = null, delayTime: number = 0, isChampion: Boolean = false) {
            yalla.util.closeAllDialog();
            !isChampion && yalla.Sound.stopAll();

            //TODO::1.4.4.1 避免对android有影响
            if (this.deviceType == DeviceType.Android) {
                yalla.Voice.instance.levelGameRoom();
            }
            yalla.common.connect.ReconnectControl.instance.hideConnect();
            yalla.Debug.log(delayTime + "---Native.backHall------清理Account " + !back);
            yalla.util.resetAccount();
            yalla.Global.IsUpdateZegoToken = false;
            yalla.Debug.log(json)
            if (this.deviceType == DeviceType.Browser || !back) {
                this.event(this.Event.HASBACKHALL);
            } else {
                //1.4.3注释原因 发现IOS转场的时候并不会黑屏
                if (this.deviceType != DeviceType.IOS) {//切回大厅效果，需要等ios返回，再移除界面,ios 监听了hasbackhall complete
                    if (delayTime <= 0) this.event(this.Event.HASBACKHALL);  //Android 可能出现的问题，delay在调用原生之后再执行(锦标赛表现明显，对右上角关闭按钮做延迟出现)
                    else Laya.timer.once(delayTime, this, () => {
                        this.event(this.Event.HASBACKHALL);
                    })
                }
                // if (!json || JSON.stringify(json) == "{}") {
                //     this.appClass.call("backHall");
                // }else {
                yalla.Debug.log(delayTime + "-------backHall------清理Account11111 " + !back);
                switch (this.deviceType) {
                    case DeviceType.Android:
                        if (!json || JSON.stringify(json) == "{}") {
                            this.appClass.call("backHall");
                        } else {
                            this.appClass.call("backHall", JSON.stringify(json));
                        }
                        break;
                    case DeviceType.IOS:
                        yalla.Debug.log("====ios backhall===11==hasLeaveVoiceRoom:" + this.hasLeaveVoiceRoom);
                        if (this.hasLeaveVoiceRoom) {
                            this.appClass.call("backHall:", json ? JSON.stringify(json) : "");
                        } else {
                            yalla.Voice.instance.levelGameRoom(() => {
                                yalla.Debug.log("====ios backhall===22==");
                                this.appClass.call("backHall:", json ? JSON.stringify(json) : "");
                                // this.event(this.Event.HASBACKHALL);
                            });
                        }
                        break;
                }
                // }
            }
        }

        /**
         * 退出登录
         */
        loginOut(cb?: Function) {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.call("loginOut");
            yalla.Global.Account.idx = null;
            yalla.util.resetAccount();
        }

        /**
         * 加入语音房间
         */
        joinGameRoomWithToken(token: string, channelId: any, idx: any, voiceType: VoiceType, cb?: Function) {//Zege IOS需延迟2s调用 要不然语音弹窗会弹出在匹配界面
            let data = {
                "roomId": yalla.Global.Account.roomid,
                "token": token,
                "channelId": channelId,
                "uId": idx,
                "voiceType": voiceType
            }
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((value: number) => {
                        !!cb && cb(value);
                    }, "joinGameRoomWithToken", JSON.stringify(data));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((value: number) => {
                        !!cb && cb(value);
                    }, "joinGameRoomWithToken:", data);
                    break;
                default:
                    break;
            }
        }
        /**
         * 关闭音频
         */
        disableAudio(cb: Function) {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.callWithBack((value: number) => {
                cb(value);
            }, "disableAudio");
        }
        /**
         * 打开音频
         * isClick 默认0   手动点击语音 1  (ios 不需要区分)
         */
        enableAudio(cb: Function, isClick: number = 0) {
            if (this.deviceType == DeviceType.Browser) return;
            var data = JSON.stringify({ "isClick": isClick });
            if (this.deviceType == DeviceType.IOS) {
                this.appClass.callWithBack((value: number) => {
                    cb(value);
                }, "enableAudio");
            } else {
                this.appClass.callWithBack((value: number) => {
                    cb(value);
                }, "enableAudio", data);
            }
            // this.appClass.callWithBack((value: number) => {
            //     cb(value);
            // }, "enableAudio");
        }
        /**
          * 离开房间(1.4.5 增加条件判定，多次离开语音房被多次回掉，需要有一个状态判定，这个状态在onLogin 和 第一次离开语音房回掉 )
            isForceCb, 使用场景，ios设备在backhall时，先离开语音房，再回掉backhall，这里的回掉一定要被执行
          */
        levelGameRoom(cb?, isForceCb = false) {
            if (this.deviceType == DeviceType.Browser) return;
            // this.appClass.callWithBack(() => {
            //     if (!this.hasLeaveVoiceRoom || isForceCb) {
            //         cb && cb();
            //     }
            //     this.hasLeaveVoiceRoom = true;
            // }, "levelGameRoom");
            if (this.deviceType == DeviceType.IOS) {
                this.appClass.callWithBack(() => {
                    if (!this.hasLeaveVoiceRoom || isForceCb) {
                        cb && cb();
                    }
                    this.hasLeaveVoiceRoom = true;
                }, "levelGameRoom");
            } else {
                this.appClass.callWithBack(() => {
                }, "levelGameRoom");
            }
        }
        /**
         * 屏蔽制定用户音频
         * data {
         *  "uId": number,用户ID
         *  "muted":boolean false取消 true屏蔽
         *
         *  cb 回掉参数 {"status" : 0, "uid" : uid}
         *  TODO::1.4.3 版本增加，针对zego优化，加入语音房需要延迟执行mute unmute，房间里如果多个人都是mute，由于异步，so回掉里增加idx和status状态
         * }
         */
        muteRemoteAudioStream(uId: number, muted: boolean, cb: Function) {
            var data = {
                "uId": uId,
                "muted": muted
            }
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((res) => {
                        if (typeof res == 'string') res = JSON.parse(res);
                        var status = res.status;
                        var uid = res.uid;
                        !!cb && cb(status, uid);
                    }, "muteRemoteAudioStream", JSON.stringify(data));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((res) => {
                        if (typeof res == 'string') res = JSON.parse(res);
                        var status = res.status;
                        var uid = res.uid;
                        !!cb && cb(status, uid);
                    }, "muteRemoteAudioStream:", data);
                    break;
                default:
                    break;
            }
        }

        shortScreenNative() {//截屏分享
            if (laya.utils.Browser.window.conch) {
                laya.utils.Browser.window.conch.captureScreen((arrayBuff, width, height) => {
                    let obj;
                    switch (yalla.Native.instance.deviceType) {
                        case DeviceType.Android:
                            let path: string = laya.net.LocalStorage.getItem("filePath") + "/share.jpg";
                            laya.utils.Browser.window.conch.saveAsJpeg(arrayBuff, width, height, path);
                            obj = {
                                "moduleId": 1,
                                "platform": 1,
                                "title": "Join me in a match",
                                "subTitle": "Yallaludo is really fun, let's play together",
                                "url": "https://play.google.com/store/apps/details?id=com.yalla.yallagames",
                                "image": path,
                                "screenShot": true
                            };
                            this.appClass.callWithBack(() => { }, "compartirGames", JSON.stringify(obj));
                            break;
                        case DeviceType.IOS:
                            obj = {
                                "moduleId": 1,
                                "platform": 1,
                                "title": "Join me in a match",
                                "subTitle": "Yallaludo is really fun, let's play together",
                                "url": "https://itunes.apple.com/cn/app/id1419536376?mt=8",
                                "image": "share.jpg",
                                "screenShot": true
                            };
                            laya.utils.Browser.window.conch.saveAsJpeg(arrayBuff, width, height, laya.utils.Browser.window.conch.getCachePath() + "/share.jpg");
                            this.appClass.callWithBack(() => { }, "compartirGames:", JSON.stringify(obj));
                            break;
                        default:
                            break;
                    }
                })
            }
        }
        /**
         * 初始化游戏完成时调用
         * @param cb 
         * @param boo 
         */
        initGames(cb, boo) {
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((datas) => {
                        !!cb && cb(datas);
                    }, "initGames", boo)
                    break;
                // case DeviceType.IOS:
                // this.appClass.callWithBack(() => { }, "initGames:", boo);
                // break;
                // default:
                // break;
            }
        }
        /**
         * 获取当前背景音乐的状态
         * @param cb 
         */
        currentBackgroundMusicState(cb: Function) {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.callWithBack((data: any) => {
                if (data && typeof data == 'string') data = yalla.util.parse(data);
                cb && cb(data);
                // cb(JSON.parse(data));
            }, "currentBackgroundMusicState");
        }
        /**
         * 修改背景音乐状态
         */
        changeCurrentBackgroundMusicState(json: Object) {
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("changeCurrentBackgroundMusicState", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.call("changeCurrentBackgroundMusicState:", JSON.stringify(json));
                    break;
                default:
                    break;
            }
        }
        /**
        * 获取当前音效的状态
        * @param cb 
        */
        currentGameSoundState(cb: Function) {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.callWithBack((data) => {
                if (data && typeof data == 'string') data = yalla.util.parse(data);
                cb && cb(data);
            }, "currentGameSoundState")
        }
        /**
         * 修改背景音效状态.
         */
        changeCurrentGameSoundState(json: Object) {
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("changeCurrentGameSoundState", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.call("changeCurrentGameSoundState:", JSON.stringify(json));
                    break;
                default:
                    break;
            }
        }
        /**
        * 获取当前音效的音量
        * @param cb 
        */
        systemVolume(cb: Function) {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.callWithBack((data) => {
                if (data && typeof data == 'string') data = yalla.util.parse(data);
                cb && cb(data);
            }, "systemVolume")
        }

        /**
        * 资源加载失败执行app
        */
        initEngineFailed() {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.call("loadResFail");
        }

        /**
         * rule
         * ?gameType=10019&lang=1&mode=0
         * ruleType 1.ludo规则 2ludo道具规则 3ludo组队规则 4 ludo黑夜模式  5蛇棋  6duel模式 7丛林模式 8jackraoo简单 9jack复杂 10jack1v1
         */
        showGameRules(gameid, mode, ruleType = 1) {
            var rule: showRule = {
                gameType: gameid,
                mode: mode,
                ruleType: ruleType
            }
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("showGameRules", JSON.stringify(rule));
                    break;
                case DeviceType.IOS:
                    this.appClass.call("showGameRules:", JSON.stringify(rule));
                    break;
                default:
                    break;
            }
        }

        /**
         * 友盟埋点
         * @param event 
         * @param attr 
         */
        mobClickEvent(event: string, attr: Object = null) {
            var json = { event: event, attributes: attr };
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("mobClickEvent", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.call("mobClickEvent:", JSON.stringify(json));
                    break;
                default:
                    break;
            }
        }

        /**
        * 最新埋点
        * @param type   10077
        * @param eventName
        * @param mesg
        */
        clogDBAmsg(type: number, eventName: string, mesg: Object = {}) {
            var json = { type: type, eventName: eventName, mesg: mesg };
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("clogDBAmsg", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.call("clogDBAmsg:", JSON.stringify(json));
                    break;
                default:
                    break;
            }
        }

        /**游戏内埋点统计 */
        writeGameLog(eventName: string, mesg: Object = {}) {
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("writeLog", JSON.stringify({ eventName: eventName, mesg: mesg }));
                    break;
                case DeviceType.IOS:
                    this.appClass.call("writeLog:", JSON.stringify({ tag: eventName, msg: mesg }));
                    break;
                default:
                    break;
            }
        }

        /**
         * 获取游戏数据 是否debug
         * @param cb 
         */
        loadProfileInfo(cb: Function) {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.callWithBack((data) => {
                if (data && typeof data == 'string') data = yalla.util.parse(data);
                cb && cb(data);
            }, "loadProfileInfo")
        }
        /**
         * @param name IOS播放音效 ["1kills","2kills","3kills","4kills","5kills","6kills","cheer","domino_fail","ludo_fail"]
         */
        playAudio(name: string, cb?: Function) {
            var json = { name: name };
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack(() => {
                        cb && cb();
                    }, "playAudio", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack(() => {
                        cb && cb();
                    }, "playAudio:", JSON.stringify(json));
                    break;
                default:
                    break;
            }
        }
        playSkinSound(json: string, cb?: Function) {
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack(() => {
                        cb && cb();
                    }, "playAudio", json);
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack(() => {
                        cb && cb();
                    }, "playAudio:", json);
                    break;
                default:
                    break;
            }

        }
        stopAllAudio() {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.call("stopAllAudio");
        }

        /**
         * @param json  {name，state：0是重新开始播放 1播放 2暂停}
         * @param cb 
         */
        playMusic(json: Object, cb?: Function) {
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack(() => {
                        cb && cb();
                    }, "playMusic", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack(() => {
                        cb && cb();
                    }, "playMusic:", JSON.stringify(json));
                    break;
                default:
                    break;
            }
        }
        /**
         * 获取ip
         * @param cb 
         * 回调方法中data就是ip
         */
        getHostIp(host, cb: Function) {
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                        cb && cb(data);
                    }, "getHostIp", JSON.stringify({ host: host }));
                    break;
                case DeviceType.IOS:
                    var arr = String(host).split("//");
                    host = arr[1] ? arr[1] : host;
                    this.appClass.callWithBack((data) => {
                        cb && cb(data);
                    }, "getHostIp:", JSON.stringify({ host: host }));
                    break;
                default:
                    break;
            }
        }
        /**新手引导结束调用 */
        noviceComplete(isFinish: boolean = false, mode = null) {
            if (this.deviceType == DeviceType.Browser) return;
            var json = {
                gameType: yalla.Global.gameType,
                isFinish, mode
            }
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("noviceComplete", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.call("noviceComplete:", JSON.stringify(json));
                    break;
                default:
                    break;
            }
        }
        //隐藏 规则webview
        removeGameRulesView() {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.call("removeGameRulesView");
        }
        //快速购买
        buyGood(cb: Function) {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.callWithBack((json) => {
                var obj = yalla.util.parse(json);
                cb && cb(obj);
            }, "buyGood");
        }
        getSystemInfo(cb: Function) {
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                        cb && cb(data);
                    }, "getSystemInfo");
                    break;
            }
        }
        /**
         * 获取语音授权
         */
        currentAudioStatus(cb: Function) {
            if (this.deviceType == DeviceType.Browser) {
                cb({ "status": 0 });
                return;
            }
            this.appClass.callWithBack((json) => {
                var obj = yalla.util.parse(json);
                cb(obj);
            }, "currentAudioStatus");
        }

        /**
         * 返回锦标赛
         */
        backChampion(back: boolean = this.isBack) {
            yalla.util.closeAllDialog();
            laya.media.SoundManager.stopAll();
            yalla.Voice.instance.levelGameRoom();
            yalla.common.connect.ReconnectControl.instance.hideConnect();
            yalla.Global.Account.ip = '';
            yalla.Global.Account.token = '';
            yalla.Global.Account.encryptToken = '';
            yalla.Global.Account.roomid = null;
            yalla.Global.Account.autoAddExp = null;
            yalla.Global.IsUpdateZegoToken = false;
        }

        /**
         * 1获取锦标赛信息&用户信息 2报名 3放弃 4匹配 5取消匹配    6更新匹配信息匹配成功 7胜负通知
         * ChampionshipOption(int optionType)
         * {
            "option": 1,
            "data": {}
            }
         * @param cb 
         */
        ChampionshipOption(json: any, cb: Function) {
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                        var obj = yalla.util.parse(data);
                        cb && cb(obj);
                    }, "championshipOption", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                        var obj = yalla.util.parse(data);
                        cb && cb(obj);
                    }, "championshipOption:", JSON.stringify(json));
                    break;
            }
        }

        showMoney(json: any) {
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                    }, "showMoney", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                    }, "showMoney:", JSON.stringify(json));
                    break;
            }
        }

        /**
         * 金币不足，打开大厅的shop
         * @param json 
         */
        showShop() {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.call("showShop");
        }

        /**
         * 提示大厅显示toast
         * @param json {msg:'Please check your network connection'}
         */
        showToast(json: any) {
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                    }, "showToast", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                    }, "showToast:", JSON.stringify(json));
                    break;
            }
        }

        downLayaBoxFile(folderName: string, fileNames: Array<string>) {
            if (this.deviceType == DeviceType.Browser) return;
            var list = {
                file: folderName,
                fileNames: fileNames
            }
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("downLayaBoxFile", JSON.stringify(list));
                    break;
                case DeviceType.IOS:
                    this.appClass.call("downLayaBoxFile:", JSON.stringify(list));
                    break;

            }
        }
        downloadFileByFileName(path, url, cb: Laya.Handler = null) {
            var p = { url: url, path: path };
            switch (this.deviceType) {
                case DeviceType.Android:
                    // if (!yalla.Global.googleInfo || !yalla.Global.googleInfo.isCanDownload) {
                    this.appClass.callWithBack((data) => {
                        cb && cb.runWith(data);
                        this.event("downloadFileByUrl", data);
                    }, "downloadFileByUrl", JSON.stringify(p));
                    // }   
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                        if (data && typeof data == 'string') data = yalla.util.parse(data);
                        cb && cb.runWith(data);
                        this.event("downloadFileByUrl", data);
                    }, "downloadFileByUrl:", JSON.stringify(p));
                    break;
                case DeviceType.Browser:
                    cb && cb.runWith({
                        url: url,
                        path: path,
                        localPath: url,
                        success: true
                    })
                    break;
            }
        }
        downloadImgByUrl(path: string, url: string, cb) {
            var p = { url: url, path: path };
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                        cb && cb(data);
                    }, "downloadImgByUrl", JSON.stringify(p));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                        if (data && typeof data == 'string') data = yalla.util.parse(data);
                        cb && cb(data);
                    }, "downloadImgByUrl:", JSON.stringify(p));
                    break;
                case DeviceType.Browser:
                    cb({
                        url: url,
                        path: path,
                        localPath: url,
                        success: true
                    })
                    break;
            }
        }

        /**
         * 被T后，执行ios的kicketOut，避免游戏和原生都弹出被T的弹框
         * 原生的处理逻辑（弹出被T的弹框，点击确认按钮，回调alertAccountLoggedView，然后返回到登录页 ）
         * @param cb 
         */
        alertAccountLoggedView(cb) {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.callWithBack((data) => {
                var obj = yalla.util.parse(data);
                cb && cb(obj);
            }, "alertAccountLoggedView");
        }

        /**
         * 显示跑马灯
         * @param json {status:1} 0:停止 1:滚动
         */
        showMaquee(json: any) {
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                    }, "showMaquee", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                    }, "showMaquee:", JSON.stringify(json));
                    break;
            }
        }
        writeLog(msg: string) {
            var json = {
                tag: "GameAlert",
                msg: {
                    errorMsg: msg
                }
            }
            this.appClass.call("writeLog:", JSON.stringify(json));
        }
        callKeyBoard(type: number = 1, msg: string = "") {//1表示文字输入,2表示小黄脸表情
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((n) => {
                        this.event(this.Event.SHOWKEYBOARD, n);
                    }, "callKeyBoard", JSON.stringify({ type: type, msg: msg }));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((n) => {
                        // this.event(this.Event.SHOWKEYBOARD, n);
                    }, "callKeyBoard:", JSON.stringify({ type: type, msg: msg }));
                    break;
                default:
                    break;
            }
        }

        closeKeyBoard() {
            if (this.deviceType == DeviceType.Browser) return;
            Laya.stage.y = 0;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("closeKeyBoard");
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack(() => { }, "closeKeyBoard:", "");
                    break;
            }
        }
        /**
         * 调起个人详情
         * @param json {idx:, muteStatus:1屏蔽 0取消屏蔽}
         */
        showUserProfile = yalla.util.throttle((idx: number) => {
            var muteStatus = yalla.Mute.muted(idx) ? 1 : 0;
            var isWatching: boolean = false;
            switch (yalla.Global.gameType) {//是否是观战
                case GameType.DOMINO:
                    isWatching = Boolean(yalla.Global.Account.leagueWatch)
                    break;
                case GameType.LUDO:
                    isWatching = Boolean(ludoRoom.instance.watchIdx);
                    break;
                // case GameType.SNAKEANDLADER:
                //     isWatching = Boolean(yalla.data.snake.UserService.instance.room.watchIdx)
                //     break;
            }
            var json = { idx: idx, muteStatus: muteStatus, gameId: yalla.Global.gameType, roomId: yalla.Global.Account.roomid, isWatching: isWatching };
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                    }, "showUserProfile", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                    }, "showUserProfile:", JSON.stringify(json));
                    break;
            }
        }, 1000)
        // showUserProfile(idx: number) {
        // }

        /**
         * 关闭个人详情
         * ios 需要传参，默认空
         */
        closeUserProfile() {
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("closeUserProfile");
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                    }, "closeUserProfile:", "");
                    break;
            }
        }

        /**
         * 聊天消息举报
         * @param json sourceId（gameId 10021） 、gameRoomType、beReportedUserId、onlineNum、onLookerNum、chatInfo
         */
        reportMsg(json: any) {
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                    }, "reportMsg", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                    }, "reportMsg:", JSON.stringify(json));
                    break;
            }
        }

        /**
        * 关闭举报面板
        */
        closeReportMsg() {
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("closeReportMsg");
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => { }, "closeReportMsg:", "");
                    break;
            }
        }
        /**
         * 用户动作记录添加
         * @param actionCode 编号
         * @param guidActionType 操作类型 1：打开 2：点击
         * @param isSkip 是否跳过 0： false 1：true
         * @param isAllDone 是否全部完成 0：false 1：true
         * @param actionType 动作类型 1注销账号 2引导步骤
         * @param languageId 语区 1：英语 2:阿语
         */
        accountActionRecordAdd(actionCode: number, isSkip: number, isAllDone: number, guidActionType: number, actionType: number = 2) {
            if (this.deviceType == DeviceType.Browser) return;
            var data = JSON.stringify({
                actionCode: actionCode,
                guidActionType: guidActionType,
                actionType: actionType,
                languageId: yalla.Font.lanIndex,
                isSkip: isSkip,
                isAllDone: isAllDone
            })
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack(() => { }, "accountActionRecordAdd", data);
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack(() => { }, "accountActionRecordAdd:", data);
                    break;
            }
        }

        getGooglePlayInfo(cb: Function) {//1.3.1添加此方法，目前只有Android使用
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                        if (data && typeof data == 'string') data = yalla.util.parse(data);
                        cb && cb(data);
                    }, "getGooglePlayInfo");
                    break;
            }
        }

        zegoTokenResponse(token, channelId) {
            if (this.deviceType == DeviceType.Browser) return;
            var data = JSON.stringify({ token: token, channelId: channelId });
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("zegoTokenResponse", data);
                    break;
                case DeviceType.IOS:
                    this.appClass.call("zegoTokenResponse:", data);
                    break;
            }
        }

        /**
         * 表情列表
         * @param cb 
         * @param state 0不刷新列表  1需要重新拉去列表
         */
        emojiResponse(cb, state = 0) {
            if (this.deviceType == DeviceType.Browser) return;
            var data = JSON.stringify({ state: state });
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                        var obj = yalla.util.parse(data);
                        cb && cb(obj);
                    }, "emojiResponse", data);

                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                        var obj = yalla.util.parse(data);
                        cb && cb(obj);
                    }, "emojiResponse:", data);
                    break;
            }
        }

        /**
        * 好友聊天面板  1打开  0关闭
        {gameId,status,pos:{bottom:0, arrowX:0, screenScale:0}}
        */
        friendChat(gameI, status: number = 0, pos = null) {
            if (this.deviceType == DeviceType.Browser) return;
            var data: any = { gameId: gameI, status: status };
            if (pos) data['pos'] = pos;
            data = JSON.stringify(data);
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("friendChat", data);
                    break;
                case DeviceType.IOS:
                    this.appClass.call("friendChat:", data);
                    break;
            }
        }

        /**
        * 打开道具
        {gameId}
        */
        propDropImgClick(gameId: number) {
            if (this.deviceType == DeviceType.Browser) return;
            var data: any = { gameId: gameId };
            data = JSON.stringify(data);
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("propDropImgClick", data);
                    break;
                case DeviceType.IOS:
                    this.appClass.call("propDropImgClick:", data);
                    break;
            }
        }

        /**
        * 购买表情包
        {id 主题id, price:表情价格, priceType:价格类型 (1 金币 2钻石)（原生需要弹出对应购买钻石礼包，如果钻石不够的话 1.4.4）}
        */
        buyEmojiTheme(id: number, price: number = 0, priceType = 0, cb: Function = null) {
            if (this.deviceType == DeviceType.Browser) return;
            var data: any = { id: id, price: price, priceType: priceType };
            data = JSON.stringify(data);
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                        /**{result : 请求结果（0:失败 1:成功） , code: 错误码, id:主题id} */
                        if (data && typeof data == 'string') data = yalla.util.parse(data);
                        cb && cb(data);
                    }, "buyEmojiTheme", data);

                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                        /**{result : 请求结果（0:失败 1:成功） , code: 错误码, id:主题id} */
                        if (data && typeof data == 'string') data = yalla.util.parse(data);
                        cb && cb(data);
                    }, "buyEmojiTheme:", data);
                    break;
            }
        }

        /**
         * 获取备用域名方法
         */
        getDomainType(cb: Function = null) {
            if (this.deviceType == DeviceType.Browser) return;
            var typeList: any = [DomainType.GAME_ACCOUNT, DomainType.COMMON_FILE, DomainType.GAME_ACTIVITY];
            typeList = JSON.stringify(typeList);
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                        if (data && typeof data == 'string') data = yalla.util.parse(data);
                        yalla.Global.DomainTypeInfo = data;
                        cb && cb(data);
                    }, "getDomainType", typeList);

                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                        if (data && typeof data == 'string') data = yalla.util.parse(data);
                        yalla.Global.DomainTypeInfo = data;
                        cb && cb(data);
                    }, "getDomainType:", typeList);
                    break;
            }
        }

        /**
         * WatchRoomReply 结果通知原生
         * @param json {"code":xxx}
         */
        watchResult(json: any) {
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                    }, "watchResult", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                    }, "watchResult:", JSON.stringify(json));
                    break;
            }
        }
        /**
         * 通过原生请求ajax  需要zues加密
         *resultCode =200 成功
         * @param json 
         */
        sendHttpRequest(url, json, cb?) {
            if (this.deviceType == DeviceType.Browser) return;
            var sendData = { url: url, data: json };
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                        if (data) {
                            if (typeof data == 'string') data = yalla.util.parse(data);
                            if (typeof data.data == 'string') data.data = yalla.util.parse(data.data);
                            cb && cb(data.data.resultCode);
                        }
                    }, "sendHttpRequest", JSON.stringify(sendData));
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                        if (data) {
                            if (typeof data == 'string') data = yalla.util.parse(data);
                            if (typeof data.data == 'string') data.data = yalla.util.parse(data.data);
                            cb && cb(data.data.resultCode);
                        }
                    }, "sendHttpRequest:", JSON.stringify(sendData));
                    break;
            }
        }
        /**
         * 获取代理信息
         */
        getProxyInfo(url, cb) {
            var sendData = JSON.stringify({ url: url });
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                        Debug.log("====代理信息：");
                        Debug.log(data);
                        if (typeof data == 'string') data = yalla.util.parse(data);
                        cb && cb(data);
                    }, "getProxyInfo", sendData);
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                        Debug.log("====代理信息：");
                        Debug.log(data);
                        if (typeof data == 'string') data = yalla.util.parse(data);
                        cb && cb(data);
                    }, "getProxyInfo:", sendData);
                    break;
            }
        }
        /*
        * 调用原生手机震动
        */
        phoneVibrate() {
            if (this.deviceType == DeviceType.Browser) return;
            // if (yalla.Native.instance.deviceType == DeviceType.IOS) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("phoneVibrate");
                    break;
                case DeviceType.IOS:
                    this.appClass.call("phoneVibrate");
                    break;
            }
        }
        /*
        * 断开try网关
        */
        sendNetTyrClose() {
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("sendNetTyrClose");
                    break;
                case DeviceType.IOS:
                    this.appClass.call("sendNetTyrClose");
                    break;
            }
        }
        /*
        * 调用原生拷贝日志
        */
        copyLog(data: string) {
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.call("copyLog", data);
                    break;
                case DeviceType.IOS:
                    this.appClass.call("copyLog:", data);
                    break;
            }
        }

        /**
         * 是否是配置低端手机
         * @param cb 
         * @returns 
         */
        isLowPerformanceDevice(cb: Function) {//1.3.1添加此方法，目前只有Android使用
            if (this.deviceType == DeviceType.Browser) return;
            switch (this.deviceType) {
                case DeviceType.Android:
                    this.appClass.callWithBack((data) => {
                        if (data && typeof data == 'string') data = yalla.util.parse(data);
                        cb && cb(data);
                    }, "isLowPerformanceDevice");
                    break;
                case DeviceType.IOS:
                    this.appClass.callWithBack((data) => {
                        if (data && typeof data == 'string') data = yalla.util.parse(data);
                        cb && cb(data);
                    }, "isLowPerformanceDevice");
                    break;
            }
        }
        /**
         * 显示购买同款商店
         * @param players 需要根据业务需要排好序 例如ludo的color 0: 红 1:绿 2:黄 3:蓝,，无颜色的游戏传蓝色3
         * @param diamond 钻石
         * @param money 金币
         */
        showSameStyleStore(players: Array<{ idx: number, faceUrl: string, nickName: string, color: number }>, diamond: number, money: number) {
            if (this.deviceType == DeviceType.Browser) return;
            var callName = this.deviceType == DeviceType.IOS ? "showSameStyleStore:" : "showSameStyleStore";
            this.appClass.call(callName, JSON.stringify({
                players, diamond, money, gameId: yalla.Global.Account.gameId
            }));
            yalla.util.clogDBAmsg("10408");
        }
        removeSameStyleStore() {
            if (this.deviceType == DeviceType.Browser) return;
            this.appClass.call("removeSameStyleStore");
        }
        closeAllNativeView() {
            this.closeReportMsg();
            this.closeUserProfile();
            this.removeGameRulesView();
            this.removeSameStyleStore();
            this.closeKeyBoard();
        }
    }
}