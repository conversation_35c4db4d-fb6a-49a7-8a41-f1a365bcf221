module yalla {
    export class NativeWebSocket extends Laya.EventDispatcher {

        private static _instance: yalla.NativeWebSocket;
        static get instance(): yalla.NativeWebSocket {
            if (!this._instance)
                this._instance = new yalla.NativeWebSocket();
            return this._instance;
        }
        constructor() {
            super();
            window["onNetOpen"] = (n) => {
                yalla.Debug.log("--NativeWebSocket.onNetOpen--")
                this.event(Laya.Event.OPEN);
            }
            window["onNetMsg"] = (res) => {
                // yalla.Debug.log("<<< NativeWebSocket.onNetMsg >>>>" + res);
                if (typeof res == "string") res = eval(res);
                if (yalla.Global.Account.leagueWatch) {
                    this.event('Message_onNetMsg', [res])
                } else {
                    this.event(Laya.Event.MESSAGE, [res])
                }
            }

            window["onNetResponseMsg"] = (res) => {
                if (typeof res == "string") res = eval(res);
                yalla.Debug.log("--NativeWebSocket.onNetResponseMsg--");
                this.event('Message_onNetResponseMsg', [res])
            }
            window["onNetClose"] = (res) => {
                if (yalla.Global.Account.leagueWatch) return;
                yalla.Debug.log("--NativeWebSocket.onNetClose--")
                this.event(Laya.Event.CLOSE);

                yalla.DomainUtil.instance.checkProxy_changeSocketUrl(res);
            }
            window["onNetError"] = (res) => {
                if (yalla.Global.Account.leagueWatch) return;
                yalla.Debug.log("--NativeWebSocket.onNetError--");
                this.event(Laya.Event.ERROR);
                yalla.DomainUtil.instance.checkProxy_changeSocketUrl(res);
            }
            window["onNetConnecting"] = () => {
                yalla.Debug.log("--NativeWebSocket.onNetConnecting--")
                if (!yalla.Global.onlineMode) {
                    return;
                }

                var justShowAni = false;
                if (yalla.Global.gameType == GameType.SNAKEANDLADER || yalla.Global.gameType == GameType.JACKARO || yalla.Global.Account.leagueWatch) {//TODO::其实收到这个消息，只需要展示小马动画，无需重连，这里先从蛇棋开始(domino 联赛)
                    yalla.Global.isconnect = false;
                    justShowAni = true;
                    if (yalla.Global.Account.leagueWatch) {
                        yalla.data.RoomService.instance.resetData();
                        var watchThink = yalla.common.connect.WatchThinking._instance;
                        watchThink && watchThink.hide();
                    }
                }
                yalla.common.connect.ReconnectControl.instance.connect(true, justShowAni);
                // yalla.common.connect.ReconnectControl.instance.connect(true, true);
            }
            window["onWatchError"] = (res) => {
                yalla.Debug.log("--NativeWebSocket.onWatchError--");
                this.event("onWatchError", [res]);
            }
        }

        init() {

        }

        /**
        * 通知原生连socket
        */
        connectSocket(): void {
            var deviceType = yalla.Native.instance.deviceType;
            if (deviceType == DeviceType.Browser) return;

            var url = ''
            if (!DomainUtil.instance.socket_urlList || DomainUtil.instance.socket_urlList.length <= 0) {
                url = `${yalla.Global.Account.host}:${yalla.Global.Account.port}`;
                if (yalla.Global.Account.url && yalla.Global.Account.url.length > 0) {
                    url = yalla.Global.Account.url;
                    if (deviceType == DeviceType.Android && Number(yalla.Global.Account.androidVersion) < 23) {
                        //TODO::端口0或1以下则不拼port    *******
                        if (Number(yalla.Global.Account.port) <= 0) url = yalla.Global.Account.host;
                        else url = `${yalla.Global.Account.host}:${yalla.Global.Account.port}`;
                    }
                }
            } else {
                url = yalla.Global.Account.url ? yalla.Global.Account.url : "";
            }


            // url = 'ws://*************:9091'
            // url = 'ws://fat-domino.yalla.games:9038';
            // yalla.Global.Account.port = '9037';
            // yalla.Global.Account.url = url;
            // url = 'ws://*************:9091';
            var data = JSON.stringify({
                url: url,
                gameId: yalla.Global.Account.gameId,
                connectType: yalla.Global.Account.connectType
            });
            yalla.Debug.log(yalla.Global.Account.url + '-socket连接-connectSocket--url:' + url + '--' + Number(yalla.Global.Account.androidVersion));
            yalla.Debug.log(data)
            switch (deviceType) {
                case DeviceType.Android:
                    yalla.Native.instance.appClass.callWithBack(() => { }, "connectSocket", data);
                    break;
                case DeviceType.IOS:
                    yalla.Native.instance.appClass.callWithBack(() => { }, "connectSocket:", data);
                    break;
            }
        }

        /**
         * 发送请求
         * @param jsonstring  bytearray
         */
        sendNetMsg(json: any) {
            var deviceType = yalla.Native.instance.deviceType;
            if (yalla.Native.instance.deviceType == DeviceType.Browser) return;
            switch (deviceType) {
                case DeviceType.Android:
                    yalla.Native.instance.appClass.callWithBack((data) => {
                    }, "sendNetMsg", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    yalla.Native.instance.appClass.callWithBack((data) => {
                    }, "sendNetMsg:", JSON.stringify(json));
                    break;
            }
        }

        /**
         * 发送请求(观战)
         * @param jsonstring  bytearray
         */
        sendNetMsg_watch(json: any) {//command 5聊天 6退出房间
            yalla.Debug.log('-----发送联赛观战消息sendNetMsg_watchg----' + JSON.stringify(json));
            var deviceType = yalla.Native.instance.deviceType;
            if (yalla.Native.instance.deviceType == DeviceType.Browser) return;
            yalla.Debug.log("sendNetMsg_watch:" + JSON.stringify(json))
            switch (deviceType) {
                case DeviceType.Android:
                    yalla.Native.instance.appClass.callWithBack((data) => {
                        yalla.Debug.log('==发送请求观战消息 响应 android===');
                    }, "sendNetMsg_watch", JSON.stringify(json));
                    break;
                case DeviceType.IOS:
                    // var msg = laya.utils.Browser.window.Base64.encode(json);
                    yalla.Native.instance.appClass.callWithBack((data) => {
                        yalla.Debug.log('==发送请求观战消息 响应 ios===');
                    }, "sendNetMsg_watch:", JSON.stringify(json));
                    break;
            }
        }
        /**
         * 断线重连
         * @param
         * 换域名，直接再走connectsocket
         */
        sendNetReConnect() {
            // yalla.Native.instance.appClass.call("sendNetReConnect");
            this.connectSocket();
        }

        /**
         * close socket
         * @param
         */
        sendNetClose() {
            yalla.Debug.log('-----sendNetClose----');
            if (yalla.Native.instance.deviceType == DeviceType.Browser) return;
            yalla.Native.instance.appClass.call("sendNetClose");
        }
    }
}