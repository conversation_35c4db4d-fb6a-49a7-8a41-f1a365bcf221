module yalla {
    //震动开关
    // 0:关闭，1：开启
    export class PhoneVibrate {
        static _phoneVibrateSetting_cache: boolean = true
        static set phoneVibrateSetting_cache(value: boolean) {
            var isOpen = Boolean(value);
            Laya.LocalStorage.setJSON("phoneVibrate_setting_cache", { isOpen });
            this._phoneVibrateSetting_cache = isOpen;
        }
        static isOpen(): boolean {
            return this._phoneVibrateSetting_cache;
        }
        static init() {
            var cache = Laya.LocalStorage.getJSON("phoneVibrate_setting_cache");
            if (cache) {
                this._phoneVibrateSetting_cache = cache.isOpen;
            }
        }
    }
}