

module yalla {
    export class Screen {
        public static clientScale = 750 / 1334;//
        public static screenWidth = 750
        public static screenHeight = 1334;
        public static screen_top_height = 0;
        public static screen_top_width = 0;
        public static screen_scale = 0;
        public static showAllMaxScale: number = 0.9//接近于正方形屏幕比例
        public static fixedHeightMaxScale: number = 0.562;//设计尺寸的比例
        /**
         * 屏幕尺寸发生变化且需要重新设置stage适配的时候调用，resetSize为true的时候会触发Laya.Event.RESIZE,不要在该事件监听中执行此方法，否则会死循环
         * @param resetSize 是否重新计算stage的宽高 默认false
         * @param width 屏幕宽度，默认为Laya.Browser.clientWidth
         * @param height 屏幕高度，默认为Laya.Browser.clientHeight
         */
        static onResize(resetSize: boolean = false, width: number = Laya.Browser.clientWidth, height: number = Laya.Browser.clientHeight) {
            var pixelRatio = Laya.Browser.pixelRatio,
                designHeight = Laya.stage.designHeight,
                designWidth = Laya.stage.designWidth;
            this.clientScale = width / height;
            this.screenWidth = width * pixelRatio;
            this.screenHeight = height * pixelRatio;
            if (this.clientScale > this.fixedHeightMaxScale) {
                if (this.clientScale > this.showAllMaxScale) {//比例大于0.9 接近于正方形屏幕 折叠屏或者平板公平 暂时处理两边留黑
                    Laya.stage.scaleMode = Laya.Stage.SCALE_SHOWALL;
                    resetSize && Laya.stage.setScreenSize(designWidth, designHeight);
                } else {
                    Laya.stage.scaleMode = Laya.Stage.SCALE_FIXED_HEIGHT;//比例大于设计尺寸的比例 一般为平板竖屏 固定高度，宽度根据实际比例和设计高度计算得出
                    resetSize && Laya.stage.setScreenSize(width / height * designHeight, designHeight);
                }
                this.screen_scale = this.screenHeight / designHeight;
            } else {//比例小于设计尺寸比例 一般为全面屏手机 固定宽度，高度根据实际比例和设计宽度计算得出
                Laya.stage.scaleMode = Laya.Stage.SCALE_FIXED_WIDTH;
                resetSize && Laya.stage.setScreenSize(designWidth, height / width * designWidth);
                this.screen_scale = this.screenWidth / designWidth;
            }
            var h = designHeight * this.screen_scale;
            var w = designWidth * this.screen_scale;
            this.screen_top_height = Math.round((this.screenHeight - h) / 2);
            this.screen_top_width = Math.round((this.screenWidth - w) / 2);
            // let root: Laya.Sprite = Laya.stage as Laya.Sprite;
            // root.graphics.clear();
            // root.graphics.drawRect(0, 0, root.width, root.height, "#ffffff");
        }
        /**
         * 超宽屏幕下返回false 其它按照原生给的值返回
         */
        static get hasHair(): boolean {
            return this.clientScale > this.showAllMaxScale ? false : yalla.Global.ProfileInfo.hasHair;
        }
        /**
         * 超宽屏幕下返回0 其它按照原生给的值返回
         */
        static get hairHeight(): number {
            return this.clientScale > this.showAllMaxScale ? 0 : yalla.Global.ProfileInfo.hairHeight;
        }
        /**
         * 舞台高度/宽度比例
         */
        static get screen_ratio(): number {
            return Laya.stage.height / Laya.stage.width;
        }
    }
}