module yalla{
    export class SkeletonManager extends Laya.EventDispatcher {

        static _instance: SkeletonManager;
        private _templateDic: Object;
        private _templateLoadEndDic: Object;
        private _skeletonDic: Object;

        constructor() {
            super();
        }

        static get instance(): SkeletonManager {
            return SkeletonManager._instance || (SkeletonManager._instance = new SkeletonManager());
        }

        /**
         * 如果一个动画不需要创建多个sk对象，则k可以不传，默认key就是uName
         * @param uName 
         * @param k 
         */
        public initSk(uName:string, k:string = ''): void {
            if (!this._templateDic) this._templateDic = {};
            if (!this._templateLoadEndDic) this._templateLoadEndDic = {};
            if (!this._skeletonDic) this._skeletonDic = {};
            var key = k;
            if (key.length < 1) key = uName;

            if (!this._templateLoadEndDic[key]) {
                if (!this._templateDic[key]) this._templateDic[key] = new Laya.Templet();
                this._templateDic[key].on(Laya.Event.COMPLETE, this, () => {
                    if (this._skeletonDic && this._templateDic[key]) {
                        if (!this._skeletonDic[key]) this._skeletonDic[key] = [];
                        var sk = this._templateDic[key].buildArmature(1)
                        this._skeletonDic[key].push(sk);
                        this._templateLoadEndDic[key] = true;
                        this.event(key, sk);
                    }
                });
                if (this._templateDic[key]) {
                    var url = yalla.getSkeleton(uName);
                    this._templateDic[key].loadAni(url);
                }

            } else {
                if (!this._skeletonDic[key]) this._skeletonDic[key] = [];
                if (this._templateDic[key]) {
                    var sk = this._templateDic[key].buildArmature(1)
                    this._skeletonDic[key].push(sk);
                    this.event(key, sk);
                }
            }
            
        }

        public clearByName(name:string): void{
            if (this._skeletonDic && this._skeletonDic[name]) {
                var arr = this._skeletonDic[name];
                for (var i = 0; i < arr.length; i++){
                    arr[i].offAll();
                    arr[i].destroy();
                }
                this._skeletonDic[name] = null;
            }
            if (this._templateDic && this._templateDic[name]) {
                this._templateDic[name].offAll();
                this._templateDic[name].destroy();
                this._templateDic[name] = null;
            }
        }

        public clear(): void{
            for (var k in this._skeletonDic) {
                if (this._skeletonDic[k]) {
                    var arr = this._skeletonDic[k];
                    for (var i = 0; i < arr.length; i++){
                        arr[i].destroy(true);
                    }
                }
            }
            for (var k in this._templateDic) {
                if (this._templateDic[k]) {
                    this._templateDic[k].offAll();
                    this._templateDic[k].destroy();
                }
            }
            this._skeletonDic = null;
            this._templateDic = null;
            this.offAll();
        }
    }
}