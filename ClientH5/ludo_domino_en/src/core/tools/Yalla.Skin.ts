module yalla {
    export class Skin {
        private skinData = null;
        public version: number = 0;

        private successCallBack: Function = null;
        static _instance: yalla.Skin = null;
        constructor() { }
        init(successCallBack: Function) {
            yalla.DomainUtil.instance.initDomainData();
            // this.setDomainData();

            this.successCallBack = successCallBack;
            var v = Laya.LocalStorage.getItem("skinVersion");
            yalla.File.init();
            if (v) {
                this.version = parseInt(v);
                if (this.version > 0 && !yalla.File.existed(yalla.File.folder + yalla.File.relativePath)) {
                    this.version = 0;
                }
                if (this.successCallBack) {
                    this.successCallBack();
                    this.successCallBack = null;
                }
            } else/* if (yalla.Native.instance.deviceType == DeviceType.IOS) */ {
                this.version = 11;
                yalla.Debug.log(this.isCanDownload + ' *** ' + Native.instance.deviceType);
                if (this.isCanDownload || Native.instance.deviceType == DeviceType.Android) {
                    Laya.LocalStorage.setItem("relativePath", "ludoCacheFile");
                } else {
                    Laya.LocalStorage.setItem("relativePath", "ludoCache11");
                }
                if (yalla.Global.ProfileInfo.isDebug)
                    this.readJson("res/json/skin.json");
                else
                    this.readJson("res/json/skinmaster.json");
                this.successCallBack && Laya.stage.timer.once(2000, this, () => {
                    if (this.successCallBack) {
                        yalla.Debug.log("超时");
                        this.successCallBack();
                        this.successCallBack = null;
                    }
                })
            }
            yalla.Debug.log("Skin init version:" + this.version);

            this.detection();
        }

        updateSkinDataRootUrl() {
            if (this.skinData && this.skinData.rootUrl) {
                this.skinData.rootUrl = yalla.DomainUtil.instance.getFileDomainType_flexible(this.skinData.rootUrl);
            }
        }

        detection() {
            this.getVersion(Laya.Handler.create(this, (data) => {
                if (parseInt(data.version) == this.version) {
                    var fn = "skinmaster"
                    if (yalla.Global.ProfileInfo.isDebug) {
                        fn = "skin";
                    }
                    var path = `${yalla.File.folder}skinCache/${fn + this.version}.json`.replace(new RegExp('//', 'g'), "/");
                    yalla.Debug.log(yalla.File.existed(path) + '------------Yalla.Skin.detection path-' + path);
                    if (yalla.File.existed(path)) {
                        yalla.Debug.log("readJson");
                        this.readJson(`file://${path}`);
                    } else {
                        yalla.Debug.log("downJson1--data.url" + data.url);
                        this.downJson(data.url);
                    }
                } else {
                    yalla.Debug.log("downJson2--data.url-" + data.url);
                    this.downJson(data.url);
                }
            }))
        }
        /**
         * 获取皮肤文件信息（版本号）
         * @param cb 
         */
        getVersion(cb: Laya.Handler = null) {
            var defaultVersion = 203;
            var game_activityUrl = yalla.DomainUtil.instance.game_activity_url;//'http://172.20.43.114:8901'; //
            var fileUrl = `${yalla.DomainUtil.instance.game_common_url}/H5/Skin/json/skinmaster${defaultVersion}.json`;

            var url = `${game_activityUrl}/YallaGame/Skin/SkinVersionInfo`;
            var self = this;
            yalla.util.ajaxget({
                url: url,
                success: (res) => {
                    yalla.Debug.log("获取版本号接口返回成功");
                    // yalla.Debug.log(res);
                    if (typeof res === "string") {
                        try {
                            res = JSON.parse(res);
                        } catch (error) {
                            res = null;
                        }
                    }
                    if (res && res.data && res.resultCode === 200) {
                        if (res.data.url) res.data.url = yalla.DomainUtil.instance.getFileDomainType_flexible(res.data.url);
                        cb && cb.runWith(res.data);
                    } else {
                        cb && cb.runWith({ "version": defaultVersion, "url": fileUrl });
                    }
                },
                error: function (e) {
                    var status = e.status; //服务器状态错误码 >=500 (后续讨论，统一只要失败就还)  
                    yalla.Debug.log("获取版本号接口返回失败 status：" + status);
                    // if (status >= 500) {
                    var nextUrl = yalla.DomainUtil.instance.getNextUrlByType(DomainType.GAME_ACTIVITY);
                    if (nextUrl) {
                        yalla.Debug.log(url + "：前url===备用域名 皮肤版本文件 切换url==" + nextUrl);
                        self.getVersion(cb);
                    } else {
                        cb && cb.runWith({ "version": defaultVersion, "url": fileUrl });
                    }
                    // } else {
                    //     cb && cb.runWith({ "version": "183", "url": fileUrl });
                    // }
                }
            })
        }

        downJson(url: string) {//下载配置文件
            yalla.Debug.log("downloadjson start " + new Date().getTime() + " url:" + url);
            var fN = (url as string).slice(url.lastIndexOf("/") + 1);
            // console.log("=====下载配置文件json=====fn="+fN);
            yalla.event.YallaEvent.instance.once(fN, this, (data) => {
                yalla.Debug.log("Skin    downloadjson end " + new Date().getTime());
                if (data && data.success) {
                    yalla.Debug.log("Skin    download json success!");
                    var localPath = "file://" + (yalla.File.folder + "/skinCache/" + fN).replace(new RegExp('//', 'g'), "/");
                    this.readJson(localPath);
                } else {
                    yalla.Debug.log("download json failed!");
                }
            })
            yalla.Native.instance.downloadFileByFileName("skinCache", url);
        }
        readJson(localPath: string) {//读取json配置
            yalla.Debug.log("readJson localPath:" + localPath);
            Laya.loader.load(localPath, Laya.Handler.create(this, (e) => {
                if (!!e) {
                    this.skinData = e;
                    this.updateSkinDataRootUrl();

                    yalla.Debug.log(this.skinData.updateVersion + " =this.version: " + this.version);
                    yalla.Debug.log('****getGooglePlayInfo 11****' + yalla.Global.googleInfo.isCanDownload);
                    if (this.skinData.updateVersion >= this.version) {//需要创建新文件夹并重新下载
                        if (this.isCanDownload || Native.instance.deviceType == DeviceType.Android) {//TODO::这里还是用原目录，资源下载需要原生强制覆盖
                            Laya.LocalStorage.setItem("relativePath", "ludoCacheFile");
                        } else {
                            Laya.LocalStorage.setItem("relativePath", "ludoCache" + this.skinData.version);
                        }
                    }
                    Laya.LocalStorage.setItem("skinVersion", this.skinData.version);
                    yalla.File.init();
                    this.downLoad();
                } else {
                    yalla.Debug.log("加载本地皮肤json失败")
                }
                if (this.successCallBack) {
                    this.successCallBack();
                    this.successCallBack = null;
                }
            }))
        }
        static get instance() {
            if (!this._instance) this._instance = new yalla.Skin();
            return this._instance;
        }

        downLoad() {
            if (this.skinData.skins) {
                var fileNames = [];

                for (var key in this.skinData.skins) {
                    if (this.skinData.skins.hasOwnProperty(key)) {
                        var element = this.skinData.skins[key];
                        if (!this.isCanDownload) {//TODO::1.3.1 Android 目前音效需要上传七牛云，需要下载，其他都是googleplay下载后，直接使用
                            if (element.atlas)
                                fileNames.push(this.getSkinUrlByName(element.atlas));
                            if (element.sk)
                                fileNames.push(this.getSkinUrlByName(element.sk));
                            if (element.images)
                                element.images.forEach(name => {
                                    fileNames.push(this.getSkinUrlByName(name));
                                })
                        }
                        if (element.sounds)
                            element.sounds.forEach(n => {
                                fileNames.push(this.getSkinUrlByName(n + ".mp3"));
                            });
                    }
                }
                if (this.skinData.gameGifts) {
                    var gameGiftSkins = this.skinData.gameGifts.skins;
                    for (var k in gameGiftSkins) {
                        var itemData = gameGiftSkins[k];
                        // if (!this.isCanDownload) {//TODO::1.3.1 Android 目前音效需要上传七牛云，需要下载，其他都是googleplay下载后，直接使用
                        if (itemData.sk) {
                            fileNames.push(this.getSkinUrlByName(itemData.sk));
                        }
                        if (itemData.images)
                            itemData.images.forEach(name => {
                                fileNames.push(this.getSkinUrlByName(name));
                            })
                        if (itemData.sound)
                            fileNames.push(this.getSkinUrlByName(itemData.sound + ".mp3"));
                        // }
                    }
                }
                if (this.skinData.extaRes) {
                    this.skinData.extaRes.forEach(name => {
                        var path = this.getSkinUrlByName(name);
                        if (fileNames.indexOf(path) < 0) fileNames.push(path);
                    })
                }
                if (this.skinData.emoji) {
                    var skList = this.skinData.emoji.sk;
                    var imgList = this.skinData.emoji.images;
                    if (skList) {
                        for (var k in skList) {
                            fileNames.push(this.getSkinUrlByName(skList[k]));
                        }
                    }
                    if (imgList) {
                        for (var k in imgList) {
                            fileNames.push(this.getSkinUrlByName(imgList[k]));
                        }
                    }
                }
                yalla.Native.instance.downLayaBoxFile(yalla.File.relativePath, fileNames);
            }
        }

        getSkinItem(id: number) {
            if (this.skinData && this.skinData.skins && this.skinData.skins["skin_" + id])
                return this.skinData.skins["skin_" + id];
            return {};
        }
        isDynamic(id: number): boolean {
            return Boolean(this.getSkinItem(id).sk);
        }
        hasSound(id: number): boolean {
            return Boolean(this.getSkinItem(id).sounds);
        }
        hasFurniture(id: number): boolean {
            if (this.skinData && this.skinData.skins && this.skinData.skins["skin_" + id])
                return Boolean(this.skinData.skins["skin_" + id].furniture);
            return true;
        }
        hasBG(id: number): boolean {
            return Boolean(this.getSkinItem(id).bg);
        }
        hasFront(id: number): boolean {
            return Boolean(this.getSkinItem(id).front);
        }
        hasBack(id: number): boolean {
            return Boolean(this.getSkinItem(id).back);
        }
        getSkinUrlByName(name: string) {
            if (this.skinData && this.skinData.rootUrl) {
                return this.skinData.rootUrl + name;
            }
            return `${yalla.DomainUtil.instance.game_common_url}/Skin/H5/ludoCache11905/${name}`;
            // if(this.common_file) return this.common_file + "/Skin/H5/ludoCache11905/" + name;
            // return "https://file.yallaludo.com/Skin/H5/ludoCache11905/" + name;
        }

        get isCanDownload() {
            // var isAndroid = Native.instance.deviceType == DeviceType.Android;
            // if (isAndroid) {
            //     return yalla.Global.googleInfo && yalla.Global.googleInfo.isCanDownload;
            // }
            return true;
        }

        get gameCnfGifts() {
            if (this.skinData && this.skinData['gameGifts']) return this.skinData['gameGifts'];
            return null;
        }

        getGiftItem(id: number) {
            var gameGifts = this.gameCnfGifts;
            if (gameGifts) {
                return gameGifts.skins[String(id)];
            }
            return {};
        }
        hasGiftSound(id: number): boolean {
            var gameGifts = this.gameCnfGifts;
            if (gameGifts) {
                var giftItem = gameGifts.skins[String(id)]
                if (giftItem) return giftItem.sound;
            }
            return false;
        }
    }
}