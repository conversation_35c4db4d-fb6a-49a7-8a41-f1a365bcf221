module yalla {
    /**
     * 声音管理
     */
    export class Sound {
        static audios = {
            /**domino 背景音乐 1*/
            domino_bg: 'bgm_domino',
            /**jackaroo 背景音乐 */
            jackaroo_bg: 'bgm_jackaro',
            /**锦标赛 背景音乐 */
            champion_bg: 'champion'
        }

        // static soundType = ".ogg";
        static canPlayMusic: boolean = false;

        static init() {
            this.canPlayMusic = false;
            laya.media.SoundManager.autoReleaseSound = true;
            laya.media.SoundManager.autoStopMusic = true;//失去焦点是否停止音乐
            yalla.Native.instance.currentGameSoundState((data) => {
                this.sound = !data.state;
            })
            yalla.Native.instance.currentBackgroundMusicState((data) => {
                this.music = !data.state;
            })
            yalla.Native.instance.systemVolume((data) => {
                this.soundVolume = data.volume;
            })
        }

        static set sound(boo: boolean) {
            yalla.Native.instance.changeCurrentGameSoundState({
                "state": !boo
            })
            laya.media.SoundManager.soundMuted = boo;
        }
        /**
         * true 静音
         */
        static get soundVolume(): number {
            return laya.media.SoundManager.soundVolume;
        }

        static set soundVolume(volume: number) {
            laya.media.SoundManager.soundVolume = volume;
        }
        /**
         * true 静音
         */
        static get sound(): boolean {
            return laya.media.SoundManager.soundMuted;
        }

        static set music(boo: boolean) {
            yalla.Native.instance.changeCurrentBackgroundMusicState({
                "state": !boo
            })
            laya.media.SoundManager.musicMuted = boo;
        }
        static get music(): boolean {
            return laya.media.SoundManager.musicMuted;
        }
        /**
         * 播放背景音乐 0是重新开始播放 1播放 2暂停   onBlur、停止音乐传2
         * @param res 资源名
         */
        static playMusic(name: string, state: number, cb: Laya.Handler = null) {
            if (this.music) {
                cb && cb.run();
                return;
            }
            if ((state == 0 || state == 1) && !yalla.Global.isFouce) return;
            yalla.Native.instance.playMusic({ name: name, state: state, loop: -1 }, () => {
                cb && cb.run();
            });
        }

        /**
         * 播放音效
         * @param name 资源名
         */
        static playSound(name: string, cb: Laya.Handler = null) {
            if (this.sound || !yalla.Global.isFouce) {
                cb && cb.run();
                return;
            }
            yalla.Native.instance.playAudio(name, () => {
                cb && cb.run();
            });
        }
        static playSkinSound(name, loops: number = 1, cb: Laya.Handler = null) {
            if (this.sound || !yalla.Global.isFouce) {
                cb && cb.run();
                return;
            }
            var par = { "name": name, "file": yalla.File.relativePath }
            yalla.Native.instance.playSkinSound(JSON.stringify(par), () => {
                cb && cb.run();
            });
        }

        /**
         * ios 需要特殊处理，播放音效也要走Music的逻辑
         * @param name 
         * @param loops 
         * @param cb 
         */
        static playChampionSound(name: string, loops: number = 1, cb: Laya.Handler = null) {
            if (this.sound || !yalla.Global.isFouce) {
                cb && cb.run();
                return;
            }
            if (yalla.Native.instance.deviceType == DeviceType.IOS) {
                yalla.Native.instance.playMusic({ name: name, state: 0, loop: 0 }, () => {
                    cb && cb.run();
                });
            } else {
                yalla.Native.instance.playAudio(name, () => {
                    cb && cb.run();
                });
            }
        }

        /**
         * 停止所有音效音频
         * ios 需要把所有音效也停止
         */
        static stopAllSound(): void {
            yalla.Native.instance.stopAllAudio();
        }

        /**
         * 停止背景音乐
         * @param name 
         */
        static stopMusic(name: string): void {
            yalla.Native.instance.playMusic({ name: name, state: 2, loop: -1 });
        }

        /**
         * 播放震动
         * @param name 资源名
         */
        static playPhoneVibrate(cb: Laya.Handler = null) {
            if (this.sound || !yalla.Global.isFouce) {
                cb && cb.run();
                return;
            }
            yalla.Native.instance.phoneVibrate();
        }
        /**
         * 停止所有音效
         * 停止所有背景音乐
         */
        static stopAll(): void {
            this.stopAllSound();
            this.playMusic(this.audios.champion_bg, 2);
            this.playMusic(this.audios.domino_bg, 2);
            this.playMusic(this.audios.jackaroo_bg, 2);
            this.canPlayMusic = false;
        }
    }
}