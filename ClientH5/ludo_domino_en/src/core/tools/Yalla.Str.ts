module yalla {
    export class Str {
        static _rectifyReg: RegExp = null;
        // static rectifyWord = { "؀": 6, "؁": 10, "؂": 4, "؃": 8, "؆": 4, "؇": 4, "؈": 7, "؉": 4, "؊": 5, "؋": 5, "،": 3, "؍": 2, "؎": 8, "؏": 4, "ؐ": 0, "ؑ": 0, "ؒ": 0, "ؓ": 0, "ؔ": 0, "ؕ": 0, "ؖ": 0, "ؗ": 0, "ؘ": 0, "ؙ": 0, "ؚ": 0, "؛": 3, "؞": 2, "؟": 3, "ء": 3, "آ": 2, "أ": 2, "ؤ": 3, "إ": 2, "ئ": 6, "ا": 2, "ب": 5, "ة": 3, "ت": 5, "ث": 5, "ج": 4, "ح": 4, "خ": 4, "د": 3, "ذ": 3, "ر": 3, "ز": 3, "س": 8, "ش": 8, "ص": 10, "ض": 10, "ط": 5, "ظ": 5, "ع": 4, "غ": 4, "ػ": 6, "ؼ": 6, "ؽ": 6, "ؾ": 6, "ؿ": 6, "ـ": 2, "ف": 6, "ق": 6, "ك": 5, "ل": 4, "م": 4, "ن": 4, "ه": 3, "و": 3, "ى": 6, "ي": 6, "ً": 0, "ٌ": 0, "ٍ": 0, "َ": 0, "ُ": 0, "ِ": 0, "ّ": 0, "ْ": 0, "ٓ": 0, "ٔ": 0, "ٕ": 0, "ٖ": 0, "ٗ": 0, "٘": 0, "ٙ": 0, "ٚ": 0, "ٛ": 0, "ٜ": 0, "ٝ": 0, "ٞ": 0, "٠": 4, "١": 4, "٢": 4, "٣": 4, "٤": 4, "٥": 4, "٦": 4, "٧": 4, "٨": 4, "٩": 4, "٪": 4, "٫": 3, "٬": 3, "٭": 4, "ٮ": 5, "ٯ": 6, "ٰ": 0, "ٱ": 2, "ٲ": 2, "ٳ": 2, "ٴ": 0, "ٵ": 2, "ٶ": 3, "ٷ": 3, "ٸ": 6, "ٹ": 5, "ٺ": 5, "ٻ": 5, "ټ": 5, "ٽ": 5, "پ": 5, "ٿ": 5, "ڀ": 5, "ځ": 4, "ڂ": 4, "ڃ": 4, "ڄ": 4, "څ": 4, "چ": 4, "ڇ": 4, "ڈ": 3, "ډ": 3, "ڊ": 3, "ڋ": 3, "ڌ": 3, "ڍ": 3, "ڎ": 3, "ڏ": 3, "ڐ": 3, "ڑ": 3, "ڒ": 3, "ړ": 3, "ڔ": 3, "ڕ": 3, "ږ": 3, "ڗ": 3, "ژ": 3, "ڙ": 3, "ښ": 8, "ڛ": 8, "ڜ": 8, "ڝ": 7, "ڞ": 7, "ڟ": 5, "ڠ": 4, "ڡ": 6, "ڢ": 6, "ڣ": 6, "ڤ": 6, "ڥ": 6, "ڦ": 6, "ڧ": 6, "ڨ": 6, "ک": 6, "ڪ": 8, "ګ": 6, "ڬ": 5, "ڭ": 5, "ڮ": 5, "گ": 6, "ڰ": 6, "ڱ": 6, "ڲ": 6, "ڳ": 6, "ڴ": 6, "ڵ": 4, "ڶ": 4, "ڷ": 4, "ڸ": 4, "ڹ": 4, "ں": 4, "ڻ": 4, "ڼ": 4, "ڽ": 4, "ھ": 5, "ڿ": 4, "ۀ": 3, "ہ": 3, "ۂ": 3, "ۃ": 3, "ۄ": 3, "ۅ": 3, "ۆ": 3, "ۇ": 3, "ۈ": 3, "ۉ": 3, "ۊ": 3, "ۋ": 3, "ی": 6, "ۍ": 6, "ێ": 6, "ۏ": 3, "ې": 6, "ۑ": 6, "ے": 6, "ۓ": 6, "۔": 2, "ە": 3, "ۖ": 0, "ۗ": 0, "ۘ": 0, "ۙ": 0, "ۚ": 0, "ۛ": 0, "ۜ": 0, "۝": 7, "۞": 7, "۟": 0, "۠": 0, "ۡ": 0, "ۢ": 0, "ۣ": 0, "ۤ": 0, "ۥ": 0, "ۦ": 0, "ۧ": 0, "ۨ": 0, "۩": 4, "۪": 0, "۫": 0, "۬": 0, "ۭ": 0, "ۮ": 3, "ۯ": 3, "۰": 4, "۱": 4, "۲": 4, "۳": 4, "۴": 4, "۵": 4, "۶": 4, "۷": 4, "۸": 4, "۹": 4, "ۺ": 8, "ۻ": 7, "ۼ": 4, "۽": 3, "۾": 4, "ۿ": 5, "ݐ": 10, "ݑ": 10, "ݒ": 10, "ݓ": 10, "ݔ": 10, "ݕ": 10, "ݖ": 10, "ݗ": 8, "ݘ": 8, "ݙ": 6, "ݚ": 6, "ݛ": 5, "ݜ": 15, "ݝ": 8, "ݞ": 8, "ݟ": 8, "ݠ": 11, "ݡ": 11, "ݢ": 12, "ݣ": 12, "ݤ": 12, "ݥ": 8, "ݦ": 8, "ݧ": 8, "ݨ": 8, "ݩ": 8, "ݪ": 8, "ݫ": 5, "ݬ": 5, "ݭ": 15, "ݮ": 8, "ݯ": 8, "ݰ": 15, "ݱ": 5, "ݲ": 8, "ݳ": 3, "ݴ": 3, "ݵ": 11, "ݶ": 11, "ݷ": 11, "ݸ": 6, "ݹ": 6, "ݺ": 11, "ݻ": 11, "ݼ": 8, "ݽ": 15, "ݾ": 15, "ݿ": 9, "ﭐ": 3, "ﭑ": 4, "ﭒ": 10, "ﭓ": 12, "ﭔ": 4, "ﭕ": 5, "ﭖ": 10, "ﭗ": 12, "ﭘ": 4, "ﭙ": 5, "ﭚ": 10, "ﭛ": 12, "ﭜ": 4, "ﭝ": 5, "ﭞ": 10, "ﭟ": 12, "ﭠ": 4, "ﭡ": 5, "ﭢ": 10, "ﭣ": 12, "ﭤ": 4, "ﭥ": 5, "ﭦ": 10, "ﭧ": 12, "ﭨ": 4, "ﭩ": 5, "ﭪ": 11, "ﭫ": 11, "ﭬ": 5, "ﭭ": 7, "ﭮ": 11, "ﭯ": 11, "ﭰ": 5, "ﭱ": 7, "ﭲ": 8, "ﭳ": 9, "ﭴ": 8, "ﭵ": 9, "ﭶ": 8, "ﭷ": 9, "ﭸ": 8, "ﭹ": 9, "ﭺ": 8, "ﭻ": 9, "ﭼ": 8, "ﭽ": 9, "ﭾ": 8, "ﭿ": 9, "ﮀ": 8, "ﮁ": 9, "ﮂ": 6, "ﮃ": 7, "ﮄ": 6, "ﮅ": 7, "ﮆ": 6, "ﮇ": 7, "ﮈ": 6, "ﮉ": 7, "ﮊ": 5, "ﮋ": 6, "ﮌ": 5, "ﮍ": 6, "ﮎ": 12, "ﮏ": 12, "ﮐ": 6, "ﮑ": 7, "ﮒ": 12, "ﮓ": 12, "ﮔ": 6, "ﮕ": 7, "ﮖ": 12, "ﮗ": 12, "ﮘ": 6, "ﮙ": 7, "ﮚ": 12, "ﮛ": 12, "ﮜ": 6, "ﮝ": 7, "ﮞ": 8, "ﮟ": 10, "ﮠ": 8, "ﮡ": 10, "ﮢ": 4, "ﮣ": 5, "ﮤ": 6, "ﮥ": 7, "ﮦ": 6, "ﮧ": 7, "ﮨ": 4, "ﮩ": 6, "ﮪ": 9, "ﮫ": 7, "ﮬ": 9, "ﮭ": 7, "ﮮ": 11, "ﮯ": 11, "ﮰ": 11, "ﮱ": 11, "ﯓ": 9, "ﯔ": 10, "ﯕ": 6, "ﯖ": 7, "ﯗ": 6, "ﯘ": 6, "ﯙ": 6, "ﯚ": 6, "ﯛ": 6, "ﯜ": 6, "ﯝ": 6, "ﯞ": 6, "ﯟ": 6, "ﯠ": 6, "ﯡ": 6, "ﯢ": 6, "ﯣ": 6, "ﯤ": 11, "ﯥ": 12, "ﯦ": 4, "ﯧ": 4, "ﯨ": 4, "ﯩ": 5, "ﯪ": 7, "ﯫ": 8, "ﯬ": 10, "ﯭ": 11, "ﯮ": 9, "ﯯ": 10, "ﯰ": 9, "ﯱ": 10, "ﯲ": 9, "ﯳ": 10, "ﯴ": 9, "ﯵ": 10, "ﯶ": 16, "ﯷ": 16, "ﯸ": 8, "ﯹ": 16, "ﯺ": 16, "ﯻ": 8, "ﯼ": 11, "ﯽ": 12, "ﯾ": 4, "ﯿ": 5, "ﹰ": 2, "ﹱ": 2, "ﹲ": 2, "ﹳ": 3, "ﹴ": 2, "ﹶ": 2, "ﹷ": 2, "ﹸ": 2, "ﹹ": 2, "ﹺ": 2, "ﹻ": 2, "ﹼ": 2, "ﹽ": 2, "ﹾ": 2, "ﹿ": 2, "ﺀ": 3, "ﺁ": 2, "ﺂ": 2, "ﺃ": 2, "ﺄ": 2, "ﺅ": 3, "ﺆ": 3, "ﺇ": 2, "ﺈ": 2, "ﺉ": 6, "ﺊ": 6, "ﺋ": 2, "ﺌ": 3, "ﺍ": 2, "ﺎ": 2, "ﺏ": 5, "ﺐ": 6, "ﺑ": 2, "ﺒ": 3, "ﺓ": 3, "ﺔ": 4, "ﺕ": 5, "ﺖ": 6, "ﺗ": 2, "ﺘ": 3, "ﺙ": 5, "ﺚ": 6, "ﺛ": 2, "ﺜ": 3, "ﺝ": 4, "ﺞ": 5, "ﺟ": 4, "ﺠ": 5, "ﺡ": 4, "ﺢ": 5, "ﺣ": 4, "ﺤ": 5, "ﺥ": 4, "ﺦ": 5, "ﺧ": 4, "ﺨ": 5, "ﺩ": 3, "ﺪ": 4, "ﺫ": 3, "ﺬ": 4, "ﺭ": 3, "ﺮ": 3, "ﺯ": 3, "ﺰ": 3, "ﺱ": 8, "ﺲ": 8, "ﺳ": 6, "ﺴ": 7, "ﺵ": 8, "ﺶ": 8, "ﺷ": 6, "ﺸ": 7, "ﺹ": 7, "ﺺ": 7, "ﺻ": 5, "ﺼ": 5, "ﺽ": 7, "ﺾ": 7, "ﺿ": 5, "ﻀ": 5, "ﻁ": 5, "ﻂ": 5, "ﻃ": 5, "ﻄ": 5, "ﻅ": 5, "ﻆ": 5, "ﻇ": 5, "ﻈ": 5, "ﻉ": 4, "ﻊ": 4, "ﻋ": 4, "ﻌ": 3, "ﻍ": 4, "ﻎ": 4, "ﻏ": 4, "ﻐ": 3, "ﻑ": 6, "ﻒ": 6, "ﻓ": 3, "ﻔ": 4, "ﻕ": 6, "ﻖ": 6, "ﻗ": 3, "ﻘ": 4, "ﻙ": 5, "ﻚ": 5, "ﻛ": 3, "ﻜ": 4, "ﻝ": 4, "ﻞ": 5, "ﻟ": 2, "ﻠ": 3, "ﻡ": 4, "ﻢ": 5, "ﻣ": 4, "ﻤ": 4, "ﻥ": 4, "ﻦ": 5, "ﻧ": 2, "ﻨ": 3, "ﻩ": 3, "ﻪ": 4, "ﻫ": 5, "ﻬ": 4, "ﻭ": 3, "ﻮ": 3, "ﻯ": 6, "ﻰ": 6, "ﻱ": 6, "ﻲ": 6, "ﻳ": 2, "ﻴ": 3, "ﻵ": 4, "ﻶ": 5, "ﻷ": 4, "ﻸ": 5, "ﻹ": 4, "ﻺ": 5, "ﻻ": 4, "ﻼ": 5, "ﱠ": 0, "ﱡ": 0, "ﱢ": 0, "ﱣ": 0, "ﱞ": 0, "ﱟ": 0, "ﳲ": 4, "ﳳ": 4, "ﳴ": 4, "ﴼ": 4, "ﴽ": 3, "﴾": 7, "﴿": 7, "ﷲ": 15, "ﷳ": 17, "ﷴ": 20, "ﷺ": 15, "ﷻ": 12, "﷼": 17, "﷽": 82 };
        
        static rectifyWord1 = { "j": 1, "i": 1, "ض": 5, "ص": 5, "ش": 2, "س": 2, "ك": 5, "ة": 3 };
        static rectifyWord = { "j": 1, "i": 1, "ض": 5, "ص": 5, "ش": 2, "س": 2, "ك": 4, "ة": 3, "ر": 2, "ح": 5,"ف":2,"ا":1,"ی":2,"ﺎ":1,"ﻴ":2,"ـ":2};
        // ﻟ   ﺎ   ی   ﻟ
        //ل ي ا ل
        static get rectifyReg(): RegExp {
            if (!this._rectifyReg) {
                var re = '';
                for (var key in this.rectifyWord) {
                    re += key + "|";
                }
                re = re.slice(0, re.length - 1);
                this._rectifyReg = new RegExp(re, "ig");
            }
            return this._rectifyReg;
        }
        static init() {
            String.prototype.getByteLen = function () {//按字节计算长度
                return this.replace(/[^\x00-\xff]/g, "01").length;
            }
            String.prototype.sliceByByte = function (n) {//按照字节裁剪字符串
                if (this.getByteLen() <= n) { return this; }
                var m = Math.floor(n / 2);
                var strLen = this.length;
                for (var i = m; i < strLen; i++) {
                    var subStr = this.substr(0, i);
                    if (subStr.getByteLen() >= n) {
                        return subStr;
                    }
                }
                return this;
            }
            String.prototype.subYlEmoji = function (n: number) {//按字节截取游戏中自定义的表情
                // var pos = this.search(/\[(A.+?)\]/gm);
                var re = /\[(A.\d{2})\]/gm;
                var pos = this.search(re);
                if (pos < 0) {//没有自定义表情
                    return this.sliceByByte(n);
                } else {
                    var arr = this.replace(re, function (s) {
                        return `卩${s}卩`;
                    }).split("卩");
                    var result = "", l = 0;
                    for (let i = 0; i < arr.length; i++) {
                        var s = arr[i] || "";
                        var bytelen = s.getByteLen();
                        l += bytelen;
                        if (l > n) {
                            if (i == 0) return s.sliceByByte(n);
                            if (!re.test(s)) result += s.sliceByByte(l - n);
                            return result;
                        }
                        result += s;
                    }
                    return result;
                }
            }
            String.prototype.hasBlockWord = function () {
                return false;
            }
            String.prototype.matchRectifyWord = function (regModule: string = '') {
                return this.match(yalla.Str.rectifyReg) || [];
            }
            String.prototype.rectifyWidth = function () {
                var arr = this.matchRectifyWord();
                var result = 0;
                for (var i = 0; i < arr.length; i++) {
                    var s = arr[i];
                    var n = yalla.Str.rectifyWord[s] || 0;
                    result += n;
                }
                return result;
            }
            String.prototype.matchArabic = function (): Array<string> {
                return this.match(/[\u0600-\u06FF|\uFE70-\uFEFF]/gm) || [];
            }
            String.prototype.getArabicWidth = function (): number {
                var result = 0;
                for (var i = 0; i < this.length; i++) {
                    var s = this[i];
                    var n = yalla.Str.rectifyWord[s] || 0;
                    result += n;
                }
                return result;
            }
            String.prototype.howArabic = function (): number {
                var result, match, str = this;
                str = str.replace(/[\u0021-\u0040\s]/gm, '');
                match = str.match(/[\u0600-\u06FF|\uFE70-\uFEFF]/gm) || [];
                result = match.length / str.length;
                return result;//match.length;
            }
            String.prototype.howNotArabic = function (): number {
                var result, match, str = this;
                str = str.replace(/[\u0021-\u0040\s]/gm, '');
                match = str.match(/[^\u0621-\u0652]/gm) || [];
                result = match.length / str.length;
                return result;
            }
            String.prototype.isArabic = function (threshold: number): boolean {
                threshold = threshold || 0.79;
                return this.howArabic() >= threshold;
            }
            // String.prototype.hasArabic = function (): boolean {
            //     return /[\u0600-\u06FF\uFE70-\uFEFF\u0900-\u097F]/.test(this);
            // }
            // String.prototype.removeTashkel = function (): string {
            //     return this.replace(/[\u064B-\u0652]/gm, '');
            // }
            // String.prototype.removeNonArabic = function (): string {
            //     return this.replace(/[^\u0600-\u06FF|\u0750-\u077F|\u08a0-\u08FF]/gm, '');
            //     // return this.replace(/[^\u0621-\u0652]/gm, '');
            // }

            String.prototype.hasArabic = function (): boolean {
                return /[\u0600-\u06FF|\uFE70-\uFEFF]/.test(this);
            }
            String.prototype.removeTashkel = function (): string {
                return this.replace(/[\u064B-\u0652]/gm, '');
            }
            String.prototype.removeNonArabic = function (): string {
                return this.replace(/[^\u0600-\u06FF|\uFE70-\uFEFF|\u0900-\u097F]/gm, '');
                // return this.replace(/[^\u0621-\u0652]/gm, '');
            }

            String.prototype.removeArabic = function (): string {
                return this.replace(/[\u0621-\u0652]/gm, '');
            }
            String.prototype.hasHindi = function (): boolean {
                return /[\u0900-\u097F]/.test(this);
            }
            String.prototype.howHindi = function (): number {
                var result, match, str = this;
                str = str.replace(/[\u0021-\u0040\s]/gm, '');
                match = str.match(/[\u0900-\u097F]/gm) || [];
                result = match.length / str.length;
                return match.length;
            }
            String.prototype.removeNonHindi = function (): string {
                return this.replace(/[^\u0900-\u097F]/gm, '');
                // return this.replace(/[^\u0621-\u0652]/gm, '');
            }
            Laya.Text.prototype.lang = function (text, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10) {
                if (this.isLangPacks) {
                    text = Laya.Text.langPacks && Laya.Text.langPacks[text] ? Laya.Text.langPacks[text] : text;
                }
                this._originalWidth = text.rectifyWidth() * (this.fontSize / 12) || 0;
                //阿语文本翻转

                // var resList = text.split(' ');
                // var allTextList = [];
                // var tempText = '';
                // for (var ii = 0; ii < resList.length; ii++){
                //     if (resList[ii].hasArabic() || yalla.util.isSpecialSymbol(resList[ii])) {
                //         tempText += resList[ii] + ' ';
                //     } else {
                //         if(tempText.length > 0) allTextList.push(tempText);
                //         allTextList.push(resList[ii]);
                //         tempText = '';
                //     }
                // }
                // if (tempText.length > 0) allTextList.push(tempText);

                // console.log(resList);
                // console.log(allTextList);
                // console.log(allTextList.length+'-=-=-'+resList.length);

                // var allText = '';
                // for (var jj = 0; jj < allTextList.length; jj++){
                //     var ttt = allTextList[jj];
                //     allText += ArabicSupport.FixRL(ttt, true) + ' ';
                // }
                // text = allText;
                // console.log('==**==' + text);


                if (text.isArabic(0)) {
                    text = ArabicSupport.FixRL(text, true);
                } else {
                    if (text.hasArabic()) {
                        text = yalla.util.RerangeArabic(text);
                    }
                }


                if (arguments.length < 2) {
                    this._text = text;
                } else {
                    for (var i = 0, n = arguments.length; i < n; i++) {
                        text = text.replace("{" + i + "}", arguments[i + 1]);
                    }
                    this._text = text;
                }
            }

            Laya.getset(false, Laya.HTMLElement.prototype, 'text', function () {
                return this._text.text;
            }, function (value) {
                value = Laya.Text.langPacks && Laya.Text.langPacks[value] ? Laya.Text.langPacks[value] : value;
                this._htmlRectifyWid = value.rectifyWidth();
                this._originStr = value;
                this._htmloriginalWidth = this._htmlRectifyWid * (this.style.fontSize / 12) || 0;

                if (value.isArabic(0)) {
                    value = ArabicSupport.FixRL(value, true);
                } else {
                    if (value.hasArabic()) {
                        value = yalla.util.RerangeArabic(value);
                    }
                }

                // var resList = value.split(' ');
                // var allTextList = [];
                // var tempText = '';
                // for (var iii = 0; iii < resList.length; iii++){
                //     if (resList[iii].hasArabic() || yalla.util.isSpecialSymbol(resList[iii])) {
                //         tempText += resList[iii] + ' ';
                //     } else {
                //         if(tempText.length > 0) allTextList.push(tempText);
                //         allTextList.push(resList[iii]);
                //         tempText = '';
                //     }
                // }
                // if (tempText.length > 0) allTextList.push(tempText);

                // console.log(resList);
                // console.log(allTextList);
                // console.log(allTextList.length+'-=-=-'+resList.length);

                // var allText = '';
                // for (var jjj = 0; jjj < allTextList.length; jjj++){
                //     var ttt = allTextList[jjj];
                //     allText += ArabicSupport.FixRL(ttt, true) + ' ';
                // }
                // value = allText;
                // console.log('==**==' + this._text);


                if (this._text == Laya.HTMLElement['_EMPTYTEXT']) {
                    this._text = { text: value, words: null };
                }
                else {
                    this._text.text = value;
                    this._text.words && (this._text.words.length = 0);
                }
                Laya.Render.isConchApp && this.layaoutCallNative();
                this._renderType |=/*laya.renders.RenderSprite.CHILDS*/0x800;
                this.repaint();
                this.updateHref();
            });

            /**
            *rtl模式的getWords函數
            */
            Laya.HTMLElement.prototype['_getWords2'] = function () {
                if (!this._text)
                    return null;
                var txt = this._text.text;
                if (!txt || txt.length === 0)
                    return null;

                var i = 0, n = 0, maxLen = 200;
                var realWords;
                var drawWords;
                var originWords;
                if (!this._text.drawWords) {
                    var tempWords = this._originStr.split(' ');
                    realWords = txt.split(' ');
                    n = realWords.length - 1;
                    drawWords = [];
                    originWords = [];
                    for (i = 0; i < n; i++) {
                        drawWords.push(realWords[i], ' ');
                        originWords.push(tempWords[i], ' ');
                    }
                    if (n >= 0) {
                        drawWords.push(realWords[n]);
                        originWords.push(tempWords[n]);
                    }
                    this._text.drawWords = drawWords;
                } else {
                    drawWords = this._text.drawWords;
                    originWords = drawWords;
                };

                var words = this._text.words;
                if (words && words.length === txt.length)
                    return words;
                words === null && (this._text.words = words = []);

                var sizeWid = 0, len = 0;
                var size;
                var isChar = false;
                var style = this.style;
                var fontStr = style.font;
                var isArabic = false;

                var sizeWidHash = {};
                var indexHash = {};
                var wordHash = {};
                var startIndex = 0, endIndex = 0;
                for (i = 0, n = drawWords.length; i < n; i++) {
                    size = Laya.Utils.measureText(drawWords[i], fontStr);
                    // Debug.log(drawWords[i].matchRectifyWord().length+"====00===sizeWid:"+size.width+"==txt.charCodeAt(i):"+txt.charCodeAt(i));
                    if (size.width >= maxLen) {//文本过长，计算每个字节宽度
                        isChar = true;
                    }
                    if (i == 0) {
                        startIndex = 0;
                        endIndex = drawWords[i].length - 1;
                    } else {
                        startIndex += drawWords[i - 1].length;
                        endIndex += drawWords[i].length;
                    }
                    sizeWidHash[startIndex] = size.width;
                    indexHash[startIndex] = endIndex;
                    wordHash[startIndex] = drawWords[i];
                    // console.log(startIndex, endIndex, drawWords[i].length, size.width, '----------', drawWords[i]);
                }
                if (isChar) {
                    len = words.length = txt.length;
                    // Debug.log(ArabicFixerTool.ToCharArray(txt));//1600,1600,1600,1600,1600,1600,1600,1600,1600,1600,1600,1600,65247,65166,1740,65247
                    for (i = 0, n = len; i < n; i++) {
                        if (!!indexHash[i]) {
                            endIndex = indexHash[i];
                            startIndex = i;
                        }
                        if (i <= endIndex) {
                            if (sizeWidHash[startIndex] >= maxLen) {
                                var charStr = txt.charAt(i);
                                isArabic = charStr.hasArabic();
                                size = Laya.Utils.measureText(charStr, fontStr);
                                // if (charStr.matchRectifyWord().length) {
                                //     var originalWidth = charStr.rectifyWidth();
                                //     var ar = charStr.removeNonArabic();
                                //     sizeWid = originalWidth + Laya.Utils.measureText(charStr,fontStr).width - Laya.Utils.measureText(ar,fontStr).width / 2 + Laya.Utils.measureText(charStr.removeNonHindi(),fontStr).width * .2;
                                //     sizeWid *= 0.45;
                                // } else {
                                    sizeWid = isArabic ? size.width * 0.45 : size.width;
                                // }
                                // Debug.log(charStr);
                                // Debug.log(isArabic+":isArabic==101==sizeWid:"+sizeWid+"==:"+charStr.matchRectifyWord().length+"==:"+txt.charCodeAt(i)+"==originalWidth:"+originalWidth);
                                var tHTMLChar = words[i] = new Laya.HTMLChar(charStr, sizeWid, size.height || style.fontSize, style);
                                if (tHTMLChar.char.length > 1) {
                                    tHTMLChar.charNum = tHTMLChar.char;
                                }
                                if (this.href) {
                                    var tSprite = new Laya.Sprite();
                                    this.addChild(tSprite);
                                    tHTMLChar.setSprite(tSprite);
                                }
                            } else {
                                if (wordHash[i]) {
                                    var ar = wordHash[i].removeNonArabic();
                                    var wid1 = Laya.Utils.measureText(wordHash[i], fontStr).width;
                                    var wid2 = Laya.Utils.measureText(ar, fontStr).width * 0.5;
                                    var wid3 = Laya.Utils.measureText(wordHash[i].removeNonHindi(), fontStr).width * .2;
                                    var rvalue = 0.2;//this._htmlRectifyWid >= 5 ? 0.35 : 0.2;
                                    sizeWid = this._htmloriginalWidth * rvalue + wid1 - wid2 + wid3;
                                    // Debug.log(wordHash[i]);
                                    // Debug.log(wordHash[i].matchRectifyWord().length+"====222===sizeWid:"+size.width);
                                    var tHTMLChar = words[i] = new Laya.HTMLChar(wordHash[i], sizeWid, size.height || style.fontSize, style);
                                    if (tHTMLChar.char.length > 1) {
                                        tHTMLChar.charNum = tHTMLChar.char;
                                    }
                                    if (this.href) {
                                        var tSprite = new Laya.Sprite();
                                        this.addChild(tSprite);
                                        tHTMLChar.setSprite(tSprite);
                                    }
                                } else {
                                    words[i] = null;
                                }
                            }
                        }
                    }
                    return words;
                }
                if (words && words.length === drawWords.length)
                    return words;
                words.length = drawWords.length;
                for (i = 0, n = drawWords.length; i < n; i++) {
                    var str = drawWords[i];
                    if (str.isArabic(0) && originWords && originWords[n - i - 1]) str = originWords[n - i - 1];
                    var originalWidth = str.rectifyWidth();
                    // if (originalWidth >= 5) originalWidth = (originalWidth + 3) * this.style.fontSize / 12;
                    // else originalWidth = (originalWidth + 3) * this.style.fontSize / 12 * 0.2;

                    var ar = drawWords[i].removeNonArabic();
                    var wid1 = Laya.Utils.measureText(drawWords[i], fontStr).width;
                    var wid2 = Laya.Utils.measureText(ar, fontStr).width * 0.5;
                    var wid3 = Laya.Utils.measureText(drawWords[i].removeNonHindi(), fontStr).width * .2;
                    sizeWid = originalWidth + wid1 - wid2 + wid3;

                    var tHTMLChar = words[i] = new Laya.HTMLChar(drawWords[i], sizeWid, size.height || style.fontSize, style);
                    if (tHTMLChar.char.length > 1) {
                        tHTMLChar.charNum = tHTMLChar.char;
                    }
                    if (this.href) {
                        var tSprite = new Laya.Sprite();
                        this.addChild(tSprite);
                        tHTMLChar.setSprite(tSprite);
                    }
                }
                return words;
            }

            Laya.Layout.layout = function (element) {
                if (!element || !element._style) return null;
                if ((element._style._type & /*laya.display.css.CSSStyle.ADDLAYOUTED*/0x200) === 0)
                    return null;
                element.getStyle()._type &= ~ /*laya.display.css.CSSStyle.ADDLAYOUTED*/0x200;
                var arr;
                if (element._childs.length > 0) {
                    var hasArabic = false;
                    for (var i = 0; i < element._childs.length; i++) {
                        var text = element._childs[0]._text.text;
                        hasArabic = text && text.hasArabic();
                    }
                    if (hasArabic) {
                        arr = Laya.Layout._multiLineLayout_Arabic(element);
                    } else {
                        arr = Laya.Layout._multiLineLayout(element);
                    }
                } else {
                    arr = Laya.Layout._multiLineLayout(element);
                }
                if (Laya.Render.isConchApp && element["layaoutCallNative"]) {
                    // (element).layaoutCallNative();
                    element["layaoutCallNative"]();
                }
                return arr;
            }

            Laya.Layout._multiLineLayout_Arabic = function (element) {
                if (Laya.Text.RightToLeft) return Laya.Layout._multiLineLayout2(element);
                var elements = new Array;
                element._addChildsToLayout(elements);
                var i = 0, n = elements.length, j = 0;
                var style = element._getCSSStyle();
                var letterSpacing = style.letterSpacing;
                var leading = style.leading;
                var lineHeight = style.lineHeight;
                var widthAuto = style._widthAuto() || !style.wordWrap;
                var width = widthAuto ? 999999 : element.width;
                var height = element.height;
                var maxWidth = 0;
                var contentWidth = 0;
                var wordWidthArr = new Array;
                var exWidth = style.italic ? style.fontSize / 3 : 0;
                var align = style._getAlign();
                var valign = style._getValign();
                var endAdjust = valign !== 0 || align !== 0 || lineHeight != 0;
                var oneLayout;
                var x = 0;
                var y = 0;
                var w = 0;
                var h = 0;
                var tBottom = 0;
                var lines = new Array;
                var curStyle;
                var curPadding;
                var curLine = lines[0] = new Laya.LayoutLine();
                var newLine = false, nextNewline = false;
                var htmlWord;
                var sprite;
                curLine.h = 0;
                if (style.italic)
                    width -= style.fontSize / 3;
                var tWordWidth = 0;
                var tLineFirstKey = true;
                function addLine() {
                    curLine.y = y;
                    y += curLine.h + leading;
                    if (curLine.h == 0) y += lineHeight;
                    curLine.mWidth = tWordWidth;
                    tWordWidth = 0;
                    curLine = new Laya.LayoutLine();
                    lines.push(curLine);
                    curLine.h = 0;
                    x = width - 4;//0
                    tLineFirstKey = true;
                    newLine = false;
                    contentWidth = 0;
                    wordWidthArr = [];
                }
                x = width - 4;//0
                for (i = n - 1; i >= 0; i--) {
                    oneLayout = elements[i];
                    if (oneLayout == null) {
                        if (!tLineFirstKey) {
                            x -= Laya.Layout['DIV_ELEMENT_PADDING'];
                            contentWidth += Laya.Layout['DIV_ELEMENT_PADDING'];
                        }
                        curLine.wordStartIndex = curLine.elements.length;
                        continue;
                    }
                    tLineFirstKey = false;
                    if ((oneLayout instanceof laya.html.dom.HTMLBrElement)) {
                        addLine();
                        curLine.y = y;
                        continue;
                    } else if (oneLayout._isChar()) {
                        htmlWord = oneLayout;
                        if (!htmlWord.isWord) {
                            if (lines.length > 0 && (x - w) <= 0 && curLine.wordStartIndex > 0) {
                                var tLineWord = 0;
                                tLineWord = curLine.elements.length - curLine.wordStartIndex + 1;
                                curLine.elements.length = curLine.wordStartIndex;
                                i += tLineWord;
                                addLine();
                                continue;
                            }
                            newLine = false;
                            tWordWidth += htmlWord.width;
                        } else {
                            newLine = nextNewline || (htmlWord.char === '\n');
                            curLine.wordStartIndex = curLine.elements.length;
                        }
                        w = htmlWord.width + letterSpacing;
                        h = htmlWord.height;
                        nextNewline = false;
                        newLine = newLine || ((x - w) <= 0);
                        // newLine && console.log(i, htmlWord.charNum, w, '====333====', (x - w));
                        newLine && addLine();
                        curLine.minTextHeight = Math.min(curLine.minTextHeight, oneLayout.height);
                    } else {
                        curStyle = oneLayout._getCSSStyle();
                        sprite = oneLayout;
                        curPadding = curStyle.padding;
                        curStyle._getCssFloat() === 0 || (endAdjust = true);
                        newLine = nextNewline || curStyle.lineElement;
                        w = sprite.width * sprite._style._tf.scaleX + curPadding[1] + curPadding[3] + letterSpacing;
                        h = sprite.height * sprite._style._tf.scaleY + curPadding[0] + curPadding[2];
                        nextNewline = curStyle.lineElement;
                        newLine = newLine || ((x - w) <= 0 && curStyle.wordWrap);
                        newLine && addLine();
                    }
                    // console.log(i+'--'+oneLayout.charNum+'--'+ w+'--'+'----charNum---'+'--'+ (x-w)+'--'+ y);
                    curLine.elements.push(oneLayout);
                    curLine.h = Math.max(curLine.h, h);
                    oneLayout.x = x - w;
                    oneLayout.y = y;
                    x -= w;
                    curLine.w = x + letterSpacing;
                    curLine.y = y;
                    contentWidth += w;
                    wordWidthArr[i] = w;
                    maxWidth = Math.max(contentWidth + exWidth, maxWidth);
                    if (lines.length > 1) {
                        maxWidth = Math.max(width, maxWidth);
                    }
                }
                // console.log('-------lines.length-', lines.length);
                if (lines.length <= 1) {//TODO：未换行的话需要重新更新位置，因初始设置最大坐标是width
                    x = maxWidth;
                    for (i = n - 1; i >= 0; i--) {
                        oneLayout = elements[i];
                        if (oneLayout) {
                            x -= wordWidthArr[i];
                            oneLayout.x = x;
                        }
                    }
                }

                y = curLine.y + curLine.h;
                if (endAdjust) {
                    var tY = 0;
                    var tWidth = width;
                    if (widthAuto && element.width > 0) {
                        tWidth = element.width;
                    }
                    for (i = 0, n = lines.length; i < n; i++) {
                        lines[i].updatePos(0, tWidth, i, tY, align, valign, lineHeight);
                        tY += Math.max(lineHeight, lines[i].h + leading);
                    }
                    y = tY;
                }
                widthAuto && (element.width = maxWidth);
                (y > element.height) && (element.height = y);
                return [maxWidth, y];
            }


            /**
             * 获取文本宽度
             */
            Laya.Text.prototype['getTextWidth'] = function (text) {
                if (this._currBitmapFont) {
                    return this._currBitmapFont.getTextWidth(text);
                } else {
                    var ar = text.removeNonArabic();
                    if (!ar) return Laya.Browser.context.measureText(text).width;
                    return this._originalWidth + Laya.Browser.context.measureText(text).width - Laya.Browser.context.measureText(ar).width / 2 + Laya.Browser.context.measureText(text.removeNonHindi()).width * .2;
                    // return this._originalWidth + Laya.Browser.context.measureText(text).width;
                }
            }
            Laya.AnimationTemplet.prototype["getAniDuration"] = function (aniIndex) {
                if (!!this._anis[aniIndex])
                    return this._anis[aniIndex].playTime;
                else
                    if (this._anis[0]) return this._anis[0].playTime;
                return 1;
            }
            //阿语换行
            Laya.Text.prototype['parseLine_Arabic'] = function (line, wordWrapWidth) {
                var ctx = laya.utils.Browser.context;
                var lines = this._lines;
                var maybeIndex = 0;
                var execResult;
                var charsWidth = NaN;
                var wordWidth = NaN;
                var startIndex = line.length;
                charsWidth = this.getTextWidth(line);
                if (charsWidth <= wordWrapWidth) {
                    lines.push(line);
                    this._lineWidths.push(charsWidth);
                    return;
                }
                charsWidth = this._charSize.width;
                maybeIndex = Math.floor(wordWrapWidth / charsWidth);
                (maybeIndex == 0) && (maybeIndex = 1);
                charsWidth = this.getTextWidth(line.substring(startIndex - maybeIndex, startIndex));
                wordWidth = charsWidth;
                for (var j = startIndex - maybeIndex, m = 0; j > m; j--) {
                    charsWidth = this.getTextWidth(line.charAt(j));
                    wordWidth += charsWidth;
                    if (wordWidth > wordWrapWidth) {
                        if (this.wordWrap) {
                            var newLine = line.substring(j, startIndex);
                            if (line.hasArabic()) {
                                execResult = /\s+(\S*)/.exec(newLine);
                                if (execResult) {
                                    var d = newLine.length;
                                    // j=j + newLine.length-execResult.index;
                                    j = j + execResult.index;
                                    if (execResult.index == 0)
                                        j = startIndex - newLine.length;
                                    else
                                        newLine = line.substring(j, startIndex);
                                }
                            } else
                                if (newLine.charCodeAt(newLine.length - 1) < 255) {
                                    execResult = /\s+(\S*)$/.exec(newLine);
                                    if (execResult) {
                                        var d = newLine.length;
                                        j = j + execResult.index;
                                        if (execResult.index == 0)
                                            j = startIndex - newLine.length;
                                        else
                                            newLine = line.substring(j, startIndex);
                                    }
                                }
                            lines.push(newLine);
                            this._lineWidths.push(wordWidth - charsWidth);
                            startIndex = j;
                            if (j - maybeIndex > m) {
                                j -= maybeIndex;
                                charsWidth = this.getTextWidth(line.substring(j, startIndex));
                                wordWidth = charsWidth;
                                j++;
                            } else {
                                lines.push(line.substring(m, startIndex));
                                this._lineWidths.push(this.getTextWidth(lines[lines.length - 1]));
                                startIndex = -1;
                                break;
                            }
                        } else if (this.overflow == Laya.Text.HIDDEN) {
                            lines.push(line.substring(j, startIndex));
                            this._lineWidths.push(this.getTextWidth(lines[lines.length - 1]));
                            return;
                        }
                    }
                }
                if (this.wordWrap && startIndex != -1) {
                    lines.push(line.substring(m, startIndex));
                    this._lineWidths.push(this.getTextWidth(lines[lines.length - 1]));
                }
            }
            /**
            *@private
            *解析行文本。
            *@param line 某行的文本。
            *@param wordWrapWidth 文本的显示宽度。
            */
            Laya.Text.prototype['parseLine'] = function (line, wordWrapWidth) {
                var ctx = laya.utils.Browser.context;
                var lines = this._lines;
                var maybeIndex = 0;
                var execResult;
                var charsWidth = NaN;
                var wordWidth = NaN;
                var startIndex = 0;
                charsWidth = this.getTextWidth(line);
                if (charsWidth <= wordWrapWidth) {
                    lines.push(line);
                    this._lineWidths.push(charsWidth);
                    return;
                }
                charsWidth = this._charSize.width;
                maybeIndex = Math.floor(wordWrapWidth / charsWidth);
                (maybeIndex == 0) && (maybeIndex = 1);
                charsWidth = this.getTextWidth(line.substring(0, maybeIndex));
                wordWidth = charsWidth;
                for (var j = maybeIndex, m = line.length; j < m; j++) {
                    charsWidth = this.getTextWidth(line.charAt(j));
                    wordWidth += charsWidth;
                    if (wordWidth > wordWrapWidth) {
                        if (this.wordWrap) {
                            var newLine = line.substring(startIndex, j);
                            if (newLine.charCodeAt(newLine.length - 1) < 255) {
                                execResult = /(?:\w|-)+$/.exec(newLine);
                                if (execResult) {
                                    j = execResult.index + startIndex;
                                    if (execResult.index == 0)
                                        j += newLine.length;
                                    else
                                        newLine = line.substring(startIndex, j);
                                }
                            } else
                                if (Laya.Text.RightToLeft) {
                                    execResult = /([\u0600-\u06FF])+$/.exec(newLine);
                                    if (execResult) {
                                        j = execResult.index + startIndex;
                                        if (execResult.index == 0)
                                            j += newLine.length;
                                        else
                                            newLine = line.substring(startIndex, j);
                                    }
                                }
                            lines.push(newLine);
                            this._lineWidths.push(wordWidth - charsWidth);
                            startIndex = j;
                            if (j + maybeIndex < m) {
                                j += maybeIndex;
                                charsWidth = this.getTextWidth(line.substring(startIndex, j));
                                wordWidth = charsWidth;
                                j--;
                            } else {
                                lines.push(line.substring(startIndex, m));
                                this._lineWidths.push(this.getTextWidth(lines[lines.length - 1]));
                                startIndex = -1;
                                break;
                            }
                        } else if (this.overflow == Laya.Text.HIDDEN) {
                            lines.push(line.substring(0, j));
                            this._lineWidths.push(this.getTextWidth(lines[lines.length - 1]));
                            return;
                        }
                    }
                }
                if (this.wordWrap && startIndex != -1) {
                    lines.push(line.substring(startIndex, m));
                    this._lineWidths.push(this.getTextWidth(lines[lines.length - 1]));
                }
            }
            Laya.Text.prototype['parseLines'] = function (text) {
                var needWordWrapOrTruncate = this.wordWrap || this.overflow == Laya.Text.HIDDEN;
                if (needWordWrapOrTruncate) {
                    var wordWrapWidth = this.getWordWrapWidth();
                }
                if (this._currBitmapFont) {
                    this._charSize.width = this._currBitmapFont.getMaxWidth();
                    this._charSize.height = this._currBitmapFont.getMaxHeight();
                } else {
                    var measureResult = laya.utils.Browser.context.measureText(Laya.Text._testWord);
                    this._charSize.width = measureResult.width;
                    this._charSize.height = (measureResult.height || this.fontSize);
                };
                var lines = text.replace(/\r\n/g, "\n").split("\n");
                for (var i = 0, n = lines.length; i < n; i++) {
                    var line = lines[i];
                    if (needWordWrapOrTruncate)
                        if (text.hasArabic()) {
                            this.parseLine_Arabic(line, wordWrapWidth)
                        } else {
                            this.parseLine(line, wordWrapWidth);
                        }
                    else {
                        this._lineWidths.push(this.getTextWidth(line));
                        this._lines.push(line);
                    }
                }
            }

            Laya.getset(false, Laya.Sprite.prototype, 'scaleX', function () {
                if (this._style) return this._style._tf.scaleX;
                return 1;
            }, function (value) {
                var style = this.getStyle();
                if (style && style._tf.scaleX !== value) {
                    style.setScaleX(value);
                    this._tfChanged = true;
                    this.conchModel && this.conchModel.scale(value, style._tf.scaleY);
                    this._renderType |=/*laya.renders.RenderSprite.TRANSFORM*/0x04;
                    var p = this._parent;
                    if (p && p._repaint === 0) {
                        p._repaint = 1;
                        p.parentRepaint();
                    }
                }
            });

            /**Y轴缩放值，默认值为1。设置为负数，可以实现垂直反转效果，比如scaleX=-1。*/
            Laya.getset(false, laya.display.Sprite.prototype, 'scaleY', function () {
                if (this._style) return this._style._tf.scaleY;
                return 1;
            }, function (value) {
                var style = this.getStyle();
                if (style && style._tf.scaleY !== value) {
                    style.setScaleY(value);
                    this._tfChanged = true;
                    this.conchModel && this.conchModel.scale(style._tf.scaleX, value);
                    this._renderType |=/*laya.renders.RenderSprite.TRANSFORM*/0x04;
                    var p = this._parent;
                    if (p && p._repaint === 0) {
                        p._repaint = 1;
                        p.parentRepaint();
                    }
                }
            });

            /**透明度，值为0-1，默认值为1，表示不透明。更改alpha值会影响drawcall。*/
            Laya.getset(false, Laya.Sprite.prototype, 'alpha', function () {
                if (this._style) return this._style.alpha;
                return 1;
            }, function (value) {
                if (this._style && this._style.alpha !== value) {
                    value = value < 0 ? 0 : (value > 1 ? 1 : value);
                    this.getStyle().alpha = value;
                    this.conchModel && this.conchModel.alpha(value);
                    if (value !== 1) this._renderType |=/*laya.renders.RenderSprite.ALPHA*/0x02;
                    else this._renderType &= ~ /*laya.renders.RenderSprite.ALPHA*/0x02;
                    this.parentRepaint();
                }
            });

            /**表示是否可见，默认为true。如果设置不可见，节点将不被渲染。*/
            Laya.getset(false, Laya.Sprite.prototype, 'visible', function () {
                if (this._style) return this._style.visible;
                return true;
            }, function (value) {
                if (this._style && this._style.visible !== value) {
                    this.getStyle().visible = value;
                    this.conchModel && this.conchModel.visible(value);
                    this.parentRepaint();
                }
            });

            /**
            *当前文本内容字符串。
            *@see laya.display.Text.text
            */
            Laya.getset(false, Laya.Label.prototype, 'text', function () {
                if (this._tf) return this._tf.text;
                return '';
            }, function (value) {
                if (this._tf && this._tf.text != value) {
                    if (value)
                        value = Laya.UIUtils.adptString(value + "");
                    this._tf.text = value;
                    this.event(/*laya.events.Event.CHANGE*/"change");
                    if (!this._width || !this._height) this.onCompResize();
                }
            });
        }
        // }
    }
}