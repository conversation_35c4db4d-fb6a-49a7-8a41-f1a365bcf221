module yalla {

    export const getTimeHMS = (): string => {
        const date: Date = new Date();
        return `${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}:${date.getMilliseconds()}`;
    }

    export class System {
        public static systemTime: number;
        public static systemTimeTag: number;
        public static shareTime: number;        //诱导分享入口显示 时间

        public static clickTime: number = 0;        //点击时间

        static setSystemTime(val: any) {
            System.systemTime = val;
            System.systemTimeTag = Laya.Browser.now();
        }

        static setShareTime(val: any) {
            System.shareTime = val || 0;
        }

        static getServerTime(): number {
            const now: number = Laya.Browser.now();
            return Math.ceil(this.systemTime + (now - this.systemTimeTag) / 1000);
        }

        static getNowTime(): number {
            return new Date().getTime();
        }
        /**
         * 把当前时间毫秒转换成年月日时分秒 
         */
        static getNowTimeString(ms: number = System.getNowTime()): string {
            const date = new Date(ms);
            const year = date.getFullYear();
            const month = (date.getMonth() + 1 < 10) ? '0' + (date.getMonth() + 1) : '' + (date.getMonth() + 1);
            const day = (date.getDate() < 10) ? '0' + date.getDate() : '' + date.getDate();
            const hours = (date.getHours() < 10) ? '0' + date.getHours() : '' + date.getHours();
            const minutes = (date.getMinutes() < 10) ? '0' + date.getMinutes() : '' + date.getMinutes();
            const seconds = (date.getSeconds() < 10) ? '0' + date.getSeconds() : '' + date.getSeconds();
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }

        /**
         * 时间范围内是否连点
         * @param v 
         */
        static isRepeatClick(time: number = 3000): boolean {
            var v = false;
            var nowTime = this.getNowTime();
            if (nowTime - this.clickTime < time) v = true;
            else this.clickTime = nowTime;
            return v;
        }
    }
}