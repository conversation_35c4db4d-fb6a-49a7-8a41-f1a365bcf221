module yalla {
    /**
     * 
     * @param time 单位秒
     * @param type 枚举类型CountDownType
     * @param dibit 是否双位
     */
    export const getCountDown = (time: number, type: string = CountDownType.HMS, dibit = false): any[] => {
        let result: any[] = [];
        if (type.indexOf('s') !== -1) {
            result.push(time % 60);
        }

        if (type.indexOf('m') !== -1) {
            result.unshift(Math.floor((time % 3600) / 60));
        }

        if (type.indexOf('h') >= 0) {
            if (type == CountDownType.HMS) {
                result.unshift(Math.floor(time / 3600));
            }
            else {
                result.unshift(Math.floor((time % 86400) / 3600));
            }
        }

        if (type.indexOf('d') >= 0) {
            result.unshift(Math.floor(time / 86400));
        }

        if (dibit)
            result = result.map((ele) => {
                return ele < 10 ? `0${ele}` : ele;
            })
        return result;
    }

    export class CountDownType {
        static HMS = 'hms'
        static DHMS = 'dhms'
        static MS = 'ms';
    }
}