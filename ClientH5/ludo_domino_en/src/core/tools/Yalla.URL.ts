module yalla {

    /**
     * 获取js目录下的文件
     * @param name 文件路径，可以是任意层级
     */
    export const getJSURL = (name: string): string => getURL('js/' + name + '.js');
    /**
     * 获取proto 协议文件
     */
    export const getProto = (name: string): string => getResourceURL('proto/' + name + '.proto');
    export const getVersionProto = (name: string): string => {
        let path = getResourceURL('proto/' + name + '.proto');
        return Laya.ResourceVersion.manifest[path] || path;
    };
    /**
     * 获取.lang
     */
    export const getLang = (name: string): string => getResourceURL('lang/' + name + '.lang');
    /**
     * 获取动画 蛇棋sk
     */
    export const getSkeleton = (name: string): string => getResourceURL('sk/' + name + '.sk');
    export const getSkeletonPng = (name: string): string => getResourceURL('sk/' + name + '.png');
    /**
     * 获取动画 .ani
     */
    export const getAni = (name: string): string => getResourceURL('ani/' + name + '.ani');
    /**
     * 获取icon目录下的文件
     * @param path 文件路径，可以是任意层级
     */
    export const getImg = (name: string): string => getResourceURL('img/' + name + '.png');
    /**
     * 获取字体
     */
    export const getTTF = (name: string): string => getResourceURL('ttf/' + name + '.ttf');
    /**
     * 获取位图字体
     */
    export const getBmpFont = (name: string): string => getResourceURL('bmpfont/' + name);
    /**
     * 获取face目录下的文件
     * @param path 文件路径，可以是任意层级
     */
    export const getFace = (name: string): string => getResourceURL('img/face/' + name + '.png');

    /**
     * 获取atlas目录下的文件
     * @param path 文件路径，可以是任意层级
     */
    export const getUI = (path: string): string => getResourceURL('atlas/' + path);
    /**
     * 获取data目录下的文件
     * @param path 文件路径，可以是任意层级
     */
    export const getData = (path: string): string => getResourceURL('data/' + path);

    /**
     * 获取sound目录下的文件
     * @param path 文件路径，可以是任意层级
     */
    export const getSoundURL = (path: string, native: boolean = true): string => getResourceURL('sound/' + path, native);

    /**
     * 获取res目录下的文件
     * @param path 文件路径，可以是任意层级
     */
    export const getResourceURL = (subpath: string, native: boolean = true): string => {
        return getURL('res/' + subpath, native);
    }

    /**
     * 获取libs目录下的文件
     * @param path 文件路径，可以是任意层级
     */
    export const getLibsURL = (subpath: string): string => {
        return getURL('libs/' + subpath + '.js', true);
    }

    /**
     * 获取根目录路径
     * @param path 文件路径，可以是任意层级
     */
    export const getURL = (path: string, native: boolean = true): string => {
        if (yalla.Global.domain.length == 0) {
            return path;
        }
        return (yalla.Global.domain + "/" + path);
    }

    export const getGame = (name: any) => {
        return 'game/' + name + '.png';
    }

    export const getDomino = (name: any) => {
        return 'domino/' + name + '.png';
    }
    export const getSnake = (name: any) => {
        return 'snake/' + name + '.png';
    }
    export const getJackaro = (name: any) => {
        return 'jackaro/' + name + '.png';
    }

    export const getPublic = (name: any) => {
        return 'public/' + name + '.png';
    }

    export const getActivity = (name: any) => {
        return 'activity/' + name + '.png';
    }
    // export const getNetRes = (name: string) => {
    //     var dev = !yalla.Global.ProfileInfo.isDebug ? "" : "/dev";
    //     return `https://file.yalla.games/Skin/H5${dev}/${yalla.File.relativePath}/${name}`;
    // }
}