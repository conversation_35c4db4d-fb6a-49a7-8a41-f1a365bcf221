module yalla.util {

    export function backHall(isRun: boolean, playerNums: number = 0, delayTime = 0) {
        var account = yalla.Global.Account;
        var isBackToLeague = yalla.Native.instance.isBackToLeague;
        if ((account && account.leagueWatch) || isBackToLeague) {
            var toName = isBackToLeague ? 'league' : 'leagueWatchList';
            yalla.Native.instance.backHall(true, { to: toName, gameId: account.gameId, roomid: account.roomid }, delayTime);
            yalla.Native.instance.isBackToLeague = false;
        } else {
            var isPrivate = account.isPrivate;
            if (isPrivate == GameRoomType.CHAMPION) {
                yalla.Native.instance.event(yalla.Native.instance.Event.BACKCHAMPION, [{ winnerId: -1 }]);
            } else {
                //私人  组队(isPrivate未特殊区分)   vip
                var backName = yalla.backHall_direction[isPrivate];
                yalla.Debug.log('===backHall==backName:' + backName);
                if (isPrivate == GameRoomType.LEAGUE) {
                    yalla.Native.instance.backHall(yalla.Native.instance.isBack, { to: backName, gameId: account.gameId, num: playerNums, roomid: account.roomid }, delayTime);
                } else {
                    yalla.Debug.log(isRun + "===backHall===backName=" + backName + ' gameId:' + account.gameId);
                    if (isRun || !backName || account.needBackHall) yalla.Native.instance.backHall();
                    else {
                        if (yalla.Global.Account.gameId == GameType.SNAKEANDLADER) {
                            backName = 'snake';
                        }
                        yalla.Native.instance.backHall(yalla.Native.instance.isBack, { to: backName, gameId: account.gameId, num: playerNums, roomid: account.roomid }, delayTime);
                    }
                }
            }
        }
    }


    /**
     * 获取royalLevel，名字扫光不考虑rl是否隐藏，只要>=4都显示； 其他结合royalLevel & royVisibility
     * @param data 
     * @param isNeedVisible 
     */
    export function getRoyalLevel(data: playerShowInfo, isNeedVisible: boolean = true): number {
        if (data && data.fPlayerInfo) {
            if (data.fPlayerInfo.idx == yalla.Global.Account.idx) return data.realRoyLevel;
            if (!isNeedVisible) return data.realRoyLevel;
            if (!data.royVisibility) return data.realRoyLevel;
            // if (data.fPlayerInfo.idx == yalla.Global.Account.idx) return data.roylevel;
            // if (!isNeedVisible) return data.roylevel;
            // if (!data.royVisibility) return data.roylevel;
        }
        return 0;
    }
    /**
     * 获取royalLevel，名字扫光不考虑rl是否隐藏，只要>=4都显示； 其他结合royalLevel & royVisibility
     * @param data 
     * @param isNeedVisible 
     */
    export function getJackaroRoyalLevel(data: yalla.data.jackaro.PlayerShowInfoInterface, isNeedVisible: boolean = true): number {
        if (data && data.idx) {
            if (data.idx == yalla.Global.Account.idx) return data.royalLevel;
            if (!isNeedVisible) return data.royalLevel;
            if (!data.royalInvisible) return data.royalLevel;
            // if (data.fPlayerInfo.idx == yalla.Global.Account.idx) return data.roylevel;
            // if (!isNeedVisible) return data.roylevel;
            // if (!data.royVisibility) return data.roylevel;
        }
        return 0;
    }
    /**
     * 获取是否显示RL扫光(royalLevelNameAnimation字段由服务器转发)
     */
    export function canShowRLNameAnimation(data: playerShowInfo) {
        var royalLevel = yalla.util.getRoyalLevel(data, false);
        var royalLevelNameAnimation = true;
        if (data && data.fPlayerInfo.idx != yalla.Global.Account.idx) {
            royalLevelNameAnimation = data && data.royalLevelNameAnimation;
        }
        //4 是RL等级限制
        return royalLevel >= 4 && royalLevelNameAnimation;
    }
    /*
    * 获取是否显示RL扫光(royalLevelNameAnimation字段由服务器转发)
    */
    export function canShowJackaroRLNameAnimation(data: yalla.data.jackaro.PlayerShowInfoInterface) {
        var royalLevel = yalla.util.getJackaroRoyalLevel(data, false);
        var royalLevelNameAnimation = true;
        if (data && data.idx != yalla.Global.Account.idx) {
            royalLevelNameAnimation = data && data.royalLevelNameAnimation;
        }
        //4 是RL等级限制
        return royalLevel >= 4 && royalLevelNameAnimation;
    }
    /**
     * 1 有网
     * 0s
     */
    export function netState() {
        if (window['conch']) return window['conch'].config.getNetworkType();
        return 1;
    }
    /**
     * AJAX请求
     * @param d Param
     */
    export function ajax(d: Param, heads: any = null) {
        var data: Param = {
            progress: d.error || function (e) { },
            error: d.error || function (e) { },
            success: d.success || function (e) { },
            url: d.url || "",
            data: d.data || {},
            method: d.method || "post",
            responseType: d.responseType || "text"
        }
        var param: string = '';
        for (var key in data.data) {
            param += key + "=" + data.data[key] + "&";
        }
        param = param.slice(0, param.length - 1);
        var hr = new Laya.HttpRequest();
        hr.once(Laya.Event.PROGRESS, null, data.progress);
        hr.once(Laya.Event.COMPLETE, null, data.success);
        hr.once(Laya.Event.ERROR, null, (e) => {
            Debug.log("=ajax 请求失败==");
            data.error(hr.http);
        });
        hr.send(data.url, param, data.method, data.responseType, heads);
    }
    export function ajax2(d: Param) {
        var data: Param = {
            progress: d.error || function (e) { },
            error: d.error || function (e) { },
            success: d.success || function (e) { },
            url: d.url || "",
            data: d.data || {},
            method: d.method || "post",
            responseType: d.responseType || "text"
        }
        var param: string = JSON.stringify(data.data);
        // if (typeof data.data === 'object') {
        //     for (var c in data.data) {
        //         param += c + "=" + data.data[c] + "&";
        //     }
        //     param = param.substring(0, param.length - 1)
        // } else {
        //     param = data.data;
        // }
        var hr = new Laya.HttpRequest();
        hr.once(Laya.Event.PROGRESS, null, data.progress);
        hr.once(Laya.Event.COMPLETE, null, data.success);
        hr.once(Laya.Event.ERROR, null, data.error);
        hr.send(data.url, param, data.method, data.responseType, ["Content-Type", "application/json"]);
    }
    export function ajaxget(d: Param) {
        var data: Param = {
            progress: d.error || function (e) { },
            error: d.error || function (e) { console.log(e); },
            success: d.success || function (e) { },
            url: d.url || "",
            responseType: d.responseType || "text"
        }
        var hr = new Laya.HttpRequest();
        hr.once(Laya.Event.PROGRESS, null, data.progress);
        hr.once(Laya.Event.COMPLETE, null, data.success);
        hr.once(Laya.Event.ERROR, null, (e) => {
            Debug.log("=ajaxget 请求失败==");
            data.error(hr.http);
        });
        hr.send(data.url, "", "get", data.responseType, ["Content-Type", "application/json"]);
    }
    /**
     * 数字过滤器
     * @param num 
     */
    export function filterNum(num: number): string {
        if (!num) return "0";
        if (num < 10000) {
            return String(num || 0);
        } else if (num >= 10000 && num < 1000000) {
            return String(Math.floor(num / 100) / 10 || 0) + "K";
        } else if (num >= 1000000 && num < 1000000000) {
            return String(Math.floor(num / 100000) / 10 || 0) + "M";
        }
        return String(Math.floor(num / 100000000) / 10 || 0) + "B";
    }
    /**
     * 昵称截取
     * @param str 
     * @param len 长度默认13 
     */
    export function filterName(str: string, len: number = 13, specialStr: string = ''): string {
        if (!str) return '';
        var spStr = "MWmw@࿐ ★ ✨᭄ɪ";
        if (specialStr.length > 0) spStr = spStr.concat(specialStr);

        var bigCharLen = yalla.util.getCharCount(str, "[" + spStr + "]");
        if (bigCharLen > 5) {
            len -= Math.floor(bigCharLen / 2 - 2);
        }
        if (str.hasArabic()) len *= 2;
        str = str.replace(/[\r\n]/g, "");
        var r = /[^\x00-\xff]/g;
        if (str.replace(r, "mm").length <= len) {
            return str;
        }
        var m = Math.floor(len / 2);
        for (var i = m; i < str.length; i++) {
            var subStr = str.substr(0, i);
            if (subStr.replace(r, "mm").length >= len) {
                var code = str.charCodeAt(i - 1);
                if (code >= 0xD800 && code <= 0xDBFF) {
                    var char = String(str.charAt(i - 1) + str.charAt(i));
                    return str.substr(0, i - 1) + "...";
                }
                return subStr + "...";
            }
        }
        return str;
    }
    export function getCharCount(str: string, char: string) {
        try {
            var reg = new RegExp(char, "g");
            var result = str.match(reg);
            var count = !result ? 0 : result.length;
            return count;
        } catch (error) {
            return 0;
        }
    }
    /**
    * 字符串截取
    * @param str 
    * @param n 
    */
    export function filterStr(str: string, n: number): string {
        var r = /[^\x00-\xff]/g;
        if (str.replace(r, "mm").length <= n) { return str; }
        var m = Math.floor(n / 2);
        var strLen = str.length;
        for (var i = m; i < strLen; i++) {
            var subStr = str.substr(0, i);
            if (subStr.replace(r, "mm").length >= n) {
                return subStr;
            }
        }
        return str;
    }
    /**
     * 根据字节获取字符串长度
     * @param str 
     */
    export function getBLen(str) {
        if (str == null) return 0;
        if (typeof str != "string") {
            str += "";
        }
        return str.replace(/[^\x00-\xff]/g, "01").length;
    }
    /**
     * 数组去重
     * @param array 
     */
    export function uniq(array: Array<number>): Array<number> {
        var temp = [];
        var index = [];
        var l = array.length;
        for (var i = 0; i < l; i++) {
            for (var j = i + 1; j < l; j++) {
                if (array[i] === array[j]) {
                    i++;
                    j = i;
                }
            }
            temp.push(array[i]);
            index.push(i);
        }
        return temp;
    }
    export function angle(start, end): number {
        var diff_x = end.x - start.x,
            diff_y = end.y - start.y;
        var a = 360 * Math.atan(diff_y / diff_x) / (2 * Math.PI);
        if (diff_x < 0) {
            a -= 180;
        }
        return a;
    }
    export function sendExitType(mold: ExitType) {
        if (!yalla.Global.Account.idx) return;
        // var url = yalla.DomainUtil.instance.game_account_url + "/YallaGame/SundryInfo/ExitRecordAdd";
        var url = "/api/LudoBuriedDataRpcApiProxy/ExitRecordAdd";
        var data = {
            accountId: yalla.Global.Account.idx,
            gameId: yalla.Global.gameType,
            exitType: mold,
        }
        yalla.Native.instance.sendHttpRequest(url, data, (resultCode) => {
            //失败
            if (resultCode != 200) {
                var nextUrl = yalla.DomainUtil.instance.getNextUrlByType(DomainType.GAME_ACCOUNT);
                if (nextUrl) {
                    yalla.util.sendExitType(mold);
                }
            }
        })
    }
    export function parse(d: any): any {
        if (d && typeof d == 'string') return eval(`(` + d + `)`);
        return d;
    }
    // 过滤掉常见的不可见控制字符
    // 保留基本ASCII字符(32-126)和换行符(10, 13)
    export function parseJson(jsonString: string) {
        try {
            if (typeof jsonString !== 'string') return null;
            const cleanString = jsonString.replace(/[^\x20-\x7E\x0A\x0D]/g, '');
            if (!cleanString.trim()) return null;
            return JSON.parse(cleanString);
        } catch (error) {
            return null;
        }
    }
    /**
     * windows 截屏
     */
    export const IsWinConch = (): Boolean => {
        if (laya.utils.Browser.window.conch) return true;
        return false;
    }

    export const getDistance = (start: Laya.Point, end: Laya.Point): any => {
        var distance: number = 0;
        if (start) {
            distance = start.distance(end.x, end.y);
        }
        const lx = end.x - start.x;
        const ly = end.y - start.y;
        var angle = Math.atan2(ly, lx) * 180 / Math.PI;
        return { Distance: distance, Angle: angle };
    }

    export const getDistanceNum = (start: any, end: any): number => {
        var distance: number = 0;
        if (start) {
            // distance = start.distance(end.x, end.y);
            var xx = start.x - end.x;
            var yy = start.y - end.y;
            distance = Math.sqrt(xx * xx + yy * yy);
        }
        return distance;
    }

    /**
     * 取一条直线上的点
     * @param list 
     */
    export const getPointList = (start: Laya.Point, end: Laya.Point, stepLen: number): Array<any> => {
        var ladderLen: number = start.distance(end.x, end.y);
        // const lx = end.x - start.x;
        // const ly = end.y - start.y;
        // var ladderAngle = Math.atan2(ly, lx) * 180 / Math.PI;
        var len = Math.ceil(ladderLen / stepLen);
        var offsetX = (end.x - start.x) / len;
        var offsetY = (end.y - start.y) / len;
        var list = [];
        var pathTotal = new Laya.Point();
        for (var i = 1; i <= len; i++) {
            pathTotal.setTo(offsetX * i, offsetY * i);
            pathTotal.x = pathTotal.x + start.x;
            pathTotal.y = pathTotal.y + start.y;
            list.push([pathTotal.x, pathTotal.y]);
        }
        return list;
    }
    export function errMsg(eCode) {
        var msg = ErrorCode[eCode];
        if (!!msg)
            return msg;
        return "Login failed";
    }
    //判断两个对象内容是否相等
    export function isObjectValueEqual(a, b) {
        var aProps = Object.getOwnPropertyNames(a);
        var bProps = Object.getOwnPropertyNames(b);
        if (aProps.length != bProps.length) {
            return false;
        }
        for (var i = 0; i < aProps.length; i++) {
            var propName = aProps[i]

            var propA = a[propName]
            var propB = b[propName]
            if (propA !== propB) {
                if ((typeof (propA) === 'object')) {
                    if (this.isObjectValueEqual(propA, propB)) {
                        return true
                    } else {
                        return false
                    }
                } else {
                    return false
                }
            } else {
                return false
            }
        }
        return true;
    }
    export function isThreeSix(arr: Array<number>): boolean {
        var n = 0;
        arr.forEach((num) => {
            if (num == 6) {
                n++;
            } else {
                n = 0;
            }
        })
        return n >= 3;
    }

    /**
     * 数字分割
     * @param num 
     */
    export function formatNum(num: number): string {
        var tempStr = String(num);
        var tempLen = tempStr.length;
        if (tempLen <= 3) return tempStr;

        var [str, interval] = ['', 3];
        var [count, endIndex, beginIndex] = [Math.floor(tempLen / interval), tempLen % interval, 0];
        if (endIndex == 0) {
            count -= 1;
            endIndex = interval;
        }
        for (var i = 0; i < count; i++) {
            str += tempStr.substring(beginIndex, endIndex) + ',';
            beginIndex = endIndex;
            endIndex += interval;
        }
        str += tempStr.substr(beginIndex, endIndex);
        return str;
    }
    export function langTranslation(tx: string): string {
        return Laya.Text.langPacks && Laya.Text.langPacks[tx] ? Laya.Text.langPacks[tx] : tx;
    }
    export function throttle(fn: Function, time: number = 3000): Function {//节流函数
        let run = true;
        return function () {
            if (!run) return;
            run = false;
            fn.apply(this, arguments);
            setTimeout(() => {
                run = true;
            }, time);
        }
    }
    export var isLocked = false;
    export function withLock(fn: Function, time: number = 3000) {//多按钮节流 外部lock
        return function () {
            if (isLocked) return;
            isLocked = true;
            try {
                fn.apply(this, arguments);
            } finally {
                setTimeout(() => {
                    isLocked = false;
                }, time);
            }
        }
    }
    // export function debounce(fn, time: number = 500) {//防抖函数
    //     let timeout = null;
    //     return function () {
    //         clearTimeout(timeout);
    //         timeout = setTimeout(function () {
    //             fn.apply(this, arguments);
    //         }.bind(this), time);
    //     };
    // }

    export function swapPos(node1: Laya.Sprite, node2: Laya.Sprite, attribute: string = "x") {
        if (yalla.Font.isRight() && node1 && node2) {
            var x1 = node1[attribute], x2 = node2[attribute];
            node1[attribute] = x2;
            node2[attribute] = x1;
        }
    }

    /**
     * 如果str中包含阿语的比例小于设置比例，则分段处理
     * @param str 
     */
    export function RerangeArabic(str: string): string {
        var matchPer = 0.6;//一个字符中阿语占比
        var words: string[] = str.split(' ');
        var twoM: Array<Array<string>> = new Array<Array<string>>();
        var tCount: number = 0;
        var i = 0;
        for (i = 0; i < words.length; i++) {
            var prevIsArabic = false;
            var preIsSpecial = false;
            if (i - 1 >= 0) {
                prevIsArabic = words[i - 1].isArabic(matchPer);
                preIsSpecial = yalla.util.isSpecialSymbol(words[i - 1]);
            }
            var curIsArabic = words[i].isArabic(matchPer);
            var curIsSpecial = yalla.util.isSpecialSymbol(words[i]);

            if (i > 0 && (prevIsArabic != curIsArabic && !preIsSpecial && !curIsSpecial)) {
                tCount += 1;
            }
            // console.log(prevIsArabic, curIsArabic, curIsSpecial, preIsSpecial,i, '-----', words[i],words[i].length);
            if (!twoM[tCount]) twoM[tCount] = new Array<string>();
            twoM[tCount].push(words[i]);
        }

        var result = '';
        var len = twoM.length;
        for (i = 0; i < len; i++) {
            var str = twoM[i].join(" ");
            if (str.isArabic(matchPer)) {
                result += ArabicSupport.FixRL(str, true);
            } else {
                result += str;
            }
            if (i < len - 1) result += " ";
            // console.log('---str:', str);
        }
        return result;
    }

    /**
     * 大写 P 表示 Unicode 字符集七个字符属性之一：标点字符。
     *      S：符号（比如数学符号、货币符号等）；
     * @param str 
     */
    export function isSpecialSymbol(str: string): boolean {
        var patrn = /[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、]/;
        if (patrn.test(str) && str.length <= 2) {
            return true;
        }
        return false;
    }

    /**
     * 根据value 返回key 返回值是数组
     * @param value 
     * @param data 
     * @param compare 
     */
    export function findKey(value, data, compare = (a, b) => a === b) {
        return Object.keys(data).filter(k => compare(data[k], value))
    }
    /**
     * 
     * @param img 
     * @param size 
     * @param pos 
     */
    export function centerSetup(img: Laya.Image, size: number, pos: any) {
        if (img instanceof Laya.Image && img._bitmap && img._bitmap.source) {
            var sourceWidth = img.source.width;
            var sourceHeight = img.source.height;
            var ratio = sourceWidth / sourceHeight;
            var height = size, width = size;
            var offsetX = 0, offsetY = 0;
            var x, y = 0;
            if (pos && typeof pos === 'object') {
                x = pos.x;
                y = pos.y;
            } else {
                x = y = pos;
            }
            if (ratio > 1) {//横向图片
                width = size * ratio;
                var offset = (width - height) * .5
                x = x - offset;
                offsetX = offset;
            } else if (ratio < 1) {//竖向图片
                height = size * (sourceHeight / sourceWidth);
                var offset = (height - width) * .5
                y = y - offset;
                offsetY = offset;
            }
            img.mask && img.mask.pos(offsetX, offsetY);
            img.size(width, height);
            img.pos(x, y);
        }
    }

    /**
     * 是否浏览器操作
     */
    export const IsBrowser = (): Boolean => {
        return !IsWinConch();
    }

    /**
     * 降序
     * @param arr 
     * @param propType 
     */
    export function sortListAsc(arr: Array<any>, propType: string) {
        arr.sort((a: any, b: any) => {
            if (a[propType] > b[propType]) {
                return 1;
            } else if (a[propType] < b[propType]) {
                return -1;
            } else {
                return 0;
            }
        });
        return arr;
    };

    /**
     * 降序
     * @param arr 
     * @param propType 
     */
    export function sortListDes(arr: Array<any>, propType: string) {
        arr.sort((a: any, b: any) => {
            if (a[propType] < b[propType]) {
                return 1;
            } else if (a[propType] > b[propType]) {
                return -1;
            } else {
                return 0;
            }
        });

        return arr;
    }

    /**
     * 根据游戏类型返回url方法
     * @param gameType 
     */
    export function gameUrlFunc(gameType: number): Function {
        var urlFunc = yalla.getGame;
        if (gameType == GameType.DOMINO) {
            urlFunc = yalla.getDomino;
        } else if (this._gameType == GameType.SNAKEANDLADER) {
            urlFunc = yalla.getSnake;
        }
        return urlFunc;
    }

    export function closeAllDialog(): void {
        yalla.common.Confirm.instance.isExit = false;
        Laya.Dialog.closeAll();
        if (yalla.Global.gameType) yalla.Native.instance.friendChat(yalla.Global.gameType);
    }

    /**
     * 1 trustShip托管  2 interactiveGift互动礼物  3 chat聊天面板 4 fastChat快捷聊天  5 roundResult小回合结算
     * @param mode 
     * @param status 0关闭 1打开
     * 点击android返回键有效 非托管 非打开聊天面板 非打开互动表情 非小局结算弹窗 时，支持返回键点击弹出退出游戏弹窗
     * 非这些状态返回无效
     */
    export function updateBackPressed(mode: number, status: number = 0): void {
        if (status) {
            if (!yalla.Global.backStatusHash) yalla.Global.backStatusHash = {};
            yalla.Global.backStatusHash[mode] = status;
        }
        else {
            if (yalla.Global.backStatusHash) delete yalla.Global.backStatusHash[mode];
        }
    }
    export function isBackPressed(): boolean {
        if (!yalla.Global.backStatusHash) return true;
        for (var key in yalla.Global.backStatusHash) {
            return false;
        }
        return true;
    }

    /**
     * 埋点
     * @param eventName 
     * @param returnValue 
     * @param type  1点击时间  3曝光页面
     */
    export function clogDBAmsg(eventName: string, returnValue?: any, type: number = 1): void {
        var logMsg = {};
        if (returnValue) {
            if (typeof returnValue == "object") returnValue = JSON.stringify(returnValue);
            logMsg = { returnValue };
        }
        yalla.Native.instance.clogDBAmsg(type, eventName, logMsg);
    }

    export function clogPlayAgain(type, group = 0) {
        let gameId = yalla.Global.Account.gameId;
        gameId && yalla.util.clogDBAmsg('10407', { gameId, group, type }, 1);
    }
    export function clogShare(type, group = 0) {
        let gameId = yalla.Global.Account.gameId;
        gameId && yalla.util.clogDBAmsg('10406', { gameId, group, type }, 1);
    }
    /**
     * 重置account数据
     */
    export function resetAccount(): void {
        yalla.Global.Account = {
            ip: '',
            token: '',
            encryptToken: '',
            championshipId: 0,
            roomid: null,
            isLimitVisitor: false,
            banTalkData: "",
            version: "",
            voiceType: VoiceType.Agora,
            leagueWatch: false,
            league: null,
            roylevel: 0,
            propImgUrl: "",
            autoAddExp: null,
            sameSkinBuySwitch: 0
        }
    }

    export function getHost(url: string): string {
        if (!url) return '';
        var regex = /^(?:https?:\/\/)?(?:http?:\/\/)?(?:ws?:\/\/)?(?:wss?:\/\/)?(?:[^@\n]+@)?(?:www\.)?([^:\/\n]+)/im;
        var match = regex.exec(url);
        var host = match ? match[1] : '';
        return host;

        // var url1 = url.match(/https?:\/\/([^/]+)\//i);
        // let domain = '';
        // if (url1 && url1[1]) {
        //     domain = url1[1];
        // }
        // return domain;
    }
    export function getRandomNumber(min: number, max: number): number {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    export function getLangText(text: string): string {
        text = Laya.Text.langPacks && Laya.Text.langPacks[text] ? Laya.Text.langPacks[text] : text;
        return text;
    }

    // 定义计算两个坐标点差值的函数
    export function calculateDifference(point1, point2) {
        if (!point1 || !point2 || typeof point1.x === 'undefined' || typeof point1.y === 'undefined' ||
            typeof point2.x === 'undefined' || typeof point2.y === 'undefined') {
            return { x: 0, y: 0 };
        }
        var diffX = (point2.x - point1.x) / 2;
        var diffY = (point2.y - point1.y) / 2;
        // distance: Math.sqrt(diffX * diffX + diffY * diffY)
        return { x: point1.x + diffX, y: point1.y + diffY };
    }
    /**
     * jackaro 游戏ios 日志上报  聊天日志
     */
    export function logReport(msg) {
        //1.4.5 增加ios机型线上偶现bug日志上报，针对jackaro游戏服
        if (yalla.Global.gameType == GameType.JACKARO && yalla.Native.instance.deviceType == DeviceType.IOS) {
            let logStr = {
                log: msg,
                time: yalla.getTimeHMS()
            };
            yalla.Native.instance.writeGameLog("jackaroChatLog", logStr);
        }
    }
}