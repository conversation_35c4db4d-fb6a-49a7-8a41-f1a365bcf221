module yalla.util {

    /**
     * 游戏内弹框互斥逻辑
     * 层级互斥
     * 位置互斥
     * {type, siblingIndex,posIndex closeCb}
     */
    export class ViewExclusion {

        static _instance: ViewExclusion;
        /**互斥弹框 固定配置 */
        public ConfigList: Array<ExclusionParam> = [
            { siblingIndex: 2, posIndex: 3, type: ViewType.FastChatView },//加入jack 的topLayer
            { siblingIndex: 2, posIndex: 3, type: ViewType.ChatView },    //加入jack 的topLayer
            { siblingIndex: 2, posIndex: 3, type: ViewType.FriendChat },  //加入jack 的topLayer
            { siblingIndex: 2, posIndex: 3, type: ViewType.TopSetting },  //加入jack 的topLayer
            { siblingIndex: 2, posIndex: 3, type: ViewType.TopRank },     //加入jack 的topLayer

            { siblingIndex: 3, posIndex: 3, type: ViewType.InteractiveGift},//加入jack 的topLayer
            { siblingIndex: 3, posIndex: 3, type: ViewType.GameUndo },//加入jack 的topLayer
            { siblingIndex: 3, posIndex: 3, type: ViewType.QuickBuyView },//加入jack 的topLayer

            { siblingIndex: 0, posIndex: 3, type: ViewType.RuleCard }
        ]
        /**层级、位置 互斥hash  */
        private recodHash: any = {};
        constructor() { }

        static get instance(): ViewExclusion {
            return ViewExclusion._instance || (ViewExclusion._instance = new ViewExclusion());
        }

        /**
         * 打开面板
         * 检测是否有同位置、同层级互斥view 且 不是自己
         * closeCb 当前打开页面  的关闭方法
         */
        add(type: number, closeCb: Function) {
            if (!this.recodHash) this.recodHash = {};
            let curItem: ExclusionParam = this.getConfigItem(type);
            curItem.closeCb = closeCb;
            Debug.log(type+" 互斥逻辑打点：" + JSON.stringify(this.recodHash));
            for (let k in this.recodHash) {
                let rItem = this.recodHash[k];
                console.log(rItem.siblingIndex == curItem.siblingIndex, rItem.posIndex == curItem.posIndex, rItem.type, curItem.type)
                if ((rItem.siblingIndex == curItem.siblingIndex || rItem.posIndex == curItem.posIndex) && rItem.type != curItem.type) {
                    rItem.closeCb && rItem.closeCb();
                    this.remove(rItem.type);
                }
            }
            this.recodHash[type] = curItem;
        }

        /**
         * 关闭面板
         */
        remove(type: number) {
            if (this.recodHash && this.recodHash[type]) delete this.recodHash[type];
        }

        /**
         * 点击空白区域关闭所有,除了siblingIndex = 1的弹窗
         * siblingIndex 清理所有
         */
        removeAll(siblingIndex = 0) {
            for (let k in this.recodHash) {
                let item: ExclusionParam = this.recodHash[k];
                if (item.siblingIndex != siblingIndex) {
                    item.closeCb && item.closeCb();
                    this.remove(item.type);
                }
            }
            // this.recodHash = {};
        }

        getConfigItem(type): any {
            for (let key in this.ConfigList) {
                let item = this.ConfigList[key];
                if (type == item.type)
                    return item;
            }
            return;
        }

        getItem(type) {
            if (this.recodHash) return this.recodHash[type];
        }

        clear() {
            this.removeAll();
            this.recodHash = null;
        }

    }
    /**
     * 前提直接父级是game，子集对应的zOrder
     */
    export enum zOrderType{
        // Z_Trust = 992,                  //托管
        // Z_Tip = 992,                    //tip提示 
        // Z_InteractiveGift = 993,        //互动礼物面板
        Z_InteractiveGift_Ani = 11,     //互动礼物动画需要在jack各种蒙版层级之下
        // Z_Top = 1,                     //顶部导航栏、设置、Rank对局信息、底部聊天入口、快捷聊天、私聊、增加回合时间,互动礼物面板，加入到topLayer
    }

    export enum ViewType {
        FastChatView,
        ChatView,
        FriendChat,
        TopSetting,//banner 系统设置
        TopRank,   //banner 奖杯信息

        InteractiveGift,
        GameUndo,
        QuickBuyView,//快速购买钻石view

        RuleCard//牌规则
    }

    /**
     * {type, siblingIndex,posIndex closeCb}
     */
    export interface ExclusionParam {
        type?: number,
        siblingIndex?: number,
        posIndex?: number,
        closeCb?: Function,//每个页面对应 的关闭方法
        // LimitViews?:Array<any>,//限制页面出现，当前的页面需要被关闭
    }
}    