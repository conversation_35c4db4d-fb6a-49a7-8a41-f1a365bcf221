module yalla {
    export class Voice extends Laya.EventDispatcher {
        private _voiceOpen: boolean = false;//语音开关
        public mutePlayers: Array<number> = [];//禁言列表
        public token: string = null;
        public channelId: string = null;
        public joinSuccess: boolean = false;
        public joinTimes: number = 0;
        private static _instance: yalla.Voice = null;
        static get instance() {
            if (!this._instance) this._instance = new yalla.Voice();
            return this._instance;
        }
        constructor() { super(); }
        public set voiceOpen(b: boolean) {
            if (b != this._voiceOpen) {
                this._voiceOpen = b;
                this.event("change", [b]);
            }
        }
        public get voiceOpen(): boolean { return this._voiceOpen }
        /**
         * 加入语音房间
         */
        public joinGameRoomWithToken(idx: number) {
            if (this.joinSuccess) return;
            yalla.Debug.log("run joinGameRoomWithToken")
            yalla.Native.instance.joinGameRoomWithToken(this.token, this.channelId, idx, yalla.Global.Account.voiceType, (val: number) => {
                if (val) {
                    this.joinSuccess = !!val;
                    this.event("join");
                } else {
                    if (yalla.Global.onlineMode && this.token) {
                        this.joinTimes++;
                        if (this.joinTimes < 2) {
                            this.joinGameRoomWithToken(idx);
                        } else {
                            var tip = yalla.common.TipItem.instance;
                            tip.pushTip((Laya.stage.width - tip.width) / 2, Laya.stage.height / 2, yalla.data.TranslationD.Game_Tip_Fail_Microphone, { alpha: 0 }, 0, 5000);
                        }
                    }
                }
            })
        }
        /**
         * 关闭音频
         */
        public disableAudio(cb: Function) {
            yalla.Native.instance.disableAudio((success) => {
                if (success == 0) {
                    this.voiceOpen = false;
                    cb && cb();
                }
            })
        }
        /**
         * 打开音频
         * clickState 默认0   手动点击语音 1
         */
        public enableAudio(cb: Function, errCb: Function = null, clickState: number = 0) {
            yalla.Native.instance.currentAudioStatus(obj => {//先检测有权限再开麦  
                if (obj.status) {
                    if (!this.joinSuccess) {
                        if (!!this.token) {
                            this.joinGameRoomWithToken(yalla.Global.Account.idx);
                        } else {
                            switch(Global.gameType){
                                case GameType.LUDO:
                                    ludo.GameSocket.instance.getToken();
                                    break;
                                case GameType.SNAKEANDLADER:
                                    yalla.data.snake.UserService.instance.getToken();    
                                    break;
                                case GameType.JACKARO:
                                    yalla.data.jackaro.JackaroUserService.instance.getToken();    
                                    break;
                            }
                        }
                        return;
                    }
                    yalla.Native.instance.enableAudio((success) => {
                        if (success == 0) {
                            this.voiceOpen = true;
                            cb && cb();
                        }
                    }, clickState)
                } else {
                    // yalla.common.Tip.instance.showTip(yalla.data.TranslationD.Game_Tip_Check_Microphone);
                    if (errCb) {
                        errCb();
                    } else {
                        var tip = yalla.common.TipItem.instance;
                        tip.pushTip((Laya.stage.width - tip.width) / 2, Laya.stage.height / 2, yalla.data.TranslationD.Game_Tip_Check_Microphone, { alpha: 0 }, 0, 5000);
                    }
                    if (yalla.Native.instance.deviceType == DeviceType.Android) {
                        yalla.Native.instance.enableAudio((success) => {
                            if (success == 0) {
                                this.voiceOpen = true;
                                cb && cb();
                            }
                        }, clickState)
                    }
                }
            })
        }
        /**
         * 是否禁言
         */
        public muted(idx: number): boolean {
            return this.mutePlayers.indexOf(idx) >= 0;
        }
        /**
         * 离开房间
         */
        public levelGameRoom(cb?,isForceCb = true) {
            this.reset();
            yalla.Native.instance.levelGameRoom(cb, isForceCb);
        }

        /**
         * 离开房间
         */
        public levelGameRoom_android(cb?) {
            if (yalla.Native.instance.deviceType == DeviceType.Android) {
                if (this.joinSuccess) {
                    this.levelGameRoom(cb);
                }
            }    
        }
        /**
         * reset
         */
        public reset() {
            this.voiceOpen = false;
            this.joinSuccess = false;
            this.mutePlayers = [];
            this.token = null;
            this.channelId = null;
        }

        /**1.4.4.1 ios游戏结算先退出语音房，再播放音效；android保持不变 */
        /**1.4.5 android还是等离开后才退出语音房 ios离开语音房间回掉 中 播放结算音效*/
        public gameOverSound(soundName) {
            // this.levelGameRoom(()=>{
            //     yalla.Sound.playSound(soundName);
            // }, false);
            if (yalla.Native.instance.deviceType == DeviceType.IOS) {
                this.levelGameRoom(()=>{
                    yalla.Sound.playSound(soundName);
                });
            } else {
                yalla.Sound.playSound(soundName, Laya.Handler.create(this, () => { this.levelGameRoom(); }));
            }
        }

        /**
         * 屏蔽制定用户音频
         * data {
         *  "uId": number,用户ID
         *  "muted":boolean false取消 true屏蔽
         *  
         * }
         */
        public muteRemoteAudioStream(uId: number, muted: boolean, cb: Function) {
            yalla.Native.instance.muteRemoteAudioStream(uId, muted, (status, uid) => {
                cb && cb(status, uid);
            });
            var index = this.mutePlayers.indexOf(uId);
            if (muted) {
                if (index < 0)
                    this.mutePlayers.push(uId);
            } else {
                if (index >= 0) {
                    this.mutePlayers.splice(index, 1);
                }
            }
        }

        /**
         * 开麦、闭麦通知api
         * @param data 
         */
        public voiceStatistic(data) {
            // var url = `${yalla.DomainUtil.instance.game_account_url}/YallaGame/SundryInfo/VoiceStatistic`;
            var url = `/api/LudoBuriedDataRpcApiProxy/VoiceStatisticAdd`;
            var type = data['type'];
            yalla.Native.instance.sendHttpRequest(url, data, (resultCode) => {
                //失败
                if (resultCode != 200) {
                    var nextUrl = yalla.DomainUtil.instance.getNextUrlByType(DomainType.GAME_ACCOUNT);
                    if (nextUrl) {
                        yalla.Voice.instance.voiceStatistic(data);
                    }
                } else {
                }
            })
        }
    }
}
