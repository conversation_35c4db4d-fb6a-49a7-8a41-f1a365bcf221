class ActivityMain extends BaseMain {
    /**
     * 日锦赛 DayGame
     */
    private _firstOnloginPlay:boolean = true;//android需要onlogin 的playMusic(0)先播放， 如果执行onBlur的playMusic（1）就是接着上次续播
    private _delayMusicTime:number = 1000;

    constructor() {
        super();
        Laya.stage.bgColor = "#15d1e2";
        if(this._gameView) this.clear();
        
        if (!this._gameView) {
            this._gameView = new yalla.activity.DayGame();
            this._gameView.name = 'activityView';
            Laya.stage.addChild(this._gameView);
        }
    }

    /**
     * 切入后台
     */
    public onBlur(): void {
        super.onBlur();
        yalla.Global.isFouce = false;
        if(this._gameView && this._gameView.visible){
            this._gameView.hide();

            if (yalla.Native.instance.deviceType != DeviceType.IOS) {//ios 切到后台，背景音乐停止的代码不执行，so音乐停止ios触发
                yalla.Sound.playMusic('champion', 2);
            }
        }
    }

    /**
     * 切到前台
     */
    public onForce(): void {
        super.onForce();
        yalla.Global.isFouce = true;
        if(this._gameView && this._gameView.visible){
           this._gameView.show();
            if(!this._firstOnloginPlay){
                yalla.Sound.playMusic('champion', 1);
            }
        }
    }

    /**
     * onKickedOut
     * 被顶号
     * 音效关闭
     */
    public onKickedOut() {
        yalla.Global.IsGameOver = true;
        yalla.Sound.stopAll();
        this.clear();
    }

    /**
     * 登陆游戏
     * @param msg 
     */
    public onLogin(msg: any): void {
        super.onLogin(msg);
        this._firstOnloginPlay = false;
        yalla.Sound.playMusic('champion', 0);
        
        yalla.data.ActivityService.instance.championshipId = msg.championshipId;
        yalla.data.ActivityService.instance.ChampionshipOption(yalla.data.ChampoinEvent.CHAMPION_INFO);

        yalla.Native.instance.showMaquee({status:1});
    }

    /**
     * 大厅soket 链接情况
     * {"code":-1|1} 1 与服务器连接上，-1与服务器断开
     */
    public connectStateChange(json) {
        this.gameView && this.gameView['connectStateChange'](json.code);
    }

    /**
     * 退出日锦赛
     * @param msg 
     */
    public onQuitGame(msg: any): void {
        super.onQuitGame(msg);
    }

    /**
     * 游戏返回锦标赛
     */
    public backChampion(d:any): void { 
        super.backChampion(d);
        this._gameView.visible = true;
        this._gameView['backChampion'](d);

        yalla.Sound.playMusic('champion', 0);
        yalla.Native.instance.showMaquee({status:1});
    }

    /**
     * 切入游戏效果
     */
    private _tw:Laya.Tween;
    public changeToGame():void{
        super.changeToGame();
        yalla.util.closeAllDialog();
        yalla.Sound.playMusic('champion', 2);

        this.gameView['changeToGame']();
        this.gameView.visible = false;
        yalla.Native.instance.showMaquee({status:0});

        if (this._gameView) {//ActivityService 数据不能清理
            this._gameView.clear(true);
            yalla.activity.ChampionAnimation.instance.clear();
            yalla.activity.ChampionAnimation._instance = null;
        }
        Laya.timer.clearAll(this);
        this._gameView = null;
    }

    public backHallClear(): void {
        if (this._gameView) {
            this._gameView.clear(true);
        }
        yalla.data.ActivityService.instance.clear();
        yalla.activity.ChampionAnimation.instance.clear();
        yalla.activity.ChampionAnimation._instance = null;
        
        this._firstOnloginPlay = true;
        yalla.Global.game_state = '';
        yalla.Sound.canPlayMusic = false;
        Laya.timer.clearAll(this);
        
        //TODO ios不让执行stopAllAudio
        yalla.Sound.playMusic('champion', 2);
        Laya.SoundManager.stopAll();
        yalla.Global.LoadingChampionState = 0;
    }

    public clear(removeView: Boolean = true): void {
        super.clear();
        this.backHallClear();
        this._gameView = null;
    }
}