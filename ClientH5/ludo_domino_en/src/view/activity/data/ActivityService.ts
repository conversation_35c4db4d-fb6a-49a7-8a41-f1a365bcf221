module yalla.data {
    import EventDispatcher = laya.events.EventDispatcher;
    export class ActivityService extends EventDispatcher {
        static _instance: ActivityService;
        public championshipId: number;                          //动态竞标赛id
        public matchPlayer: ChampionshipPlayerInfo;             //{idx name headUrl money}
        public player: ChampionshipPlayerInfo = {}              //匹配者信息 {idx name headUrl}
        public championInfo: ChampionInfo = {};                 //锦标赛动态数据
        public championConfig: ChampionConfig = {};             //锦标赛 静态数据
        public MAX_LEVEL: number = 6;
        public LEVEL_POINT: any = {
            1: [[336, 912, 1]],//x,y,level
            2: [[494, 816, 2]],
            3: [[192, 720, 3]],
            4: [[562, 596, 4]],
            5: [[106, 482, 5]],
            6: [[600, 258, 6]],
            // 6: [[632, 290, 6], [336, 182, 7]],
        };
        public STAY_POINT: any = {
            1: [262, 870],
            2: [410, 740],
            3: [296, 646],
            4: [468, 508],
            5: [230, 376],
            6: [625, 120],
        }
        public HEAD_SIDE: any = {
            1: 1,
            2: 1,
            3: 0,
            4: 1,
            5: 0,
            6: 0,
        }

        Event = {
            GAME_EVENT: "ChampionshipOption",//数据事件
        }

        // public RULE: Array<any> = [
        //     // { title: 'Ludo Tournament Rules' },
        //     // { title: 'Domino Tournament Rules' },
        //     { title: 'Tournament Rules' },
        //     { box: 'rule_item'},
        //     { explain: '1. Tournament opens from level 5.'},
        //     { img:['quit.png','Continuethegame.png']},
        //     { explain: '2. After winning a round, you can either play next round, or quit the match with bonus.'},
        //     { explain: '3. You can share 25% of the prize pool as extra bonus, if you win the final round.'},
        //     // { explain: '1. Mode: Classic '},
        //     // { explain: '1. Mode: Quick '},
        //     // { explain: '1. Mode: Draw Game'},
        //     // { explain: '1. Mode: All Five'},
        //     // { explain: '2. Only players who reach level 5 can join the tournament.' },
        //     // { explain: '3. If you win a round, you can play the next round or quit this match with the golds you win. ' },
        //     // { explain: '4. This picture below shows the amount of golds that player can win in each round. '},
        //     // { explain: '5. If you win the 6th round, you can gain 25% golds of prize pool.', bg: 1},
        //     // { explain: '6. If you lost the game, your remaining golds will be added to the prize pool. '},
        //     // { explain: '7. Those who win the 6th round can gain 25% golds of prize pool .', img: 'activity/99999.png', bg: 2 },
        // ];

        constructor() {
            super();
        }

        static get instance(): ActivityService {
            return ActivityService._instance || (ActivityService._instance = new ActivityService());
        }

        public get isBusy(): boolean {
            return this.player.status == ChampionshipStatus.MATCH_ING || this.player.status == ChampionshipStatus.GAME_ING;
        }

        public getSide(lv:number):number{
            return ActivityService.instance.HEAD_SIDE[lv];
        }

        public updateGolds(d: any): void {
            if (this.player) {
                this.player.gameGold = d.gold;
            }
        }

        //游戏调用原生  1获取锦标赛信息&用户信息 2报名 3放弃 4匹配 5取消匹配    6更新匹配信息匹配成功 7胜负通知
        public ChampionshipOption(option: number): void {
            var p = { option: option, championshipId: this.championshipId };
            yalla.Native.instance.ChampionshipOption(p, (d: any) => {
                var option = d.option;
                var data = d.data;
                switch (option) {
                    case yalla.data.ChampoinEvent.CHAMPION_INFO:
                        this.initData(data);
                        break;

                    case yalla.data.ChampoinEvent.SIGNUP:
                        if (data.success && this.player) {
                            this.player.status = ChampionshipStatus.MATCH;
                            this.player.moneyLeft = this.championConfig.payMoney;
                        }
                        break;

                    case yalla.data.ChampoinEvent.SIGNUP_QUIT:
                        if (data.success) {
                            this.resetRoundPlayerData();
                        }
                        break;
                    case yalla.data.ChampoinEvent.MATCH:
                        break;

                    case yalla.data.ChampoinEvent.MATCH_CANCEL:
                        if (data.success && this.player) this.player.status = ChampionshipStatus.MATCH;
                        break;

                    case yalla.data.ChampoinEvent.MATCH_SUCCESS:
                        if (!this.player) return;
                        this.player.status = ChampionshipStatus.GAME_ING;
                        yalla.Debug.log('====锦标赛匹配成功信息===');
                        yalla.Debug.log(d.data.matchPlayer);
                        this.matchPlayer = d.data.matchPlayer;
                        var headUrl = laya.utils.Browser.window.Base64.decode(this.matchPlayer.headUrl);
                        var name = laya.utils.Browser.window.Base64.decode(this.matchPlayer.name);
                        if (this.matchPlayer.level < 1) this.matchPlayer.level = 1;
                        this.matchPlayer.name = name;
                        this.matchPlayer.headUrl = headUrl;
                        break;

                    case yalla.data.ChampoinEvent.CHAMPION_RESULT:
                        break;
                }
                this.event(this.Event.GAME_EVENT, d);
                var isMatchIng = data.matchInfo && data.matchInfo.roomid;
                if (data.errorCode > 1 && !isMatchIng) {
                    var btnName = 'Confirm';
                    if(data.errorCode == yalla.data.ErrorCode.CHAMPIONSHIP_RIGISTER_NOMONEY) btnName = 'Get More Golds';
                    yalla.common.WaringDialog.showChampionError(data.errorCode, () => {
                        if(data.errorCode == yalla.data.ErrorCode.CHAMPIONSHIP_RIGISTER_NOMONEY){
                            yalla.Native.instance.showShop();
                        }
                        yalla.common.Confirm.instance.hideConfirm();
                    }, [btnName]);
                }
            });
        }

        public initData(d: any): void {
            if (!d) return;
            if (d.player) {
                this.player = d.player;
                if (this.player.headUrl) {
                    var headUrl = laya.utils.Browser.window.Base64.decode(this.player.headUrl);
                    this.player.headUrl = headUrl;
                }
                if (this.player.name) {
                    var name = laya.utils.Browser.window.Base64.decode(this.player.name);
                    this.player.name = name;
                }
                if (this.player) {
                    if (this.player.level < 1) this.player.level = 1;
                    this.player.faceId = d.player.headId;
                }
            }
            if (d.config) {
                this.championConfig = d.config;
                this.MAX_LEVEL = this.championConfig.levelConfigList.length;
            }
            if (d.championshipInfo) this.championInfo = d.championshipInfo;
        }

        /**
         * moneyLeft 不更新，因dayGame中，主动退出用到此值
         */
        public resetRoundPlayerData(): void {
            if (this.player) {
                this.player.status = ChampionshipStatus.NO_SIGNUP;
                this.player.level = 1;
                this.player.exitCurrency = 0;
            }
        }

        public register(): void { }

        /**
         * 能否报名
         */
        public checkJoin(): boolean {
            if (this.player.gameRoleLevel < this.championConfig.minLevel) {
                yalla.common.WaringDialog.showChampionError(yalla.data.ErrorCode.CHAMPIONSHIP_RIGISTER_LEVEL, () => {
                    yalla.common.Confirm.instance.hideConfirm();
                });
                return false;
            }
            // if (this.player.gameGold < this.championConfig.payMoney) {
            //     yalla.common.WaringDialog.showChampionError(yalla.data.ErrorCode.CHAMPIONSHIP_RIGISTER_NOMONEY, () => {
            //         yalla.Native.instance.showShop();
            //         yalla.common.Confirm.instance.hideConfirm();
            //     });
            //     return false;
            // }
            if (this.player.status == ChampionshipStatus.MATCH) {//已经在匹配了
                yalla.common.WaringDialog.showChampionError(yalla.data.ErrorCode.CHAMPIONSHIP_MATCH_INMATCH, () => {
                    yalla.common.Confirm.instance.hideConfirm();
                });
                return false;
            }
            // if (this.player.status == 2 || this.player.status == 3) {//游戏中
            //     yalla.common.WaringDialog.showChampionError(yalla.data.ErrorCode.CHAMPIONSHIP_MATCH_INGAME, () => {
            //         yalla.common.Confirm.instance.hideConfirm();
            //     });
            //     return false;
            // }
            return true;
        }

        /**
         * 能否匹配
         */
        public checkNext(): boolean {
            if (this.player.gameRoleLevel < this.championConfig.minLevel) {
                yalla.common.WaringDialog.showChampionError(yalla.data.ErrorCode.CHAMPIONSHIP_RIGISTER_LEVEL, () => {
                    yalla.common.Confirm.instance.hideConfirm();
                });
                return false;
            }
            if (this.player.status == ChampionshipStatus.NO_SIGNUP) {//未报名
                yalla.common.WaringDialog.showChampionError(yalla.data.ErrorCode.CHAMPIONSHIP_MATCH_NORIGISTER, () => {
                    yalla.common.Confirm.instance.hideConfirm();
                });
                return false;
            }
            // if (this.player.status == 2 || this.player.status == 3) {//游戏中
            //     yalla.common.WaringDialog.showChampionError(yalla.data.ErrorCode.CHAMPIONSHIP_MATCH_INGAME, () => {
            //         yalla.common.Confirm.instance.hideConfirm();
            //     });
            //     return false;
            // }
            return true;
        }

        public clear(): void {
            this.championConfig = null;
            this.championInfo = null;
            this.player = null;
            this.matchPlayer = null;
            this.championshipId = 0;
        }
    }
}