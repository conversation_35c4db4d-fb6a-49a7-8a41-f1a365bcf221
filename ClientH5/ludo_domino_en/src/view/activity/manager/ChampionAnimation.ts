module yalla.activity {
    import EventDispatcher = laya.events.EventDispatcher;
    /**
     * 日锦赛 动画
     */
    export class ChampionAnimation extends EventDispatcher {
        static _instance: ChampionAnimation;
        public templet: Laya.Templet;
        public qizi1Sk: Laya.Skeleton;//播放qizi1 qizi2 用同一个动画对象
        public pipeiSk: Laya.Skeleton;
        public VSdonghuaSk: Laya.Skeleton;
        public VSnormalSk: Laya.Skeleton;
        public qizi3Sk: Laya.Skeleton;
        public qiziStarSk: Laya.Skeleton;

        Event = {
            COMPLETE: "complete",//动画加载完成
        }

        constructor() {
            super();
        }

        static get instance(): ChampionAnimation {
            return ChampionAnimation._instance || (ChampionAnimation._instance = new ChampionAnimation());
        }

        public initAnimation(): void {
            if (!this.templet) {
                this.templet = new Laya.Templet();
                this.templet.on(Laya.Event.COMPLETE, this, this.skComplete);
                this.templet.loadAni(yalla.getSkeleton('dayChampionVS/skeleton'));
            }
        }

        //VSdonghua   pipei   pipeibaodian   qizi1   qizi2
        private skComplete() {
            if(!this.templet) return;
            // var count = this.templet.getAnimationCount();
            // for(var i=0; i<count; i++){
            //     console.log(this.templet.getAnimation(i));
            // }

        //TODO::可以替换skeleton动画中的纹理，只能通过skinDataArray、skinDic替换，subTextureDic属性无法修改纹理
            //     var txture:Laya.Texture = Laya.loader.getRes("public/121008.png");//这里的路径不能传错，图集下的某资源
            //     this.templet.skinDataArray[0].slotArr[6].displayArr[0].texture = txture;


            this.qizi1Sk = this.templet.buildArmature(1);
            this.pipeiSk = this.templet.buildArmature(1);
            this.VSdonghuaSk = this.templet.buildArmature(1);
            this.VSnormalSk = this.templet.buildArmature(1);
            this.qizi3Sk = this.templet.buildArmature(1);
            this.qiziStarSk = this.templet.buildArmature(1);

            this.event(ChampionAnimation.instance.Event.COMPLETE);
        }

        private removeEvent(): void {
            if (this.pipeiSk) {
                this.pipeiSk.stop();
                this.pipeiSk.offAll();
            }

            if (this.qizi1Sk) {
                this.qizi1Sk.stop();
                this.qizi1Sk.offAll();
            }

            if (this.VSnormalSk) {
                this.VSnormalSk.stop();
                this.VSnormalSk.offAll();
            }

            if (this.VSdonghuaSk) {
                this.VSdonghuaSk.stop();
                this.VSdonghuaSk.offAll();
            }

            if (this.qizi3Sk) {
                this.qizi3Sk.stop();
                this.qizi3Sk.offAll();
            }

            if (this.qiziStarSk) {
                this.qiziStarSk.stop();
                this.qiziStarSk.offAll();
            }
            Laya.timer.clearAll(this);
        }

        public clear(): void {
            this.removeEvent();
            this.pipeiSk && this.pipeiSk.destroy(true);
            this.qizi1Sk && this.qizi1Sk.destroy(true);
            this.VSnormalSk && this.VSnormalSk.destroy(true);
            this.VSdonghuaSk && this.VSdonghuaSk.destroy(true);
            this.qizi3Sk && this.qizi3Sk.destroy(true);
            this.qiziStarSk && this.qiziStarSk.destroy(true);
            this.templet && this.templet.destroy();
            this.templet = null;
        }
    }
}