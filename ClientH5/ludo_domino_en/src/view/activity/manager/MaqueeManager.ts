module yalla.activity {
import EventDispatcher = laya.events.EventDispatcher;
    export class MaqueeManager extends EventDispatcher{

        static _instance:MaqueeManager;
        public maqueeItem:MaqueeItem;

        constructor(){
            super();
        }

        static get instance(): MaqueeManager {
            return MaqueeManager._instance || (MaqueeManager._instance = new MaqueeManager());
        }

        private _count = 0;
        private testAddMaquee(msg:any):void{
            this._count += 1;
            var [gameId, gameType] = [this._count%2 == 0 ? 10019:10021, this._count%2 == 0 ? 0 : (gameId==10019)?2:1];
            this.maqueeItem.addItem([{gameId:gameId, gameType:gameType, content:msg.content}]);
        }

        public addToWordListC(msg:any):void{
            if(!this.maqueeItem) this.maqueeItem = new MaqueeItem();
            // this.maqueeItem.addItem(msg);

            //TODO::本地测试代码
            this.testAddMaquee(msg);
            Laya.timer.loop(2000, this, this.testAddMaquee, [msg]);
        }

        /**
         * 显示锦标赛跑马灯
         */
        public playChampionMaquee(parent:any):void{
            if(this.maqueeItem && !this.maqueeItem.displayedInStage){
                this.maqueeItem.y = 128//parent['infoTxt'].y + parent['infoTxt'].height;
                // this.maqueeItem.centerX = 0;
                parent.addChild(this.maqueeItem);
            }
            this.maqueeItem && this.maqueeItem.play();
        }

        /**
         * 停止跑马灯，转场时需要停止（锦标赛到游戏）
         */
        public stop():void{
            this.maqueeItem && this.maqueeItem.stop();
        }

        public clear():void{
            Laya.timer.clearAll(this)
            this.maqueeItem && this.maqueeItem.clear();
            this.maqueeItem = null;
        }
    }
}