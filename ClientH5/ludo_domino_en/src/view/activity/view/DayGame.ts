module yalla.activity {
    import ActivityService = yalla.data.ActivityService;
    import Global = yalla.Global;
    import ChampoinEvent = yalla.data.ChampoinEvent;
    import ChampionshipStatus = yalla.data.ChampionshipStatus;

    export class DayGame extends ui.publics.activity.dayChampionUI {
        public vsAni: VsAnimation;
        public dayResult: DayResult;
        public dayRule: DayRule;
        public head: yalla.activity.HeadLevel;
        private _timeLine: Laya.TimeLine;
        private _templet: Laya.Templet;
        private _sk: Laya.Skeleton;
        private _jianSk: Laya.Skeleton;
        private _curItem: any;                  //当前管卡对象
        private _curLevelItem: LevelItem;       //当前等级动画管理对象
        private _playMoveLv: number = 0;        //角色最新等级
        private _isMoving: boolean = false;     //是否移动
        private _isMaterQuit: boolean = false;  //是否主动拿钱退出游戏
        private _backData: any;                 //游戏返回锦标赛的数据

        private _isSendMatchCD: boolean = false;  //是否发起了匹配 option=4(500ms的冷却时间，避免在未出现匹配界面前，快速点击右上角的关闭)
        private _isSendClose: boolean = false;    //是否发起右上角的关闭

        constructor() {
            super();
            this.initUI();
            this.initEvent();

            // let byte = new Uint8Array([8, 104, 16, -22, -73, -123, -89, 15, 26, -25, 2, 8, -74, 7, 26, -76, 1, 8, -60, -99, -128, 54, 16, -100, -117, 8, 26, 119, 104, 116, 116, 112, 115, 58, 47, 47, 102, 105, 108, 101, 46, 121, 97, 108, 108, 97, 108, 117, 100, 111, 46, 99, 111, 109, 47, 65, 99, 99, 111, 117, 110, 116, 83, 111, 109, 101, 116, 104, 105, 110, 103, 47, 49, 49, 51, 50, 52, 57, 57, 56, 56, 47, 65, 118, 97, 116, 97, 114, 47, 83, 116, 97, 103, 105, 110, 103, 47, 49, 55, 50, 50, 56, 52, 51, 52, 51, 48, 55, 49, 50, 47, 69, 57, 68, 68, 50, 55, 57, 55, 48, 49, 56, 67, 65, 68, 55, 57, 49, 56, 54, 69, 48, 51, 69, 56, 67, 53, 65, 69, 67, 56, 68, 67, 46, 112, 110, 103, 34, 15, 80, 108, 97, 121, 101, 114, 95, 49, 56, 57, 50, 52, 57, 51, 49, 56, 1, 64, 90, 120, 2, -120, 1, -89, -106, -114, -64, 70, -80, 1, 1, -56, 1, 1, -48, 1, 1, -40, 1, -22, -124, -20, -36, 3, -24, 1, -109, 95, 26, -86, 1, 8, -111, 118, 16, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, 26, 93, 104, 116, 116, 112, 115, 58, 47, 47, 102, 105, 108, 101, 46, 121, 97, 108, 108, 97, 108, 117, 100, 111, 46, 99, 111, 109, 47, 65, 99, 99, 111, 117, 110, 116, 83, 111, 109, 101, 116, 104, 105, 110, 103, 47, 49, 53, 49, 50, 49, 47, 65, 118, 97, 116, 97, 114, 47, 83, 116, 97, 103, 105, 110, 103, 47, 49, 54, 51, 49, 56, 55, 50, 54, 49, 50, 51, 57, 49, 47, 116, 101, 115, 116, 95, 49, 53, 49, 50, 49, 46, 71, 73, 70, 34, 30, 65, 111, -16, -97, -104, -127, -16, -97, -104, -126, -16, -97, -104, -110, -16, -97, -104, -116, -16, -97, -104, -96, -16, -97, -104, -102, -16, -97, -104, -96, 56, 104, 64, 32, 72, 1, 120, 1, -120, 1, -111, 118, -96, 1, 6, -80, 1, 1, -56, 1, 1, -48, 1, 1, -40, 1, -127, -15, 2]);
            // var uByte = new Uint8Array([8, 104, 16, 234, 183, 133, 167, 15, 26, 231, 2, 8, 182, 7, 26, 180, 1, 8, 196, 157, 128, 54, 16, 156, 139, 8, 26, 119, 104, 116, 116, 112, 115, 58, 47, 47, 102, 105, 108, 101, 46, 121, 97, 108, 108, 97, 108, 117, 100, 111, 46, 99, 111, 109, 47, 65, 99, 99, 111, 117, 110, 116, 83, 111, 109, 101, 116, 104, 105, 110, 103, 47, 49, 49, 51, 50, 52, 57, 57, 56, 56, 47, 65, 118, 97, 116, 97, 114, 47, 83, 116, 97, 103, 105, 110, 103, 47, 49, 55, 50, 50, 56, 52, 51, 52, 51, 48, 55, 49, 50, 47, 69, 57, 68, 68, 50, 55, 57, 55, 48, 49, 56, 67, 65, 68, 55, 57, 49, 56, 54, 69, 48, 51, 69, 56, 67, 53, 65, 69, 67, 56, 68, 67, 46, 112, 110, 103, 34, 15, 80, 108, 97, 121, 101, 114, 95, 49, 56, 57, 50, 52, 57, 51, 49, 56, 1, 64, 90, 120, 2, 136, 1, 167, 150, 142, 192, 70, 176, 1, 1, 200, 1, 1, 208, 1, 1, 216, 1, 234, 132, 236, 220, 3, 232, 1, 147, 95, 26, 170, 1, 8, 145, 118, 16, 255, 255, 255, 255, 255, 255, 255, 255, 255, 1, 26, 93, 104, 116, 116, 112, 115, 58, 47, 47, 102, 105, 108, 101, 46, 121, 97, 108, 108, 97, 108, 117, 100, 111, 46, 99, 111, 109, 47, 65, 99, 99, 111, 117, 110, 116, 83, 111, 109, 101, 116, 104, 105, 110, 103, 47, 49, 53, 49, 50, 49, 47, 65, 118, 97, 116, 97, 114, 47, 83, 116, 97, 103, 105, 110, 103, 47, 49, 54, 51, 49, 56, 55, 50, 54, 49, 50, 51, 57, 49, 47, 116, 101, 115, 116, 95, 49, 53, 49, 50, 49, 46, 71, 73, 70, 34, 30, 65, 111, 240, 159, 152, 129, 240, 159, 152, 130, 240, 159, 152, 146, 240, 159, 152, 140, 240, 159, 152, 160, 240, 159, 152, 154, 240, 159, 152, 160, 56, 104, 64, 32, 72, 1, 120, 1, 136, 1, 145, 118, 160, 1, 6, 176, 1, 1, 200, 1, 1, 208, 1, 1, 216, 1, 129, 241, 2]);
            // // var str = '';
            // // for (var i = 0; i < byte.length; i++){
            // //     byte[i] = (byte[i] < 0) ? (byte[i] + 256) : byte[i];
            // //     str = str + byte[i] + ",";
            // // }
            // // console.log("==111=="+str);
            // let msg = yalla.data.jackaro.JackaroUserService.instance.decodeMsg(uByte, 'SettlementPub', true);
            // console.log(msg);
            // console.log("========test======");

        }

        private initUI(): void {
            this.moneyTxt.autoSize = true;
            ChampionAnimation.instance.initAnimation();
            var screen = yalla.Screen;
            if (screen.hasHair) {
                // this.topBg.height += Global.screen_top_height / 2;
                // this.infoTxt.y += Global.screen_top_height / 2 ;
                // this.joinBtn.y += Global.screen_top_height / 2 ;
                // this.nextBtn.y += Global.screen_top_height / 2 ;
                var hairHeight = screen.hairHeight ? screen.hairHeight / screen.screen_scale : screen.screen_top_height / 2;
                this.topBg.height += hairHeight;
                this.infoTxt.y += hairHeight;
                this.joinBtn.y += hairHeight;
                this.nextBtn.y += hairHeight;
                if (Global.ProfileInfo.hairHeight) {
                    this.closeBtn.centerY = this.quitBtn.centerY = this.imgTitle.centerY = 20;
                    this.mapSp.centerY = 10;
                }
            }
            if (yalla.Font.isRight()) {
                this.quitBtn.left = NaN;
                this.quitBtn.right = 32;
                this.closeBtn.right = NaN;
                this.closeBtn.left = 20;
                this.ruleBtn.left = 20;
                this.winIcon.y = -10;
                this.winIcon.skin = "public/pic_WIN_A.png";
            } else {
                this.quitBtn.left = 32;
                this.quitBtn.right = NaN;
                this.closeBtn.right = 2;
                this.closeBtn.left = NaN;
                this.ruleBtn.left = 4;
                this.winIcon.y = -6;
                this.winIcon.skin = "public/pic_WIN.png";
            }
            this.infoTxt.width += screen.screen_top_width;
            this.initSK();

            this.updateProp();
            // ActivityService.instance.player.level = this._tempLv;
            // this.updateUserInfo();
            // this.ruleBtn.visible = true;
        }

        private initEvent(): void {
            this.nextBtn.on(Laya.Event.CLICK, this, this.onNext);//level>1 匹配
            this.joinBtn.on(Laya.Event.CLICK, this, this.onJoin);//刚进入  报名+匹配
            this.closeBtn.on(Laya.Event.CLICK, this, this.onClose);//暂时离开
            this.quitBtn.on(Laya.Event.CLICK, this, this.onQuit);//弹框二次确认
            this.ruleBtn.on(Laya.Event.CLICK, this, this.onRules);
            this.prop_icon_bg.on(Laya.Event.CLICK, this, this.onClickProp);//打开道具

            ChampionAnimation.instance.on(ChampionAnimation.instance.Event.COMPLETE, this, this.completeAnimation);
            ActivityService.instance.on(ActivityService.instance.Event.GAME_EVENT, this, this.onGameEvent);
            // yalla.Native.instance.on(yalla.Native.instance.Event.ONBACKPRESSED, this, this.onBackPressed);
        }

        /**
         * 同时播放爆点和 关卡放大效果
         */
        private initSK() {
            if (!this._templet) {
                this._templet = new Laya.Templet();
                this._templet.loadAni(yalla.getSkeleton('dayChampion/skeleton'));
                this._templet.on(Laya.Event.COMPLETE, this, () => {
                    yalla.Native.instance.removeMatchView(false);
                    yalla.Global.LoadingChampionState = 2;

                    [this._sk, this._jianSk] = [this._templet.buildArmature(1), this._templet.buildArmature(1)];
                    var player = ActivityService.instance.player;
                    if (player && player.level) this.playJianVsAni(player.level, player.status);//TODO::可能数据获得但是资源未加载完成，所以资源加载后再次执行动画播放

                });
            }
        }

        /**
         * 更新道具
         * @param d 
         */
        private updateProp(): void {
            // var url = yalla.Global.Account.propImgUrl;
            // url && yalla.File.downloadImgByUrl(url, (localPath: string) => {
            //     Laya.loader.load(localPath, Laya.Handler.create(this, (e) => {
            //         if (e && this.propIcon) {
            //             this.propIcon.skin = localPath;
            //         }
            //     }));
            // })
            // var defaultUrl = 'public/coins.png';
            // var propUrl = yalla.Global.Account.propImgUrl;
            // if (propUrl && propUrl.length > 0) {
            //     this.prop_icon_bg.visible = true;
            //     yalla.File.downloadImgByUrl(propUrl, (url) => {
            //         yalla.File.groupLoad(url, Laya.Handler.create(this, (e) => {
            //             if (!!e) {
            //                 this.propIcon.skin = url;
            //             }
            //         }));
            //     }, defaultUrl)
            // }
        }
        private onClickProp(e: Event): void {
            yalla.Native.instance.propDropImgClick(yalla.Global.Account.gameId);
        }

        //游戏返回锦标赛，先获取最新锦标赛数据
        public backChampion(d: any): void {
            if (d) {
                this._backData = d;
                if (!ActivityService.instance.championshipId && Global.Account.championshipId) {
                    ActivityService.instance.championshipId = Global.Account.championshipId;
                }
                ActivityService.instance.ChampionshipOption(ChampoinEvent.CHAMPION_INFO);
            }
            ChampionAnimation.instance.qizi3Sk && ChampionAnimation.instance.qizi3Sk.play('qizi3', true);
        }
        //游戏中返回锦标赛，做出提示
        private backChampionHint(d: any): void {
            if (!d) return;
            var player = ActivityService.instance.player;
            var idx = player.idx;
            if (Global.Account.idx && Global.Account.idx > 0) idx = Global.Account.idx;
            if (d.winnerId != idx && d.winnerId != -1) {
                yalla.common.Confirm.instance.showConfirm(yalla.data.TranslationD.Champion_Lost_Game,
                    null, Laya.Handler.create(this, () => {
                        yalla.common.Confirm.instance.hideConfirm();
                    }), ['', 'Confirm'], false, false);
                this.reset();
                this._backData = null;
                yalla.common.Confirm.instance.isExit = true;
            } else {
                if (player && d.winnerId == idx && (player.level <= ActivityService.instance.MAX_LEVEL)) {//胜利、结算
                    this.onGameEvent({ option: ChampoinEvent.CHAMPION_RESULT, data: d });//移动后再获取最新的关卡详情
                }
            }
        }

        /**
         * 大厅网络变化 1连上 -1断开
         * @param code 
         */
        public connectStateChange(code: number): void {
            if (code == 1) {
                if (!this.vsAni && !this._isMoving) ActivityService.instance.ChampionshipOption(ChampoinEvent.CHAMPION_INFO);
            } else if (code == -1) {
                if (this.vsAni && !this.vsAni.matcherPlayer) {
                    this.clearVsAni();
                    yalla.Native.instance.showToast({ msg: yalla.data.TranslationD.Champion_Toast_CheckNet });
                }
            }
        }

        //1获取锦标赛信息&用户信息 2报名(大厅处理) 3放弃（领奖） 4匹配 5取消匹配    6更新匹配信息匹配成功 7胜负通知
        private onGameEvent(d: any): void {
            var [option, data, player] = [d.option, d.data, ActivityService.instance.player];
            if (!player) return;

            switch (option) {
                case ChampoinEvent.CHAMPION_INFO:

                    if (player.status == ChampionshipStatus.NO_SIGNUP || player.status == ChampionshipStatus.RESULT) this.reset();
                    this.joinBtn.mouseEnabled = this.nextBtn.mouseEnabled = true;
                    if (player.status == ChampionshipStatus.RESULT) {
                        if (!this.dayResult || !this.dayResult.displayedInStage) {
                            this.dayResult = new DayResult(() => { this.onJoin(true) }, () => { this.onCloseResult(); });
                            Laya.stage.addChild(this.dayResult);
                            ActivityService.instance.ChampionshipOption(ChampoinEvent.SIGNUP_QUIT);//自动收钱
                        }
                    } else {
                        if (this._backData && this._backData.winnerId == player.idx) {
                            if (player.level <= ActivityService.instance.MAX_LEVEL) this._playMoveLv = player.level;
                        }
                        this.updateUserInfo();
                        this.backChampionHint(this._backData);
                        this.updateAni();
                        this.updateMoney();
                        //如果是游戏中的状态，则直接进入游戏
                        if (data.matchInfo && data.matchInfo.roomid) yalla.Native.instance.event(yalla.Native.instance.Event.CHAMPIONTOGAME, [data.matchInfo]);
                    }
                    break;
                case ChampoinEvent.SIGNUP:
                    if (data.success) {
                        this.updateMoney();
                        this.updateBtn();
                        this.onNext();
                        this.updateAni();
                        this.updateHead();
                    }
                    break;
                case ChampoinEvent.SIGNUP_QUIT://退出返回锦标赛列表 & 大厅弹获得金币框
                    if (data.success) {
                        var moneyLeft = player.moneyLeft;
                        player.moneyLeft = 0;
                        yalla.Sound.playChampionSound('tournamentbouns');//获得奖池奖金

                        if (this._isMaterQuit) {
                            yalla.Native.instance.backHall(true, { to: 'championship', gameId: yalla.Global.Account.gameId }, 500, true);
                            yalla.Native.instance.showMoney({ num: moneyLeft });
                            yalla.Native.instance.mobClickEvent(buryPoint.Arena_Quit);
                        } else {
                            this.updateUserInfo();
                            this.updateAni();
                            this.updateMoney();
                            this.dayResult && this.dayResult.updateBtn();
                        }
                    }
                    break;
                case ChampoinEvent.MATCH:
                    if (data.matchInfo && data.matchInfo.roomid) yalla.Native.instance.event(yalla.Native.instance.Event.CHAMPIONTOGAME, [data.matchInfo]);
                    else {
                        if (data.success) {//TODO::这里年后处理下，不管接口返回是否成功都显示vs
                            if (!this.vsAni || !this.vsAni.displayedInStage) {
                                this.vsAni = new VsAnimation();
                                this.addChild(this.vsAni);
                            }
                            this.vsAni.playAni();
                        } else {
                            yalla.Native.instance.mobClickEvent(buryPoint.Arena_MatchFailure);
                        }
                    }
                    break;
                case ChampoinEvent.MATCH_CANCEL:
                    if (data.success) {
                        this.joinBtn.mouseEnabled = this.nextBtn.mouseEnabled = true;
                        this.clearVsAni();
                        this.updateBtn();
                        Laya.timer.clearAll(this);
                        yalla.Native.instance.mobClickEvent(buryPoint.Arena_ExitMatch);
                    }
                    break;
                case ChampoinEvent.MATCH_SUCCESS:
                    if (this.vsAni) {
                        this.updateBtn();
                        this.joinBtn.mouseEnabled = this.nextBtn.mouseEnabled = true;
                        this.vsAni.playMatchSuc(ActivityService.instance.matchPlayer, Laya.Handler.create(this, () => {
                            yalla.Native.instance.event(yalla.Native.instance.Event.CHAMPIONTOGAME, [data]);
                        }));
                    } else {
                        yalla.Native.instance.event(yalla.Native.instance.Event.CHAMPIONTOGAME, [data]);
                    }
                    yalla.Native.instance.mobClickEvent(buryPoint.Arena_MatchSuccess);
                    break;
                case ChampoinEvent.CHAMPION_RESULT://游戏胜负通知
                    this.joinBtn.mouseEnabled = this.nextBtn.mouseEnabled = true;
                    if (data.winnerId == player.idx) {
                        if (player.level <= ActivityService.instance.MAX_LEVEL) {
                            this._playMoveLv = player.level;//终点等级
                            this.playMove();
                        }
                    }
                    this._backData = null;
                    break;
            }
            if (data.errorCode == yalla.data.ErrorCode.MATCH_GAME_FAILD_OUTTIME) {
                this.clearVsAni();
            }
        }

        /**
         * 动画加载完成啦 终极关卡 漂浮的旗子&光点
         */
        private completeAnimation(): void {
            var [x, y] = [this.item_6.x + this.item_6.width / 2, this.item_6.y - 50];
            this.bannerBox.addChild(ChampionAnimation.instance.qizi3Sk);
            ChampionAnimation.instance.qizi3Sk.play('qizi3', true);
            ChampionAnimation.instance.qizi3Sk.pos(x - 110, y - 36).scale(0.5, 0.5);

            ChampionAnimation.instance.qiziStarSk.blendMode = 'lighter';
            ChampionAnimation.instance.qiziStarSk.play('guangdian', true);
            ChampionAnimation.instance.qiziStarSk.pos(x + 4, y);
            this.mapSp.addChild(ChampionAnimation.instance.qiziStarSk);
            this.updateBtn();//TODO::避免 ChampoinEvent.CHAMPION_INFO 已经获取，但是动画还未加载完成的bug
        }

        public updateHead(): void {
            var player = ActivityService.instance.player;
            var lv = this._playMoveLv > 1 ? this._playMoveLv - 1 : player.level;
            var stayPoint = ActivityService.instance.STAY_POINT[lv];
            if (!this.head || !this.head.displayedInStage) {
                this.head = new yalla.activity.HeadLevel();
                this.head.src = player.headUrl;
                this.head.zOrder = 100;
                this.mapSp.addChild(this.head);
            }
            this._curItem = this['item_' + player.level];
            if (lv > 0) {
                this.head.side = ActivityService.instance.getSide(lv);
                this.head.pos(stayPoint[0], stayPoint[1]);
            }
            this.head.visible = player.status != ChampionshipStatus.NO_SIGNUP;
        }

        public updateUserInfo(): void {
            this.updateHead();
            //TODO::可能锦标赛info协议已经返回，但是动画还未加载完成，那么报名不能显示
            if (ChampionAnimation.instance.qiziStarSk) this.updateBtn();
        }

        public updateAni(): void {
            var player = ActivityService.instance.player;
            var levelRoles: Array<any> = ActivityService.instance.championInfo.levelInfoList;
            for (var k in levelRoles) {
                var d = levelRoles[k];
                this['txt_' + d.level].text = yalla.util.formatNum(d.num);
            }
            this.ruleBtn.visible = true;
            if (player && player.status != ChampionshipStatus.RESULT && player.status != ChampionshipStatus.NO_SIGNUP) {
                if (this._playMoveLv < 1) {
                    this.playJianVsAni(player.level, player.status);
                    this.playLevelAni(player.level, 'move', true);
                }
            }
        }
        private updateMoney(): void {
            this.moneyTxt.text = yalla.util.filterNum(ActivityService.instance.player.moneyLeft);
            this.rewardTxt.text = yalla.util.formatNum(ActivityService.instance.championInfo.bonusPool);

            this.curTxt.text = yalla.data.TranslationD.Champion_CurGold;
            if (yalla.Font.isRight()) {
                var v1 = yalla.Font.lan == 'urdu' ? 20 : 36;
                var v2 = yalla.Font.lan == 'urdu' ? 36 : 16;
                this.moneyTxt.x = (290 - (this.curTxt.width + this.moneyTxt.width + v1)) / 2;
                this.curTxt.x = this.moneyTxt.x + this.moneyTxt.width + v2;
            } else {
                this.curTxt.x = (290 - (this.curTxt.width + this.moneyTxt.width + 32)) / 2;
                this.moneyTxt.x = this.curTxt.x + this.curTxt.width + 32;
            }
        }
        /**
         * 首次进入，第一关就是报名+匹配   后续就是匹配
         * status 0可报名 1可匹配  2匹配中  3游戏中 4获胜
         */
        public updateBtn(): void {
            var player = ActivityService.instance.player;
            if (player && player.idx) {
                if (player.status == ChampionshipStatus.NO_SIGNUP) {
                    this.joinBtn.visible = true;
                    this.nextBtn.visible = this.quitBtn.visible = false;
                    this.costTxt.text = yalla.util.filterNum(ActivityService.instance.championConfig.payMoney);
                } else if (player.status == ChampionshipStatus.RESULT) {
                    this.joinBtn.visible = true;
                    this.nextBtn.visible = this.quitBtn.visible = false;
                } else {
                    this.joinBtn.visible = false;
                    this.nextBtn.visible = this.quitBtn.visible = true;
                    if (ActivityService.instance.isBusy) this.quitBtn.visible = false;//避免重新回到锦标赛，快速点击退出按钮
                }
                if (this.joinBtn.visible) {
                    if (player.gameRoleLevel < ActivityService.instance.championConfig.minLevel) {
                        this.costBox.visible = false;
                        this.joinTxt.height = 86;
                        this.joinTxt.fontSize = 36;
                        this.joinTxt.text = yalla.data.TranslationD.Champion_Unlock_Lv;
                        this.joinBtn.skin = yalla.getActivity('btn_gray_big');
                        this.joinBtn.gray = true;
                        this.joinBtn.mouseEnabled = false;
                    } else {
                        this.joinTxt.height = 40;
                        this.joinTxt.fontSize = 40;
                        this.joinTxt.text = yalla.data.TranslationD.Champion_Battle;
                        this.costBox.visible = true;
                        this.joinBtn.skin = yalla.getActivity('btn_y');
                    }
                }
            }
        }

        private onJoin(isAgainJoin: boolean = false): void {
            var state = ActivityService.instance.checkJoin();
            if (!state || this._isMoving || this._isSendClose) return;

            this._isSendMatchCD = true;
            Laya.timer.once(500, this, () => {
                this._isSendMatchCD = false;
            });

            ActivityService.instance.ChampionshipOption(ChampoinEvent.SIGNUP);
            this.joinBtn.mouseEnabled = false;
            Laya.timer.once(6000, this, () => { this.joinBtn.mouseEnabled = true; });
            this.onCloseResult();
            if (isAgainJoin) {
                yalla.Native.instance.mobClickEvent(buryPoint.Arena_SignAgain);
            } else {
                yalla.Native.instance.mobClickEvent(buryPoint.Arena_ConfirmSign);
            }
        }
        /**
         * vs 动画
         * 发起匹配 (考虑匹配超时)
         */
        private onNext(): void {
            var state = ActivityService.instance.checkNext();
            if (!state || this._isMoving || this._isSendClose) return;
            if (!this._isSendMatchCD) {
                this._isSendMatchCD = true;
                Laya.timer.once(500, this, () => {
                    this._isSendMatchCD = false;
                });
            }
            ActivityService.instance.ChampionshipOption(ChampoinEvent.MATCH);
            this.nextBtn.mouseEnabled = false;
            Laya.timer.once(6000, this, () => { this.nextBtn.mouseEnabled = true; });
        }

        /**
         * 开始进入下一关 & 停止当前关卡的动效
         */
        private playMove(): void {
            var prevLevel = this._playMoveLv - 1;
            if (prevLevel < 1 || this._isMoving) return;
            this.stopLevelAni();

            Laya.timer.once(500, this, () => {
                yalla.Sound.playChampionSound('next_round');
            });
            if (Global.isFouce) {
                this._isMoving = true;
                if (!this._timeLine) this._timeLine = new Laya.TimeLine();
                // for (var i = 1; i < addLevel + 1; i++) {
                //     lineList = lineList.concat(ActivityService.instance.LEVEL_POINT[curLevel + i]);
                // }
                var lastLineList = ActivityService.instance.LEVEL_POINT[prevLevel];
                var lineList = ActivityService.instance.LEVEL_POINT[this._playMoveLv];
                var [preLen, len] = [lastLineList.length, lineList.length];
                var [curx, cury] = [lastLineList[preLen - 1][0], lastLineList[preLen - 1][1]];

                this.head.side = ActivityService.instance.HEAD_SIDE[this._playMoveLv];
                this._timeLine.addLabel("begin", 0).to(this.head, { x: curx, y: cury }, 260);
                for (var i = 0; i < len; i++) {
                    var [x, y, lv] = [lineList[i][0], lineList[i][1], lineList[i][2]];
                    var moveTime = yalla.util.getDistanceNum({ x: curx, y: cury }, { x: x, y: y }) * 2.8;
                    this._timeLine.addLabel('move_' + lv, 0).to(this.head, { x: x, y: y }, moveTime);
                    [curx, cury] = [x, y];
                }
                var nextStayPoint = ActivityService.instance.STAY_POINT[this._playMoveLv];
                var nextTime = 360;//this._playMoveLv == ActivityService.instance.MAX_LEVEL ? 600 : 360;
                this._timeLine.addLabel("end", 0).to(this.head, { x: nextStayPoint[0], y: nextStayPoint[1] }, nextTime);
                this._timeLine.play('begin', false);

                this._timeLine.on(Laya.Event.LABEL, this, this.onLabel);
                this._timeLine.once(Laya.Event.COMPLETE, this, () => { this.arrive(); });
                Laya.timer.once(2000, this, this.onLabel);//TODO::如果timeLine的Label事件未触发，需容错处理
            } else {
                this.arrive();
            }
        }

        private onLabel(label) {
            if (!label) return;
            if (label.indexOf('move_') > -1) {
                Laya.timer.clear(this, this.onLabel);

                var lv = label.split('_')[1];
                if (lv > 0) {
                    if (lv == ActivityService.instance.MAX_LEVEL) this.head.side = 1;//6级 跟移动结束停留的头像指向相反，特殊处理
                    else this.head.side = ActivityService.instance.getSide(lv);
                }

            } else if (label == 'end') {
                if (this._playMoveLv > 0)
                    this.head.side = ActivityService.instance.getSide(this._playMoveLv);
            }
        }

        private arrive(): void {
            if (this._timeLine) {
                this._timeLine.pause();
                this._timeLine.offAll();
            }
            Laya.timer.clear(this, this.onLabel);

            var lv = this._playMoveLv;
            if (lv > 0) {
                this._curItem = this['item_' + lv];
                var stayPoint = ActivityService.instance.STAY_POINT[lv];
                this.head.side = ActivityService.instance.getSide(lv);
                this.head.pos(stayPoint[0], stayPoint[1]);
                this.playBaodian(lv);
            }
        }

        /**
         * 游戏胜利，进入下一关卡的爆点动效 & 关卡上下浮动效果  & 重新请求界面
         */
        private playBaodian(level: number) {
            this.playLevelAni(level, 'scale');

            if (this._sk) {
                this._sk.play('baodian', false);
                this._sk.pos(this._curItem.x + this._curItem.width / 2, this._curItem.y);
                if (level == ActivityService.instance.MAX_LEVEL) this._sk.scale(1.5, 1.5);
                if (this._sk && !this._sk.parent) this.mapSp.addChild(this._sk);
                this._sk.on(Laya.Event.STOPPED, this, () => {
                    this.endMove(level);
                    Laya.timer.clear(this, this.endMove);
                });
            }
            ActivityService.instance.player && this.playJianVsAni(level, ActivityService.instance.player.status);
            Laya.timer.once(3000, this, this.endMove, [level]);
        }

        private endMove(level: number): void {
            this.playLevelAni(level, 'move');
            this._playMoveLv = 0;
            this._isMoving = false;
            this._backData = null;
            this._sk && this._sk.offAll();
            this._sk && this._sk.removeSelf();
            this._sk && this._sk.destroy(true);
            this._sk = null;
        }

        /**
         * 2-5级
         */
        private playJianVsAni(lv: number, status: number): void {
            if (this._jianSk && this._curItem) {
                if (lv > 0 && lv <= ActivityService.instance.MAX_LEVEL && status != ChampionshipStatus.RESULT && status != ChampionshipStatus.NO_SIGNUP) {
                    this._jianSk.play('jian', true);//刀对杀动效
                    this._jianSk.pos(this._curItem.x + this._curItem.width / 2, this._curItem.y - 14);
                    !this._jianSk.parent && this.mapSp.addChild(this._jianSk);
                } else {
                    this._jianSk.removeSelf();
                }
            }
        }
        /**
         * 播放到达地动效 放大 上下浮动
         * @param level 
         */
        private playLevelAni(level: number, aniName: String, isLoop: boolean = false): void {
            if (this._curLevelItem && this._curLevelItem.level != level) {
                this._curLevelItem.stopAni();
                this._curLevelItem.level = level;
            }

            if (level > 1 && level < ActivityService.instance.MAX_LEVEL) {
                if (ActivityService.instance.player && ActivityService.instance.player.status != ChampionshipStatus.RESULT) {
                    if (!this._curLevelItem) this._curLevelItem = new LevelItem(this, level);
                    this._curLevelItem.playAni(aniName);
                }
            }
        }
        /**
         * 进入下一关需要把前一关的动效停止
         * @param level 
         */
        private stopLevelAni(): void {
            this._sk && this._sk.stop();
            this._sk && this._sk.offAll();
            this._sk && this._sk.removeSelf();
            this._jianSk && this._jianSk.stop();
            this._jianSk && this._jianSk.removeSelf();
            this._curLevelItem && this._curLevelItem.stopAni();
        }
        /**
         * 查看规则
         */
        private onRules(): void {
            if (!this.dayRule) this.dayRule = new DayRule();
            this.dayRule.initEvent();
            Laya.stage.addChild(this.dayRule);
            yalla.Native.instance.mobClickEvent(buryPoint.Arena_Rules);
        }
        /**
         * 关闭结算
         */
        private onCloseResult(): void {
            this.dayResult && this.dayResult.clear();
            this.dayResult = null;
        }

        private onQuit(e: Laya.Event): void {
            yalla.Sound.playChampionSound('quit');
            e.stopPropagation();
            yalla.common.Confirm.instance.showConfirm(yalla.data.TranslationD.Champion_Quit_Game,
                Laya.Handler.create(this, () => {//拿钱退出锦标赛
                    this._isMaterQuit = true;
                    ActivityService.instance.ChampionshipOption(ChampoinEvent.SIGNUP_QUIT);
                    yalla.common.Confirm.instance.hideConfirm();
                }),

                Laya.Handler.create(this, () => {
                    yalla.common.Confirm.instance.hideConfirm();
                }), [yalla.data.TranslationD.Game_Btn_Quit, yalla.data.TranslationD.Game_Btn_Cancel], false, false);
        }

        // private _tempLv:number = 1;
        private onClose(): void {//暂时离去
            if (this._isSendMatchCD) return;
            this._isSendClose = true;
            this._playMoveLv = 0;//重新进入可能存在playMoveLv残留数据，导致头像显示位置错误
            this._isMoving = false;
            yalla.Native.instance.backHall(true, { to: 'championship', gameId: yalla.Global.Account.gameId }, 500, true);
            // this._tempLv += 1;
            // this._playMoveLv = this._tempLv;
            // ActivityService.instance.player.level = this._tempLv;
            // this.playMove();
            // if(this._tempLv >= 6) this._tempLv = 1;
            // this.clear();
        }

        /**
         * 关闭规则
         * 未匹配成功，取消匹配
         * 匹配成功，返回无效
         * 关卡界面，返回大厅
         */
        public onBackPressed(): void {
            var status = ActivityService.instance.player ? ActivityService.instance.player.status : 0;
            if (this.dayResult && this.dayResult.displayedInStage) {
                this.dayResult.onBackPressed();
            } else {
                if (this.dayRule && this.dayRule.displayedInStage) {
                    this.dayRule.close();
                } else if (this._isSendMatchCD || (this.vsAni && this.vsAni.displayedInStage && status == ChampionshipStatus.MATCH)) {
                    this.vsAni && (this.vsAni.onClose());
                } else if ((status != ChampionshipStatus.GAME_ING && status != ChampionshipStatus.MATCH_ING)) {
                    this.onClose();
                }
            }
        }

        /**
         * 唤醒
         */
        public show(): void {
            if (this._isMoving) {//执行playMove了，但是中间的动画中断了
                this._isMoving = false;
                this.arrive();
            } else {
                if (this._curLevelItem && this._curLevelItem.level > 0) this._curLevelItem.playMove();
            }
            ChampionAnimation.instance.qizi3Sk && ChampionAnimation.instance.qizi3Sk.play('qizi3', true);
            this.vsAni && this.vsAni.showAni();
            this.dayResult && this.dayResult.showAni();
            var player = ActivityService.instance.player;
            if (player && player.level) this.playJianVsAni(player.level, player.status);
            this._isSendMatchCD = false;
        }

        /**
         * 休眠
         */
        public hide(): void {
            this._sk && this._sk.stop();
            this._sk && this._sk.offAll();
            this._sk && this._sk.removeSelf();

            this._jianSk && this._jianSk.stop();
            this._curLevelItem && this._curLevelItem.stopAni();
            this.vsAni && this.vsAni.hideAni();
            ChampionAnimation.instance.qizi3Sk && ChampionAnimation.instance.qizi3Sk.stop();
        }

        private reset(): void {
            this.stopLevelAni();
            this._playMoveLv = 0;
            this._isMoving = false;
            this._isMaterQuit = false;
            this._isSendMatchCD = false;
            if (this._timeLine) {
                this._timeLine.pause();
                this._timeLine.offAll();
                this._timeLine.destroy();
                this._timeLine = null;
            }
            this._curLevelItem && (this._curLevelItem.level = 1);
            Laya.timer.clearAll(this);
        }

        public clearVsAni(): void {
            var player = ActivityService.instance.player;
            player && (player.status = ChampionshipStatus.MATCH);//TODO::避免vs关闭后，点击next 不可操作
            if (this.vsAni) {
                this.vsAni.clear();
                this.vsAni = null;
            }
            //vs 匹配关闭后，就重置是否匹配cd中的状态
            this._isSendMatchCD = false;
        }

        public changeToGame(): void {
            this.clearVsAni();
            this.hide();
        }

        private removeEvent(): void {
            // this.reset();
            if (this._timeLine) {
                this._timeLine.pause();
                this._timeLine.offAll();
                this._timeLine.destroy();
                this._timeLine = null;
            }
            Laya.timer.clearAll(this);

            this.joinBtn.off(Laya.Event.CLICK, this, this.onJoin);
            this.nextBtn.off(Laya.Event.CLICK, this, this.onNext);
            this.closeBtn.off(Laya.Event.CLICK, this, this.onClose);
            this.quitBtn.off(Laya.Event.CLICK, this, this.onQuit);
            this.ruleBtn.off(Laya.Event.CLICK, this, this.onRules);
            this.prop_icon_bg.off(Laya.Event.CLICK, this, this.onClickProp);//打开道具
            ChampionAnimation.instance.off(ChampionAnimation.instance.Event.COMPLETE, this, this.completeAnimation);
            ActivityService.instance.off(ActivityService.instance.Event.GAME_EVENT, this, this.onGameEvent);
            // yalla.Native.instance.off(yalla.Native.instance.Event.ONBACKPRESSED, this, this.onBackPressed);
        }

        public clear(isRemove: boolean = false): void {
            this.removeEvent();
            this._playMoveLv = 0;
            this._isMoving = false;
            this._isMaterQuit = false;
            this._isSendMatchCD = false;
            this._isSendClose = false;
            for (var i = 2; i < 6; i++) {
                this['move' + i].stop();
                this['move' + i].removeSelf();
                this['move' + i].destroy(true);
                this['scale' + i].stop();
                this['scale' + i].removeSelf();
                this['scale' + i].destroy(true);
            }
            if (this._sk) {
                this._sk.removeSelf();
                this._sk.destroy();
                this._sk = null;
            }
            if (this._jianSk) {
                this._jianSk.removeSelf();
                this._jianSk.destroy();
                this._jianSk = null;
            }
            if (this._templet) {
                this._templet.offAll();
                this._templet.destroy();
                this._templet = null;
            }
            this.head && this.head.clear();
            if (this.vsAni) {
                this.vsAni.clear();
                this.vsAni = null;
            }
            this.onCloseResult();
            this.dayRule && this.dayRule.clear();
            this.dayRule = null;
            this._backData = null;
            this._curItem = null;
            this._isMaterQuit = false;
            this.removeSelf();
            this.destroy(true);
        }
    }
}