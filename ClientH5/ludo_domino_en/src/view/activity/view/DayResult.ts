module yalla.activity {
    export class DayResult extends ui.publics.activity.dayResultUI {
        private _bannerTemplet: Laya.Templet;
        private _bannerSk: Laya.Skeleton;

        private _baoqianTemplet: Laya.Templet;
        private _baoqianSk: Laya.Skeleton;
        private _baoqianSaoGuangSk: Laya.Skeleton;

        private _head: HeadResult;
        private _onJoinFun: Function;
        private _onCloseFunc: Function;
        private _exitCurrency: number = 0;

        constructor(onJoinFun: Function, onCloseFunc: Function) {
            super();
            this._onJoinFun = onJoinFun;
            this._onCloseFunc = onCloseFunc;
            this.initUI();
            this.initEvent();
        }

        private initUI(): void {
            if(!this._head){
                this._head = new HeadResult();
                this._head.pos(300, 300);
                this._head.visible = false;
                this.box.addChildAt(this._head, 3);
            }

            this.initSK();
            yalla.Sound.playChampionSound('win_pool');
            this.closeBtn.visible = this.againBtn.visible = false;
            var player = yalla.data.ActivityService.instance.player;
            if (player && player.exitCurrency) {
                this._exitCurrency = player.exitCurrency;
                this.costTxt.text = yalla.util.filterNum(yalla.data.ActivityService.instance.championConfig.payMoney);
            }
        }

        private initEvent(): void {
            this.closeBtn.on(Laya.Event.CLICK, this, this.onClickClose);
            this.againBtn.on(Laya.Event.CLICK, this, this.onClickAgain);
        }

        public updateBtn(): void {
            this.closeBtn.visible = this.againBtn.visible = true;
        }

        //等爆点动画加载完成后再次加载banner动画，确保后续播放爆点动画是存在的
        private initSK() {
            if (!this._baoqianTemplet) {
                this._baoqianTemplet = new Laya.Templet();
                this._baoqianTemplet.on(Laya.Event.COMPLETE, this, this.baoqianSkComplete);
                this._baoqianTemplet.loadAni(yalla.getSkeleton('dayChampionResult/baodian/skeleton'));
            }
        }
        //爆点动画加载成功后，再加载旗帜动效
        private baoqianSkComplete() {
            !this._baoqianSk && (this._baoqianSk = this._baoqianTemplet.buildArmature(1));
            this._baoqianSk.visible = false;
            this._baoqianSk.stop();

            if (!this._bannerTemplet) {
                this._bannerTemplet = new Laya.Templet();
                this._bannerTemplet.on(Laya.Event.COMPLETE, this, this.bannerSkComplete);
                this._bannerTemplet.loadAni(yalla.getSkeleton('dayChampionResult/banner/skeleton'));
            }
        }
        //旗帜动效播放
        private bannerSkComplete() {
            if (!this._bannerSk) {
                this._bannerSk = this._bannerTemplet.buildArmature(1);
                this._bannerSk.pos(this.bannerBox.width / 2, this.bannerBox.height / 2 - 100);
                this._bannerSk.on(Laya.Event.LABEL, this, this.onBannerLabel);
                this._bannerSk.on(Laya.Event.STOPPED, this, () => {
                    this.onCompleteBanner();
                    this._bannerSk.offAll();
                });
                this.bannerBox.addChild(this._bannerSk);
            }
            this._bannerSk.play('chuxian', false);
        }

        /**
         * 出现头像
         * @param e 
         */
        private _isFirst = true;
        private onBannerLabel(e: any): void {
            if (this._isFirst) {
                this._isFirst = false;

                this.playHeadAni(yalla.data.ActivityService.instance.player, Laya.Handler.create(this, () => {
                    this.playSaoguang();
                }));
                if (this._baoqianSk) {
                    this._baoqianSk.pos(this._head.x + this._head.width / 2 - 4, this._head.y + this._head.height / 2 - 20);
                    this._baoqianSk.visible = true;
                    this._baoqianSk.blendMode = 'lighter';
                    this._baoqianSk.play('baodian', false);
                    this.box.addChild(this._baoqianSk);
                }
                this.rewardTxt.text = yalla.util.filterNum(this._exitCurrency);
            }
        }
        //旗帜动效出现播放完成后，播放xunhuan
        private onCompleteBanner(e: any = null): void {
            this._bannerSk.play('xuanhuan', true);
        }

        /**
         * 1.爆点结束出现皇冠   2.再扫光   3.出现文字
         */
        private playSaoguang(): void {
            this.crown.visible = true;
            Laya.Tween.to(this.crown, { y: this.crown.y + 82 }, 200, Laya.Ease.cubicOut, Laya.Handler.create(this, () => {
                this._baoqianSaoGuangSk = this._baoqianTemplet.buildArmature(1);
                this._baoqianSaoGuangSk.play("hgsaoguang", false);
                this._baoqianSaoGuangSk.pos(this._head.x + this._head.width / 2, this.crown.y + this.crown.height / 2);
                this.box.addChild(this._baoqianSaoGuangSk);

                this.contentBox.alpha = 0;
                Laya.Tween.to(this.contentBox, { alpha: 1 }, 800, null, null, 600);
            }));
        }

        /**
         * headUrl 
         * @param d 
         */
        private playHeadAni(d: any, callBack: Laya.Handler = null): void {
            if(this._head){
                (d && d.headUrl) && (this._head.src = d.headUrl);
                this._head.visible = true;
                this._head.playResultRoleAni(callBack);
            }
        }

        public showAni(): void {
            this._head && this._head.updateResultUI();
        }

        private onClickClose(): void {
            this._onCloseFunc && this._onCloseFunc();
            // yalla.Native.instance.mobClickEvent(buryPoint.Arena_OK);
        }

        private onClickAgain(): void {
            this._onJoinFun && this._onJoinFun();
            // yalla.Native.instance.shortScreenNative();
            // yalla.Native.instance.on(yalla.Native.instance.Event.SHARE, this, this.backHall);
        }

        private removeEvent(): void {
            this.closeBtn.off(Laya.Event.CLICK, this, this.onClickClose);
            this.againBtn.off(Laya.Event.CLICK, this, this.onClickAgain);
            // yalla.Native.instance.off(yalla.Native.instance.Event.SHARE, this, this.backHall);
            if (this._bannerSk) {
                this._bannerSk.stop();
                this._bannerSk.offAll();
            }
            if (this._baoqianSk) {
                this._baoqianSk.stop();
                this._baoqianSk.offAll();
            }
            if (this._baoqianSaoGuangSk) {
                this._baoqianSaoGuangSk.stop();
                this._baoqianSaoGuangSk.offAll();
            }
            Laya.timer.clearAll(this);
        }

        public onBackPressed(): void {
            if (this.closeBtn.visible) {
                this.onClickClose();
            }
        }

        public backHall(): void {
            this.removeEvent();
            yalla.Native.instance.backHall(true, { to: 'championship', gameId: yalla.Global.Account.gameId }, 300, true);
        }

        public clear(): void {
            this._isFirst = true;
            this.removeEvent();
            if(this._bannerSk){
                this._bannerSk.stop();
                this._bannerSk.removeSelf();
                this._bannerSk.destroy();
            }
            if(this._baoqianSk){
                this._baoqianSk.stop();
                this._baoqianSk.removeSelf();
                this._baoqianSk.destroy();
            }
            if(this._baoqianSaoGuangSk){
                this._baoqianSaoGuangSk.stop();
                this._baoqianSaoGuangSk.removeSelf();
                this._baoqianSaoGuangSk.destroy(true);
            }
            if (this._bannerTemplet) {
                this._bannerTemplet.offAll();
                this._bannerTemplet.destroy();
                this._bannerTemplet = null;
            }
            if (this._baoqianTemplet) {
                this._baoqianTemplet.offAll();
                this._baoqianTemplet.destroy();
                this._baoqianTemplet = null;
            }
            this._head && this._head.clear();
            this.removeSelf();
            this.destroy(true);
        }
    }
}