module yalla.activity {
    /**
     * 日锦赛
     */
    export class DayRule extends ui.publics.activity.dayRuleUI {
        constructor() {
            super();
            this.initUI();
            this.initEvent();
        }

        public initEvent():void{
            this.bg.once(Laya.Event.CLICK, this, this.close);
            this.closeBtn.once(Laya.Event.CLICK, this, this.close);
            // this.confirmBtn.once(Laya.Event.CLICK, this, this.close);
        }

        public initUI(): void {

            this.panel.vScrollBarSkin = "";
            this.panel.vScrollBar.isVertical = true;
            this.panel.vScrollBar.elasticBackTime = 300;
            this.panel.vScrollBar.elasticDistance = 200;
            this.content_1.text = yalla.data.TranslationD.Champion_Rule_1;
            // this.content_2.text = yalla.data.TranslationD.Champion_Rule_2;
            this.img_rule.skin = yalla.getActivity("img_rule_" + yalla.Font.lan);
            this.img_win.skin = yalla.getActivity("img_win_" + yalla.Font.lan);

            if (yalla.Font.lan == 'ar') {
                // this.content_2.width = 820;
                // this.content_2.right = 120;
                this.content_1.width = 1100;
                this.content_1.right = 120;
                this.content_1.align /*= this.content_2.align */= 'right';
                this.closeBtn.x = -20;
            }
            // else if (yalla.Font.lan == 'urdu') {
            //     this.content_1.width = this.content_2.width = 690;
            // }
            else {
                this.content_1.width/* = this.content_2.width*/ = 646;
                this.closeBtn.x = 603;
            }
            if (yalla.data.ActivityService.instance.championConfig) {
                    var payMoney =yalla.data.ActivityService.instance.championConfig.payMoney;
                    var levelMoneys = yalla.data.ActivityService.instance.championConfig.levelConfigList;
                    if(levelMoneys) var maxLevel = levelMoneys.length;
                    var money = payMoney;
                    for (var k in levelMoneys) {
                        var d = levelMoneys[k];
                        money += d.winMoney;
                        if (d.level == maxLevel) {
                            var perStr = yalla.Font.lan == 'ar' ? ' + %25 ' : ' + 25% ';
                            this['txt_' + d.level].text = yalla.util.formatNum(money) + perStr + yalla.util.langTranslation(yalla.data.TranslationD.Champion_Rule_PrizePool);
                        }else{
                            this['txt_' + d.level].text = yalla.util.formatNum(money);
                        }
                    }
                }

            // this.contentBg.height = 6;
            // var arr = [];
            // var gameId = yalla.data.ActivityService.instance.championConfig.gameId;
            // if (gameId) {
            //     arr = [0, 1, 2, 3, 4];
            //     // var gameType = yalla.data.ActivityService.instance.championConfig.gameType;
            //     // switch (gameId) {
            //     //     case GameType.LUDO:
            //     //         // if(gameType == 0) arr = [0, 2, 3, 7, 8];
            //     //         // else if(gameType == 2) arr = [0, 2, 4, 7, 8];
            //     //         break;

            //     //     case GameType.DOMINO:
            //     //         // if(gameType == 0) arr = [1, 2, 5, 7, 8];
            //     //         // else if(gameType == 1) arr = [1, 2, 6, 7, 8];
            //     //         break;
            //     // }
            // }
            // var len = arr.length;
            // var hei: number = 0;
            // for (var i = 0; i < len; i++) {
            //     var ruleItemData = yalla.data.ActivityService.instance.RULE[arr[i]];
            //     if (ruleItemData.title) {//title
            //         this.titleTxt.text = ruleItemData.title;
            //         continue;
            //     }
            //     var ruleItem = new RuleItem(ruleItemData);
            //     if(ruleItemData.explain) hei += (ruleItem.height - 2) * 2;
            //     else hei += ruleItem.height - 4;
            //     this.ruleList.addChild(ruleItem);
            // }
            // if(yalla.Font.lan == 'urdu') hei += 12;
            // else hei -= 6;
            // this.contentBg.height += hei;
        }

        private removeEvent():void{
            this.bg.off(Laya.Event.CLICK, this, this.close);
            this.closeBtn.off(Laya.Event.CLICK, this, this.close);
            // this.confirmBtn.off(Laya.Event.CLICK, this, this.close);
        }

        public close():void{
            this.removeEvent();
            this.removeSelf();
        }

        public clear():void{
            this.close();
            this.destroy(true);
        }
    }
}