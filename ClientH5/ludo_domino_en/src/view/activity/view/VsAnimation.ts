module yalla.activity {
    import ActivityService = yalla.data.ActivityService;
    /**
     * 日锦赛 匹配动画
     */
    export class VsAnimation extends ui.publics.activity.dayVsUI {
        private _isLoadRes: boolean = false;
        private _MatchSucCallBack: Laya.Handler;
        private _headHash: Object;
        public matcherPlayer: any;

        constructor() {
            super();
            this.initUI();
            this.initEvent();
        }

        private initUI(): void {
            if (yalla.Screen.screen_top_width > 0) {
                this.vsBox.autoSize = true;
                var scale = yalla.Screen.screen_top_width / Laya.stage.width;
                this.vsBox.scale(scale + 1, scale + 1);
                this.vsBox.pos(-yalla.Screen.screen_top_width / 2, -Laya.stage.height * scale / 2);
            }
            this.setCloseBtn(true);
            this.WinTxt.autoSize = this.winGoldTxt.autoSize = true;
            this.WinTxt.text = 'Win';

            var levelConfigList = ActivityService.instance.championConfig.levelConfigList;
            for (var k in levelConfigList) {
                if (levelConfigList[k].level == ActivityService.instance.player.level) {//TODO::用自己的等级
                    var num = yalla.util.filterNum(levelConfigList[k].winMoney);
                    this.winGoldTxt.text = num;
                    break;
                }
            }
            if (yalla.Font.isRight()) {
                var tempWinTxtX = this.WinTxt.x;
                this.winGoldTxt.x = tempWinTxtX;
                this.goldIcon.x = this.winGoldTxt.x + this.winGoldTxt.width + 20;
                this.WinTxt.x = this.goldIcon.x + this.goldIcon.width + 40;
            } else {
                this.WinTxt.x = 0;
                this.goldIcon.x = this.WinTxt.x + this.WinTxt.width + 50;
                this.winGoldTxt.x = this.goldIcon.x + this.goldIcon.width + 10;
            }

            this.openFrameLoop();
        }

        private initEvent(): void {
            this.btnClose.on(Laya.Event.CLICK, this, this.onClose);
        }

        /** r4 r5名字特殊处理 */
        private openFrameLoop(): void {
            Laya.timer.clear(this, this.frameLoopName);
            Laya.timer.frameLoop(1, this, this.frameLoopName);
        }
        /** 主界面名称  游戏结算名称 */
        private frameLoopName(): void {
            for (var k in this._headHash) {
                var data = this._headHash[k].data;
                var canShowRLNameAnimation = data.royalLevel >= 4;
                if (this.matcherPlayer && this.matcherPlayer.idx == data.idx) {
                    canShowRLNameAnimation = data.royalLevelNameAnimation;
                }
                if (canShowRLNameAnimation) this._headHash[k].frameLoopName();
            }
        }

        //VSdonghua VSnormal   pipei   pipeibaodian   qizi1   qizi2
        public playAni() {
            if (ChampionAnimation.instance.qizi1Sk) {
                ChampionAnimation.instance.qizi1Sk.pos(Laya.stage.width / 2, Laya.stage.height / 2);
                ChampionAnimation.instance.qizi1Sk.play('qizi1', true);
                this.vsBox.addChild(ChampionAnimation.instance.qizi1Sk);
            }
            this.titleBox.visible = true;

            if (yalla.Global.isFouce) {
                this.titleBox.alpha = 0;

                //自己头像
                Laya.timer.once(600, this, () => {
                    ChampionAnimation.instance.qizi1Sk && ChampionAnimation.instance.qizi1Sk.play('qizi2', true);
                    this.addHead(ActivityService.instance.player, [Laya.stage.width / 4, Laya.stage.height / 2]);
                    Laya.Tween.to(this.titleBox, { alpha: 1 }, 800);
                });


            } else {
                ChampionAnimation.instance.qizi1Sk && ChampionAnimation.instance.qizi1Sk.play('qizi2', true);
                this.addHead(ActivityService.instance.player, [Laya.stage.width / 4, Laya.stage.height / 2]);

                this.titleBox.alpha = 1;
            }

        }

        /**
         * headUrl 
         * @param d 
         */
        private addHead(d: any, pos: Array<number>, isMatchSuc: Boolean = false): void {
            if (!this._headHash) this._headHash = {};

            var head: HeadCost = new HeadCost();
            head.pos(pos[0] - head.width / 2, pos[1] - head.height / 2);
            head.updateData(d);
            this.vsBox.addChild(head);
            this._headHash[d.idx] = head;

            if (isMatchSuc) {
                head.playMatchingCallBack = null;//如果在动画播放时间内 快速匹配上玩家，那么匹配loading头像不展示，直接显示真实用户
                head.playMatchRoleAni();
            } else {
                head.playMatchingCallBack = Laya.Handler.create(this, this.addMatchingHead);
                head.playSelfRoleAni();
            }
        }

        /**
         * 匹配中的对方头像，播放的sk动画,loading 转圈；
         */
        private addMatchingHead(): void {
            if (ChampionAnimation.instance.pipeiSk) {
                ChampionAnimation.instance.pipeiSk.scale(0.5, 0.5);
                Laya.Tween.to(ChampionAnimation.instance.pipeiSk, { scaleX: 1.2, scaleY: 1.2 }, 300, null, Laya.Handler.create(this, () => {
                    Laya.Tween.to(ChampionAnimation.instance.pipeiSk, { scaleX: 1, scaleY: 1 }, 100);
                }));
                ChampionAnimation.instance.pipeiSk.play('pipei', true);
                ChampionAnimation.instance.pipeiSk.pos(Laya.stage.width / 2 + Laya.stage.width / 4, Laya.stage.height / 2);
                this.vsBox.addChild(ChampionAnimation.instance.pipeiSk);
            }

            //TODO::这里一定需要，如果还未loading转圈，匹配成功，需要先播放播放动画 再展示匹配到用户；callBack是进入游戏通知事件
            this._isLoadRes = true;
            if (this.matcherPlayer) this.playMatchSuc(this.matcherPlayer, this._MatchSucCallBack);
        }

        /**
         * 匹配成功,laoding 转圈头像更新为真实匹配玩家信息
         */
        private _isFirst: boolean = true;
        public playMatchSuc(d, callBack: Laya.Handler = null): void {
            this.setCloseBtn(false);
            this.matcherPlayer = d;
            this._MatchSucCallBack = callBack;

            if (!this._isLoadRes) return;
            this.addHead(d, [Laya.stage.width / 2 + Laya.stage.width / 4, Laya.stage.height / 2], true);
            if (yalla.Global.isFouce) {
                ChampionAnimation.instance.pipeiSk.play('pipeibaodian', false);
                ChampionAnimation.instance.pipeiSk.pos(Laya.stage.width / 2 + Laya.stage.width / 4, Laya.stage.height / 2 - 10);
                ChampionAnimation.instance.pipeiSk.once(Laya.Event.STOPPED, this, () => {
                    if (this._isFirst) {
                        this._isFirst = false;
                        ChampionAnimation.instance.VSnormalSk.play("VSnormal", false);
                        ChampionAnimation.instance.VSnormalSk.pos(Laya.stage.width / 2, Laya.stage.height / 2);
                        this.vsBox.addChild(ChampionAnimation.instance.VSnormalSk);

                        ChampionAnimation.instance.VSdonghuaSk.play('VSdonghua', false);
                        ChampionAnimation.instance.VSdonghuaSk.blendMode = 'lighter';
                        ChampionAnimation.instance.VSdonghuaSk.pos(Laya.stage.width / 2, Laya.stage.height / 2);
                        this.vsBox.addChild(ChampionAnimation.instance.VSdonghuaSk);

                        //进入游戏
                        ChampionAnimation.instance.VSdonghuaSk.once(Laya.Event.STOPPED, this, () => {
                            ChampionAnimation.instance.VSnormalSk.stop();
                            ChampionAnimation.instance.VSdonghuaSk.stop();
                            ChampionAnimation.instance.VSdonghuaSk.offAll();
                            callBack && callBack.run();
                            this._MatchSucCallBack = null;
                        });
                    }
                });
                if (ChampionAnimation.instance.pipeiSk) {
                    ChampionAnimation.instance.pipeiSk.stop();
                    ChampionAnimation.instance.pipeiSk.removeSelf();
                }

            } else {
                this.hideAni();
            }
        }

        public showAni(): void {
            if (!this.matcherPlayer && this._headHash) {
                for (var key in this._headHash) {
                    var head = this._headHash[key];
                    head.updateUI();
                }
            }
        }

        /**
         *  切换到后台，如果已经匹配到玩家，则自动进入游戏，不播放动画 
         */
        public hideAni(): void {
            if (this.matcherPlayer) {
                if (ChampionAnimation.instance.VSnormalSk) {
                    ChampionAnimation.instance.VSnormalSk.stop();
                    ChampionAnimation.instance.VSdonghuaSk.stop();
                    ChampionAnimation.instance.VSdonghuaSk.offAll();
                }
                if (ChampionAnimation.instance.pipeiSk) {
                    ChampionAnimation.instance.pipeiSk.stop();
                    ChampionAnimation.instance.pipeiSk.removeSelf();
                }
                this._MatchSucCallBack && this._MatchSucCallBack.run();
                this._MatchSucCallBack = null;
            } else {
                this.showAni();
            }
        }

        public setCloseBtn(v: boolean): void {
            this.btnClose.visible = v;
        }

        public onClose(): void {
            if (!ActivityService.instance.isBusy) {
                this.btnClose.mouseEnabled = false;
                ActivityService.instance.ChampionshipOption(yalla.data.ChampoinEvent.MATCH_CANCEL);
                Laya.timer.once(1000, this, () => { this.btnClose.mouseEnabled = true; });
            }
        }

        private removeEvent(): void {
            if (ChampionAnimation.instance.pipeiSk) {
                ChampionAnimation.instance.pipeiSk.stop();
                ChampionAnimation.instance.pipeiSk.offAll();
                ChampionAnimation.instance.pipeiSk.removeSelf();
            }

            if (ChampionAnimation.instance.qizi1Sk) {
                ChampionAnimation.instance.qizi1Sk.stop();
                ChampionAnimation.instance.qizi1Sk.offAll();
                ChampionAnimation.instance.qizi1Sk.removeSelf();
            }

            if (ChampionAnimation.instance.VSnormalSk) {
                ChampionAnimation.instance.VSnormalSk.stop();
                ChampionAnimation.instance.VSnormalSk.offAll();
                ChampionAnimation.instance.VSnormalSk.removeSelf();
            }

            if (ChampionAnimation.instance.VSdonghuaSk) {
                ChampionAnimation.instance.VSdonghuaSk.stop();
                ChampionAnimation.instance.VSdonghuaSk.offAll();
                ChampionAnimation.instance.VSdonghuaSk.removeSelf();
            }
            this.btnClose.off(Laya.Event.CLICK, this, this.onClose);
            Laya.timer.clear(this, this.frameLoopName);
            Laya.timer.clearAll(this);
        }

        public resetData(): void {
            this._isLoadRes = false;
            this._isFirst = true;
        }

        public clear(): void {
            this.removeEvent();
            this.resetData();
            for (var key in this._headHash) {
                this._headHash[key].clear();
            }
            this._headHash = null;
            this.matcherPlayer = null;
            this.removeSelf();
            this.destroy(true);
        }
    }
}