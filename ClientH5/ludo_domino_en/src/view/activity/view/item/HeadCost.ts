module yalla.activity {
    export class HeadCost extends ui.publics.activity.item.head_costUI {
        private _src: string = "activity/default_head.png";
        private _timeLineSelf: Laya.TimeLine = null;
        private _timeLine: Laya.TimeLine = null;
        private _nameItem: yalla.common.NameItem;

        private _facetemplet: Laya.Templet;
        private _faceSk: Laya.Skeleton;
        private _faceId: number = -1;

        public playMatchingCallBack: Laya.Handler;//等待匹配的对手转圈头像框
        constructor() {
            super();
        }

        public playSelfRoleAni() {
            if (yalla.Global.isFouce) {
                if (!this._timeLineSelf) this._timeLineSelf = new Laya.TimeLine();
                this.nameBox.visible = false;
                this.head.scale(0.5, 0.5);
                this._timeLineSelf.addLabel("head", 0).to(this.head, { scaleX: 1.2, scaleY: 1.2 }, 200)
                this._timeLineSelf.addLabel("head1", 0).to(this.head, { scaleX: 1, scaleY: 1 }, 100)
                this._timeLineSelf.addLabel("name", 0).to(this.nameBox, { scaleX: 1.2, scaleY: 1.2 }, 100)
                this._timeLineSelf.addLabel("name1", 0).to(this.nameBox, { scaleX: 1, scaleY: 1 }, 100)
                this._timeLineSelf.play("head", false);
                this._timeLineSelf.on(Laya.Event.LABEL, this, this.onLabel);

                //TODO::避免timeLine未出发label事件
                Laya.timer.once(1000, this, () => {
                    if (!this.nameBox.visible) {
                        this.head.scale(1, 1);
                        this.nameBox.visible = true;
                    }

                    this.playMatchingCallBack && this.playMatchingCallBack.run();
                    this.playMatchingCallBack = null;
                });
            } else {

                this.playMatchingCallBack && this.playMatchingCallBack.run();
                this.playMatchingCallBack = null;
            }
        }

        public playMatchRoleAni() {
            if (yalla.Global.isFouce) {
                if (!this._timeLine) this._timeLine = new Laya.TimeLine();
                this.nameBox.scale(0, 0);
                this._timeLine.addLabel("name", 0).to(this.nameBox, { scaleX: 1.2, scaleY: 1.2 }, 100)
                this._timeLine.addLabel("name1", 0).to(this.nameBox, { scaleX: 1, scaleY: 1 }, 100)
                this._timeLine.play("name", false);

                //TODO::避免timeLine未触发label事件
                Laya.timer.once(800, this, () => {
                    yalla.Debug.log('=====playMatchRoleAni timer800=====')
                    if (this.nameBox.scaleX != 1) {
                        this.nameBox.scale(1, 1);
                        this.nameBox.visible = true;
                    }
                });
            }
        }

        showFaceBox(faceId:number) {
            if (faceId == this._faceId) return;
            this._faceId = faceId;
            yalla.Debug.log("_faceId:" + faceId);
            if (faceId > 0) {
                if (yalla.Skin.instance.isDynamic(faceId)) {
                    yalla.event.YallaEvent.instance.once(`faceframe_${faceId}.sk`, this, () => {
                        if (this.destroyed) return;
                        this.removeSk();
                        this.initSK(yalla.File.filePath + `faceframe_${faceId}.sk`);
                    })
                    yalla.event.YallaEvent.instance.once(`faceframe_${faceId}.png`, this, () => {
                        yalla.File.getFileByNative(`faceframe_${faceId}.sk`);
                    })
                    yalla.File.getFileByNative(`faceframe_${faceId}.png`);
                } else {
                    yalla.event.YallaEvent.instance.once(`face_${faceId}.png`, this, d => {
                        if (this.destroyed) return;
                        this.bg.cacheAs = 'bitmap';//TODO::webgl 进行画布缓存，后续不会再次加载（未加这行之前，匹配成功进入游戏前，静态头像框消失，渲染性能不足）
                        this.bg.scale(1.1, 1.1);
                        
                        this.bg.visible = true;
                        this.bg.skin = `${yalla.File.filePath}face_${faceId}.png`;
                    })
                    yalla.File.getFileByNative(`face_${faceId}.png`)
                }
            } else {
                this.removeSk();
                this.bg.visible = true;
                this.bg.skin = 'activity/default.png';
            }
        }

        private initSK(url: string) {
            if (this._facetemplet && this._faceSk) {
                if (this._faceId > 0) this._faceSk.play(this._faceId.toString(), true);
            } else {
                this._facetemplet = new Laya.Templet();
                this._facetemplet.on(Laya.Event.COMPLETE, this, this.faceSkComplete);
                this._facetemplet.loadAni(url);
            }
        }

        private faceSkComplete() {
            if (!this._facetemplet) return;
            this._faceSk = this._facetemplet.buildArmature(1);
            this._faceSk.pos(this.bg.x, this.bg.y);
            this.head.addChildAt(this._faceSk, 2);
            this._faceSk.scale(1.1, 1.1);
            if (this._faceId > 0) this._faceSk.play(0, true);
            this.bg.visible = false;
        }

        public removeSk(): void {
            if (this._facetemplet) {
                this._facetemplet.offAll();
                this._facetemplet.destroy();
                this._facetemplet = null;
            }
            if (this._faceSk) {
                this._faceSk.stop();
                this._faceSk.removeSelf();
                this._faceSk.destroy(true);
                this._faceSk = null;
            }
        }


        private onLabel(label) {
            if (label == 'name1') {
                this.nameBox.visible = true;
                this.nameBox.scale(0, 0);
                this.playMatchingCallBack && this.playMatchingCallBack.run();
                this.playMatchingCallBack = null;
            }
        }

        public frameLoopName(): void{
            if (this._nameItem) {
                this._nameItem.frameLoopName();
            }
        }

        public updateData(player: ChampionshipPlayerInfo) {
            this.dataSource = player;
            this.bg.skin = yalla.getActivity('default');
            this.src = player.headUrl;
            this.showFaceBox(player.faceId);

            if (player.realRoyLevel >= 4) {
                var playerShowInfo: any = {
                    fPlayerInfo: { nikeName: player.name},roylevel:player.royalLevel,realRoyLevel:player.realRoyLevel,royVisibility:player.royVisibility};
                this.nameTxt.text = '';
                this.nameTxt.align = 'left';
                if (!this._nameItem) {
                    this._nameItem = new yalla.common.NameItem(this.nameItem, this.nameTxt);
                    this._nameItem.gameName_domino(playerShowInfo, 13);
                    this.nameItem.x = Math.max(0, (this.width - this._nameItem.maskMoveDis) / 2);
                }

            } else {
                this.nameTxt.visible = true;
                this.nameTxt.align = 'center';
                yalla.Emoji.lateLable(yalla.util.filterName(player.name), this.nameTxt);
            }
        
            
            yalla.Emoji.lateLable(yalla.util.filterName(player.name), this.nameTxt);
        }
        /**
         * 匹配头像
         */
        public updateUI(): void {
            if (this._timeLineSelf) {
                this._timeLineSelf.pause();
                this._timeLineSelf.offAll();
                this.nameBox.visible = true;
                this.nameBox.scale(1, 1);
                this.head.scale(1, 1);
            }
        }
       

        get data(): ChampionshipPlayerInfo {
            return this.dataSource;
        }

        set src(value: string) {
            this._src = value;
            this.face.skin = yalla.getActivity('default_head');
            if (value && value.length > 0) {
                yalla.File.downloadImgByUrl(value, (url) => {
                    yalla.File.groupLoad(url, Laya.Handler.create(this, (e) => {
                        this.face.skin = url;
                        yalla.util.centerSetup(this.face, 136, { x: 10, y: 10 });
                    }));
                })
            }
        }
        get src(): string {
            return this._src;
        }

        public clear(): void {
            Laya.timer.clearAll(this);
            if (!!this._timeLineSelf) {
                this._timeLineSelf.pause();
                this._timeLineSelf.offAll();
                this._timeLineSelf.destroy();
            }
            if (!!this._timeLine) {
                this._timeLine.pause();
                this._timeLine.offAll();
                this._timeLine.destroy();
            }

            if (this._nameItem && this._nameItem.ui) {
                this._nameItem.ui.removeSelf();
            }
            this.removeSk();
            this.destroy(true);
        }
    }
}