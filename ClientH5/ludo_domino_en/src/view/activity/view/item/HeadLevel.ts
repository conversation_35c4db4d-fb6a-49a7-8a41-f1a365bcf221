module yalla.activity {
    export class HeadLevel extends ui.publics.activity.item.head_levelUI {
        private _src: string = "activity/default_head.png";

        constructor() {
            super();
        }

        set side(value: number) {
            // this['bg' + value].visible = true;
            // this['bg' + (1 - value)].visible = false;
            this['bg' + value] && (this['bg' + value].visible = true);
            this['bg' + (1 - value)] && (this['bg' + (1 - value)].visible = false);
        }

        set src(value: string) {
            this._src = value;
            this.face.skin = yalla.getActivity('default_head');
            if(value && value.length > 0){
                yalla.File.downloadImgByUrl(value, (url)=>{
                    this.face.skin = url;
                    yalla.util.centerSetup(this.face, 62, { x: 13.5, y: 9 });
                })
            }

            // Laya.loader.load(value, Laya.Handler.create(this, (e) => {
            //     if (!!e) {
            //         this._src = value;
            //         this.face.skin = this._src;
            //     }
            // }));
        }
        get src(): string {
            return this._src;
        }

        public clear(): void {
            this.removeSelf();
            // this.destroy();
            // this.destroy(true);
        }
    }
}