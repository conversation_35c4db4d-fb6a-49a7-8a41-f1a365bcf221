module yalla.activity {
    export class HeadResult extends ui.publics.activity.item.head_resultUI {
        private _src: string = "activity/default_head.png";
        private _timeLine: Laya.TimeLine = null;
        private _callBack: <PERSON><PERSON>.Handler;

        constructor() {
            super();
        }

        public playResultRoleAni(callBack: Laya.Handler = null) {
            this._callBack = callBack;
            if (yalla.Global.isFouce) {
                if (!this._timeLine) this._timeLine = new Laya.TimeLine();
                this.head.scale(0.5, 0.5);
                this._timeLine.addLabel("head", 0).to(this.head, { scaleX: 1.8, scaleY: 1.8 }, 280)
                this._timeLine.addLabel("head1", 0).to(this.head, { scaleX: 1, scaleY: 1 }, 180)
                this._timeLine.play("head", false);
                this._timeLine.on(Laya.Event.COMPLETE, this, () => {
                    this._callBack && this._callBack.run();
                    this._callBack = null;
                });

                //TODO::避免timeLine未出发label事件
                Laya.timer.once(600, this, () => {
                    if (this.head.scaleX != 1) this.head.scale(1, 1);
                    this._callBack && this._callBack.run();
                    this._callBack = null;
                });

            } else {
                this._callBack && this._callBack.run();
                this._callBack = null;
            }
        }

        /**
         * 结算的头像
         */
        public updateResultUI(): void {
            if (this._timeLine) {
                this._timeLine.pause();
                this._timeLine.offAll();
                this.head.scale(1, 1);
            }
        }

        get data(): ChampionshipPlayerInfo {
            return this.dataSource;
        }

        set src(value: string) {
            this._src = value;
            this.face.skin = yalla.getActivity('default_head');
            if(value && value.length > 0){
                yalla.File.downloadImgByUrl(value, (url)=>{
                    this.face.skin = url;
                    yalla.util.centerSetup(this.face, 110, 12);
                })
            }
            // Laya.loader.load(value, Laya.Handler.create(this, (e) => {
            //     if (!!e) {
            //         this._src = value;
            //         this.face.skin = this._src;
            //     }
            // }));
        }
        get src(): string {
            return this._src;
        }

        public clear(): void {
            Laya.timer.clearAll(this);
            if (!!this._timeLine) {
                this._timeLine.pause();
                this._timeLine.offAll();
                this._timeLine.destroy();
            }
            this.destroy(true);
        }
    }
}