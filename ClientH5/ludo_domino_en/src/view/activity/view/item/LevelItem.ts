module yalla.activity {
    export class LevelItem {
        private _ui: ui.publics.activity.dayChampionUI;
        public level: number;
        private _isMove:boolean = false;

        constructor(ui: ui.publics.activity.dayChampionUI, level: number) {
            this._ui = ui;
            this.level = level;
        }

        public playAni(aniName:String):void{
            if (aniName == 'scale') this.playScale();
            if (aniName == 'move') this.playMove();
        }

        public playScale(): void {
            this._ui['scale'+this.level].play(0, false);
        }

        public playMove(): void {
            if(!this._isMove){
                if(this._ui['move'+this.level]) this._ui['move'+this.level].play(0, true);
                this._isMove = true;
            }
        }

        public stopAni(): void {
            if(this._ui['move'+this.level]) this._ui['move'+this.level].gotoAndStop(0);
            this._isMove = false;
        }

        public clear(): void {
            this._isMove = false;
            if(this._ui['scale'+this.level]){
                this._ui['scale'+this.level].gotoAndStop(0);
                this._ui['scale'+this.level].removeSelf();
                this._ui['scale'+this.level].destroy();
            }
            if(this._ui['move'+this.level]){
                this._ui['move'+this.level].gotoAndStop(0);
                this._ui['move'+this.level].removeSelf();
                this._ui['move'+this.level].destroy();
            }
        }
    }
}