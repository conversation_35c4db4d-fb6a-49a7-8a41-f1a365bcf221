module yalla.activity {
    export class MaqueeItem extends ui.publics.activity.item.marquee_itemUI  {

        private _label:Laya.Label;
        private _gameTypeStr:string;
        private _txtWidth:number;

        public wordList:Array<any>;                     //跑马灯信息
        public curData:any;                             //当前播放的信息
        public dir:number = -1;                         //跑马灯方向 -1右到左   1左到右
        public speed:number = 2.5;                      //移动速度

        constructor() {
            super();
            this.init();
        }

        private init():void{
            if(yalla.Font.lan == 'en'){
                this.dir = -1;
                this._label = this.txt_en;
            }else{
                this.dir = 1;
                this._label = this.txt_ar;
            }
            this._label.autoSize = true;
        }

        //添加
        public addItem(wordList:Array<any>):void{
            if(!this.wordList) this.wordList = [];
            if(wordList && wordList.length > 0) this.wordList = this.wordList.concat(wordList);
        }
        
        //ActivityService.instance.championConfig.gameId 、gameType
        //Congratulations to {name}, he wins {nums} golds in Domino Tournament(Draw Game)!
        private initData():void{
            if(!this.wordList || this.wordList.length < 1){
                Laya.timer.clear(this, this.loopPlay);
                return;
            }

            if(!this.curData){
                this.curData = this.wordList[0];
                // var [gameId, gameType] = [this.curData.gameId, this.curData.gameType];
                // if(gameId == GameType.LUDO){
                //     this._gameTypeStr = 'Ludo Tournament';
                //     if(gameType == 0){
                //         this._gameTypeStr += '(Classic)!';
                //     }else if(gameType == 2){
                //         this._gameTypeStr += '(Quick)!';
                //     }

                // }else if(gameId == GameType.DOMINO){
                //     this._gameTypeStr = 'Domino Tournament';
                //     gameType == 0 ? (this._gameTypeStr += '(Draw Game)!') : (this._gameTypeStr += '(All Five)!');
                // }
                // this._label.text = 'Congratulations to {name}, he wins {nums} golds in ' + this._gameTypeStr;
                //TODO::表情显示有问题
                var [fontSize, arVal] = [this._label.fontSize, 0.2];
                if(yalla.Font.lan == 'urdu') arVal = 0.15;

                this._label.text = this.curData.content;
                Laya.timer.frameLoop(1, this, this.loopPlay);

                var howAbric = this._label.text.howArabic();
                var howNotAbric = 1 - howAbric;
                var totalLen = yalla.util.getBLen(this._label.text);
                var arbLen = totalLen * howAbric * fontSize * arVal;
                var otherLen = totalLen * howNotAbric * fontSize * 0.5;
                this._txtWidth = arbLen + otherLen;

                yalla.Debug.log(arbLen+'---'+ otherLen +'=======this._txtWidth========='+this._txtWidth);
                yalla.Debug.log(howAbric+'===getTextWidth:' + this._txtWidth);
            }
        }

        
        private loopPlay():void{
            
            if(this.dir == -1){
                if(this._label.x < -this._txtWidth) {
                    this._label.x = yalla.Screen.screen_top_width + Laya.stage.width;
                    this.wordList.shift();
                    this.curData = null;
                    this.initData();
                }
            }else{
                if(this._label.x > this._txtWidth * 2) 
                {
                    this._label.x = 0;
                    this.wordList.shift();
                    this.curData = null;
                    this.initData();
                }
            }
            this._label.x += this.speed * this.dir;
        }

        private reset():void{
            if(this.dir == -1){
                this._label.x = yalla.Screen.screen_top_width + Laya.stage.width;
                this.wordList.shift();
                this.curData = null;
            }else{
                this._label.x = 0;
                this.wordList.shift();
                this.curData = null;
            }
        }

        /**
         * 继续跑马灯
         */
        public play():void{
            this.initData();
        }

        /**
         * 当前有跑马灯信息，停止跑马灯，转场时需要停止（锦标赛到游戏）
         */
        public stop():void {
            Laya.timer.clear(this, this.loopPlay);
            if(this.curData) this.reset();
        }

        public clear(): void {
            Laya.timer.clearAll(this);
            this._label && this._label.removeSelf();
            this.wordList = null;
            this.curData = null;
        }
    }
}