module yalla.activity {
    export class RuleItem extends Laya.Box {
        private _txt: Laya.Label;

        constructor(d: any) {
            super();
            var y = 0;
            if (d.box) {
                var rItem = new ui.publics.activity.item.rule_itemUI();
                this.addChild(rItem);
                y += rItem.height;
                if (yalla.data.ActivityService.instance.championConfig) {
                    var payMoney =yalla.data.ActivityService.instance.championConfig.payMoney;
                    var levelMoneys = yalla.data.ActivityService.instance.championConfig.levelConfigList;
                    if(levelMoneys) var maxLevel = levelMoneys.length;
                    var money = payMoney;
                    for (var k in levelMoneys) {
                        var d = levelMoneys[k];
                        money += d.winMoney;
                        if(d.level == maxLevel){
                            rItem['txt_' + d.level].text = yalla.util.formatNum(money) + ' + 25% ' + yalla.util.langTranslation('prize pool');
                        }else{
                            rItem['txt_' + d.level].text = yalla.util.formatNum(money);
                        }
                    }
                }
            }

            this._txt = new Laya.Label(d.explain);
            this._txt.fontSize = 28;
            this._txt.autoSize = true;
            this._txt.color = '#428371';
            this._txt.width = 620;
            this._txt.wordWrap = true;
            this.addChild(this._txt);
            this._txt.pos(20, 0);
            
            if (yalla.Font.lan == 'ar') {
                this._txt.right = 28;
                this._txt.align = 'right';
                this._txt.width = 690;
            } else if (yalla.Font.lan == 'urdu') {
                this._txt.pos(80, 0);
                this._txt.right = 28;
                this._txt.align = 'right';
                this._txt.width = 630;
            }
        }
    }
}