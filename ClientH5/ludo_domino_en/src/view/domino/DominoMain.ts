class DominoMain extends BaseMain {
    private _client;
    private _gameBox: Laya.Box;

    constructor(gameType: number) {
        super();
        Laya.stage.bgColor = "#007b54";
        if (this._gameView) this.clear();
        if (!this._gameView) {
            yalla.Debug.log("===domino 注册字体==="+!!yalla.Font.numberBmpFont);
            if (yalla.Font.numberBmpFont) {
                Laya.Text.registerBitmapFont('number', yalla.Font.numberBmpFont);
            }
            this.initGameBox();
            this._gameView = new yalla.view.game.Game();
            this._gameView.name = 'gameView';
            this._gameBox.addChild(this._gameView);
        }
    }

    private initGameBox(): void {
        this._gameBox = new Laya.Box();
        this._gameBox.size(Laya.stage.width, Laya.stage.height);
        Laya.stage.addChild(this._gameBox);
    }

    /**
     * 切入后台
     */
    public onBlur(): void {
        super.onBlur();
        yalla.Global.game_state = yalla.data.GameState.State_Sleep;
        yalla.Sound.stopAllSound();

        if (this._gameView) {
            this._gameView.hide();
            yalla.common.InteractiveGift.Instance.onBlur();

        }

        if (!yalla.Global.IsGameOver) yalla.Sound.playMusic(yalla.Sound.audios.domino_bg, 2);
    }

    /**
     * 切到前台
     */
    public onForce(): void {
        if (yalla.Global.IsGameOver) return;
        super.onForce();
        if (yalla.Global.game_state != yalla.data.GameState.State_Wake && yalla.Global.game_state.length > 0) {
            yalla.Global.game_state = yalla.data.GameState.State_Wake;
            if (this._gameView) this._gameView.show();

            if (yalla.Sound.canPlayMusic) {
                yalla.Global.isFouce = true;
                yalla.Sound.playMusic(yalla.Sound.audios.domino_bg, 1);
            }
        }

        //重新心跳时间，发送心跳包
        if (this._client) this._client.resumeHeart();
    }

    /**
     * 登陆游戏
     * @param msg 
     */
    public onLogin(msg: any): void {
        super.onLogin(msg); yalla.Debug.log('onLogin');
        // let str = yalla.Global.Account.connectType ? ('connectType: ' + yalla.Global.Account.connectType + " url:" + yalla.Global.Account.url) : "非新网关 host: " + yalla.Global.host + ":" + yalla.Global.port;
        // yalla.Debug.specialLog("Domino 服务 - " + str);

        if (msg) {
            // yalla.data.UserService.instance.clear();
            yalla.data.RoomService.instance.clear();
            yalla.data.VoiceService.instance.clear();

            // yalla.data.RoomService.instance.setNet(yalla.Global.Account.connectType);
            // this._client = yalla.data.RoomService.instance.client;

            this.restartGame();
            yalla.data.UserService.instance.user.update(msg);
            yalla.data.RoomService.instance.room.update(msg);

            // yalla.Global.Account.connectType = 2;
            if (yalla.util.IsBrowser()) {
                //浏览器走这个逻辑，1.3.1使用原生 则不用主动发起
                if (yalla.Global.Account.connectType == 2) {
                    console.log("=====Main.onLogin  0=====");
                    yalla.ClientBrower.instance.init(Laya.Handler.create(this, () => {
                        yalla.data.RoomService.instance.ticketLogin();
                    }));
                } else {
                    console.log("=====Main.onLogin  1=====");
                    yalla.net.Client.instance.init(Laya.Handler.create(this, () => {
                        yalla.data.RoomService.instance.ticketLogin();
                    }));
                }
                yalla.data.RoomService.instance.setNet(yalla.Global.Account.connectType);
                this._client = yalla.data.RoomService.instance.client;

            } else {
                //现在改为主动调起原生soket连接（因锦标赛）
                yalla.data.RoomService.instance.setNet(yalla.Global.Account.connectType);
                this._client = yalla.data.RoomService.instance.client;
                yalla.data.RoomService.instance.onWebSocketInit(yalla.Global.Account.connectType);

                if (msg.isChampionship) Laya.timer.once(yalla.Global.reconnect_interval, this, this.checkConnect);
                // else {
                //     if(yalla.Global.Account.connectType != 2) yalla.net.NativeClient.instance.initSocket();
                // }
            }
        }
    }

    public onWebSocketOpen() {
        super.onWebSocketOpen();
        Laya.timer.clear(this, this.checkConnect);

        yalla.Debug.log(!!yalla.data.RoomService.instance.client + "===DominoMain socket 连上处理逻辑======IsGameOver=" + yalla.Global.IsGameOver);
        if (yalla.Global.IsGameOver) return;
        if (!this._client) {//TODO::******* 第一次进入游戏 onOpen 没有执行，则不会initSocket，所以现在 onWebSocketInit在onLogin就执行，open后触发游戏内的onWebSocketOpen； 但是游戏内短线重连，需要考虑 client是否存在，没有则继续init
            yalla.data.RoomService.instance.onWebSocketInit(yalla.Global.Account.connectType);
        }
        this._client = yalla.data.RoomService.instance.client;

        yalla.data.RoomService.instance.onWebSocketOpen(yalla.Global.Account.connectType);
        yalla.common.connect.ReconnectControl.instance.connectSucss();
    }


    private checkConnect() {
        // yalla.net.NativeClient.instance.checkConnect();
        this._client && this._client.checkConnect();
    }

    /**
     * 再来一局匹配成功
     */
    public restartGame(): void {
        super.restartGame();
        yalla.Global.game_state = yalla.data.GameState.State_Playing;
        yalla.data.UserService.instance.register();
        yalla.data.RoomService.instance.register();
        // yalla.data.VoiceService.instance.register();
    }

    /**
     * onKickedOut
     * 被顶号
     * 音效关闭
     */
    public onKickedOut() {
        yalla.data.VoiceService.instance.levelGameRoom();
        yalla.Sound.canPlayMusic = false;
        yalla.Global.IsGameOver = true;
        yalla.Sound.stopAll();
        this.clear(false);
    }
    /**
     * 断线重连
     * socket 断掉
     * game 的event 和 tween停止
     * 执行连接success 逻辑
     */
    public onReconnect(): void {
        if (yalla.Global.IsGameOver) return;
        super.onReconnect();
        Laya.timer.clearAll(this);

        if (this._client) this._client.clear();
        if (yalla.util.IsBrowser()) {
            yalla.Debug.log(' client init ', 'green');

            if (yalla.Global.Account.connectType == 2) {
                //浏览器走这个逻辑，1.3.1使用原生 则不用主动发起
                yalla.ClientBrower.instance.init(Laya.Handler.create(this, () => {
                    yalla.data.RoomService.instance.ticketLogin();
                }));
            } else {
                yalla.net.Client.instance.init(Laya.Handler.create(this, () => {
                    yalla.data.RoomService.instance.ticketLogin();
                    yalla.common.connect.ReconnectControl.instance.connectSucss();
                }));
            }
        } else {
            yalla.NativeWebSocket.instance.sendNetReConnect();
        }
    }

    /**
     * 重连倒计时结束 手动结束socket连接
     */
    public onReTimeOver() {
        super.onReTimeOver();
        this._client && this._client.clear();
    }

    /**
     * 退出游戏(接受其他玩家请求加入游戏,并结束当前游戏 ps:被动返回大厅)
     * @param msg 
     */
    public onQuitGame(msg: any): void {
        super.onQuitGame(msg);
        yalla.data.RoomService.instance.quitRoom();
        yalla.util.sendExitType(yalla.data.ExitType.JOIN_OTHER_GAME_QUIT);
    }

    public updateMoney() {
        yalla.data.UserService.instance.user.gold = yalla.Global.Account.currentDiamondNum;
        yalla.data.UserService.instance.user.money = yalla.Global.Account.currentGoldNum;
        yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_Update_Player_Coin);
    }

    private removeInstance(): void {
        Laya.timer.clear(this, this.checkConnect);
        Laya.timer.clearAll(this);
        yalla.data.UserService.instance.clear();
        yalla.data.RoomService.instance.clear();
        // yalla.data.VoiceService.instance.levelGameRoom();
        yalla.data.VoiceService.instance.clear();

        yalla.data.RoomService._instance = null;
        yalla.data.VoiceService._instance = null;
        yalla.data.UserService._instance = null;

        if (this._client) {
            this._client.resetMsg();
            this._client.clear();
            this._client.firstLogin = true;
        }
        this._client = null;
    }

    public backHallClear(remove: boolean = false): void {
        super.backHallClear();
        yalla.Global.game_state = '';
        yalla.Sound.canPlayMusic = false;
        this.removeInstance();
        yalla.Debug.log((!!this._gameView) + "==啦啦啦=DominoMain.backHallClear====" + !remove);
        if (!!this._gameView) {
            if (!remove) {
                this._gameView.backHallClear();
            } else {
                this._gameView.clear();
                this._gameView = null;
                if (this._gameBox) {
                    this._gameBox.removeSelf();
                    this._gameBox.destroy(true);
                    this._gameBox = null;
                }
            }
        }
        yalla.Global.IsGameOver = true;
        yalla.Global.isconnect = true;
        yalla.File.loadStack = [];
        Laya.Pool.clearBySign('cardItem');
        Laya.Pool.clearBySign('vcardItem');
        Laya.Pool.clearBySign('otherCardItem');
        Laya.Pool.clearBySign('votherCardItem');
        Laya.Pool.clearBySign('outCardItem');
        Laya.Pool.clearBySign('voutCardItem');
    }

    public clear(removeView: Boolean = true): void {
        this.backHallClear(true);
        super.clear();
    }
}