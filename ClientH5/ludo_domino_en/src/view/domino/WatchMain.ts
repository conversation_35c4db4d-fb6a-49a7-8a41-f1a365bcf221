class WatchMain extends BaseMain {

    constructor(gameType: number) {
        super();
        Laya.stage.bgColor = "#007b54";
        if (this._gameView) this.clear();
        yalla.Debug.log('====watchMain==='+(!this._gameView))
        if (!this._gameView) this._gameView = new yalla.view.game.WatchGame();
        this._gameView.name = 'gameView';
        Laya.stage.addChild(this._gameView);
    }

    /**
     * 切入后台
     */
    public onBlur(): void {
        super.onBlur();
        yalla.Debug.log('===domino==onBlur==');
        yalla.Global.game_state = yalla.data.GameState.State_Sleep;
        yalla.Sound.stopAllSound();

        if (this._gameView) {
            this._gameView.hide();
            yalla.common.InteractiveGift.Instance.onBlur();
        }
        if (!yalla.Global.IsGameOver) yalla.Sound.playMusic(yalla.Sound.audios.domino_bg, 2);
    }

    /**
     * 切到前台
     */
    public onForce(): void {
        if(yalla.Global.IsGameOver) return;
        super.onForce();
        yalla.Debug.log('===domino==onForce==');
        if (yalla.Global.game_state != yalla.data.GameState.State_Wake && yalla.Global.game_state.length > 0) {
            yalla.Global.game_state = yalla.data.GameState.State_Wake;
            if (this._gameView) this._gameView.show();
        }
        
        yalla.Global.isFouce = true;
        yalla.data.RoomService.instance.playMsg();
        yalla.Sound.playMusic('bgm_domino', 1);

        //重新心跳时间，发送心跳包
        // if(this._client) this._client.resumeHeart();
    }

    /**
     * 登陆游戏
     * @param msg 
     */
    public onLogin(msg: any): void {
        super.onLogin(msg);
        yalla.data.UserService.instance.clear();
        yalla.data.RoomService.instance.clear();
        yalla.Native.instance.removeMatchView(false);

        yalla.Debug.log('=====onLogin watchmain=====');
        if (msg) {
            yalla.Debug.log(msg)
            this.restartGame();
            // yalla.data.UserService.instance.user.update(msg);
            //TODO:: 房间的showId就是roomId （观战这里客户端自己处理）

            yalla.data.RoomService.instance.room.update(msg);

            this.onWebSocketOpen()
            yalla.data.RoomService.instance.initWatchData();
            this._gameView && this._gameView.onLogin();
        }
    }
    // private checkConnect() {
    //     yalla.net.NativeClient.instance.checkConnect();
    // }

    /**
     * 再来一局匹配成功
     */
    public restartGame(): void {
        super.restartGame();
        yalla.Global.game_state = yalla.data.GameState.State_Playing;
        yalla.data.UserService.instance.register();
        // yalla.data.RoomService.instance.register();
        // yalla.data.VoiceService.instance.register();
    }

    /**
     * onKickedOut
     * 被顶号
     * 音效关闭
     */
    public onKickedOut() {
        yalla.Global.IsGameOver = true;
        yalla.Sound.stopAll();
        this.clear(false);
    }
    /**
     * 断线重连
     * socket 断掉
     * game 的event 和 tween停止
     * 执行连接success 逻辑
     */
    // public onReconnect(): void {
    //     if(yalla.Global.IsGameOver) return;
    //     super.onReconnect();
    //     Laya.timer.clearAll(this);

    //     if(this._client) this._client.clear();
    //     if (yalla.util.IsBrowser()) {
    //         yalla.Debug.log(' client init ', 'green')
    //         yalla.net.Client.instance.init(Laya.Handler.create(this, () => {
    //             yalla.data.RoomService.instance.ticketLogin();
    //             yalla.common.connect.ReconnectControl.instance.connectSucss();
    //         }));
    //     } else {
    //         yalla.NativeWebSocket.instance.sendNetReConnect();
    //     }
    // }

    /**
     * 重连倒计时结束 手动结束socket连接
     */
    public onReTimeOver() {
        super.onReTimeOver();
        // this._client && this._client.clear();
    }

    /**
     * 退出游戏(接受其他玩家请求加入游戏,并结束当前游戏 ps:被动返回大厅)
     * @param msg 
     */
    public onQuitGame(msg: any): void {
        super.onQuitGame(msg);
        yalla.data.RoomService.instance.quitRoom();
        yalla.util.sendExitType(yalla.data.ExitType.JOIN_OTHER_GAME_QUIT);
    }

    /**原生socket连上后，展示游戏界面 */
    private _timeOut = null;
    public onWebSocketOpen() {
        super.onWebSocketOpen();
        yalla.Debug.log(yalla.Global.Account.connectType+'======WatchMain.onWebSocketOpen======yalla.Global.IsGameOver=' + yalla.Global.IsGameOver);
        if (yalla.Global.IsGameOver) return;

        // yalla.net.NativeClient.instance.initSocket();
        yalla.data.RoomService.instance.setNet(yalla.Global.Account.connectType);
        if (!yalla.data.RoomService.instance.client) {
            yalla.data.RoomService.instance.onWebSocketInit(yalla.Global.Account.connectType);
        }
        yalla.data.RoomService.instance.onWebSocketOpen(yalla.Global.Account.connectType);

        if (yalla.Native.instance.deviceType == DeviceType.IOS) {
            this._timeOut && clearTimeout(this._timeOut);
            this._timeOut = setTimeout(function () {
                clearTimeout(this._timeOut);
                yalla.Debug.log('===onWebSocketOpen playMusic====');
                yalla.Sound.playMusic('bgm_domino', 0);
            }, 2000);
        } else {
            yalla.Sound.playMusic('bgm_domino', 0);
        }

        yalla.common.connect.ReconnectControl.instance.connectSucss();
    }

    private removeInstance(): void {
        // Laya.timer.clear(this, this.checkConnect);
        Laya.timer.clearAll(this);
        this._timeOut && clearTimeout(this._timeOut);
        yalla.data.UserService.instance.clear();
        yalla.data.RoomService.instance.clear();
        yalla.data.VoiceService.instance.clear();
        // yalla.net.NativeClient.instance.clear();

        yalla.data.RoomService._instance = null;
        yalla.data.VoiceService._instance = null;
        yalla.data.UserService._instance = null;
        
        var client = yalla.data.RoomService.instance.client;
        if (client) {
            client.resetMsg();
            client.clear();
            client.firstLogin = true;
        }
        client = null;        
        // yalla.net.NativeClient.instance.resetMsg();
        // yalla.net.Client.instance.firstLogin = true;
    }

    public backHallClear(remove:boolean = false): void {
        super.backHallClear();
        yalla.Global.game_state = '';
        yalla.Sound.canPlayMusic = false;
        this.removeInstance();
        yalla.Debug.log((!!this._gameView) + "==啦啦啦=DominoMain.backHallClear====" + !remove);
        if (!!this._gameView) {
            if (!remove) {
                this._gameView.backHallClear();
            } else {
                this._gameView.clear();
                this._gameView = null;
            }
        }
        yalla.Global.IsGameOver = true;
        yalla.File.loadStack = [];
        Laya.Pool.clearBySign('cardItem');
        Laya.Pool.clearBySign('vcardItem');
        Laya.Pool.clearBySign('otherCardItem');
        Laya.Pool.clearBySign('votherCardItem');
        Laya.Pool.clearBySign('outCardItem');
        Laya.Pool.clearBySign('voutCardItem');
    }

    public clear(removeView: Boolean = true): void {
        this.backHallClear(true);
        super.clear();
    }
}