module yalla.net {
    import Handler = Laya.Handler;
    import EventDispatcher = Laya.EventDispatcher;

    export class Client extends EventDispatcher {
        static _instance: Client;
        private _socket: yalla.net.GameSocket;
        private _preparedHandler: Handler;

        private _heartTime: number = 0;
        public msgHash: Object = null;
        public msgIndex: number = 0;
        public firstLogin: boolean = true;

        constructor() {
            super();
        }

        static get instance(): Client {
            return Client._instance || (Client._instance = new Client());
        }

        public get protobuff() { return laya.utils.Browser.window.protobuf; }

        public init(preparedHandler: Handler): void {
            if (this._socket) this.clear();

            this._preparedHandler = preparedHandler;
            this._socket = new yalla.net.GameSocket(ludo.Proto.instance.root_domino);
            this._socket.init(new Laya.Socket(), Laya.Handler.create(this, this.onMessage, null, false));
            if (!yalla.Global.Account.ip || yalla.Global.Account.ip.length < 1) {
                if (yalla.Native.instance.deviceType != DeviceType.Browser) {
                    yalla.Native.instance.getHostIp(yalla.Global.Account.host, (ip) => {
                        if (!ip || ip.length < 1) {
                            // yalla.common.connect.ReconnectControl.instance.connect();
                        } else {
                            if (!this._socket) {//TODO::游戏结束但是回调方法还存在
                                this.clear();
                                return;
                            }
                            yalla.Global.Account.ip = 'ws://' + ip;
                            var url = `${yalla.Global.Account.ip}:${yalla.Global.Account.port}`;
                            this._socket.connectByUrl(`${yalla.Global.Account.ip}:${yalla.Global.Account.port}`, Laya.Handler.create(this, () => {
                                this.onOpen();
                            }));
                        }
                    });
                } else {
                    var url = `${yalla.Global.Account.host}:${yalla.Global.Account.port}`;
                    this._socket && this._socket.connectByUrl(url, Laya.Handler.create(this, () => {
                        this.onOpen();
                    }));
                }
            } else {
                this._socket.connectByUrl(`${yalla.Global.Account.ip}:${yalla.Global.Account.port}`, Laya.Handler.create(this, () => {
                    this.onOpen();
                }));
            }
            this.sendHeart();
            this.onheart();//计时器从0开始时先执行一次
        }

        /**
         * @param name 消息名称
         * @param cmd command
         * @param obj 参数
         */
        public sendMsg(name: string, cmd: number, obj?: Object): boolean {
            if (!yalla.util.IsWinConch()) {
                yalla.Debug.log(`[${yalla.getTimeHMS()}][send:][${name}_${cmd}]`);
                yalla.Debug.log(obj);
            }
            if (this._socket && this._socket.isOpen) {
                this._socket.sendMsg(name, cmd, obj);
                return true;
            }
            return false;
        }

        /**
         * socket 连接成功
         */
        private onOpen(): void {
            this._preparedHandler && this._preparedHandler.run();
        }

        /**
         * 协议返回 事件抛出
         * @param cmd 
         * @param content 
         */
        private onMessage(cmd: any, content: any, isHeart: boolean = false): void {
            if (cmd == '__closed' || cmd == '__error') {
                yalla.Debug.log(yalla.getTimeHMS() + '  onMessage closed or error: ' + yalla.Global.isconnect + '-isHeart:-' + isHeart);
                if (yalla.Global.isconnect || isHeart) {
                    if (yalla.Global.isconnect) {
                        yalla.util.sendExitType(yalla.data.ExitType.SOCKETFAILED_QUIT);
                        //TODO::原先之所以加下面这个逻辑，短时间断线后不发起重连，表现更友好
                        if (yalla.data.RoomService.instance.msgQueueList.length < 1) {
                            yalla.data.RoomService.instance.addMsgInManunal();
                        }
                    }
                    yalla.Debug.log('---isconnect 000------');
                    yalla.Global.isconnect = false;
                    Laya.timer.clear(this, this.checkConnect);
                    Laya.timer.clear(this, this.onheart);
                    yalla.Global.onLoseNextTime = yalla.System.getNowTime();
                    yalla.common.connect.ReconnectControl.instance.connect(yalla.data.RoomService.instance.ImmediateConnect);
                }

                if (yalla.Global.reconnectTimes < 1) {
                    yalla.Native.instance.mobClickEvent(buryPoint.DGAME_BREAK);
                } else {
                    yalla.Native.instance.mobClickEvent(buryPoint.DGAME_BREAK_CONNECTFAILURE);
                }
                yalla.Global.reconnectTimes++;
                return;
            }

            if (!yalla.util.IsWinConch()) yalla.Debug.log(`[${yalla.getTimeHMS()} ][response][cmd:${cmd}]`);
            var game_cmd = this.eventName + cmd;
            this.event(game_cmd, content);

            if (cmd == yalla.data.Command.PLAYER_LOGIN) {
                this.firstLogin = false;
                // Laya.timer.clear(this, this.checkConnect);
                //this._socket.sendHeart();
                this.sendHeart();
                this.onheart();//计时器从0开始时先执行一次

                this._preparedHandler && this._preparedHandler.run();
                yalla.Global.reconnectTimes = 0;
                yalla.Native.instance.mobClickEvent(buryPoint.DGAME_BREAK_RECONNECTSUCCESS);

            } else if (cmd == yalla.data.Command.HEART) {
                var msg = this.decodeMsg(content, "HeartRequestAndResponse");//心跳
                this._heartTime = yalla.System.getNowTime();
            }
        }

        public decodeMsg(bytearr: any, name: string): any {
            try {
                let msg: any = {};
                let root = ludo.Proto.instance.root_domino;
                if (root) {
                    let loginMessage = root.lookupType(name);
                    msg = loginMessage.decode(bytearr);

                    if (msg && msg.msgindex) {
                        if (!this.msgHash) this.msgHash = {};
                        this.msgHash[msg.msgindex] = msg;
                        this.msgIndex = msg.msgindex;
                    }
                    if (!yalla.util.IsWinConch()) yalla.Debug.log(msg);
                }
                return msg;

            } catch (error) {
                this._socket.close();
            }
        }

        /**
         * 登录游戏
        * @param idx 
         * @param token 
         */
        public loginGame(idx: number, token: string, roomId: number) {
            this.sendMsg("TicketLoginRequest", yalla.data.Command.TICKET_LOGIN, {
                idx: idx,
                roomid: roomId,
                token: token
            });
        }

        /**
         * 从后台回来重新发送心跳
         */
        public resumeHeart(): void {
            if (this._socket) {
                if (this._socket.isOpen) this._socket.sendHeart();
                if (this.firstLogin && !this._socket.isOpen) {
                    Laya.timer.clear(this, this.checkConnect);
                    Laya.timer.once(4000, this, this.checkConnect);
                }
            }
        }

        /**
         * 心跳包
         */
        private sendHeart(): void {
            Laya.timer.clear(this, this.onheart);
            Laya.timer.clear(this, this.checkConnect);

            this._heartTime = yalla.System.getNowTime();
            Laya.timer.loop(yalla.Global.heart_interval, this, this.onheart, null, true, true);
            // //心跳包5s，等7s检测是否连上,因onheart subTime >= yalla.Global.heart_interval + 2000需要第二次循环，即10s后才能检测是否断开
            // Laya.timer.once(yalla.Global.heart_interval + 2500, this, ()=>{
            //     this.onheart();//TODO::套一层()=>{}，避免结束检测后，timer事件被停止
            // });

            if (this.firstLogin) {
                Laya.timer.once(4000, this, this.checkConnect);
            }
        }
        private onheart(): void {
            if (!this._socket) return;
            var subTime = yalla.System.getNowTime() - this._heartTime;
            yalla.Debug.log(this._heartTime+'=====onheart====subTime='+subTime);
            if (this._heartTime > 0 && subTime >= yalla.Global.heart_interval + 2000) {
                this._socket.close();
                this.onMessage('__error', null, true);
                return;
            }
            this._socket.sendHeart();
        }

        public checkConnect(): void {//TODO::4s内还未连上则断线重连
            if (!this._socket) return;

            // yalla.Debug.log(!yalla.Global.isconnect + '===checkConnect==len='+yalla.data.RoomService.instance.msgQueueList.length);
            if ((yalla.data.RoomService.instance.msgQueueList.length < 1 || !yalla.Global.isconnect)) {
                this.firstLogin = false;
                this._socket.close();
                this.onMessage('__error', null, true);
            }
        }

        get eventName() {
            if (yalla.Global.gameType == GameType.DOMINO) return "Dominose_";
            if (yalla.Global.gameType == GameType.SNAKEANDLADER) return "Snake_";
        }

        public resetMsg(): void {
            this.msgIndex = 0;
            this.msgHash = null;
        }

        public clear(): void {
            if (this._socket) this._socket.cleanSocket();
            yalla.Debug.log('---clear isconnect 111------');
            yalla.Global.isconnect = false;
            Laya.timer.clear(this, this.onheart);
            Laya.timer.clear(this, this.checkConnect);
            Laya.timer.clearAll(this);
            this._socket = null;
            this._heartTime = 0;
        }

        public test(): void {
            if (this._socket) {
                this._socket.test();
            }
        }
    }
}