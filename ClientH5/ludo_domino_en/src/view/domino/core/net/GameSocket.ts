module yalla.net {
	import Handler = Laya.Handler;
	import Event = Laya.Event;
	import Byte = Laya.Byte;

	export enum SocketState {NASCENT=1, OPEN, CLOSED, KICKED, ERROR, CLEAN};

	export class GameSocket {
		private _root:any;
		private _state: SocketState;
		private _socket: Laya.Socket;
		private _seq: number;

		private _onConnected: Handler;
		private _messageHandler: Handler;

		private _callbacks: Object;
		private _errorHandlers: Object;
		private _componentMsgCb: Handler;
		private _componentErrorCb: Handler;

		constructor(root) {
			this._root = root;
		}

		public init(socket: Laya.Socket, messageHandler: Handler=null): void {
			if (this._socket) return;
			this._state = SocketState.NASCENT;
			this._socket = socket;
			this._messageHandler = messageHandler;
			this.initEvent();
		}

		public connect(host: string, port:number, onConnected: Handler=null): void {
			this._onConnected = onConnected;
			this._socket && this._socket.connect(host, port);
		}

		public connectByUrl(url:string, onConnected: Handler=null): void {
			this._onConnected = onConnected;
			this._socket && this._socket.connectByUrl(url);
		}

		public close(): void {
			this.cleanSocket();
			this._state = SocketState.CLOSED;
		}

		public cleanSocket():void{
			this._state = SocketState.CLEAN;
			if(this._socket){
				this.removeEvent();
				this._socket.close();
				this._socket.cleanSocket();
			}
			this._socket = null;
		}

		public test():void{
			if(this._socket){
				this._socket.close();
			}
		}

		get state(): SocketState {
			return this._state;
		}

		get isOpen(): boolean {
			return (this._state == SocketState.OPEN ? true : false);
		}

		public setProtocol(protocol: string): GameSocket {
			this._socket.protocols.push(protocol);
			return this;
		}

		public sendHeart(): void {
			if (!this.isOpen) return;
			
        	this.sendMsg("HeartRequestAndResponse", 127);
		}

		        /**
         * @param name 消息名称
         * @param cmd command
         * @param obj 参数
         */
        public sendMsg(name: string, cmd: number, obj?: Object) {
			if(!this.isOpen) return false;

            let loginMessage = this._root.lookupType(name);
            let message = loginMessage.create(obj);
            let byteArray = loginMessage.encode(message).finish();
            let len = byteArray.byteLength + 4;
            let sendByteArray = new ArrayBuffer(len);
            let headByteArray = new Uint8Array(sendByteArray);
            headByteArray[0] = (byteArray.byteLength & 0xff);
            headByteArray[1] = (byteArray.byteLength >> 8) & 0xff;
            headByteArray[2] = 0;
            headByteArray[3] = cmd;
            let position = 4;
            byteArray = new Uint8Array(byteArray);
            for (let i = 0; i < byteArray.byteLength; i++) {
                headByteArray[position] = byteArray[i];
                position++;
            }
            if (this._socket.connected == true)
                this._socket.send(sendByteArray);
        }

		private initEvent(): void {
			this._socket.once(Event.OPEN, this, this.onOpen, null);
			this._socket.on(Event.MESSAGE, this, this.onMessage, null);
			this._socket.once(Event.CLOSE, this, this.onClose, null);
			this._socket.once(Event.ERROR, this, this.onError, null);
		}

		private removeEvent(): void {
			this._socket.off(Event.OPEN, this, this.onOpen);
			this._socket.off(Event.CLOSE, this, this.onClose);
			this._socket.off(Event.MESSAGE, this, this.onMessage);
			this._socket.off(Event.ERROR, this, this.onError);
			this._socket.offAll();
		}

		protected onOpen(e: any): void {
			yalla.Debug.log('---onOpen isconnect 111------');
			yalla.Global.isconnect = true;
			this._state = SocketState.OPEN;
			this._seq = 0;
			this._callbacks = {};
			this._errorHandlers = {};

			this._onConnected && this._onConnected.run();
			this._onConnected = null;
		}

		protected onMessage(res: any): void {
			var totalLen = res.byteLength;
			var getByteArray = new Uint8Array(res);
			var potobufByteArray = new ArrayBuffer(totalLen - 4);
			
			if(totalLen < potobufByteArray.byteLength+4){
				this.close();
				return;
			}
			var position = 4;
			var potobufSetByteArray = new Uint8Array(potobufByteArray);
			for (var i = 0; i < getByteArray.byteLength; i++) {
				potobufSetByteArray[i] = getByteArray[position];
				position++;
			}
			var head = (res as ArrayBuffer).slice(0, 4);
			var int8head = new Int8Array(head);
			var command = Array.apply([], int8head);
			var cmd = command[3];
			if (this._messageHandler) this._messageHandler.runWith([cmd, potobufSetByteArray]);
		}

		protected onClose(e: any): void {
			// yalla.Debug.log('Domino  Socket:' + ' closed. '+!this._messageHandler);

			this._callbacks = this._errorHandlers = null;
			this._state = SocketState.CLOSED;
			this._socket = null;
			if (this._messageHandler) this._messageHandler.runWith(['__closed', this]);
			this._messageHandler = null;
		}

		protected onError(e: any): void {
			// yalla.Debug.log('Domino  Socket' + ' error. '+!this._messageHandler);
			
			this._callbacks = this._errorHandlers = null;
			this._state = SocketState.ERROR;
			this._socket = null;
			if (this._messageHandler) this._messageHandler.runWith(['__error', this]);
			this._messageHandler = null;
		}
	}
}