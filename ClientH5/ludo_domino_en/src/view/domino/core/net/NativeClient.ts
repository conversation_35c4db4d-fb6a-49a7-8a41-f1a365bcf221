module yalla.net {
    import Handler = Laya.Handler;
    import EventDispatcher = Laya.EventDispatcher;

    export class NativeClient extends EventDispatcher {
        static _instance: NativeClient;
        private _socket: yalla.net.NativeGameSocket;
        private _preparedHandler: Handler;

        private _heartTime: number = 0;
        public msgHash: Object = null;
        public msgIndex: number = 0;
        public firstLogin: boolean = true;

        constructor() {
            super();
        }

        static get instance(): NativeClient {
            return NativeClient._instance || (NativeClient._instance = new NativeClient());
        }

        public get protobuff() { return laya.utils.Browser.window.protobuf; }
        public get protoRoot() {
            var root = ludo.Proto.instance.root_domino;
            if (yalla.Global.Account.leagueWatch) root = ludo.Proto.instance.root_watch;
            return root;
        }

        public initSocket(): void{
            yalla.Debug.log("======domino 网络初始化=NativeClient.initSocket=======!this._socket:"+!this._socket);
            // if (this._socket) this.clear();
             if (!this._socket)  {
                this._socket = new yalla.net.NativeGameSocket(this.protoRoot);
            }
            if (yalla.Global.Account.leagueWatch) {
                this._socket.init(Laya.Handler.create(this, this.onMessage_watch, null, false));
            } else {
                this._socket.init(Laya.Handler.create(this, this.onMessage, null, false));
                this.sendHeart();
                // this.onheart();//计时器从0开始时先执行一次
            }
        }

        // public init(preparedHandler: Handler): void {
        //     this._preparedHandler = preparedHandler;
        //     if (!this._socket)  {
        //         this._socket = new yalla.net.NativeGameSocket(this.protoRoot);
        //         if (yalla.Global.Account.leagueWatch) {
        //             this._socket.init(Laya.Handler.create(this, this.onMessage_watch, null, false));
        //         } else {
        //             this._socket.init(Laya.Handler.create(this, this.onMessage, null, false));
        //         }

        //     }
        //     this._socket.onOpen();//socket 中的onOpen在initEvent后触发，导致未监听到，这里手动触发
        //     this._preparedHandler && this._preparedHandler.run();

            // if (!this.isWatch) {
            //     this.sendHeart();
            //     this.onheart();//计时器从0开始时先执行一次
            // }
        // }

        /**
         * @param name 消息名称
         * @param cmd command
         * @param obj 参数
         */
        public sendMsg(name: string, cmd: number, obj?: Object): boolean {
            if (!yalla.util.IsWinConch()) {
                yalla.Debug.log(`[${yalla.getTimeHMS()}][send:][${name}_${cmd}]`);
                yalla.Debug.log(obj);
            }
            // if (this._socket && this._socket.isOpen) {
            yalla.Debug.log(name+" : "+ cmd +"=====NativeCLient.sendMsg=======this._socket="+(!!this._socket));
            if (this._socket) {
                this._socket.sendMsg(name, cmd, obj);
                return true;
            }
            // return false;
        }

        /**
         * socket 连接成功
         */
        private onOpen(): void {
            this._preparedHandler && this._preparedHandler.run();
        }

        /**
         * 协议返回 事件抛出
         * @param cmd 
         * @param content 
         */
        private onMessage(cmd: any, content: any, isHeart: boolean = false): void {
            if (cmd == '__closed' || cmd == '__error') {
                yalla.Debug.log(this._socket.state + '  onMessage closed or error: ' + yalla.Global.isconnect + '-isHeart:-' + isHeart);
                if (yalla.Global.isconnect || isHeart) {
                    if (yalla.Global.isconnect) {
                        yalla.util.sendExitType(yalla.data.ExitType.SOCKETFAILED_QUIT);
                        //TODO::原先之所以加下面这个逻辑，短时间断线后不发起重连，表现更友好
                        if (yalla.data.RoomService.instance.msgQueueList.length < 1) {
                            yalla.data.RoomService.instance.addMsgInManunal();
                        }
                    }
                    yalla.Global.isconnect = false;
                    Laya.timer.clear(this, this.checkConnect);
                    Laya.timer.clear(this, this.onheart);
                    yalla.Global.onLoseNextTime = yalla.System.getNowTime();
                    yalla.common.connect.ReconnectControl.instance.connect(yalla.data.RoomService.instance.ImmediateConnect);
                }

                if (yalla.Global.reconnectTimes < 1) {
                    yalla.Native.instance.mobClickEvent(buryPoint.DGAME_BREAK);
                } else {
                    yalla.Native.instance.mobClickEvent(buryPoint.DGAME_BREAK_CONNECTFAILURE);
                }
                yalla.Global.reconnectTimes++;
                return;
            }

            if (!yalla.util.IsWinConch()) yalla.Debug.log(`[${yalla.getTimeHMS()} ][response][cmd:${cmd}]`);
            var game_cmd = this.eventName + cmd;
            this.event(game_cmd, [content]);
            // yalla.Debug.log("===NativeClient.onMessage====game_cmd=" + game_cmd);
            if (cmd == yalla.data.Command.PLAYER_LOGIN) {
                this.firstLogin = false;
                Laya.timer.clear(this, this.checkConnect);
                this.sendHeart();
                this.onheart();//计时器从0开始时先执行一次

                this._preparedHandler && this._preparedHandler.run();
                yalla.Global.reconnectTimes = 0;
                yalla.Native.instance.mobClickEvent(buryPoint.DGAME_BREAK_RECONNECTSUCCESS);

            } else if (cmd == yalla.data.Command.HEART) {
                var msg = this.decodeMsg(content, "HeartRequestAndResponse");//心跳
                this._heartTime = yalla.System.getNowTime();
                yalla.Debug.log('-----HeartRequestAndResponse----');
            }
        }

        /**
         * 观战 协议返回 事件抛出
         * @param cmd 
         * @param content 
         */
        public onMessage_watch(cmd: any, content: any, isHeart: boolean = false): void {
            if (!yalla.util.IsWinConch()) yalla.Debug.log(`[${yalla.getTimeHMS()} ][response][UserMessageEnvelope`);
            if(cmd == -10000){
                //走老的协议
                console.log("=======NativeClient.老协议=====");
                this.event('UserMessageEnvelope_old', [content]);
            } else {
                this.event('UserMessageEnvelope', [cmd, content]);
            }
        }

        public decodeMsg_watch(bt: any, name: string): any {
            var result = this._socket.onMessage_watch_new(bt);
            yalla.Debug.log(result);
            yalla.Debug.log('=======NativeClient=decodeMsg_watch=========');
            if (!result) return;
            var bytearr = result[1];
            // try {
            let msg: any = {};
            let root = this.protoRoot;
            // console.log(name,'=====', bytearr);
            if (root) {
                let loginMessage = root.lookupType(name);
                msg = loginMessage.decode(bytearr);

                if (msg && msg.msgindex) {
                    if (!this.msgHash) this.msgHash = {};
                    this.msgHash[msg.msgindex] = msg;
                    this.msgIndex = msg.msgindex;
                }
                // if (!yalla.util.IsWinConch()) yalla.Debug.log(msg);
            }
            return msg;

            // } catch (error) {
            //     this._socket.close();
            // }
        }


        public decodeMsg(bytearr: any, name: string): any {
            // try {
            let msg: any = {};
            let root = this.protoRoot;
            // console.log(name,'=====', bytearr);
            if (root) {
                let loginMessage = root.lookupType(name);
                msg = loginMessage.decode(bytearr);

                if (msg && msg.msgindex) {
                    if (!this.msgHash) this.msgHash = {};
                    this.msgHash[msg.msgindex] = msg;
                    this.msgIndex = msg.msgindex;
                }
                if (!yalla.util.IsWinConch()) yalla.Debug.log(msg);
            }
            return msg;

            // } catch (error) {
            //     this._socket.close();
            // }
        }

        /**
         * 登录游戏
        * @param idx 
         * @param token 
         */
        public loginGame(idx: number, token: string, roomId: number) {
            this.sendMsg("TicketLoginRequest", yalla.data.Command.TICKET_LOGIN, {
                idx: idx,
                roomid: roomId,
                token: token
            });
        }

        /**
         * 从后台回来重新发送心跳
         */
        public resumeHeart(): void {
            if (this._socket) {
                if (this._socket.isOpen) this._socket.sendHeart();
                if (this.firstLogin && !this._socket.isOpen) {
                    Laya.timer.clear(this, this.checkConnect);
                    Laya.timer.once(4000, this, this.checkConnect);
                }
            }
        }

        /**
         * 心跳包
         */
        private sendHeart(): void {
            Laya.timer.clear(this, this.onheart);
            Laya.timer.clear(this, this.checkConnect);

            this._heartTime = yalla.System.getNowTime();
            Laya.timer.loop(yalla.Global.heart_interval, this, this.onheart, null, true, true);
        }
        private onheart(): void {
            if (!this._socket) return;
            var subTime = yalla.System.getNowTime() - this._heartTime;
            if (this._heartTime > 0 && subTime >= yalla.Global.heart_interval + 3000) {
                this._socket.close();
                this.onMessage('__error', null, true);
                return;
            }
            this._socket.sendHeart();
        }

        public checkConnect(): void {//TODO::4s内还未连上则断线重连
            Debug.log('------checkConnect------' + (!yalla.Global.isconnect));
            if (!this._socket) return;
            if ((yalla.data.RoomService.instance.msgQueueList.length < 1 || !yalla.Global.isconnect)) {
                this.firstLogin = false;
                this._socket.close();
                this.onMessage('__error', null, true);
            }
        }

        get eventName() {
            if (yalla.Global.gameType == GameType.DOMINO) return "Dominose_";
            if (yalla.Global.gameType == GameType.SNAKEANDLADER) return "Snake_";
        }

        public resetMsg(): void {
            this.msgIndex = 0;
            this.msgHash = null;
        }

        public clear(): void {
            yalla.Debug.log('======clear=======');
            if (this._socket) this._socket.cleanSocket();
            yalla.Global.isconnect = false;
            Laya.timer.clear(this, this.onheart);
            Laya.timer.clear(this, this.checkConnect);
            Laya.timer.clearAll(this);
            this._socket = null;
        }
    }
}