module yalla.net {
	import Handler = Laya.Handler;
	import Event = Laya.Event;
	import Byte = Laya.Byte;

	export class NativeGameSocket {
		private byte: Laya.Byte;
		private _root: any;
		private _state: SocketState;
		private _seq: number;

		private _onConnected: Handler;
		private _messageHandler: Handler;

		private _callbacks: Object;
		private _errorHandlers: Object;
		private _componentMsgCb: Handler;
		private _componentErrorCb: Handler;

		constructor(root) {
			this.byte = new Laya.Byte();
			this._root = root;
			// this.arr = [];
		}

		public init(messageHandler: Handler = null): void {
			this._state = SocketState.NASCENT;
			this._messageHandler = messageHandler;
			this.initEvent();
		}

		public connect(host: string, port: number, onConnected: Handler = null): void {
			this._onConnected = onConnected;
			// this._socket && this._socket.connect(host, port);
		}

		public connectByUrl(url: string, onConnected: Handler = null): void {
			this._onConnected = onConnected;
			// this._socket && this._socket.connectByUrl(url);
		}

		public close(): void {
			yalla.Debug.log('--NativeGameSocket.close--');
			this.cleanSocket();
			this._state = SocketState.CLOSED;
		}

		public cleanSocket(): void {
			this._state = SocketState.CLEAN;
			yalla.NativeWebSocket.instance.sendNetClose();
			this.removeEvent();
			// if(this._socket){
			// 	this.removeEvent();
			// 	this._socket.close();
			// 	this._socket.cleanSocket();
			// }
			// this._socket = null;
		}

		get state(): SocketState {
			return this._state;
		}

		get isOpen(): boolean {
			return (this._state == SocketState.OPEN ? true : false);
		}

		public sendHeart(): void {
			// if (!this.isOpen) return;
			this.sendMsg("HeartRequestAndResponse", 127);
		}

		/**
 * @param name 消息名称
 * @param cmd command
 * @param obj 参数
 */
		public sendMsg(name: string, cmd: number, obj?: Object) {
			// // if(!this.isOpen) return false;
			// let loginMessage = this._root.lookupType(name);
			// let message = loginMessage.create(obj);
			// let byteArray = loginMessage.encode(message).finish();
			// let len = byteArray.byteLength + 4;
			// let sendByteArray = new ArrayBuffer(len);

			// let headByteArray;
			// let position = 4;
			// if (yalla.Native.instance.deviceType == DeviceType.Android) {
			// 	headByteArray = new Int8Array(sendByteArray);
			// 	headByteArray[0] = (byteArray.byteLength & 0xff);
			// 	headByteArray[1] = (byteArray.byteLength >> 8) & 0xff;
			// 	headByteArray[2] = 0;
			// 	headByteArray[3] = cmd;
			// 	byteArray = new Int8Array(byteArray);
			// } else {
			// 	headByteArray = new Uint8Array(sendByteArray);
			// 	headByteArray[0] = (byteArray.byteLength & 0xff);
			// 	headByteArray[1] = (byteArray.byteLength >> 8) & 0xff;
			// 	headByteArray[2] = 0;
			// 	headByteArray[3] = cmd;
			// 	byteArray = new Uint8Array(byteArray);
			// }
			// for (let i = 0; i < byteArray.byteLength; i++) {
			//     headByteArray[position] = byteArray[i];
			//     position++;
			// }

			// var pp = Array.prototype.slice.call(headByteArray);
			// yalla.NativeWebSocket.instance.sendNetMsg(pp);

			let loginMessage = this._root.lookupType(name);
			let message = loginMessage.create(obj);
			let byteArray = loginMessage.encode(message).finish();
			if (byteArray && this.byte) {
				this.byte.clear();
				this.byte.writeInt16(byteArray.byteLength);
				this.byte.writeByte(0);
				this.byte.writeByte(cmd);
				this.byte.writeArrayBuffer(byteArray);

				var arr: Array<number>;
				if (yalla.Native.instance.deviceType == DeviceType.IOS) {
					arr = Array.prototype.slice.call(new Uint8Array(this.byte.buffer));
				} else {
					arr = Array.prototype.slice.call(new Int8Array(this.byte.buffer));
				}
				yalla.NativeWebSocket.instance.sendNetMsg(arr);
			}
		}

		private initEvent(): void {
			yalla.NativeWebSocket.instance.on(Laya.Event.OPEN, this, this.onOpen, null);
			yalla.NativeWebSocket.instance.on(Laya.Event.MESSAGE, this, this.onMessage, null);
			yalla.NativeWebSocket.instance.on('Message_onNetMsg', this, this.onMessage_onNetMsg, null);
			yalla.NativeWebSocket.instance.on('Message_onNetResponseMsg', this, this.onMessage_netResponseMsg, null);

			yalla.NativeWebSocket.instance.on(Laya.Event.CLOSE, this, this.onClose, null);
			yalla.NativeWebSocket.instance.on(Laya.Event.ERROR, this, this.onError, null);
			// this._socket.once(Event.OPEN, this, this.onOpen, null);
			// this._socket.on(Event.MESSAGE, this, this.onMessage, null);
			// this._socket.once(Event.CLOSE, this, this.onClose, null);
			// this._socket.once(Event.ERROR, this, this.onError, null);
		}

		private removeEvent(): void {
			yalla.NativeWebSocket.instance.off(Laya.Event.OPEN, this, this.onOpen, null);
			yalla.NativeWebSocket.instance.off(Laya.Event.MESSAGE, this, this.onMessage, null);
			yalla.NativeWebSocket.instance.off('Message_onNetMsg', this, this.onMessage_onNetMsg, null);
			yalla.NativeWebSocket.instance.off('Message_onNetResponseMsg', this, this.onMessage_netResponseMsg, null);
			yalla.NativeWebSocket.instance.off(Laya.Event.CLOSE, this, this.onClose, null);
			yalla.NativeWebSocket.instance.off(Laya.Event.ERROR, this, this.onError, null);
			// this._socket.off(Event.OPEN, this, this.onOpen);
			// this._socket.off(Event.CLOSE, this, this.onClose);
			// this._socket.off(Event.MESSAGE, this, this.onMessage);
			// this._socket.off(Event.ERROR, this, this.onError);
			// this._socket.offAll();
		}

		public onOpen(e: any = null): void {
			this._state = SocketState.OPEN;
			this._seq = 0;
			this._callbacks = {};
			this._errorHandlers = {};

			this._onConnected && this._onConnected.run();
			this._onConnected = null;
		}

		protected onMessage(res: any): void {
			if (this.byte) {
				this.byte.clear();
				if (res) this.byte.writeArrayBuffer(new Uint8Array(res));
				this.byte.pos = 0;
				if (this.byte.length < 4 || this.byte.length != this.byte.getInt16() + 4) {
					this.close();
				}
				this.byte.pos = 3;
				var cmd = this.byte.readByte();
				var potobufSetByteArray = this.byte.getUint8Array(4, this.byte.length - 4);
				if (this._messageHandler) {
					this._messageHandler.runWith([cmd, potobufSetByteArray]);
				}
			}
		}
		// private arr = [];
		public onMessage_onNetMsg(res: any): any {
			// this.arr.push(res);
			// Laya.LocalStorage.setJSON("watchData", this.arr)

			if (this.byte) {
				this.byte.clear();
				if (res) this.byte.writeArrayBuffer(new Uint8Array(res));
				var potobufSetByteArray = this.byte.getUint8Array(0, this.byte.length);
				// return potobufSetByteArray;
				if (this._messageHandler) {
					this._messageHandler.runWith([-10000, potobufSetByteArray]);
				}
			}
		}
		public onMessage_netResponseMsg(res: any): any {
			if (this.byte) {
				this.byte.clear();
				if (res) this.byte.writeArrayBuffer(new Uint8Array(res));
				if (this.byte.length < 15) return;
				
				this.byte.pos = 6;
                let gateWayCmd = this.byte.readByte();
	
                this.byte.pos = 10;
                var cmd = this.byte.readByte();

                this.byte.pos = 14;
                let code = this.byte.readByte();// 响应码
				
				yalla.Debug.log(code+'-'+gateWayCmd+'===新==NativeGameSOcket.onMessage_netResponseMsg=====cmd=='+cmd);
				var potobufSetByteArray = this.byte.getUint8Array(15, this.byte.length - 15);
				if (this._messageHandler) {
					this._messageHandler.runWith([cmd, potobufSetByteArray]);
				}
			}
		}

		/**这是需要返回的*/
		public onMessage_watch_new(res: any): any {
			// yalla.Debug.log(res)
			// this.arr.push(res);
			// Laya.LocalStorage.setJSON("watchData", this.arr)
			if (this.byte) {
				this.byte.clear();
				if (res) this.byte.writeArrayBuffer(new Uint8Array(res));
				if (this.byte.length < 15) return;
				
				this.byte.pos = 6;
                let gateWayCmd = this.byte.readByte();
	
                this.byte.pos = 10;
                var cmd = this.byte.readByte();

                this.byte.pos = 14;
                let code = this.byte.readByte();// 响应码
				
				yalla.Debug.log(code+'-'+gateWayCmd+'===老==onMessage_watch=====cmd=='+cmd);
				// var potobufSetByteArray = this.byte.getUint8Array(0, this.byte.length);
				var potobufSetByteArray = this.byte.getUint8Array(15, this.byte.length - 15);
				return [cmd, potobufSetByteArray];
				// if (this._messageHandler) {
				// 	this._messageHandler.runWith([cmd, potobufSetByteArray]);
				// }
			}
		}

		protected onClose(e: any): void {
			// yalla.Debug.log('Domino  Socket:' + ' closed. '+!this._messageHandler);

			this._callbacks = this._errorHandlers = null;
			this._state = SocketState.CLOSED;
			if (this._messageHandler) this._messageHandler.runWith(['__closed', this]);
			this._messageHandler = null;
		}

		protected onError(e: any): void {
			yalla.Debug.log('Domino  Socket' + ' error. '+!this._messageHandler);

			this._callbacks = this._errorHandlers = null;
			this._state = SocketState.ERROR;
			if (this._messageHandler) this._messageHandler.runWith(['__error', this]);
			this._messageHandler = null;
		}
	}
}