module yalla.data {

    export const CardWid = 68;          //宽度 高度=宽度*2

    export class GameState {
        public static State_Loading = 'Loading';            //加载
        public static State_Playing = 'Playing';            //游戏中
        public static State_ReLoading = 'ReLoading';        //重加载
        public static State_Sleep = 'Sleep';                //休眠
        public static State_Wake = 'Wake';                  //工作中
    }

    /**
     * 游戏中数据配置
     */
    export class GameConfig {
        public static Width = 750;
        public static Height = 1334;
        // public static Dice_CD = 10000                      //掷骰子cd时间5s
        public static DrewScore = 100;                     //积分模式总分
        public static MugginScore = 100;                   //轮次模式总分
        public static PlayCardSpeed = 1.6;                 //出牌的移动速度 0.9
        // public static ColorList: Array<string> = ['#f44c3c', '#0ad94c', '#f6b60a', '#5aadfd'];
        // public static NextRoundTime:number = 4;            //开始下一轮所需时间 

        // 左右 对应方向系数(以右为准， 左边的话把系数*-1) 奇数水平，偶数垂直
        public static RowHash = { 1: 1, 2: 1, 3: -1, 4: -1, 5: 1, 6: 1, 7: -1, 8: -1, 9: 1, 10: 1 };

        // 上下 对应方向系数(以下为准， 上边的话把系数*-1) 奇数垂直，偶数水平
        public static ColHash = { 1: 1, 2: 1, 3: -1, 4: -1, 5: 1, 6: 1, 7: -1, 8: -1, 9: 1, 10: 1 };

        public static typeNames: Array<string> = ["Draw Game", "All Five"];
    }

    /**
     * 28张牌
     */
    export const MapList = {
        1: { id: 1, tailNum: 0, headNum: 0 },
        2: { id: 2, tailNum: 1, headNum: 1 },
        3: { id: 3, tailNum: 2, headNum: 2 },
        4: { id: 4, tailNum: 3, headNum: 3 },
        5: { id: 5, tailNum: 4, headNum: 4 },
        6: { id: 6, tailNum: 5, headNum: 5 },
        7: { id: 7, tailNum: 6, headNum: 6 },

        8: { id: 8, tailNum: 0, headNum: 1 },
        9: { id: 9, tailNum: 0, headNum: 2 },
        10: { id: 10, tailNum: 0, headNum: 3 },
        11: { id: 11, tailNum: 0, headNum: 4 },
        12: { id: 12, tailNum: 0, headNum: 5 },
        13: { id: 13, tailNum: 0, headNum: 6 },

        14: { id: 14, tailNum: 1, headNum: 2 },
        15: { id: 15, tailNum: 1, headNum: 3 },
        16: { id: 16, tailNum: 1, headNum: 4 },
        17: { id: 17, tailNum: 1, headNum: 5 },
        18: { id: 18, tailNum: 1, headNum: 6 },

        19: { id: 19, tailNum: 2, headNum: 3 },
        20: { id: 20, tailNum: 2, headNum: 4 },
        21: { id: 21, tailNum: 2, headNum: 5 },
        22: { id: 22, tailNum: 2, headNum: 6 },

        23: { id: 23, tailNum: 3, headNum: 4 },
        24: { id: 24, tailNum: 3, headNum: 5 },
        25: { id: 25, tailNum: 3, headNum: 6 },

        26: { id: 26, tailNum: 4, headNum: 5 },
        27: { id: 27, tailNum: 4, headNum: 6 },

        28: { id: 28, tailNum: 5, headNum: 6 },
    }

    /**
     * domino 中使用的文本
     */
    export class TranslationD {
        static Game_Btn_Quit = 'Quit';
        static Game_Btn_Cancel = 'Cancel';
        static Game_Btn_Connect = 'Connect';
        static Game_Btn_Exit = 'Exit';
        static Game_Btn_Confirm = 'Confirm';
        static Game_Btn_Reconnect = 'Reconnect';
        static Game_Btn_Lobby = 'Lobby';

        static Game_Quit_Lose = 'Are you sure you want to quit the\ngame? You will lose bets!';
        static Game_Quit = 'Are you sure you want to quit the\ngame?';//主动退出游戏提示，这是私人房间
        static Game_Quit_Punish = 'Are you sure you want to quit the game? You will lose bets and may not be able to match the players normally for a while!';//主动退出时长小于规定时间的提示
        static Game_Quit_Watch = 'Are you sure you want to quit the game? You are watching the game.';

        static Game_Info_WinRate = 'Domino Win Rate';
        static Game_Info_Wins = 'Domino Wins';
        static Game_Info_Unmute = 'Unmute';
        static Game_Info_Mute = 'Mute';
        static Game_Title_Drew = 'Draw Game to';
        static Game_Title_Five = 'All Five to';

        static Game_LoginOut_Another_Content = 'Account has logged in another device.';
        static Game_LoginOut_KickOut_Content = 'You have been removed from the game due to prolonged use of AUTO mode.';
        static Game_LoginOut_KickOut_Content_Exception = 'Sorry, the game was terminated abruptly due to an unexpected system error.';
        static Game_LoginOut_Maintenance_Content = 'Server maintenance';
        static Game_LoginOut_Btn = 'I got it';

        static Game_Tip_Check_Microphone = 'Please check the setting of microphone.';
        static Game_Tip_Check_Microphone2 = "Please allow Yalla Ludo to access your microphone. Go to the phone \"Settings\" to allow it.";
        static Game_Tip_Fail_Microphone = 'Your microphone cannot work,\n please turn on it again.';
        static Game_Tip_Fail_BuyGood = 'Fail to purchase diamonds, please try again.';
        static Game_Tip_Add_Friend = 'Request sent, waiting for response';

        static Game_Error_LOGIN_FAILD_PW = 'LOGIN_FAILD_PW';
        static Game_Error_FAILD_OLDSIGN = 'ID error';
        static Game_Error_LOGIN_FAILD_SIGN = 'LOGIN_FAILD_SIGN';
        static Game_Error_LOGIN_FAILD_TIME = 'LOGIN_FAILD_TIME';
        static Game_Error_QUICKSTART_FAILD_NOROOM = 'QUICKSTART_FAILD_NOROOM';
        static Game_Error_ENTER_ROOM_FAILD_NOROOM = 'ENTER_ROOM_FAILD_NOROOM';
        static Game_Error_ENTER_ROOM_FAILD_ROOMID = 'ENTER_ROOM_FAILD_ROOMID';
        static Game_Error_GAMEOPERATE_FAILD_SIT_NOSTOOL = 'GAMEOPERATE_FAILD_SIT_NOSTOOL';
        static Game_Error_GAMEOPERATE_FAILD_SIT_WRONGSTOOLNUM = 'GAMEOPERATE_FAILD_SIT_WRONGSTOOLNUM';
        static Game_Error_GAMEOPERATE_FAILD_SIT_WRONGTIME = 'GAMEOPERATE_FAILD_SIT_WRONGTIME';
        static Game_Error_NO_MONEY = 'GAMEOPERATE_FAILD_SIT_WRONGTIME';
        static Game_Error_NO_DIAMOND = 'NO_DIAMOND';
        static Game_Error_ACCOUNT_BE_FROZEN = 'Your account has been frozen';
        static Game_Error_MAX_LIMIT = 'MAX_LIMIT';
        static Game_Error_GAME_FINISH = 'The game is finished';
        static Game_Error_NONE_ERROE = 'Fail to enter the room';


        static Champion_Lost_Game = 'You have lost the game! Please keep trying in the next game. ';
        static Champion_Quit_Game = 'You can take all the golds but you have to register again.';
        static Champion_CurGold = 'Current Golds';
        static Champion_Unlock_Lv = 'Unlocks at Level 5';
        static Champion_Battle = 'Battle';
        static Champion_Rule_1 = 'You can share 25% of the prize pool as extra bonus, if you win the final round.';
        static Champion_Rule_2 = 'After winning a round, you can either play next round, or quit the match with bonus.';
        static Champion_Rule_PrizePool = 'prize pool';
        static Champion_Toast_CheckNet = 'Please check your network connection';

        static Champion_Error_RIGISTER_LEVEL = 'Only players who reach level 5 can join the tournament';
        static Champion_Error_RIGISTER_NOMONEY = 'Insufficient Golds!';
        static Champion_Error_MATCH_NORIGISTER = "You haven't joined";
        static Champion_Error_MATCH_INMATCH = 'Matching';
        static Champion_Error_MATCH_INGAME = 'You are in the game';
        static Champion_Error_MATCHCANCEL_FAILED = 'Cancel failed';
        static Champion_Error_GETREWARD_GAMEOVER = 'You have collected the reward';
        static Champion_Error_RIGISTER_EXIST = 'You have joined the tournaments';
        static Champion_Error_RIGISTER_WRONGID = 'Sorry, you can not join the tournaments';
        static Champion_Error_FAILD_OUTTIME = 'Match Failed';
    }
}
