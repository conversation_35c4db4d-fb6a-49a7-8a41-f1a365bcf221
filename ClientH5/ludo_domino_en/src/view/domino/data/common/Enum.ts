module yalla.data {

	export class EnumCustomizeCmd {
		static Dominose_ShareResponse = 'Dominose_ShareResponse';		//分享回调
		static Dominose_Speaker = 'Dominose_Speaker';					//语音
		static Dominose_FilterCardPos = 'Dominose_FilterCardPos';		//点击手牌中可出的牌，筛选哪些位置可以出牌
		static Dominose_PlayCard = 'Dominose_PlayCard';					//出牌
		static Dominose_TurnCard = 'Dominose_TurnCard';					//翻牌
		// static Dominose_SelectMode = 'Dominose_SelectMode';			//选择模式
		// static Dominose_KeyboardPressEnter = 'Dominose_KeyboardPressEnter';	//键盘隐藏
		// static Dominose_SoundUpdate = 'Dominose_SoundUpdate';		//音效设置
		// static Dominose_RoundEnd = 'Dominose_RoundEnd';				//每一轮跑结束
		// static Dominose_Mute = 'Dominose_Mute';						//举报
		// static Dominose_UnMute = 'Dominose_UnMute';					//取消举报
		static Dominose_JoinAgora = 'Dominose_JoinAgora';				//加入语音聊天房间
		static Dominose_Back = 'Dominose_Back';							//返回上一层view
		static Server_Closed = 'Server_Closed';							//服务器断联
		static Server_Error = 'Server_Error';							//socket连接失败
		static Dominose_EnableAudio = 'Dominose_EnableAudio';			//打开音频
		static Dominose_DisableAudio = 'Dominose_DisableAudio';			//关闭音频

		static Dominose_PlayCardNext = 'Dominose_PlayCardNext';			//出牌、发牌结束
		static Dominose_MsgInManunal = 'Dominose_MsgInManunal';			//断网后 客户端模拟添加消息
		static Dominose_Chat_My = 'Dominose_70_My';        	 			//聊天
		static Dominose_ChatOFF_My = 'Dominose_ChatOFF_My';				//设置屏蔽聊天
		// static Dominose_PassdEnd = 'Dominose_PassdEnd';					//pass 结束

		static ResetInputTextFocus = "ResetInputTextFocus";				//取消layabox输入框焦点
	}
	export class EnumCmd {
		// 主命令
		static Dominose_Login = 'Dominose_10';            				//登录成功相应
		static Dominose_TicketLogin = 'Dominose_11';            		//票据登录成功相应（domino现在返回11，10不走，但是两个都监听）
		static Dominose_EnterRoom = 'Dominose_20';         				//进入房间
		static Dominose_QuitRoom = 'Dominose_21';         				//推出房间
		static Dominose_GAME_OPERATE = 'Dominose_30';					//游戏操作
		static Dominose_GameEvent = 'Dominose_31';        				//游戏操作响应
		static Dominose_OutGame = 'Dominose_126';        				//顶号
		static Dominose_Update_Player_Coin = 'Dominose_80';				//刷新用户金币钻石
		static Dominose_Update_Player_Level = 'Dominose_81';			//玩家升级
		static Dominose_Chat = 'Dominose_70';        	 				//聊天
		static Dominose_GetAgoraToken = 'Dominose_90';					//获取语音token
		static Dominose_Agora_Operate = 'Dominose_91'; 					//进入语音聊天房间
		static Dominose_GetZegoToken = 'Dominose_92';					//获取语音token
		static Dominose_FRIEND_LIST = 'Dominose_101'; 					//好友列表
		static Dominose_Gift_Cnf_List = 'Dominose_110';					//获取游戏内互动礼物配置列表 1.3.1 add
		static Dominose_Gift_Send = 'Dominose_111';						//游戏内互动礼物赠送, 1.3.1 add

		static Dominose_UserMessageEnvelope_old = 'UserMessageEnvelope_old';	//联赛观战老协议
		static Dominose_UserMessageEnvelope = 'UserMessageEnvelope';	//联赛观战
	}

	//游戏命令
	export enum Command {
		PLAYER_LOGIN = 10,//登陆
		QUICK_START = 20,//快速开始
		TICKET_LOGIN = 11,//票据登陆
		QUIT_ROOM = 21,//退出房间
		JOIN_ROOM = 22,//加入房间

		GAME_OPERATE = 30,//游戏操作
		GAME_EVENT = 31,//游戏操作

		PLAYER_CHAT_ALL = 70,//聊天
		UPDATA_PLAYER_COIN = 80,//刷新用户金币钻石
		UPDATA_PLAYER_LEVEL = 81,//玩家升级
		FLUSH_CURRENCY = 82,//刷新货币 参考domino

		GET_AGORA_TOKEN = 90,//获取语音频道token
		AGORA_OPERATE = 91,//语音频道操作
		GET_ZEGO_TOKEN = 92,
		FRIEND_ADD_MSG = 100,//添加好友
		FRIEND_LIST = 101,//好友列表
		FRIEND_INVITE = 102,//邀请好友
		GAME_GIFT_CNF_LIST = 110, // 获取游戏内互动礼物配置列表 1.3.1 add
		GAME_GIFT_SEND = 111, // 游戏内互动礼物赠送, 1.3.1 add


		OUT_GAME = 126,//踢出服务
		HEART = 127,//心跳包
	}
	export enum GameType {
		SNAKEANDLADER = 10020,//蛇棋
		DOMINO = 10021,//多米诺
		SHEEP = 10022,//顶
		BLACKANDWHITE = 10023,//黑白棋
	}

	export enum PlayerColor {
		RED = 0,
		YELLOW = 2,
		BLUE = 3,
		GREEN = 1,
		ORANGE = 4,
	}

	export enum ErrorCode {
		NONE_ERROE = 1,//无错误
		LOGIN_FAILD_PW = 101,//密码错误
		LOGIN_FAILD_ID = 102,//登陆ID错误
		LOGIN_FAILD_OLDSIGN = 103,//重复的签名
		LOGIN_FAILD_SIGN = 104,//签名错误
		LOGIN_FAILD_TIME = 105,//客户端时间错误
		QUICKSTART_FAILD_NOROOM = 203,//房间已满
		ENTER_ROOM_FAILD_NOROOM = 204,//房间已满
		ENTER_ROOM_FAILD_ROOMID = 205,//错误的房间号
		GAMEOPERATE_FAILD_SIT_NOSTOOL = 301,// 已有人
		GAMEOPERATE_FAILD_SIT_WRONGSTOOLNUM = 302,//错误的凳子ID
		GAMEOPERATE_FAILD_SIT_WRONGTIME = 303,//错误的操作时间
		TRUST_PUNISHMENT_KICKED_OUT = 307,//玩家因为托管惩罚被踢出游戏
		NO_MONEY = 401,//余额不足
		NO_DIAMOND = 402,//钻石不足 老
		ACCOUNT_BE_FROZEN = 403,//钻石不足 新

		MAX_LIMIT = 601,//超出限制
		JOIN_ROOM_FAILD_NOROOM = 701,//无此房间
		JOIN_ROOM_FAILD_MAX = 702,//房间人数已满
		USERINFO_ERR = 901, //用户信息错误
		DOMINO_PLAY_NOTALLOW = 902, //当前不允许出牌
		GET_DOMINO_ERR = 903, //获取骨牌出错
		DOMINO_HEAD_FORBID = 904, //头部不允许再放牌
		DOMINO_TAIL_FORBID = 905, //尾部不允许再放牌
		DOMINO_CONNECT_ERR = 906, //不能相连
		DOMINO_REMAIN_NULL = 907, //补牌区已空
		DOMINO_REMAIN_EXSITERR = 908, //此牌已存在
		DOMINO_PLAY_ERR = 909, //出牌错误
		GAME_HAS_FINISH = 1000, //房间已经结束

		MATCH_GAME_FAILD_OUTTIME = 1104,//匹配超时
		CHAMPIONSHIP_RIGISTER_EXIST = 1900,//已注册
		CHAMPIONSHIP_RIGISTER_WRONGID = 1901,//注册传入错误的锦标赛id
		CHAMPIONSHIP_RIGISTER_LEVEL = 1902,//等级不够
		CHAMPIONSHIP_RIGISTER_NOMONEY = 1903,//金币不足

		CHAMPIONSHIP_MATCH_NORIGISTER = 1920,//未报名
		CHAMPIONSHIP_MATCH_INMATCH = 1921,//匹配中
		CHAMPIONSHIP_MATCH_INGAME = 1922,//游戏中
		CHAMPIONSHIP_MATCH_UNKNOWN = 1924,//未知错误
		CHAMPIONSHIP_MATCHCANCEL_FAILED = 1930,//取消失败

		CHAMPIONSHIP_GETREWARD_NORIGISTER = 1940,//未报名
		CHAMPIONSHIP_GETREWARD_INMATCH = 1941,//匹配中
		CHAMPIONSHIP_GETREWARD_INGAME = 1942,//游戏中
		CHAMPIONSHIP_GETREWARD_GAMEOVER = 1943,//已结算
	}
	//游戏状态
	export enum GameStatus {
		GAME_PREPARE = 0,//开始报名
		GAME_START = 1,//游戏开始
		GAME_ING = 2,//游戏中
		GAME_RESULT = 3,//游戏结果
		GAME_END = 4,//游戏结束
		GAME_ROUNT_RESULT = 5, //游戏单局结果
	}
	//操作结果
	export enum OperateResult {
		FAILED = 0,//失败
		SUCCESS = 1,//成功
	}

	//游戏操作
	export enum GameOperate {
		SIT = 0,//入座
		UP = 1,//离开座位
		SIGN_UP = 2,//准备
		SIGN_CANCEL = 3,//取消准备
		THROW = 4,//掷骰子
		RESET_THROW = 5,//重置投掷
		CHOOSE_CHESS = 6,//选择棋子
		CHESS_MOVE = 7,//棋子移动
		SYSTEM_TRUST = 8,//系统托管
		SYSTEM_TRUST_CANCEL = 9,//取消系统托管
		DOMINO_PLAY = 10, //多米诺出牌
		FILL_DOMAIN_CARD = 11, //补牌
		GRADE_ADD = 14, //得分通知
		DESK_DOMINO_HEAD_FORBID = 15, //头部禁止放牌
		DESK_DOMINO_TAIL_FORBID = 16, //尾部禁止放牌
		DOMINO_PLAYER_CHANGE = 17, //不能连接骨牌，切换玩家
		DESK_DOMINO_LEFT_FORBID = 18, //左部禁止放牌
		DESK_DOMINO_RIGHT_FORBID = 19, //右部禁止放牌
		DOMINO_RESTART = 20,//多米诺重新开始
		DOMINO_PASS = 21,//pass
		DOMINO_ADD_ROUNDTIME = 22,//增加回合时间

		CHAT_OFF = 23,//屏蔽聊天
		CHAT_OFF_CANCEL = 24,//取消屏蔽聊天
	}

	export enum GameEvent {
		GAME_STATE_CHANGE = 1,//游戏状态改变
		GAME_COLOR_SELECT = 2,//玩家颜色选择
		GAME_PLAYER_STATUS_CHANGE = 3,//用户状态改变
		GAME_PLAYER_THROW = 4,//玩家掷骰子
		GAME_SHOW_RESULT = 5,//游戏结果
		SAL_CHESS_MOVE = 7,//蛇棋棋子移动
		SAL_CHESS_MOVE_SNAKE = 8,//蛇棋🐍移动
		SAL_CHESS_MOVE_LADDER = 9,//蛇棋梯子移动
		SAL_CHESS_BORN = 10,//蛇棋棋子移动到初始点
		DOMINO_AUTO_PLAY = 11,//多米诺自动出牌
		DOMINO_AUTO_FILL = 12,//多米诺自动补牌
		DOMINO_INFO = 13,
		DOMINO_ADD_ROUNDTIME = 14,//延时
		CHAT_OFF = 15,//屏蔽聊天
	}

	export enum EnumPlayerTurnStatus {
		THROW_START = 0,				//开始投掷
		THROW_END = 0,				//开始投掷
		RETHROW_START = 0,				//开始投掷
		RETHROW_END = 0,			//开始投掷
		CHOOSECHESS_START = 0,				//开始投掷
		CHOOSECHESS_END = 0,				//开始投掷
		CHESSMOVE_START = 0,				//开始投掷
		CHESSMOVE_END = 0,				//开始投掷
		DOMINO_PLAY_START = 8, //多米诺出牌
		DOMINO_FILL_CARD = 9, //多米诺补牌
		DOMINO_PASS_SELF = 10,
	}

	export enum ChessEvent {
		BORN = 1,
		MOVE = 2,
		SNAKE = 3,
		LADDER = 4,
	}

	export enum ExitType {
		INITIATIVE_QUIT = 1,			//主动退出
		GAMEOVER_QUIT = 2,				//游戏结束
		SOCKETFAILED_QUIT = 3,			//socket中断，失败
		LOGGED_ANOTHER_QUIT = 4,		//其他地方登陆
		KICKED_QUIT = 5,				//被踢
		SERVER_MAINTENANCE_QUIT = 6,	//服务器维护
		JOIN_OTHER_GAME_QUIT = 7,		//接受好友，加入其它游戏
		PLAY_AGAIN = 8,					//再来一局
	}

	export enum ChampoinEvent {
		CHAMPION_INFO = 1,//获取锦标赛信息&用户信息
		SIGNUP = 2,//报名
		SIGNUP_QUIT = 3,//放弃
		MATCH = 4,//匹配
		MATCH_CANCEL = 5,//取消匹配
		MATCH_SUCCESS = 6,//更新匹配信息匹配成功
		CHAMPION_RESULT = 7,//胜负通知
	}

	export enum ChampionshipStatus {
		NO_SIGNUP = 0,				//0未报名
		MATCH = 1,					//1可匹配
		MATCH_ING = 2,				//2比赛中
		GAME_ING = 3,				//3游戏中
		RESULT = 4					//4通关可领奖
	}

	export enum DirType {
		TOP = 1,
		BOTTOM = 2,
		LEFT = 3,
		RIGHT = 4
	}

	export enum HorV {
		VERTICAL = 0,
		HORIZ = 1
	}


}