module yalla.data {

    export class User extends yalla.data.DataItem {
        public idx: number;                          //用户idx
        public playerShowInfo: playerShowInfo;
        public gold: number = 0;                       //钻石
        public money: number = 0;                      //金币 游戏玩法消耗
        public isRecharged: boolean;
        public exp: number = 0;

        constructor() {
            super();
        }

        public update(d: Object): void {
            super.update(d);
            yalla.Global.Account.currentGoldNum = this.money;
            yalla.Global.Account.currentDiamondNum = this.gold;
        }

        /**
         * 更新coin
         * @param d 
         */
        public updateCoin(d: any): void {
            if (!d) return;
            switch (d.type) {
                case 0:
                    this.money = d.value;
                    yalla.Global.Account.currentGoldNum = this.money;
                    break;
                case 1:
                    this.gold = d.value;
                    yalla.Global.Account.currentDiamondNum = this.gold;
                    break;
            }
        }

        /**
         * 更新player level exp
         * @param d 
         */
        public updatePlayerLevel(d: any): void {
            if (!d) return;
            if (d.level) this.playerShowInfo.fPlayerInfo.level = d.level;
            if (d.exp) this.exp = d.exp;
        }

        /**
         * 更新coin
         * @param d 
         */
        public addCoin(d: any): void {
            if (!d) return;
            if (d.diamond > 0) {
                this.gold += parseInt(d.diamond);
                yalla.Global.Account.currentDiamondNum = this.gold;
            }
            if (d.money > 0) {
                this.money += parseInt(d.money);
                yalla.Global.Account.currentGoldNum = this.money;
            }
        }

        /**
         * 更新是否屏蔽聊天
         */
        public updateChatOff(state: number): void {
            this.playerShowInfo && (this.playerShowInfo.isInChatOff = state);//1 - this.playerShowInfo.isInChatOff;
        }
    }
}