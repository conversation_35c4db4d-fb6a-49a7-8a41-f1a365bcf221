module yalla.data {
    import EventDispatcher = laya.events.EventDispatcher;

    export class UserService extends EventDispatcher {
        static _instance: UserService;
        public _user = new User();
        // private _client;

        constructor() {
            super();
            // if (yalla.util.IsBrowser()) {
            //     this._client = yalla.net.Client.instance;
            // } else {
            //     this._client = yalla.net.NativeClient.instance;
            // }
        }

        static get instance(): UserService {
            return UserService._instance || (UserService._instance = new UserService());
        }

        public register(): void {
            if (!this._user) this._user = new User();
            // this._client.on(yalla.data.EnumCmd.Dominose_Login, this, this.handleLoginGame);
            // this._client.on(yalla.data.EnumCmd.Dominose_OutGame, this, this.handleOutGame);
        }

        public clear(): void {
            // this._client.off(yalla.data.EnumCmd.Dominose_Login, this, this.handleLoginGame);
            // this._client.off(yalla.data.EnumCmd.Dominose_OutGame, this, this.handleOutGame);
        }

        public get user() { return this._user; }

        //-----------------------------------------handle=------------------------------------
        /**
         * 成功登录游戏
         * @param d 
         */
        // public handleLoginGame(d: any, isDecode: boolean = true): void {
        //     var msg;
        //     if (isDecode) msg = this._client.decodeMsg(d, "LoginResponse");
        //     else msg = d;
        //     console.log(msg);

        //     if (!msg) {
        //         yalla.common.WaringDialog.showResultError(yalla.data.ErrorCode.NONE_ERROE, yalla.data.UserService.instance.backHall);
        //         return;
        //     }
        //     if (msg.result == 1) {
        //         this._user.idx = msg.palyerInfo.playerShowInfo.fPlayerInfo.idx;
        //         this._user.update(msg.palyerInfo);
        //         yalla.Global.Account.banTalkData = msg.banTalkData || "";
        //         yalla.Global.Account.voiceType = msg.voiceType || VoiceType.Agora;//1.2.8 add 2021年11月1日10:02:06
        //         // yalla.Global.Account.voiceType =  VoiceType.Agora;

        //         this.event(yalla.data.EnumCmd.Dominose_Login, msg);
        //     } else {
        //         yalla.Native.instance.removeMatchView();
        //         yalla.Global.IsGameOver = true;
        //         yalla.common.WaringDialog.showResultError(msg.code, yalla.data.UserService.instance.backHall);
        //     }
        // }

        // public handleOutGame(d: any, isDecode: boolean = true): void {
        //     var msg;
        //     if (isDecode) msg = this._client.decodeMsg(d, "OutGameResponse");//其它地方登陆此号
        //     else msg = d;
        //     if (!msg) return;
        //     var reason = msg.reason;
        //     if (msg.idx && msg.idx == yalla.data.UserService.instance.user.idx) yalla.Global.IsGameOver = true;
        //     VoiceService.instance.levelGameRoom();//TODO::1.3.4 因被顶号后backhall有概率退出语音房释放，这里再次执行levelGameRoom
        //     this._client && this._client.clear();
        //     switch (reason) {
        //         case 1:
        //             yalla.Debug.log('别处登陆');
        //             yalla.util.sendExitType(yalla.data.ExitType.LOGGED_ANOTHER_QUIT);
        //             // this.showDialog('loginOut', TranslationD.Game_LoginOut_Another_Content, [TranslationD.Game_LoginOut_Btn]);
        //             yalla.Native.instance.alertAccountLoggedView(() => {
        //                 yalla.Native.instance.backHall(false);
        //             });
        //             break;
        //         case 3:
        //             yalla.Debug.log('被踢');
        //             yalla.DialogManager.instance.showDialog('loginOut', TranslationD.Game_LoginOut_KickOut_Content, [TranslationD.Game_LoginOut_Btn]);
        //             yalla.util.sendExitType(yalla.data.ExitType.KICKED_QUIT);
        //             break;
        //         case 2:
        //             yalla.Debug.log('服务器维护');
        //             yalla.DialogManager.instance.showDialog('loginOut', TranslationD.Game_LoginOut_Maintenance_Content, [TranslationD.Game_LoginOut_Btn]);
        //             yalla.util.sendExitType(yalla.data.ExitType.SERVER_MAINTENANCE_QUIT);
        //             break;
        //     }
        // }

        // private handleUpdatePlayLevel(d: any): void  {
        //     var oldLv = this._user.playerShowInfo.fPlayerInfo.level;

        //     var msg = yalla.net.Client.instance.decodeMsg(d, "MassagePlayerLevelUPResponse");
        //     this._user.updatePlayerLevel(msg);
        //     var nowLv = this._user.playerShowInfo.fPlayerInfo.level;

        //     if (nowLv > oldLv) {
        //         this.LevelUp = true;
        //     }
        // }

        // public showDialog(name: string, content: string, btnNameList: Array<string> = []): void {
        //     var caller = this;//name.length > 0 ? yalla.Native.instance : this;
        //     yalla.Native.instance.removeMatchView();
        //     yalla.common.Confirm.instance.showConfirm(content, Laya.Handler.create(caller, () => {
        //         yalla.Native.instance.backHall(false);
        //         if (name.length > 0) yalla.Native.instance[name]();
        //     }), null, btnNameList);
        // }

        /**
         * @param isRun 是否逃跑   true，返回大厅，false根据情况定
         */
        public backHall(isRun: boolean = false, delayTime: number = 0): void {
            var client = yalla.data.RoomService.instance.client;
            yalla.data.VoiceService.instance.clear();
            // this._client && this._client.clear();
            if (client) client.clear();
            yalla.Debug.log(yalla.Global.Account);
            yalla.Debug.log("===UserService.backHall===isPrivate=" + yalla.Global.Account.isPrivate);

            if (yalla.Global.Account && yalla.Global.Account.leagueWatch) {//TODO::backhall 需要把roomservice数据清理，尤其settimeout
                yalla.Global.IsGameOver = true;
                yalla.data.RoomService.instance.clear();
            }
            yalla.util.backHall(isRun, yalla.data.RoomService.instance.room.playerNums, delayTime);
        }
    }
}