//多米诺
interface Domino {
    id:number,
    headNum:number, //首数
    tailNum:number, //尾数
    side:number,
    leftDominoId:number,
    rightDominoId:number,
    bottomDominoId:number,
    topDomainoId:number,
    deskSort:number;
}

//SideObject
interface SideObject{
    leftDominoId:number,
    rightDominoId:number,   //左侧多米诺ID
    topDomainoId:number,    //上侧多米诺ID
    bottomDominoId:number,  //下侧多米诺ID
}

interface PlayerAndDominoes {
    dominoes: Array<Domino>,
    playerId:number
}

//多米诺单局结果
interface DominoRoundInfo {
    winnerId: number,
    winnerNickName: string,
    winnerIntergral: number,            //成功者总积分
    roundIntergral: number,             //本剧获得积分
    roundNum: number,                   //局数
    winnerDomino: Array<Domino>,        //成功者剩余手牌
    winnerRoundIntergral:number;        //优胜玩家本局得分
    losersInfo:Array<PlayerDomino>,     //失败玩家剩余多米诺
    winnerExp:number                    //本局经验
}

//多米诺整局结果
interface DominoGameResult {
    winnerId:number,                    //优胜玩家
    winnerNickName:string,              //优胜玩家昵称
    winGoldNum:number,
    winnerIntergral:number,
    winnerRoundIntergral:number,        //优胜玩家本局得分
    roundIntergral:number,              //本局积分
    roundNum:number,                    //局数
    winnerDomino: Array<Domino>,        //成功者剩余手牌
    losersInfo:PlayerDomino,            //失败玩家剩余多米诺
    winnerExp:number                    //本局经验    (ps:可能最终赢的玩家不是本轮的胜利一方, 服务端为不更改结构前提下，增加winnerExp 和 PlayerDomino的exp)
}

interface PlayerDomino {
    playerId:number,
    playerNickName:string,
    playerIntergral:number,
    playerRoundIntergral:number,
    dominoes:Domino,
    exp:number                   //经验
}

/**
 * 自定义格式
 */
interface DefineRoundResult{
    idx:number,
    intergral:number,
    roundIntergral:number,
    domino: Array<Domino>,
    nickName:string,
    winnerExp:number,
    exp:number
}