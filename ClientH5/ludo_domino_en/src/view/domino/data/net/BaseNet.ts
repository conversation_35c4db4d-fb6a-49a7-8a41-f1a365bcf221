module yalla.data {

    export class BaseNet {

        public _client;
        // public _room: Room = new Room();
        public msgQueueList: Array<any> = [];             //队列消息
        public selectCardList: Array<number> = [];
        public _isDominoInfo: boolean = false;

        /**未收到cmd=20 快速进入房间消息 存放的消息列表 */
        public _readyMsgList: Array<any> = [];
        public _isReady: boolean = false;

        constructor() {

        }

        public register(): void {
            if (this._client) {
                this._client.on('UserMessageEnvelope_old', this, this.userMessageEnvelope_old, null);
                this._client.on('UserMessageEnvelope', this, this.userMessageEnvelope, null);
            }

            if (Global.Account.leagueWatch) {
                NativeWebSocket.instance.on("onWatchError", this, this.onWatchError);
            }
        }
        public ticketLogin(): void { }
        public quitRoom(): void { }
        /**
         * 设置聊天屏蔽  
         * @param chatOffType false取消屏蔽   true设置屏蔽
         */
        public sendChatOff(isChatOff: number): void { }
        public sendPlayCard(d: Domino, deskSort: number = 1): void { 
            // yalla.CLogDBA.instance.record_request(yalla.CLogDBA_EventID.Domino_PlayCard_Resp);
        }
        public sendPass(): void { }
        public sendNexgRound(): void { }
        public sendPlayerCoin(type: number = 0): void { }
        public cancleTrust() { }
        public sendChat(msgResult: any = {}): void { }
        public sendAddRoundTime(): void { 
            yalla.CLogDBA.instance.record_request(yalla.CLogDBA_EventID.Domino_Roundtime_Resp);
        }
        public getGiftCnfList(): void { }
        public sendGift(giftId: number, rcvIdxs: Array<any>): void { }
        public GameResponse(code: any, msg: any, cmd, businessOrder): void { }
        /**
         * 获取语音房间token
         */
        public getToken() { }

        /**
         * 成功登录游戏
         * @param d
         * 登陆返回错误，直接返回大厅
         */
        public handleLoginGame(d: any, isDecode: boolean = true): void {
            var msg;
            if (isDecode) msg = this._client.decodeMsg(d, "LoginResponse");
            else msg = d;
            yalla.Debug.log("======BaseNet 登陆响应LoginResponse======");
            yalla.Debug.log(msg);

            if (!msg) {
                yalla.common.WaringDialog.showResultError(ErrorCode.NONE_ERROE, () => {
                    UserService.instance.backHall(true);
                });
                return;
            }
            if (msg.result == 1) {
                var user = UserService.instance.user;
                user.idx = msg.palyerInfo.playerShowInfo.fPlayerInfo.idx;
                user.update(msg.palyerInfo);
                yalla.Global.Account.banTalkData = msg.banTalkData || "";
                yalla.Global.Account.voiceType = msg.voiceType || VoiceType.Agora;//1.2.8 add 2021年11月1日10:02:06

                // this.event(yalla.data.EnumCmd.Dominose_Login, msg);
                yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_Login, msg);
            } else {
                Native.instance.removeMatchView();
                Global.IsGameOver = true;
                yalla.common.WaringDialog.showResultError(msg.code, () => {
                    UserService.instance.backHall(true);
                });
            }
        }

        public handleOutGame(d: any, isDecode: boolean = true): void {
            if (!this._client) return;
            var msg;
            if (isDecode) msg = this._client.decodeMsg(d, "OutGameResponse");//其它地方登陆此号
            else msg = d;
            if (!msg) return;
            var reason = msg.reason;
            if (msg.idx && msg.idx == UserService.instance.user.idx) Global.IsGameOver = true;
            VoiceService.instance.levelGameRoom();//TODO::1.3.4 因被顶号后backhall有概率退出语音房释放，这里再次执行levelGameRoom
            this._client && this._client.clear();
            switch (reason) {
                case 1:
                    yalla.Debug.log('别处登陆');
                    yalla.util.sendExitType(ExitType.LOGGED_ANOTHER_QUIT);
                    // this.showDialog('loginOut', TranslationD.Game_LoginOut_Another_Content, [TranslationD.Game_LoginOut_Btn]);
                    Native.instance.alertAccountLoggedView(() => {
                        yalla.Native.instance.backHall(false);
                    });
                    break;
                case 3:
                    yalla.Debug.log('被踢');
                    DialogManager.instance.showDialog('loginOut', TranslationD.Game_LoginOut_KickOut_Content, [TranslationD.Game_LoginOut_Btn]);
                    yalla.util.sendExitType(ExitType.KICKED_QUIT);
                    break;
                case 2:
                    yalla.Debug.log('服务器维护');
                    DialogManager.instance.showDialog('loginOut', TranslationD.Game_LoginOut_Maintenance_Content, [TranslationD.Game_LoginOut_Btn]);
                    yalla.util.sendExitType(ExitType.SERVER_MAINTENANCE_QUIT);
                    break;
            }
        }

        public handleEndterRoom(d: any, isDecode: boolean = true): void {
            if (!this._client) return;
            yalla.Debug.log('---handleEndterRoom----');
            var msg;
            if (isDecode) msg = this._client.decodeMsg(d, "QuickEnterRoomResponse");//收到当前房间的玩家的状态 之前进入的玩家信息
            else msg = d;
            if (!msg) return;
            yalla.CLogDBA.instance.clear();

            this._isDominoInfo = false;
            this.msgQueueList = [];
            if (msg.result == 1) {
                if (msg.status == GameStatus.GAME_END) {//进入房间，但是游戏已经结束，需要提示
                    Global.IsGameOver = true;
                    yalla.common.WaringDialog.showResultError(ErrorCode.GAME_HAS_FINISH, UserService.instance.backHall);
                    Native.instance.mobClickEvent(buryPoint.DGAME_BREAK_GAMEOVER);
                }

                Global.ProtoEnterRoomMsgIndex = Global.ProtocolMsgIndex = 0;
                yalla.Debug.log('=====QuickEnterRoom===11====')
                yalla.Debug.log(msg);
                var user = UserService.instance.user;
                RoomService.instance._room.initData(msg);
                RoomService.instance._room.leftDiamond = user.gold;//默认是登陆返回的gold
                user.update({ playerShowInfo: RoomService.instance._room.getPlayerByIdx(user.idx) });
                Global.Account.isPrivate = RoomService.instance._room.isPrivate;
                if (user.playerShowInfo) {
                    Global.Account.roylevel = user.playerShowInfo.roylevel;
                    Global.Account.realRoyLevel = user.playerShowInfo.realRoyLevel;
                }
                if (msg.hasOwnProperty('autoAddExp')) yalla.Global.Account.autoAddExp = msg.autoAddExp;

                // this.event(yalla.data.EnumCmd.Dominose_EnterRoom, msg);
                yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_EnterRoom, msg);

                this.sendChatOff(ludo.muteSpectator.isMute);//TODO::避免设置取消屏蔽协议发送失败（但本地已改），导致房间内同个玩家状态不一致
                this.getGiftCnfList();
            } else {
                yalla.common.WaringDialog.showResultError(msg.code, () => {
                    UserService.instance.backHall(true);
                });
            }

            this._client.resetMsg();//避免内存过大
        }

        public handleQuitRoom(d: any, isDecode: boolean = true): void {
            if (!this._client) return;
            var msg;
            if (isDecode) msg = this._client.decodeMsg(d, "QuitRoomResponse");//收到当前房间的玩家的状态 之前进入的玩家信息
            else msg = d;
            if (!msg) return;
            if (msg.idx == UserService.instance.user.idx) yalla.Global.IsGameOver = true;
            if (msg.result == 1) {
                yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_QuitRoom, msg);
                // this.event(yalla.data.EnumCmd.Dominose_QuitRoom, msg);
            } else {
                yalla.common.WaringDialog.showResultError(msg.code, UserService.instance.backHall);
            }
        }

        public handleGameEvent(d: any, isdecode: boolean = true): void {
            if (!this._client || !d) return;
            var msg = isdecode ? this._client.decodeMsg(d, "GameEventResponse") : d;
            if (!msg) return;

            var status = msg.status;
            if (!Global.IsGameOver && status == GameStatus.GAME_END) {//进入房间，但是游戏已经结束，需要提示
                Global.IsGameOver = true;
                yalla.common.WaringDialog.showResultError(ErrorCode.GAME_HAS_FINISH, UserService.instance.backHall);
                Native.instance.mobClickEvent(buryPoint.DGAME_BREAK_GAMEOVER);
            }
            if (msg.code == ErrorCode.ACCOUNT_BE_FROZEN) {
                yalla.common.Confirm.instance.showConfirm(TranslationD.Game_Error_ACCOUNT_BE_FROZEN, Laya.Handler.create(this, () => {
                    yalla.common.Confirm.instance.hideConfirm();
                }), null, [TranslationD.Game_Btn_Confirm]);
            }
            switch (msg.event) {
                case GameEvent.GAME_STATE_CHANGE://游戏开始
                    RoomService.instance._room.update(msg);
                    yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    // this.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    break;
                case GameEvent.DOMINO_INFO:
                    yalla.Debug.log("====DOMINO_INFO====");
                    yalla.Debug.log(msg);
                    //牌信息（桌牌&手牌），插入队列最前面
                    if (Global.ProtoEnterRoomMsgIndex <= 0) {
                        this.msgQueueList.unshift(msg);
                        RoomService.instance._room.reset();
                        RoomService.instance._room.update(msg);
                    }
                    if (msg.hasOwnProperty('score')) RoomService.instance._room.initScore(msg.score);

                    // this.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    break;
                case GameEvent.GAME_PLAYER_STATUS_CHANGE:
                    yalla.Debug.log("===切换用户消息====");
                    yalla.Debug.log(msg);    
                    //切换玩家
                    this.msgQueueList.push(msg);
                    RoomService.instance._room.updateRoundTime(msg);
                    // this.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    break;
                case GameEvent.DOMINO_AUTO_PLAY:
                    yalla.Debug.log("===返回出牌消息====");
                    yalla.Debug.log(msg);
                    //出牌
                    this.msgQueueList.push(msg);
                    RoomService.instance._room.updatePlayerScore(msg.idx, msg.connectScore, 1);
                    RoomService.instance._room.removeHandCard(msg.idx, msg.autoPlayDomino);
                    this.selectCardList = [];
                    // this.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);

                    // yalla.CLogDBA.instance.record_response(yalla.CLogDBA_EventID.Domino_PlayCard_Resp);
                    break;
                case GameEvent.DOMINO_AUTO_FILL:
                    //补牌
                    yalla.Debug.log("===补牌消息====");
                    yalla.Debug.log(msg);
                    this.msgQueueList.push(msg);
                    RoomService.instance._room.addHandCard(msg.idx, msg.autoFillDomino);
                    // this.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    break;
                case GameEvent.DOMINO_ADD_ROUNDTIME:
                    //延时
                    this.msgQueueList.push(msg);
                    RoomService.instance._room.updateRoundTime(msg);
                    // this.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    yalla.CLogDBA.instance.record_response(yalla.CLogDBA_EventID.Domino_Roundtime_Resp);
                    break;
                case GameEvent.GAME_SHOW_RESULT:
                    //结算（单局 & 总结算）
                    var result;
                    if (msg.roundResut) {
                        yalla.Debug.log('======小局结算数据========');
                        yalla.Debug.log(msg);
                        result = msg.roundResut;
                    } else if (msg.gResut) {
                        yalla.Debug.log('======游戏结算数据========');
                        yalla.Debug.log(msg);
                        result = msg.gResut;
                        yalla.Global.IsGameOver = true;
                    }

                    if (result) {
                        RoomService.instance._room.updatePlayerScore(result.winnerId, result.winnerIntergral);
                        var losersInfo = result.losersInfo;
                        for (var i = 0; i < losersInfo.length; i++) {
                            RoomService.instance._room.updatePlayerScore(losersInfo[i].playerId, losersInfo[i].playerIntergral);
                        }
                    }
                    RoomService.instance._room.reset();
                    this._isDominoInfo = false;
                    this.msgQueueList = [];
                    this._client.resetMsg();
                    // this.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    break;
                default:
                    // this.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_GameEvent, msg);
                    break;
            }
        }

        /**
         * 游戏操作
         * @param d 
         */
        public handleGameOperateResponse(d: any, isDecode: boolean = true): void {
            if (!this._client) return;
            var msg;
            if (isDecode) msg = this._client.decodeMsg(d, "GameOperateResponse");
            else msg = d;
            if (msg && msg.code == ErrorCode.ACCOUNT_BE_FROZEN) {
                yalla.common.Confirm.instance.showConfirm(TranslationD.Game_Error_ACCOUNT_BE_FROZEN, Laya.Handler.create(this, () => {
                    yalla.common.Confirm.instance.hideConfirm();
                }), null, [TranslationD.Game_Btn_Confirm]);
            }
            if (!msg || msg.result == 0) {
                return;
            }
            var op = msg.op;
            switch (op) {
                case GameOperate.DOMINO_PLAY:
                    // yalla.CLogDBA.instance.record_response(yalla.CLogDBA_EventID.Domino_PlayCard_Resp);
                case GameOperate.DOMINO_PASS:
                    this.msgQueueList.push(msg);
                    break;
                case GameOperate.DOMINO_ADD_ROUNDTIME:
                    RoomService.instance._room.updateRoundTime(msg);
                    yalla.CLogDBA.instance.record_response(yalla.CLogDBA_EventID.Domino_Roundtime_Resp);
                    break;
                case GameOperate.CHAT_OFF://聊天屏蔽
                    UserService.instance.user.updateChatOff(1);
                    break;
                case GameOperate.CHAT_OFF_CANCEL://聊天屏蔽取消
                    UserService.instance.user.updateChatOff(0);
                    break;
            }
            // this.event(yalla.data.EnumCmd.Dominose_GAME_OPERATE, msg);
            yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_GAME_OPERATE, msg);
        }

        public handleUpdatePlayCoin(d: any, isDecode: boolean = true): void {
            if (!this._client) return;
            var user = UserService.instance.user;
            var msg;
            if (isDecode) msg = this._client.decodeMsg(d, "UpdataCoinResponse");
            else msg = d;
            user.updateCoin(msg);

            if (RoomService.instance._room.leftDiamond != user.gold) {
                RoomService.instance._room.leftDiamond = user.gold;
                // this.event(yalla.data.EnumCmd.Dominose_Update_Player_Coin, msg);
                yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_Update_Player_Coin, msg);
            }
        }

        public handleChat(d: any): void {
            if (!this._client) return;
            var msg = this._client.decodeMsg(d, "GameRoomPlayerChatToAllRequestAndResponse");
            // this.event(yalla.data.EnumCmd.Dominose_Chat, msg);
            yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_Chat, msg);
        }

        public handleUpdateGiftCnf(d: any): void {
            if (!this._client) return;
            var msg = this._client.decodeMsg(d, "GameGiftCnfListRespone");
            yalla.Debug.log('==domino handleUpdateGiftCnf==');
            yalla.Debug.log(msg);
            // this.event(yalla.data.EnumCmd.Dominose_Gift_Cnf_List, msg);
            yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_Gift_Cnf_List, msg);
        }

        public handleRevGift(d: any, isDecode: boolean = true): void {
            if (!this._client) return;
            var msg;
            if (isDecode) msg = this._client.decodeMsg(d, "GameGiftSendResponse");
            else msg = d;
            // yalla.Debug.log('==domino handleRevGift==');
            // yalla.Debug.log(msg);
            if (msg.code > 1) {
                yalla.common.InteractiveGift.Instance.checkError(msg.code);
                return;
            }

            if (msg.senderIdx == yalla.Global.Account.idx) {
                var user = UserService.instance.user;
                if (msg.hasOwnProperty('senderMoney')) user.updateCoin({ type: 0, value: msg.senderMoney });
                if (msg.hasOwnProperty('senderDiamond')) user.updateCoin({ type: 1, value: msg.senderDiamond });
            }
            yalla.common.InteractiveGift.Instance.pushMsg(msg);
            // this.event(yalla.data.EnumCmd.Dominose_Gift_Send, msg);
            yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_Gift_Send, msg);
        }

        public handleGetAgoraToken(d: any, isDecode: boolean = true): void {
            if (!this._client) return;
            var msg = this._client.decodeMsg(d, "GetAgoraTokenResponse");
            VoiceService.instance.handleGetAgoraToken(msg);
        }

        public handleGetZegoToken(d: any, isDecode: boolean = true): void {
            if (!this._client) return;
            var msg = this._client.decodeMsg(d, "GetZegoTokenResponse");
            VoiceService.instance.handleGetZegoToken(msg);
        }

        /**
         * 是否立即重连
         */
        public get ImmediateConnect(): boolean {
            if (!this._client || !this._client.msgHash || this._client.msgIndex <= 0) return true;
            if (this._client.msgHash) {
                var idx = Global.Account.idx;
                var msg = this._client.msgHash[this._client.msgIndex];
                if (msg) {
                    if (msg.idx == idx && msg.event != GameEvent.DOMINO_AUTO_PLAY) return true;
                    if (msg.idx != idx && msg.event == GameEvent.DOMINO_AUTO_PLAY) return true;
                }
            }
            return false;
        }

        /**
         * 手动添加消息
         * 目前只添加了切换用户信息
         */
        public addMsgInManunal(): void {
            if (!this._client) return;
            if (Global.isconnect) return;
            if (Global.IsGameOver) return;
            if (this._client.msgIndex <= 0) return;
            if (this.ImmediateConnect) return;

            var msg = this._client.msgHash[this._client.msgIndex];
            if (msg) {
                var event = msg.event;
                switch (event) {
                    case GameEvent.DOMINO_AUTO_PLAY:
                        //TODO::这里有个问题，如果下一个用户没有牌可出，则是pass，而不是切换用户
                        var nextPlayer = RoomService.instance._room.getNextPlayer(msg.idx);
                        if (nextPlayer) {
                            var nextMsg = {
                                event: GameEvent.GAME_PLAYER_STATUS_CHANGE,
                                idx: nextPlayer.fPlayerInfo.idx,
                                ptStatus: EnumPlayerTurnStatus.DOMINO_PLAY_START,
                                roomid: RoomService.instance._room.roomid,
                            }
                            this._client.msgIndex += 1;
                            this._client.msgHash[this._client.msgIndex] = nextMsg;
                            this.msgQueueList.push(nextMsg);
                            yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Dominose_MsgInManunal);
                        }
                        break;
                }
            }
        }

        //====================================联赛观战======================================

        private onWatchError(d) {
            if (!this._client) return;
            var res;
            var resultCode = 0;
            if (!d) resultCode = -1;
            else {
                yalla.Debug.log('=========onWatchError  000========');
                yalla.Debug.log(d);
                res = this._client.decodeMsg_watch(d, "WatchRoomReply");
                yalla.Debug.log(res);
                if (res) {
                    resultCode = res.result;
                    res.banTalkData && (yalla.Global.Account.banTalkData = res.banTalkData);//是否禁言

                    //1.4.0 修改：watchError不处理gameEventList，等到34 服务端成功建立连接后，使用最新的gameEventList op=3
                    // this.initDominoGameEventList(res);

                    //初始观战人数
                    this.updateWatchNum(res);
                }
            }

            //收到消息通知原生 0能正常进入观战返回给原生1 表示成功， 0表示失败
            var watchCode = (res && res.result == 0) ? 1 : 0;
            Native.instance.watchResult({ code: watchCode });

            yalla.Debug.log('=========onWatchError  111========');
            var msg = null;
            switch (resultCode) {
                case 0://正常可进入观战
                    !this._isQuickEnter && yalla.common.connect.WatchThinking.instance.show();
                    return;
                case 2004://观战聊天过于频繁
                    yalla.common.TipManager.instance.showOneTip("Send messages too frequently.\n Please try again later", {}, 2000);
                    return;
                case 2006://玩家被禁言
                    yalla.Mute.showBanTalkDialog();
                    return;
                case 2007://聊天内容为空
                case 2008://内容包含敏感信息
                    return;
                case 2000://房间不存在
                case 2001://房间没有准备好 
                case 2005://游戏已结束
                    msg = "Unable to spectate the match";
                    break;
                case 2002://正在其他房间观战
                    msg = "Failed to load the data. Please try again.";
                    break;
                case 2003://观战人数达到上限
                    msg = "The number of spectators reaches the maximum limit. Please choose another match.";
                    break;
                default://协议消息解析失败或其它错误码
                    msg = "Loading, please wait...";
                    break;
            }
            res && res.watchingRoomId && NativeWebSocket.instance.sendNetMsg_watch({ command: 6, data: { roomId: res.watchingRoomId } });
            yalla.common.Confirm.instance.showConfirm(msg, null, Laya.Handler.create(this, () => {
                RoomService.instance._room.roomid && NativeWebSocket.instance.sendNetMsg_watch({ command: 6, data: { roomId: RoomService.instance._room.roomid } });
                RoomService.instance.backHallTo('leagueWatchList');
            }))
        }

        public initWatchData(): void {
            if (this._client) this._client.isWatch = true;
            RoomService.instance._room.showId = RoomService.instance._room.roomid;

            // yalla.data.watchDataList.forEach(element => {
            //     this.userMessageEnvelope(10, element);
            // });
        }
        private _gameEventList = [];
        private _isQuickEnter: boolean = false;
        private _beforeQuickEventList = []; //在QuickEnter之前发了GameEventResponse事件

        public userMessageEnvelope_old(d: any): void {
            if (!this._client || Global.IsGameOver) return;
            // var msg = this._client.decodeMsg_watch(d, "UserMessageEnvelope");
            var msg = this._client.decodeMsg(d, "UserMessageEnvelope");
            var msg2;
            yalla.Debug.log('====userMessageEnvelope_old====msg.MessageType=' + msg.MessageType);
            if (msg.message)
                switch (msg.MessageType) {
                    case 13:
                        msg2 = this._client.decodeMsg(msg.message, "QuickEnterRoomResponse");
                        this.initQuickEnterRoom(msg2);
                        var watchThink = yalla.common.connect.WatchThinking._instance;
                        watchThink && watchThink.hide();
                        break;
                    case 14:
                        yalla.Debug.log('-----解析联赛消息从原生转发的 旧 cmd=14----');
                        msg2 = this._client.decodeMsg(msg.message, "WatchGameChat");
                        yalla.Debug.log("WatchGameChat:")
                        yalla.Debug.log(msg2)
                        // this.event(yalla.data.EnumCmd.Dominose_Chat, msg2);
                        yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_Chat, msg2);
                        break;

                    case 15:
                        msg2 = this._client.decodeMsg(msg.message, "GameEventResponse");
                        this.updateGameEventList(msg2);
                        break;

                    case 16:
                        msg2 = this._client.decodeMsg(msg.message, "WatchPlayerResponse");
                        RoomService.instance._room.watchNum = msg2.watchNum;
                        // this.event("watchNumChange", [msg2.watchNum]);
                        yalla.event.YallaEvent.instance.event("watchNumChange", [msg2.watchNum]);
                        break;
                    case 17:

                        msg2 = this._client.decodeMsg(msg.message, "DominoGameEventList");
                        if (msg2.quickEnterRoomResponse) {
                            this.initQuickEnterRoom(msg2.quickEnterRoomResponse);
                        }
                        var gameEventList = msg2.gameEventResponse;
                        if (gameEventList) {
                            yalla.Debug.log(this._isQuickEnter + '====观战信息 0===gameEventList:' + gameEventList.length);
                            yalla.Debug.log(gameEventList);
                            gameEventList = this.checkGameEventBeforeQuick(this._beforeQuickEventList, gameEventList);
                        }

                        this.setDominoInfo(gameEventList);
                        this.playMsg();
                        var watchThink = yalla.common.connect.WatchThinking._instance;
                        watchThink && watchThink.hide();
                        break;
                    case 18:
                        msg2 = this._client.decodeMsg(msg.message, "WatchOverMessage");
                        yalla.Debug.log("系统维护" + JSON.stringify(msg2));
                        this.updateWatchOverMsg(msg2);
                        break;
                    case 34:
                        msg2 = this._client.decodeMsg(msg, "WatchGameResponse");
                        yalla.Debug.log("-old-1.4.0 新增协议" + JSON.stringify(msg2));
                        this.initWatchGameResponse(msg2);
                        break;
                    default:
                        yalla.Debug.log(msg);
                        break;
                }
            // console.log('-response--', msg2);
        }

        public userMessageEnvelope(cmd: any, msg: any): void {
            if (!this._client || Global.IsGameOver) return;
            var msg2;
            yalla.Debug.log('=BaseNet===userMessageEnvelope 新====cmd=' + cmd);
            if (msg)
                switch (cmd) {
                    case 11:
                        msg2 = this._client.decodeMsg(msg, "WatchGameChat");
                        yalla.Debug.log("-----解析联赛消息从原生转发的 新 cmd=11----")
                        yalla.Debug.log(msg2)
                        // this.event(yalla.data.yalla.data.EnumCmd.Dominose_Chat, msg2);
                        yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_Chat, msg2);
                        break;

                    case 10:
                        msg2 = this._client.decodeMsg(msg, "GameEventResponse");
                        yalla.Debug.log("==userMessageEnvelope cmd=10====GameEventResponse:");
                        yalla.Debug.log(msg2);
                        this.updateGameEventList(msg2);
                        break;

                    case 12:
                        msg2 = this._client.decodeMsg(msg, "WatchPlayerResponse");
                        yalla.Debug.log("======WatchPlayerResponse:")
                        yalla.Debug.log(msg2);
                        RoomService.instance._room.watchNum = msg2.watchNum;
                        // this.event("watchNumChange", [msg2.watchNum]);
                        yalla.event.YallaEvent.instance.event("watchNumChange", [msg2.watchNum]);
                        break;

                    case 13:
                        msg2 = this._client.decodeMsg(msg, "WatchOverMessage");
                        yalla.Debug.log("系统维护" + JSON.stringify(msg2));
                        this.updateWatchOverMsg(msg2);
                        break;
                    case 34:
                        msg2 = this._client.decodeMsg(msg, "WatchGameResponse");
                        yalla.Debug.log("1.4.0 新增协议" + JSON.stringify(msg2));
                        this.initWatchGameResponse(msg2);
                        break;
                    default:
                        yalla.Debug.log(msg);
                        break;
                }
        }
        /**
         * 1.4.0联赛优化，加入联赛观战后，原生通知服务端（原先4条消息流改成2条，降低流量）
         * op  0:游戏消息 1:人数消息 2: 游戏退出
         * @param msg 
         */
        private initWatchGameResponse(res: any): void {
            yalla.Debug.log(!this._client + "=00==1.4.0initWatchGameResponse==gameOVer=" + yalla.Global.IsGameOver + "--" + !res);
            if (!this._client || yalla.Global.IsGameOver || !res) return;
            // var res = this._client.decodeMsg_watch(msg, "WatchGameResponse");
            yalla.Debug.log("===1.4.0 新增协议 initWatchGameResponse==res.op=" + res.op);
            yalla.Debug.log(res);
            var op = res.op;
            switch (op) {
                case 0:
                    this.updateGameEventList(res.gameEventResponse);
                    break;
                case 1:
                    this.updateWatchNum(res.watchPlayerResponse);
                    break;
                case 2:
                    this.updateWatchOverMsg(res.watchOverMessage);
                    break;
                case 3:
                    this.initDominoGameEventList(res);
                    break;
            }
        }

        private initDominoGameEventList(msg: any): void {
            if (!this._client || yalla.Global.IsGameOver) return;
            if (!msg || !msg.list) return;

            msg = msg.list;
            if (msg.quickEnterRoomResponse) {
                this.initQuickEnterRoom(msg.quickEnterRoomResponse);
            }
            var gameEventList = msg.gameEventResponse;
            if (gameEventList) {
                yalla.Debug.log(this._isQuickEnter + '====观战信息 initDominoGameEventList===gameEventList:' + gameEventList.length);
                yalla.Debug.log(gameEventList);
                gameEventList = this.checkGameEventBeforeQuick(this._beforeQuickEventList, gameEventList);
            }

            this.setDominoInfo(gameEventList);
            this.playMsg();
            var watchThink = yalla.common.connect.WatchThinking._instance;
            watchThink && watchThink.hide();
        }

        private initQuickEnterRoom(msg: any) {
            this.resetData(); //TODO::原生可能会发起重连，游戏重新进入房间，重新获取数据，但是未通知游戏重连，这里要主动先清理

            RoomService.instance._room.initWatchData(msg);

            Global.Account.isPrivate = RoomService.instance._room.isPrivate;
            var user = UserService.instance.user;
            var idx = RoomService.instance._room.player.player[0].fPlayerInfo.idx;
            user.idx = idx;
            user.update({ playerShowInfo: RoomService.instance._room.getPlayerByIdx(idx) });
            this._isQuickEnter = true;

            // this.event(yalla.data.EnumCmd.Dominose_EnterRoom, msg);
            yalla.event.YallaEvent.instance.event(yalla.data.EnumCmd.Dominose_EnterRoom, msg);
        }

        private _roundMsg = null;
        private _dominoInfoCount = 0;
        private pushEventMsg(msgItem: any): void {
            if (!this._gameEventList) this._gameEventList = [];
            if (msgItem.event == 5) {
                this._roundMsg = msgItem;
                this._dominoInfoCount = 0;
            }
            if (msgItem.event == 13) {
                this._roundMsg = null;
                this._dominoInfoCount += 1;
            }
            if (this._roundMsg && msgItem.event != 5 && msgItem.event != 13) {
                // yalla.Debug.log(msgItem.event, '====11====');
                return;
            }
            if (!this._roundMsg && this._dominoInfoCount == 0 && msgItem.event != 13) {
                // yalla.Debug.log(msgItem.event, '====22====');
                return;
            }
            if (!this._roundMsg && this._dominoInfoCount > 1 && msgItem.event == 13) {
                // yalla.Debug.log(msgItem.event, '====33====');
                return;
            }

            this._gameEventList.push(msgItem);
        }

        //QuickEnter前收到的消息跟GameEventList列表最后一条消息对比SendMessageTime
        private checkGameEventBeforeQuick(beforeEventList: Array<any>, eventList: Array<any>) {
            if (beforeEventList && eventList) {
                var beforeOneItem = beforeEventList[0];
                var endOneItem = eventList[eventList.length - 1];
                console.log(!beforeOneItem);
                console.log(!endOneItem);
                if (beforeOneItem && endOneItem && beforeOneItem.sendMessageTime && endOneItem.sendMessageTime && beforeOneItem.sendMessageTime > endOneItem.sendMessageTime) {
                    eventList = eventList.concat(beforeEventList);
                }
            }
            this._beforeQuickEventList = null;
            return eventList;
        }

        //过滤桌面信息前的消息
        private setDominoInfo(eventList: Array<any>) {
            if (!eventList) return;
            eventList.forEach(element => {
                this.pushEventMsg(element);
            });
        }


        private _msgBeginTime = 0;
        private _timeOutList: Array<any> = null;
        public _isPlay: boolean = false;
        public playMsg(): void {
            if (!this._gameEventList || this._gameEventList.length < 1 || this._isPlay || !yalla.Global.isFouce) return;

            var msg = this._gameEventList.shift();
            this._isPlay = true;
            if (!this._msgBeginTime) this._msgBeginTime = msg.sendMessageTime;
            if (!msg.sendMessageTime) this._msgBeginTime = 0;
            var time = msg.sendMessageTime - this._msgBeginTime;

            // yalla.Debug.log(time+'---' + msg.event+'=====RoomService.playMsg===='+yalla.Global.isFouce);
            if (msg.event == 11) {
                yalla.Debug.log(msg.sendMessageTime + '-' + msg.roundNum + "==PlayMsg  开始出牌==" + msg.autoPlayDomino.headNum + ':' + msg.autoPlayDomino.tailNum);
            }
            if (time > 0 && Global.isFouce) {
                if (!this._timeOutList) this._timeOutList = [];
                var timeOut = setTimeout((msg) => {
                    if (!this._isQuickEnter) return; //TODO::避免退出重进，settimeout没有清理干净
                    // yalla.Debug.log(time + '===playMsg 2===event=' + msg.event);
                    if (msg.event == 11) {
                        yalla.Debug.log(msg.sendMessageTime + '--' + msg.roundNum + '--' + msg.idx + "==PlayMsg 已出牌==" + msg.autoPlayDomino.headNum + ':' + msg.autoPlayDomino.tailNum);
                    }
                    this._msgBeginTime = msg.sendMessageTime;
                    this._isPlay = false;
                    this.handleGameEvent(msg, false);
                    this.playMsg();
                }, time, msg);
                this._timeOutList.push(timeOut);

            } else {
                if (!this._isQuickEnter) return; //TODO::避免退出重进，settimeout没有清理干净
                this._isPlay = false;
                this.handleGameEvent(msg, false);
                this.playMsg();
            }
        }
        /**
         * 推送GameEventResponse
         */
        private updateGameEventList(res: any): void {
            if (!res) return;
            yalla.Debug.log("==updateGameEventList cmd=10====GameEventResponse:");
            yalla.Debug.log(res);
            if (!this._isQuickEnter) {
                if (!this._beforeQuickEventList) this._beforeQuickEventList = [];
                this._beforeQuickEventList.push(res);
                return;
            }

            this.pushEventMsg(res);
            if (this._gameEventList && this._gameEventList.length <= 1) {
                yalla.Debug.log('====updateGameEventList  的  playMsg======');
                this.playMsg();
            }
        }

        /**
         * 观战人数更新
         * @param res 
         */
        private updateWatchNum(res: any): void {
            if (res.hasOwnProperty('watchNum')) {
                RoomService.instance._room.watchNum = res.watchNum;
                yalla.event.YallaEvent.instance.event("watchNumChange", [res.watchNum]);
            }
        }

        /**
         * watchover message
         *
         */
        private updateWatchOverMsg(res): void {
            yalla.Debug.log("系统维护" + JSON.stringify(res))
            if (res.code != 0) {
                yalla.common.Confirm.instance.showConfirm("The game is finished", null, Laya.Handler.create(this, () => {
                    NativeWebSocket.instance.sendNetMsg_watch({ command: 6, data: { roomId: RoomService.instance._room.roomid } });
                    RoomService.instance.backHallTo('leagueWatchList');
                }))
            }
        }

        //=================================================================================

        public resetData(): void {
            if (this._timeOutList) {
                for (var i = 0; i < this._timeOutList.length; i++) {
                    clearTimeout(this._timeOutList[i]);
                }
                this._timeOutList = null;
            }
            this.msgQueueList = [];
            this.selectCardList = [];

            this._msgBeginTime = 0;
            this._dominoInfoCount = 0;
            this._isPlay = false;
            this._isQuickEnter = false;
            this._roundMsg = null;
            this._gameEventList = null;
        }

        public clear() {
            this.resetData();
            this._beforeQuickEventList = null;
            if (this._client) {
                this._client.off('UserMessageEnvelope_old', this, this.userMessageEnvelope_old, null);
                this._client.off('UserMessageEnvelope', this, this.userMessageEnvelope, null);
            }
            NativeWebSocket.instance.off("onWatchError", this, this.onWatchError);
        }
    }
}