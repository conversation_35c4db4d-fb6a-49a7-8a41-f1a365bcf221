module yalla.data {

    export class DominoNet extends BaseNet{

        constructor() {
            super();
            if (yalla.util.IsBrowser()) {
                this._client = yalla.net.Client.instance;
            } else {
                this._client = yalla.net.NativeClient.instance;
            }
        }

        public register(): void {
            if (!this._client) return;
            yalla.Debug.log("====DominoNet 游戏内通讯事件注册======");
            super.register();
            this._client.on(yalla.data.EnumCmd.Dominose_Login, this, this.handleLoginGame);
            this._client.on(yalla.data.EnumCmd.Dominose_TicketLogin, this, this.handleLoginGame);
            this._client.on(yalla.data.EnumCmd.Dominose_EnterRoom, this, this.handleEndterRoom);
            this._client.on(yalla.data.EnumCmd.Dominose_GameEvent, this, this.handleGameEvent);
            this._client.on(yalla.data.EnumCmd.Dominose_QuitRoom, this, this.handleQuitRoom);
            this._client.on(yalla.data.EnumCmd.Dominose_Chat, this, this.handleChat);
            this._client.on(yalla.data.EnumCmd.Dominose_GAME_OPERATE, this, this.handleGameOperateResponse);
            this._client.on(yalla.data.EnumCmd.Dominose_Update_Player_Coin, this, this.handleUpdatePlayCoin);
            this._client.on(yalla.data.EnumCmd.Dominose_Gift_Cnf_List, this, this.handleUpdateGiftCnf);
            this._client.on(yalla.data.EnumCmd.Dominose_Gift_Send, this, this.handleRevGift);
            this._client.on(yalla.data.EnumCmd.Dominose_GetAgoraToken, this, this.handleGetAgoraToken);
            this._client.on(yalla.data.EnumCmd.Dominose_GetZegoToken, this, this.handleGetZegoToken);
        }

        /**
         * 票据登录
         */
        public ticketLogin(): void {
            if (!this._client) return;
            super.ticketLogin();
            yalla.Debug.log('-DominoNet-ticketLogin-');
            this._client.sendMsg("TicketLoginRequest", Command.TICKET_LOGIN, {
                idx: Global.Account.idx,
                // roomid: RoomService.instance._room.roomid,
                // token: RoomService.instance._room.token,
                roomid: Global.Account.roomid,
                token: Global.Account.token,
                version: Global.Account.version,
                // version: '1.4.2.0',
                msgindex: 0
            });
        }

        /**
         * 退出房间
         */
        public quitRoom(): void {
            super.quitRoom();
            if (!yalla.Global.Account.leagueWatch) {
                var isSend = this._client && this._client.sendMsg("QuitRoomRequest", Command.QUIT_ROOM, {
                    roomid: Global.Account.roomid
                });
                if (!isSend) {
                    //TODO::1.4.4.1 返回大厅放入离开房间回掉
                    if (yalla.Native.instance.deviceType == DeviceType.Android) {
                        VoiceService.instance.levelGameRoom(() => {
                            UserService.instance.backHall(true);
                        });
                    } else {
                        UserService.instance.backHall(true);
                    }
                }
            } else {
                Global.IsGameOver = true;
                NativeWebSocket.instance.sendNetMsg_watch({ command: 6, data: { roomId: Global.Account.roomid } });
                UserService.instance.backHall(true);
            }
        }

        /**
         * 出牌(出牌后，就把当前激活玩家置空)
         * @param d 
         * @param deskSort 相对桌面，出的方向 1234
         */
        public sendPlayCard(d: Domino, deskSort: number = 1): void {
            if (!this._client) return;
            var room = RoomService.instance._room;
            super.sendPlayCard(d, deskSort);
            var waitPlayCardHash = room.deskPool.waitPlayCardHash;
            yalla.Debug.log("====发牌0=====" + (!waitPlayCardHash[d.id]));
            if (!waitPlayCardHash[d.id]) return;
            yalla.Debug.log("====发牌1=====" + (room.activeIdx == UserService.instance.user.idx));
            if (room.activeIdx == UserService.instance.user.idx) {
                var p = room.deskPool.getSendPlayCardParams(d.id, deskSort);
                p.roomid = room.roomid;
                p.op = GameOperate.DOMINO_PLAY;
                p.opid = 0;
                p.type = GameType.DOMINO;
                this._client.sendMsg("GameOperateRequest", Command.GAME_OPERATE, p);
                room.activeIdx = 0;
            }
        }

        /**
         * pass 跳过
         */
        public sendPass(): void {
            if (!this._client) return;
            // super.sendPass();
            var p: any = {
                roomid: Global.Account.roomid,
                op: GameOperate.DOMINO_PASS,
                opid: 0,
                type: GameType.DOMINO
            };
            this._client.sendMsg("GameOperateRequest", Command.GAME_OPERATE, p);
        }

        /**
         * 下一轮
         * @param d 
         */
        public sendNexgRound(): void {
             if (!this._client) return;
            // super.sendNexgRound();
            var p: any = {};
            p.roomid = Global.Account.roomid;
            p.op = GameOperate.DOMINO_RESTART;
            p.opid = 0;
            p.type = GameType.DOMINO;
            this._client.sendMsg("GameOperateRequest", Command.GAME_OPERATE, p);
        }

        /**
         * 快捷短语聊天
         */
        public sendChat(msgResult: any = {}): void {
            if (!this._client) return;
            super.sendChat(msgResult);
            msgResult.idx = UserService.instance.user.idx;
            msgResult.roomid = Global.Account.roomid;
            this._client.sendMsg("GameRoomPlayerChatToAllRequestAndResponse", Command.PLAYER_CHAT_ALL, msgResult);
        }

        /**
         * 
         * @param d 
         */
        public sendAddRoundTime(): void {
             if (!this._client) return;
            super.sendAddRoundTime();
            var p: any = {};
            p.roomid = Global.Account.roomid;
            p.op = GameOperate.DOMINO_ADD_ROUNDTIME;
            p.opid = 0;
            p.type = GameType.DOMINO;
            this._client.sendMsg("GameOperateRequest", Command.GAME_OPERATE, p);
        }

        /**
         * 获取金币接口  0金币 1钻石
         */
        public sendPlayerCoin(type: number = 0): void {
             if (!this._client) return;
            // super.sendPlayerCoin(type);
            this._client.sendMsg("UpdataCoinRequest", Command.UPDATA_PLAYER_COIN, { type: type });
        }

        /**
         * 设置聊天屏蔽  
         * @param chatOffType false取消屏蔽   true设置屏蔽
         */
        public sendChatOff(isChatOff: number): void {
             if (!this._client) return;
            // super.sendChatOff(isChatOff);
            var p: any = {};
            p.roomid = Global.Account.roomid;
            p.op = isChatOff ? GameOperate.CHAT_OFF : GameOperate.CHAT_OFF_CANCEL;
            p.opid = 0;
            p.type = GameType.DOMINO;
            this._client.sendMsg("GameOperateRequest", Command.GAME_OPERATE, p);

            ludo.muteSpectator.isMute = isChatOff;
            //TODO::自己操作的话不等服务端返回，避免因消息不返回，设置的状态和玩家头像上的状态不一致
            // this.event(EnumCustomizeCmd.Dominose_ChatOFF_My, { idx: UserService.instance.user.idx, num: isChatOff });
            yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Dominose_ChatOFF_My, { idx: UserService.instance.user.idx, num: isChatOff });
        }

        public getGiftCnfList(): void {
            // super.getGiftCnfList();
            this._client.sendMsg("GameGiftCnfListRequest", Command.GAME_GIFT_CNF_LIST);
        }

        public sendGift(giftId: number, rcvIdxs: Array<any>): void {
             if (!this._client) return;
            // super.sendGift(giftId, rcvIdxs);
            yalla.Debug.log(JSON.stringify(rcvIdxs) + '==GameGiftSendRequest 0==' + giftId);
            var p: any = {};
            p.giftId = giftId;
            p.rcvIdx = rcvIdxs;
            this._client.sendMsg("GameGiftSendRequest", Command.GAME_GIFT_SEND, p);
        }

        /**
         * 取消托管
         */
        public cancleTrust() {
             if (!this._client) return;
            // super.cancleTrust();
            var p: any = {};
            p.roomid = Global.Account.roomid;
            p.op = GameOperate.SYSTEM_TRUST_CANCEL;
            p.opid = 0;
            p.type = GameType.DOMINO;
            this._client.sendMsg("GameOperateRequest", Command.GAME_OPERATE, p);
        }

       /**
		 * 获取语音房间token
		 */
        public getToken() {
             if (!this._client) return;
            yalla.Debug.log("==getToken==voiceType:" + Global.Account.voiceType);
            var idx = Global.Account.idx;
            var roomid = Global.Account.roomid;
            switch (Global.Account.voiceType) {
                case VoiceType.Agora:
                    this._client.sendMsg("GetAgoraTokenRequest", Command.GET_AGORA_TOKEN, {
                        idx: idx,
                        roomid: roomid
                    });
                    break;
                case VoiceType.Zego://1.2.8接入zego
                    this._client.sendMsg("GetZegoTokenRequest", Command.GET_ZEGO_TOKEN, {
                        idx: idx,
                        roomid: roomid,
                        version:4
                    });
                    break;
            }
        }

        public clear(): void{
            yalla.Debug.log("=====游戏内通讯事件移除====");
            super.clear();
            if (this._client) {
                this._client.off(yalla.data.EnumCmd.Dominose_Login, this, this.handleLoginGame);
                this._client.off(yalla.data.EnumCmd.Dominose_TicketLogin, this, this.handleLoginGame);
                this._client.off(yalla.data.EnumCmd.Dominose_EnterRoom, this, this.handleEndterRoom);
                this._client.off(yalla.data.EnumCmd.Dominose_GameEvent, this, this.handleGameEvent);
                this._client.off(yalla.data.EnumCmd.Dominose_QuitRoom, this, this.handleQuitRoom);
                this._client.off(yalla.data.EnumCmd.Dominose_Chat, this, this.handleChat);
                this._client.off(yalla.data.EnumCmd.Dominose_GAME_OPERATE, this, this.handleGameOperateResponse);
                this._client.off(yalla.data.EnumCmd.Dominose_Update_Player_Coin, this, this.handleUpdatePlayCoin);
                this._client.off(yalla.data.EnumCmd.Dominose_Gift_Cnf_List, this, this.handleUpdateGiftCnf);
                this._client.off(yalla.data.EnumCmd.Dominose_Gift_Send, this, this.handleRevGift);
                this._client.off(yalla.data.EnumCmd.Dominose_GetAgoraToken, this, this.handleGetAgoraToken);
                this._client.off(yalla.data.EnumCmd.Dominose_GetZegoToken, this, this.handleGetZegoToken);
                this._client.clear();
            }
            // yalla.net.NativeClient.instance.clear();
        }

    }
}
