module yalla.data {

    export class DominoStreamNet extends BaseNet{
        constructor() {
            super();
            if (yalla.util.IsBrowser()) {
                this._client = yalla.ClientBrower.instance;
            } else {
                this._client = yalla.Client.instance;
            }
        }

        public register(): void{
            super.register();
            if (this._client) {
                this._client.on('Game_4', this, this.GameResponse);
                this._client.on('Game_SocketClose', this, this.handleSocketClose);
                this._client.on('Game_GateWay_Error', this, this.handleGatewayError);
            }
        }

        public GameResponse(code: any, msg: any, cmd, businessOrder?): void {
            if (Global.gameType != GameType.DOMINO) return;
            if (yalla.data.IsTestCode) return;
            if (Global.IsGameOver) return; 
            if (cmd == 0 || !msg) return;

            yalla.Debug.log(`[${yalla.getTimeHMS()} ][response] [domino-gateway-code:${code}] [cmd:${cmd}]`);
            yalla.Debug.log(this._isReady);
            if (!this._isReady) {
                if (!this._readyMsgList) this._readyMsgList = [];
                if (cmd > 20) {//没有收到房间信息之前除登录和房间信息之外任何消息暂存不处理
                    this._readyMsgList.push({
                        cmd: cmd,
                        businessOrder: businessOrder,
                        msg: msg
                    })
                    return;
                }
            }
            yalla.Debug.log(cmd + '======this._readyMsgList==' + this._readyMsgList.length);
            yalla.Debug.log(this._readyMsgList);

            this.processMsg(businessOrder, cmd, msg);
        }

        /**接口指令 */
        private processMsg(businessOrder, cmd, data) {
            if (!data) return;
            yalla.Debug.log(businessOrder+'：businessOrder===domino===processMsg===cmd:' + cmd);
            // yalla.Debug.log(data);
            var msg;
            switch (businessOrder) {
                case BusinessOrderCode.domino_callGame:
                    this.doubleStreamResponse(cmd, data);
                    break;
                case BusinessOrderCode.domino_getAgoraToken:
                    msg = this.decodeMsg(data, "GetAgoraTokenResponse");   
                    if (!msg) return;       
                    VoiceService.instance.handleGetAgoraToken(msg);    
                    break;
                case BusinessOrderCode.domino_getZegoToken:
                    msg = this.decodeMsg(data, "GetZegoTokenResponse");   
                    if (!msg) return;     
                    VoiceService.instance.handleGetZegoToken(msg);
                    break;
                case BusinessOrderCode.domino_updataCoin://刷新用户金币
                    msg = this.decodeMsg(data, "UpdataCoinResponse");   
                    if (!msg) return;      
                    this.handleUpdatePlayCoin(msg);
                    break;
                case BusinessOrderCode.domino_gameGiftCnfList:
                    msg = this.decodeMsg(data, "GameGiftCnfListResponse");   
                    if (!msg) return;  
                    // this.event(EnumCmd.Dominose_Gift_Cnf_List, msg);
                    yalla.event.YallaEvent.instance.event(EnumCmd.Dominose_Gift_Cnf_List, msg);
                    break;  
            }
        }
        /**双向流接口指令一样，按cmd区分 */
        private doubleStreamResponse(cmd, data): void{
            var d;
            var msg;
            switch (cmd) {
                case Command.TICKET_LOGIN:
                    if (this._client) {
                        d = this._client.decodeMsg(data, "DominoGameResp");
                        yalla.Debug.log("=======DominoGameResp.LoginResponse========");
                        yalla.Debug.log(d);
                        if (!d || !d.data) return;
                        if (d && d.roomId && d.roomId != Global.Account.roomid) return;
                        msg = this._client.decodeMsg(d.data, "LoginResponse");
                        yalla.Debug.log(msg);
                        this.handleLoginGame(msg, false);
                    }    
                    break;
                case Command.QUICK_START:
                    msg = this.decodeMsg(data, "QuickEnterRoomResponse", true);
                    if (!msg) return;
                    this._isReady = true;
                    this.handleEndterRoom(msg, false);
                    break;
                case Command.QUIT_ROOM:
                    msg = this.decodeMsg(data, "QuitRoomResponse", true);
                    if (!msg) return;
                    this.handleQuitRoom(msg,false);
                    break;
                case Command.GAME_OPERATE:
                    d = this.decodeMsg(data, "GameOperateResponse", true);
                    if (!d) return;
                    this.handleGameOperateResponse(d,false);
                    break;
                case Command.GAME_EVENT:
                    d = this.decodeMsg(data, "GameEventResponse", true);
                    if (!d) return;
                    if (d && d.roomid && d.roomid != yalla.Global.Account.roomid) return;
                    this.handleGameEvent(d, false);
                    break;
                case Command.PLAYER_CHAT_ALL:
                    msg = this.decodeMsg(data, "GameRoomPlayerChatToAllRequestAndResponse", true);
                    if (!msg) return;
                    // this.event(EnumCmd.Dominose_Chat, msg);
                    yalla.event.YallaEvent.instance.event(EnumCmd.Dominose_Chat, msg);
                    break;
                case Command.GAME_GIFT_SEND:
                    console.log("==收到礼物===");
                    msg = this.decodeMsg(data, "GameGiftSendResponse",true); 
                    console.log(msg);
                    if (!msg) return;      
                    this.handleRevGift(msg,false);
                    break;  
                case Command.OUT_GAME:
                    msg = this.decodeMsg(data, "OutGameResponse",true);   
                    if (!msg) return;       
                    this.handleOutGame(msg, false);
                    break;   
            }    
        }

        /**是否双向流解析 */
        private decodeMsg(data: any, messageName: string, isStreamResponse: boolean = false): void{
            if (!this._client) return;
            var msg;
            if (isStreamResponse) {
                var d = this._client.decodeMsg(data, "DominoGameResp");
                yalla.Debug.log(d);
                if (!d || !d.data) return;
                msg = this._client.decodeMsg(d.data, messageName);
            } else {
                msg = this._client.decodeMsg(data, messageName);
            }
            yalla.Debug.log(messageName+"=====game.decodeMsg stream====="+isStreamResponse);
            yalla.Debug.log(msg);
            return msg;
        }

        private handleSocketClose() {
            this._readyMsgList = null;
            this._isReady = false;
        }

        private handleGatewayError(code:number) {
            if (yalla.Global.gameType != GameType.DOMINO) return;
            if (code > 0) {
                yalla.common.WaringDialog.showGatewayError(code, UserService.instance.backHall);
                return;
            }
        }

        public sendMsg(cmd, cmdName, bodyData?, businessOrder = 1): void {
            if (!this._client) return;
            var msg = bodyData
            if (!msg) msg = {};
            msg.roomId = Global.Account.roomid;
            msg.command = cmd;
            this._client.sendMsg(cmdName, cmd, msg, { ringRouteKey: Global.Account.roomid }, businessOrder);
        }

        /**
         * 票据登录
         */
        public ticketLogin(): void {
            // super.ticketLogin();
            var bodyData = {
                idx: Global.Account.idx,
                roomid: RoomService.instance._room.roomid,
                token: RoomService.instance._room.token,
                version: Global.Account.version,
                // version: '1.3.4.0',
                msgindex: 0//yalla.Global.ProtocolMsgIndex
            };
            yalla.Debug.log(bodyData);
            if (ludo.Proto.instance.root_domino_new) {
                var loginMessage = ludo.Proto.instance.root_domino_new.lookupType('TicketLoginRequest');
                var message = loginMessage.create(bodyData);
                var byteArray = loginMessage.encode(message).finish();
            }
            yalla.Debug.log("====发起票据登陆=====");
            yalla.Debug.log(byteArray);
            this.sendMsg(Command.TICKET_LOGIN, 'DominoGameReq', {
                data: byteArray
            }, BusinessOrderCode.domino_callGame);
        }

        /**
         * 退出房间
         */
        public quitRoom(): void {
            // super.quitRoom();
            yalla.Debug.log('---退出房间stream QuitRoomRequest---');
            if (!Global.Account.leagueWatch) {
                var p = {
                    roomid: Global.Account.roomid
                };
                if(this._client)
                    this._client.sendMsg("QuitRoomRequest", Command.QUIT_ROOM, p, { ringRouteKey: Global.Account.roomid }, BusinessOrderCode.domino_quitRoom);
                // this.sendMsg(Command.QUIT_ROOM, "QuitRoomRequest", { roomid: Global.Account.roomid },BusinessOrderCode.domino_quitRoom);
            } else {
                Global.IsGameOver = true;
                NativeWebSocket.instance.sendNetMsg_watch({ command: 6, data: { roomId: Global.Account.roomid } });
                UserService.instance.backHall(true);
            }
        }

        /**
         * 出牌(出牌后，就把当前激活玩家置空)
         * @param d 
         * @param deskSort 相对桌面，出的方向 1234
         */
        public sendPlayCard(d: Domino, deskSort: number = 1): void {
            super.sendPlayCard(d, deskSort);
            var room = RoomService.instance._room;
            var waitPlayCardHash = room.deskPool.waitPlayCardHash;
            yalla.Debug.log(room.activeIdx + ":idx======StreamNet.sendPlayCard 0========id:" + d.id + "==="+!waitPlayCardHash[d.id]);
            // yalla.Debug.log(waitPlayCardHash);
            if (!waitPlayCardHash[d.id]) return;
            // yalla.Debug.log("======StreamNet.sendPlayCard 1========"+(room.activeIdx == UserService.instance.user.idx));
            if (room.activeIdx == UserService.instance.user.idx) {
                var p = room.deskPool.getSendPlayCardParams(d.id, deskSort);
                p.roomid = room.roomid;
                p.op = GameOperate.DOMINO_PLAY;
                p.opid = 0;
                p.type = GameType.DOMINO;

                this.sendMsg(Command.GAME_OPERATE, "GameOperateRequest", p, BusinessOrderCode.domino_gameOperate);
                room.activeIdx = 0;
            }
        }

        /**
         * pass 跳过
         */
        public sendPass(): void {
            // super.sendPass();
            var p: any = {
                roomid: Global.Account.roomid,
                op: GameOperate.DOMINO_PASS,
                opid: 0,
                type: GameType.DOMINO
            };
            this.sendMsg(Command.GAME_OPERATE, "GameOperateRequest", p, BusinessOrderCode.domino_gameOperate);
        }

        /**
         * 下一轮
         * @param d 
         */
        public sendNexgRound(): void {
            // super.sendNexgRound();
            var p: any = {
                roomid: Global.Account.roomid,
                op: GameOperate.DOMINO_RESTART,
                opid: 0,
                type: GameType.DOMINO
            };
            this.sendMsg(Command.GAME_OPERATE, "GameOperateRequest", p, BusinessOrderCode.domino_gameOperate);
        }

        /**
       * 快捷短语聊天
       */
        public sendChat(msgResult: any = {}): void {
            // super.sendChat(msgResult);
            msgResult.idx = UserService.instance.user.idx;
            msgResult.roomid = Global.Account.roomid;
            this.sendMsg(Command.PLAYER_CHAT_ALL, "GameRoomPlayerChatToAllRequestAndResponse", msgResult, BusinessOrderCode.domino_playerChatToAll);
        }

        /**
         * 
         * @param d 
         */
        public sendAddRoundTime(): void {
            super.sendAddRoundTime();
            var p: any = {
                roomid: Global.Account.roomid,
                op: GameOperate.DOMINO_ADD_ROUNDTIME,
                opid: 0,
                type: GameType.DOMINO
            };
            this.sendMsg(Command.GAME_OPERATE, "GameOperateRequest", p, BusinessOrderCode.domino_gameOperate);
        }

        /**
         * 获取金币接口  0金币 1钻石
         */
        public sendPlayerCoin(type: number = 0): void {
            if (!this._client) return;
            // super.sendPlayerCoin(type);
            // if (!this.IsUpdatePlayerCoin) return;
            var p: any = { type: type, roomid: Global.Account.roomid};
            this._client.sendMsg("UpdataCoinRequest", Command.UPDATA_PLAYER_COIN, p, { ringRouteKey: Global.Account.roomid }, BusinessOrderCode.domino_updataCoin);
        }

        /**
         * 设置聊天屏蔽  
         * @param chatOffType false取消屏蔽   true设置屏蔽
         */
        public sendChatOff(isChatOff: number): void {
            // super.sendChatOff(isChatOff);
            var p: any = {
                roomid: Global.Account.roomid,
                op: isChatOff ? GameOperate.CHAT_OFF : GameOperate.CHAT_OFF_CANCEL,
                opid: 0,
                type: GameType.DOMINO
            };
            this.sendMsg(Command.GAME_OPERATE, "GameOperateRequest", p, BusinessOrderCode.domino_gameOperate);

            ludo.muteSpectator.isMute = isChatOff;
            //TODO::自己操作的话不等服务端返回，避免因消息不返回，设置的状态和玩家头像上的状态不一致
            // this.event(EnumCustomizeCmd.Dominose_ChatOFF_My, { idx: UserService.instance.user.idx, num: isChatOff });
            yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Dominose_ChatOFF_My, { idx: UserService.instance.user.idx, num: isChatOff });
        }

        public getGiftCnfList(): void {
            if (!this._client) return;
            // super.getGiftCnfList();
            var p = {
                roomid: Global.Account.roomid
            };
            this._client.sendMsg("GameGiftCnfListRequest", Command.GAME_GIFT_CNF_LIST, p, { ringRouteKey: Global.Account.roomid }, BusinessOrderCode.domino_gameGiftCnfList);
        }

        public sendGift(giftId: number, rcvIdxs: Array<any>): void {
            if (!this._client) return;   
            // super.sendGift(giftId, rcvIdxs);
            yalla.Debug.log(JSON.stringify(rcvIdxs) + '==GameGiftSendRequest 0==' + giftId);
            var p: any = { roomid: Global.Account.roomid, giftId: giftId, rcvIdx: rcvIdxs };
            this._client.sendMsg("GameGiftSendRequest", Command.GAME_GIFT_SEND, p, { ringRouteKey: Global.Account.roomid }, BusinessOrderCode.domino_gameGiftSend);//roomid
        }

        /**
         * 取消托管
         */
        public cancleTrust() {
            // super.cancleTrust();
            var p: any = {
                roomid: Global.Account.roomid,
                op: GameOperate.SYSTEM_TRUST_CANCEL,
                opid: 0,
                type: GameType.DOMINO
            };
            this.sendMsg(Command.GAME_OPERATE, "GameOperateRequest", p, BusinessOrderCode.domino_gameOperate);
        }

        /**
		 * 获取语音房间token
		 */
        public getToken() {
            if (!this._client) return;
            yalla.Debug.log("==getToken==voiceType:" + Global.Account.voiceType);
            var idx = Global.Account.idx;
            var roomid = Global.Account.roomid;
            switch (Global.Account.voiceType) {
                case VoiceType.Agora:
                    this._client.sendMsg("GetAgoraTokenRequest", Command.GET_AGORA_TOKEN, {
                        idx: idx,
                        roomid: roomid
                    }, { ringRouteKey: Global.Account.roomid }, BusinessOrderCode.domino_getAgoraToken);
                    break;
                case VoiceType.Zego://1.2.8接入zego
                    // 使用token
                    this._client.sendMsg("GetZegoTokenRequest", Command.GET_ZEGO_TOKEN, {
                        idx: idx,
                        roomid: roomid,
                        version:4
                    }, { ringRouteKey: Global.Account.roomid }, BusinessOrderCode.domino_getZegoToken);
                    break;
            }
        }


        public clear(): void{
            this._readyMsgList = null;
            this._isReady = false;
            if (this._client) {
                this._client.off('Game_4', this, this.GameResponse);
                this._client.off('Game_SocketClose', this, this.handleSocketClose);
                this._client.off('Game_GateWay_Error', this, this.handleGatewayError);
                this._client.clear();
            }
            // yalla.net.NativeClient.instance.clear();
        }

    }
}