module yalla.data {

    import EventDispatcher = laya.events.EventDispatcher;

    export class RoomService extends EventDispatcher {
        static _instance: RoomService;
        private _baseNet: BaseNet;

        public _room: Room = new Room();
        // public msgQueueList: Array<any> = [];             //队列消息
        public IsUpdatePlayerCoin: boolean = false;       //是否更新金币钻石
        // public selectCardList: Array<number> = [];

        // private _client;

        constructor() {
            super();
            // if (yalla.util.IsBrowser()) {
            //     this._client = yalla.net.Client.instance;
            // } else {
            //     this._client = yalla.net.NativeClient.instance;
            // }
        }

        static get instance(): RoomService {
            return  RoomService._instance || (RoomService._instance = new RoomService());
        }

        public get client(): any{
            if (this._baseNet) this._baseNet._client;
            return null;
        }

        public get selectCardList(): Array<number>{
            if (this._baseNet) return this._baseNet.selectCardList;
            return [];
        }
        /**队列消息 */
        public get msgQueueList(): Array<any> {
            if (this._baseNet) return this._baseNet.msgQueueList;
            return [];
        }

        public get ImmediateConnect(): boolean {
            if (this._baseNet) return this._baseNet.ImmediateConnect;
            return false;
        }

        public register(): void {
            if (this._baseNet) return this._baseNet.register();
        }

        /** TODO::之前在websocket open时候初始化client，但是现在区分新老协议，现在改这里初始化，等待验证 */
        public onWebSocketInit(type: number = 0): void{
            yalla.Debug.log("====RoomService.onWebSocketInit===type="+type);
            if (type == 2) {
                Client.instance.init(null);
                //  Client.instance.init(Laya.Handler.create(this, () => {
                    // this.ticketLogin();
                // }));
            } else {
                yalla.net.NativeClient.instance.initSocket();
                // if(!yalla.Global.Account.leagueWatch) this.ticketLogin();
            }
        }
        public onWebSocketOpen(type: number = 0): void {
            yalla.Debug.log("====RoomService.onWebSocketOpen===type="+type);
            if (type == 2) {
            //      Client.instance.init(Laya.Handler.create(this, () => {
                    this.ticketLogin();
                // }));
            } else {
                // yalla.net.NativeClient.instance.initSocket();
                if(!yalla.Global.Account.leagueWatch) this.ticketLogin();
            }
        }
        /**区分新老协议net */
        public setNet(type: number = 0): void{
            yalla.Debug.log((!!this._baseNet)+"====RoomService.setNet===type="+type);
            if (type == 2) {
                if (!this._baseNet) {
                    this._baseNet = new DominoStreamNet();
                    this._baseNet.register();
                }
            } else {
                if (!this._baseNet) {
                    this._baseNet = new DominoNet();
                    this._baseNet.register();
                }
            }
        }

        public resetData(): void {
            if (this._baseNet) this._baseNet.resetData();
            this.IsUpdatePlayerCoin = false;
        }

        public clear(): void {
            yalla.Debug.log("=====domino RoomService.clear  清理======");
            this.resetData();
            this._room && this._room.clear();
            if (this._baseNet) this._baseNet.clear();
            this._baseNet = null;
            
            var watchThink = yalla.common.connect.WatchThinking._instance;
            watchThink && watchThink.hide();
        }

        public get room() { return this._room; }
        public handCardList(idx: number) {
            return this._room.getPlayerAndDominoByIdx(idx);
        }

        /**
         * 票据登录
         */
        public ticketLogin(): void {
            yalla.Debug.log("====RoomService.ticketLogin 0======");
            if (!this._baseNet) return;
            yalla.Debug.log('-RoomService.ticketLogin-');
            this._baseNet.ticketLogin();
        }

        /**
         * 退出房间
         */
        public quitRoom(): void {
            yalla.Debug.log("======主动退出游戏======");
            if (!this._baseNet) return;
            this._baseNet.quitRoom();
        }

        /**
         * 出牌(出牌后，就把当前激活玩家置空)
         * @param d 
         * @param deskSort 相对桌面，出的方向 1234
         */
        public sendPlayCard(d: Domino, deskSort: number = 1): void {
            yalla.Debug.log('====domino 出牌协议发送==='+(!this._baseNet));
            if (!this._baseNet) return;
            this._baseNet.sendPlayCard(d, deskSort);
        }

        /**
         * pass 跳过
         */
        public sendPass(): void {
            if (!this._baseNet) return;
            this._baseNet.sendPass();
        }

        /**
         * 下一轮
         * @param d 
         */
        public sendNexgRound(): void {
            if (!this._baseNet) return;
            this._baseNet.sendNexgRound();
        }

        /**
       * 快捷短语聊天
       */
        public sendChat(msgResult: any = {}): void {
            if (!this._baseNet) return;
            this._baseNet.sendChat(msgResult);
        }

        /**
         * 
         * @param d 
         */
        public sendAddRoundTime(): void {
            if (!this._baseNet) return;
            this._baseNet.sendAddRoundTime();
            //埋点 游戏id+模式+分组+匹配类型+房间人数+报名费
            var returnValue = yalla.Global.gameType + "_" + this.room.gameType + "_" + this.room.isPrivate + "_0_" + this.room.playerNums + "_" + this.room.cost;
            yalla.util.clogDBAmsg('10321', returnValue);
        }

        /**
         * 获取金币接口  0金币 1钻石
         */
        public sendPlayerCoin(type: number = 0): void {
            if (!this._baseNet) return;
            if (!this.IsUpdatePlayerCoin) return;
            this._baseNet.sendPlayerCoin(type);
        }

        /**
         * 设置聊天屏蔽  
         * @param chatOffType false取消屏蔽   true设置屏蔽
         */
        public sendChatOff(isChatOff: number): void {
            if (yalla.Global.Account.leagueWatch) return;
            if (!this._baseNet) return;
            this._baseNet.sendChatOff(isChatOff);
        }

        public getGiftCnfList(): void {
            if (!this._baseNet) return;
            this._baseNet.getGiftCnfList();
        }

        public sendGift(giftId: number, rcvIdxs: Array<any>): void {
            if (!this._baseNet) return;
            this._baseNet.sendGift(giftId,rcvIdxs);
            yalla.Debug.log(JSON.stringify(rcvIdxs) + '==GameGiftSendRequest 0==' + giftId);
        }

        /**
         * 取消托管
         */
        public cancleTrust() {
            if (!this._baseNet) return;
            this._baseNet.cancleTrust();
        }

        /**
		 * 获取语音房间token
		 */
        public getToken() {
            if (!this._baseNet) return;
            this._baseNet.getToken();
        }

        public playMsg(): void {
            if (!this._baseNet) return;
            this._baseNet.playMsg();
        }

        public addMsgInManunal(): void {
            if (!this._baseNet) return;
            this._baseNet.addMsgInManunal();
        }

        public initWatchData(): void {
            yalla.Debug.log("=======RoomService.initWatchData========");
            if(this._baseNet && this._baseNet._client) this._baseNet._client.isWatch = true;
            // this._client.isWatch = true;
            if (this._room) this._room.showId = this._room.roomid;
            yalla.Debug.log("=======RoomService.initWatchData= 11=======");
        }   

        public backHallTo(toName: string, isBack: boolean = true): void{
            yalla.Native.instance.backHall(isBack, { to: toName, gameId: yalla.Global.Account.gameId });
        } 

        
        //================================双向流====================================
        public GameResponse(code: any, msg: any, cmd, businessOrder): void {
            if (!this._baseNet) return;
            this._baseNet.GameResponse(code, msg, cmd, businessOrder);
        }

        private handleSocketClose() {
            if (!this._baseNet) return;
            this._baseNet._readyMsgList = null;
            this._baseNet._isReady = false;
        }

        private handleGatewayError(code:number) {
            if (yalla.Global.gameType != GameType.DOMINO) return;
            if (code > 0) {
                yalla.common.WaringDialog.showGatewayError(code, UserService.instance.backHall);
                return;
            }
        }

        public testList: Array<any> = [
            // { headNum: 5, tailNum: 5, id: 6, deskSort: 3, side: 1, leftDominoId: 24, rightDominoId: 28, topDomainoId: 0, bottomDominoId: 26 },
            // { headNum: 5, tailNum: 3, id: 24, deskSort: 3, side: 4, leftDominoId: 23, rightDominoId: 6, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 4, tailNum: 3, id: 23, deskSort: 3, side: 3, leftDominoId: 11, rightDominoId: 24, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 4, tailNum: 0, id: 11, deskSort: 3, side: 4, leftDominoId: 8, rightDominoId: 23, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 1, tailNum: 0, id: 8, deskSort: 3, side: 3, leftDominoId: 14, rightDominoId: 11, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 2, tailNum: 1, id: 14, deskSort: 3, side: 3, leftDominoId: 21, rightDominoId: 8, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 5, tailNum: 2, id: 21, deskSort: 3, side: 3, leftDominoId: 0, rightDominoId: 14, topDomainoId: 0, bottomDominoId: 0 },

        
            // { headNum: 6, tailNum: 5, id: 28, deskSort: 4, side: 4, leftDominoId: 6, rightDominoId: 13, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 6, tailNum: 0, id: 13, deskSort: 4, side: 3, leftDominoId: 28, rightDominoId: 10, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 3, tailNum: 0, id: 10, deskSort: 4, side: 4, leftDominoId: 13, rightDominoId: 19, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 3, tailNum: 2, id: 19, deskSort: 4, side: 3, leftDominoId: 10, rightDominoId: 2, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 2, tailNum: 0, id: 9, deskSort: 4, side: 3, leftDominoId: 19, rightDominoId: 1, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 0, tailNum: 0, id: 1, deskSort: 4, side: 1, leftDominoId: 9, rightDominoId: 12, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 5, tailNum: 0, id: 12, deskSort: 4, side: 4, leftDominoId: 1, rightDominoId: 17, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 5, tailNum: 1, id: 17, deskSort: 4, side: 3, leftDominoId: 12, rightDominoId: 2, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 1, tailNum: 1, id: 2, deskSort: 4, side: 1, leftDominoId: 17, rightDominoId: 15, topDomainoId: 0, bottomDominoId: 0 },

            // // { headNum: 3, tailNum: 1, id: 15, deskSort: 4, side: 4, leftDominoId: 2, rightDominoId: 4, topDomainoId: 0, bottomDominoId: 0 },
            // // { headNum: 3, tailNum: 3, id: 4, deskSort: 4, side: 1, leftDominoId: 15, rightDominoId: 0, topDomainoId: 0, bottomDominoId: 0 },

            // // { headNum: 5, tailNum: 4, id: 26, deskSort: 2, side: 1, leftDominoId: 0, rightDominoId: 0, topDomainoId: 6, bottomDominoId: 0 },


            //=======有问题的
            // { headNum: 3, tailNum: 5, id: 24, deskSort: 3, side: 3, leftDominoId: 23, rightDominoId: 6, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 3, tailNum: 4, id: 23, deskSort: 3, side: 4, leftDominoId: 11, rightDominoId: 24, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 0, tailNum: 4, id: 11, deskSort: 3, side: 3, leftDominoId: 8, rightDominoId: 23, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 0, tailNum: 1, id: 8, deskSort: 3, side: 4, leftDominoId: 14, rightDominoId: 11, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 1, tailNum: 2, id: 14, deskSort: 3, side: 4, leftDominoId: 21, rightDominoId: 8, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 2, tailNum: 5, id: 21, deskSort: 3, side: 4, leftDominoId: 0, rightDominoId: 14, topDomainoId: 0, bottomDominoId: 0 },

            // { headNum: 5, tailNum: 5, id: 6, deskSort: 3, side: 1, leftDominoId: 24, rightDominoId: 28, topDomainoId: 0, bottomDominoId: 26 },
        
            // { headNum: 5, tailNum: 6, id: 28, deskSort: 4, side: 3, leftDominoId: 6, rightDominoId: 13, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 0, tailNum: 6, id: 13, deskSort: 4, side: 4, leftDominoId: 28, rightDominoId: 10, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 0, tailNum: 3, id: 10, deskSort: 4, side: 3, leftDominoId: 13, rightDominoId: 19, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 2, tailNum: 3, id: 19, deskSort: 4, side: 4, leftDominoId: 10, rightDominoId: 2, topDomainoId: 0, bottomDominoId: 0 },
          
            // { headNum: 0, tailNum: 2, id: 9, deskSort: 4, side: 4, leftDominoId: 19, rightDominoId: 1, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 0, tailNum: 0, id: 1, deskSort: 4, side: 1, leftDominoId: 9, rightDominoId: 12, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 0, tailNum: 5, id: 12, deskSort: 4, side: 3, leftDominoId: 1, rightDominoId: 17, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 1, tailNum: 5, id: 17, deskSort: 4, side: 4, leftDominoId: 12, rightDominoId: 2, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 1, tailNum: 1, id: 2, deskSort: 4, side: 1, leftDominoId: 17, rightDominoId: 15, topDomainoId: 0, bottomDominoId: 0 },

            // { headNum: 1, tailNum: 3, id: 15, deskSort: 4, side: 3, leftDominoId: 2, rightDominoId: 4, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 3, tailNum: 3, id: 4, deskSort: 4, side: 1, leftDominoId: 15, rightDominoId: 0, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 4, tailNum: 5, id: 26, deskSort: 2, side: 2, leftDominoId: 0, rightDominoId: 0, topDomainoId: 6, bottomDominoId: 0 },


            { headNum: 3, tailNum: 4, id: 23, deskSort: 3, side: 4, leftDominoId: 11, rightDominoId: 24, topDomainoId: 0, bottomDominoId: 0 },
             { headNum: 3, tailNum: 5, id: 24, deskSort: 4, side: 3, leftDominoId: 23, rightDominoId: 6, topDomainoId: 0, bottomDominoId: 0 },
            { headNum: 0, tailNum: 4, id: 11, deskSort: 3, side: 3, leftDominoId: 8, rightDominoId: 23, topDomainoId: 0, bottomDominoId: 0 },
            { headNum: 0, tailNum: 1, id: 8, deskSort: 3, side: 4, leftDominoId: 14, rightDominoId: 11, topDomainoId: 0, bottomDominoId: 0 },
            { headNum: 1, tailNum: 2, id: 14, deskSort: 3, side: 4, leftDominoId: 21, rightDominoId: 8, topDomainoId: 0, bottomDominoId: 0 },
            { headNum: 2, tailNum: 5, id: 21, deskSort: 3, side: 4, leftDominoId: 0, rightDominoId: 14, topDomainoId: 0, bottomDominoId: 0 },

            { headNum: 5, tailNum: 5, id: 6, deskSort: 3, side: 1, leftDominoId: 24, rightDominoId: 28, topDomainoId: 0, bottomDominoId: 26 },
        
            { headNum: 5, tailNum: 6, id: 28, deskSort: 4, side: 3, leftDominoId: 6, rightDominoId: 13, topDomainoId: 0, bottomDominoId: 0 },
            { headNum: 0, tailNum: 6, id: 13, deskSort: 4, side: 4, leftDominoId: 28, rightDominoId: 10, topDomainoId: 0, bottomDominoId: 0 },
            { headNum: 0, tailNum: 3, id: 10, deskSort: 4, side: 3, leftDominoId: 13, rightDominoId: 19, topDomainoId: 0, bottomDominoId: 0 },
            { headNum: 2, tailNum: 3, id: 19, deskSort: 4, side: 4, leftDominoId: 10, rightDominoId: 2, topDomainoId: 0, bottomDominoId: 0 },
          
            { headNum: 0, tailNum: 2, id: 9, deskSort: 4, side: 4, leftDominoId: 19, rightDominoId: 1, topDomainoId: 0, bottomDominoId: 0 },
            { headNum: 0, tailNum: 0, id: 1, deskSort: 4, side: 1, leftDominoId: 9, rightDominoId: 12, topDomainoId: 0, bottomDominoId: 0 },
            { headNum: 0, tailNum: 5, id: 12, deskSort: 4, side: 3, leftDominoId: 1, rightDominoId: 17, topDomainoId: 0, bottomDominoId: 0 },
            { headNum: 1, tailNum: 5, id: 17, deskSort: 4, side: 4, leftDominoId: 12, rightDominoId: 2, topDomainoId: 0, bottomDominoId: 0 },
            { headNum: 1, tailNum: 1, id: 2, deskSort: 4, side: 1, leftDominoId: 17, rightDominoId: 15, topDomainoId: 0, bottomDominoId: 0 },

            { headNum: 1, tailNum: 3, id: 15, deskSort: 4, side: 3, leftDominoId: 2, rightDominoId: 4, topDomainoId: 0, bottomDominoId: 0 },
            { headNum: 3, tailNum: 3, id: 4, deskSort: 4, side: 1, leftDominoId: 15, rightDominoId: 25, topDomainoId: 0, bottomDominoId: 0 },
            // { headNum: 3, tailNum: 6, id: 25, deskSort: 4, side: 3, leftDominoId: 4, rightDominoId: 0, topDomainoId: 0, bottomDominoId: 0 },
            { headNum: 4, tailNum: 5, id: 26, deskSort: 2, side: 2, leftDominoId: 0, rightDominoId: 0, topDomainoId: 6, bottomDominoId: 0 },


            // {headNum: 4, tailNum: 1, id: 16, deskSort: 3, side: 4, leftDominoId: 17, rightDominoId: 27, topDomainoId: 0, bottomDominoId: 0},
            // {headNum: 6, tailNum: 0, id: 13, deskSort: 4, side: 3, leftDominoId: 27, rightDominoId: 12, topDomainoId: 0, bottomDominoId: 0},
            // {headNum: 5, tailNum: 0, id: 12, deskSort: 4, side: 4, leftDominoId: 13, rightDominoId: 21, topDomainoId: 0, bottomDominoId: 0},
            // {headNum: 5, tailNum: 2, id: 21, deskSort: 4, side: 3, leftDominoId: 12, rightDominoId: 19, topDomainoId: 0, bottomDominoId: 0},
            // {headNum: 3, tailNum: 2, id: 19, deskSort: 4, side: 4, leftDominoId: 21, rightDominoId: 10, topDomainoId: 0, bottomDominoId: 0},
            // {headNum: 3, tailNum: 0, id: 10, deskSort: 4, side: 3, leftDominoId: 19, rightDominoId: 8, topDomainoId:0, bottomDominoId: 0},
            // {headNum: 1, tailNum: 0, id: 8, deskSort: 4, side: 4, leftDominoId: 10, rightDominoId: 14, topDomainoId: 0, bottomDominoId: 0},
            // {headNum: 2, tailNum: 1, id: 14, deskSort: 4, side: 4, leftDominoId: 8, rightDominoId: 20, topDomainoId: 0, bottomDominoId: 0},
            // {headNum: 5, tailNum: 1, id: 17, deskSort: 3, side: 3, leftDominoId: 26, rightDominoId: 16, topDomainoId: 0, bottomDominoId: 0},
            // {headNum: 5, tailNum: 4, id: 26, deskSort: 3, side: 4, leftDominoId: 11, rightDominoId: 17, topDomainoId: 0, bottomDominoId: 0},
            // {headNum: 4, tailNum: 0, id: 11, deskSort: 3, side: 4, leftDominoId: 9, rightDominoId: 26, topDomainoId: 0, bottomDominoId: 0},
           
            // {headNum: 2, tailNum: 0, id: 9, deskSort: 3, side: 3, leftDominoId: 22, rightDominoId: 11, topDomainoId: 0, bottomDominoId: 0},
            // {headNum: 4, tailNum: 2, id: 20, deskSort: 4, side: 4, leftDominoId: 14, rightDominoId: 0, topDomainoId: 0, bottomDominoId: 0},
            // {headNum: 6, tailNum: 2, id: 22, deskSort: 3, side: 3, leftDominoId: 25, rightDominoId: 9, topDomainoId: 0, bottomDominoId: 0},
            // {headNum: 6, tailNum: 3, id: 25, deskSort: 3, side: 4, leftDominoId: 15, rightDominoId: 22, topDomainoId: 0, bottomDominoId: 0},
            // {headNum: 3, tailNum: 1, id: 15, deskSort: 3, side: 4, leftDominoId: 0, rightDominoId: 25, topDomainoId: 0, bottomDominoId: 0},
        ];
    }
}