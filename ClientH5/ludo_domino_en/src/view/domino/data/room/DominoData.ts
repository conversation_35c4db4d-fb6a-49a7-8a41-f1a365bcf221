
module yalla.data {

    export class OutDominoData extends DataItem {
        public id: number = 0;
        public headNum: number = -1;
        public tailNum: number = -1;
        public side: number = 0;                             //上下左右  1 2 3 4

        public leftDominoId: number = -1;                    //左边的牌
        public rightDominoId: number = -1;                   //右边的牌
        public topDomainoId: number = -1;                    //上边的牌
        public bottomDominoId: number = -1;                  //下边的牌

        public xx: number = 0;                               //牌坐标x
        public yy: number = 0;                               //牌坐标y
        public showSide: number = 0;                         //积分场换行（显示方向同实际方向不同）
        public wrapCount: number = 1;                        //第几次换行
        public deskSort:number = 0;

        constructor() {
            super();
        }

        public pos(x: number, y: number): void  {
            this.xx = x;
            this.yy = y;
        }

        public get rotation()  {
            var rotation = 0;
            if (this.side == DirType.BOTTOM) rotation = 180;
            else if (this.side == DirType.LEFT) rotation = 270;
            else if (this.side == DirType.RIGHT) rotation = 90;
            return rotation;
        }

        public get showRotation()  {
            var rotation = 0;
            if (this.showSide == DirType.BOTTOM) rotation = 180;
            else if (this.showSide == DirType.LEFT) rotation = 270;
            else if (this.showSide == DirType.RIGHT) rotation = 90;
            return rotation;
        }

        /**
         * 1 2 垂直  3 4水平
         * 0垂直     1水平
         */
        public get sideType()  {
            if (this.side == DirType.TOP || this.side == DirType.BOTTOM) return HorV.VERTICAL;
            return HorV.HORIZ;
        }

        public get width()  {
            if (this.showSide == DirType.TOP || this.showSide == DirType.BOTTOM) return yalla.data.CardWid;
            return yalla.data.CardWid * 2;
        }

        public get height()  {
            if (this.showSide == DirType.TOP || this.showSide == DirType.BOTTOM) return yalla.data.CardWid * 2;
            return yalla.data.CardWid;
        }

        /**
         * 1 2 垂直  3 4水平
         * 0垂直     1水平
         */
        public get showSideType()  {
            if (this.showSide == DirType.TOP || this.showSide == DirType.BOTTOM) return 0;
            return 1;
        }

        /**
         * 是否双牌
         */
        public get isSame(): boolean  {
            return this.headNum == this.tailNum;
        }
    }
}