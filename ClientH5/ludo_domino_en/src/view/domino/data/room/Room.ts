
module yalla.data {

    export class Room extends DataItem {
        public roomid: number = 0;
        public token: string;
        public roundNum: number = 1;                        //当前局数
        public gameType: number = 0;                        //玩法类型 (0积分场  1轮次场)
        public showId: number = 0;                           //房间号 再来一局传入showid，显示房间id也是showid
        public cost: number;
        public costIndex: number = 0;
        public isPrivate: number = 0;                       //0公开  1私密  3锦标赛  4vip房间 

        public player: RoomInfoPlayerInfo;                  //用户列表
        public playDominoed: Array<PlayerAndDominoes> = [];   //用户多米诺手牌列表
        public remainDominoes: Array<Domino> = [];            //自己的可翻牌列表
        public deskDominoes: Array<Domino> = [];               //桌面上已经存在的牌

        public deskPool: yalla.data.Base;                   //已出桌面牌数据
        public selectCardId: number = 0;                    //当前选择的手牌id
        public playerNums: number = 0;                      //房间人数
        public playerRoundTime: number = 0;                  //玩家轮次时长 结算s
        public gameRoundTime: number = 0;                    //游戏轮次间隔时长 每局 ms
        // public usedRoundCount:number = 0;                //已经使用的延长次数
        // public isAddCurrent:boolean = false;             //本回合是否延长过
        public leftDiamond: number = 0;                      //剩余钻石
        public nextDiamond: number = 0;                      //下次延时需要钻石
        public roundLeftTime: number = 0;                    //当前轮次剩余时间 s
        public roundTime: number = 0;                        //当前轮次总时间   s
        public addTimeCost: Array<number> = [];              //每次消耗钻石
        public isUseAddTime = false;                         //是否增加回合时间，切换用户清理

        private _activeIdx: number = 0;                     //当前房间中被激活的玩家
        private _playScoreList = {};                        //角色积分(积分场 玩家总积分    轮次场 玩家实时总分)

        private _playerHash: Object = {};
        private _playDominoedHash: Object = {};

        public quitPunishTime: number = 60;
        public gameBeginTime: number = 0;
        /**是否托管 */
        public isTrust: boolean = false;
        public watchNum: number = 0;
        public ScoreList: Array<any> = [];//分数
        /**购买同款 传入用户列表 */
        public buyPlayerList = [];

        constructor() {
            super();
        }

        public update(d: Object): void {
            super.update(d);
            if (!d) return;
            if (d['player']) {
                var pList = d['player']['player'];
                for (var k in pList) {
                    this.updatePlayerScore(pList[k].fPlayerInfo.idx, pList[k].score);
                }
            }
            if (d['playerList']) {
                var playerList = d['playerList'];
                var showPlayerList = [];
                for (var k in playerList) {
                    showPlayerList.push({
                        fPlayerInfo: {
                            faceUrl: playerList[k].url,
                            idx: playerList[k].idx,
                            nikeName: playerList[k].name,
                            level: 0,
                            winCount: 0,
                            totalCount: 0
                        }
                    });
                }
                var p: any = { player: showPlayerList };
                this.player = p;
            }
        }

        get activeIdx() {
            return this._activeIdx;
        }
        set activeIdx(value: number) {
            this._activeIdx = value;
        }

        get isMyActive(): boolean {
            return this._activeIdx == yalla.data.UserService.instance.user.idx;
        }

        public getPlayerScore(idx: number): number {
            return this._playScoreList[idx] || 0;
        }

        /**
         * enterRoom
         * @param msg 
         */
        public initData(msg: any): void {
            this.update(msg);
            this.isTrust = false;//断线重连上来，取消托管，因服务端没有传这个字段，这里写死
            this.setDominoPool();
            this.sortPlayer();
            // this.checkRoundTimeFree()
        }

        /**
         * enterRoom
         * @param msg 
         */
        public initWatchData(msg: any): void {
            this.update(msg);
            this.player = { player: msg.player };
            this.setDominoPool();
            this.sortPlayer();
            // this.checkRoundTimeFree()
        }

        public initScore(scoreList: any): void {
            for (var k in scoreList) {
                this.updatePlayerScore(scoreList[k].idx, scoreList[k].score);
            }
        }

        /**
         * @param idx 
         * @param score 
         * @param mode  0替换 1累加
         */
        public updatePlayerScore(idx: number, score: number, mode: number = 0): void {
            if (!this._playScoreList[idx]) this._playScoreList[idx] = 0;

            if (mode == 0) {
                // if (score && score > 0) this._playScoreList[idx] = score;
                this._playScoreList[idx] = score;
            } else {
                this._playScoreList[idx] += score;
            }
        }

        /**
         * 等级1级，强制修改首次消耗为0钻石
         */
        // public checkRoundTimeFree():void{
        //     if(yalla.data.UserService.instance.user.playerShowInfo.fPlayerInfo.level <= 1){
        //         if(this.addTimeCost) this.addTimeCost[0] = 0;
        //     }
        // }
        public isRoundTimeFree(cost: number): boolean {
            var usr = yalla.data.UserService.instance.user;
            if (this.addTimeCost && this.addTimeCost.indexOf(cost) == 0 && usr.playerShowInfo.fPlayerInfo.level <= 1) return true;
            return false;
        }
        /**
         * 当前钻石是否足够
         */
        public isLessDiamond(): boolean {
            var isLess = (this.leftDiamond < this.nextDiamond && this.nextDiamond != -1);
            if (isLess && this.isRoundTimeFree(this.nextDiamond)) isLess = false;
            return isLess;
        }

        public updateRoundTime(msg: any): void {
            if (msg.roundLeftTime) this.roundLeftTime = msg.roundLeftTime;
            if (msg.roundTime) this.roundTime = msg.roundTime;
            if (msg.nextDiamond) this.nextDiamond = msg.nextDiamond;
            if (msg.leftDiamond) this.leftDiamond = msg.leftDiamond;
        }

        /**
         * 剩余增加延时次数
         */
        public get remainRoundCounts(): number {
            if (!this.nextDiamond || this.nextDiamond == -1) return 0;
            var len = this.addTimeCost.length;
            // if(this.nextDiamond > this.addTimeCost[len-1]) return 0;
            return len - this.addTimeCost.indexOf(this.nextDiamond)
        }

        /**
         * 更新coin
         * @param d 
         */
        public addCoin(d: any): void {
            if (!d) return;
            if (d.diamond > 0) {
                this.leftDiamond += parseInt(d.diamond);
            }
        }

        /**
         * 出牌后清理数据
         */
        public clearPlayCard(): void {
            if (this.deskPool) this.deskPool.posList = [];
            this.selectCardId = 0;
        }

        /**
         * 一局结束后重置
         */
        public reset(): void {
            this._playDominoedHash = {};
            this.playDominoed = [];
            this.deskDominoes = [];
            this.remainDominoes = [];
            this.activeIdx = 0;
            this.clearPlayCard();
            this.deskPool && this.deskPool.initData();
        }

        /**
         * 生成房间类型对应的桌面牌数据池
         */
        public setDominoPool(): void {
            if (this.gameType == 0) {
                this.deskPool = new yalla.data.DrawGamePool();
            } else if (this.gameType >= 1) {
                this.deskPool = new yalla.data.MugginsPool();
            }
            this.playerNums = this.player.player.length;
            this._playerHash = {};//TODO::onLogin获得角色信息是基本的，QuickEnterRoomResponse 需要把player信息更新
        }

        /**
         * 角色排序
         */
        public sortPlayer(): void {
            if (!this.player) return;
            //购买同款用户数据筛选
            var players = [];
            var base64Encode = laya.utils.Browser.window.Base64.encode;
            this.player.player.forEach(val => {
                var f = val.fPlayerInfo;
                if (f.idx != Global.Account.idx) {
                    players.push({
                        idx: f.idx,
                        faceUrl: base64Encode(f.faceUrl),
                        nickName: base64Encode(f.nikeName),
                        color: 3
                    })
                }
            });
            this.buyPlayerList = players;

            var myIdxIndex = -1;
            this.player.player.forEach((value: playerShowInfo, index: number, arr: Array<playerShowInfo>) => {
                // value.roylevel = 5;
                // value.fPlayerInfo.viplevel = 2;
                if (value.fPlayerInfo.idx == yalla.data.UserService.instance.user.idx) {
                    myIdxIndex = index;
                    return true;
                }
            });
            if (myIdxIndex > 0) {
                var list1 = this.player.player.slice(0, myIdxIndex);
                var list2 = this.player.player.slice(myIdxIndex);
                this.player.player = list2.concat(list1);
            }
        }

        /**
         * 根据idx获取角色信息
         */
        public getPlayerByIdx(idx: number): playerShowInfo {
            if (!this.player) return;
            if (this._playerHash[idx]) return this._playerHash[idx];
            this.player.player.forEach((value: playerShowInfo, index: number, arr: Array<playerShowInfo>) => {
                this._playerHash[value.fPlayerInfo.idx] = value;
            });
            return this._playerHash[idx];
        }

        /** 是否尊贵的royal level >= 4 */
        public get isHonorRoyal(): boolean {
            var players = this.player.player;
            for (var key in players) {
                var element: playerShowInfo = players[key];
                if (element.realRoyLevel >= 4) return true;
            }
            return false;
        }

        /**
         * 根据当前idx获取下一个角色
         */
        public getNextPlayer(idx: number): playerShowInfo {
            if (!this.player) return;
            var len = this.player.player.length;
            var d: playerShowInfo;
            this.player.player.forEach((value: playerShowInfo, index: number, arr: Array<playerShowInfo>) => {
                if (idx == value.fPlayerInfo.idx) {
                    if (index >= len - 1) {
                        d = this.player.player[0];
                        return true;
                    }
                    d = this.player.player[index + 1];
                    return true;
                }
            });
            return d;
        }


        /**
         * 根据idx获取角色信息
         * @param idx 
         */
        public getPlayerAndDominoByIdx(idx: number): PlayerAndDominoes {
            if (this._playDominoedHash[idx]) return this._playDominoedHash[idx];

            if (this.playDominoed) {
                this.playDominoed.forEach((value: PlayerAndDominoes, index: number, arr: Array<PlayerAndDominoes>) => {
                    this._playDominoedHash[value.playerId] = value;
                });
            }
            return this._playDominoedHash[idx];
        }

        /**
         * 重新构造每局结果数据列表
         */
        public updateRoundResult(losersInfo: Array<PlayerDomino>, propType: string): Array<DefineRoundResult> {
            if (!losersInfo || losersInfo.length < 1) return [];

            var arr = [];
            var len = losersInfo.length;
            for (var i = 0; i < len; i++) {
                var item = losersInfo[i];
                // yalla.Debug.log(JSON.stringify(item));
                var d = {
                    idx: item.playerId, intergral: item.playerIntergral, nickName: item.playerNickName,
                    roundIntergral: item.playerRoundIntergral, domino: item.dominoes, exp: item.exp
                };
                arr.push(d);
            }

            arr.sort((a: DefineRoundResult, b: DefineRoundResult) => {
                if (a[propType] < b[propType]) {
                    return 1;
                } else if (a[propType] > b[propType]) {
                    return -1;
                } else {
                    return 0;
                }
            });

            return arr;
        }

        /**
         * 移除手牌
         * @param idx 
         * @param cardId 
         */
        public removeHandCard(idx: number, d: Domino): void {
            var handCard = this.getPlayerAndDominoByIdx(idx);
            var list = handCard ? handCard.dominoes : [];
            var len = list.length;
            for (var i = 0; i < len; i++) {
                if (!list[i] || (d && list[i].id == d.id)) {
                    list.splice(i, 1);
                    len = list.length;
                    i -= 1;
                }
            }
            // if (list[i].id == d.id) {
            //     list.splice(i, 1);
            //     return;
            // }
        }

        /**
         * 翻牌后 增加手牌
         * @param idx 
         * @param d 
         */
        public addHandCard(idx: number, fillList: Array<Domino>): PlayerAndDominoes {
            var handCard = this.getPlayerAndDominoByIdx(idx);
            var list = handCard ? handCard.dominoes : [];
            if (handCard) handCard.dominoes = list.concat(fillList)
            return handCard;
        }

        /**
         * 移除翻牌数据
         * @param cardId 
         */
        public removeRemainCard(cardId: number): number {
            var len = this.remainDominoes.length;
            for (var i = 0; i < len; i++) {
                if (this.remainDominoes[i].id == cardId) {
                    this.remainDominoes.splice(i, 1);
                    return i;
                }
            }
            return -1;
        }

        /**
         * 根据idx获取角色座位
         */
        public getSideByIdx(idx: number): number {
            var side = 0;
            if (!this.player) return side;
            if (this.player.player.length == 2 && idx != yalla.data.UserService.instance.user.idx) side = 2;
            else {
                this.player.player.forEach((value: playerShowInfo, index: number, arr: Array<playerShowInfo>) => {
                    if (value.fPlayerInfo.idx == idx) {
                        side = index;
                        return true;
                    }
                });
            }
            return side;
        }

        // /**
        //  * 目前是卡牌皮肤对别人也可见
        //  * @param idx 
        //  */
        // public getCardSkinIdByIdx(idx: number): number{
        //     var v = 0;
        //     this.player.player.forEach((value: playerShowInfo, index: number, arr: Array<playerShowInfo>) => {
        //         if (value.fPlayerInfo.idx == yalla.data.UserService.instance.user.idx) {
        //             v = value.cardSkinId;
        //             return true;
        //         }
        //     });
        //     return v;    
        // }

        public clear() {
            this.reset();
            this.roomid = 0;
            this.token = '';
            this.isTrust = false;
            this.playerRoundTime = 0;
            this.roundLeftTime = 0;
            this.roundTime = 0;
            this.gameRoundTime = 0;
            this.player = null;
            this._playScoreList = {};
            this._playerHash = {};
            this.buyPlayerList = [];
        }
    }
}