module yalla.data {

    export class Base {
        public startCardData: OutDominoData;             //首牌双
        public firstCardData: OutDominoData;

        public left: SideData=new SideData();                           //左可以接的牌列表{id num list可以接的牌列表}          DrawGame && Muggin
        public right: SideData=new SideData();                          //右可以接的牌列表{id num list可以接的牌列表}          DrawGame && Muggin
        public top: SideData=new SideData();                            //上面可以接的牌列表{id num list可以接的牌列表}        Muggin
        public bottom: SideData=new SideData();                         //下面可以接的牌列表{id num list可以接的牌列表}        Muggin

        public cardHash: Object = {};                    //已出牌的集合
        public waitPlayCardHash: Object = {};            //筛选出一组可以出的牌集合
        public posList: Array<any> = [];

        public wrapSideCardHash: any = {};               //四个方向 换行对应的牌列表
        public currentWrapSide: Object = {};             //当前方向 换行次数
        public currentScale: number = 1.6;
        public MinScale: number = 0.5;//0.52;                  //最小缩放比
        public MaxScale: number = 5;

        public deskSpWid: number = 0;                    //牌面容器最大宽度
        public deskSpHei: number = 0;                    //牌面容器最大高度
        public applyCardX: number = 0;                   //补牌的初始坐标
        public applyCardY: number = 0;                   //补牌的初始坐标
        public deskSpIndex: number = 0;                  //补牌出牌所在层

        //轮次场
        public leftList: Array<OutDominoData> = [];
        public rightList: Array<OutDominoData> = [];
        public topList: Array<OutDominoData> = [];
        public bottomList: Array<OutDominoData> = [];
        public allCardList: Array<OutDominoData> = [];
        public hWrapSideHashNums: Object = {};   //desksort=3,4 的转行情况
        public vWrapSideHashNums: Object = {};   //desksort=1,2 的转行情况
        public hWrapIDHash: Object = {};         //desksort=3,4 水平换行，双牌方向特殊处理
        public vWrapIDHash: Object = {};         //desksort=1,2 水平换行，双牌方向特殊处理
        public isUpdateAll: boolean = false;
        public tempAddId: Array<number> = [];//TODO:: cardHash 已经提前存储桌牌，，这里主要存储第一个双牌，反推left|right的牌（还未add的先不反推）
        public calc_scale_hintCard = 0;      //选择提示牌需要计算desksp 的scale 次数，只需要1次就行

        constructor() {
        }

        public initData(): void  {
            this.startCardData = null;
            this.firstCardData = null;
            this.currentScale = 1.6;
            this.calc_scale_hintCard = 0;
            this.waitPlayCardHash = {};
            this.wrapSideCardHash = {};
            this.currentWrapSide = {};
            this.cardHash = {};
            this.posList = [];
            this.tempAddId = [];

            if (!this.left) this.left = new SideData();
            else this.left.reset();

            if (!this.right) this.right = new SideData();
            else this.right.reset();

            if (!this.top) this.top = new SideData();
            else this.top.reset();

            if (!this.bottom) this.bottom = new SideData();
            else this.bottom.reset();

            this.leftList = [];
            this.rightList = [];
            this.topList = [];
            this.bottomList = [];
            this.allCardList = [];
            this.hWrapSideHashNums = {};
            this.vWrapSideHashNums = {};
            this.hWrapIDHash = {};
            this.vWrapIDHash = {};
        }
        public initGameAddCard(cardList: Array<any>): void {
            var len = cardList.length;
            if (!this.cardHash) this.cardHash = {};
            for (var i = 0; i < len; i++) {
                var outData = new yalla.data.OutDominoData();
                outData.update(cardList[i]);
                this.cardHash[outData.id] = outData;
            }
        }

        public existSameCard(idList: Array<Domino>): boolean {
            for (var k in idList) {
                if (this.cardHash[idList[k].id]) return true;
            }
            return false;
        }

        public add(data: any): OutDominoData  {
            this.calc_scale_hintCard = 0;
            return null;
        }

        public filterPlayCardList(handList: Array<Domino>): void  {
            this.waitPlayCardHash = {};
        }

        public fileterCardPos(d: Domino): void  {
            this.posList = [];
        }
        public updateCardBlockPos(minXData: any, minYData: any, cardData: OutDominoData): void {
        }

        /**
         * x方向最多可以放多少个块
         */
        public getHorizMaxCardNums(): number {
            var num: number = Math.floor((this.deskSpWid / yalla.data.CardWid) / this.MinScale - 3);
            return num;
        }

        /**
         * 出牌区缩放比
         */
        public getVertMaxCardNums(): number {
            var num: number = Math.floor((this.deskSpHei / yalla.data.CardWid) / this.MinScale - 3);
            return num;
        }

        /**
         * 出牌区缩放比
         * @param xNum  x方向单块数量
         */
        public getScaleX(xNum: number): number {
            var scaleX: number = (this.deskSpWid / yalla.data.CardWid) / (xNum + 3);
            scaleX = Math.min(this.MaxScale, scaleX);
            var scale = Math.max(scaleX, this.MinScale);
            return scale;
        }

        /**
         * 出牌区缩放比
         * @param yNum  y方向单块数量
         */
        public getScaleY(yNum: number): number {
            var scaleY: number = (this.deskSpHei / yalla.data.CardWid) / (yNum + 3);
            scaleY = Math.min(this.MaxScale, scaleY);
            var scale = Math.max(scaleY, this.MinScale);
            return scale;
        }

        /**
         * 上下左右分别进行了几次换行
         * @param deskSide 
         */
        public getSideNum(deskSide: number): number  {
            var sideWrapCount = this.currentWrapSide[deskSide];
            return sideWrapCount || 1;
        }

        /**
         * 转的方向是 1水平 还是 0垂直 
         * @param sideNum 
         * @param deskSide 
         */
        public getSideType(sideNum: number, deskSide: number, connectOutData: OutDominoData): number  {
            if (deskSide == DirType.LEFT || deskSide == DirType.RIGHT) {
                return sideNum % 2;
            }
            return 1 - sideNum % 2;
        }

        public getSendPlayCardParams(sendCardID: number, deskSort: number): any { }

        public getScore(d: Domino = null, deskSort: number = 1): number { return 0; }

        public getNextInfo(deskSide: number, outData: any, connectOutData: OutDominoData, isHint: boolean = false): Array<any>  {
            return [];
        }
        public isMath(num: number, numList: Array<number>): boolean  {
            if (num > -1 && numList.indexOf(num) > -1) return true;
            return false;
        }

        public getDistance(outData: any, connectOutData: OutDominoData)  {
            var dis = 0;
            var outSideType = (outData.showSide == DirType.TOP || outData.showSide == DirType.BOTTOM) ? HorV.VERTICAL : HorV.HORIZ;
            var connectSideType = (connectOutData.showSide == DirType.TOP || connectOutData.showSide == DirType.BOTTOM) ? HorV.VERTICAL : HorV.HORIZ;
            if (connectSideType != outSideType && !(this.vWrapIDHash[connectOutData.id] || this.hWrapIDHash[connectOutData.id])) {
                dis = yalla.data.CardWid + yalla.data.CardWid / 2;
            } else {
                dis = yalla.data.CardWid * 2;
            }
            return dis;
        }

        /**
         * 获取水平 垂直系数
         */
        public getSideValue(deskSide: number, sideNum: number): Array<number>  {
            var hv = 1;
            var vv = 1;
            if (deskSide == DirType.TOP) {
                vv = yalla.data.GameConfig.ColHash[sideNum] * -1;
                hv = -1;
            } else if (deskSide == DirType.BOTTOM) {
                vv = yalla.data.GameConfig.ColHash[sideNum];
            } else if (deskSide == DirType.LEFT) {
                hv = yalla.data.GameConfig.RowHash[sideNum] * -1;
            } else if (deskSide == DirType.RIGHT) {
                hv = yalla.data.GameConfig.RowHash[sideNum];
                vv = -1;
            }
            return [hv, vv];
        }

        /**
         * 每一次换行的 数据列表
         * @param deskSide 
         * @param sideNum 
         * @param outData 
         */
        public setSideListData(deskSide, sideNum, outData: OutDominoData): void {
            if (!this.wrapSideCardHash[deskSide]) this.wrapSideCardHash[deskSide] = {};
            if (!this.wrapSideCardHash[deskSide][sideNum]) this.wrapSideCardHash[deskSide][sideNum] = [];
            this.wrapSideCardHash[deskSide][sideNum].push(outData);
        }

        /**
         * 根据方向 获取那一行牌的列表
         * @param deskSide 
         * @param sideNum 
         */
        public getSideListData(deskSide, sideNum: number): Array<OutDominoData>  {
            if (!this.wrapSideCardHash[deskSide]) return [];
            return this.wrapSideCardHash[deskSide][sideNum] || [];
        }

        /**
         * 垂直方向单块个数
         * @param vList 
         */
        public getVerticalLen(vList: Array<any>): number  {
            var num = 0;
            var len = vList.length;
            for (var i = 0; i < len; i++) {
                num += this.getCardBlockNum(0, vList[i].side);
            }
            num = Math.max(num, 1);
            return num;
        }
        /**
         * 水平方向 单块个数
         * @param hList 
         */
        public getHorizontalLen(hList: Array<any>): number  {
            var num = 0;
            var len = hList.length;
            for (var i = 0; i < len; i++) {
                num += this.getCardBlockNum(1, hList[i].side);
            }
            num = Math.max(num, 1);
            return num;
        }
        /**
         * 根据牌方向获取 所在垂直方向或者水平方向占的单块数
         * @param sideType 
         * @param side 
         */
        public getCardBlockNum(sideType: number, side: number): number  {
            var num = 0;
            if (sideType == HorV.HORIZ) {
                if (side == DirType.TOP || side == DirType.BOTTOM) num += 1;
                else num += 2;
            } else {
                if (side == DirType.TOP || side == DirType.BOTTOM) num += 2;
                else num += 1;
            }
            return num;
        }

        public clear(): void{
            this.waitPlayCardHash = null;
            this.wrapSideCardHash = null;
            this.cardHash = null;
        } 
    }

    export class SideData {
        public id: number = 0;
        public num: number = -1;
        public side: number = 1;     //1上 2下 3左 4右
        public isSame: boolean = false;//是否双牌

        public update(id, num, side, isSame: boolean): void  {
            this.id = id;
            this.num = num;
            this.side = side;
            this.isSame = isSame;
        }

        /**
         * 十字模式计算积分，需要知道点数
         */
        public get totalNum(): number  {
            if (this.isSame) return this.num * 2;
            return this.num;
        }

        public reset(): void {
            this.id = 0;
            this.num = -1;
            this.side = 1;
            this.isSame = false;
        }
    }
}