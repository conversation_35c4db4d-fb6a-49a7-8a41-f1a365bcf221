module yalla.data {
    /**
     * 横竖 模式
     * 第一张双牌  默认方向side=1
     */
    export class DrawGamePool extends Base {

        constructor() {
            super();
        }

        /**
         * 更新水平方向 left right 当前可以接牌的信息{id, num, list}
         * 如果左右left right还没有属性，则默认设置第一张牌
         */
        public updatePickUpCard(cardData: yalla.data.OutDominoData, deskSide: number): void  {
            var isSame = cardData.isSame;
            //首张双牌
            if (this.left.id < 1 && this.right.id < 1 || this.startCardData == cardData) {
                if (isSame) {
                    if (this.left.id < 1 || deskSide == DirType.LEFT) this.left.update(cardData.id, cardData.headNum, cardData.side, isSame);
                    if (this.right.id < 1 || deskSide == DirType.RIGHT) this.right.update(cardData.id, cardData.headNum, cardData.side, isSame);
                }else{
                    if (cardData.side == DirType.LEFT) {
                        this.left.update(cardData.id, cardData.headNum, cardData.side, isSame);
                        this.right.update(cardData.id, cardData.tailNum, cardData.side, isSame);
                    } else if (cardData.side == DirType.RIGHT) {
                        this.left.update(cardData.id, cardData.tailNum, cardData.side, isSame);
                        this.right.update(cardData.id, cardData.headNum, cardData.side, isSame);
                    }
                }
                return;
            }

            var num = 0;
            if (cardData.side == DirType.TOP || cardData.side == DirType.BOTTOM) {//双牌(上下方向)
                num = cardData.headNum;
            } else {//单牌(左右方向)
                if (deskSide == DirType.LEFT) {
                    num = cardData.side == DirType.LEFT ? cardData.headNum : cardData.tailNum;
                } else if (deskSide == DirType.RIGHT) {
                    num = cardData.side == DirType.LEFT ? cardData.tailNum : cardData.headNum;
                }
            }

            if (cardData.rightDominoId == this.left.id) {//左边更新
                this.left.update(cardData.id, num, cardData.side, isSame);
            }
            if (cardData.leftDominoId == this.right.id) {//右边更新
                this.right.update(cardData.id, num, cardData.side, isSame);
            }
        }

        /**
         * 出牌或者自动收到出牌协议
         * 记录首牌
         * @param msg
         */
        public add(msg: any): yalla.data.OutDominoData  {
            super.add(msg);
            // if (!msg || msg.id < 1) return;
            if (!msg) return;
            var outData = new yalla.data.OutDominoData();
            outData.update(msg);

            var connectOutData = outData;
            var deskSide = msg.deskSort; //1234相对桌面，出的牌在上下左右位置
            if(deskSide == 0) {
                if(outData.isSame) deskSide = DirType.TOP;
                else deskSide = DirType.LEFT;
            }
            outData.deskSort = deskSide;
            if (this.left.id < 1 && this.right.id < 1) {
                outData.pos(0, 0);
            }else{
                //出A牌后，连接的B牌，他相对A牌的属性也需要更新
                if (deskSide == DirType.LEFT && this.cardHash[outData.rightDominoId]) {
                    connectOutData = this.cardHash[outData.rightDominoId];
                    connectOutData.leftDominoId = outData.id;
                } else if (deskSide == DirType.RIGHT && this.cardHash[outData.leftDominoId]) {
                    connectOutData = this.cardHash[outData.leftDominoId];
                    connectOutData.rightDominoId = outData.id;
                }
            }

            if (!this.firstCardData) {
                this.firstCardData = outData;
                // Debug.log("===不能出牌 55===cId:"+outData.id);
            }
            
            if (!this.startCardData && outData.isSame) {
                this.startCardData = outData;//首牌
            }

            this.updateWrap(outData, connectOutData, deskSide);//1234相对桌面，出的牌在上下左右位置
            this.updatePickUpCard(outData, deskSide);

            if (!this.cardHash) this.cardHash = {};
            this.cardHash[outData.id] = outData;
            return outData;
        }

        /**
         * 桌面上的牌 首 尾 是否跟自己的手牌有匹配的
         * TODO::服务端把首次可以出的牌放在首牌列表头部
         * 积分场第一轮 第一张是双牌，，后面单双牌皆可
         * @param handList 
         */
        public filterPlayCardList(handList: Array<Domino>): void  {
            super.filterPlayCardList(handList);

            if (!this.left && !this.right) return; //TODO::避免先收到status_change 再收到 Domino_info
            
            var handLen = handList.length;
            // yalla.Debug.log(yalla.data.RoomService.instance.room.roundNum+":roundNum===不能出牌33===firstCardData:"+!this.firstCardData);
            if (yalla.data.RoomService.instance.room.roundNum <= 1) {
                if (!this.firstCardData) {
                    var sameBigID = -1;//双牌最大点数的显示
                    for (var i = 0; i < handLen; i++) {
                        if(handList[i].headNum == handList[i].tailNum && handList[i].id > sameBigID){
                            sameBigID = handList[i].id;
                        }
                    }
                    if (sameBigID > 0) {
                        this.waitPlayCardHash[sameBigID] = yalla.data.MapList[sameBigID];
                    }
                    return;
                }
            }else{
                if (this.left.id < 1 && this.right.id < 1) {
                    for (var i = 0; i < handLen; i++) {
                        var id = handList[i].id;
                        this.waitPlayCardHash[id] = yalla.data.MapList[id];
                    }
                    // Debug.log("==筛选可出牌 0==");
                    return;
                }
            }

            // Debug.log("==筛选可出牌 1==");
            for (var i = 0; i < handLen; i++) {
                var itemData = handList[i];
                var numList = [itemData.tailNum, itemData.headNum];
                var isLeftMatch = this.isMath(this.left.num, numList);
                var isRightMatch = this.isMath(this.right.num, numList);
                if (isLeftMatch || isRightMatch) {
                    this.waitPlayCardHash[itemData.id] = yalla.data.MapList[itemData.id];
                }
            }
        }

        /**
         * 点击手牌中可出牌，筛选出可以放置的位置
         * @param d 
         */
        public fileterCardPos(d: Domino): void  {
            super.fileterCardPos(d);
            var isLeftMatch = this.isMath(this.left.num, [d.tailNum, d.headNum]);
            var isRightMatch = this.isMath(this.right.num, [d.tailNum, d.headNum]);
            var sideOutData;
            var side = 0;
            if (isLeftMatch) {
                sideOutData = this.cardHash[this.left.id];
                if (d.tailNum == d.headNum) side = DirType.TOP;
                else side = (sideOutData.headNum == this.left.num) ? DirType.RIGHT : DirType.LEFT;
                this.posList.push({ deskSort: DirType.LEFT, sideDominoId: sideOutData.id, tailNum: d.tailNum, headNum: d.headNum, side: side, rightDominoId:this.left.id });
            }

            if (isRightMatch) {
                sideOutData = this.cardHash[this.right.id];
                if (d.tailNum == d.headNum) side = DirType.TOP;
                else side = (sideOutData.headNum == this.right.num) ? DirType.LEFT : DirType.RIGHT;
                this.posList.push({ deskSort: DirType.RIGHT, sideDominoId: sideOutData.id, tailNum: d.tailNum, headNum: d.headNum, side: side, leftDominoId:this.right.id });
            }
        }

        /**
         * 根据出的牌，找出牌的相邻id  发牌传输的参数
         */
        public getSendPlayCardParams(sendCardID: number, deskSort: number): any {
            var sideDominoId = 0;
            if (deskSort == DirType.TOP && !this.startCardData) {
                sideDominoId = 0;
            } else if (deskSort == DirType.LEFT) {
                sideDominoId = this.left.id;
            } else if (deskSort == DirType.RIGHT) {
                sideDominoId = this.right.id;
            }
            return { 'dominoId': sendCardID, 'sideDominoId': sideDominoId, 'deskSort': deskSort };
        }

        /**
         * 检测换行
         */
        public updateWrap(outData: yalla.data.OutDominoData, connectOutData: yalla.data.OutDominoData, deskSide: number): void  {
            var result = this.getNextInfo(deskSide, outData, connectOutData);//[nextD, {isWrap:isWrap, sideNum:sideNum}];
            var nextD = result[0];
            var sideNum = result[1]['sideNum'];
            outData.showSide = nextD.showSide;
            outData.pos(nextD.xx, nextD.yy);

            this.currentWrapSide[deskSide] = sideNum;
            this.setSideListData(deskSide, sideNum, outData);
        }

        private isWrap(outData: any, list: Array<yalla.data.OutDominoData>, sideType: number): boolean  {
            var len = list.length;
            var num = 0;
            for (var i = 0; i < len; i++) {
                num += this.getCardBlockNum(sideType, list[i].showSide);
            }
            num += this.getCardBlockNum(sideType, outData.side || 0);
            var scale: number = this.getScaleX(num);
            this.currentScale = Math.max(Math.min(this.currentScale, scale), this.MinScale);
            if (this.currentScale > 0.6) this.currentScale = this.currentScale * 0.95;
            if (sideType == 1) {
                if (scale <= this.MinScale) return true;
            } else {
                if (num >= 1) return true;
            }
            return false;
        }

        /**
         * @param deskSide 
         * @param outData  {sideType,tailNum,headNum,side}
         * @param connectOutData 
         */
        public getNextInfo(deskSide: number, outData: any, connectOutData: yalla.data.OutDominoData, isHint: boolean = false): Array<any>  {
            super.getNextInfo(deskSide, outData, connectOutData, isHint);
            var sideNum = this.getSideNum(deskSide);
            var sideType = this.getSideType(sideNum, deskSide,connectOutData);
            var vList = this.getSideValue(deskSide, sideNum);
            var hv = vList[0];
            var vv = vList[1];
            var isSame = outData.tailNum == outData.headNum;
            var nextD = { id: outData.id, showSide: outData.side };

            var hList = this.getSideListData(deskSide, sideNum);
            if (sideNum == 1) hList = hList.concat(this.getSideListData(7 - deskSide, sideNum));
            var isWrap = this.isWrap(outData, hList, sideType);
            if(this.left.id == this.right.id && this.startCardData == outData) isWrap = false;
            if (isWrap) {
                sideNum += 1;
                sideType = this.getSideType(sideNum, deskSide,connectOutData);
                vList = this.getSideValue(deskSide, sideNum);
                hv = vList[0];
                vv = vList[1];

                if (sideType == HorV.VERTICAL) {//此行垂直
                    if(!isSame){
                        if (outData.side == DirType.LEFT) nextD['showSide'] = DirType.BOTTOM;
                        else nextD['showSide'] = DirType.TOP;
                    }
                    if (connectOutData.isSame) {
                        nextD['xx'] = connectOutData.xx;
                        nextD['yy'] = connectOutData.yy + yalla.data.CardWid * 2 * vv;
                    } else {
                        nextD['xx'] = connectOutData.xx + yalla.data.CardWid / 2 * hv;
                        nextD['yy'] = connectOutData.yy + yalla.data.CardWid * 1.5 * vv;
                    }
                } else {//此行水平
                    if (isSame) {
                        nextD['showSide'] = DirType.LEFT;
                        nextD['xx'] = connectOutData.xx + yalla.data.CardWid / 2 * hv;
                        nextD['yy'] = connectOutData.yy + yalla.data.CardWid * 1.5 * vv;
                    } else {
                        
                        if ((hv < 0 && deskSide == DirType.RIGHT) || (hv > 0 && deskSide == DirType.LEFT)) {
                            if (outData.side == DirType.LEFT) nextD['showSide'] = DirType.RIGHT;
                            else if (outData.side == DirType.RIGHT) nextD['showSide'] = DirType.LEFT;
                        }
                        nextD['xx'] = connectOutData.xx + yalla.data.CardWid / 2 * hv;
                        nextD['yy'] = connectOutData.yy + yalla.data.CardWid * 1.5 * vv;
                    }
                }
            } else {
                var dis = 0;
                if (sideType == HorV.VERTICAL) {
                     if(this.left.id == this.right.id && this.startCardData == outData){//首牌&双牌
                        nextD['showSide'] = outData.side;
                    }else{
                        if (outData.side == DirType.TOP || outData.side == DirType.BOTTOM) nextD['showSide'] = DirType.LEFT;
                        else if (outData.side == DirType.LEFT) nextD['showSide'] = DirType.BOTTOM;
                        else nextD['showSide'] = DirType.TOP;
                    }

                    dis = this.getDistance(nextD, connectOutData);
                    nextD['xx'] = connectOutData.xx;
                    nextD['yy'] = connectOutData.yy + dis * vv;

                } else {
                    if ((hv < 0 && deskSide == DirType.RIGHT) || (hv > 0 && deskSide == DirType.LEFT)) {
                        if (outData.side == DirType.LEFT) nextD['showSide'] = DirType.RIGHT;
                        else if (outData.side == DirType.RIGHT) nextD['showSide'] = DirType.LEFT;
                    }
                    dis = this.getDistance(nextD, connectOutData);
                    nextD['xx'] = connectOutData.xx + dis * hv;
                    nextD['yy'] = connectOutData.yy;
                }
            }
            return [nextD, { isWrap: isWrap, sideNum: sideNum }];
        }
    }
}