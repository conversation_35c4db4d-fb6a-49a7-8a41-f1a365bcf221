module yalla.data {
    /**
     * 十字模式
     * 第一张可以是单牌或者双牌，，首牌以第一个双牌为准
     */
    export class MugginsPool extends Base {

        private _vertialOutData: OutDominoData;      //第二次换行是的牌（第三次换行需要）
        private _horizentalOutData: OutDominoData;  //第二次换行是的牌（第三次换行需要）
        constructor() {
            super();
        }

        /**
         * 更新四个方向 top bottom left right 当前可以接牌的信息{id, num, list}
         * 如果左右left right还没有属性，则默认设置第一张牌
         */
        public updatePickUpCard(cardData: yalla.data.OutDominoData, deskSide: number = 1): void {
            //第一张牌 如果是双牌默认side=1 单牌默认side=4
            var isSame = cardData.isSame;
            if (this.left.id < 1 && this.right.id < 1 || this.startCardData == cardData) {
                if (isSame) {
                    if (this.top.id < 1 || deskSide == DirType.TOP) this.top.update(cardData.id, cardData.headNum, cardData.side, isSame);
                    if (this.bottom.id < 1 || deskSide == DirType.BOTTOM) this.bottom.update(cardData.id, cardData.headNum, cardData.side, isSame);
                    if (this.left.id < 1 || deskSide == DirType.LEFT) this.left.update(cardData.id, cardData.headNum, cardData.side, isSame);
                    if (this.right.id < 1 || deskSide == DirType.RIGHT) this.right.update(cardData.id, cardData.headNum, cardData.side, isSame);
                } else {
                    if (cardData.side == DirType.LEFT) {
                        this.left.update(cardData.id, cardData.headNum, cardData.side, isSame);
                        this.right.update(cardData.id, cardData.tailNum, cardData.side, isSame);
                    } else if (cardData.side == DirType.RIGHT) {
                        this.left.update(cardData.id, cardData.tailNum, cardData.side, isSame);
                        this.right.update(cardData.id, cardData.headNum, cardData.side, isSame);
                    }
                }
                return;
            }

            var num = 0;
            if (deskSide == DirType.LEFT || deskSide == DirType.RIGHT) {//牌放在桌面左右方向

                if (cardData.side == DirType.TOP || cardData.side == DirType.BOTTOM) {//双牌(上下方向)
                    num = cardData.headNum;
                } else {//单牌(左右方向)
                    if (deskSide == DirType.LEFT) {
                        num = cardData.side == DirType.LEFT ? cardData.headNum : cardData.tailNum;
                    } else if (deskSide == DirType.RIGHT) {
                        num = cardData.side == DirType.LEFT ? cardData.tailNum : cardData.headNum;
                    }
                }
                if (cardData.rightDominoId == this.left.id) {//左边更新
                    this.left.update(cardData.id, num, cardData.side, isSame);

                }
                else if (cardData.leftDominoId == this.right.id) {//右边更新
                    this.right.update(cardData.id, num, cardData.side, isSame);
                }

            } else {//牌放在桌面上下方向

                if (cardData.side == DirType.LEFT || cardData.side == DirType.RIGHT) {//双牌(左右方向)
                    num = cardData.headNum;
                } else {//单牌(上下方向)
                    if (deskSide == DirType.TOP) {
                        num = cardData.side == DirType.BOTTOM ? cardData.tailNum : cardData.headNum;
                    } else if (deskSide == DirType.BOTTOM) {
                        num = cardData.side == DirType.BOTTOM ? cardData.headNum : cardData.tailNum;
                    }
                }

                if (cardData.bottomDominoId == this.top.id) {//上边更新
                    this.top.update(cardData.id, num, cardData.side, isSame);
                }
                else if (cardData.topDomainoId == this.bottom.id) {//下边更新
                    this.bottom.update(cardData.id, num, cardData.side, isSame);
                }
            }
        }

        /**
         * 出牌或者自动收到出牌协议
         * 记录首牌
         * 更新桌面牌的实际宽高
         * @param msg
         */
        
        public add(msg: any): yalla.data.OutDominoData {
            super.add(msg);
            if (!msg) return;
            if (!this.cardHash) this.cardHash = {};
            var outData = this.cardHash[msg.id]
            if (!outData) {
                outData = new yalla.data.OutDominoData();
                this.cardHash[msg.id] = outData;
            }
            outData.update(msg);

            var deskSide = msg.deskSort; //1234相对桌面，出的牌在上下左右位置
            if (!this.firstCardData) {
                deskSide = outData.isSame ? DirType.TOP : DirType.LEFT;
                outData.deskSort = deskSide;
            }

            this.allCardList.push(outData);
            var connectOutData = outData;
            if (this.left.id < 1 && this.right.id < 1) {//第一张牌设置位置
                outData.pos(0, 0);
                if (deskSide == DirType.TOP) this.topList.push(outData);
                else if (deskSide == DirType.LEFT) this.leftList.push(outData);

            } else {
                if (deskSide == DirType.TOP && this.cardHash[outData.bottomDominoId]) {
                    connectOutData = this.cardHash[outData.bottomDominoId];
                    connectOutData.topDomainoId = outData.id;
                    this.topList.push(outData);

                } else if (deskSide == DirType.BOTTOM && this.cardHash[outData.topDomainoId]) {
                    connectOutData = this.cardHash[outData.topDomainoId];
                    connectOutData.bottomDominoId = outData.id;
                    this.bottomList.push(outData);

                } else if (deskSide == DirType.LEFT && this.cardHash[outData.rightDominoId]) {
                    connectOutData = this.cardHash[outData.rightDominoId];
                    connectOutData.leftDominoId = outData.id;
                    this.leftList.push(outData);

                } else if (deskSide == DirType.RIGHT && this.cardHash[outData.leftDominoId]) {
                    connectOutData = this.cardHash[outData.leftDominoId];
                    connectOutData.rightDominoId = outData.id;
                    this.rightList.push(outData);
                }
            }
            if (!this.firstCardData) this.firstCardData = outData;

            if (!this.startCardData && outData.isSame) {
                this.startCardData = outData;//首个双牌
                if (this.leftList.length > 0 && deskSide == DirType.LEFT) {
                    //把当前所有牌列表赋值给右列表
                    this.leftList = [];
                    this.rightList = this.allCardList;
                } else if (this.rightList.length > 0 && deskSide == DirType.RIGHT) {
                    //把当前所有牌列表赋值给左列表
                    this.rightList = [];
                    this.leftList = this.allCardList;
                }
            }

            this.tempAddId.push(outData.id);
            this.updatePickUpCard(outData, deskSide);
            this.getNextInfo(deskSide, outData, connectOutData);//1234相对桌面，出的牌在上下左右位置
            this.updateScale(outData);
            return outData;
        }

        /**
         * 出牌后更新缩放比,这里只计算x轴方向的缩放
         */
        public updateScale(outData: yalla.data.OutDominoData): void {
            var minMaxPosList = this.getCardListMaxMinPos();
            var [distanceX, distanceY, tempScaleX, tempScaleY] = [0, 0, 0, 0];
            var [minXData, maxXData, minYData, maxYData] = [minMaxPosList[0], minMaxPosList[1], minMaxPosList[2], minMaxPosList[3]];

            distanceY = Math.abs(maxYData.yy - minYData.yy) + yalla.data.CardWid;
            tempScaleY = this.getScaleY(Math.ceil(distanceY / yalla.data.CardWid));

            distanceX = Math.abs(maxXData.xx - minXData.xx) + yalla.data.CardWid;
            tempScaleX = this.getScaleX(Math.ceil(distanceX / yalla.data.CardWid));
            if (this.allCardList.length > 1) {
                this.currentScale = Math.max(Math.min(this.currentScale, Math.min(tempScaleX, tempScaleY)), this.MinScale);
                this.currentScale = this.currentScale <= this.MinScale ? (this.currentScale - 0.02) : this.currentScale;
                if (this.currentScale > 0.6) this.currentScale = this.currentScale * 0.95;
            }
        }

        /**
         * 桌面上的牌 上下左右 是否跟自己的手牌有匹配的
         * 如果是自己出牌,且第一次出牌，那么首牌都可以选择
         * @param handList 
         */
        public filterPlayCardList(handList: Array<Domino>): void {
            super.filterPlayCardList(handList);
            var handLen = handList.length;

            if (this.left.id < 1 && this.right.id < 1) {
                for (var i = 0; i < handLen; i++) {
                    var id = handList[i].id;
                    this.waitPlayCardHash[id] = yalla.data.MapList[id];
                }
                return;
            }

            for (var i = 0; i < handLen; i++) {
                var itemData = handList[i];
                var numList = [itemData.tailNum, itemData.headNum];
                var isLeftMatch = this.isMath(this.left.num, numList);
                var isRightMatch = this.isMath(this.right.num, numList);
                var isTopMatch = this.isMath(this.top.num, numList);
                var isBottomMatch = this.isMath(this.bottom.num, numList);
                if (isLeftMatch || isRightMatch || isTopMatch || isBottomMatch) {
                    this.waitPlayCardHash[itemData.id] = yalla.data.MapList[itemData.id];
                }
            }
        }

        /**
         * 点击手牌中可出牌，筛选出可以放置的位置
         * @param d 
         */
        public fileterCardPos(d: Domino): void {
            super.fileterCardPos(d);
            var isLeftMatch = this.isMath(this.left.num, [d.tailNum, d.headNum]);
            var isRightMatch = this.isMath(this.right.num, [d.tailNum, d.headNum]);
            var isTopMatch = this.isMath(this.top.num, [d.tailNum, d.headNum]);
            var isBottomMatch = this.isMath(this.bottom.num, [d.tailNum, d.headNum]);
            var side = 0;
            var score = 0;
            var sideOutData;
            if (isTopMatch) {
                sideOutData = this.cardHash[this.top.id];
                if (d.tailNum == d.headNum) side = DirType.LEFT;
                else side = (sideOutData.headNum == this.top.num) ? DirType.BOTTOM : DirType.TOP;
                score = this.getScore(d, DirType.TOP);
                this.posList.push({ deskSort: DirType.TOP, id: d.id, tailNum: d.tailNum, headNum: d.headNum, sideDominoId: sideOutData.id, side: side, score: score, bottomDominoId: this.top.id });
            }

            if (isBottomMatch) {
                sideOutData = this.cardHash[this.bottom.id];
                if (d.tailNum == d.headNum) side = DirType.LEFT;
                else side = (sideOutData.headNum == this.bottom.num) ? DirType.TOP : DirType.BOTTOM;
                score = this.getScore(d, DirType.BOTTOM);
                this.posList.push({ deskSort: DirType.BOTTOM, id: d.id, tailNum: d.tailNum, headNum: d.headNum, sideDominoId: sideOutData.id, side: side, score: score, topDomainoId: this.bottom.id });
            }

            if (isLeftMatch) {
                sideOutData = this.cardHash[this.left.id];
                if (d.tailNum == d.headNum) side = DirType.TOP;
                else side = (sideOutData.headNum == this.left.num) ? DirType.RIGHT : DirType.LEFT;
                score = this.getScore(d, DirType.LEFT);
                this.posList.push({ deskSort: DirType.LEFT, id: d.id, tailNum: d.tailNum, headNum: d.headNum, sideDominoId: sideOutData.id, side: side, score: score, rightDominoId: this.left.id });
            }

            if (isRightMatch) {
                sideOutData = this.cardHash[this.right.id];
                if (d.tailNum == d.headNum) side = DirType.TOP;
                else side = (sideOutData.headNum == this.right.num) ? DirType.LEFT : DirType.RIGHT;
                score = this.getScore(d, DirType.RIGHT);
                this.posList.push({ deskSort: DirType.RIGHT, id: d.id, tailNum: d.tailNum, headNum: d.headNum, sideDominoId: sideOutData.id, side: side, score: score, leftDominoId: this.right.id });
            }
        }

        /**
         * 根据出的牌，找出牌的相邻id  发牌传输的参数
         */
        public getSendPlayCardParams(sendCardID: number, deskSort: number): any {
            var sideDominoId = 0;
            if (deskSort == DirType.TOP) {
                sideDominoId = this.top.id;
            } else if (deskSort == DirType.BOTTOM) {
                sideDominoId = this.bottom.id;
            } else if (deskSort == DirType.LEFT) {
                sideDominoId = this.left.id;
            } else if (deskSort == DirType.RIGHT) {
                sideDominoId = this.right.id;
            }
            return { 'dominoId': sendCardID, 'sideDominoId': sideDominoId, 'deskSort': deskSort };
        }

        /**
         * 根据当前牌列表，获取上下左右最大，最小坐标
         * @param tempArr 
         */
        public getCardListMaxMinPos(tempArr: Array<any> = []): Array<any> {
            var minXData = { xx: 0 };
            var minYData = { yy: 0 };
            var maxXData = { xx: 0 };
            var maxYData = { yy: 0 };
            var list = (tempArr && tempArr.length > 0) ? tempArr : this.allCardList;
            var len = list.length;
            for (var i = 0; i < len; i++) {
                var cardData = list[i];
                if (cardData.xx < minXData['xx']) {
                    minXData = cardData;
                }
                if (cardData.yy < minYData['yy']) {
                    minYData = cardData;
                }

                if (cardData.xx > maxXData['xx']) {
                    maxXData = cardData;
                }
                if (cardData.yy > maxYData['yy']) {
                    maxYData = cardData;
                }
            }
            return [minXData, maxXData, minYData, maxYData];
        }

        /**
         * 检测换行//[nextD, {isWrap:isWrap, sideNum:sideNum}];
         * 1.要确保第一张双牌在水平方向，且竖向放置
         * @param deskSide 
         * @param outData  {sideType,tailNum,headNum,side}
         * @param connectOutData 
         */
        public getNextInfo(deskSide: number, outData: any, connectOutData: yalla.data.OutDominoData, isHint: boolean = false): Array<any> {
            super.getNextInfo(deskSide, outData, connectOutData, isHint);

            this.isUpdateAll = false;
            outData.showSide = outData.side;
            outData.xx = 0;
            outData.yy = 0;
            var isWrap = false;
            var outIsSame = outData.tailNum == outData.headNum;
            //检测当前的牌是否双牌，且是否不再水平方向
            if ((!this.startCardData && outIsSame && this.allCardList.length > 1) || (this.startCardData && outData.id == this.startCardData.id)) {
                //这里要根据距离双牌超过多上张牌就换行
                var list = deskSide == DirType.LEFT ? this.rightList : this.leftList;
                var horizMaxCardNums = this.getHorizMaxCardNums();
                var curHorizCardNums = this.getHorizontalLen(list);
                if (curHorizCardNums > horizMaxCardNums / 2 && !isHint) {
                    this.isUpdateAll = true;
                    this.resetCardListPos(outData, deskSide);
                } else {
                    if(!isHint){
                        // console.log('======hWrapSideHashNums 被重置了======', outData.id, outData.headNum)
                        this.hWrapSideHashNums = {};
                        this.vWrapSideHashNums = {};
                        this.hWrapIDHash = {};
                        this.vWrapIDHash = {};
                    }
                    isWrap = this.updateSideCard(outData, connectOutData, deskSide, isHint);
                }

            } else {
                if (outData != connectOutData) {
                    isWrap = this.updateSideCard(outData, connectOutData, deskSide, isHint);
                }
            }
            return [outData, { isWrap: isWrap }];
        }

        //根据第一张牌推算左列表
        private _tempLeftList: Array<yalla.data.OutDominoData> = [];
        private _tempRightList: Array<yalla.data.OutDominoData> = [];
        private getLeftListByFirst(cardData: yalla.data.OutDominoData): void {
            if (cardData.leftDominoId > 0) {
                if(this.tempAddId.indexOf(cardData.leftDominoId) < 0) return;
                var leftData = this.cardHash[cardData.leftDominoId];
                if (!leftData) return;
                this._tempLeftList.push(leftData);
                this.getLeftListByFirst(leftData);
            }
        }
        //根据第一张牌推算右列表
        private getRightListByFirst(cardData: yalla.data.OutDominoData): void {
            if (cardData.rightDominoId > 0) {
                var rightData = this.cardHash[cardData.rightDominoId];
                if(this.tempAddId.indexOf(cardData.rightDominoId) < 0) return;
                if (!rightData) return;
                this._tempRightList.push(rightData);
                this.getRightListByFirst(rightData);
            }
        }

        /**
         * 出现第一张双牌前 存在换行，如果出现双牌了，需要重新排列位置
         * @param outData 
         * @param deskSide 
         */
        private resetCardListPos(outData: yalla.data.OutDominoData, deskSide: number): void {
            this.hWrapSideHashNums = {};
            this.vWrapSideHashNums = {};
            this.hWrapIDHash = {};
            this.vWrapIDHash = {};
            this._tempLeftList = [];
            this._tempRightList = [];
            if (this.firstCardData) {
                this._tempLeftList.push(this.firstCardData);
                this.getLeftListByFirst(this.firstCardData);
                this.getRightListByFirst(this.firstCardData);
            }

            var tempList = [];
            if (this.startCardData) {
                if (deskSide == DirType.LEFT) {
                    this._tempLeftList.reverse();
                    this._tempLeftList = this._tempLeftList.concat(this._tempRightList);
                    tempList = this._tempLeftList;

                } else if (deskSide == DirType.RIGHT) {
                    this._tempRightList.reverse();
                    this._tempRightList = this._tempRightList.concat(this._tempLeftList);
                    tempList = this._tempRightList;
                }
                var len = tempList.length;
                for (var i = 0; i < len; i++) {
                    tempList[i].showSide = 0;//重置
                    tempList[i].pos(0, 0);
                    if (i <= 0) {
                        this.updateSideCard(tempList[i], tempList[i], tempList[i].deskSort);//重置位置，桌面方向默认为1
                    } else {
                        if (this.startCardData.deskSort == DirType.LEFT) {
                            tempList[i].deskSort = 7 - deskSide;
                        } else {
                            if (i < len - 1) {
                                tempList[i].deskSort = 7 - deskSide;
                            }
                        }
                        this.updateSideCard(tempList[i], tempList[i - 1], tempList[i].deskSort);
                    }
                }
                var minMaxPosList = this.getCardListMaxMinPos();
                if (tempList[len - 1]) this.updateCardBlockPos(minMaxPosList[0], minMaxPosList[2], tempList[len - 1]);
            }
        }

        /**
         * 重新再校验牌位置
         */
        public updateCardBlockPos(minXData: any, minYData: any, cardData: yalla.data.OutDominoData): void {
            super.updateCardBlockPos(minXData, minYData, cardData);
            var [disLeftX, disTopY, leftDis, topDis] = [0, 0, 0, 0];

            if (minXData.xx <= 0) {
                disLeftX = minXData.xx;
                leftDis = minXData.width / 2 || cardData.width / 2;
                disLeftX -= leftDis;
            }

            if (minYData.yy <= 0) {
                disTopY = minYData.yy;
                topDis = minYData.height / 2 || cardData.height / 2;
                disTopY -= topDis;
            }
            if (disLeftX != 0 || disTopY != 0) {
                var cardLen = this.allCardList.length;
                for (var i = 0; i < cardLen; i++) {
                    this.allCardList[i].xx -= disLeftX;
                    this.allCardList[i].yy -= disTopY;
                }
            }
        }

        private isWrap(num: number, sideType: number): boolean {
            var tempScale = 1;
            if (sideType == HorV.HORIZ) {
                tempScale = this.getScaleX(num);
            } else {
                tempScale = this.getScaleY(num);
            }
            if (tempScale <= this.MinScale) {
                return true;
            }
            return false;
        }

        /**
         * 更新牌放置位置
         * @param outData 
         * @param connectOutData 
         * @param deskSide 
         * @param minMaxPosList 
         */
        private updateSideCard(outData: any, connectData: yalla.data.OutDominoData, deskSide: number, isHint: boolean = false): boolean {
            var [connectSide, conType, conShowSide, conShowType, conSame] = [connectData.side, connectData.sideType, connectData.showSide, connectData.showSideType, connectData.isSame];
            var [dis, h, v, disX, disY] = [0, -1, -1, 0, 0];

            var isWrap = false;
            var outIsSame = outData.tailNum == outData.headNum;

            if (!this.hWrapSideHashNums[deskSide]) this.hWrapSideHashNums[deskSide] = 1;
            if (!this.vWrapSideHashNums[deskSide]) this.vWrapSideHashNums[deskSide] = 1;

            outData.showSide = outData.side;
            if (deskSide == DirType.LEFT || deskSide == DirType.RIGHT) {
                //第一张双牌；水平方向的单牌；水平方向的双牌
                var isFirstDouble = (outData == this.startCardData);//是否第一张双排
                var isFirstCard = (outData == connectData);//是否第一张牌
                if (isFirstDouble || isFirstCard || (!conSame && conShowType == HorV.HORIZ) || (conSame && conShowType == HorV.VERTICAL)) {
                    //水平方向坐标，显示方向
                    h = (connectSide == conShowSide || connectData == this.startCardData) ? -1 : 1;
                    v = -1;
                    if (deskSide == DirType.RIGHT) {
                        h *= -1;
                        v *= -1;
                    }
                    dis = this.getDistance(outData, connectData);
                    outData.xx = connectData.xx + dis * h;
                    outData.yy = connectData.yy;

                    // Debug.log(outData.tailNum + ':' + outData.headNum + ' ' + outData.id + '=AllFive==000=====' + deskSide + ' ' + (conType == conShowType) + ' ' + this.hWrapSideHashNums[deskSide]);
                    if (conType == conShowType && this.hWrapSideHashNums[deskSide] < 3) {
                        if (!this.startCardData) {
                            var leftData = this.cardHash[this.left.id];
                            var rightData = this.cardHash[this.right.id];
                            if (leftData && rightData) disX = Math.abs(rightData.xx - leftData.xx);
                            else disX = leftData ? leftData.xx : rightData.xx;
                        } else {
                            disX = Math.abs(this.startCardData.xx - connectData.xx);
                            disX *= 2;
                        }
                        disX += yalla.data.CardWid * 2;
                        isWrap = this.isWrap(disX / yalla.data.CardWid * 1.4, 1);//提前转行
                        // Debug.log(outData.tailNum + ":" + outData.headNum + '=AllFive==001=====' + isWrap + " " + deskSide + " " + this.hWrapSideHashNums[deskSide]);
                        if (connectData != this.startCardData && isWrap) {
                            if (!isHint) {
                                // console.log("===+0=="+outData.id)
                                this.hWrapSideHashNums[deskSide] += 1;
                            }
                            //水平转垂直
                            outData.showSide = outData.side == DirType.LEFT ? DirType.TOP : DirType.BOTTOM;
                            if (outIsSame) {
                                outData.showSide = DirType.LEFT;//TODO::原本是showSide=1，应需求强制为3
                                if (!isHint) this.hWrapIDHash[outData.id] = DirType.TOP;
                            }
                            if (connectData.isSame) {
                                outData.xx = connectData.xx;
                                outData.yy = connectData.yy + yalla.data.CardWid * 2 * v;
                            } else {
                                if (outIsSame) {

                                    outData.xx = connectData.xx + yalla.data.CardWid * (this.hWrapIDHash[outData.id] ? 1.5 : 2) * h;
                                    outData.yy = connectData.yy;
                                } else {
                                    outData.xx = connectData.xx + yalla.data.CardWid / 2 * h;
                                    outData.yy = connectData.yy + yalla.data.CardWid * 1.5 * v;
                                }
                            }
                        }
                    } else {
                        //第二次水平方向，与第一次水平方向side相反
                        h = 1;
                        if (deskSide == DirType.RIGHT) h *= -1;
                        if (!outIsSame) outData.showSide = outData.side == DirType.LEFT ? DirType.RIGHT : DirType.LEFT;
                        dis = this.getDistance(outData, connectData);
                        outData.xx = connectData.xx + dis * h;
                        outData.yy = connectData.yy;
                        // Debug.log(outData.tailNum + ":" + outData.headNum + " " + outData.showSide + '=AllFive===二次水平0====' + deskSide + " " + this.hWrapSideHashNums[deskSide]);
                        if (this._horizentalOutData) {
                            //水平到垂直，第三次转向
                            var topData = this.cardHash[this.top.id];
                            var bottomData = this.cardHash[this.bottom.id];
                            var horizX = Math.abs(this._horizentalOutData.xx - connectData.xx) + (this._horizentalOutData.width + connectData.width) / 2;
                            var isHorWrap = horizX >= (yalla.data.CardWid * 20);
                            if (isHorWrap) isWrap = isHorWrap;
                            // Debug.log(outData.tailNum + ":" + outData.headNum + '=AllFive===二次水平1====' + isWrap + " " + deskSide + " " + this.hWrapSideHashNums[deskSide]);
                            if (isWrap) {
                                v *= -1;
                                if (!isHint) {
                                    this.hWrapSideHashNums[deskSide] += 1;
                                    // console.log("===+1=="+outData.id)
                                }
                                outData.showSide = outData.side == DirType.LEFT ? DirType.BOTTOM : DirType.TOP;
                                if (connectData.isSame) {
                                    outData.xx = connectData.xx;
                                    outData.yy = connectData.yy + yalla.data.CardWid * 2 * v;
                                } else {
                                    if (outIsSame) {
                                        outData.xx = connectData.xx + yalla.data.CardWid * 1.5 * h;
                                        outData.yy = connectData.yy;
                                    } else {
                                        outData.xx = connectData.xx + yalla.data.CardWid / 2 * h;
                                        outData.yy = connectData.yy + yalla.data.CardWid * 1.5 * v;
                                    }
                                }
                            }
                        }

                    }

                } else {
                    //垂直方向
                    v = -1;
                    if (deskSide == DirType.RIGHT) {
                        v *= -1;
                    }
                    if (this.hWrapSideHashNums[deskSide] >= 4) {
                        v *= -1;
                        outData.showSide = (outData.side == DirType.LEFT ? DirType.BOTTOM : DirType.TOP);
                    } else {
                        outData.showSide = outData.side == DirType.LEFT ? DirType.TOP : DirType.BOTTOM;
                    }
                    if (outIsSame) outData.showSide = DirType.LEFT;

                    dis = this.getDistance(outData, connectData);
                    outData.xx = connectData.xx;
                    outData.yy = connectData.yy + dis * v;

                    if (this.hWrapSideHashNums[deskSide] < 5) {
                        var leftData = this.cardHash[this.left.id];
                        var rightData = this.cardHash[this.right.id];
                        if (!this.startCardData) {
                            if (leftData && rightData) disY = Math.abs(rightData.yy - leftData.yy);
                            else disY = leftData ? leftData.yy : rightData.yy;
                            isWrap = this.isWrap(disY / yalla.data.CardWid * 0.85, 0);
                        } else {
                            var cData = this.cardHash[connectData.id];
                            var horizY = Math.abs(cData.yy - this.startCardData.yy) + (cData.height + this.startCardData.height / 2) / 2;
                            if (conSame && horizY / yalla.data.CardWid > 7) isWrap = true
                            else if (!conSame && horizY / yalla.data.CardWid >= 7) isWrap = true;
                        }
                    }
                    // Debug.log(outData.tailNum + ":" + outData.headNum + " " + outData.showSide + ' ' + outData.side + '=AllFive===垂直====' + deskSide + ' ' + this.hWrapSideHashNums[deskSide] + ' ' + isWrap);
                    //推迟换行
                    if (isWrap) {
                        //垂直转水平
                        if (!isHint) {
                            this.hWrapSideHashNums[deskSide] += 1;
                            // console.log("===+2=="+outData.id)
                        }
                        h = 1;
                        if (deskSide == DirType.RIGHT) h *= -1;

                        outData.showSide = outData.side == DirType.LEFT ? DirType.RIGHT : DirType.LEFT;

                        if (connectData.isSame) {
                            outData.xx = connectData.xx + yalla.data.CardWid * 2 * h;
                            outData.yy = connectData.yy;
                        } else {
                            if (outIsSame) {
                                outData.xx = connectData.xx;
                                outData.yy = connectData.yy + yalla.data.CardWid * 1.5 * v;
                            } else {
                                outData.xx = connectData.xx + yalla.data.CardWid / 2 * h;
                                outData.yy = connectData.yy + yalla.data.CardWid * 1.5 * v;
                            }
                        }
                        if (!isHint) this._horizentalOutData = outData;
                    }
                }

            } else {
                //垂直方向
                if ((conShowType == HorV.VERTICAL && !connectData.isSame) || (connectData.isSame && conShowType == HorV.HORIZ) || (connectData == this.startCardData)) {
                    h = 1;
                    v = (connectSide == conShowSide ? -1 : 1);
                    if (deskSide == DirType.BOTTOM) {
                        h *= -1;
                        v *= -1;
                    }
                    dis = this.getDistance(outData, connectData);
                    outData.xx = connectData.xx;
                    outData.yy = connectData.yy + dis * v;

                    if (connectSide == conShowSide && this.vWrapSideHashNums[deskSide] < 3) {
                        if (this.startCardData) {
                            disY = Math.abs(this.startCardData.yy - connectData.yy) * 2;
                            disY += yalla.data.CardWid * 2;
                            isWrap = this.isWrap(disY / yalla.data.CardWid * 1.4, 0);//提前换行

                            if (connectData != this.startCardData && isWrap) {
                                //垂直转水平
                                if (!isHint) {
                                    this.vWrapSideHashNums[deskSide] += 1;
                                }
                                outData.showSide = outData.side == DirType.TOP ? DirType.RIGHT : DirType.LEFT;
                                if (connectData.isSame) {
                                    outData.xx = connectData.xx + yalla.data.CardWid * 2 * h;
                                    outData.yy = connectData.yy;
                                } else {
                                    if (outIsSame) {
                                        outData.xx = connectData.xx;
                                        outData.yy = connectData.yy + yalla.data.CardWid * 1.5 * v;
                                        if(outData.showRotation == 0 || outData.showRotation == 180) outData.yy = connectData.yy + yalla.data.CardWid * 2 * v;
                                        if (!isHint) {
                                            this.vWrapSideHashNums[deskSide] -= 1;
                                        }
                                    } else {
                                        outData.xx = connectData.xx + yalla.data.CardWid * 1.5 * h;
                                        outData.yy = connectData.yy + yalla.data.CardWid / 2 * v;
                                    }
                                }
                            }
                        }
                    } else {
                        //第二次垂直方向，与第一次垂直方向side相反
                        v = 1;
                        if (deskSide == DirType.BOTTOM) v *= -1;
                        if (!outIsSame) outData.showSide = outData.side == DirType.TOP ? DirType.BOTTOM : DirType.TOP;
                        dis = this.getDistance(outData, connectData);
                        outData.xx = connectData.xx;
                        outData.yy = connectData.yy + dis * v;

                        if (this._vertialOutData) {
                            //垂直到水平，第三次转向
                            var topData = this.cardHash[this.top.id];
                            var bottomData = this.cardHash[this.bottom.id];
                            var horizY = Math.abs(this._vertialOutData.yy - connectData.yy) + (this._vertialOutData.height + connectData.height) / 2;
                            var isVerWrap = horizY >= (yalla.data.CardWid * 12);
                            if (isVerWrap) isWrap = isVerWrap;
                            if (isWrap) {
                                h *= -1;
                                if (!isHint) {
                                    this.vWrapSideHashNums[deskSide] += 1;
                                }
                                outData.showSide = outData.side == DirType.TOP ? DirType.LEFT : DirType.RIGHT;
                                if (connectData.isSame) {
                                    outData.xx = connectData.xx + yalla.data.CardWid * 2 * h;
                                    outData.yy = connectData.yy;
                                } else {
                                    if (outIsSame) {
                                        outData.xx = connectData.xx;
                                        outData.yy = connectData.yy + yalla.data.CardWid * 1.5 * v;
                                        if(outData.showRotation == 0 || outData.showRotation == 180) outData.yy = connectData.yy + yalla.data.CardWid * 2 * v;
                                        
                                        if (!isHint) this.vWrapSideHashNums[deskSide] -= 1;
                                    } else {
                                        outData.xx = connectData.xx + yalla.data.CardWid * 1.5 * h;
                                        outData.yy = connectData.yy + yalla.data.CardWid / 2 * v;
                                    }
                                }
                            }
                        }
                    }

                } else {
                    //水平方向
                    h = 1;
                    if (deskSide == DirType.BOTTOM) h *= -1;

                    if (this.vWrapSideHashNums[deskSide] >= 4) {
                        h *= -1;
                        outData.showSide = (outData.side == DirType.TOP ? DirType.LEFT : DirType.RIGHT);
                    } else {
                        outData.showSide = (outData.side == DirType.TOP ? DirType.RIGHT : DirType.LEFT);
                    }
                    if (outIsSame) outData.showSide = DirType.TOP;

                    dis = this.getDistance(outData, connectData);
                    outData.xx = connectData.xx + dis * h;
                    outData.yy = connectData.yy;

                    if (this.vWrapSideHashNums[deskSide] < 5) {
                        if (this.startCardData) {
                            disX = Math.abs(this.startCardData.xx - connectData.xx) * 2;
                            isWrap = this.isWrap(disX / yalla.data.CardWid * 0.9, 1);
                        
                            //TODO::检测top & bottom 的x差值是否>=20 是则换行 且距离第一张双牌的x>=10
                            var topData = this.cardHash[this.top.id];
                            var bottomData = this.cardHash[this.bottom.id];
                            var horizX = Math.abs(topData.xx - bottomData.xx) + (topData.width + bottomData.width) / 2;
                            var startDis = Math.abs(outData.xx - this.startCardData.xx) + (outData.width + this.startCardData.width) / 2;
                            if (!isHint) {
                                var cData = this.cardHash[connectData.id];
                                if (this.top.id == outData.id) horizX = Math.abs(cData.xx - bottomData.xx) + (cData.width + bottomData.width) / 2;
                                else if (this.bottom.id == outData.id) horizX = Math.abs(cData.xx - topData.xx) + (cData.width + topData.width) / 2;
                                startDis = Math.abs(cData.xx - this.startCardData.xx) + (cData.width + this.startCardData.width) / 2;
                            }
                            var isHorizWrap = (horizX >= (yalla.data.CardWid * 20) && startDis >= (yalla.data.CardWid * 10));
                            if (isHorizWrap) isWrap = isHorizWrap;
                        }
                    }
                    if (isWrap) {
                        v = 1;
                        if (deskSide == DirType.BOTTOM) v *= -1;
                        if (!isHint) this.vWrapSideHashNums[deskSide] += 1;
                        //水平转垂直
                        outData.showSide = outData.side == DirType.BOTTOM ? DirType.TOP : DirType.BOTTOM;
                        if (outIsSame) {
                            outData.showSide = DirType.LEFT;
                            if (!isHint) this.vWrapIDHash[outData.id] = 1;
                        }
                        if (connectData.isSame) {
                            outData.xx = connectData.xx;
                            outData.yy = connectData.yy + yalla.data.CardWid * 2 * v;
                        } else {
                            if (outIsSame) {
                                outData.xx = connectData.xx + yalla.data.CardWid * (this.vWrapIDHash[outData.id] ? 1.5 : 2) * h;
                                outData.yy = connectData.yy;
                            } else {
                                outData.xx = connectData.xx + yalla.data.CardWid / 2 * h;
                                outData.yy = connectData.yy + yalla.data.CardWid * 1.5 * v;
                            }
                        }
                        if (!isHint) this._vertialOutData = outData;
                    }
                }
                // console.log(outData.id, outData.headNum, outData.tailNum, '===1 2==', outData.xx, outData.yy, outData.showSide);
            }
            return isWrap;
        }

        /**
         * 桌面牌总分数
         * @param d 
         * @param deskSort 
         */
        public getScore(d: Domino = null, deskSort: number = 0): number {
            if (this.startCardData && !d) {
                var len = 0;
                for (var k in this.cardHash) len++;
                if (len <= 1) return this.startCardData.headNum + this.startCardData.tailNum;
            }
            var [top, bottom, left, right] = [0, 0, 0, 0];
            var [tId, bId, lId, rId] = [this.top.id, this.bottom.id, this.left.id, this.right.id];
            var [tNum, bNum, lNum, rNum] = [this.top.num, this.bottom.num, this.left.num, this.right.num];
            var [tSame, bSame, lSame, rSame] = [this.top.isSame, this.bottom.isSame, this.left.isSame, this.right.isSame];

            var sCardData = this.startCardData;

            if (deskSort == DirType.LEFT) {
                lId = d.id;
                lSame = false;
                if (d.headNum == d.tailNum) {
                    lSame = true;
                    lNum = d.headNum;
                    if (!this.startCardData) {
                        tId = lId;
                        tNum = lNum;
                        tSame = true;

                        bId = lId;
                        bNum = lNum;
                        bSame = true;
                    }
                } else {
                    lNum = d.headNum == this.left.num ? d.tailNum : d.headNum;
                }
            }

            if (deskSort == DirType.RIGHT) {
                rId = d.id;
                rSame = false;
                if (d.headNum == d.tailNum) {
                    rSame = true;
                    rNum = d.headNum;
                    if (!this.startCardData) {
                        tId = rId;
                        tNum = rNum;
                        tSame = true;

                        bId = rId;
                        bNum = rNum;
                        bSame = true;
                    }
                } else {
                    rNum = d.headNum == this.right.num ? d.tailNum : d.headNum;
                }
            }

            if (deskSort == DirType.TOP) {
                tId = d.id;
                tSame = false;
                if (d.headNum == d.tailNum) {
                    tSame = true;
                    tNum = d.headNum;
                } else {
                    tNum = d.headNum == this.top.num ? d.tailNum : d.headNum;
                }
            }

            if (deskSort == DirType.BOTTOM) {
                bId = d.id;
                bSame = false;
                if (d.headNum == d.tailNum) {
                    bSame = true;
                    bNum = d.headNum;
                } else {
                    bNum = d.headNum == this.bottom.num ? d.tailNum : d.headNum;
                }
            }

            if (lId > 0) {
                if (lSame) {
                    if ((lId == bId && lId == tId && lId != rId) || (sCardData && sCardData.id != lId && lId != bId && lId != tId)) {
                        left = lNum * 2;
                    }
                } else {
                    left = lNum;
                }
            }
            if (rId > 0) {
                if (rSame) {
                    if ((rId == bId && rId == tId && rId != lId) || (sCardData && sCardData.id != rId && rId != bId && rId != tId)) {
                        right = rNum * 2;
                    }
                } else {
                    right = rNum;
                }
            }
            if (tId > 0) {
                if (tSame) {
                    if ((tId == lId && tId == rId && tId != bId) || (sCardData && sCardData.id != tId && tId != lId && tId != rId)) {
                        top = tNum * 2;
                    }
                } else {
                    top = tNum;
                }
            }
            if (bId > 0) {
                if (bSame) {
                    if ((bId == lId && bId == rId && bId != tId) || (sCardData && sCardData.id != bId && bId != lId && bId != rId)) {
                        bottom = bNum * 2;
                    }
                } else {
                    bottom = bNum;
                }
            }
            return left + right + top + bottom;
        }
    }
}