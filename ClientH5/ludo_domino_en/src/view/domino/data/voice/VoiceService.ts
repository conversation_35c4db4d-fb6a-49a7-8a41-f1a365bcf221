module yalla.data {

    import EventDispatcher = laya.events.EventDispatcher;

    export class VoiceService extends EventDispatcher {
        static _instance: VoiceService;

        public isJoinRoom: Boolean = false;
        // public banTalkData: string = '';           //是否被后台设置禁言,本地需要记录是否被禁言，因需要跟上局比较，如果上局禁言，下局解禁，，不论之前是否关闭麦克风，默认都要打开
        private _voiceToken: string = '';		   //token(加入语音聊天房间需要)
        private _channelName: string;
        private _voiceOpen: boolean = false;       //语音开关  TODO::保存本地,下次进入以上次为准
        // private _client;

        constructor() {
            super();
            // if (yalla.util.IsBrowser()) {
            //     this._client = yalla.net.Client.instance;
            // } else {
            //     this._client = yalla.net.NativeClient.instance;
            // }
        }

        static get instance(): VoiceService {
            return VoiceService._instance || (VoiceService._instance = new VoiceService());
        }

        // public register(): void {
        //     this._client.on(yalla.data.EnumCmd.Dominose_Agora_Operate, this, this.handleAgoraOperate);
        //     this._client.on(yalla.data.EnumCmd.Dominose_GetAgoraToken, this, this.handleGetAgoraToken);
        //     this._client.on(yalla.data.EnumCmd.Dominose_GetZegoToken, this, this.handleGetZegoToken);
        // }

        public get voiceToken(): string { return this._voiceToken; }
        public get channelName(): string { return this._channelName; }
        public get voiceOpen(): boolean { return this._voiceOpen; }

		// /**
		//  * 获取语音房间token
		//  */
        // public getToken() {
        //     yalla.Debug.log("==getToken==voiceType:"+yalla.Global.Account.voiceType);
        //     switch (yalla.Global.Account.voiceType) {
        //         case VoiceType.Agora:
        //             this._client.sendMsg("GetAgoraTokenRequest", yalla.data.Command.GET_AGORA_TOKEN, {
        //                 idx: yalla.data.UserService.instance.user.idx,
        //                 roomid: yalla.data.RoomService.instance.room.roomid
        //             });
        //             break;
        //         case VoiceType.Zego://1.2.8接入zego
        //             // 不使用token
        //             // var sRoomid = String(yalla.data.RoomService.instance.room.roomid);
        //             // this._voiceToken = sRoomid;
        //             // this._channelName = sRoomid;
        //             // this.joinGameRoomWithToken(yalla.data.UserService.instance.user.idx);
        //             // 使用token
        //             this._client.sendMsg("GetZegoTokenRequest", yalla.data.Command.GET_ZEGO_TOKEN, {
        //                 idx: yalla.data.UserService.instance.user.idx,
        //                 roomid: yalla.data.RoomService.instance.room.roomid,
        //                 version:4
        //             });
        //             break;
        //     }
        // }
        /**
         * 加入语音房间
         */
        public joinGameRoomWithToken(idx, callBack: Laya.Handler = null, from: number = 1) {
            yalla.Debug.log(this._channelName+'==audio====VoiceService.joinGameRoomWithToken=====voiceType='+yalla.Global.Account.voiceType);
            yalla.Global.Account.banTalkData = yalla.Global.Account.banTalkData || '';

            this.updateVoiceState();
            if (this.isJoinRoom) {
                var voiceState = laya.net.LocalStorage.getJSON('voice_open').isOpen;
                Debug.log("==audio==joinGameRoomWithToken=====voiceState:" + voiceState);
                // if (voiceState && !yalla.Global.Account.isLimitVisitor) {//TODO::新包进过一次游戏，已经获取语音tocken了，后断线重连会走这里的逻辑，避免限制游客

                if (voiceState && !yalla.Mute.isBanTalk) {
                    this.enableAudio(idx, () => {
                        if (!yalla.Sound.canPlayMusic) {
                            yalla.Debug.log('==audio=111===');
                            yalla.Sound.canPlayMusic = true;
                            yalla.Sound.playMusic('bgm_domino', 0);
                        }
                        this.event(yalla.data.EnumCustomizeCmd.Dominose_JoinAgora);
                    }, from);
                } else {
                    this.disableAudio(idx, () => {
                        if (!yalla.Sound.canPlayMusic) {
                            yalla.Debug.log('=audio==222===')
                            yalla.Sound.canPlayMusic = true;
                            yalla.Sound.playMusic('bgm_domino', 0);
                        }
                        this.event(yalla.data.EnumCustomizeCmd.Dominose_JoinAgora);
                    });
                }
            } else {

                yalla.Native.instance.joinGameRoomWithToken(this.voiceToken, this.channelName, idx, yalla.Global.Account.voiceType, (value) => {
                    this.isJoinRoom = value;
                    yalla.Debug.log(value + '=audio==555===game_state:' + yalla.Global.game_state + "--" + value);//权限关闭，不在这里播放，ios会有问题（音乐关闭不了）
                    if (!value) {
                        callBack && callBack.run();
                        return;
                    }

                    if (yalla.Global.Account.isLimitVisitor || yalla.Mute.isBanTalk) {//被限制的游客，不能使用麦克风（TODO::1.4.2.2 背限制游客，不设置disableaudio 避免麦克风提示没有）
                        // this.disableAudio(idx, () => {
                            // if (!yalla.Sound.canPlayMusic) {
                                yalla.Sound.canPlayMusic = true;
                                yalla.Sound.playMusic('bgm_domino', 0);
                                yalla.Debug.log('===LimitVisitor 000===')
                            // }
                        // });
                        return;
                    }

                    var voiceState = laya.net.LocalStorage.getJSON('voice_open').isOpen;
                    yalla.Debug.log("=audio==555 1 ====voiceState:" + voiceState);
                    if (voiceState && !yalla.Mute.isBanTalk) {
                        this.enableAudio(idx, () => {
                            // if (!yalla.Sound.canPlayMusic) {
                                yalla.Debug.log('===666===')
                                yalla.Sound.canPlayMusic = true;
                                yalla.Sound.playMusic('bgm_domino', 0);
                            // }
                            this.event(yalla.data.EnumCustomizeCmd.Dominose_JoinAgora);
                        }, from);
                    } else {
                        this.disableAudio(idx, () => {
                            // if (!yalla.Sound.canPlayMusic) {
                                yalla.Debug.log('=audio==777===')
                                yalla.Sound.canPlayMusic = true;
                                yalla.Sound.playMusic('bgm_domino', 0);
                            // }
                            this.event(yalla.data.EnumCustomizeCmd.Dominose_JoinAgora);
                        });
                    }
                });
            }
        }

        private updateVoiceState(): void {
            if (yalla.Mute.isBanTalk) {
                var voice_open = Laya.LocalStorage.getJSON("voice_open");
                if (!voice_open || !voice_open.isOpen)
                    Laya.LocalStorage.setJSON("voice_open", { isOpen: true });
            }
        }

        /**
         * 打开音频
         * from 1加入语音房间  2点击语音按钮打开关闭
         */
        public enableAudio(idx, cb, from: number = 1) {
            yalla.Native.instance.currentAudioStatus(obj => {//先检测时候有权限再开麦  
                Debug.log("===audio======888========obj.status=" + obj.status);
                if (obj.status) {
                    yalla.Native.instance.enableAudio((success) => {
                        if (success == 0) {
                            this._voiceOpen = true;
                            Laya.LocalStorage.setJSON("voice_open", { isOpen: true });
                            this.event(yalla.data.EnumCustomizeCmd.Dominose_EnableAudio, idx);
                            cb && cb(success);
                        }
                    }, from == 2 ? 1 : 0);
                } else {
                    this._voiceOpen = false;
                    if (from == 2)
                    {
                        if(yalla.Native.instance.deviceType != DeviceType.Android)
                            yalla.common.Tip.instance.showTip(TranslationD.Game_Tip_Check_Microphone2);
                    }else {
                        // yalla.Debug.log("=======999========");
                        yalla.common.TipManager.instance.showTip(yalla.data.TranslationD.Game_Tip_Check_Microphone, {}, 3000, 0);
                        Laya.timer.callLater(this, () => {//TODO::权限关闭，播放音乐,延迟播放了，ios关闭也正常
                            if (yalla.Native.instance.deviceType != DeviceType.Android) {
                                if (!yalla.Sound.canPlayMusic) {
                                    yalla.Sound.canPlayMusic = true;
                                    yalla.Sound.playMusic('bgm_domino', 0);
                                }
                            }
                        });

                        //获取权限失败
                        this.event(yalla.data.EnumCustomizeCmd.Dominose_JoinAgora, false);
                    }
                    // yalla.common.TipManager.instance.showTip(TranslationD.Game_Tip_Check_Microphone, { alpha: 0 }, 0, 5000);
                    if (yalla.Native.instance.deviceType == DeviceType.Android) {
                        yalla.Native.instance.enableAudio((success) => {
                            yalla.Debug.log("=audio===101010======success:" + success);
                            if (success == 0) {
                                this._voiceOpen = true;
                                Laya.LocalStorage.setJSON("voice_open", { isOpen: true });
                                this.event(yalla.data.EnumCustomizeCmd.Dominose_EnableAudio, idx);
                                cb && cb(success);
                            } else if (success == -1) {//拒绝麦克风权限
                                if (!yalla.Sound.canPlayMusic) {
                                    yalla.Sound.canPlayMusic = true;
                                    yalla.Sound.playMusic('bgm_domino', 0);
                                }
                            }
                        }, from == 2 ? 1 : 0);
                    }
                }
            })
        }

        /**
         * 关闭音频
         */
        public disableAudio(idx, cb: Function) {
            yalla.Native.instance.disableAudio((success) => {
                yalla.Debug.log("=audio==音频disableAudio===success="+success);
                if (success == 0) {
                    this._voiceOpen = false;
                    Laya.LocalStorage.setJSON("voice_open", { isOpen: false });
                    this.event(yalla.data.EnumCustomizeCmd.Dominose_DisableAudio, idx);
                    cb && cb(success);
                }
            });
        }
        /**
         * 离开房间
         */
        public levelGameRoom(cb?, isForceCb = true) {
            this._voiceOpen = false;
            this._voiceToken = '';
            this.isJoinRoom = false;
            yalla.Native.instance.levelGameRoom(cb, isForceCb);
        }

        /**1.4.5 android还是等离开后才退出语音房 ios离开语音房间回掉 中 播放结算音效*/
        public gameOverSound(soundName) {
            // this.levelGameRoom(()=>{
            //     yalla.Sound.playSound(soundName);
            // }, false);
            if (yalla.Native.instance.deviceType == DeviceType.IOS) {
                this.levelGameRoom(()=>{
                    yalla.Sound.playSound(soundName);
                });
            } else {
                yalla.Sound.playSound(soundName, Laya.Handler.create(this, () => { this.levelGameRoom(); }));
            }
        }

        //----------------------handle----------------------
        public handleGetAgoraToken(msg: any): void {
            // var msg;
            // if (isDecode) msg = this._client.decodeMsg(d, "GetAgoraTokenResponse");
            // else msg = d;
            yalla.Debug.log(this._voiceToken + '-' + "==handleGetAgoraToken==IsUpdateZegoToken:"+Global.IsUpdateZegoToken);
            yalla.Debug.log(msg);
            this._voiceToken = msg.token;
            this._channelName = msg.cName;
            this.joinGameRoomWithToken(yalla.data.UserService.instance.user.idx);
        }

        public handleGetZegoToken(msg: any): void {
            // var msg;
            // if (isDecode) msg = this._client.decodeMsg(d, "GetZegoTokenResponse");
            // else msg = d;

            yalla.Debug.log(this._voiceToken + '-' + "==handleGetZegoToken==IsUpdateZegoToken:"+Global.IsUpdateZegoToken);
            yalla.Debug.log(msg);
            this._voiceToken = msg.token;
            this._channelName = msg.cName;
            if (Global.IsUpdateZegoToken) {
                yalla.Native.instance.zegoTokenResponse(this._voiceToken, this._channelName);
                Global.IsUpdateZegoToken = false;
            } else {
                this.joinGameRoomWithToken(yalla.data.UserService.instance.user.idx);
            }
        }

        // private handleAgoraOperate(d: any): void {
        //     var msg = this._client.decodeMsg(d, "AgoraOperateRequestAndResponse");
        // }

        public clear(): void {
            this._voiceOpen = false;
            this._voiceToken = '';
            this.isJoinRoom = false;
            Laya.timer.clearAll(this);

            // yalla.net.Client.instance.offAll();
            // this._client.off(yalla.data.EnumCmd.Dominose_Agora_Operate, this, this.handleAgoraOperate);
            // this._client.off(yalla.data.EnumCmd.Dominose_GetAgoraToken, this, this.handleGetAgoraToken);
            // this._client.off(yalla.data.EnumCmd.Dominose_GetZegoToken, this, this.handleGetZegoToken);
        }
    }
}