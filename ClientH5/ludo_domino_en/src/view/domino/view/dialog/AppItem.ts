module yalla {
    import View = laya.ui.View;
    import Tween = Laya.Tween;
    import Ease = Laya.Ease;
    import Handler = Laya.Handler;
    export class AppItem{
        private _ui:View;

        constructor(ui:View) {
            this._ui = ui;
        }

        public get ui():View
        {
            return this._ui;
        }

        public get name():string
        {
            return this._ui.name;
        }

        public show():void
        {
            if(!this._ui) return;

            this._ui.pivot(this._ui.width/2, this._ui.height/2);
            this._ui.pos(Laya.stage.width /2, Laya.stage.height/2);
            Laya.stage.addChild(this._ui);
            // if(yl.Global.game_state != yl.data.GameState.State_Sleep){
            //     this._ui.scale(0.01,0.01);
            //     this._ui.scale(1,1);
            //     Laya.Tween.from(this._ui, {x:Laya.stage.width / 2,y:Laya.stage.height / 2,scaleX:0,scaleY:0},300,
            //                             Ease.backOut,Handler.create(this,function(ui){
            //                                 Laya.Tween.clearTween(ui);
            //                             },[this._ui]));
            // }
        }

        public hide():void{
            if(!this._ui) return;
            Tween.to(this._ui,{x:Laya.stage.width / 2,y:Laya.stage.height / 2,scaleX:0,scaleY:0},300,
                                Ease.strongOut,Handler.create(this,(ui)=>{
                                    // Laya.Tween.clear(ui);
                                }, [this._ui]),0,true,true);
        }

        public clear():void
        {
            if(this._ui) {
                Laya.Tween.clearAll(this._ui);
                this._ui.destroy();
                // this._ui.removeSelf();
            }
        }
    }
}