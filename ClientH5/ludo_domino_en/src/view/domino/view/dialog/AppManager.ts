module yalla {
    import View = laya.ui.View;
    import Tween = Laya.Tween;
    import Ease = Laya.Ease;
    import Handler = Laya.Handler;

	export class AppManager extends laya.events.EventDispatcher {

		private static _instance: AppManager;
        private _appHash:Object = {};

		constructor() {
			super();
		}

		static get instance(): AppManager {
			return this._instance || (this._instance = new AppManager());
		}

        public load(app:yalla.AppItem):void
        {
            if(this._appHash[app.name] || !app) return;
            this._appHash[app.name] = app;
            app.show();
        }

        public close(app:yalla.AppItem):void
        {
            if(!this._appHash[app.name] || !app) return;
            app.hide();
            delete this._appHash[app.name];
        }
	}
}