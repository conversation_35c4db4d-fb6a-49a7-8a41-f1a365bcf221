module yalla.view.game {
    /**
     * 自己相关信息   聊天fastchat   chat  加入语音房间
     */
    export class BottomView {
        private _ui: ui.domino.gameUI;
        private _fastChat: yalla.common.FastChat;
        private _chat: any;
        private _chatAniTemplete: Laya.Templet;
        private _chatAniSk: Laya.Skeleton;

        private _friendUnReadMsg: yalla.common.UnReadMsg;
        private _chatUnReadMsg: yalla.common.UnReadMsg;

        private _isJoin: boolean = false;           //是否成功加入语音房间
        private _playersView: ui.domino.item.seatPlayerUI;
        private _playersViewCls: PlayersView;
        // private _emojiBtnPoint: Laya.Point;
        // private _chatBtnPoint: Laya.Point;

        private isSpectator: boolean = false;
        private _isOpenFriend: boolean = false;

        constructor(ui: ui.domino.gameUI, isSpectator: boolean = false, playersView: PlayersView, topHeight: number = 0) {
            this.isSpectator = isSpectator;
            this._ui = ui;
            this._playersView = playersView._view as ui.domino.item.seatPlayerUI;
            this._playersViewCls = playersView;
            // this._emojiBtnPoint = this._playersView.emojiBtn.localToGlobal(new Laya.Point());
            // this._chatBtnPoint = this._playersView.chatBtn.localToGlobal(new Laya.Point());

            if (!isSpectator) {
                this.init();
                this._friendUnReadMsg = new yalla.common.UnReadMsg(this._playersView.friend_toBeRead);
                this._chatUnReadMsg = new yalla.common.UnReadMsg(this._playersView.toBeRead);
                this.readMsgNum();
                // this.readMsgNum(yalla.Global.unReadMsg);
                // yalla.Global.unReadMsg = null;
            } else {
                this.initLiveChat(topHeight);
            }
        }
        private initLiveChat(topHeight: number) {
            this._chat = new LiveChat(topHeight);
            this._ui.addChild(this._chat);
            this.initLiveChatEvent();

        }
        private initLiveChatEvent() {
            yalla.event.YallaEvent.instance.off("ludo_chat_my", this, this.onInputChat);
            yalla.event.YallaEvent.instance.on("ludo_chat_my", this, this.onInputChat);//自己发送的消息
        }

        private init(): void {
            var key = "domino_addRound_extendOn";
            if (!Laya.LocalStorage.getJSON(key)) Laya.LocalStorage.setJSON(key, { selected: false });
            this._playersViewCls.addTimeCheckBox.selected = Laya.LocalStorage.getJSON(key).selected;
            if (yalla.Font.lan == 'ar') {
                this._playersViewCls.addTimeCount.x += 28;
                this._playersViewCls.extendOnTxt.x -= 2;
            }

            this._chat = new yalla.common.Chat(10021);
            this._chat.visible = false;
            this.initEvent();
        }

        private initEvent(): void {
            this._playersView.voiceBtn.on(Laya.Event.CLICK, this, this.onClickVoice);
            this._playersView.emojiBtn.on(Laya.Event.CLICK, this, this.onClickFastChat);
            this._playersView.chatSp.on(Laya.Event.CLICK, this, this.onClickChat);
            this._playersView.friendBtn.on(Laya.Event.CLICK, this, this.onClickFriend);

            this._chat.on(Laya.Event.CLICK, this, this.onClickChatView);
            this._chat.on('close', this, this.closeChat);
            this._playersView.addTimeBtn.on(Laya.Event.CLICK, this, this.onClickAddTime);
            this._playersView.quick_buy_view.on(Laya.Event.CLICK, this, this.onClickQuickBuy);
            this._playersViewCls.addTimeCheckBox.on(Laya.Event.CLICK, this, this.onClickAddSelected);

        }

        public get isChatOpen(): boolean {
            return this._chat.visible;
        }

        // public getChatBtnPoint(): Laya.Point{
        //     if (!this._chatBtnPoint) {
        //         if (!this._playersView || !this._playersView.chatBtn) return new Laya.Point(0, 0);
        //         this._chatBtnPoint = this._playersView.chatBtn.localToGlobal(new Laya.Point());
        //     }
        //     return this._chatBtnPoint;
        // }
        // public getEmojiBtnPoint(): Laya.Point{
        //     if (!this._emojiBtnPoint) {
        //         if (!this._playersView || !this._playersView.emojiBtn) return new Laya.Point(0, 0);
        //         this._emojiBtnPoint = this._playersView.emojiBtn.localToGlobal(new Laya.Point());
        //     }
        //     return this._emojiBtnPoint;
        // }

        /**
         * 聊天框按钮变更
         * @param isVisible 
         */
        private updateChatBtn(isVisible: boolean): void {
            if (!this._chat) return;
            if (!this.isSpectator) {
                this._chat.setVisible(isVisible);
            }
            
            if (isVisible) {
                this._chat.visible = true;
                yalla.common.ChatManager.instance.clearChatMsgAni();
                this._playersView.chatBtn.skin = yalla.getDomino('btn_closeChat');
                this._chat.show();
            } else {
                this._chat.close();
                this._playersView.chatBtn.skin = yalla.getDomino('btn_chat');
                this._chatUnReadMsg && this._chatUnReadMsg.updateStatus(0);
            }
        }

        /**
         * 快捷聊天框按钮变更
         * @param isVisible 
         */
        private updateFastChatBtn(isVisible: boolean): void {
            if (!this._fastChat) return;

            this._fastChat.setVisible(isVisible);
            if (isVisible) {
                this._playersView.emojiBtn.skin = yalla.getDomino('btn_closeChat');
            } else {
                this._playersView.emojiBtn.skin = yalla.getDomino('btn_Expression');
            }
        }

        public closeFriendChat(): void {
            if (this._isOpenFriend) {
                this._isOpenFriend = !this._isOpenFriend;
                this.exclusiveOperate(3);
            }
        }

        private updateFriendBtn(isVisible: boolean): void {
            this._isOpenFriend = isVisible;
            var pos;
            if (isVisible) {
                var bottom = 265;
                var arrowX = 388;
                var p = this._playersView.friendBtn.localToGlobal(Laya.Pool.getItemByClass('Point_chat', Laya.Point));
                if (p && p.x) arrowX = p.x + 45;
                pos = { bottom: bottom, arrowX: arrowX, screenScale: yalla.Screen.screen_scale };
                this._playersView.friendBtn.skin = yalla.getDomino('btn_closeChat');
            } else {
                this._playersView.friendBtn.skin = yalla.getDomino('btn_friendChat');
                if (!this._friendUnReadMsg || this._friendUnReadMsg.readNum < 1) {
                    this._playersView.friendBtn.visible = false;
                    this.updateBtnBox();
                }
            }
            yalla.Native.instance.friendChat(yalla.Global.gameType, Number(isVisible), pos);
        }

        private voiceFailTip(): void {
            yalla.common.TipManager.instance.showTip(yalla.data.TranslationD.Game_Tip_Fail_Microphone, {}, 3000, 0, this._ui);
        }

        /**
         * 音频
         */
        private _joinRoomNums: number = 0;
        private _timeout = null;
        private onClickVoice(e: Event): void {
            yalla.Debug.log(yalla.data.VoiceService.instance.isJoinRoom + "=isJoinRoo====onClickVoice====voiceToken:" + yalla.data.VoiceService.instance.voiceToken);
            e.stopPropagation();
            this.exclusiveOperate();

            if (yalla.data.VoiceService.instance.voiceToken.length < 1) {
                //重新获取tocken
                // yalla.data.VoiceService.instance.getToken();
                yalla.data.RoomService.instance.getToken();
                return;
            }
            var idx = yalla.data.UserService.instance.user.idx;
            if (!yalla.data.VoiceService.instance.isJoinRoom) {
                // 重新加入房间
                clearTimeout(this._timeout);
                this._timeout = setTimeout(() => {//1.2.9 add 鸿蒙系统首次domino连续点击语音按钮 应用卡顿甚至闪退 
                    yalla.data.VoiceService.instance.joinGameRoomWithToken(idx, Laya.Handler.create(this, () => {
                        if (this._joinRoomNums > 0) { yalla.Debug.log("voiceFailT___1"); this.voiceFailTip(); }//
                        this._joinRoomNums++;
                    }), 2);

                }, 500)
                return;
            }
            var room = yalla.data.RoomService.instance.room;
            var data = {
                accountId: idx,
                gameId: 10021,
                roomId: room.roomid,
                roomUserNum: room.playerNums,
                roomCost: room.cost
            }
            yalla.Debug.log(data);
            yalla.Debug.log(yalla.Mute.isBanTalk);
            if (yalla.data.VoiceService.instance.voiceOpen) {
                yalla.data.VoiceService.instance.disableAudio(idx, (v) => {
                    if (v == 0) {
                        this.updateVoiceBtn(false);
                        data['type'] = 0;
                        yalla.Voice.instance.voiceStatistic(data);
                    } else {
                        this.voiceFailTip(); yalla.Debug.log("voiceFailT___3")
                    }
                });
            } else {
                if (yalla.Mute.isBanTalk) {
                    //禁言提示
                    yalla.Mute.showBanTalkDialog();
                    return;
                }
                yalla.data.VoiceService.instance.enableAudio(idx, (v) => {
                    if (v == 0) {
                        this.updateVoiceBtn(true);
                        data['type'] = 1;
                        yalla.Voice.instance.voiceStatistic(data);

                    } else {
                        this.voiceFailTip(); yalla.Debug.log("voiceFailT___2")
                    }
                }, 2);
            }
        }

        private onClickChatView(e: Laya.Event): void {
            e.stopPropagation();

            if (this._fastChat && this._fastChat.visible) {
                this.updateFastChatBtn(false);
            }
        }

        /**
         * 点击游戏界面
         */
        public onClickGameUI(): void {
            if (this.isSpectator) {

            } else {
                if (this._fastChat && this._fastChat.visible) {
                    this.updateFastChatBtn(false);
                }
                if (this._chat && this._chat.visible && !this._chat.inputChat.focus) {
                    this.updateChatBtn(false);
                }
                if (this._isOpenFriend) this.updateFriendBtn(false);

                this.hideAddRoundTimeView();
            }
        }

        /**
         * 红点消息 (还需要更新chat 或者fastchat 打开情况下，弹框箭头更新)
         * @param msg {num:1}
         */
        private _btnBoxX: number = 0;
        public readMsgNum(msg: any = null): void {
            var num = 0;
            if (msg && msg.num) num = msg.num;

            if (this._playersView) {
                if (this._isOpenFriend) {
                    this._playersView.friendBtn.visible = true;
                } else {
                    this._playersView.friendBtn.visible = num > 0
                }
            }
            this._friendUnReadMsg && this._friendUnReadMsg.updateStatus(num);
            this.updateBtnBox();
        }

        private updateBtnBox(): void {
            this._playersView.btnBox.x = (this._playersView.friendBtn.visible ? 152 : 180);
            if (this._btnBoxX != this._playersView.btnBox.x) {
                var p;
                if (this._chat && this._chat.visible) {
                    p = this._playersView.chatBtn.localToGlobal(new Laya.Point());
                    this._chat.arrow.x = p.x + 35;
                    this._btnBoxX = this._playersView.btnBox.x;

                } else if (this._fastChat && this._fastChat.visible) {
                    p = this._playersView.emojiBtn.localToGlobal(new Laya.Point());
                    this._fastChat.arrow.x = p.x + 20;

                    this._btnBoxX = this._playersView.btnBox.x;
                }
            }
        }

        /**
         * 点击快捷聊天
         */
        private onClickFastChat(e: Laya.Event): void {
            yalla.Sound.playSound('click');
            e.stopPropagation();

            this.exclusiveOperate(1);

            yalla.Native.instance.mobClickEvent(buryPoint.DGAME_EXPRESSION);//点击表情
        }

        /**
         * 点击聊天入口
         */
        private onClickChat(e: Laya.Event): void {
            yalla.Sound.playSound('click');
            e.stopPropagation();

            this._chatUnReadMsg && this._chatUnReadMsg.updateStatus(0);

            this._playersView.toBeRead.visible = false;
            var chatPoint = this._playersView.chatBtn.localToGlobal(new Laya.Point());
            if (!this._ui.contains(this._chat) && this._playersView.chatBtn) {
                this._chat.box.top = this._ui.topBg.height;
                this._chat.top = 0;
                this._chat.bottom = Laya.stage.height - chatPoint.y + 30;
                this._ui.addChild(this._chat);
                this._chat.zOrder = 991;
            }
            this._chat.arrow.x = chatPoint.x + 35;

            this.exclusiveOperate(2);

            yalla.Native.instance.mobClickEvent(buryPoint.DGAME_MESSAGE);//点击消息
        }

        /**
         * 点击好友入口
         * @param e 
         */
        private onClickFriend(e: Laya.Event): void {
            yalla.Sound.playSound('click');
            e.stopPropagation();
            this._isOpenFriend = !this._isOpenFriend;
            this.exclusiveOperate(3);

            yalla.util.clogDBAmsg('10179');
        }

        /**
         * 1 fastchat 2 chat  3 friendchat 互斥点击
         */
        private exclusiveOperate(type: number = 0) {
            yalla.Debug.log(type + ":type====exclusiveOperate====");
            if (type == 0) {
                this.onClickGameUI();

            } else if (type == 1) {
                if (this._chat) {
                    this._chat.close();
                    this._playersView.chatBtn.skin = yalla.getDomino('btn_chat');
                }
                this.updateFriendBtn(false);

                var p = this._playersView.emojiBtn.localToGlobal(new Laya.Point());
                if (this._fastChat) {
                    this.updateFastChatBtn(!this._fastChat.visible);
                } else {
                    this._fastChat = new yalla.common.FastChat(yalla.Global.gameType);
                    var x: number = yalla.data.GameConfig.Width - this._fastChat.width - 60;
                    this._fastChat.pos(x, p.y - 15);
                    this._ui.addChild(this._fastChat);
                    this._fastChat.zOrder = 991;
                    this.updateFastChatBtn(true);
                }
                this._fastChat.arrow.x = p.x + 20;

            } else if (type == 2) {
                this.updateChatBtn(!this._chat.visible);

                this.updateFastChatBtn(false);
                this.updateFriendBtn(false);

            } else if (type == 3) {
                this.updateFriendBtn(this._isOpenFriend);

                this.updateChatBtn(false);
                this.updateFastChatBtn(false);
            }

            yalla.common.InteractiveGift.Instance.hideGiftView();
        }

        private closeChat(): void {
            if (this._playersView && this._playersView.chatBtn) {
                this._playersView.chatBtn.skin = yalla.getDomino('btn_chat');
            }
            this._chatUnReadMsg && this._chatUnReadMsg.updateStatus(0);
            this._chat && this._chat.close();
        }

        private onInputChat(msg) {
            yalla.Debug.log("===联赛观战聊天===" + JSON.stringify(msg));
            var room = yalla.data.RoomService.instance.room;
            yalla.NativeWebSocket.instance.sendNetMsg_watch({ command: 5, data: { roomId: room.roomid, msg: msg.msg, extension: msg.extension } })
        }

        public onLiveChat(msg: any, myIdx: number) {
            var isMe = msg.idx == myIdx;
            var side = isMe ? 1 : 0;
            if (Font.lan == 'ar') side = isMe ? 0 : 1;

            this._chat.onChat({
                playerShowInfo: {
                    fPlayerInfo: msg
                },
                chat: msg,
                side: side,
                isMe: isMe,
                watch: true,
            }, isMe)
        }
        /**
         * 如果快捷聊天是自己发送，则关闭fastChatview
         * @param msg 
         * @param playerInfo 
         * @param color 
         * @param myIdx 
         */
        public onChat(msg: any, playerInfo: playerShowInfo, myIdx: number) {
            if (!this._chat || !playerInfo) return;
            yalla.Debug.log("--------onChat")
            var isMe = msg.idx == myIdx;
            var side = isMe ? 1 : 0;
            if (Font.lan == 'ar') side = isMe ? 0 : 1;

            var chat = {
                idx: msg.idx,
                playerShowInfo: playerInfo,
                chat: msg,
                isMe: isMe,
                side: side
            }
            this._chat.onChat(chat);
            if (msg.idx != myIdx) {
                this._chatUnReadMsg && this._chatUnReadMsg.addReadNum();
            } else {
                this.updateFastChatBtn(false);
            }
            if ((this._chatUnReadMsg && this._chatUnReadMsg.readNum > 0) && (!this._chat.visible || !this._ui.contains(this._chat))) {
                this._playersView.toBeRead.visible = true;
            }
        }

        private hideChat(): void {
            this.closeChat();
            // this._chat && this._chat.close();
            if (this._fastChat) {
                this.updateFastChatBtn(false);
            }
        }

        /**
         * domino/btn_speak.png
         * domino/btn_nospeak.png
         * @param voiceOpen 
         */
        private updateVoiceBtn(voiceOpen: boolean): void {
            if (voiceOpen) {
                this._playersView.voiceBtn.skin = yalla.getDomino('btn_speak');
                this._playersView.voiceBtn.stateNum = 2;
            } else {
                this._playersView.voiceBtn.skin = yalla.getDomino('btn_nospeak');
                this._playersView.voiceBtn.stateNum = 1;
            }
        }

        /**
         * 语音房间
         */
        public handleJoinAgora(): void {
            this._isJoin = true;
            var isVoice = yalla.data.VoiceService.instance.voiceOpen;
            this.updateVoiceBtn(isVoice);
            if (isVoice) {
                if (!Laya.LocalStorage.getJSON("voice_first")) {
                    Laya.LocalStorage.setJSON("voice_first", { isFirst: false });
                    if (!this._chatAniTemplete) {
                        this._chatAniTemplete = new Laya.Templet();
                    }

                    this._chatAniTemplete.loadAni(yalla.getSkeleton('voicebtn/anniusaoguang'));
                    this._chatAniTemplete.once(Laya.Event.COMPLETE, this, () => {
                        this._chatAniSk = this._chatAniTemplete.buildArmature(0);
                        this._chatAniSk.pos(this._playersView.voiceBtn.width / 2 - 3, this._playersView.voiceBtn.height / 2 - 3);
                        this._chatAniSk.blendMode = "lighter";
                        this._playersView.voiceBtn.addChild(this._chatAniSk);
                        this._chatAniSk.play(0, true);
                        var pp: Laya.Point = this._playersView.btnBox.localToGlobal(new Laya.Point());
                        pp.x = this._playersView.btnBox.x;
                        var tip = new VoiceTip(pp);
                        this._ui.addChild(tip);
                        tip.once("click", this, () => {
                            tip.removeSelf();
                            tip.destroy(true);
                            if (!!this._chatAniSk) {
                                this._chatAniSk.removeSelf();
                                this._chatAniSk.destroy(true);
                            }
                        });
                    });
                }
            }
        }
        /**
         * 增加延时消息返回
         * @param msg 
         */
        public handleAddRoundTime(msg: any): void {
            if (!yalla.data.RoomService.instance.room.isMyActive) return;
            if (msg.code > 1) {
                this._playersViewCls.roundTime_add_View.visible = true;
                return;
            }
            this.updateAddCount();
        }

        public updateAddCount() {
            if (this.isSpectator) return;
            var room = yalla.data.RoomService.instance.room;
            var isLess = room.isLessDiamond();
            // yalla.Debug.log(room.leftDiamond + '--updateAddCount--'+room.nextDiamond + '---'+room.remainRoundCounts+'---'+isLess);
            if (isLess || room.remainRoundCounts < 1) {//钻石不足 次数用尽 按钮置灰
                this._playersView.clockImg.skin = yalla.getDomino('clock_off');
                this._playersView.addTimeGoldTxt.width = 44;
                this._playersView.addTimeGoldTxt.text = "Lack";
                this._playersViewCls.addTimeCount.text = "0";
                this._playersView.coinImg.visible = false;
                return;
            }
            // yalla.Debug.log(room.leftDiamond + '--updateAddCount111--'+room.nextDiamond + '---'+room.remainRoundCounts);
            if (!this._playersViewCls.addTimeCheckBox.selected) this._playersView.clockImg.skin = yalla.getDomino('clock_on');
            else this._playersView.clockImg.skin = yalla.getDomino('clock_off');
            if (room.isRoundTimeFree(room.nextDiamond)) {
                this._playersView.addTimeGoldTxt.width = 44;
                this._playersView.addTimeGoldTxt.text = "Free";
                this._playersView.coinImg.visible = false;
            } else {
                this._playersView.coinImg.visible = true;
                this._playersView.addTimeGoldTxt.width = 30;
                this._playersView.addTimeGoldTxt.text = room.nextDiamond + '';
            }
            this._playersViewCls.addTimeCount.text = room.remainRoundCounts + '';
        }

        /**
         * 非自己操作，点击显示面板；
         * 自己操作,且money足够,且本回合未延长，点击则消耗钻石延长时间
         * 
         * 设置延长cd时间
         * dominoInfo 获取延长cd次数 （包括断线重连后也需要更新cd次数）；切换用户也需要知道本次是否已经使用cd延长次数
         * 点击 使用延长次数
         */
        private onClickAddTime(e: Laya.Event): void {
            e.stopPropagation();
            yalla.Sound.playSound('click');
            var room = yalla.data.RoomService.instance.room;
            var isSelected = this._playersViewCls.addTimeCheckBox.selected;
            var isLess = room.isLessDiamond();
            // yalla.Debug.log(room.leftDiamond+'--room.nextDiamond:'+room.nextDiamond+"======onClickAddTime=====isLess="+isLess);
            if (isSelected || isLess || room.remainRoundCounts < 1 || !room.isMyActive || room.roundTime > room.playerRoundTime) {
                if (isLess && room.remainRoundCounts > 0) {
                    this._playersView.quick_buy_view.visible = !this._playersViewCls.roundTime_add_View.visible;
                } else {
                    this._playersViewCls.roundTime_add_View.visible = !this._playersViewCls.roundTime_add_View.visible;
                    //此时同步位置
                    let globalPoint = this._playersView.roundTime_placehold_box.localToGlobal(new Laya.Point());
                    let localPoint = (this._playersViewCls.roundTime_add_View.parent as Laya.View).globalToLocal(globalPoint);
                    this._playersViewCls.roundTime_add_View.pos(localPoint.x, localPoint.y);
                }
            } else {
                yalla.data.RoomService.instance.sendAddRoundTime();
            }

            //如果在托管中，需要取消托管
            if (yalla.data.RoomService.instance.room.isTrust) this.onTrust();
        }

        /**
         * 购买金币
         */
        private onClickQuickBuy(): void {
            yalla.Native.instance.buyGood((obj) => {
                // yalla.Debug.log(yalla.data.UserService.instance.user.gold+'===购买钻石==='+yalla.data.RoomService.instance.room.leftDiamond)
                // yalla.Debug.log(obj);
                if (!!obj.status) {
                    yalla.data.UserService.instance.user.addCoin(obj);
                    yalla.data.RoomService.instance.room.addCoin(obj);
                    this.updateAddCount();

                    if (!yalla.Global.IsGameOver) yalla.data.RoomService.instance.IsUpdatePlayerCoin = true;
                    // yalla.Debug.log(yalla.data.UserService.instance.user.gold+'===购买钻石11==='+yalla.data.RoomService.instance.room.leftDiamond)
                } else {
                    if (!yalla.Global.IsGameOver) yalla.data.RoomService.instance.sendPlayerCoin(1);
                    // yalla.common.TipManager.instance.showTip(yalla.data.TranslationD.Game_Tip_Fail_BuyGood, { alpha: 0 }, 0, 5000);
                }
            });
        }

        /**
         * 是否自动延长时间
         */
        private onClickAddSelected(): void {
            var room = yalla.data.RoomService.instance.room;
            if (!this._playersViewCls.addTimeCheckBox.selected && room.remainRoundCounts > 0) {
                this._playersView.clockImg.skin = yalla.getDomino('clock_on');
            } else {
                this._playersView.clockImg.skin = yalla.getDomino('clock_off');
            }
            Laya.LocalStorage.setJSON("domino_addRound_extendOn", { selected: this._playersViewCls.addTimeCheckBox.selected });
        }

        /**------------系统托管-------------------- */
        private trustDely() {
            this.addTrustEvent();
            this._playersView.trust_wait.visible = false;
            this._playersView.trust.selected = false;
        }

        private addTrustEvent() {
            this._ui.bg.on(Laya.Event.CLICK, this, this.onTrust);
            this._playersView.on(Laya.Event.CLICK, this, this.onTrust);
            this._playersView.trusteeship.on(Laya.Event.CLICK, this, this.onTrust);

        }
        private removeTrustEvent() {
            this._ui.bg.off(Laya.Event.CLICK, this, this.onTrust);
            this._playersView.trust.off(Laya.Event.CLICK, this, this.onTrust);
            this._playersView.off(Laya.Event.CLICK, this, this.onTrust);
        }

        //===============托管需要再1.3.4添加，这个功能生效的话，必须tiketLogin传入******* 的版本号================        
        /**
         * 取消托管事件监听
         */
        public onTrust() {
            if (yalla.Global.IsGameOver) return;
            this._playersView.trust_wait && (this._playersView.trust_wait.visible = true);
            yalla.data.RoomService.instance.cancleTrust();
            Laya.timer.once(5000, this, this.trustDely);
            this.removeTrustEvent();
        }


        public systemTrust(msg: any) {
            var room = yalla.data.RoomService.instance.room;
            // var v = msg.idx == yalla.Global.Account.idx && !room.isTrust;
            var v = !room.isTrust;
            if (v) {
                room.isTrust = true;
                var hasdice = false;
                this.showTrust(hasdice);
            }
            return v;
        }

        /**
     * 显示托管状态
     */
        public showTrust(hasdice: boolean = false) {
            var v = !hasdice;
            if (this._playersView.trusteeship.visible != v) {
                this._playersView.trusteeship.visible = v;
                yalla.Global.isFouce && this.changeTrustAni(v);
                this._playersView.trust.offAll();
                this._playersView.trust_wait.visible = false;
                this._playersView.trust.selected = false;
                this.addTrustEvent();
            }
        }
        /**取消托管UI */
        public hideTrust(removeEvent: boolean) {
            Laya.timer.clear(this, this.trustDely);
            this._playersView.trusteeship.visible = false;
            this.changeTrustAni(false);
            this._playersView.trust.selected = false;
            this._playersView.trust_wait.visible = false;
            removeEvent && this.removeTrustEvent();
        }

        public changeTrustAni(show: boolean) {
            if (show) {
                this._playersView.trust_jump.play(0, true);
                this._playersView.trust_wait.play(0, true);
            } else {
                this._playersView.trust_jump.gotoAndStop(0);
                this._playersView.trust_wait.gotoAndStop(0);
            }
        }

        /**------------------------------trust end--------------------------- */


        private hideAddRoundTimeView(): void {
            if (!this.isSpectator) this._playersView.quick_buy_view.visible = this._playersViewCls.roundTime_add_View.visible = false;
        }

        public hide(): void {
            this.hideChat();
            this.hideAddRoundTimeView();
        }
        public clearInputTxt(): void{
            this._chat && this._chat.clearInputTxt();
        }

        private removeEvent(): void {
            this._chat.off(Laya.Event.CLICK, this, this.onClickChatView);
            this._chat.off('close', this, this.closeChat);
            this._playersView.voiceBtn.off(Laya.Event.CLICK, this, this.onClickVoice);
            this._playersView.emojiBtn.off(Laya.Event.CLICK, this, this.onClickFastChat);
            this._playersView.chatSp.off(Laya.Event.CLICK, this, this.onClickChat);
            this._playersView.friendBtn.off(Laya.Event.CLICK, this, this.onClickFriend);

            this._playersView.addTimeBtn.off(Laya.Event.CLICK, this, this.onClickAddTime);
            this._playersView.quick_buy_view.off(Laya.Event.CLICK, this, this.onClickQuickBuy);
            this._playersViewCls.addTimeCheckBox.off(Laya.Event.CLICK, this, this.onClickAddSelected);
        }

        private removeLiveChatEvent() {
            yalla.event.YallaEvent.instance.off("ludo_chat_my", this, this.onInputChat);
        }
        public clear(): void {
            Laya.Pool.recover('Point_chat', Laya.Point);
            !this.isSpectator ? this.removeEvent() : this.removeLiveChatEvent();
            this._fastChat && this._fastChat.clear();
            this._chat && this._chat.clear();
            // this._chatBtnPoint = null;
            // this._emojiBtnPoint = null;
            this._chat = null;
            this._fastChat = null;
        }
    }
}