module yalla.view.game {
    export class Game extends ui.domino.gameUI {
        private _topView: TopView;
        private _tipView: TipView;
        private _bottomView: BottomView;
        public _playersView: PlayersView;
        private _roundTimeView: ui.domino.item.roundTimeUI;

        private _uiList: Array<any> = [];
        private _playerItemHash: Object = {};
        private _resultView: yalla.view.result.Result;
        private _roundResultView: yalla.view.result.RoundResult;

        private _room: yalla.data.Room;
        private _gameMode: AMode;
        private _isGetAllDeskCard: boolean = false;      //是否先获取手牌数据（如果没有，就不执行出牌的协议） ps:如果重连后先给发牌协议就会报错
        private _virtualOutCardItem: OutCardItem;        //当前出的牌（动画表现）
        private _playCardItemTween: Laya.Tween;          //当前还在播放出牌的item的tween
        private _firstPlayIdx: number = 0;               //每一局是否收到 ready 消息, 且保存第一个出牌的idx（自己）
        private scence_id: number = 21000;
        private hairHeight: number = 0;

        private _isSpectator: boolean = false;

        constructor() {
            super();
            yalla.Global.IsGameOver = false;
            this.initUI();
            this.initEvent();
        }
        set sceneId(val: number) {
            if (val != this.scence_id && val > 21000) {
                this.scence_id = val;
                // if (val == 21007) {
                //     this.bg.skin = `res/test/BG_21007.jpg`;
                //     yalla.Debug.log("使用本地皮肤");
                //     return;
                // }
                var fevent = yalla.event.YallaEvent.instance;
                yalla.Debug.log("sceneId------")
                yalla.Debug.log(yalla.Skin.instance.hasBG(val))
                if (yalla.Skin.instance.hasBG(val)) {
                    fevent.once(`BG_${val}.jpg`, this, () => {
                        if (this.destroyed) return;
                        if (this.bg) {
                            // this.bg.scale(scale, scale);
                            this.bg.skin = yalla.File.filePath + `BG_${val}.jpg`;
                        }
                    })
                    yalla.File.getFileByNative(`BG_${val}.jpg`)
                }
                if (yalla.Skin.instance.hasFurniture(val)) {
                    fevent.once(`furniture_${val}.png`, this, () => {
                        if (this.destroyed) return;
                        if (this.furniture) {
                            this.furniture.skin = yalla.File.filePath + `furniture_${val}.png`;
                        }
                    })
                    yalla.File.getFileByNative(`furniture_${val}.png`)
                }
            }
        }
        get sceneId(): number {
            return this.scence_id;
        }
        set isSpectator(val: boolean) {
            this._isSpectator = val;
            this._topView.isSpectator = val;
        }

        public updateAudiencePLayer() {
            if (this._topView['_topUI']['titleSpectator'].visible && Audience.instance.displayedInStage) {
                Audience.instance.list.array = ludoRoom.instance.getAudiencePlayers();
            }
        }

        private initUI(): void {
            yalla.Debug.log("======domino Game 初始化UI=====");
            // this.deskSp.cacheAs = 'normal';
            this.deskSp.rotation = 90;   //横竖屏切换
            this._topView = new TopView(this);
            this._tipView = new TipView(this);
            var scale = Laya.stage.height / 1336;
            this.bg.scale(scale, scale);

            if (yalla.Screen.hasHair) {
                // this._topView.topUI['topMc'].top += 50;
                // this.cardSp_2.top += 50;
                // this.topBg.height += 50;
                this.hairHeight = yalla.Global.ProfileInfo.hairHeight ? yalla.Global.ProfileInfo.hairHeight / yalla.Screen.screen_scale : 50;
                this._topView.adapaterUI(this.hairHeight);
                // this._playersView._view['cardSp_2'].top += hairHeight;
                this.topBg.height += this.hairHeight;
            }

            this._room = yalla.data.RoomService.instance.room;
            this._topView.init(this._room);

            this.isSpectator = false;
            this._roundTimeView = new ui.domino.item.roundTimeUI();
            this._playersView = new PlayersView(this, this._isSpectator, this.hairHeight, this._roundTimeView)
            this.setLimitVisitor();
            this._bottomView = new BottomView(this, this._isSpectator, this._playersView, this.hairHeight);
            this.addChild(this._roundTimeView);
            // this._roundTimeView.visible = false;
            // this.setLimitVisitor();
        }

        private initEvent(): void {
            this.on(Laya.Event.CLICK, this, this.onClickGameUI);

            yalla.event.YallaEvent.instance.on(yalla.data.EnumCustomizeCmd.Dominose_Chat_My, this, this.handleChatMy);
            yalla.event.YallaEvent.instance.on(yalla.data.EnumCustomizeCmd.Dominose_Back, this, this.onClickBack);
            yalla.event.YallaEvent.instance.on(yalla.data.EnumCustomizeCmd.Dominose_PlayCardNext, this, this.handleNextPlayMsg);
            yalla.event.YallaEvent.instance.on(yalla.data.EnumCustomizeCmd.Dominose_MsgInManunal, this, this.playMsg);
            yalla.event.YallaEvent.instance.on(yalla.data.EnumCustomizeCmd.Dominose_ChatOFF_My, this, this.handleChatOff);
            yalla.event.YallaEvent.instance.on('click_gameTopview', this, this.onClickGameTopView);

            yalla.Native.instance.on(yalla.Native.instance.Event.READMSGNUM, this, this.readMsgNum);
            yalla.Native.instance.on(yalla.Native.instance.Event.SPEAKER, this, this.handleSpeakResponse);
            yalla.common.InteractiveGiftAnimation.Instance.on(yalla.common.InteractiveGiftAnimation.Instance.Event.UpdateGiftIcon, this, this.updateGiftIcon);
            yalla.data.VoiceService.instance.on(yalla.data.EnumCustomizeCmd.Dominose_JoinAgora, this, this.handleJoinAgora);
            yalla.data.VoiceService.instance.on(yalla.data.EnumCustomizeCmd.Dominose_EnableAudio, this, this.handleEnableAudio);
            yalla.data.VoiceService.instance.on(yalla.data.EnumCustomizeCmd.Dominose_DisableAudio, this, this.handleDisableAudio);

            yalla.event.YallaEvent.instance.on(yalla.data.EnumCmd.Dominose_EnterRoom, this, this.handleEnterRoom);
            yalla.event.YallaEvent.instance.on(yalla.data.EnumCmd.Dominose_GameEvent, this, this.handleStartGame);
            yalla.event.YallaEvent.instance.on(yalla.data.EnumCmd.Dominose_GAME_OPERATE, this, this.handleGameOperate);
            yalla.event.YallaEvent.instance.on(yalla.data.EnumCmd.Dominose_QuitRoom, this, this.handleQuitGame);
            yalla.event.YallaEvent.instance.on(yalla.data.EnumCmd.Dominose_Chat, this, this.handleChat);
            yalla.event.YallaEvent.instance.on(yalla.data.EnumCmd.Dominose_Update_Player_Coin, this, this.handleUpdatePlayerCoin);
            yalla.event.YallaEvent.instance.on(yalla.data.EnumCmd.Dominose_Gift_Cnf_List, this, this.handleUpdateGiftCnf);
            yalla.event.YallaEvent.instance.on(yalla.data.EnumCmd.Dominose_Gift_Send, this, this.handleRevGift);
            yalla.Native.instance.on(yalla.Native.instance.Event.ONASSETSCHANGE, this, this.onAssetsChange);
            yalla.Native.instance.on(yalla.Native.instance.Event.OPEN_ASSETSVIEW, this, this.openAssetsView);
        }

        private openFrameLoop(): void {
            Laya.timer.clear(this, this.frameLoopName);
            var isHonorRoyal = yalla.data.RoomService.instance.room.isHonorRoyal;
            yalla.Debug.log("===domino 是否打开frameLoop==isHonorRoyal=" + isHonorRoyal);
            if (isHonorRoyal) {
                Laya.timer.frameLoop(1, this, this.frameLoopName);
            }
        }
        /** 主界面名称  小局结算名称  游戏结算名称 */
        private frameLoopName(): void {
            for (var k in this._playerItemHash) {
                var data = this._playerItemHash[k].data;
                var canShowRLNameAnimation = yalla.util.canShowRLNameAnimation(data);
                if (canShowRLNameAnimation) this._playerItemHash[k].frameLoopName();
            }

            if (this._roundResultView && this._roundResultView.ui.displayedInStage) {
                this._roundResultView.frameLoopName();
            }

            if (this._resultView && this._resultView.displayedInStage) {
                this._resultView.frameLoopName();
            }
        }

        private _cardSkinId = 0;
        private handleEnterRoom(msg: any = null): void {
            this._cardSkinId = 0;
            this._room = yalla.data.RoomService.instance.room;
            var myPlayer = this._room.getPlayerByIdx(yalla.Global.Account.idx);
            if (myPlayer) {
                yalla.Debug.log("myPlayer.sceneId:" + myPlayer.sceneId);
                this._cardSkinId = myPlayer.cardSkinId;
                if (myPlayer.sceneId) this.sceneId = myPlayer.sceneId;
            }
            Native.instance.removeMatchView();

            if (this._resultView) {
                this._resultView.backHallClear();
                this._resultView.removeSelf();
            }
            yalla.data.RoomService.instance.getToken();

            this._isGetAllDeskCard = false;
            this._isPlayCard = false;

            this.initPlayerView(myPlayer);//如果onligin初始化失败，确保进入房价再次初始化一次
            var [vw, vh] = [503, 240];////设置桌面容器宽高 205+298   120+120
            if (this._room.gameType == 1) {
                vw += 80;
                if (this._room.playerNums > 2) vh += 80;
            }
            var vy = 0;
            if (yalla.Screen.screen_top_height <= 0 && yalla.Screen.screen_scale <= 1) vy = 160;

            this._room.deskPool.deskSpWid = yalla.data.GameConfig.Height - vw;
            this._room.deskPool.deskSpHei = yalla.data.GameConfig.Width - vh;
            if (this._topView && this._topView.topUI) {
                this._room.deskPool.applyCardX = yalla.data.GameConfig.Width - this._topView.topUI['stockMc'].width / 2 + yalla.Screen.screen_top_width;
                this._room.deskPool.applyCardY = this._topView.topUI['infoMc'].top + this._topView.topUI['stockMc'].height / 2;
                this._room.deskPool.deskSpIndex = this.getChildIndex(this.passMc);
                if (!this._gameMode) this._gameMode = new yalla.view.game.AMode(this, this._room, vy);
                this._topView.setRank(this._room);
            }
            if (this._playersView && this._playersView._view) {
                if (this._playersView._view['addTimeBtn']) this._playersView._view['addTimeBtn'].visible = true;
            }

            yalla.common.InteractiveGift.Instance.updateMoney(yalla.data.UserService.instance.user.money, yalla.data.UserService.instance.user.gold);

            if (this._room.isPrivate == GameRoomType.PRIVATE) yalla.Native.instance.mobClickEvent(buryPoint.DOMINO_PRIVATE_PLAYING);
            else if (this._room.isPrivate == GameRoomType.PUBLIC) yalla.Native.instance.mobClickEvent(buryPoint.DOMINO_PUBLIC_PLAYING);

            this.updateTrust(this._room.isTrust);
            if (!this._room.isTrust && yalla.common.TipManager.instance.hasTrustTip) {
                yalla.common.TipManager.instance.showTrustTip(0, this);
            }

            this.openFrameLoop();
        }

        /**
         * 特殊游客登录处理，聊天和麦克风入口隐藏，默认麦克风关闭
         */
        private setLimitVisitor(): void {
            var isLimitVisitor = yalla.Global.Account.isLimitVisitor;
            var isShow = (yalla.Global.Account.isPrivate == 1) && isLimitVisitor;
            yalla.Debug.log(isLimitVisitor + "：isLimitVisitor===限制游客=====isShow：" + isShow + "---isPrivate:" + yalla.Global.Account.isPrivate);
            if (this._playersView._view['chatSp']) this._playersView._view['chatSp'].visible = !isShow;
            if (this._playersView._view['voiceBtn']) this._playersView._view['voiceBtn'].visible = !isShow;
        }

        private handleStartGame(msg: any): void {
            if (!msg || !msg.event) return;
            switch (msg.event) {
                case yalla.data.GameEvent.DOMINO_INFO:
                    if ((msg.status == 1 || msg.status == 2) && yalla.Global.ProtoEnterRoomMsgIndex <= 0) {
                        this._isGetAllDeskCard = false;
                        this.closeRoundView();
                        this.handlePlayMsg();
                    }
                    break;

                case yalla.data.GameEvent.GAME_PLAYER_STATUS_CHANGE:
                case yalla.data.GameEvent.DOMINO_AUTO_PLAY:
                case yalla.data.GameEvent.DOMINO_AUTO_FILL:
                case yalla.data.GameEvent.DOMINO_ADD_ROUNDTIME:
                    this.handlePlayMsg();
                    break;
                case yalla.data.GameEvent.CHAT_OFF://屏蔽状态
                    this.handleChatOff(msg);
                    break;
                case yalla.data.GameEvent.GAME_SHOW_RESULT://游戏结束
                    this.gameOver(msg);
                    break;
            }
        }

        /**
         * 第一局初始化角色信息
         */
        public initPlayerView(myPlayer: playerShowInfo): void {
            if (!this._room || !this._room.player) return;
            if (!this._uiList) this._uiList = [];
            if (!this._playerItemHash) this._playerItemHash = {};
            if (this._uiList.length < 1) {
                var len = this._room.player.player.length;
                var myPlayer = this._room.getPlayerByIdx(yalla.Global.Account.idx);

                if (len == 2) this._uiList = [this._playersView._view['cardSp_0'], this._playersView._view['cardSp_2']];
                else {
                    for (var n = 0; n < len; n++) {
                        this._uiList.push(this._playersView._view['cardSp_' + n]);
                    }
                }
                for (var i = 0; i < len; i++) {
                    var playerData = this._room.player.player[i];
                    var playerItem = new PlayerItem(this._uiList[i], (d: Domino, isDouble: boolean = false) => { this.filterCardPos(d, isDouble); }, this._isSpectator);
                    this._playerItemHash[playerData.fPlayerInfo.idx] = playerItem;
                    playerItem.setData(playerData, myPlayer);
                }
            } else {
                for (var k in this._playerItemHash) {
                    var playerData = yalla.data.RoomService.instance.room.getPlayerByIdx(parseInt(k));
                    this._playerItemHash[k].updateData(playerData, myPlayer);
                }
            }
        }
        private resetDominoInfo(): void {
            this._isGetAllDeskCard = true;
            for (var k in this._playerItemHash) {
                this._playerItemHash[k].reset();
            }
            if (this._virtualOutCardItem) {
                this._virtualOutCardItem.visible = false;
                Laya.Tween.clearAll(this._virtualOutCardItem);
            }
            if (!this._gameMode) {
                this._room = yalla.data.RoomService.instance.room;
                this._gameMode = new yalla.view.game.AMode(this, this._room);
            }
            this._gameMode.reset();
            this._tipView && this._tipView.reset();
            if (this._topView) {
                this._topView.initFillNum(this._room.remainDominoes.length);
                this._topView.updateRoundNum(this._room.roundNum);
            }
        }
        /**
         * 初始化桌面信息
         */
        private initDominoInfo(): void {
            this.resetDominoInfo();
            this._bottomView && this._bottomView.updateAddCount();
            this.updateDeskCard(); //如果已经存在出的桌牌，需要更新显示
            this.handleIssueCard();
        }

        /**
         * 发牌
         */
        private handleIssueCard(): void {
            var deskDominoesLen = yalla.data.RoomService.instance.room.deskDominoes.length;
            var isIssueTween: boolean = false;
            for (var k in this._playerItemHash) {
                var idx = parseInt(k);
                var playerDomino: PlayerAndDominoes = yalla.data.RoomService.instance.room.getPlayerAndDominoByIdx(idx);
                var playItem: PlayerItem = this._playerItemHash[k];
                if (playItem) {
                    if (playerDomino) playItem.issueCard(playerDomino.dominoes, false, deskDominoesLen < 1);
                    if (playItem.isIssueTween) isIssueTween = playItem.isIssueTween;//TODO::只要有一个玩家发牌是tween动效，那nextMsg就要延时
                }
            }

            if (isIssueTween) { //TODO::这里的延时处理，如果时间设置过长，会导致消息队列过长，自动执行，可能跟这里的逻辑冲突，重复执行同一条消息,现在更改消息队列阻塞长度；降低时间
                Laya.timer.once(1000, this, () => {

                    if (this._firstPlayIdx > 1) {
                        this.filterPlayCard(this._firstPlayIdx);//TODO::如果ready消息先于DominoInfo，则会造成头像cd在播放，但是没有可以出的牌
                    }
                    this.handleNextPlayMsg();
                });
            } else {
                if (this._firstPlayIdx > 1) {
                    this.filterPlayCard(this._firstPlayIdx);//TODO::如果ready消息先于DominoInfo，则会造成头像cd在播放，但是没有可以出的牌
                }
                this.handleNextPlayMsg();
            }
        }

        /**
         * 显示桌牌
         */
        private updateDeskCard(): void {
            if (!this._room.deskPool) return;
            this._room.clearPlayCard();

            for (var k in this._playerItemHash) {
                if (this._playerItemHash[k]) {
                    var score = this._room.getPlayerScore(parseInt(k));
                    this._playerItemHash[k].updateScore(score);
                    this._playerItemHash[k].endAni();
                }
            }
            // this._room.deskDominoes = yalla.data.RoomService.instance.testList;
            var len = this._room.deskDominoes.length;

            if (len < 1) return;
            this._room.deskPool.initGameAddCard(this._room.deskDominoes);
            for (var i = 0; i < len; i++) {
                var d = this._room.deskDominoes[i];
                var outDominoData = this._room.deskPool.add(d);
                if (!outDominoData || outDominoData.id < 1) return;

                var outCardItem: OutCardItem = Laya.Pool.getItemByClass('outCardItem', OutCardItem);
                outCardItem.setCardData(outDominoData);
                outCardItem.cardSkinId = this._cardSkinId;
                outCardItem.pos(outDominoData.xx, outDominoData.yy).rotation = this.getRotation(outDominoData);
                outCardItem.visible = true;
                this.deskSp.addChild(outCardItem);

                if (this._room.gameType == 1 && this._room.deskPool.isUpdateAll) {
                    this._gameMode.cardList.push(outCardItem);
                    var cLen = this._gameMode.cardList.length;
                    for (var j = 0; j < cLen; j++) {
                        var item = this._gameMode.cardList[j];
                        item.pos(item.data.xx, item.data.yy).rotation = this.getRotation(item.data);
                    }
                    this._gameMode.checkDeskSp();

                } else {
                    this._gameMode.updateDeskCard(outCardItem, this._room.deskPool, d.deskSort);
                    outCardItem.pos(outDominoData.xx, outDominoData.yy);
                }
            }
        }

        /**
         * 更新玩家分数
         */
        private updatePlayerScoreAndAni(isEndAni: boolean = true): void {
            for (var k in this._playerItemHash) {
                if (this._playerItemHash[k]) {
                    var score = this._room.getPlayerScore(parseInt(k));
                    this._playerItemHash[k].updateScore(score);
                    isEndAni && this._playerItemHash[k].endAni();
                }
            }
        }

        /**
         * 发牌等操作
         * @param msg 
         */
        private handleGameOperate(msg: any): void {
            switch (msg.op) {
                case yalla.data.GameOperate.DOMINO_PLAY:
                    this.handlePlayMsg();
                    break;
                case yalla.data.GameOperate.DOMINO_PASS:
                    this.handlePass(msg);
                    break;
                case yalla.data.GameOperate.DOMINO_ADD_ROUNDTIME:
                    this._bottomView.handleAddRoundTime(msg);
                    break;
                case yalla.data.GameOperate.SYSTEM_TRUST:
                    yalla.Debug.log('=====domino 托管了======');
                    if (this._bottomView && !this._room.isTrust) {
                        this._room.isTrust = true;
                        this.updateTrust(true);
                    }
                    break;
                case yalla.data.GameOperate.SYSTEM_TRUST_CANCEL:
                    yalla.Debug.log('=====domino 取消托管了======');
                    if (this._bottomView && this._room.isTrust) {
                        this._room.isTrust = false;
                        this.updateTrust(false);
                    }

                    yalla.common.TipManager.instance.showTrustTip(0, this);
                    break;
            }
        }
        private onTrust(): void {
            this._bottomView && this._bottomView.onTrust();
        }

        /**
         * 金币更新
         * @param msg 
         */
        private handleUpdatePlayerCoin(msg: any = null): void {
            if (this._bottomView) this._bottomView.updateAddCount();
            yalla.common.InteractiveGift.Instance.updateMoney(yalla.data.UserService.instance.user.money, yalla.data.UserService.instance.user.gold);
        }

        /**
         * 玩家游戏状态
         */
        private changePlayerStatus(msg: any): void {
            var status = msg.ptStatus;
            switch (status) {
                case yalla.data.EnumPlayerTurnStatus.DOMINO_PLAY_START:
                    this.readyPlayCard(msg);
                    this.handleNextPlayMsg();
                    break;
                case yalla.data.EnumPlayerTurnStatus.DOMINO_PASS_SELF:
                    this.handlePass(msg);
                    break;
                default:
                    this.handleNextPlayMsg();
                    break;
            }
        }

        /**
         * 提示谁该出牌，准备出牌阶段
         * TODO::切换用户时，检测上一轮出牌是否正常，如果未显示的牌这里重新显示
         */
        private readyPlayCard(msg: any): void {
            if (!this._isGetAllDeskCard || !this._playerItemHash) return;

            // Debug.log(msg.idx + ":idx==readyPlayCard==activeIdx:" + this._room.activeIdx);
            //TODO::切后台，断线重连后，可能activeIdx为0 (Domino_Info消息会room.reset)，原先根据activeIdx判定CD动画是否停止会有问题，出现两个玩家同时播放
            //TODO::偶现偶现，有玩家牌手牌在出完牌后未更新，叠一起
            if (this._room.activeIdx > 0) {
                var prePlayItem: PlayerItem = this._playerItemHash[this._room.activeIdx];
                if (prePlayItem) {
                    prePlayItem.disableOpera(true);
                    if (prePlayItem.isInCD) prePlayItem.endAni();//TODO::断线重连后，可能同时存在两个玩家都是roleCD，需要结束上一个玩家cd
                }
            } else {
                for (var k in this._playerItemHash) {
                    var pItem: PlayerItem = this._playerItemHash[k];
                    if (pItem && pItem.data.fPlayerInfo.idx != msg.idx) {
                        pItem.checkCardPos();
                        pItem.disableOpera(true);
                        if (pItem.isInCD) pItem.endAni();
                    }
                }
            }
            //TODO::切换到其他玩家，如果是托管状态，需要把托管UI显示出来
            if (this._room.activeIdx == yalla.Global.Account.idx) {
                if (this._room.isTrust) {
                    this.updateTrust(true);
                }
            }

            this._room.activeIdx = msg.idx;
            if (this._room.deskPool && this._room.deskPool.cardHash) {
                var cardHash = this._room.deskPool.cardHash;
                for (var k in cardHash) {
                    cardHash[k].visible = true;
                }
            }

            if (this._firstPlayIdx < 1 && msg.idx == yalla.data.UserService.instance.user.idx) this._firstPlayIdx = msg.idx;
            // yalla.Debug.log("===不能出牌00==this._firstPlayIdx:" + this._firstPlayIdx); 
            this.filterPlayCard(msg.idx);
        }

        private filterPlayCard(idx: number) {
            if (!this._playerItemHash || !this._playerItemHash[idx]) return;
            var playItem: PlayerItem = this._playerItemHash[idx];
            var handCard = this._room.getPlayerAndDominoByIdx(idx);
            var list = handCard ? handCard.dominoes : [];
            // yalla.Debug.log(idx + ":idx===不能出牌11==handList.length:" + list.length);
            this._room.deskPool.filterPlayCardList(list);
            // if(idx == yalla.data.UserService.instance.user.idx) playItem.filterPlayCard(this._room.deskPool.waitPlayCardHash);
            playItem.filterPlayCard(this._room.deskPool.waitPlayCardHash);
            !playItem.isInCD && playItem.playAni();
        }

        /**
         * 加入出牌，补牌的消息队列
         * @param msg 
         */
        private handlePlayMsg(): void {
            var len = yalla.data.RoomService.instance.msgQueueList.length;
            if (len <= 1 || len >= 8) {
                if (len >= 8) this._isGetAllDeskCard = true;
                this.playMsg();
            }
        }

        private _prevMsg = null;
        private playMsg(): void {
            if (yalla.data.RoomService.instance.msgQueueList.length < 1) return;
            var msg = yalla.data.RoomService.instance.msgQueueList[0];
            if (msg == this._prevMsg) return;
            this._prevMsg = msg;

            var eventType = msg.event;
            if (msg.msgindex) {
                yalla.Global.ProtocolMsgIndex = msg.msgindex;
                if (yalla.Global.ProtocolMsgIndex >= yalla.Global.ProtoEnterRoomMsgIndex) yalla.Global.ProtoEnterRoomMsgIndex = 0;
            }
            if (eventType == yalla.data.GameEvent.DOMINO_INFO) {
                this.initDominoInfo();
            } else if (eventType == yalla.data.GameEvent.DOMINO_AUTO_PLAY || msg.op == yalla.data.GameOperate.DOMINO_PLAY) {
                this.playCard(msg);
            } else if (eventType == yalla.data.GameEvent.DOMINO_AUTO_FILL) {
                this.addHandCard(msg)
            } else if (eventType == yalla.data.GameEvent.GAME_PLAYER_STATUS_CHANGE) {
                this.changePlayerStatus(msg);
            } else if (eventType == yalla.data.GameEvent.DOMINO_ADD_ROUNDTIME) {
                this.handleRoundTime(msg);
            }
        }

        private handleNextPlayMsg(): void {
            if (yalla.data.RoomService.instance.msgQueueList.length > 0) yalla.data.RoomService.instance.msgQueueList.shift();
            if (yalla.data.RoomService.instance.msgQueueList.length < 1) yalla.data.RoomService.instance.addMsgInManunal();
            this.playMsg();
        }

        /**
         * 出牌
         * 筛选出的posList清空
         */
        private _isPlayCard: boolean = false;
        private playCard(msg: any): void {
            if (!this._isGetAllDeskCard || !this._playerItemHash || !this._room.deskPool || this._isPlayCard) return;
            if (msg && msg.autoPlayDomino && this._room.deskPool.cardHash[msg.autoPlayDomino.id]) {//避免之前tween动画出现bug 没有发起执行下一条消息事件；这里检测如果出现出牌id一样 直接执行下一条
                this.handleNextPlayMsg();
                return;
            }
            var cardSkinId = 0;
            var user = yalla.data.UserService.instance.user;
            if (user && user.playerShowInfo) cardSkinId = user.playerShowInfo.cardSkinId;
            //TODO::这里是皮肤对别人可见
            var playItem: PlayerItem = this._playerItemHash[msg.idx];
            // if (playItem && playItem.data) cardSkinId = playItem.data.cardSkinId;

            this._isPlayCard = true;

            this._room.clearPlayCard();
            var outDominoData = this._room.deskPool.add(msg.autoPlayDomino);
            if (!outDominoData || outDominoData.id < 1) return;
            var [rotation, outCardItem] = [this.getRotation(outDominoData), Laya.Pool.getItemByClass('outCardItem', OutCardItem)];
            this.deskSp.addChild(outCardItem);
            outCardItem.setCardData(outDominoData);
            outCardItem.cardSkinId = cardSkinId;
            outCardItem.pos(outDominoData.xx, outDominoData.yy).rotation = rotation;

            var beginPoint;
            if (playItem) {
                playItem.disableOpera();
                beginPoint = playItem.removeHandCard(msg.autoPlayDomino, yalla.Global.ProtoEnterRoomMsgIndex <= 0);
                yalla.Sound.playSound('play_card');
                this._tipView.showScore(msg.connectScore, playItem);
            }

            if (!beginPoint) beginPoint = new Laya.Point();
            if (this._room.deskPool.isUpdateAll) {
                this._isPlayCard = false;
                this._gameMode.hideHintItem();
                this._gameMode.cardList.push(outCardItem);

                var len = this._gameMode.cardList.length;
                for (var i = 0; i < len; i++) {
                    var item = this._gameMode.cardList[i];
                    item.pos(item.data.xx, item.data.yy);
                    item.rotation = this.getRotation(item.data);
                    item.visible = true;
                }
                this._gameMode.checkDeskSp();
                this.handleNextPlayMsg();//下一条消息

            } else {
                this._gameMode.updateDeskCard(outCardItem, this._room.deskPool, msg.deskSort);
                outCardItem.pos(outDominoData.xx, outDominoData.yy);
                if (yalla.Global.game_state != yalla.data.GameState.State_Sleep && yalla.Global.ProtoEnterRoomMsgIndex <= 0) {
                    //outCardItem  最终显示在桌面的牌b
                    outCardItem.visible = false;

                    //this._virtualOUtCardItem 飞出的牌
                    if (!this._virtualOutCardItem) {
                        this._virtualOutCardItem = new OutCardItem();
                        // this.addChildAt(this._virtualOUtCardItem, this._room.deskPool.deskSpIndex);
                        this.applyBox.addChild(this._virtualOutCardItem);
                    }
                    this._virtualOutCardItem.visible = true;
                    this._virtualOutCardItem.setCardData(outDominoData);
                    this._virtualOutCardItem.cardSkinId = cardSkinId;
                    this._virtualOutCardItem.pos(beginPoint.x, beginPoint.y).scale(1, 1);

                    var p = this.deskSp.localToGlobal(new Laya.Point(outDominoData.xx, outDominoData.yy));
                    var scale = this._room.deskPool.currentScale;
                    var goal = { x: p.x, y: p.y, scaleX: scale, scaleY: scale, rotation: rotation + 90 };
                    //移动时间
                    var moveTime = yalla.util.getDistance(beginPoint, p)['Distance'] / yalla.data.GameConfig.PlayCardSpeed;
                    if (moveTime < 300) moveTime = 300;
                    else if (moveTime > 600) moveTime = 600;

                    if (this._playCardItemTween) this._playCardItemTween.complete();
                    this._playCardItemTween = Laya.Tween.to(this._virtualOutCardItem, goal, moveTime, Laya.Ease.cubicOut, Laya.Handler.create(this, (oCardItem) => {
                        this._isPlayCard = false;
                        oCardItem.visible = true;
                        this._virtualOutCardItem.visible = false;
                        // Laya.Tween.clearAll(this._virtualOutCardItem)
                        this._playCardItemTween = null;

                        this.handleNextPlayMsg();
                    }, [outCardItem]));
                } else {
                    this._isPlayCard = false;
                    this.handleNextPlayMsg();
                }
            }

            if (this._room.gameType == 1) this.updatePlayerScoreAndAni(false);
            playItem && playItem.endAni();
        }

        private getRotation(dominoData: data.OutDominoData): number {
            if ((dominoData.showSide == yalla.data.DirType.LEFT || dominoData.showSide == yalla.data.DirType.RIGHT) && this._room.gameType == 1)
                if (this._room.deskPool.hWrapIDHash[dominoData.id] || this._room.deskPool.vWrapIDHash[dominoData.id])
                    return 0;
            return dominoData.showRotation;
        }

        /**
         * 选择手牌区的一张牌，筛选出可以出的位置
         * 如果可以放置的位置只有一个，那么在此点击此手牌，则发送出牌协议
         */
        private filterCardPos(d: Domino, isDouble: boolean): void {
            if (this._gameMode.cardList.length < 1) {
                var deskSort = d.tailNum != d.headNum ? yalla.data.DirType.LEFT : yalla.data.DirType.TOP;
                d.deskSort = deskSort;
                d.side = deskSort;
                yalla.data.RoomService.instance.sendPlayCard(d, deskSort);
                return;
            }
            if (this._room.deskPool.posList.length == 1) {
                if (this._room.selectCardId == d.id && isDouble) {
                    var posData = this._room.deskPool.posList[0];
                    yalla.data.RoomService.instance.sendPlayCard(d, posData.deskSort);
                    return;
                }
            }

            if (this._playerItemHash && this._room.selectCardId > 0 && this._room.selectCardId != d.id) {
                var playItem: PlayerItem = this._playerItemHash[yalla.data.UserService.instance.user.idx];
                playItem && playItem.recoverState(this._room.selectCardId);
            }
            this._room.selectCardId = d.id;
            this._room.deskPool.fileterCardPos(d);
            this._gameMode.fileterCardPos(this._room.deskPool.posList, d);
        }


        /**
         * 补牌  (十字模式，可能返回获得积分connectScore)
         * @param msg 
         */
        private addHandCard(msg: any): void {
            if (!this._isGetAllDeskCard || !this._playerItemHash) return;
            if (this.checkFillCardSame(msg)) {//避免之前tween动画出现bug 没有发起执行下一条消息事件；这里检测如果出现补牌数据一样列表 直接执行下一条
                this.handleNextPlayMsg();
                return;
            }
            var autoFillDominoList: Array<Domino> = msg.autoFillDomino;
            this._topView.updateFillNum(autoFillDominoList.length);
            var playItem: PlayerItem = this._playerItemHash[msg.idx];
            playItem && playItem.issueCard(autoFillDominoList, true, yalla.Global.ProtoEnterRoomMsgIndex <= 0);
        }
        /**
         * 检测补牌是否存在重复牌
         * @param msg 
         */
        private checkFillCardSame(msg: any): boolean {
            var autoFillDominoList: Array<Domino> = msg.autoFillDomino;
            if (this._room.deskPool.existSameCard(autoFillDominoList)) return true;

            if (this._playerItemHash && this._playerItemHash[msg.idx]) {
                var myCardListItem = this._playerItemHash[msg.idx].myCardItemList;
                for (var k in autoFillDominoList) {
                    for (var n in myCardListItem) {
                        if (myCardListItem[n].data.id == autoFillDominoList[k].id) return true;
                    }
                }
            }
            return false;
        }

        /**
         * 是否显示pass
         */
        private handlePass(msg: any = null): void {
            if (msg && msg.idx && this._playerItemHash) {
                var playItem: PlayerItem = this._playerItemHash[msg.idx];
                playItem && this._tipView.showPass(playItem);
                yalla.Sound.playSound("pass");
            }
        }

        /**
         * 聊天返回
         */
        private handleChat(msg: any): void {
            var user = yalla.data.UserService.instance.user;
            if (yalla.Global.IsGameOver || (!msg || (!msg.msg && !msg.extension))) return;
            if (yalla.Mute.muted(msg.idx) || msg.idx == user.idx || (user.playerShowInfo && user.playerShowInfo.isInChatOff)) return;
            if (yalla.Mute.muted(msg.idx) || (user.playerShowInfo && user.playerShowInfo.isInChatOff)) return;
            if (yalla.Global.Account.isPrivate == 1 && !yalla.Mute.isShowMsgByLimit(msg)) return;

            if (!this._playerItemHash) return;
            var playItem: PlayerItem = this._playerItemHash[msg.idx];
            msg.msg = msg.msg;
            if (playItem) {
                playItem.showChatBox(msg);
                this._bottomView && this._bottomView.onChat(msg, playItem.data, yalla.data.UserService.instance.user.idx);
            }
        }

        /**
         * 自己发送消息，不等返回先显示
         * 需要屏蔽敏感词
         */
        private handleChatMy(msg: any): void {
            if (yalla.Global.IsGameOver || (!msg || (!msg.msg && !msg.extension)) || !this._playerItemHash) return;
            msg['idx'] = yalla.data.UserService.instance.user.idx;
            var playItem: PlayerItem = this._playerItemHash[msg.idx];
            if (playItem) {
                playItem.showChatBox(msg);
                this._bottomView && this._bottomView.onChat(msg, playItem.data, msg.idx);
            }
        }

        /**
         * 调用语音
         */
        private handleSpeakResponse(data: Array<number>): void {
            if (yalla.Global.game_state == yalla.data.GameState.State_Sleep || !this._playerItemHash) return;
            var dLen = data.length;
            for (var i = 0; i < dLen; i++) {
                var playItem: yalla.view.game.PlayerItem = this._playerItemHash[data[i]];
                playItem && playItem.playVoice();
            }
        }
        /**
         * 红点消息
         * @param msg {num:1}
         */
        private readMsgNum(msg: any): void {
            this._bottomView && this._bottomView.readMsgNum(msg);
        }

        private onClickGameTopView(msg: any): void {
            this._bottomView && this._bottomView.closeFriendChat();
        }

        /**
         * 加入语音频道成功,
         *针对zego 需要延迟执行mute unmute
         */
        public handleJoinAgora(joinSuc = true): void {
            if (joinSuc) this._bottomView && this._bottomView.handleJoinAgora();
            // this.timer.once(1000, this, () => {
                for (var k in this._playerItemHash) {
                    var playerItem: PlayerItem = this._playerItemHash[k];
                    if (playerItem) {
                        var idx = Number(k);
                        var isMute = yalla.Mute.muted(idx);
                        //如果已经被屏蔽的玩家，需要禁言
                        if (isMute) {
                            yalla.Voice.instance.muteRemoteAudioStream(idx, true, (val,uid) => {
                                if (val == 0) {
                                    if (this._playerItemHash) {
                                        var cbPlayer:PlayerItem = this._playerItemHash[uid];
                                        if (cbPlayer && cbPlayer.roleItem) {
                                            var gameHead = cbPlayer.roleItem.playerGameHead;
                                            if (gameHead) {
                                                gameHead.mute();
                                            }
                                        }
                                    }
                                } else {
                                    yalla.Voice.instance.muteRemoteAudioStream(uid, false, (val, uid) => {
                                        if (val == 0) {
                                            yalla.Mute.remove(uid);
                                        }
                                        yalla.Debug.log('屏蔽别人失败后，取消屏蔽 updateMuteVoice():' + val);
                                    });
                                }
                            })
                        } else {
                            if (playerItem.roleItem) {
                                var gameHead = playerItem.roleItem.playerGameHead;
                                if (gameHead) {
                                    gameHead.updateVoiceState();
                                }
                            }
                        }
                    }
                }
            // });
        }

        /**
         * 打开音频
         */
        private handleEnableAudio(idx): void {
            if (!this._playerItemHash || !this._playerItemHash[idx]) return;
            this._playerItemHash[idx].enableAudio();
        }
        /**
         * 关闭音频
         */
        private handleDisableAudio(idx): void {
            if (!this._playerItemHash || !this._playerItemHash[idx]) return;
            this._playerItemHash[idx].disableAudio();
        }

        /**
         * 延长cd时长
         */
        private handleRoundTime(msg: any): void {
            if (!this._playerItemHash) return;
            var playItem: PlayerItem = this._playerItemHash[msg.idx];
            if (playItem) {
                playItem.updateCDAni();
                this._tipView && this._tipView.showRoundTime(playItem);
            }
            this.handleNextPlayMsg();//执行下一条消息
        }

        private handleChatOff(msg: any): void {
            if (this._topView && this._topView.setting) this._topView.setting.updateChatSwitch();
            if (!this._playerItemHash || !this._playerItemHash[msg.idx]) return;
            this._playerItemHash[msg.idx].updateChatOff(msg.num);
        }

        /**
         * 游戏结束status=4 & 单局结束status=5
         */
        private gameOver(msg: any): void {
            if (msg.msgindex) {
                yalla.Global.ProtocolMsgIndex = msg.msgindex;
                yalla.Global.ProtoEnterRoomMsgIndex = 0;
            }
            this._isPlayCard = false;
            this._firstPlayIdx = 0;
            var [status, isPrivate] = [msg.status, yalla.data.RoomService.instance.room.isPrivate];
            if (status == yalla.data.GameStatus.GAME_RESULT) {
                yalla.Debug.log("=====游戏结束监听Game.gameover=====");
                yalla.Debug.log(msg);
                //停止背景音乐
                yalla.Sound.stopMusic('bgm_domino');
                yalla.common.TipManager.instance.hideTrustTip();

                var isWin = (msg.gResut.winnerId == yalla.data.UserService.instance.user.idx);
                var resultSound = isWin ? 'cheer' : 'domino_fail';

                Laya.timer.clear(this, this.forceQuit);
                yalla.common.ChatManager.instance.clearChatMsgAni();
                yalla.Native.instance.mobClickEvent(buryPoint.DOMINO_SETTLE);
                yalla.util.sendExitType(yalla.data.ExitType.GAMEOVER_QUIT);

                // yalla.Sound.playSound(resultSound, Laya.Handler.create(this, () => { yalla.data.VoiceService.instance.levelGameRoom(); }));
                
                //TODO::*******
                yalla.data.VoiceService.instance.gameOverSound(resultSound);

                yalla.util.closeAllDialog();
                yalla.Native.instance.closeAllNativeView();
                // if(yalla.Global.backTime < 1) yalla.Global.backTime = yalla.System.getNowTime();
                // var dTime = yalla.System.getNowTime() - yalla.Global.backTime;
                // if (yalla.Global.game_state == yalla.data.GameState.State_Sleep && dTime > 60000) {//TODO::切到后台超过1分钟，不弹结算，直接返回大厅或者锦标赛（ps：锦标赛类型游戏会有问题，弹出结算后只要不操作不会主动返回, 这里就不改了）
                //     if (isPrivate == GameRoomType.CHAMPION) yalla.Native.instance.event(yalla.Native.instance.Event.BACKCHAMPION, [msg.gResut]);
                //     else yalla.data.UserService.instance.backHall();

                // } else {
                if (!this._resultView) {
                    this._resultView = new yalla.view.result.Result(msg, () => {//返回锦标赛界面
                        if (isPrivate == GameRoomType.CHAMPION) yalla.Native.instance.event(yalla.Native.instance.Event.BACKCHAMPION, [msg.gResut]);
                        else {
                            yalla.data.UserService.instance.backHall(false, 500);
                            this.gameOverBack();
                        }
                    }, () => {
                        if (isPrivate == GameRoomType.VIP) this.share();
                        else {
                            this.playAgain();
                            this.gameOverBack();
                        }
                    });
                    this._resultView.name = 'resultView';
                }
                if (this._resultView && !this._resultView.displayedInStage) Laya.stage.addChild(this._resultView);
                // }

                if (isPrivate == GameRoomType.CHAMPION) {
                    if (yalla.util.IsBrowser) yalla.net.Client.instance.clear();//避免已经结算了，但是长时间未操作，已经还是收到game is finished
                    else yalla.net.NativeClient.instance.clear();
                }

                //TODO::清理roundresult
                if (this._roundResultView) {
                    this._roundResultView.clear();
                    this._roundResultView = null;
                }

            } else if (status == yalla.data.GameStatus.GAME_ROUNT_RESULT) {
                this.showRoundView(msg);
                yalla.Native.instance.closeKeyBoard();
                yalla.Native.instance.removeGameRulesView();
            }
            this._bottomView.clearInputTxt();
            this._bottomView.hide();
            
            this.updatePlayerScoreAndAni();
        }

        /**
         * 返回准备界面
         */
        private onClickBack(): void {
            yalla.Native.instance.isBack = true;
            var room = yalla.data.RoomService.instance.room;
            // var str = room.isPrivate == GameRoomType.PRIVATE ? yalla.data.TranslationD.Game_Quit : yalla.data.TranslationD.Game_Quit_Lose;
            var str = yalla.data.TranslationD.Game_Quit;
            if (room.isPrivate != GameRoomType.PRIVATE) {
                if (room.gameBeginTime + room.quitPunishTime > new Date().getTime() / 1000) {
                    str = yalla.data.TranslationD.Game_Quit_Punish;
                } else {
                    str = yalla.data.TranslationD.Game_Quit_Lose;
                }
            }
            yalla.common.Confirm.instance.isExit = true;
            yalla.common.Confirm.instance.showConfirm(str,
                Laya.Handler.create(this, () => {
                    yalla.Native.instance.mobClickEvent(buryPoint.DGAME_EXIT_CONFIRM);
                    yalla.data.RoomService.instance.quitRoom();
                    yalla.util.sendExitType(yalla.data.ExitType.INITIATIVE_QUIT);
                    var time = room.isPrivate == GameRoomType.CHAMPION ? 4000 : 2000; //TODO::quitRoom没返回，2s后回到锦标赛，但锦标赛获取信息还在游戏中，又跳转游戏情况,so延长未响应时间
                    Laya.timer.once(time, this, this.forceQuit, [room.isPrivate]);
                }),
                Laya.Handler.create(this, () => {
                    yalla.Native.instance.mobClickEvent(buryPoint.DGAME_EXIT_CANCEL);
                    yalla.common.Confirm.instance.hideConfirm();
                }), ['Confirm', 'Cancel']);
        }

        /**
         * 主动退出游戏，quitRoom未响应，则强制退出
         * 但是需要再结算界面弹出时候把这个计时器清掉
         */
        private forceQuit(isPrivate: number): void {
            if (isPrivate != GameRoomType.CHAMPION) {
                yalla.data.UserService.instance.backHall(true, 500);
            } else {
                if (!this._resultView) yalla.Native.instance.event(yalla.Native.instance.Event.BACKCHAMPION, [{ winnerId: -1 }]);
            }
        }

        /**
         * 游戏再来一局   TODO::backHall 返回大厅事件不抛，等在下次再进入时销毁
         */
        private playAgain(): void {
            // yalla.data.VoiceService.instance.levelGameRoom();
            this.leaveVoiceRoom();
            yalla.util.sendExitType(yalla.data.ExitType.PLAY_AGAIN);
            yalla.data.RoomService.instance.clear();

            yalla.util.clogPlayAgain(this._room.gameType);

            var p: playAgainArg = {
                gamePay: this._room.cost,
                gameId: yalla.Global.gameType,
                gameType: this._room.gameType,
                playerNum: this._room.playerNums,
                roomId: this._room.showId,
                gameGroup: 0,
                isPrivate: yalla.Global.Account.isPrivate
            };
            yalla.Native.instance.playAgain(p);
        }

        public share() {
            // yalla.data.RoomService.instance.quitRoom();
            yalla.util.clogShare(this._room.gameType);
            this.leaveVoiceRoom();
            // yalla.data.VoiceService.instance.levelGameRoom();
            yalla.Native.instance.shortScreenNative();
            yalla.Native.instance.on(yalla.Native.instance.Event.SHARE, this, this.shareResponse);
        }
        private shareResponse(): void {
            yalla.data.UserService.instance.backHall();
            yalla.Native.instance.off(yalla.Native.instance.Event.SHARE, this, this.shareResponse);
        }
        leaveVoiceRoom() {
        //TODO::******* android playagai 等先退出语音房，音playsound 没有回掉（声网）
        // yalla.data.VoiceService.instance.levelGameRoom();
        if (yalla.Native.instance.deviceType == DeviceType.Android) {
            yalla.data.VoiceService.instance.levelGameRoom();
        }
    }

        /**
         * 主动发起退出房间的人才返回ready界面，否则等到gameevent=5结束游戏
         * @param msgup
         */
        private handleQuitGame(msg: any): void {
            if (msg.idx == yalla.data.UserService.instance.user.idx) {
                if (yalla.data.RoomService.instance.room.isPrivate != GameRoomType.CHAMPION) {
                    yalla.data.UserService.instance.backHall(true, 500);
                }
            }
        }

        private handleUpdateGiftCnf(msg: any): void {
            var posHash = {};
            yalla.common.InteractiveGift.Instance.resData = msg;
            for (var k in this._playerItemHash) {
                var playerItem: PlayerItem = this._playerItemHash[k];
                if (playerItem) {
                    var facePos = playerItem.playerUI['face'].localToGlobal(new Laya.Point());//playerItem.ui.localToGlobal(playerItem.playerUI['face']);//
                    posHash['player_' + k] = { x: facePos.x, y: facePos.y };
                    if (playerItem.data.giftId > 0) {
                        var giftData = yalla.common.InteractiveGift.Instance.getGiftItemData(playerItem.data.giftId);
                        if (giftData) playerItem.showGift(giftData.id, giftData.icon);
                    }
                    // playerItem.btnGift.visible = true;
                    playerItem.btnGift.visible = !this._isSpectator;
                }
            }
            // yalla.common.InteractiveGift.Instance.handleUpdateGiftCnf(msg, posHash, this.parent);
            yalla.common.InteractiveGift.Instance.handleUpdateGiftCnf(msg, posHash, this.giftBox);
        }

        private handleRevGift(msg: any): void {
            var msgList = yalla.common.InteractiveGift.Instance.msgList;
            // yalla.Debug.log('--domino handleRevGift--msgList.length:' + msgList.length);
            if (msgList.length <= 1) yalla.common.InteractiveGift.Instance.handleRevGift(msg);
        }
        private updateGiftIcon(sendData: any): void {
            if (this._playerItemHash) {
                var playItem: PlayerItem = this._playerItemHash[sendData.recIdx];
                if (!playItem) return;
                playItem.showGift(sendData.giftId, sendData.icon);
            }
        }

        private updateTrust(isTrust: boolean) {
            var idx = yalla.Global.Account.idx;
            if (isTrust) {
                this._bottomView && this._bottomView.showTrust();
                if (this._playerItemHash && this._playerItemHash[idx]) {
                    var playItem: PlayerItem = this._playerItemHash[idx];
                    playItem.roleItem.playerGameHead.trustee = true;
                    playItem.roleItem.hideName();
                    playItem.playerUI['face'].on("click", this, this.onTrust);
                    playItem.playerUI['roleSp'].on("click", this, this.onTrust);
                }
                yalla.common.TipManager.instance.showTrustTip(1, this);

            } else {
                this._bottomView && this._bottomView.hideTrust(true);
                if (this._playerItemHash && this._playerItemHash[idx]) {
                    var playItem: PlayerItem = this._playerItemHash[idx];
                    playItem.roleItem.playerGameHead.trustee = false;
                    playItem.roleItem.showName();
                    playItem.playerUI['face'].off("click", this, this.onTrust);
                    playItem.playerUI['roleSp'].off("click", this, this.onTrust);
                }

            }
        }

        /**
         * 从后台返回
         */
        public show(): void {
            for (var k in this._playerItemHash) {
                var playItem: PlayerItem = this._playerItemHash[k];
                if (playItem) {
                    playItem.hide();
                    playItem.checkCardPos();
                }
            }
            if (this._playersView && this._playersView._view) {
                var trust_jump = this._playersView._view['trust_jump'];
                if (trust_jump && trust_jump.visible && !trust_jump.isPlaying) {
                    trust_jump.play(0, true);
                }
            }
        }

        public hide(): void {
            if (this._playCardItemTween) this._playCardItemTween.complete();
            for (var k in this._playerItemHash) {
                var playItem: PlayerItem = this._playerItemHash[k];
                if (playItem) {
                    playItem.stopAni(); //TODO::改本地时间的话，如果Animation不停止 会卡主闪退
                }
            }
        }

        private showRoundView(msg: any): void {
            if (!this._roundResultView) this._roundResultView = new yalla.view.result.RoundResult(this._room.gameType);
            this._roundResultView.setData(msg.roundResut, this._cardSkinId);
            this._roundResultView.updateNextTime(190, this._room.gameRoundTime);
            if (!this._roundResultView.ui.parent) Laya.stage.addChild(this._roundResultView.ui);
            yalla.Sound.playSound('round_end');
        }

        private closeRoundView(isClose: boolean = false): void {
            this.resetDominoInfo();
            if (this._roundResultView) {
                this._roundResultView.close();
            }
        }

        private onClickGameUI(e: Laya.Event): void {
            this._bottomView && this._bottomView.onClickGameUI();
            this._topView && this._topView.onClickGameUI();
            yalla.common.InteractiveGift.Instance.hideGiftView();
        }
        /**购买钻石礼包回掉 */
        private onAssetsChange(data) {
            if (data) {
                if (data.hasOwnProperty('diamond')) {
                    yalla.data.UserService.instance.user.updateCoin({ type: 1, value: parseInt(data.diamond || 0) });
                }
                if (data.hasOwnProperty('money')) {
                    yalla.data.UserService.instance.user.updateCoin({ type: 0, value: parseInt(data.money || 0) });
                }
                this.handleUpdatePlayerCoin();
            }
        }
        private openAssetsView() {
            this._topView && this._topView.showSameStore();
        }

        private removeEvent(): void {
            Laya.timer.clear(this, this.frameLoopName);
            this.off(Laya.Event.CLICK, this, this.onClickGameUI);

            yalla.event.YallaEvent.instance.off(yalla.data.EnumCustomizeCmd.Dominose_Chat_My, this, this.handleChatMy);
            yalla.event.YallaEvent.instance.off(yalla.data.EnumCustomizeCmd.Dominose_Back, this, this.onClickBack);
            yalla.event.YallaEvent.instance.off(yalla.data.EnumCustomizeCmd.Dominose_PlayCardNext, this, this.handleNextPlayMsg);
            yalla.event.YallaEvent.instance.off(yalla.data.EnumCustomizeCmd.Dominose_MsgInManunal, this, this.playMsg);
            yalla.event.YallaEvent.instance.off(yalla.data.EnumCustomizeCmd.Dominose_ChatOFF_My, this, this.handleChatOff);
            yalla.common.InteractiveGiftAnimation.Instance.off(yalla.common.InteractiveGiftAnimation.Instance.Event.UpdateGiftIcon, this, this.updateGiftIcon);

            yalla.event.YallaEvent.instance.off(yalla.data.EnumCmd.Dominose_Chat, this, this.handleChat);
            yalla.event.YallaEvent.instance.off(yalla.data.EnumCmd.Dominose_QuitRoom, this, this.handleQuitGame);
            yalla.event.YallaEvent.instance.off(yalla.data.EnumCmd.Dominose_GameEvent, this, this.handleStartGame);
            yalla.event.YallaEvent.instance.off(yalla.data.EnumCmd.Dominose_EnterRoom, this, this.handleEnterRoom);
            yalla.event.YallaEvent.instance.off(yalla.data.EnumCmd.Dominose_GAME_OPERATE, this, this.handleGameOperate);
            yalla.event.YallaEvent.instance.off(yalla.data.EnumCmd.Dominose_Update_Player_Coin, this, this.handleUpdatePlayerCoin);
            yalla.event.YallaEvent.instance.off(yalla.data.EnumCmd.Dominose_Gift_Cnf_List, this, this.handleUpdateGiftCnf);
            yalla.event.YallaEvent.instance.off(yalla.data.EnumCmd.Dominose_Gift_Send, this, this.handleRevGift);

            yalla.data.VoiceService.instance.off(yalla.data.EnumCustomizeCmd.Dominose_JoinAgora, this, this.handleJoinAgora);
            yalla.data.VoiceService.instance.off(yalla.data.EnumCustomizeCmd.Dominose_EnableAudio, this, this.handleEnableAudio);
            yalla.data.VoiceService.instance.off(yalla.data.EnumCustomizeCmd.Dominose_DisableAudio, this, this.handleDisableAudio);
            yalla.Native.instance.off(yalla.Native.instance.Event.SPEAKER, this, this.handleSpeakResponse);
            yalla.Native.instance.off(yalla.Native.instance.Event.SHARE, this, this.shareResponse);
            yalla.Native.instance.off(yalla.Native.instance.Event.READMSGNUM, this, this.readMsgNum);
            yalla.event.YallaEvent.instance.off('click_gameTopview', this, this.onClickGameTopView);
            yalla.Native.instance.off(yalla.Native.instance.Event.ONASSETSCHANGE, this, this.onAssetsChange);
            yalla.Native.instance.off(yalla.Native.instance.Event.OPEN_ASSETSVIEW, this, this.openAssetsView);
            // yalla.Native.instance.off(yalla.Native.instance.Event.CLOSEFRIENDCHAT, this, this.closeFriendChat);
            Laya.timer.clearAll(this);
            this.offAll();
        }

        /**
         * ******* 只有ios 结算的back playAgain 才先关闭界面，避免下一次进入游戏有上一局的界面
         */
        public gameOverBack() {
            if (yalla.Native.instance.deviceType == DeviceType.IOS) {
                this.backHallClear();
            }
        }

        public backHallClear(): void {
            yalla.Debug.log("=====domino Game  清理======" + !!this._resultView);
            yalla.Sound.playMusic('bgm_domino', 2);
            this.removeEvent();
            if (this._roundResultView) {
                this._roundResultView.clear();
            }
            for (var k in this._playerItemHash) {
                this._playerItemHash[k] && this._playerItemHash[k].clear();
            }
            this._virtualOutCardItem && this._virtualOutCardItem.destroy();
            this._gameMode && this._gameMode.clear();
            this._topView && this._topView.clear();
            this._tipView && this._tipView.clear();
            this._bottomView && this._bottomView.clear();
            this._resultView && this._resultView.clear();

            this._prevMsg = null;
            this._cardSkinId = 0;
            this._firstPlayIdx = 0;
            this._uiList = null;
            this._playCardItemTween = null;
            this._playerItemHash = null;
            this._roundResultView = null;
            this._gameMode = null;
            this._topView = null;
            this._tipView = null;
            this._roundTimeView = null;
            this._bottomView = null;
            this._resultView = null;

            this.applyBox.removeSelf();
            this.deskSp.removeSelf();
            this.applyBox.destroy(true);
            this.deskSp.destroy(true);
        }

        public clear(isRemove: boolean = false): void {
            this.backHallClear();
            this.removeSelf();
            this.destroy(true);
        }
    }
}