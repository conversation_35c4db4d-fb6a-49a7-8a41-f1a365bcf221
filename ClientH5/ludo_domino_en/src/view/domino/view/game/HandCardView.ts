module yalla.view.game {
    /**
     * 玩家手牌区域
     */
    export class HandCardView {
        private _ui: Laya.Box;
        private _cardListUI: any;
        private _myCardList: Array<CardItem> = [];
        private _otherCardList: Array<OtherCardItem> = [];
        private _fillCardList: Array<CardItem> = [];

        private _issueTime: number = 60;
        private _applyTime: number = 150;
        private _playCardFunc: Function;

        public indexType = 0;   //0下  1右  2上  3左
        public cardListUIWid: number = 0;
        public myCardWid: number = 85;
        public otherCardWid: number = 44;

        public myCardDis: number = 12;
        public otherCardDis: number = 2;
        public isIssueTween: boolean = false;

        public spectatorCardWid: number = 63;
        private _isSpectator: boolean = false;
        private _spectatorCardY: number = -11;

        public data: playerShowInfo = null;

        public applyUIBox: Laya.Box = null;

        constructor(ui: Laya.Box, applyBox:Laya.Box, cardViewType: number, playCardFunc: Function, _isSpectator: boolean) {
            this._isSpectator = _isSpectator;
            this._ui = ui;
            this.applyUIBox = applyBox;
            this.indexType = cardViewType;
            this._playCardFunc = playCardFunc;
            this._cardListUI = this._ui.getChildByName('cardList');
            this.cardListUIWid = this._cardListUI.width;
        }

        /**
         * 发牌
         * @param arr 
         */
        public issueCard(arr: Array<Domino>, isApply: boolean = false, playAni: boolean): void {
            var cardSkinId = 0;
            if (this.data) {
                cardSkinId = this.data.cardSkinId;
            }

            switch (this.indexType) {
                case 1:
                case 2:
                case 3:
                    if (isApply) this.applyOtherCard(arr, cardSkinId);
                    else this.issueOtherCard(arr, playAni, cardSkinId);
                    break;
                case 0:
                    if (isApply) this.applyMyCard(arr, cardSkinId);
                    else this.issueMyCard(arr, playAni, cardSkinId);
                    break;
            }
        }
        public getCardWidth(width: number) {
            return !this._isSpectator ? width : this.spectatorCardWid;
        }
        public getCardDis(dis:number) {
            return this._isSpectator ? this.myCardDis : dis;
        }
        // /**
        //  * 发牌(自己)
        //  */
        // public issueMyCard(arr: Array<Domino>, playAni: boolean, cardSkinId: number): void {
        //     var len = arr.length;
        //     if (len < 1) return;
        //     this.isIssueTween = false;
        //     var cardWidth = this.getCardWidth(this.myCardWid);
        //     var list = this.getHandView(cardWidth, len, this.getCardDis(this.myCardDis));
        //     var [dis, bX, bY] = [list[0], list[1], this._isSpectator ? this._spectatorCardY : 6];
        //     var beginX = -cardWidth - yalla.Screen.screen_top_width;
        //     if (this._isSpectator) beginX = 0;
        //     var isWake = yalla.Global.game_state != yalla.data.GameState.State_Sleep;
        //     for (var i = 0; i < len; i++) {
        //         // var cardItem = Laya.Pool.getItemByClass('cardItem', yalla.view.game.CardItem);
        //         var cardItem = this.getCardItem(true);
        //         cardItem.pos(beginX, bY);
        //         cardItem.setCardData(arr[i], this._playCardFunc);
        //         cardItem.cardSkinId = cardSkinId;
        //         this._myCardList.push(cardItem);
        //         this._cardListUI.addChild(cardItem);

        //         var xx = bX + (cardWidth + dis) * i;
        //         if (isWake && playAni) {
        //             this.isIssueTween = true;
        //             var tw = Laya.Tween.to(cardItem, { x: xx, y: bY }, 300, Laya.Ease.cubicOut, Laya.Handler.create(this, (cardItem, index) => {
        //                 Laya.Tween.clearAll(cardItem);
        //                 if (index >= len - 1) {
        //                     // yalla.Debug.log("==disableOpera 00==");
        //                     this.disableOpera();
        //                 }
        //             }, [cardItem, i]), i * this._issueTime, true);
        //         } else {
        //             cardItem.pos(xx, bY);
        //             if (i >= len - 1) {
        //                 // yalla.Debug.log("==disableOpera 11==");
        //                 this.disableOpera();
        //             }
        //         }
        //     }
        // }

        public issueMyCard(arr: Array<Domino>, playAni: boolean, cardSkinId: number): void {
            var len = arr.length;
            if (len < 1) return;

            // setTimeout(() => {
            //     yalla.common.TipManager.instance.showTip(yalla.data.TranslationD.Game_Tip_Check_Microphone, {}, 1000, 0);
            //     yalla.common.TipManager.instance.showTip(yalla.data.TranslationD.Game_Tip_Check_Microphone, {}, 2000, 0);
            //     yalla.common.TipManager.instance.showTip(yalla.data.TranslationD.Game_Tip_Check_Microphone, {}, 3000, 0);
            // },500)

            // 性能优化：批量处理前先清理之前的动画
            this.clearPreviousAnimations();

            this.isIssueTween = false;
            var cardWidth = this.getCardWidth(this.myCardWid);
            var list = this.getHandView(cardWidth, len, this.getCardDis(this.myCardDis));
            var [dis, bX, bY] = [list[0], list[1], this._isSpectator ? this._spectatorCardY : 6];
            var beginX = -cardWidth - yalla.Screen.screen_top_width;
            if (this._isSpectator) beginX = 0;
            var isWake = yalla.Global.game_state != yalla.data.GameState.State_Sleep;

            // 性能优化：先计算所有位置，避免循环中重复计算
            var targetPositions = [];
            for (var i = 0; i < len; i++) {
                targetPositions[i] = bX + (cardWidth + dis) * i;
            }

            // 性能优化：批量创建卡牌
            var cardItems = [];
            for (var i = 0; i < len; i++) {
                var cardItem = this.getCardItem(true);
                cardItem.pos(beginX, bY);
                cardItem.setCardData(arr[i], this._playCardFunc);
                cardItem.cardSkinId = cardSkinId;
                cardItems.push(cardItem);
                this._myCardList.push(cardItem);
            }

            // 批量添加到UI，减少重绘次数
            for (var i = 0; i < cardItems.length; i++) {
                this._cardListUI.addChild(cardItems[i]);
            }

            if (isWake && playAni) {
                this.playIssueAnimation(cardItems, targetPositions, bY, len);
            } else {
                this.setImmediatePositions(cardItems, targetPositions, bY, len);
            }
        }

        /**
         * 清理之前的动画，防止动画冲突
         */
        clearPreviousAnimations() {
            if (this._myCardList && this._myCardList.length > 0) {
                for (var i = 0; i < this._myCardList.length; i++) {
                    var card = this._myCardList[i];
                    if (card) {
                        Laya.Tween.clearAll(card);
                    }
                }
            }
        }

        /**
         * 播放发牌动画 - 优化版本
         */
        playIssueAnimation(cardItems, targetPositions, bY, len) {
            this.isIssueTween = true;
            var completedCount = 0;

            // 优化：使用更合理的延迟时间，避免过于密集的动画
            var optimizedIssueTime = Math.max(this._issueTime || 80, 50); // 最小50ms间隔
            var animationDuration = Math.min(300, 150 + len * 10); // 根据卡牌数量调整动画时长

            for (var i = 0; i < cardItems.length; i++) {
                var cardItem = cardItems[i];
                var targetX = targetPositions[i];
                var delay = i * optimizedIssueTime + 300;

                // 使用更平滑的缓动函数
                Laya.Tween.to(cardItem,
                    { x: targetX, y: bY },
                    animationDuration,
                    Laya.Ease.cubicOut, // 更平滑的缓动
                    Laya.Handler.create(this, this.onCardAnimationComplete, [++completedCount, len]),
                    delay,
                    true
                );
            }
        }

        /**
         * 卡牌动画完成回调 - 优化版本
         */
        onCardAnimationComplete(completedCount, totalCount) {
            if (completedCount >= totalCount) {
                this.isIssueTween = false;
                // yalla.Debug.log("==disableOpera 动画完成==");
                this.disableOpera();
            }
        }

        /**
         * 立即设置位置（无动画）
         */
        setImmediatePositions(cardItems, targetPositions, bY, len) {
            for (var i = 0; i < cardItems.length; i++) {
                cardItems[i].pos(targetPositions[i], bY);
            }
            // yalla.Debug.log("==disableOpera 立即完成==");
            this.disableOpera();
        }

        /**
         * 补牌(自己)
         */
        public applyMyCard(arr: Array<Domino>, cardSkinId: number): void {
            var len = arr.length;
            if (len < 1) return;
            if (!this._fillCardList) this._fillCardList = [];
            this.isIssueTween = false;
            var isWake = yalla.Global.game_state != yalla.data.GameState.State_Sleep;
            if (len == 1) yalla.Sound.playSound('fill_card');
            var beginX: number = yalla.data.RoomService.instance.room.deskPool.applyCardX;
            var beginY: number = yalla.data.RoomService.instance.room.deskPool.applyCardY;

            var myCardNum = this._myCardList.length;
            var cardWidth = this.getCardWidth(this.myCardWid)
            var list = this.getHandView(cardWidth, myCardNum + len, this.getCardDis(this.myCardDis));
            var [dis, bX, bY, scale] = [list[0], list[1], 6, 1];
            if (this._isSpectator) {
                bY = this._spectatorCardY;
                scale = 0.66;
            }
            var endX: number = bX;
            for (var i = 0; i < myCardNum; i++) {
                var cdItem = this._myCardList[i];
                cdItem.x = bX + (cardWidth + dis) * i;
            }
            if (this._myCardList[myCardNum - 1]) endX = this._myCardList[myCardNum - 1].x;
            for (var i = 0; i < len; i++) {
                // var cardItem = Laya.Pool.getItemByClass('cardItem', yalla.view.game.CardItem);
                var cardItem = this.getCardItem(true)
                var xx = endX + (cardWidth + dis) * (i + 1);
                cardItem.pos(xx, bY);
                cardItem.setCardData(arr[i], this._playCardFunc);
                cardItem.cardSkinId = cardSkinId;
                this._cardListUI.addChild(cardItem);
                this._myCardList.push(cardItem);

                if (isWake) {
                    this.isIssueTween = true;
                    var virtualCardItem = Laya.Pool.getItemByClass('vcardItem', yalla.view.game.CardItem);
                    virtualCardItem.setCardData(arr[i], this._playCardFunc);
                    virtualCardItem.cardSkinId = cardSkinId;
                    virtualCardItem.pos(beginX, beginY).scale(0.01, 0.01);
                    if (this.applyUIBox) {
                        this.applyUIBox.addChild(virtualCardItem);
                        this._fillCardList.push(virtualCardItem);
                    }

                    cardItem.visible = false;
                    var targetPoint = cardItem.localToGlobal(new Laya.Point());
                    var tw = Laya.Tween.to(virtualCardItem, { x: targetPoint.x, y: targetPoint.y, scaleX: scale, scaleY: scale }, 350, Laya.Ease.cubicOut, Laya.Handler.create(this, (vCardItem, cardItem, index) => {
                        cardItem.visible = true;
                        Laya.Tween.clearAll(vCardItem);
                        vCardItem.removeSelf();
                        Laya.Pool.recover('vcardItem', vCardItem);
                        if (index >= len - 1) yalla.event.YallaEvent.instance.event(yalla.data.EnumCustomizeCmd.Dominose_PlayCardNext);
                    }, [virtualCardItem, cardItem, i]), i * this._applyTime);

                    if (len > 1) {
                        tw.update = Laya.Handler.create(this, () => {
                            yalla.Sound.playSound('fill_card');
                        }, null, true);
                    }

                } else {
                    if (i >= len - 1) yalla.event.YallaEvent.instance.event(yalla.data.EnumCustomizeCmd.Dominose_PlayCardNext);
                }
            }
        }

        private getCardItem(mycard: boolean = false) {
            var cardItem;
            if (this._isSpectator || mycard) {
                cardItem = Laya.Pool.getItemByClass('cardItem', CardItem);
                this._isSpectator && cardItem.scale(0.66, 0.66);
                // cardItem.size(55, 86)
            } else {
                cardItem = Laya.Pool.getItemByClass('otherCardItem', OtherCardItem);
            }
            return cardItem;
        }

        /**
         * 对方发牌(发牌是房间内所有玩家同时进行,这里就不对其它玩家发牌完成后抛事件,避免同时抛出多次)
         * @param arr 
         */
        public issueOtherCard(arr: Array<Domino>, playAni: boolean, cardSkinId:number): void {
            var len = arr.length;
            if (len < 1) return;
            this.isIssueTween = false;
            var isWake = yalla.Global.game_state != yalla.data.GameState.State_Sleep;
            var cardWidth = this.getCardWidth(this.otherCardWid);
            var list = this.getHandView(cardWidth, len, this.getCardDis(this.otherCardDis));
            var [dis, bX, bY] = [list[0], list[1], this._isSpectator ? this._spectatorCardY : 3]
            for (var i = 0; i < len; i++) {
                // var cardItem = Laya.Pool.getItemByClass('otherCardItem', OtherCardItem);
                var cardItem = this.getCardItem();
                cardItem.pos(dis, bY);
                cardItem.setCardData(arr[i]);
                cardItem.cardSkinId = cardSkinId;
                this._otherCardList.push(cardItem);
                this._cardListUI.addChild(cardItem);
                // var xx = bX + (cardItem.width * cardItem.scaleX + dis) * i;
                var xx = bX + (cardWidth + dis) * i;
                if (isWake && playAni) {
                    this.isIssueTween = true;
                    var tw = Laya.Tween.to(cardItem, { x: xx, y: bY }, 280, Laya.Ease.cubicOut, Laya.Handler.create(this, (cardItem, index) => {
                        if (index >= len - 1) {
                            // yalla.Debug.log("==disableOpera 33==");
                            this.disableOpera();
                        }
                        Laya.Tween.clearAll(cardItem);
                    }, [cardItem, i]), i * this._issueTime);
                } else {
                    cardItem.pos(xx, bY);
                }
            }
        }

        /**
         * 补牌(对方)
         */
        public applyOtherCard(arr: Array<Domino>, cardSkinId:number): void {
            var len = arr.length;
            if (len < 1) return;
            if (!this._fillCardList) this._fillCardList = [];

            this.isIssueTween = false;
            var isWake = yalla.Global.game_state != yalla.data.GameState.State_Sleep;
            if (len == 1) yalla.Sound.playSound('fill_card');
            var beginX: number = yalla.data.RoomService.instance.room.deskPool.applyCardX;
            var beginY: number = yalla.data.RoomService.instance.room.deskPool.applyCardY;

            var otherCardNum = this._otherCardList.length;
            var cardWidth = this.getCardWidth(this.otherCardWid);
            var list = this.getHandView(cardWidth, otherCardNum + len, this.getCardDis(this.otherCardDis));
            var [dis, bX, bY, scale] = [list[0], list[1], 3, 1];
            if (this._isSpectator) {
                bY = this._spectatorCardY;
                scale = 0.66;
            }
            
            var endX = bX;
            for (var i = 0; i < otherCardNum; i++) {
                var cdItem = this._otherCardList[i];
                cdItem.x = bX + (cardWidth + dis) * i;
                // cdItem.x = bX + (cdItem.width + dis) * i;
            }
            if (this._otherCardList[otherCardNum - 1]) endX = this._otherCardList[otherCardNum - 1].x;
            for (var i = 0; i < len; i++) {
                // var cardItem = Laya.Pool.getItemByClass('otherCardItem', OtherCardItem);
                var cardItem = this.getCardItem();
                // var xx = endX + (cardItem.width * cardItem.scaleX + dis) * (i + 1);
                var xx = endX + (cardWidth + dis) * (i + 1);
                cardItem.pos(xx, bY);
                cardItem.setCardData(arr[i]);
                cardItem.cardSkinId = cardSkinId;
                this._cardListUI.addChild(cardItem);
                this._otherCardList.push(cardItem);
                if (isWake) {
                    this.isIssueTween = true;
                    cardItem.visible = false;
                    var virtualCardItem;
                    
                    if(this._isSpectator) virtualCardItem = Laya.Pool.getItemByClass('vcardItem', yalla.view.game.CardItem);
                    else virtualCardItem = Laya.Pool.getItemByClass('votherCardItem', OtherCardItem);

                    virtualCardItem.setCardData(arr[i]);
                    virtualCardItem.cardSkinId = cardSkinId;
                    virtualCardItem.pos(beginX, beginY).scale(0.1, 0.1);
                    virtualCardItem.visible = true;
                    if (this.applyUIBox) {
                        this.applyUIBox.addChild(virtualCardItem);
                        this._fillCardList.push(virtualCardItem);
                    }

                    var targetPoint = cardItem.localToGlobal(new Laya.Point());
                    var prop = { x: targetPoint.x, y: targetPoint.y, scaleX: scale, scaleY: scale };
                    if (this.indexType == 1) prop['rotation'] = 90;
                    else if (this.indexType == 3) prop['rotation'] = -90;
                    var tw = Laya.Tween.to(virtualCardItem, prop, 300, Laya.Ease.cubicOut, Laya.Handler.create(this, function (vCardItem, cardItem, index) {
                        cardItem.visible = true;
                        Laya.Tween.clearAll(vCardItem);
                        vCardItem.removeSelf();
                        if (this._isSpectator) Laya.Pool.recover('vcardItem', vCardItem);
                        else Laya.Pool.recover('votherCardItem', vCardItem);
                        if (index >= len - 1) yalla.event.YallaEvent.instance.event(yalla.data.EnumCustomizeCmd.Dominose_PlayCardNext);
                    }, [virtualCardItem, cardItem, i]), i * this._applyTime);
                    if (len > 1) {
                        tw.update = Laya.Handler.create(this, () => {
                            yalla.Sound.playSound('fill_card');
                        }, null, true);
                    }

                } else {
                    if (i >= len - 1) yalla.event.YallaEvent.instance.event(yalla.data.EnumCustomizeCmd.Dominose_PlayCardNext);
                }
            }
        }

        /**
         * 根据id获取手牌
         * @param id 
         */
        public getHandCardItemList(cardItemList: Array<any>, id: number): Array<any> {
            var len = cardItemList.length;
            var arr = [];
            for (var i = 0; i < len; i++) {
                if (cardItemList[i].data.id == id) {
                    arr.push(cardItemList[i]);
                }
            }
            return arr;
        }

        /**
         * 根据id移除手牌
         * @param id 
         */
        public removeHandCardItem(cardItemList: Array<any>, id: number): any {
            var len = cardItemList.length;
            for (var i = 0; i < len; i++) {
                Laya.Tween.clearAll(cardItemList[i]); //防止重复调用移除方法，tween动画重复执行，导致位置异常
                if (cardItemList[i].data.id == id) {
                    return cardItemList.splice(i, 1)[0];
                }
            }
        }

        /**
         * 所有手牌锁定
         */
        public disableOpera(isForced: boolean = false): void {
            if (this.indexType == 0 && this._myCardList && !this._isSpectator && yalla.data.RoomService.instance.room.deskPool) {
                var isEmpt: boolean = !yalla.data.RoomService.instance.room.deskPool.firstCardData;
                var len = this._myCardList.length;
                // Debug.log(isForced+":isForced===不能出牌44 锁牌==isEmpt："+isEmpt);
                for (var i = 0; i < len; i++) {
                    if (!isEmpt) this._myCardList[i].updateCardState(0);
                    else {
                        if (isForced) this._myCardList[i].updateCardState(0);
                    }
                    // if(isForced || !isEmpt) this._myCardList[i].updateCardState(0);
                    // this._myCardList[i].updateCardState(0);
                }
            }
        }

        /**
         * 筛选出有哪些牌可以出（只有自己才需处理）
         */
        public filterPlayCard(waitPlayCardHash: Object): void {
            // yalla.Debug.log(this.indexType+":indexType===不能出牌22==this._myCardList:" + this._myCardList.length); 
            if (this.indexType == 0 && this._myCardList && !this._isSpectator) {
                var len = this._myCardList.length;
                for (var i = 0; i < len; i++) {
                    var d: Domino = this._myCardList[i].data;
                    var state = waitPlayCardHash[d.id] ? 1 : 0;
                    this._myCardList[i].updateCardState(state);
                }
            }
        }

        /**
        * 出牌 翻拍后更新位置
        */
        public updateCardPosNoAni(cardWid: number, cardItemList: Array<any>, distance: number = 0): void {
            var cardLen = cardItemList.length;
            var list = this.getHandView(cardWid, cardLen, distance);
            var dis = list[0];
            var bX = list[1];
            // console.log(cardItemList);
            for (var i = 0; i < cardLen; i++) {
                var cardItem = cardItemList[i];
                Laya.Tween.clearAll(cardItem);
                cardItem.x = bX + (cardWid + dis) * i;
                cardItem.visible = true;
            }
        }

        /**
         * 出牌 翻拍后更新位置
         */
        public updateCardPos(cardWid: number, cardItemList: Array<any>, easeType: Function, distance: number = 0, beginX: number = 0, isAni: boolean = true): void {
            var cardLen = cardItemList.length;
            var list = this.getHandView(cardWid, cardLen, distance);
            var dis = list[0];
            var bX = list[1];
            for (var i = 0; i < cardLen; i++) {
                var xx = bX + (cardWid + dis) * i;
                cardItemList[i].visible = true;
                // Laya.Tween.clearAll(cardItemList[i]);//TODO:再次进入游戏，可能还在处理发牌动画，先抛出发牌动画结束事件（其他玩家动画还未完成，手牌多一点）,这时刚好其他玩家出牌，触发了更新手牌动画事件，so这里先清理每个手牌的Tween动画
                if (yalla.Global.game_state == yalla.data.GameState.State_Sleep || !isAni) {
                    Laya.Tween.clearAll(cardItemList[i]);
                    cardItemList[i].x = xx;
                } else {
                    Laya.Tween.to(cardItemList[i], { x: xx }, 90, easeType, Laya.Handler.create(this, (cardItem) => {
                        Laya.Tween.clearAll(cardItem);
                    }, [cardItemList[i]]), 0, true);
                }
            }
        }

        /** 
         * 出牌
         */
        public removeHandCard(d: Domino, isAni: boolean = true): Laya.Point {
            var cardItem;
            switch (this.indexType) {
                case 1:
                case 2:
                case 3:
                    cardItem = this.removeHandCardItem(this._otherCardList, d.id);
                    if (cardItem) this.updateCardPos(this.getCardWidth(this.otherCardWid), this._otherCardList, Laya.Ease.cubicOut, this.getCardDis(this.otherCardDis), 0, isAni);
                    break;
                case 0:
                    cardItem = this.removeHandCardItem(this._myCardList, d.id);
                    if (cardItem) this.updateCardPos(this.getCardWidth(this.myCardWid), this._myCardList, Laya.Ease.cubicOut, this.getCardDis(this.myCardDis), 0, isAni);
                    break;
            }
            var point = new Laya.Point();
            if (cardItem) {
                point = cardItem.localToGlobal(new Laya.Point());
                cardItem.removeSelf();
                Laya.Pool.recover(this.indexType == 0 ? 'cardItem' : 'otherCardItem', cardItem);
            } else {
                yalla.Debug.log(this._otherCardList.length + '-'+this._myCardList.length+'---removeHandCard failed' + d.id + '--indexType:' + this.indexType);
            }
            return point;
        }

        /**
         * 可选择的手牌区域切换，需要重置之前牌的状态
         * @param id 
         */
        public recoverState(id: number): void {
            var cardItemList = this.getHandCardItemList(this._myCardList, id);
            var len = cardItemList.length;
            for (var i = 0; i < len; i++) {
                cardItemList[i].updateCardState(1);
            }
        }

        /**
         * 获取 手牌之间距离，以及x初始坐标
         * @param cardWid    每张手牌宽度
         * @param cardItemList  手牌列表
         * @param distance   牌之间最大间距
         */
        private getHandView(cardWid: number, cardLen: number, distance: number = 0): Array<number> {
            var [dis, bX] = [0, 0];
            if (this._isSpectator) {
                // cardWid = this.spectatorCardWid;
                distance = 0;
            }
            if (this.cardListUIWid / cardLen < cardWid) {
                bX = (this.indexType == 0 || this._isSpectator) ? 1 : 5;
                dis = (this.cardListUIWid - bX * 2 - cardWid) / (cardLen - 1) - cardWid;
            } else {
                dis = Math.min(distance, (this.cardListUIWid - cardWid / 2) / cardLen - cardWid);
                bX = (this.cardListUIWid - (cardWid + dis) * cardLen) / 2;
            }
            return [dis, bX];
        }

        /**
         * 后台切回游戏后，重新校验卡牌位置
         */
        public checkCardPos(): void {
            switch (this.indexType) {
                case 1:
                case 2:
                case 3:
                    this.updateCardPosNoAni(this.getCardWidth(this.otherCardWid), this._otherCardList, this.getCardDis(this.otherCardDis));
                    break;
                case 0:
                    this.updateCardPosNoAni(this.getCardWidth(this.myCardWid), this._myCardList, this.getCardDis(this.myCardDis));
                    break;
            }
        }

        public get myCardItemList(): Array<CardItem> {
            return this._myCardList || [];
        }

        public reset(): void {
            var i;
            var myLen = this._myCardList.length;
            var otherLen = this._otherCardList.length;
            var fillLen = this._fillCardList ? this._fillCardList.length : 0;
            for (i = 0; i < myLen; i++) {
                Laya.Tween.clearAll(this._myCardList[i]);
                this._myCardList[i].removeSelf();
                Laya.Pool.recover('cardItem', this._myCardList[i]);
            }
            for (i = 0; i < otherLen; i++) {
                Laya.Tween.clearAll(this._otherCardList[i]);
                this._otherCardList[i].removeSelf();
                if (this.indexType == 0 || this._isSpectator) Laya.Pool.recover('cardItem', this._otherCardList[i]);
                else Laya.Pool.recover('otherCardItem', this._otherCardList[i]);
            }
            for (i = 0; i < fillLen; i++) {
                Laya.Tween.clearAll(this._fillCardList[i]);
                this._fillCardList[i].removeSelf();
                if(this.indexType == 0 || this._isSpectator) Laya.Pool.recover('vcardItem', this._fillCardList[i]);
                else Laya.Pool.recover('votherCardItem', this._fillCardList[i]);
            }
            
            this._myCardList.length = 0;
            this._otherCardList.length = 0;
            this._fillCardList.length = 0;
        }

        public clear(): void {
            this._otherCardList.length = 0;
            this._myCardList.length = 0;
            this._fillCardList.length = 0;
            if (this._cardListUI) {
                this._cardListUI.removeSelf();
                this._cardListUI.destroy(true);
            }
        }
    }
}