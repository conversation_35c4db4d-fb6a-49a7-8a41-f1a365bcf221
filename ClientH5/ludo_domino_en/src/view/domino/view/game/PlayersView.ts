
class PlayersView {
    public _view: Laya.View;
    private _roundTimeView: ui.domino.item.roundTimeUI;
    public get roundTimeView(): ui.domino.item.roundTimeUI {
        return this._roundTimeView;
    }
    constructor(gameUI: ui.domino.gameUI, _isSpectator: boolean = false, hairHeight: number, roundTimeView: ui.domino.item.roundTimeUI = null) {
        this._view = _isSpectator ? new ui.domino.item.standPlayerUI() : new ui.domino.item.seatPlayerUI();
        this._view.size(Laya.stage.width, Laya.stage.height);
        this._view['cardSp_2'].top += hairHeight;

        var index: number = gameUI.getChildIndex(gameUI.deskSp);
        gameUI.addChildAt(this._view, index + 1);
        this._roundTimeView = roundTimeView;
    }
    public get addTimeCheckBox() {
        return this._roundTimeView.addTimeCheckBox;
    }
    public get addTimeCount() {
        return this._roundTimeView.addTimeCount;
    }
    public get roundTime_add_View() {
        return this._roundTimeView.roundTime_add_View;
    }
    public get extendOnTxt() {
        return this._roundTimeView.extendOnTxt;
    }
}