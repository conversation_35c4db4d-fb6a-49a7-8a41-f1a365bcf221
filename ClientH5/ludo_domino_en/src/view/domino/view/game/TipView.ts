module yalla.view.game {
    /**
     * tipView   
     * pass & addScore
     */
    export class TipView {
        private _ui: ui.domino.gameUI;
        private _scoreTween:Laya.Tween;
        private _roundTimeTween:Laya.Tween;

        public indexType: number;
        public posList: Array<any> = [{ w: 264, h: 132, scale: 1 }, { w: 114, h: 158, scale: -1 }, { w: 169, h: 96, scale: -1 }, { w: 114, h: 158, scale: 1 }];
        public scorePosList: Array<any> = [{ left: 370, bottom: 135 }, { x: Laya.stage.width - 220, centerY: -60 }, { top: 155, centerX: 0 }, { left: 20, centerY: -20 }];
        public scoreList: Array<any> = [{ min: 5, max: 15, type: 'good' }, { min: 20, max: 30, type: 'nice' }, { min: 35, max: 50, type: 'excellent' }];
        public roundTimePosList: Array<any> = [{ left: 310, bottom: 280 }, { x: Laya.stage.width - 220, centerY: 130 }, { top: 240, right: 30 }, { left: 20, centerY: -325 }];

        constructor(ui: ui.domino.gameUI) {
            this._ui = ui;
        }

        public showPass(playItem: PlayerItem): void {
            if (!this._ui.passMc.visible) {
                if (yalla.Global.game_state != yalla.data.GameState.State_Sleep) {
                    this._ui.passMc.visible = true;
                    Laya.timer.once(1000, this, this.hidePass);
                    var posData = this.posList[playItem.indexType];
                    if (posData) {
                        this._ui.passBg.scaleX = posData.scale;
                        var p = playItem.getPlayerPos();
                        this._ui.passMc.pos(p.x + posData.w * posData.scale, p.y);
                        if (posData.scale == -1) {
                            this._ui.passTxt.pos(0, 28);
                        } else {
                            this._ui.passTxt.pos(19, 28);
                        }
                    }
                } else {
                    this.hidePass();
                }
            }
        }

        public hidePass(): void {
            this._ui.passMc.visible = false;
            yalla.event.YallaEvent.instance.event(yalla.data.EnumCustomizeCmd.Dominose_PlayCardNext);
        }

        public showScore(score: number, playItem: PlayerItem): string {
            if(score < 1 || !playItem) return;

            var scoreType = '';
            if (yalla.Global.game_state != yalla.data.GameState.State_Sleep) {
                if(this._scoreTween) this.resetScore();
                Laya.timer.clear(this, this.resetScore);
                var posData = this.scorePosList[playItem.indexType];
                if (posData) {
                    for (var k in posData) {
                        this._ui.scoreBox[k] = posData[k];
                    }
                    scoreType = this.getScoreType(score);
                    if (scoreType.length > 0) {
                        this._ui.scoreTypeImg.skin = yalla.getDomino(scoreType);//`domino/${scoreType}.png`;
                        this._ui.scoreImg.skin = yalla.getDomino(score);//`domino/${score}.png`;
                        this._ui.scoreTypeImg.visible = score <= 5 ? false : true;
                    }
                    this._ui.scoreBox.visible = true;
                    this._ui.scoreBox.alpha = 0;
                }
                if (playItem.indexType == 2) {
                    this._ui.scoreBox.y += yalla.Global.ProfileInfo.hairHeight / 2;
                }
                var y = this._ui.scoreBox.y - 40;
                this._scoreTween = Laya.Tween.to(this._ui.scoreBox, { alpha: 1, y: y }, 500, null, Laya.Handler.create(this, () => {
                    // Laya.Tween.clearTween(this._ui.scoreBox);
                    Laya.Tween.clearAll(this._ui.scoreBox);
                    Laya.timer.once(1000, this, this.resetScore);
                }));
            }
            return scoreType;
        }

        private resetScore():void{
            this._scoreTween = null;
            this._ui.scoreBox.visible = false;
            this._ui.scoreBox.left = this._ui.scoreBox.right = this._ui.scoreBox.top = this._ui.scoreBox.bottom = NaN;
            this._ui.scoreBox.centerX = this._ui.scoreBox.centerY = NaN;
        }

        private getScoreType(score: number): string {
            for (var k in this.scoreList) {
                if (score >= this.scoreList[k].min && score <= this.scoreList[k].max) {
                    return this.scoreList[k].type;
                }
            }
            return '';
        }

        public reset():void{
            if(this._scoreTween) {
                this._scoreTween.complete();
                this._ui.scoreBox.visible = false;
                this._ui.scoreBox.left = this._ui.scoreBox.right = this._ui.scoreBox.top = this._ui.scoreBox.bottom = NaN;
                this._ui.scoreBox.centerX = this._ui.scoreBox.centerY = NaN;
            }
        }

        public showRoundTime(playItem: PlayerItem): void {
            if (!this._ui.roundTimeBox.visible) {
                if (yalla.Global.game_state != yalla.data.GameState.State_Sleep) {

                    if(this._roundTimeTween) this.resetRoundTime();
                    Laya.timer.clear(this, this.resetRoundTime);
                    var posData = this.roundTimePosList[playItem.indexType];
                    if (posData) {
                        for (var k in posData) {
                            this._ui.roundTimeBox[k] = posData[k];
                        }
                        this._ui.roundTimeBox.visible = true;
                        this._ui.roundTimeBox.alpha = 0;
                    }
                    var y = this._ui.roundTimeBox.y - 40;
                    this._roundTimeTween = Laya.Tween.to(this._ui.roundTimeBox, { alpha: 1, y: y }, 500, null, Laya.Handler.create(this, () => {
                        Laya.timer.once(1000, this, this.resetRoundTime);
                    }), 0, true);
                } else {
                    this.resetRoundTime();
                }
            }
        }

        private resetRoundTime():void{
            Laya.Tween.clearAll(this._ui.roundTimeBox);
            this._roundTimeTween = null;
            this._ui.roundTimeBox.visible = false;
            this._ui.roundTimeBox.left = this._ui.roundTimeBox.right = this._ui.roundTimeBox.top = this._ui.roundTimeBox.bottom = NaN;
            this._ui.roundTimeBox.centerX = this._ui.roundTimeBox.centerY = NaN;
        }

        public clear(): void {
            if(this._scoreTween) this._scoreTween.clear();
            Laya.Tween.clearAll(this._ui.roundTimeBox);
            Laya.timer.clear(this, this.resetScore);
            Laya.timer.clear(this, this.hidePass);
            Laya.timer.clear(this, this.resetRoundTime);
            Laya.timer.clearAll(this);
        }
    }
}