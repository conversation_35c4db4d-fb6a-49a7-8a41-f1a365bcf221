module yalla.view.game {
    /**
     * topMc
     * rankSystemUI 父级 domino.topBox
     */
    export class TopView {
        private _gameUI: ui.domino.gameUI;
        private _topUI: Laya.View;
        private _rankSystemUI: Laya.View;
        private _fillNum: number;
        private _playerNums: number = 0;
        public setting: yalla.common.Setting;

        constructor(gameUI: ui.domino.gameUI) {
            this._gameUI = gameUI;
            if (yalla.Font.lan == 'en') {
                this._topUI = new ui.domino.item.game_top_enUI();
                this._rankSystemUI = new ui.domino.item.rank_system_enUI();
            } else {
                this._topUI = new ui.domino.item.game_top_arUI();
                this._rankSystemUI = new ui.domino.item.rank_system_arUI();
            }
            
            var index: number = gameUI.getChildIndex(gameUI.roundTimeBox);
            gameUI.addChildAt(this._topUI, index);
            gameUI.topBox.addChild(this._rankSystemUI);
            this.initEvent();
        }
        public set isSpectator(val: boolean) {
            this._topUI['titleSpectator'].visible = val;
            this._topUI['titleImg'].visible = !val;

            // var isShow = (yalla.Global.Account.isPrivate != GameRoomType.LEAGUE && !val && Global.Account.sameSkinBuySwitch)
            var isShow = (!yalla.Global.Account.league && !val && Global.Account.sameSkinBuySwitch);
            this._topUI['store_btn'].visible = isShow;
            // val && this.initSpectatorEvent();
        }

        public set audienceNum(val: number) {
            (this._topUI['audience_num'] as Laya.Label).text = val < 1000 ? String(val) : "999+";
        }

        public adapaterUI(hairHeight) {
            this.topUI['topMc'].top += hairHeight;
            this._rankSystemUI['set'].top += hairHeight;
            this._rankSystemUI['rankMc'].top += hairHeight;
        }

        // private initSpectatorEvent() {
        //     this._topUI['titleSpectator'].on(Laya.Event.CLICK, this, this.showSpectator);
        // }
        private showSpectator() {
            Audience.instance.popup(true, true);
        }
        private initEvent(): void {
            this._topUI['settingBtn'].on(Laya.Event.CLICK, this, this.onClickSetting);
            this._topUI['winBtn'].on(Laya.Event.CLICK, this, this.onClickPrize);
            yalla.event.YallaEvent.instance.on("watchNumChange", this, this.onWatchNumChange);
            this._topUI['store_btn'].on(Laya.Event.CLICK, this, this.showSameStore);
        }

        private onWatchNumChange(n: number) {
            this.audienceNum = n || 0
        }

        public showSameStore() {//显示快速商店
            // var playerList = yalla.data.RoomService.instance.room.player.player;
            // if (playerList) {
            //     var players = [];
            //     playerList.forEach(val => {
            //         var f = val.fPlayerInfo;
            //         players.push({
            //             idx: f.idx,
            //             faceUrl: f.faceUrl,
            //             nickName: f.nikeName,
            //             color: 3
            //         })
            //     })
                yalla.Native.instance.showSameStyleStore(yalla.data.RoomService.instance.room.buyPlayerList, yalla.data.UserService.instance.user.gold, yalla.data.UserService.instance.user.money);
            // }
        }

        public init(room: yalla.data.Room): void {
            this.onClickPrize();
        }

        public updateTitle(room: yalla.data.Room): void {
            if (room.gameType == 0) {
                this._topUI['titleNumTxt'].text = yalla.data.GameConfig.DrewScore + '';
                this._topUI['titleTxt'].text = yalla.data.TranslationD.Game_Title_Drew;

            } else if (room.gameType == 1) {
                this._topUI['titleNumTxt'].text = yalla.data.GameConfig.MugginScore + '';
                this._topUI['titleTxt'].text = yalla.data.TranslationD.Game_Title_Five;
            }

            if (room.isPrivate == GameRoomType.VIP) {
                this._gameUI.topBg.skin = yalla.getPublic('top-bg');
                this._topUI['stockMc'].skin = yalla.getDomino('image_title_bg_vip');
                this._topUI['titleImg'].skin = yalla.getDomino('image_title_bg_vip');
            } else {
                this._gameUI.topBg.skin = yalla.getDomino('top-bg');
                this._topUI['stockMc'].skin = yalla.getDomino('image_title_bg');
                this._topUI['titleImg'].skin = yalla.getDomino('image_title_bg');
            }
        }

        public initFillNum(num: number): void {
            this._fillNum = num;
            this.updateFillNum(0);
        }

        public updateFillNum(subNum: number): void {
            this._fillNum -= subNum;
            if (this._fillNum < 0) this._fillNum = 0;
            this._topUI['fillNumTxt'].text = this._fillNum + '';
        }

        public updateRoundNum(roundNum: number): void {
            if(this._topUI['roundNumTxt']) this._topUI['roundNumTxt'].text = String(roundNum);
        }

        /** 设置榜单的信息  获胜金币书 ｜  联赛相关信息*/
        public setRank(room: yalla.data.Room) {
            this._playerNums = room.playerNums;
            this.updateTitle(room);
            if (yalla.Global.Account.leagueWatch || yalla.Global.Account.isPrivate == GameRoomType.LEAGUE) {
                // this._topUI['league'].visible = true;
                // this._topUI['commonRank'].visible = false;
                // this._topUI['rank_1'].text = '';
                // this._topUI['typeTxt'].text = '';
                // this._topUI['rankImgTxt1'].align = 'center';
                // this._topUI['roomIdTxt'].text = room.showId + '';
                // if (yalla.Global.Account.league) {
                //     this._topUI['league_groupNum_txt'].text = yalla.Global.Account.league['groupNo'];
                //     if (yalla.Font.lan == 'en') this._topUI['rankImgTxt1'].text = 'Challenge ' + yalla.Global.Account.league['round'];
                //     else this._topUI['rankImgTxt1'].text = ' تحدي ' + yalla.Global.Account.league['round'];
                // }
                this._rankSystemUI['league'].visible = true;
                this._rankSystemUI['commonRank'].visible = false;
                this._rankSystemUI['rank_1'].text = '';
                this._rankSystemUI['typeTxt'].text = '';
                this._rankSystemUI['rankImgTxt1'].align = 'center';
                this._rankSystemUI['roomIdTxt'].text = room.showId + '';
                if (yalla.Global.Account.league) {
                    this._rankSystemUI['league_groupNum_txt'].text = yalla.Global.Account.league['groupNo'];
                    if (yalla.Font.lan == 'en') this._rankSystemUI['rankImgTxt1'].text = 'Challenge ' + yalla.Global.Account.league['round'];
                    else this._rankSystemUI['rankImgTxt1'].text = ' تحدي ' + yalla.Global.Account.league['round'];
                }
                    
            } else {
                // this._topUI['league'].visible = false;
                // this._topUI['commonRank'].visible = true;
                // this._topUI['typeTxt'].text = yalla.data.GameConfig.typeNames[room.gameType];
                // if (room.isPrivate == GameRoomType.CHAMPION) {
                //     this._topUI['rank_1'].text = yalla.util.filterNum(room.cost);
                // } else {
                //     var royalty = yalla.Global.Account.royalty ? yalla.Global.Account.royalty : 0;
                //     this._topUI['rank_1'].text = yalla.util.filterNum(room.cost * this._playerNums * (1 - royalty / 100));
                // }
                // this._topUI['rank_roomId'].text = room.showId + '';
                this._rankSystemUI['league'].visible = false;
                this._rankSystemUI['commonRank'].visible = true;
                this._rankSystemUI['typeTxt'].text = yalla.data.GameConfig.typeNames[room.gameType];
                if (room.isPrivate == GameRoomType.CHAMPION) {
                    this._rankSystemUI['rank_1'].text = yalla.util.filterNum(room.cost);
                } else {
                    var royalty = yalla.Global.Account.royalty ? yalla.Global.Account.royalty : 0;
                    this._rankSystemUI['rank_1'].text = yalla.util.filterNum(room.cost * this._playerNums * (1 - royalty / 100));
                }
                this._rankSystemUI['rank_roomId'].text = room.showId + '';
            }
            if (this._playerNums >= 4) {
                this._topUI['stockMc'].visible = false;
                this._topUI['titleImg'].centerX = 0;
                this._topUI['titleSpectator'].centerX = 0;
                this._topUI['infoMc'].space = 30;
            } else {
                this._topUI['stockMc'].visible = true;
            }
            if (room.isPrivate == GameRoomType.PRIVATE) {
                // this._topUI['rank1'].visible = false;
                this._rankSystemUI['rank1'].visible = false;
            } else {
                // this._topUI['rank1'].visible = true;
                // this._topUI['rankMc'].height = 143;

                this._rankSystemUI['rank1'].visible = true;
                this._rankSystemUI['rankMc'].height = 143;
            }
        }

        /**观战的chaton 不显示 */
        public setWatchSetting(): void{
            if (!this.setting) this.setting = new yalla.common.Setting(this._rankSystemUI);
            this.setting.setWatchSetting(yalla.Font.lan == 'en' ? true : false);
        }

        private onClickSetting(e: Laya.Event): void {
            if (e) {
                yalla.Sound.playSound('click');
                e.stopPropagation();
            }
            // if (this._topUI['rankMc'].visible) {
            if(this._rankSystemUI['rankMc'].visible){
                Laya.timer.clear(this, this.hideRank);
                this.hideRank();
            }

            if (!this.setting) this.setting = new yalla.common.Setting(this._rankSystemUI);
            // this._topUI['set'].visible = !this._topUI['set'].visible;
            // if (this._topUI['set'].visible) this.setting.update();
            this._rankSystemUI['set'].visible = !this._rankSystemUI['set'].visible;
            if (this._rankSystemUI['set'].visible) this.setting.update();

            yalla.Native.instance.mobClickEvent(buryPoint.DGAME_SETTING);
            yalla.event.YallaEvent.instance.event('click_gameTopview');
        }

        /**
         * 奖励信息 显示隐藏
         */
        private onClickPrize(e: Laya.Event = null): void {
            if (e) {
                yalla.Sound.playSound('click');
                e.stopPropagation();
            }
            // if (this._topUI['set'].visible) this._topUI['set'].visible = false;
            if (this._rankSystemUI['set'].visible) this._rankSystemUI['set'].visible = false;

            Laya.timer.clear(this, this.hideRank);
            // this._topUI['rankMc'].visible = !this._topUI['rankMc'].visible;
            // if (this._topUI['rankMc'].visible) {
            this._rankSystemUI['rankMc'].visible = !this._rankSystemUI['rankMc'].visible;
            if(this._rankSystemUI['rankMc'].visible){
                if (yalla.Global.game_state != yalla.data.GameState.State_Sleep) {
                    Laya.timer.once(2000, this, this.hideRank);
                } else {
                    this.hideRank();
                }
            }
            yalla.Native.instance.mobClickEvent(buryPoint.DGAME_REWARD);
            yalla.event.YallaEvent.instance.event('click_gameTopview');
            // yalla.common.Tip.instance.showTip(" You have been banned from chat until 2022-03-06 06:2:35(GMT+3). Reason: spread pornographic/sexual conteent.Please note that the repeated behaviors against Yalla Ludo Conduct Regulations could lead to your account's permanent block.")
        }

        public get topUI(): Laya.View {
            return this._topUI;
        }

        /**
         * 隐藏奖励信息
         */
        private hideRank(): void {
            // this._topUI['rankMc'].visible = false;
            this._rankSystemUI['rankMc'].visible = false;
        }

        public onClickGameUI(): void {
            this.hideRank();
            // this._topUI['set'].visible = false;
            this._rankSystemUI['set'].visible = false;
        }

        private removeEvent(): void {
            this._topUI.offAll();
            this._topUI['settingBtn'].off(Laya.Event.CLICK, this, this.onClickSetting);
            this._topUI['winBtn'].off(Laya.Event.CLICK, this, this.onClickPrize);
            this._topUI['titleSpectator'].off(Laya.Event.CLICK, this, this.showSpectator);
            yalla.event.YallaEvent.instance.off("watchNumChange", this, this.onWatchNumChange);
            this._topUI['store_btn'].off(Laya.Event.CLICK, this, this.showSameStore);
            Laya.timer.clear(this, this.hideRank);
            Laya.timer.clearAll(this);
        }

        public clear(): void {
            this.removeEvent();
            this.setting && this.setting.clear();
            if (this._topUI) {
                this._topUI.removeSelf();
                this._topUI.destroy(true);
            }
            this.setting = null;
        }
    }
}