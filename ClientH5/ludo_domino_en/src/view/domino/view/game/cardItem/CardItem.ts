module yalla.view.game {

    /**
     * 自己的手牌
     */
    export class CardItem extends ui.domino.item.cardItemUI {
        private _data: Domino;
        private _state: number = 0;      //0默认状态  1可以出牌  -1不可出牌
        private _playCardFunc: Function;
        private _clickTime: number = 0;

        constructor() {
            super();
            this.initUI();
            this.initEvent();
        }

        private initUI(): void {
        }

        private initEvent(): void {
            this.on(Laya.Event.CLICK, this, this.onClick);
        }

        private removeEvent(): void {
            this.off(Laya.Event.CLICK, this, this.onClick);
        }

        private onClick(): void {
            if (!yalla.data.RoomService.instance || !yalla.data.RoomService.instance.selectCardList || !this._data) return;
            yalla.data.RoomService.instance.selectCardList.push(this._data.id);
            var selectArr = yalla.data.RoomService.instance.selectCardList;
            var len = selectArr.length;
            var isDouble = false;
            if (selectArr.length >= 2 && selectArr[len - 2] == selectArr[len - 1] && selectArr[len - 2] == this._data.id) {
                isDouble = true;
            }
            this.selectCard(isDouble);
        }

        private selectCard(isDouble: boolean = false): void {
            // yalla.Debug.log(this._state+"=====selectCard======="+yalla.data.RoomService.instance.room.isMyActive + "--"+this._data.headNum+":"+this._data.tailNum);
            if (!yalla.data.RoomService.instance.room.isMyActive) return;
            if (this._state != 1) return;
            this._playCardFunc && this._playCardFunc(this._data, isDouble);

            if (!this.selectBg.visible) {
                yalla.Sound.playSound('select');
                this.y = -14;
            }
            this.selectBg.visible = true;
        }

        public setCardData(data: Domino, playCardFunc: Function = null): void {
            this._data = data;
            this._playCardFunc = playCardFunc;
            this.updateCard(data);
            this.updateCardState(0);
        }

        set cardSkinId(v: number) {
            if (!v || v == 20000) {
                // this.cardSkinImg.skin = yalla.getDomino('image_domino_front');
                this.cardSkinImg.visible = false;
            } else {
                this.cardSkinImg.visible = true;
                this.cardSkinImg.skin = '';
                if (yalla.Skin.instance.hasFront(v)) {
                    yalla.event.YallaEvent.instance.once(`image_domino_front_${v}.png`, this, d => {
                        if (this.destroyed) return;
                        this.cardSkinImg.skin = `${yalla.File.filePath}image_domino_front_${v}.png`;
                    })
                    yalla.File.getFileByNative(`image_domino_front_${v}.png`)
                }
            }
        }

        public updateCard(data: Domino): void {
            this.tail.visible = this.head.visible = false;
            if (data.tailNum > 0) {
                this.tail.visible = true;
                this.tail.skin = yalla.getDomino('image_domino_number_' + data.tailNum);//"domino/image_domino_number_" + data.tailNum + '.png';
            }
            if (data.headNum > 0) {
                this.head.visible = true;
                this.head.skin = yalla.getDomino('image_domino_number_' + data.headNum);//"domino/image_domino_number_" + data.headNum + '.png';
            }
        }

        /**
         * 更新卡状态state 0常规状态(不可以出牌)   1可以出牌
         */
        public updateCardState(state: number = 0): void {
            this._state = state;
            this.maskBg.visible = state == 0 ? true : false;
            this.selectBg.visible = false;
            if(!yalla.Global.Account.leagueWatch) this.y = 6;
        }

        public setResultView(): void {
            this.maskBg.visible = false;
            this.selectBg.visible = false;
            this.y = 0;
        }

        public get data(): Domino {
            return this._data;
        }


        public clear(): void {
            this.removeEvent();
        }
    }
}