module yalla.view.game {
    /**
     * 提示牌
     */
    export class HintCardItem extends Laya.Image {
        private _data: Domino;
        private _deskSort: number;
        private wid:number;
        private hei:number;
        constructor() {
            super();
            this.initUI();
            this.initEvent();
        }

        private initUI(): void {
            this.wid = yalla.data.CardWid;
            this.hei = yalla.data.CardWid * 2;
            this.skin = yalla.getDomino('alphaBg');//'domino/alphaBg.png';
            this.size(this.wid, this.hei);
            this.anchorX = this.anchorY = 0.5;
            this.mouseEnabled = true;
            this.mouseThrough = false;
        }

        private initEvent(): void {
            this.on(Laya.Event.CLICK, this, this.onClickPlayCard);
        }

        private removeEvent(): void {
            this.off(Laya.Event.CLICK, this, this.onClickPlayCard);
        }

        private onClickPlayCard(): void {
            if (!this._data) return;
            yalla.data.RoomService.instance.sendPlayCard(this._data, this._deskSort);
        }

        public setCardData(d:Domino, deskSort:number): void {
            this._data = d;
            this._deskSort = deskSort;
            this.visible = true;
        }

        /**
         * 扩大提示牌的点击区域
         * @param scale 
         */
        public updateSize(scale:number):void{
            if(scale > 1) this.size(this.wid * scale, this.hei * scale);
            else this.size(this.wid/scale * 1.4, this.hei/scale * 1.4);
        }

        public get data(): Domino {
            return this._data;
        }

        public hide():void{
            this._data = null;
            this.visible = false;
        }

        public clear(): void {
            this._data = null;
            this.removeEvent();
        }
    }
}