module yalla.view.game {
    /**
     * 对方的手牌
     */
    export class OtherCardItem extends Laya.Image {
        private _data: Domino;
        private _state: number = 0;      //0默认状态  1可以出牌  -1不可出牌

        constructor() {
            super();
            this.skin = yalla.getDomino('image_domino_back');
        }

        public setCardData(data: Domino): void {
            this._data = data;
            // this.updateCardState(0);
            // this.updateCard(data);
        }

        set cardSkinId(v: number) {
            if (!v || v == 20000) {
                this.skin = yalla.getDomino('image_domino_back');
            } else {
                this.skin = yalla.getDomino('image_domino_back');
                if (yalla.Skin.instance.hasBack(v)) {
                    yalla.event.YallaEvent.instance.once(`image_domino_back_${v}.png`, this, d => {
                        if (this.destroyed) return;
                        this.skin = `${yalla.File.filePath}image_domino_back_${v}.png`;
                    })
                    yalla.File.getFileByNative(`image_domino_back_${v}.png`)
                }
            }
        }

        // public updateCard(data: Domino): void {
        //     this.tail.visible = this.head.visible = false;
        //     if (data.tailNum > 0) {
        //         this.tail.visible = true;
        //         this.tail.skin = "game/image_domino_number_" + data.tailNum + '.png';//yl.getImg('image_domino_number_' + data.tailNum);
        //     }
        //     if (data.headNum > 0) {
        //         this.head.visible = true;
        //         this.head.skin = "game/image_domino_number_" + data.headNum + '.png';//yl.getImg('image_domino_number_' + data.headNum);
        //     }
        // }

        /**
         * 更新卡状态state 0常规状态(不可以出牌)   1可以出牌
         */
        public updateCardState(state: number = 0): void {
            this._state = state;
        }

        public get data(): Domino {
            return this._data;
        }

        public clear(): void {
        }
    }
}