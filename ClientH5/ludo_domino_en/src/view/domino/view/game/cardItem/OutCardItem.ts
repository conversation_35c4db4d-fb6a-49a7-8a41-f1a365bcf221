module yalla.view.game {
    /**
     * 已出的牌
     */
    export class OutCardItem extends Laya.Image{//ui.ludo.page.item.outCardItemUI
        private _data: yalla.data.OutDominoData;
        private _tailImg:Laya.Image;
        private _headImg: Laya.Image;
        private _cardSkinImg: Laya.Image;//牌皮肤

        constructor() {
            super();
            this.initUI();
        }

        private initUI(): void {
            this.skin = yalla.getDomino('image_domino_normal');
            this.size(yalla.data.CardWid, yalla.data.CardWid*2);

            this._tailImg = new Laya.Image();
            this._headImg = new Laya.Image();
            this._tailImg.size(yalla.data.CardWid, yalla.data.CardWid);
            this._headImg.size(yalla.data.CardWid, yalla.data.CardWid);
            this._tailImg.pos(0, yalla.data.CardWid);
            this.pivot(yalla.data.CardWid / 2, yalla.data.CardWid);
            this.addChild(this._tailImg);
            this.addChild(this._headImg);
        }

        public setCardData(data: yalla.data.OutDominoData): void {
            this._data = data;
            if (this._data.tailNum == 0) {
                this._tailImg.visible = false;
            } else {
                this._tailImg.visible = true;
                this._tailImg.skin = yalla.getDomino("image_domino_number_" + data.tailNum);
            }

            if (this._data.headNum == 0) {
                this._headImg.visible = false;
            } else {
                this._headImg.visible = true;
                this._headImg.skin = yalla.getDomino("image_domino_number_" + data.headNum);
            }
        }

        set cardSkinId(v: number) {
            if (!v || v == 20000) {
                // this._cardSkinImg.visible = false;
            } else {
                if (!this._cardSkinImg) {
                    this._cardSkinImg = new Laya.Image();
                    if(!this._cardSkinImg.parent) this.addChild(this._cardSkinImg);
                }
                // this._cardSkinImg.visible = true;
                this._cardSkinImg.skin = '';
                // this._cardSkinImg.skin = yalla.getDomino('image_domino_front');
                if (yalla.Skin.instance.hasFront(v)) {
                    yalla.event.YallaEvent.instance.once(`image_domino_front_${v}.png`, this, d => {
                        if (this.destroyed) return;
                        this._cardSkinImg.skin = `${yalla.File.filePath}image_domino_front_${v}.png`;
                    })
                    yalla.File.getFileByNative(`image_domino_front_${v}.png`)
                }
            }
        }

        public setRotation(side: number): void {
            var rotation = 0;
            if (side == yalla.data.DirType.BOTTOM) rotation = 180;
            else if (side == yalla.data.DirType.LEFT) rotation = 270;
            else if (side == yalla.data.DirType.RIGHT) rotation = 90;
            this.rotation = rotation;
        }

        public get data(): yalla.data.OutDominoData {
            return this._data;
        }

        public clear(): void {
        }
    }
}