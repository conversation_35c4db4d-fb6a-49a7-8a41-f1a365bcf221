module yalla.view.game {

    /**
     * 每轮结局牌
     */
    export class ResultCardItem extends Laya.Image {
        private _data: Domino;
        private _tailImg: Laya.Image;
        private _headImg: Laya.Image;
        private _cardSkinImg: Laya.Image;//牌皮肤

        public wid: number = 20.4;
        constructor() {
            super();
            this.initUI();
        }
        private initUI(): void {
            this.skin = yalla.getDomino('image_domino_normal');
            this.size(this.wid, this.wid * 2);

            this._tailImg = new Laya.Image();
            this._headImg = new Laya.Image();
            this._tailImg.size(this.wid, this.wid);
            this._headImg.size(this.wid, this.wid);
            this._tailImg.pos(0, this.wid);

            this.addChild(this._tailImg);
            this.addChild(this._headImg);

            // this._cardSkinImg = new Laya.Image();
            // this._cardSkinImg.size(this.wid, this.wid * 2);
            // this.addChild(this._cardSkinImg);
        }

        public setCardData(data: Domino): void {
            this._data = data;
            this.updateCard(data);
        }

        set cardSkinId(v: number) {
            if (!v || v == 20000) {
                // this._cardSkinImg.visible = false;
            } else {
                if (!this._cardSkinImg) {
                    this._cardSkinImg = new Laya.Image();
                    this._cardSkinImg.size(this.wid, this.wid * 2);
                    if(!this._cardSkinImg.parent) this.addChild(this._cardSkinImg);
                }
                // this._cardSkinImg.visible = true;
                this._cardSkinImg.skin = '';
                if (yalla.Skin.instance.hasFront(v)) {
                    yalla.event.YallaEvent.instance.once(`image_domino_front_${v}.png`, this, d => {
                        if (this.destroyed) return;
                        this._cardSkinImg.skin = `${yalla.File.filePath}image_domino_front_${v}.png`;
                    })
                    yalla.File.getFileByNative(`image_domino_front_${v}.png`)
                }
            }
        }

        public updateCard(data: Domino): void {
            this._tailImg.visible = this._headImg.visible = false;
            if (data.tailNum > 0) {
                this._tailImg.visible = true;
                this._tailImg.skin = yalla.getDomino("image_domino_number_" + data.tailNum);
            }
            if (data.headNum > 0) {
                this._headImg.visible = true;
                this._headImg.skin = yalla.getDomino("image_domino_number_" + data.headNum);
            }
        }

        public get data(): Domino {
            return this._data;
        }

        public clear(): void {
        }
    }
}