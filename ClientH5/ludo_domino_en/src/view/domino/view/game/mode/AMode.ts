module yalla.view.game {
    import Point = Laya.Point;
    export class AMode extends Laya.EventDispatcher {
        protected _ui: ui.domino.gameUI;
        protected _cardList: Array<OutCardItem> = [];
        protected _deskPool: yalla.data.Base;
        protected _vy: number = 0;
        private _room: yalla.data.Room;

        protected _hintImgList:Array<Laya.Image> = [];
        protected _hintItemList: Array<HintCardItem> = [];
        protected _hintItemTxtList: Array<Laya.Text> = [];
        protected _hintNums:number = 2;

        constructor(ui: ui.domino.gameUI, room: yalla.data.Room, vy:number = 0) {
            super();
            this._ui = ui;
            this._room = room;
            this._vy = vy;
            this._hintNums = this._room.gameType == 0 ? 2:4;
            this.initHintItem();
        }

        private initHintItem():void{
            var len = this._hintNums;//最多同时有4个位置提示
            for (var i = 0; i < len; i++) {
                var hintCardItem = new yalla.view.game.HintCardItem();
                hintCardItem.visible = false;
                this._hintItemList[i] = hintCardItem;

                var hintImg = new Laya.Image('domino/image_domino_mask.png');
                hintImg.visible = hintImg.mouseEnabled = false;
                hintImg.mouseThrough = true;
                hintImg.size(yalla.data.CardWid, yalla.data.CardWid*2);
                hintImg.anchorX = hintImg.anchorY = 0.5;
                this._hintImgList[i] = hintImg;

                if(this._room.gameType == 1){
                    var txt: Laya.Text = new Laya.Text();
                    txt.align = 'center';
                    txt.font = 'number';
                    txt.size(yalla.data.CardWid, 48);
                    txt.pivot(txt.width / 2, txt.height / 2);
                    txt.rotation = -90;
                    txt.visible = txt.mouseEnabled = false;
                    txt.mouseThrough = true;
                    this._hintItemTxtList[i] = txt;
                }
            }
        }

        public getMoveTime(start: Point, end: Point): number {
            var distance = yalla.util.getDistance(start, end)['Distance'];
            var moveToDeskTime:number = distance * 1.5;     //1.5 根据初始发牌距离/时间算出的系数
            return moveToDeskTime;
        }

        /**
         * 校正放牌区位置 
         * @param cardItem 
         * @param deskPool 
         */
        public updateDeskCard(cardItem: OutCardItem, deskPool: yalla.data.Base, deskSort: number = 1): void {
            this.hideHintItem();
            this._deskPool = deskPool;
            this._cardList.push(cardItem);

            var [disLeftX, disTopY, leftDis, topDis] = [0,0,0,0];
            var cardData = cardItem.data;
            if (cardData.xx <= 0) {
                disLeftX = cardData.xx;
                leftDis = cardData.width / 2;
                disLeftX -= leftDis;
            }

            if (cardData.yy <= 0) {
                disTopY = cardData.yy;
                topDis = cardData.height / 2;
                disTopY -= topDis;
            }

            if (disLeftX != 0 || disTopY != 0) {
                var cardLen = this._cardList.length;
                for (var i = 0; i < cardLen; i++) {
                    this._cardList[i].x -= disLeftX;
                    this._cardList[i].y -= disTopY;
                    this._cardList[i].data.pos(this._cardList[i].x, this._cardList[i].y);
                }
            }
            this.checkDeskSp();
        }

        public checkDeskSp(): void {
            var scaleList = this.getDeskScale();
            var scale = scaleList[0];
            this._ui.deskSp.pivot(scaleList[2] / 2, scaleList[1] / 2);
            this._ui.deskSp.scale(scale, scale);
            var x = (Laya.stage.width - this._ui.deskSp.width)/2;
            var y = (Laya.stage.height - this._ui.deskSp.height - this._vy)/2;
            this._ui.deskSp.pos(x, y);
            // yalla.Debug.log(this._vy+'===checkDeskSp===='+x+":"+y + '-----' + ((Laya.stage.height - this._ui.deskSp.height - 160)/2));
            // this._ui.deskSp.graphics.drawRect(0, 0, scaleList[2], scaleList[1], 0xffff00, 0xff0000,5);
        }

        public fileterCardPos(posList: Array<any>, d: Domino): void {
            this.hideHintItem();

            var len = posList.length;
            for (var i = 0; i < len; i++) {
                var pd = posList[i];
                var deskSort = pd.deskSort;
                var hintCardItem = this._hintItemList[i];
                var hintImg = this._hintImgList[i];
                if(!hintCardItem.displayedInStage) {
                    this._ui.deskSp.addChild(hintImg);
                    hintImg.zOrder = 800;
                    this._ui.deskSp.addChild(hintCardItem);
                    hintCardItem.zOrder = 900;
                }

                var sideDonimo = this._deskPool.cardHash[pd.sideDominoId];
                var nextD = this._deskPool.getNextInfo(deskSort, pd, sideDonimo, true)[0];
                var sideType = (nextD['showSide'] == yalla.data.DirType.TOP || nextD['showSide'] == yalla.data.DirType.BOTTOM) ? 0 : 1;//0垂直  1水平
                hintCardItem.pos(nextD['xx'], nextD['yy']);
                // hintCardItem.setRotation(sideType);
                hintCardItem.rotation = this.getRotation(d, nextD['showSide']);
                hintCardItem.setCardData(d, deskSort);
                hintCardItem.updateSize(this._deskPool.currentScale);
                hintImg.pos(nextD['xx'], nextD['yy']);
                hintImg.rotation = hintCardItem.rotation;
                hintImg.visible = true;

                if(this._room.gameType == 1){
                    var txt = this._hintItemTxtList[i];
                    if(!txt.displayedInStage) {
                        this._ui.deskSp.addChild(txt);
                        txt.zOrder = 1000;
                    }
                    txt.visible = true;
                    var score = pd.score;
                    txt.text = ((score > 0 && score % 5 == 0) ? '+' : '') + score;
                    txt.pos(hintCardItem.x, hintCardItem.y);
                }
            }
        }

        public get cardList(): Array<OutCardItem> { return this._cardList; }
        /**
         * 十字模式，当前上下左右分数
         */
        private getDeskScore(): number { return 0; }

        public getDeskScale(): Array<number> {
            var minXData = { xx: 0 };
            var minYData = { yy: 0 };
            var maxXData = { xx: 0 };
            var maxYData = { yy: 0 };
            var len = this._cardList.length;
            for (var i = 0; i < len; i++) {
                var cardData = this._cardList[i].data;
                if (cardData.xx < minXData['xx']) {
                    minXData = cardData;
                }
                if (cardData.yy < minYData['yy']) {
                    minYData = cardData;
                }

                if (cardData.xx > maxXData['xx']) {
                    maxXData = cardData;
                }
                if (cardData.yy > maxYData['yy']) {
                    maxYData = cardData;
                }
            }
            var [hLen, vLen] = [0,0];
            if (len <= 1) {
                hLen = this._cardList[0].data.width;
                vLen = this._cardList[0].data.height;
            } else {
                hLen = maxXData['xx'] - minXData['xx'] + ((minXData['id'] ? minXData['width'] : 0) + (maxXData['id'] ? maxXData['width'] : 0)) / 2;
                vLen = maxYData['yy'] - minYData['yy'] + ((minYData['id'] ? minYData['height'] : 0) + (maxYData['id'] ? maxYData['height'] : 0)) / 2;
            }

            var scale = this._deskPool.currentScale;
            return [scale, vLen, hLen];
        }

        private getRotation(d:Domino, showSide:number):number{
            if((showSide == yalla.data.DirType.LEFT||showSide == yalla.data.DirType.RIGHT) && this._room.gameType == 1)
                if(this._deskPool.hWrapIDHash[d.id] || this._deskPool.vWrapIDHash[d.id])
                    return 0;

            var rotation = 0;
            if (showSide == yalla.data.DirType.LEFT || showSide == yalla.data.DirType.RIGHT) rotation = 90;
            return rotation;
        }

        /**
         * 隐藏
         */
        public hideHintItem():void{
            var len = this._hintItemList.length;
            for (var i = 0; i < len; i++) {
                this._hintItemList[i].hide();
                if(this._hintItemTxtList[i]){
                    this._hintItemTxtList[i].visible = false;
                }
                if(this._hintImgList[i]) this._hintImgList[i].visible = false;
            }
        }

        /**
         * 清理
         */
        public removeHintItem(): void {
            var len = this._hintNums;
            for (var i = 0; i < len; i++) {
                this._hintItemList[i].clear();
                // this._hintItemList[i].removeSelf();
                // this._hintItemTxtList[i] && this._hintItemTxtList[i].removeSelf();
            }

            this._hintItemList = [];
            this._hintItemTxtList = [];
            this._hintImgList = [];
        }

        public reset(): void {
            for (var i = 0; i < this._cardList.length; i++) {
                Laya.Tween.clearAll(this._cardList[i]);
                Laya.Pool.recover('outCardItem', this._cardList[i]);
                if(this._cardList[i]) this._cardList[i].removeSelf();
            }
            this._cardList = [];
            if(this._ui && this._ui.deskSp){
                this._ui.deskSp.removeChildren();
                this._ui.deskSp.scale(1, 1);
            }
            this.hideHintItem();
        }

        public clear(): void {
            // this.reset();
            // if(this._cardList){
            //     for (var i = 0; i < this._cardList.length; i++) {
            //         Laya.Tween.clearAll(this._cardList[i]);
            //         Laya.Pool.recover('outCardItem', this._cardList[i]);
            //         if(this._cardList[i]) this._cardList[i].clear();
            //     }
            //     // this._cardList = null;
            // }
        }
    }
}