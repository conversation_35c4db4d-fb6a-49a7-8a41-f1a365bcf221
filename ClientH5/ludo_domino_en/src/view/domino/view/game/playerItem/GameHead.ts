module yalla.view.game {
    /**
     * domino内共用头像
     */
    export class GameHead {
        private _ui: Laya.View;
        private _data: playerShowInfo;

        private _facetemplet: Laya.Templet;
        private _faceSk: Laya.Skeleton;
        private _faceAni: any;

        constructor(ui: Laya.View) {
            this._ui = ui;
        }

        private initSK(url: string) {
            this._facetemplet = new Laya.Templet();
            this._facetemplet.on(Laya.Event.COMPLETE, this, this.faceSkComplete);
            this._facetemplet.loadAni(url);
        }

        private faceSkComplete() {
            if (this._facetemplet) {
                this._faceSk = this._facetemplet.buildArmature(1);
                this._faceSk.pos(this._ui['faceBox'].x + 160 / 2, this._ui['faceBox'].y + 160 / 2);
                this._ui.addChildAt(this._faceSk, Math.max(this._ui.numChildren - 1, 0));
                //经过对比native引擎版本更改记录，说是从1.0.2(当前使用版本)到1.0.3修复了骨骼动画闪烁问题，修复方式主要就是更改渲染提交使用缓冲区大小。
                //偶现游戏内对手头像框不显示一直闪烁，从逻辑上来说也是符合，初进游戏当时集中提交的渲染数据超出缓冲区大小，所以延迟一帧播放，延迟提交渲染数据，错过峰值提交。
                Laya.timer.callLater(this, () => {
                    this.playFaceBoxAni();
                });
            }
        }

        public setData(data: playerShowInfo): void {
            this._data = data;
            this.loadFace(this._data);
            // yalla.Debug.log(this._data.fPlayerInfo.idx + ':idx----gameURL-----faceUrl:' + this._data.fPlayerInfo.faceUrl);
            var faceId = this._data.fPlayerInfo.faceId;
            // var faceId = 121036;
            if (faceId > 0) {
                // //TODO::test
                // this._faceAni = 0;
                // this._ui['faceBox'].visible = false;
                // this.initSK('res/test/' + `faceframe_${faceId}.sk`);
                // // this._ui['faceBox'].skin = 'res/test/' + `face_${faceId}.png`;

                this.showFaceBox();
            }
        }

        public loadFace(data: playerShowInfo): void {
            var defaultUrl = yalla.getDomino('default_head');
            this._faceUrl = defaultUrl;

            var faceUrl = data.fPlayerInfo.faceUrl;
            if (faceUrl && faceUrl.length > 0) {
                if (this._ui['faceImg'].skin === faceUrl) return;
                this._ui['faceImg'].skin = defaultUrl;
                yalla.File.downloadImgByUrl(faceUrl, (url) => {//默认头像没有回调 怀疑和http有关
                    yalla.File.groupLoad(url, Laya.Handler.create(this, (e) => {
                        if (!!e) {
                            this._faceUrl = url;
                            this.setFaceSkin(url);;
                        }
                    }));
                })
            } else {
                this._ui['faceImg'].skin = defaultUrl;
            }
        }

        public setFaceSkin(url: string) {
            if (this._ui['faceImg'].skin != url) {
                this._ui['faceImg'].skin = url;
                yalla.util.centerSetup(this._ui['faceImg'], 118, 22);
            }
        }

        /**
         * 显示头像框
         * 有头像动效则显示，否则只显示头像框
         */
        private showFaceBox() {
            var faceId = this._data.fPlayerInfo.faceId;
            if (faceId > 0) this._faceAni = faceId + '';

            if (yalla.Skin.instance.isDynamic(faceId)) {
                yalla.event.YallaEvent.instance.once(`faceframe_${faceId}.sk`, this, () => {
                    if (this.ui.destroyed) return;
                    this._ui['faceBox'].visible = false;
                    this._faceAni = 0;
                    this.initSK(yalla.File.filePath + `faceframe_${faceId}.sk`);
                })
                yalla.event.YallaEvent.instance.once(`faceframe_${faceId}.png`, this, () => {
                    yalla.File.getFileByNative(`faceframe_${faceId}.sk`)
                })
                yalla.File.getFileByNative(`faceframe_${faceId}.png`);
            } else {
                yalla.event.YallaEvent.instance.once(`face_${faceId}.png`, this, () => {
                    if (this.ui.destroyed) return;
                    this._ui['faceBox'].visible = true;
                    this._ui['faceBox'].skin = yalla.File.filePath + `face_${faceId}.png`;
                })
                yalla.File.getFileByNative(`face_${faceId}.png`)
            }
        }

        public playFaceBoxAni(): void {
            if (this._faceAni == 0 || (this._faceAni && this._faceAni.length > 0)) {
                this._faceSk && this._faceSk.play(this._faceAni, true);
            }
        }

        public stopFaceBoxAni(): void {
            this._faceSk && this._faceSk.stop();
        }

        /**
         * 屏蔽聊天
         */
        public updateChatOff(isChatOff: number): void {
            this._ui['chatOff'].visible = isChatOff;
        }
        // public updateChatOffPos():void{
        //     if(this._ui['chatOff'].visible && yalla.Font.lan == 'ar'){
        //         var leftP = this._ui['chatIcon'].left;
        //         this._ui['chatIcon'].left = NaN;
        //         this._ui['chatIcon'].right = leftP;
        //         this._ui['chatTxt'].left = leftP;
        //     }
        // }

        private _faceUrl: string = '';
        get faceUrl(): string {
            return this._faceUrl;
        }

        public get ui(): Laya.View {
            return this._ui;
        }

        public clear(): void {
            if (this._facetemplet) {
                this._facetemplet.offAll();
                this._facetemplet.destroy();
                this._facetemplet = null;
            }
            if (this._faceSk) {
                this._faceSk.offAll();
                this._faceSk.destroy();
            }
            this._faceAni = null;
        }
    }
}