module yalla.view.game {
    /**
     * 四方玩家头像信息包含音频
     */
    export class PlayerGameHead {
        private _ui: Laya.Box;
        private _faceUI: Laya.View;
        private _voiceBtn: Laya.Button;
        private _cdAni: yalla.common.CdAni;
        private _voiceAni: Laya.Animation;
        private _timeLine: Laya.TimeLine;

        public gameHead: yalla.view.game.GameHead;

        private _data: playerShowInfo;
        private _isMute: boolean = false;
        private _isPlay: boolean = false;
        private _color: number = 0;
        private _tmpScale: number = 0;

        constructor(ui: Laya.Box, voiceUrl: string) {
            this._ui = ui;
            this._faceUI = this._ui['face'];
            this._voiceBtn = this._ui['voice'];
            this.initUI(voiceUrl);
            this.initEvent();
        }

        private initUI(voiceUrl: string): void {
            if (!this.gameHead) {
                this.gameHead = new yalla.view.game.GameHead(this._faceUI);
                this._tmpScale = this._faceUI.scaleX;
            }

            if (!this._voiceAni) {
                this._voiceAni = new Laya.Animation();
                this._voiceAni.source = voiceUrl;
                // this._voiceAni.stop();
                this._voiceAni.play(0, false, "stop");
                this._voiceAni.pos(10, 2);
                this._voiceBtn.addChild(this._voiceAni);
                this._voiceBtn.visible = false;
            }
        }

        private initEvent(): void {
            this._faceUI.on(Laya.Event.CLICK, this, this.onClickRole);
            this._ui['roleSp'].on(Laya.Event.CLICK, this, this.onClickRole);
            this._voiceBtn.on(Laya.Event.CLICK, this, this.onClickVoice);
            yalla.Mute.event.on(yalla.Mute.UNMUTE, this, this.handleUnMute);
            yalla.Mute.event.on(yalla.Mute.MUTE, this, this.handleMute);
        }

        private onClickRole(e: Laya.Event): void {
            e.stopPropagation();
            if (this._data) {
                yalla.Sound.playSound('click');
                //check face
                this.gameHead.setFaceSkin(this.gameHead.faceUrl);
                // this.gameHead.loadFace(this._data);

                //TODO::1.3.2   2.点击个人信息头像框 会和互动表情窗口互斥
                if (yalla.common.InteractiveGift.Instance.visible) {
                    yalla.common.InteractiveGift.Instance.hideGiftView();
                } else {
                    yalla.Native.instance.showUserProfile(this._data.fPlayerInfo.idx);
                }
                // var view;
                // if (this._data.fPlayerInfo.idx == yalla.data.UserService.instance.user.idx) {
                //     view = yalla.common.playerinfo.MyInfo.instance;
                //     yalla.DialogManager.instance.open(view, true, true, true, (e: Laya.Event)=> {});
                // } else {
                //     view = yalla.common.playerinfo.OtherInfo.instance;
                //     yalla.DialogManager.instance.open(view, true, true, true, (e: Laya.Event)=> {});
                // }
                // //onLogin后的进入房间，玩家数据会更新
                // this._data = yalla.data.RoomService.instance.room.getPlayerByIdx(this._data.fPlayerInfo.idx);
                // if(this._data && view){
                //     view.setData(this._data);
                // }
            }
        }

        private onClickVoice(e: Laya.Event): void {
            e.stopPropagation();

            var idx = this._data.fPlayerInfo.idx;
            if (idx == yalla.data.UserService.instance.user.idx) return;
            var isMute = yalla.Mute.muted(idx);
            if (isMute) return;

            if (yalla.Voice.instance.muted(idx)) {
                yalla.Native.instance.mobClickEvent(buryPoint.DGAME_USER_OPENMIC);
                yalla.Voice.instance.muteRemoteAudioStream(idx, false, (val, uid) => {
                    if (val == 0) {
                        this._isMute = false;
                        this.pause();
                    }
                })
            } else {
                yalla.Native.instance.mobClickEvent(buryPoint.DGAME_USER_QUIETMIC);
                yalla.Voice.instance.muteRemoteAudioStream(idx, true, (val, uid) => {
                    if (val == 0) {
                        Laya.timer.clear(this, this.pause);
                        this._isMute = true;
                        this._voiceAni.play(0, false, "stop");
                    }
                })
            }
        }

        private handleUnMute(idx: number): void {
            if (idx == this._data.fPlayerInfo.idx) {
                yalla.Voice.instance.muteRemoteAudioStream(idx, false, (val, uid) => {
                    this.unMute();
                })
            }
        }

        private handleMute(idx: number): void {
            if (idx == this._data.fPlayerInfo.idx) {
                yalla.Voice.instance.muteRemoteAudioStream(idx, true, (val, uid) => {
                    this.mute();
                })
            }
        }

        public setData(data: playerShowInfo): void {
            this._data = data;
            // this.initMuteRemoteAudioStream(data);
            this.setGameHead(data);
            this._voiceBtn.visible = true;
        }

        // public initMuteRemoteAudioStream(data: playerShowInfo): void {
        //     var idx = data.fPlayerInfo.idx;
        //     this._isMute = yalla.Mute.muted(data.fPlayerInfo.idx);
        //     //如果已经被屏蔽的玩家，需要禁言
        //     if (this._isMute) {
        //         yalla.Voice.instance.muteRemoteAudioStream(idx, true, (val) => {
        //             if (val == 0) {
        //                 Laya.timer.clear(this, this.pause);
        //                 this._voiceAni.play(0, false, "stop");
        //                 this._voiceBtn.visible = true;
        //             }
        //         })
        //     } else {
        //         this._voiceBtn.visible = true;
        //         this.updateVoiceState();
        //     }
        // }

        public updateVoiceState(): void {
            if (this._data && this._data.fPlayerInfo.idx == yalla.data.UserService.instance.user.idx) {
                if (yalla.data.VoiceService.instance.voiceOpen) {
                    this._voiceAni.play(0, false, "pause");
                    this._isPlay = false;
                } else {
                    this._voiceAni.play(0, false, "stop");
                }
            } else {
                this._voiceAni.play(0, false, "pause");
                this._isPlay = false;
            }
        }

        public setGameHead(data: playerShowInfo): void {
            this._data = data;

            if (!this._cdAni) {
                this._cdAni = new yalla.common.CdAni();
                var [r, room] = [this._faceUI['faceImg'].width / 2 - 4, yalla.data.RoomService.instance.room];
                var roundTime = room.playerRoundTime ? room.playerRoundTime : 10000;
                if (room.roundTime) roundTime = room.roundTime;
                // if(roundTime > 1000) roundTime = Math.round(roundTime/1000);
                this._cdAni.init(r, roundTime);
                // this._faceUI['faceImg'].addChild(this._cdAni);
                var index = this._faceUI.numChildren - 2;
                index = index > 0 ? index : 0;
                this._cdAni.pos(24, 24);
                this._faceUI.addChildAt(this._cdAni, index);
            }
            this.gameHead.setData(data);
        }

        public get isInCD(): boolean {
            return this._cdAni.InCD;
        }


        public playCdAni(): void {
            var room = yalla.data.RoomService.instance.room;
            var roundTime = room.playerRoundTime ? room.playerRoundTime : 10000;
            var overMs = 0;
            if (room.roundTime) roundTime = room.roundTime;
            // if(roundTime > 1000) roundTime = Math.round(roundTime/1000);
            // yalla.Debug.log(this._data.fPlayerInfo.idx + "--" + room.roundLeftTime + ":roundLeftTime=======playCdAni========roundTime:" + roundTime);
            if (room.roundLeftTime > 0) {
                overMs = roundTime - room.roundLeftTime;
                this._cdAni.resetParams(roundTime, overMs);
                this._cdAni.start(0, overMs);
            } else {
                this._cdAni.end();
            }
            // if(room.roundLeftTime > 0) {
            //     overMs = roundTime - room.roundLeftTime;
            //     this._cdAni.resetParams(roundTime, overMs);
            // }else{
            //     this._cdAni.ms = roundTime;
            // }
            // this._cdAni.start(0, overMs);
        }

        /**
         * 支持cd增加时间
         */
        public updateCDAni(): void {
            if (!this._cdAni) return;
            this._cdAni.end();

            var room = yalla.data.RoomService.instance.room;
            var roundTime = room.playerRoundTime ? room.playerRoundTime : 10000;
            if (room.roundTime) roundTime = room.roundTime;
            // if(roundTime > 1000) roundTime = Math.round(roundTime/1000);
            var overMs = roundTime - room.roundLeftTime;
            this._cdAni.resetParams(roundTime, overMs);
            this._cdAni.start(0, overMs);
        }

        public endCdAni(): void {
            this._cdAni.end();
        }

        public playVoice(): void {
            if (this._isMute) {
                this.mute();
                return;
            }
            Laya.timer.clear(this, this.pause);
            if (!this._isPlay) this._voiceAni.play(0, true, "play");
            this._isPlay = true;
            Laya.timer.once(500, this, this.pause);
        }

        public pause(): void {
            if (!this._isMute) {
                this.updateVoiceState();
            }
        }
        public mute(): void {
            this._isMute = true;
            this.pause();
            Laya.timer.clear(this, this.pause);
            this._voiceAni.play(0, false, "stop");
        }
        public unMute(): void {
            this._isMute = false;
            this.pause();
        }

        public get voiceAni(): Laya.Animation {
            return this._voiceAni;
        }

        public get color(): number {
            return this._color;
        }

        /**
         * 轮到玩家出牌，头像放大
         * 头像框动画停止
         */
        public playRoleAni(): void {
            if (yalla.Global.game_state != yalla.data.GameState.State_Sleep) {
                this.gameHead.stopFaceBoxAni();

                if (yalla.data.RoomService.instance.room.roundLeftTime > 0) {
                    if (!this._timeLine) {
                        this._timeLine = new Laya.TimeLine();
                        this._timeLine.addLabel("big", 0).to(this._faceUI, { scaleX: 1.42 * this._tmpScale, scaleY: 1.42 * this._tmpScale }, 350, null, 0)
                            .addLabel("recover", 0).to(this._faceUI, { scaleX: 0.7 * this._tmpScale, scaleY: 0.7 * this._tmpScale }, 200, null, 0)
                            .addLabel("recover", 0).to(this._faceUI, { scaleX: this._tmpScale, scaleY: this._tmpScale }, 150, null, 0);
                    }
                    this._timeLine.play('big', false);
                }
                Laya.timer.once(900, this, this.hideRoleAni);
            }
        }

        private hideRoleAni(): void {
            if (this._faceUI.scaleX != this._tmpScale || this._faceUI.scaleY != this._tmpScale) {
                this._faceUI.scale(this._tmpScale, this._tmpScale);
            }
            this.gameHead.playFaceBoxAni();
        }

        public hide(): void {
            this._timeLine && this._timeLine.pause();
            Laya.timer.clear(this, this.hideRoleAni);
            this.hideRoleAni();
        }

        private _trustee: boolean = false;
        set trustee(val: boolean) {//是否显示托管
            if (this._trustee == val) return;
            this._trustee = val;
            if (this._trustee) {
                this.gameHead.setFaceSkin("public/Robot.png");
                // this.removeEvent();
            } else {
                this.gameHead.setFaceSkin(this.gameHead.faceUrl);
                // this.addEvent();
            }
        }
        get trustee(): boolean {
            return this._trustee;
        }

        public reset(): void {
            Laya.timer.clear(this, this.pause);
        }

        private removeEvent(): void {
            this._timeLine && this._timeLine.pause();
            this.gameHead && this.gameHead.stopFaceBoxAni();
            this._faceUI.off(Laya.Event.CLICK, this, this.onClickRole);
            this._ui['roleSp'].off(Laya.Event.CLICK, this, this.onClickRole);
            this._voiceBtn.off(Laya.Event.CLICK, this, this.onClickVoice);
            yalla.Mute.event.off(yalla.Mute.UNMUTE, this, this.handleUnMute);
            yalla.Mute.event.off(yalla.Mute.MUTE, this, this.handleMute);
            Laya.timer.clear(this, this.pause);
            Laya.timer.clear(this, this.hideRoleAni);
            Laya.timer.clearAll(this);
        }

        public clear(): void {
            this.removeEvent();
            this._voiceAni && this._voiceAni.destroy(true);
            this._cdAni && this._cdAni.clear();
            this.gameHead && this.gameHead.clear();
        }
    }
}