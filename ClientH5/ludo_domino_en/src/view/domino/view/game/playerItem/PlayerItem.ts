module yalla.view.game {
    /**
     * 四方玩家（包括各自手牌）
     */
    export class PlayerItem {
        private _ui: Laya.Box;
        private _playerUI: Laya.Box;
        private _roleItem: PlayerRoleItem;
        private _handCard: HandCardView;
        private _playCardFunc: Function;
        private _bubble: yalla.common.Bubble;
        public btnGift: Laya.Image;
        public btnVoice: Laya.Button;
        public imgGift: Laya.Image;

        public data: playerShowInfo = null;
        public myPlayer: playerShowInfo = null;
        public indexType: number = 0;
        private _giftPos: Laya.Point = null;
        private _isSpectator: boolean = false;

        constructor(ui: Laya.Box, playCardFunc: Function, _isSpectator: boolean = false) {
            this._ui = ui;
            this._playCardFunc = playCardFunc;
            this._isSpectator = _isSpectator;
            this.initUI();
            this.initEvent();
        }

        private initUI(): void {
            this._playerUI = this._ui.getChildByName('player') as Laya.Box;

            this.indexType = parseInt(this._ui.name);
            this._roleItem = new PlayerRoleItem(this._playerUI, this.indexType);
            this._handCard = new HandCardView(this._ui, this._ui.parent.parent['applyBox'], this.indexType, this._playCardFunc, this._isSpectator);

            this.btnGift = this._playerUI['face']['btn_gift'];
            this.imgGift = this._playerUI['face']['img_gift'];

            var side = this.indexType;
            if (this.indexType == 1) side = 3;
            if (this.indexType == 3) side = 0;
            this._bubble = new yalla.common.Bubble(side);
            // this._bubble.skinId = 12002;
            this._ui.parent.addChild(this._bubble);
            if (this.indexType == 1 || this.indexType == 2) {
                this.btnGift.pos(8, 8);
            }
        }
        private initEvent(): void {
            this.btnGift.on(Laya.Event.CLICK, this, this.onClickGift);
        }

        public getPlayerPos(): Laya.Point {
            return this._playerUI.localToGlobal(new Laya.Point());
        }

        public frameLoopName(): void {
            this._roleItem && this._roleItem.frameLoopName();
        }

        public setData(data: playerShowInfo, myPlayer: playerShowInfo): void {
            this._ui.visible = true;
            if (!data) return;
            this.data = data;
            this.myPlayer = myPlayer;
            var nameLen: number = this.getNameLen(data);
            this._roleItem.setData(data, nameLen);
            if (data.talkSkinId) this._bubble.skinId = data.talkSkinId;

            if (this._isSpectator) {
                this._playerUI['voice'].visible = false;
            } else {
                var isChatOff = data.isInChatOff;
                if (this.indexType == 0) {
                    isChatOff = ludo.muteSpectator.isMute;
                    this._playerUI['voice'].visible = !yalla.Global.Account.isLimitVisitor
                }
                this.updateChatOff(isChatOff);
            }
        }

        public updateData(data: playerShowInfo, myPlayer: playerShowInfo) {
            this._ui.visible = true;
            if (!data) return;
            this.data = data;
            this.myPlayer = myPlayer;
            var nameLen: number = this.getNameLen(data);
            this._roleItem.updateName(data, nameLen);
            if (data.talkSkinId) this._bubble.skinId = data.talkSkinId;

            if (this._isSpectator) {
                this._playerUI['voice'].visible = false;
            } else {
                var isChatOff = data.isInChatOff;
                if (this.indexType == 0) {
                    isChatOff = ludo.muteSpectator.isMute;
                    this._playerUI['voice'].visible = !yalla.Global.Account.isLimitVisitor;
                }
                this.updateChatOff(isChatOff);
            }
        }

        public getNameLen(data: playerShowInfo): number {
            var len = 13;
            if (data && data.fPlayerInfo) {
                var vl = data.fPlayerInfo.viplevel;
                var rl = data.roylevel;
                if (this.indexType == 1) {
                    if (vl > 0 && rl > 0) len = 4;
                    else if (vl < 1 && rl < 1) len = 10;
                    else len = 8;

                } else if (this.indexType == 2) {
                    if (vl > 0 && rl > 0) len = 8;
                    else if (vl > 0 || rl > 0) len = 10;
                } else if (this.indexType == 3) {
                    if (vl > 0 && rl > 0) len = 8;
                    else if (vl > 0 || rl > 0) len = 10;
                }
            }
            return len;
        }

        /**
         * 更新积分
         * @param score 
         */
        public updateScore(score: number): void {
            this._roleItem.updateScore(score);
        }
        /**
         * 语音动画
         */
        public playVoice(): void {
            this._roleItem.playVoice();
        }
        /**
         * 打开音频
         */
        public enableAudio(): void {
            this._roleItem.enableAudio();
        }
        /**
         * 关闭音频
         */
        public disableAudio(): void {
            this._roleItem.disableAudio();
        }

        public stopAni(): void {
            this._roleItem.stopAudioAni();
            this._roleItem.stopFaceAni();
        }
        /**
         * 聊天气泡
         * @param msg 
         */
        public showChatBox(msg: any): void {
            this._bubble.show();
            this._bubble.onChat(msg);
            // this._bubble.chat = msg.msg;
            var p: Laya.Point = this._playerUI.localToGlobal(new Laya.Point());
            var [x, y, disX, disY] = [0, 0, 34, 20];
            switch (this.indexType) {
                case 0:
                    [x, y] = [p.x + this._playerUI.width / 4, p.y - disY];
                    break;
                case 1:
                    [x, y] = [p.x + this._playerUI.width / 2 - disX, p.y - disY];
                    break;
                case 2:
                    [x, y] = [p.x + this._playerUI.width / 2, p.y + this._playerUI.height - 10];
                    break;
                case 3:
                    [x, y] = [p.x + this._playerUI.width / 2 + disX, p.y - disY];
                    break;
            }
            this._bubble.pos(x, y);
            this._bubble.zOrder = 990;
        }

        public get isInCD(): boolean {
            return this._roleItem.isInCD;
        }
        /**
         * cdAni
         * 角色操作倒计时
         */
        public playAni(): void {
            this._roleItem.playAni();
        }
        /**
         * 延长cd，需要更新
         */
        public updateCDAni(): void {
            this._roleItem.updateCDAni();
        }

        /**
         * 屏蔽聊天
         */
        public updateChatOff(isChatOff: number): void {
            if (!isChatOff) isChatOff = 0;
            this._roleItem.updateChatOff(isChatOff);
        }

        /**
         * fplayerInfo如果有giftId，需要在头像上方显示礼物
         */
        public showGift(giftId: number, icon: string): void {
            if (giftId > 0) {
                console.log(!this._isSpectator);

                this.btnGift.visible = !this._isSpectator;

                var path = `${yalla.File.cachePath}/giftIcon_${giftId}`;
                yalla.Debug.log(yalla.File.existed(`${path}.png`) + '==DominoPlayerItem gift===' + path);
                if (yalla.File.existed(`${path}.png`)) {
                    this.imgGift.skin = yalla.File.filePath + `giftIcon_${giftId}.png`;
                } else {
                    this.imgGift.skin = icon;
                    // yalla.event.YallaEvent.instance.once(`giftIcon_${giftId}.png`, this, () => {
                    //     this.updateSkin(giftId);
                    // })
                    // yalla.File.getFileByNative(`giftIcon_${giftId}.png`);
                }
                // this.imgGift.skin = icon;//`test/${giftId}.png`;
                this.imgGift.pivot(95 / 2, 95 / 2).pos(33, 33);
                this.imgGift.scale(0.9, 0.9);
            }
        }
        // private updateSkin(giftId: number): void {
        //     var path = yalla.File.cachePath + `/giftIcon_${giftId}.png`
        //     if (yalla.File.existed(path)) {
        //         this.imgGift.skin = yalla.File.filePath + `giftIcon_${giftId}.png`;
        //         // this.imgGift.pivot(95 / 2, 95 / 2).pos(22, 22);
        //         // this.imgGift.scale(0.75, 0.75);
        //     }    
        // }

        /**
         * 结束倒计时
         */
        public endAni(): void {
            this._roleItem.endAni();
        }

        public hide(): void {
            this._roleItem.hide();
        }

        get isIssueTween(): boolean {
            return this._handCard.isIssueTween;
        }
        /**
         * 发牌
         */
        public issueCard(arr: Array<Domino>, isApply: boolean = false, playAni: boolean = true): void {
            if (this._handCard) {
                this._handCard.data = this.myPlayer;
                this._handCard.issueCard(arr, isApply, playAni);
            }
        }

        /**
         * 筛选出有哪些牌可以出（只有自己才需处理）
         */
        public filterPlayCard(waitPlayCardHash: Object): void {
            this._handCard && this._handCard.filterPlayCard(waitPlayCardHash);
        }

        /** 
         * 出牌
         */
        public removeHandCard(d: Domino, isAni: boolean = true): any {
            var cardItem: any = this._handCard.removeHandCard(d, isAni);
            return cardItem;
        }

        /**
         * 可选择的手牌区域切换，需要重置之前牌的状态
         * @param id 
         */
        public recoverState(id: number): void {
            this._handCard && this._handCard.recoverState(id);
        }

        /**
         * 所有手牌锁定
         */
        public disableOpera(isForced: boolean = false): void {
            this._handCard && this._handCard.disableOpera(isForced);
        }

        /**
         * 校验手牌位置
         */
        public checkCardPos(): void {
            this._handCard && this._handCard.checkCardPos();
        }

        public get myCardItemList(): Array<CardItem> {
            if (this._handCard) return this._handCard.myCardItemList;
            return [];
        }

        public reset(): void {
            this._roleItem.reset();
            this._handCard && this._handCard.reset();
        }

        public get color(): number {
            return this._roleItem.color;
        }
        public get ui(): Laya.Box {
            return this._ui;
        }
        public get playerUI(): Laya.Box {
            return this._playerUI;
        }

        public get roleItem(): PlayerRoleItem {
            return this._roleItem;
        }


        /**
         * 互动礼物
         */
        private onClickGift(e: Event): void {
            e.stopPropagation();
            if (!yalla.common.InteractiveGift.Instance.displayedInStage) {
                // var p = this._ui.parent.parent.parent;
                // p.addChild(yalla.common.InteractiveGift.Instance);
                // yalla.common.InteractiveGift.Instance.zOrder = 991;
                this._ui.parent.parent['giftBox'].addChild(yalla.common.InteractiveGift.Instance);
            }
            if (!this._giftPos) {
                this._giftPos = this._playerUI['face'].localToGlobal(new Laya.Point());
                this._giftPos.y = this._giftPos.y - 18;
            }

            var isShow = yalla.common.InteractiveGift.Instance.visible;
            if (isShow) yalla.common.InteractiveGift.Instance.hideGiftView();
            else yalla.common.InteractiveGift.Instance.showGiftView(this.indexType, this._giftPos, this.data);

        }

        public clear(): void {
            this._ui.offAll();
            this._roleItem && this._roleItem.clear();
            this._handCard && this._handCard.clear();
            this._bubble && this._bubble.clear();
        }
    }
}