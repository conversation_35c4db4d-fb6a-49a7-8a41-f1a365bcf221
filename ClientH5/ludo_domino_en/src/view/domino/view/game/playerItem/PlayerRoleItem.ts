module yalla.view.game {
    /**
     * 四方玩家详细信息包含音频,名称，分数
     */
    export class PlayerRoleItem {
        private _ui: Laya.Box;
        private _vipIcon: Laya.Image;
        private _royalIcon: Laya.Image;
        private _nameTxt: Laya.Label;
        private _scoreTxt: Laya.Label;
        private _scoreImg: Laya.Image;
        private _gameHead: PlayerGameHead;
        private _nameItem: yalla.common.NameItem;

        private _defaultNameTxtX = 0;
        private _indexType: number = 0;
        private _data: playerShowInfo = null;
        private _vipKey: string = '';
        private _royalKey: string = '';
        public VipName: string = "vip/xunzhang";
        public RoyalName: string = "royallevel/huangguan";
        /**
         * @param ui 
         */
        constructor(ui: Laya.Box, indexType: number) {
            this._ui = ui;
            this._vipKey = this.VipName + indexType;
            this._royalKey = this.RoyalName + indexType;
            this.initUI(indexType);
            this.initEvent();
        }

        private initUI(indexType: number): void {
            var voiceUrl = 'publics/ani/ani_voice_domino.ani';
            if (indexType == 0) voiceUrl = 'publics/ani/ani_voice_me.ani';
            this._indexType = indexType;
            this._gameHead = new PlayerGameHead(this._ui, voiceUrl);
            this._vipIcon = this._ui['vipIcon'];
            this._royalIcon = this._ui['royalIcon'];
            this._scoreTxt = this._ui['scoreTxt'];
            this._scoreImg = this._ui['scoreImg'];

            this._nameTxt = this._ui['nameTxt'];
            this._defaultNameTxtX = this._nameTxt.x;
        }
        private initEvent(): void {
            yalla.SkeletonManager.instance.on(this._vipKey, this, this.playVipAni);
            yalla.SkeletonManager.instance.on(this._royalKey, this, this.playRoyalAni);
        }

        public frameLoopName(): void {
            if (this._nameItem) {
                this._nameItem.frameLoopName();
            }
        }

        public setData(data: playerShowInfo, nameLen: number): void {
            this._data = data;
            this._gameHead.setData(data);

            var tempX = this._defaultNameTxtX;
            var royalLevel = yalla.util.getRoyalLevel(data);
            if (royalLevel > 0 && !this._royalIcon.visible) {
                yalla.SkeletonManager.instance.initSk(this.RoyalName, this._royalKey);
                this._royalIcon.visible = true;
                tempX += this._royalIcon.width;
            }
            if (data.fPlayerInfo.viplevel > 0 && !this._vipIcon.visible) {
                yalla.SkeletonManager.instance.initSk(this.VipName, this._vipKey);
                this._vipIcon.visible = true;
                this._vipIcon.x = tempX;
                tempX += this._vipIcon.width;
            }
            this._nameTxt.x += tempX;
            this._ui['nameItem'].x = this._nameTxt.x;
            if (tempX > 0 || data.realRoyLevel >= 4) this._nameTxt.align = 'left'; //TODO::扫光遮照效果需要文本左对齐
            // var name = yalla.util.filterName(this._data.fPlayerInfo.nikeName, nameLen);
            // yalla.Emoji.lateLable(name, this._nameTxt, ['color', 'fontSize']);

            if (!this._nameItem) {
                var moveDis = this._ui.width - tempX;
                this._nameItem = new yalla.common.NameItem(this._ui['nameItem'], this._nameTxt);
                this._nameItem.gameName_domino(data, nameLen, this._indexType == 1 ? moveDis : 0);//, this._indexType ? 158 : 122);
            }
        }


        public updateName(data: playerShowInfo, nameLen: number) {
            this._data = data;
            this._gameHead.setGameHead(data);

            var tempX = 0;
            var royalLevel = yalla.util.getRoyalLevel(data);
            if (royalLevel > 0 && !this._royalIcon.visible) {
                this._royalIcon.visible = true;
                tempX += this._royalIcon.width + 2;
            }
            if (data.fPlayerInfo.viplevel > 0 && !this._vipIcon.visible) {
                this._vipIcon.visible = true;
                this._vipIcon.x = tempX;
                tempX += this._vipIcon.width + 2;
            }
            this._nameTxt.x += tempX;
            this._ui['nameItem'].x = this._nameTxt.x;
            if (tempX > 0) this._nameTxt.align = 'left';
            // var name = yalla.util.filterName(this._data.fPlayerInfo.nikeName, nameLen);
            // yalla.Emoji.lateLable(name, this._nameTxt, ['color', 'fontSize']);
            if (!this._nameItem) {
                var moveDis = this._ui.width - tempX;
                this._nameItem = new yalla.common.NameItem(this._ui['nameItem'], this._nameTxt);
                this._nameItem.gameName_domino(data, nameLen, this._indexType == 1 ? moveDis : 0);
            }
        }

        private playVipAni(sk: Laya.Skeleton): void {
            if (!this._data || this._data.fPlayerInfo.viplevel < 1) return;
            this._vipIcon.addChild(sk);
            sk.pos(this._vipIcon.width, this._vipIcon.height / 2);
            sk.play("vip_" + this._data.fPlayerInfo.viplevel, true);
        }

        private playRoyalAni(sk: Laya.Skeleton): void {
            if (!this._data) return;
            var royalLevel = yalla.util.getRoyalLevel(this._data);
            if (royalLevel < 1) return;
            yalla.Debug.log(this._data.fPlayerInfo.idx + "==========royalLevel=" + royalLevel);
            this._royalIcon.addChild(sk);
            sk.pos(this._royalIcon.width, this._royalIcon.height / 2);
            //目前r+5等级标识和rl5一样
            let rlAnimationLv: number = royalLevel > 5 ? 5 : royalLevel;
            sk.play("R" + rlAnimationLv, true);
        }

        public updateScore(score: number): void {
            this._scoreTxt.text = score + '';
        }

        public get isInCD(): boolean {
            return this._gameHead.isInCD;
        }

        public get playerGameHead(): PlayerGameHead {
            return this._gameHead;
        }

        public playAni(): void {
            this._gameHead.playRoleAni();
            this._gameHead.playCdAni();
        }

        public updateCDAni(): void {
            this._gameHead.updateCDAni();
        }

        /**
         * 屏蔽聊天
         */
        public updateChatOff(isChatOff: number): void {
            this._gameHead.gameHead.updateChatOff(isChatOff);
        }

        public endAni(): void {
            this._gameHead.endCdAni();
        }

        public hide(): void {
            this._gameHead.hide();
        }

        public playVoice(): void {
            this._gameHead.playVoice();
        }

        public enableAudio(): void {
            this._gameHead.voiceAni.play(0, false, "pause");
        }

        public disableAudio(): void {
            this._gameHead.voiceAni.play(0, false, "stop");
        }

        public stopAudioAni(): void {
            this._gameHead.voiceAni && this._gameHead.voiceAni.gotoAndStop(0);
        }
        public stopFaceAni(): void {
            this._gameHead.gameHead && this._gameHead.gameHead.stopFaceBoxAni();
        }

        public showName() {
            this._ui['nameBox'].visible = true;
        }
        public hideName() {
            this._ui['nameBox'].visible = false;
        }

        public get data(): playerShowInfo {
            return this._data;
        }

        public get color(): number {
            return this._gameHead.color;
        }

        public reset(): void {
            this._gameHead.reset();
        }

        public clear(): void {
            this._gameHead.clear();
            yalla.SkeletonManager.instance.off(this._vipKey, this, this.playVipAni);
            yalla.SkeletonManager.instance.off(this._royalKey, this, this.playRoyalAni);
            yalla.SkeletonManager.instance.clearByName(this._vipKey);
            yalla.SkeletonManager.instance.clearByName(this._royalKey);
        }
    }
}