module yalla.view.result {
    export class Result extends ui.domino.resultUI {

        private _backFunc: Function;
        private _playAgainFunc: Function;

        private _templet: Laya.Templet;
        private _sk: Laya.Skeleton;
        private _gameHeadHash: Object = {};
        private _nameItemHash: Object = {};

        private _data: any;
        private _isWin: boolean = false;
        private backBtnName = 'Back';

        constructor(msg: any, back: Function, playAgain: Function) {
            super();
            yalla.util.swapPos(this.again, this.back);
            this._data = msg.gResut;
            this._backFunc = back;
            this._playAgainFunc = playAgain;
            this._isWin = (this._data.winnerId == yalla.data.UserService.instance.user.idx);
            this.name = 'resultView';
            this.initUI();
            this.initEvent();

            if (yalla.Global.Account.roomid) yalla.util.clogDBAmsg('200019', { roomid: yalla.Global.Account.roomid }, 3);
        }

        private initUI() {
            var loadCount = 0;
            var skName = (this._isWin || yalla.Global.Account.leagueWatch) ? 'result/win' : 'result/lose';
            this._templet = new Laya.Templet();
            this._templet.on(Laya.Event.COMPLETE, this, this.parseComplete);
            this._templet.on(Laya.Event.ERROR, this, () => {
                if (loadCount < 4) this._templet.loadAni(yalla.getSkeleton(skName));
                loadCount++;
            })
            this._templet.loadAni(yalla.getSkeleton(skName));

            this.playAni(this._data);
            var room = yalla.data.RoomService.instance.room;
            var isPrivate = room.isPrivate;
            if (room.player && room.player.player) {
                var len = room.player.player.length;
                if (isPrivate == GameRoomType.CHAMPION || isPrivate == GameRoomType.LEAGUE) {
                    this.again.visible = false;
                    this.back.centerX = 0;
                    this.back.y = (this.back.y - (4 - len) * (125 + 14));
                    this.back.scale(0, 0);
                    this.back.label = 'Back';
                }
                else {
                    this.back.y = this.again.y = (this.back.y - (4 - len) * (125 + 14));
                    this.back.scaleX = this.back.scaleY = 0;
                    this.again.scaleX = this.again.scaleY = 0;
                    if (isPrivate == GameRoomType.VIP) {
                        this.again.label = 'Share';
                    } else {
                        this.again.label = 'Play Again';
                    }
                }
            }

            if (this._isWin) {
                this.titleTxt.strokeColor = '#a62619';
            } else {
                this.titleTxt.color = '#ffffff';
                this.titleTxt.strokeColor = '#297bc5';
            }
            this.titleTxt.stroke = 4;
        }

        private initEvent(): void {
            var isPrivate = yalla.data.RoomService.instance.room.isPrivate;
            if (isPrivate == GameRoomType.CHAMPION || isPrivate == GameRoomType.LEAGUE) {
                if (isPrivate == GameRoomType.LEAGUE) {
                    Laya.timer.once(5000, this, this._backFunc);
                }
                this.back.once("click", this, this.backHander);
            } else {
                Laya.timer.once(10000, this, this._backFunc);
                this.back.once("click", this, this.backHander);
                this.again.once('click', this, this.playagainHander);
            }
        }

        private backHander = yalla.util.withLock(() => {
            var isPrivate = yalla.data.RoomService.instance.room.isPrivate;
            if (isPrivate != GameRoomType.CHAMPION && isPrivate != GameRoomType.LEAGUE) {
                yalla.Native.instance.mobClickEvent(buryPoint.DGAME_BACK);
                Laya.timer.clear(this, this._backFunc);
            }
            this._backFunc();
            if (this.back.label == 'Back' && yalla.Global.Account.roomid) {
                yalla.util.clogDBAmsg('10269', { roomid: yalla.Global.Account.roomid });
            }
        })

        private playagainHander = yalla.util.withLock(() => {
            this._playAgainFunc();
            Laya.timer.clear(this, this._backFunc);
        })

        private parseComplete() {
            this._sk = this._templet.buildArmature(1);
            this._sk.pos(this.topMc.width / 2, this.topMc.height / 2 + 30);
            this.topMc.addChild(this._sk);
            this.topMc.setChildIndex(this._sk, 0);
            this._sk.play(0, false);
            this._sk.on(Laya.Event.STOPPED, this, () => {
                this._sk.play(1, true);
            });
        }

        private playAni(result: any): void {
            var room = yalla.data.RoomService.instance.room;
            var watch = yalla.Global.Account.leagueWatch;
            var str = this._isWin ? 'You Win' : 'You Lose';
            if (watch) str = 'Good game';
            this.titleTxt.text = str;
            var losersInfo: Array<DefineRoundResult> = room.updateRoundResult(result.losersInfo, 'intergral');
            var roleList = [{ idx: result.winnerId, nickName: result.winnerNickName, intergral: result.winnerIntergral }].concat(losersInfo);
            var len = roleList.length;
            for (var i = 0; i < len; i++) {
                this['info_' + i].x = 750;
                this['info_' + i].visible = true;
                Laya.Tween.to(this['info_' + i], { x: 0 }, 500, Laya.Ease.backOut, null, i * 80, true);

                var idx = roleList[i].idx;
                var playerData = room.getPlayerByIdx(idx);
                if (playerData) {
                    if (!this._gameHeadHash[idx]) {
                        var gameHeadUI = new ui.domino.item.gameHeadUI();
                        if (i == 0) gameHeadUI.pos(108, 23).scale(0.7, 0.7);
                        else gameHeadUI.pos(108, 8).scale(0.7, 0.7);
                        this['info_' + i].addChild(gameHeadUI);
                        this._gameHeadHash[idx] = new yalla.view.game.GameHead(gameHeadUI);
                    }
                    this._gameHeadHash[idx].setData(playerData);

                    if (!watch && roleList[i].idx == yalla.data.UserService.instance.user.idx) {
                        this['my_' + i].visible = true;
                    }
                    this['totalTxt_' + i].text = roleList[i].intergral + '';

                    var nameItem: yalla.common.NameItem = this._nameItemHash[i];
                    if (!nameItem) {
                        nameItem = new yalla.common.NameItem(this['nameItem_' + i], this['nikeName_' + i]);
                        this._nameItemHash[i] = nameItem;
                    }
                    nameItem.resultName(playerData, yalla.util.filterName(roleList[i].nickName, 13, "m"));
                    // yalla.Emoji.lateLable(yalla.util.filterName(roleList[i].nickName, 13, "m"), this['nikeName_' + i], ['color', 'fontSize', 'stroke', 'strokeColor']);
                }
            }
            if (room.isPrivate == GameRoomType.PRIVATE || room.isPrivate == GameRoomType.LEAGUE) {
                this.goldExp.visible = false;
            } else {
                this.goldExp.visible = true;
                this.goldBg.visible = this.goldIcon.visible = this.gold_0.visible = result.winGoldNum;

                this.expBg.visible = this.expIcon.visible = this.expTxt.visible = result.winnerExp;
                this.expTxt.text = '+' + result.winnerExp;
            }
            // this.goldExp.visible = !(room.isPrivate == GameRoomType.PRIVATE || room.isPrivate == GameRoomType.LEAGUE) && result.winGoldNum;
            // if (result.winnerExp) {
            //     this.expTxt.text = '+' + result.winnerExp;
            // } else {
            //     this.expBg.visible = this.expIcon.visible = this.expTxt.visible = false;
            // }

            Laya.Tween.to(this.back, { scaleX: 1, scaleY: 1 }, 200, Laya.Ease.backOut, null, len * 80 + 500);
            if (yalla.data.RoomService.instance.room.isPrivate == GameRoomType.CHAMPION) {
                this['gold_0'].text = '+' + yalla.util.filterNum(yalla.data.RoomService.instance.room.cost);
            } else {
                var royalty = yalla.Global.Account.royalty ? yalla.Global.Account.royalty : 0;
                this['gold_0'].text = '+' + yalla.util.filterNum(result.winGoldNum * (1 - royalty / 100));
                Laya.Tween.to(this.again, { scaleX: 1, scaleY: 1 }, 200, Laya.Ease.backOut, null, len * 80 + 500 + 100);
            }

            Laya.timer.once(2500, this, () => {
                if (this.back.scaleX != 1) this.back.scale(1, 1);
                if (this.again.visible && this.again.scaleX != 1) this.again.scale(1, 1);
            })
        }

        public frameLoopName(): void {
            for (var key in this._nameItemHash) {
                var nameItem = this._nameItemHash[key];
                // yalla.Debug.log(key+": "+nameItem.ui.nameMaskSp.x+":"+nameItem.ui.nameMaskSp.y +"=====result==nameTxt.width:"+nameItem.ui.nameTxt.width+"=="+nameItem.ui.nameMaskSp.height);
                nameItem.frameLoopName();
            }
        }

        public backHallClear(): void {
            this.removeEvent();
        }

        private removeEvent(): void {
            Laya.timer.clearAll(this);
            Laya.timer.clear(this, this.playAni);
            Laya.timer.clear(this, this._backFunc);
        }

        public clear(): void {
            this.backHallClear();
            for (var j in this._gameHeadHash) {
                this._gameHeadHash[j].clear();
                delete this._gameHeadHash[j];
            }
            if (this._templet) {
                this._templet.offAll();
                this._templet.destroy();
                this._templet = null;
            }
            if (this._sk) {
                this._sk.offAll();
                this._sk.destroy();
            }
            this.topMc.destroy(true);
            this.removeSelf();
            this.destroy(true);
        }
    }
}