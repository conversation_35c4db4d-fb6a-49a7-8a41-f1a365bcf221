module yalla.view.result {
    /**
     * 单局结束（积分）
     */
    export class RoundDrawResult extends ui.domino.dialog.roundDrawUI  {
        private _cardItemHash:Object = {};
        private _gameHeadHash:Object = {};

        private _isSend: boolean = false;
        private _nameItemHash: Object = {};

        public nameItemPos = [8, 174, 336, 500];
        public nameTxtPos = [131, 297, 459, 623];
        
        constructor() {
            super();
        }

        private initUI(): void  {
            this.bgImg.height = 210 + 163 * yalla.data.RoomService.instance.room.playerNums;
            this.nextTimeTxt.y = this.bgImg.height - this.nextTimeTxt.height - 38;
            this.nextRoundTxt.y = this.nextTimeTxt.y;
            this.content.y = (Laya.stage.height - this.bgImg.height)/2;
        }

        public setData(msg: DominoRoundInfo, cardSkinId: number = 0): void {
            if (!this._nameItemHash) this._nameItemHash = {};
            if (!this._gameHeadHash) this._gameHeadHash = {};
            this.initUI();
            if (msg) {
                this._isSend = false;
                this.nextRoundTxt.x =  190; this.nextTimeTxt.x = 474;
                if (yalla.Font.lan == "urdu") this.roundTxt.text = `دورانیہ  ${msg.roundNum}`;
                else if(yalla.Font.lan == 'ar') this.roundTxt.text = `الجولة  ${msg.roundNum}`;
                else this.roundTxt.text = `ROUND  ${msg.roundNum}`;

                var losersInfo: Array<DefineRoundResult> = yalla.data.RoomService.instance.room.updateRoundResult(msg.losersInfo,'roundIntergral');
                var list = [{ idx: msg.winnerId, intergral: msg.winnerIntergral, roundIntergral: msg.winnerRoundIntergral, domino: msg.winnerDomino }].concat(losersInfo);
                if(yalla.data.RoomService.instance.room.isPrivate != GameRoomType.PRIVATE && msg.winnerExp){
                    this.exp.visible = this.expTxt.visible = true;
                    this.expTxt.changeText('+' + msg.winnerExp);
                } else {
                    this.exp.visible = this.expTxt.visible = false;
                }

                for (var i = 0; i < 4; i++){
                    // var cell = this['item_'+i];
                    var data = list[i];
                    if(data){
                        var idx = data['idx'];
                        var playerData = yalla.data.RoomService.instance.room.getPlayerByIdx(idx);
                        if(!playerData) return;
                        // itemBoxTxt.visible = itemBgImg.visible = cell.visible = bg.visible = true;
                        var itemBgImg = this['item_bg_' + i];
                        var itemBoxTxt = this['box_txt_' + i];
                        var nameItemUI = this['nameItem_' + i];
                        var nameTxt: Laya.Label = this['nameTxt_' + i];
                        itemBoxTxt.visible = itemBgImg.visible = true;

                        var head = this._gameHeadHash[idx];
                        if(!head){
                            var gameHeadUI = new ui.domino.item.gameHeadUI();
                            gameHeadUI.scale(0.8, 0.8);
                            head = new yalla.view.game.GameHead(gameHeadUI);
                            this._gameHeadHash[idx] = head;
                            head.setData(playerData);
                        }
                        if (!head.ui.parent) this.nameItemBox.addChildAt(head.ui, 0);
                        head.ui.pos(34, i * 160);
                       
                        var isMe = playerData.fPlayerInfo.idx == yalla.data.UserService.instance.user.idx ? true:false;
                        itemBgImg.skin = isMe ? yalla.getDomino('bg_oneself'):yalla.getDomino('bg_others');
                        itemBgImg.getChildByName('bg0').skin = isMe ? yalla.getDomino('bg_oneself2'):yalla.getDomino('bg_others2');
                        itemBgImg.getChildByName('bg1').skin = isMe ? yalla.getDomino('bg_oneself2'):yalla.getDomino('bg_others2');
                        itemBoxTxt.getChildByName('numTxt').color = isMe?'#f2a500':'#7ab8a6';
                        
                        var nameItem:yalla.common.NameItem = this._nameItemHash[idx];//TODO::如果传入key=idx，后续有排名发生变化，但是UI表现永远是第一次生成的排序
                        if(!nameItem){
                            nameTxt.color = isMe ? '#fa9300' : '#428371';
                            nameItem = new yalla.common.NameItem(nameItemUI, nameTxt);
                            nameItem.roundName_domino(playerData, yalla.util.filterName(playerData.fPlayerInfo.nikeName,13,"m"));
                            this._nameItemHash[idx] = nameItem;
                        }else {
                            //名字颜色改变，需要重新赋值
                            if (playerData.realRoyLevel < 4) {
                                nameItem.defaultNameTxt.color = isMe ? '#fa9300' : '#428371';
                                yalla.Emoji.lateLable(yalla.util.filterName(playerData.fPlayerInfo.nikeName, 13, "m"), nameItem.defaultNameTxt, ['color', 'fontSize', 'strokeColor']);
                            }
                        }
                        if (msg.winnerId == idx && playerData.realRoyLevel >= 4) {
                            this.exp.x = Math.max(425, 200 + nameItem.ui.nameTxt.width + 40);
                            this.expTxt.x = this.exp.x + 53;
                        }
                        // yalla.Emoji.lateLable(yalla.util.filterName(playerData.fPlayerInfo.nikeName,13,"m"), nameTxt, ['color', 'fontSize', 'stroke','strokeColor']);
                        nameItem.ui.y = this.nameItemPos[i];
                        nameItem.defaultNameTxt.y = this.nameTxtPos[i];
                        // yalla.Debug.log(i +"==="+ playerData.fPlayerInfo.nikeName +"===小局结算信息==nameItemUI.y="+nameItemUI.y+" nameTxt.y");

                        var scoreTxt: Laya.Label = itemBoxTxt.getChildByName('scoreTxt') as Laya.Label;
                        var totalTxt: Laya.Label = itemBoxTxt.getChildByName('totalTxt') as Laya.Label;
                        scoreTxt.changeText(data['roundIntergral'] + '');
                        totalTxt.changeText(data['intergral'] + '');
                        // this.showHandCard(cell.getChildByName('cardList') as Laya.Box, data['domino'], playerData.cardSkinId);
                        this.showHandCard(itemBgImg.getChildByName('cardList'), data['domino'], cardSkinId);
                    }
                }
            }
        }

        private showHandCard(mc: Laya.Box, list: Array<Domino>,cardSkinId:number): void  {
            if (!list) return;
            if (!this._cardItemHash) this._cardItemHash = {};
            var len = list.length;
            var initY = len <= 7 ? 40 : 2;
            for (var i = 0; i < len; i++) {
                var k = list[i].id;
                var cardItem = this._cardItemHash[k];
                if(!cardItem){
                    cardItem = new yalla.view.game.ResultCardItem();
                    this._cardItemHash[k] = cardItem;
                }
                cardItem.updateCard(list[i]);
                cardItem.visible = true;
                cardItem.cardSkinId = cardSkinId;
                if(!cardItem.parent) mc.addChild(cardItem);

                var endX = 10+(cardItem.width + 2) * (i % 7);
                var endY = initY + (cardItem.width * 2 + 10) * Math.floor(i / 7);
                if (yalla.Global.game_state == yalla.data.GameState.State_Sleep) {
                    cardItem.pos(endX, endY);
                } else {
                    cardItem.pos(mc.width+75, endY);
                    Laya.Tween.to(cardItem, { x: endX }, 300, Laya.Ease.strongOut, null, i * 80 + 80, true);
                }
            }
        }

        public frameLoopName(): void{
            for (var key in this._nameItemHash) {
                var nameItem = this._nameItemHash[key];
                nameItem.frameLoopName();
            }
        }

        /**
         * 进入下一轮
         */
        private onClickConfirm(): void  {
            if(!this._isSend){
                this._isSend = true;
                yalla.data.RoomService.instance.sendNexgRound();
            }
        }

        public close(): void{
            for (var k in this._cardItemHash) {
                Laya.Tween.clearAll(this._cardItemHash[k]);
                this._cardItemHash[k].removeSelf();
                Laya.Pool.recover('resultCardItem', this._cardItemHash[k]);
            }
        }

        public clear(): void  {
            for (var k in this._cardItemHash) {
                Laya.Tween.clearAll(this._cardItemHash[k]);
                this._cardItemHash[k].removeSelf();
                delete this._cardItemHash[k];
            }
            for (var j in this._gameHeadHash) {
                this._gameHeadHash[j].clear();
                delete this._gameHeadHash[j];
            }
            this.nameItemBox.removeSelf();
            this.nameItemBox.destroy(true);
            this._nameItemHash = null;
            this._cardItemHash = null;
            this._gameHeadHash = null;
        }
    }
}