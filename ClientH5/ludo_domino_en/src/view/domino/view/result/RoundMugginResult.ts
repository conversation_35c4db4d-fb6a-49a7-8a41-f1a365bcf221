module yalla.view.result {
    /**
     * 单局结束（轮次场）
     */
    export class RoundMugginResult extends ui.domino.dialog.roundMugginUI  {
        private _isSend:boolean = false;
        private _gameHeadHash: Object = {};
        private _nameItemHash: Object = {};
        public nameItemPos = [147, 315, 481, 646];
        public nameTxtPos = [139, 306, 472, 637];

        constructor() {
            super()
        }

        private initUI(): void  {
            this.bgImg.height = 210 + 163 * yalla.data.RoomService.instance.room.playerNums;
            this.nextTimeTxt.y = this.bgImg.height - this.nextTimeTxt.height - 38;
            this.nextRoundTxt.y = this.nextTimeTxt.y;
            this.content.y = (Laya.stage.height - this.bgImg.height)/2;
        }
        
        public setData(msg: DominoRoundInfo, cardSkinId: number = 0): void {
            if (!this._nameItemHash) this._nameItemHash = {};
            if (!this._gameHeadHash) this._gameHeadHash = {};
            this.initUI();
            if (msg) {
                this._isSend = false;
                this.nextRoundTxt.x =  190; this.nextTimeTxt.x = 474;
                if (yalla.Font.lan == "urdu") this.roundTxt.text = `دورانیہ  ${msg.roundNum}`;
                else if(yalla.Font.lan == 'ar') this.roundTxt.text = `الجولة  ${msg.roundNum}`;
                else this.roundTxt.text = `ROUND  ${msg.roundNum}`;

                var losersInfo: Array<DefineRoundResult> = yalla.data.RoomService.instance.room.updateRoundResult(msg.losersInfo,'roundIntergral');
                var list = [{ idx: msg.winnerId, intergral: msg.winnerIntergral, winnerExp:msg.winnerExp, roundIntergral: msg.winnerRoundIntergral, domino: msg.winnerDomino }].concat(losersInfo);
                for (var i = 0; i < 4; i++){
                    var data = list[i];
                    if(data){
                        var idx = data['idx'];
                        var playerData = yalla.data.RoomService.instance.room.getPlayerByIdx(idx);
                        if (!playerData) return;

                        var itemBgImg = this['bg' + i];
                        var cell = this['item_'+i];
                        var nameItemUI = this['nameItem_' + i];
                        var nameTxt: Laya.Label = this['nameTxt_' + i];
                        cell.visible = itemBgImg.visible = true;
                        
                        var head = this._gameHeadHash[idx];
                        if(!head){
                            var gameHeadUI = new ui.domino.item.gameHeadUI();
                            gameHeadUI.scale(0.8, 0.8);
                            head = new yalla.view.game.GameHead(gameHeadUI);
                            this._gameHeadHash[idx] = head;
                            head.setData(playerData);
                        }
                        if (!head.ui.parent) this.nameItemBox.addChildAt(head.ui, 0);
                        head.ui.pos(36, 121 + i * 165);
                        // var faceImg = cell.getChildByName('faceImg');
                        // faceImg['skin'] = "public/default_head.png";
                        // var faceUrl = playerData && playerData.fPlayerInfo.faceUrl ? playerData.fPlayerInfo.faceUrl : "";
                        // if(faceUrl.indexOf('png') > -1 || faceUrl.indexOf('jpg') > -1 || faceUrl.indexOf('jpeg')>-1) faceImg['skin'] = faceUrl;

                        var exp = itemBgImg.getChildByName('exp');
                        var expTxt = cell.getChildByName('expTxt');
                        if(yalla.data.RoomService.instance.room.isPrivate != GameRoomType.PRIVATE && (data.winnerExp || data['exp'])){
                            exp.visible = expTxt.visible = true;
                            expTxt.text = '+'+(data.winnerExp || data['exp']);
                        } else {
                            exp.visible = expTxt.visible = false;
                        }

                        var isMe = playerData.fPlayerInfo.idx == yalla.data.UserService.instance.user.idx ? true : false;
                        itemBgImg.skin = isMe ? yalla.getDomino('bg_oneself'):yalla.getDomino('bg_others');//'domino/bg_oneself.png':'domino/bg_others.png';
                        itemBgImg.getChildByName('bg').skin = itemBgImg.getChildByName('bg0').skin = (isMe ? yalla.getDomino('bg_oneself2'):yalla.getDomino('bg_others2'));
                        
                        var nameItem:yalla.common.NameItem = this._nameItemHash[idx];
                        if(!nameItem){
                            nameTxt.color = isMe ? '#fa9300' : '#428371';
                            nameItem = new yalla.common.NameItem(nameItemUI, nameTxt);
                            nameItem.roundName_domino(playerData, yalla.util.filterName(playerData.fPlayerInfo.nikeName, 13, "m"));
                            this._nameItemHash[idx] = nameItem;
                        } else {
                            //名字颜色改变，需要重新赋值
                            if (playerData.realRoyLevel < 4) {
                                nameItem.defaultNameTxt.color = isMe ? '#fa9300' : '#428371';
                                yalla.Emoji.lateLable(yalla.util.filterName(playerData.fPlayerInfo.nikeName, 13, "m"), nameItem.defaultNameTxt, ['color', 'fontSize', 'strokeColor']);
                            }
                        }
                        nameItem.ui.y = this.nameItemPos[i];
                        nameItem.defaultNameTxt.y = this.nameTxtPos[i];
                        // yalla.Debug.log(i + "===" + playerData.fPlayerInfo.nikeName + "===小局结算信息==nameItemUI.y=" + nameItemUI.y + " nameTxt.y=" + nameTxt.y);
                        
                        var scoreTxt: Laya.Label = cell.getChildByName('scoreTxt') as Laya.Label;
                        var totalTxt: Laya.Label = cell.getChildByName('totalTxt') as Laya.Label;
                        scoreTxt.changeText(data['roundIntergral'] + '');
                        totalTxt.changeText(data['intergral'] + '');
                    }
                }
            }
        }

        public frameLoopName(): void{
            for (var key in this._nameItemHash) {
                var nameItem = this._nameItemHash[key];
                nameItem.frameLoopName();
            }
        }

        /**
         * 进入下一轮
         */
        private onClickConfirm(): void  {
            if(!this._isSend){
                this._isSend = true;
                yalla.data.RoomService.instance.sendNexgRound();
            }
        }

        public close(): void {
        }

        public clear(): void  {
            for (var j in this._gameHeadHash) {
                this._gameHeadHash[j].ui.removeSelf();
                delete this._gameHeadHash[j];
            }
        }
    }
}