module yalla.view.result {
    /**
     * 单局结束（积分剩余卡 和轮次场ui有所区别）
     */
    export class RoundResult  {
        private _drawUI: yalla.view.result.RoundDrawResult;
        private _mugginUI: yalla.view.result.RoundMugginResult;
        private _gameType: number = 0;
        private _nextRoundTime;
        private _callBack: Function = null;

        constructor(gameType: number,callBack:Function = null) {
            this._gameType = gameType;
            this._callBack = callBack;
            this.init()
        }

        private init():void{
            if (this._gameType == 0) {
                this._drawUI = new yalla.view.result.RoundDrawResult();
            } else {
                this._mugginUI = new yalla.view.result.RoundMugginResult();
            }
        }

        public setData(msg: DominoRoundInfo,cardSkinId:number = 0): void {
            if (this._gameType == 0) {
                this._drawUI.setData(msg, cardSkinId);
                this._drawUI.visible = true;
            } else {
                this._mugginUI.setData(msg, cardSkinId);
                this._mugginUI.visible = true;
            }
            yalla.util.updateBackPressed(5, 1);
        }

        /**
         * 下一局 倒计时
         * @param xx 
         */
        public updateNextTime(xx:number, gameRoundTime:number):void {
            var lan = yalla.Font.lan;
            if (lan != 'en') {
                var [nextTimeV, nextRoundV] = [0,0];
                if (lan == 'ar') [nextTimeV, nextRoundV] = [-20, 58];
                else if (yalla.Font.lan == 'urdu') [nextTimeV, nextRoundV] = [-54, 46];
                // else if(yalla.Font.lan == 'indy') [nextTimeV, nextRoundV] = [-40, 62];

                this.ui['nextTimeTxt'].x = xx + nextTimeV;
                this.ui['nextRoundTxt'].x = this.ui['nextTimeTxt'].x + nextRoundV;
            }

            this._nextRoundTime = Math.round(gameRoundTime/1000);//yalla.data.GameConfig.NextRoundTime;
            if(!this._nextRoundTime) this._nextRoundTime = 3;
            this.ui['nextTimeTxt'].text = `${this._nextRoundTime}s`;
            Laya.timer.loop(1000, this, this.nextRound, [this.ui]);
        }

        private nextRound(ui:any):void{
            if (this._nextRoundTime == 0) {
                this._callBack && this._callBack();
                // this.clear();
                // this.ui.removeSelf();
                this.close();
            }
            if(this._nextRoundTime > 0){
                this._nextRoundTime--;
                if(ui) ui['nextTimeTxt'].text = `${this._nextRoundTime}s`;
            }
        }

        public frameLoopName(): void {
            if (this._gameType == 0) {
                this._drawUI.frameLoopName();
            } else {
                this._mugginUI.frameLoopName();
            }
        }

        public get ui(): View  {
            if (this._gameType == 0) {
                return this._drawUI;
            }
            return this._mugginUI;
        }

        public close(): void{
            Laya.timer.clear(this, this.nextRound);
            Laya.timer.clearAll(this);
            if (this._gameType == 0) {
                if (this._drawUI) {
                    this._drawUI.close();
                    this._drawUI.visible = false;
                }
            } else {
                if (this._mugginUI) {
                    this._mugginUI.close();
                    this._mugginUI.visible = false;
                }
            }
            yalla.util.updateBackPressed(5);
        }

        public clear(): void  {
            this._callBack = null;
            Laya.timer.clear(this, this.nextRound);
            Laya.timer.clearAll(this);

            if (this._gameType == 0) {
                if (this._drawUI) {
                    this._drawUI.clear();
                    this._drawUI.removeSelf();
                    this._drawUI.destroy(true);
                }
            } else {
                if (this._mugginUI) {
                    this._mugginUI.clear();
                    this._mugginUI.removeSelf();
                    this._mugginUI.destroy(true);
                }
            }
            this._drawUI = null;
            this._mugginUI = null;
            yalla.util.updateBackPressed(5);
        }
    }
}