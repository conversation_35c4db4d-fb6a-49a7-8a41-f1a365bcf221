module yalla.view.game.jackaro {
    /**
     * 自己相关信息   聊天fastchat   chat  加入语音房间
     */
    export class BottomView {
        private gameUI: ui.jackaro.gameUI;

        private _isSpectator: boolean = null;
        private _isSendResetMsg: boolean = false;

        public chat: any;
        public readNum: number = 0;
        public activated_id: number = 0;
        private logTag: string = "Jackaro BottomView : ";
        //手牌拖动时，触摸遮挡层
        private touchMask: Laya.View;

        constructor(ui: ui.jackaro.gameUI) {
            this.gameUI = ui;
            this.init();
            this.initEvent();
        }

        private init(): void {
            if (!this.touchMask) {
                this.touchMask = new Laya.View();
                this.touchMask.width = 550;
                this.touchMask.height = 100;
                this.touchMask.bottom = 17;
                //<Image var="maskBg" top="0" skin="jackaro/alphaBg.png" right="0" mouseThrough="false" mouseEnabled="false" left="0" disabled="true" bottom="0" editorInfo="compId=63"/>
                let maskBg = new Laya.Image();
                maskBg.skin = "jackaro/alphaBg.png";
                maskBg.mouseThrough = false;
                maskBg.mouseEnabled = false;
                maskBg.disabled = true;
                maskBg.left = maskBg.right = maskBg.top = maskBg.bottom = 0;
                this.touchMask.addChild(maskBg);
                this.touchMask.mouseEnabled = true;
            }
        }
        /**
         * 是否显示聊天区域触摸遮挡层
         */
        setTouchMaskShow(val: boolean) {
            if (!this.touchMask.parent) {
                this.gameUI && this.gameUI.topLayer.addChild(this.touchMask);
            }
            this.touchMask.visible = val;
        }

        private initEvent(): void {
            yalla.data.jackaro.JackaroUserService.instance.on(yalla.data.jackaro.EnumCmd.Game_GetAgoraToken, this, this.voiceTokenInit);
            yalla.data.jackaro.JackaroUserService.instance.on(yalla.data.jackaro.EnumCmd.Game_GetZegoToken, this, this.voiceTokenInit);
            yalla.event.YallaEvent.instance.on("open_dialog", this, this.onOpenDialog);
        }

        set isSpectator(val: boolean) {
            // if (this._isSpectator == val) return;
            // this._isSpectator = val;
            if (!this.chat) {
                var hairHeight = 0
                var screen = yalla.Screen;
                if (screen.hasHair) {
                    hairHeight = screen.hairHeight ? screen.hairHeight / screen.screen_scale : 50;
                }
                // if (!val) {
                this.chat = new LudoChat(this.gameUI.topImg.height);
                // } else {
                // this.chat = new LiveChat(this.gameUI.topImg.height);
                // }

                this.chat.bottom = hairHeight;
                // var profileInfo = yalla.Global.ProfileInfo;
                this.gameUI.topLayer.addChild(this.chat);
                // var index: number = this.gameUI.getChildIndex(this.gameUI.topLayer);
                // this.gameUI.addChildAt(this.chat, index);
                // this.chat.zOrder = yalla.util.zOrderType.Z_Top;
            }
        }

        public getChatGlobalPosY(): number {
            if (this.chat && this.chat.voice_btn) {
                let point = this.chat.voice_btn.localToGlobal(new Laya.Point());
                let halfCardHeight = yalla.view.jackaro.JackaroCardItem.cardHeight / 2;
                return point.y - halfCardHeight - 10;
            }
            return null
        }

        public voiceTokenInit(cmd, token: string, cName: string) {
            if (cmd == 92 && yalla.Global.IsUpdateZegoToken) {
                yalla.Native.instance.zegoTokenResponse(token, cName);
                yalla.Global.IsUpdateZegoToken = false;
            } else {
                var voice: yalla.Voice = yalla.Voice.instance;
                if (!voice.joinSuccess) {
                    [voice.token, voice.channelId] = [token, cName];
                    voice.joinTimes = 0;
                    voice.joinGameRoomWithToken(yalla.Global.Account.idx);
                } else {
                    this.joinVoiceCallback();
                }
            }
        }

        public joinVoiceCallback() {
            // ludoRoom.instance.muteAudiencePlayers();
            // if (yalla.Global.Account.isLimitVisitor && yalla.Global.Account.isPrivate == 1) return;//阿联酋游客账号不可开麦 1.3.6加入模式
            if (yalla.Mute.isBanTalk) {
                this.playBgm();
                return;//后台管理员禁言用户
            }
            if (Laya.LocalStorage.getJSON("voice_open").isOpen && !this._isSpectator) {
                yalla.Voice.instance.enableAudio(() => {
                    this.playBgm(true);
                    this.chat && this.chat.trunVoice();
                    if (!Laya.LocalStorage.getJSON("voice_first")) {
                        if (this.chat && this.chat.voice_btn) {
                            Laya.LocalStorage.setJSON("voice_first", { isFirst: false });
                            var point: Laya.Point = this.chat.voice_btn.localToGlobal(new Laya.Point(), true);
                            point.x += 28;
                            var tip = new VoiceTip(point);
                            this.gameUI.addChild(tip);
                            tip.on("click", this, () => {
                                tip.removeSelf();
                            })
                        }
                    }
                }, () => {
                    this.playBgm();
                    var tip = yalla.common.TipItem.instance;
                    tip.pushTip((Laya.stage.width - tip.width) / 2, Laya.stage.height / 2, yalla.data.TranslationD.Game_Tip_Check_Microphone, { alpha: 0 }, 0, 5000);
                });
            } else {
                this.playBgm();
            }
        }
        private playBgm(isFouce = false) {
            if (!yalla.Sound.canPlayMusic || isFouce) {
                yalla.Sound.canPlayMusic = true;
                yalla.Sound.playMusic(yalla.Sound.audios.jackaroo_bg, 0);
            }
        }

        public onChat(msg: any, playerInfo: playerShowInfo, myIdx: number, color) {
            if (!playerInfo) return;
            let isMe = msg.idx == myIdx;
            let side = isMe;
            if (yalla.Font.lan == "ar") side = !side;
            let chat = {
                color: color,
                idx: msg.idx,
                playerShowInfo: playerInfo,
                chat: msg,
                side: side,
                isMe: isMe
            }
            this.chat && this.chat.onChat(chat, isMe, playerInfo.sitNum > -1);
        }

        public onClickGameUI(): void {
            this.hide();
        }

        /**有弹框出来时，需要关闭好友私聊界面，避免界面无法点击 */
        private onOpenDialog(): void {
            if (this.chat) {
                this.chat.updateFriendBtn(false);
            }
        }

        public hide(): void {
            if (this.chat) {
                this.chat.hideChat(false);
                this.chat.hideFastChat(false);
                this.chat.updateFriendBtn(false);
            }
            //TODO::这里不删除互动礼物面板，避免mouseDown事件冒泡，拖动giftlist 面板关闭了
            // this.chat && this.chat.hideAllChat();
        }

        private removeEvent(): void {
            yalla.data.jackaro.JackaroUserService.instance.off(yalla.data.jackaro.EnumCmd.Game_GetAgoraToken, this, this.voiceTokenInit);
            yalla.data.jackaro.JackaroUserService.instance.off(yalla.data.jackaro.EnumCmd.Game_GetZegoToken, this, this.voiceTokenInit);
            yalla.event.YallaEvent.instance.off("open_dialog", this, this.onOpenDialog);
        }

        public clear(): void {
            this.removeEvent();

            this.chat && this.chat.clear();
            this.chat = null;
        }
    }
}