class CircularProgressBar extends ui.jackaro.sub.progressUI {
    private radius: number = 120;
    private startAngle: number = -185; // 起始角度
    private endAngle: number = -86;// 结束角度
    private currentAngle: number = -86;// 当前角度
    private current: number = 0;//进度条当前值
    private total: number = 0;//进度条最大值
    private maskGraphics: Laya.Graphics = null;
    private lastTime: number = 0;
    private deltaAngle = this.endAngle - this.startAngle;
    private currentColor: number = null;
    private myColor: number = 1;//左下角玩家的颜色通常是自己的颜色
    private againstType: yalla.data.jackaro.GameAgainstType = yalla.data.jackaro.GameAgainstType.MELEE
    constructor() {
        super();
        this.maskGraphics = this.maskSp.graphics;
        this.progress_img.mask = this.maskSp;
    }
    /**
     * @param current 剩余时间
     * @param total 总时间
     * @param currentColor 当前颜色
     */
    public showProgress(current: number, total: number, currentColor: number) {
        // console.log("showProgress", current, total, currentColor);
        this.setCurrentColor(currentColor);
        this.currentColor = currentColor;
        this.current = Math.min(total, current);
        this.total = total;
        this.lastTime = Date.now();
        this.show();
        this.stopProgress();
        this.drawProgress();
        this.timer.frameLoop(2, this, this.drawProgress);
    }
    public addTime(time: number) { // 添加时间
        if (this.currentColor !== null)
            this.showProgress(this.current + time, this.total + time, this.currentColor);
    }
    public setMyColor(color: number, againstType: yalla.data.jackaro.GameAgainstType) {
        this.myColor = color;
        this.againstType = againstType;
    }
    private show() {
        if (!this.destroyed && this.total > 0) this.visible = true;
    }
    private setCurrentColor(currentColor: number) {
        if (this.currentColor == currentColor) return;
        this.progress_img.skin = `jackaro/cPro_${currentColor}.png`;
        let dataSources = [{
            rotation: 270, scaleX: 1, scaleY: 1
        }, {
            rotation: 270, scaleY: -1, scaleX: 1
        }, {
            rotation: 0, scaleX: -1, scaleY: 1
        }, {
            rotation: 0, scaleX: 1, scaleY: 1
        }]
        let side = 0;
        if (this.againstType == yalla.data.jackaro.GameAgainstType.ONE_VS_ONE) {
            side = this.myColor == currentColor ? 0 : 2;
        } else {
            side = (currentColor - this.myColor + 4) % 4;
        }
        this.progressBox.dataSource = dataSources[side];
    }
    //停止并隐藏进度条
    public hideProgress() {
        this.stopProgress();
        this.visible = false;
    }

    private drawProgress() {//使用差值计算，无论时间间隔是多少，进度条的变化都是相同的
        let now = Date.now();
        this.current -= (now - this.lastTime);
        this.lastTime = now;
        this.currentAngle = this.startAngle + this.current / this.total * this.deltaAngle;
        this.maskGraphics.clear();
        if (this.currentAngle <= this.startAngle) { this.hideProgress(); return }
        this.maskGraphics.drawPie(122, 122, this.radius, this.startAngle, this.currentAngle, "#ffffff", "#ffffff", 0);
    }
    private stopProgress() {//仅停止进度条
        this.timer.clear(this, this.drawProgress);
    }
}