module yalla.view.game.jackaro {
    import Command = yalla.data.jackaro.Command;
    /**
     * game层级：
     * toastLayer （游戏内的toast 非自己回合提示）
     * trustLayer 托管提示
     * operateLayer（弃牌tip，全部、单个、牌10）+ 吃子后小马表情+ 经验星星动画showstart
     * tipLayer （选牌 规则 选择棋子等tip）
     */

    export class Game extends ui.jackaro.gameUI {
        protected _topView: yalla.view.game.jackaro.TopView;
        protected _bottomView: yalla.view.game.jackaro.BottomView;
        private _resultView: any;
        protected _addTimeView: yalla.view.game.jackaro.gamepage.JackaroAddTimeView;
        private _baseMap: JackaroMap;
        private _chessBubbleManager: ChessBubbleManager;
        private _playerHash: Object = {};
        // private _testView: TestView = null;

        public get playerHash(): Object {
            return this._playerHash;
        }
        public set playerHash(value: Object) {
            this._playerHash = value;
        }
        private _chessHash: Object = {};
        private _user: yalla.data.jackaro.User;
        private _room: yalla.data.jackaro.Room;
        public playerStatus: number = 0;
        /**骰子转动时间 */
        public diceTime: number = 400;
        private topMc_top: number = 0;
        private selfForcedDiscard: Laya.Image = null;
        public endPointNormalAni: SpineAniPlayer = null;
        private selfForceDiscardTimeLine: Laya.TimeLine;
        private roundTimeTween: Laya.Tween;
        private logTag: string = "Game : ";
        private chessRoalArr: Array<Array<Laya.Image>> = [];
        private chessPanel_id: number = yalla.data.jackaro.ChessBoardId;
        public isNeedRestorePlayPoker: boolean = false;
        /** 从1开始递增,客户端本地缓存此字段,如果服务端重启后,同一房间id重开一局,此字段会加1,
         * 客户端对比服务端返回的值与本地的值,如果不一样则清空本地数据  ChessPanel里返回version*/
        private version = 1;
        // 记牌器文本
        private pokerCountText: Laya.Text = null;
        // 总牌数
        private totalPokerCount: number = 0;
        // 发牌动画定时器
        private dealPokerAniTimer;
        private endPointAniArr: Array<SpineAniPlayer> = [];
        private cProgress: CircularProgressBar = null;

        constructor() {
            super();
            yalla.Global.IsGameOver = false;
            yalla.PhoneVibrate.init();
            yalla.JackaroTipsShow.init();
            this.initUI();
            this.initEvent();
            this.name = this.logTag;
        }
        public onResize() {
            this.size(Laya.stage.width, Laya.stage.height);
            this.adapterUI();
            this.resizeUpdateTipView();
        }
        /**
         * 屏幕尺寸改变时更新游戏tips界面（主要是挖空和选中效果位置）
         */
        private resizeUpdateTipView(): void {
            //移除选中效果
            JkChooseChessManager.ins.removeAll();

            if (TipsViewManager.getInstance().isShow(TipViewType.EXCHANGE_CHESS)) {
                (TipsViewManager.getInstance().getTipView(TipViewType.EXCHANGE_CHESS) as ExChangeChessView).checkShowChessMask();
            }

            if (TipsViewManager.getInstance().isShow(TipViewType.CHOOSE_CHESS_OPERATE)) {
                (TipsViewManager.getInstance().getTipView(TipViewType.CHOOSE_CHESS_OPERATE) as ExChangeChessView).checkShowChessMask();
            }
        }
        private initUI(): void {
            var scale = Laya.stage.height / 1334;
            this.furniture.scale(scale, scale);
            this._topView = new yalla.view.game.jackaro.TopView(this);
            this._bottomView = new yalla.view.game.jackaro.BottomView(this);
            this._baseMap = new JackaroMap();
            this.map_layer.addChild(this._baseMap);

            if (yalla.Global.onlineMode) {
                this.cProgress = new CircularProgressBar();
                this._baseMap.sign_box.addChild(this.cProgress);
            }


            // this._testView = new TestView(this._baseMap);
            // if (this._baseMap.close_socket) {
            //     (this._baseMap.close_socket as Laya.Button).visible = true
            //     // var open = false;
            //     this._baseMap.close_socket.on("click", this, () => {
            //         // open = !open;
            //         // JkChooseChessManager.ins.testShowAll(this.topLayer, open)
            //         yalla.Native.instance.sendNetTyrClose();
            //         yalla.NativeWebSocket.instance.sendNetClose();
            //     });//测试网络掉线
            // }
            // if (this._baseMap.debug_log) {
            //     (this._baseMap.debug_log as Laya.Button).visible = true;
            //     this._baseMap.debug_log.label = "8.18 13:36";
            //     this._baseMap.debug_log.on("click", this, () => {
            //         // JackaroCardManger.instance.clearDiscardPoker();
            //         // JackaroChessManagerTest.instance.testCase();
            //         // if (JackaroGamePlayerManager.instance.isMyActive()) {
            //         //     return;
            //         // }
            //         // TipsViewManager.getInstance().showTip(TipViewType.GM_SELECT_CARD, {
            //         //     tip: "Please select a card.",
            //         // });
            //         // JackaroChessManagerTest.instance.testCase();
            //         yalla.Debug.logMore("********日志打点*********");
            //     });//测试网络掉线
            // }
            this._chessBubbleManager = new ChessBubbleManager();
            yalla.SkAnimation.instance.initAnimation(this._baseMap.ani_box);
            this._topView.init();
            this.topMc_top = this._topView.topUI['topMc'].top;
            this._topView.isSpectator = false;

            this._addTimeView = new yalla.view.game.jackaro.gamepage.JackaroAddTimeView();

            this.adapterUI();
            // this.toastLayer.addChild(this._addTimeView);
            this.topLayer.addChild(this._addTimeView);
            // this.addChild(this._addTimeView);
            // this._addTimeView.zOrder = yalla.util.zOrderType.Z_Top;

            this._addTimeView.visible = false;

            //初始化玩家管理器
            JackaroGamePlayerManager.instance.init(this, this.players_layer, this.handCardLayerPlaceHolder, this.cProgress);
            JackaroChessManager.instance.setMapLayer(this._baseMap);
            //TODO::选择棋子动画层级调整，调整到tipLayer
            // JackaroChessManager.instance.setTopAniLayar(this.topAniLayer);
            JackaroChessManager.instance.setTopAniLayar(this.tipLayer);

            this.endPointNormalAni = new SpineAniPlayer(yalla.getSkeleton("jackaro/finish/Finish"));
            this._baseMap.ani_box.addChild(this.endPointNormalAni);

            ToastManager.initialize(this.toastLayer);
            EffectAniManager.instance.initialize(this.operateLayer);

            JackaroCardManger.instance.registerDealPokerPlaceholder(this.dealPokerPlaceholder);
            JackaroCardManger.instance.registerPlayCardLayer(this.middleLayer);
            JackaroCardManger.instance.registerShuffleSpineLayer(this.shuffleSpineLayer);
            JackaroCardManger.instance.registerReplenishCardLayer(this.replenishCardLayer);
            JackaroCardManger.instance.init();
            TipsViewManager.getInstance().setCurGameView(this);

            // 初始化记牌器文本
            this.initPokerCountText();
            // 禁用或启动翻牌动画
            this.saveCardFlipAnimationSetting(!yalla.Global.isLowPerformanceDevice);

        }

        /**
         * 初始化记牌器文本
         */
        private initPokerCountText(): void {
            this.pokerCountText = new Laya.Text();
            this.pokerCountText.fontSize = 80;
            this.pokerCountText.color = "#ffffff";
            this.pokerCountText.stroke = 2;
            this.pokerCountText.strokeColor = "#000000";
            this.pokerCountText.align = "center";
            this.pokerCountText.valign = "middle";
            this.pokerCountText.width = 140;
            this.pokerCountText.height = 200;
            this.pokerCountText.pivot(70, 100);
            this.pokerCountText.pos(0, 0); // 将文本放在牌的上方
            this.pokerCountText.visible = false;
            this.dealPokerAni.addChild(this.pokerCountText);
        }

        private adapterUI() {
            var screen = yalla.Screen;
            if (screen.hasHair) {
                var hairHeight = screen.hairHeight ? screen.hairHeight / screen.screen_scale : 50;
                if (this._addTimeView) this._addTimeView.bottom = hairHeight + 28;
                if (this.handCardLayerPlaceHolder) this.handCardLayerPlaceHolder.centerY = 580;
                if (this._topView && this._topView.topUI) this._topView.topUI['topMc'].top = this.topMc_top + hairHeight;
                if (this.topImg) this.topImg.height = 95 + hairHeight;
            } else if (screen.screen_ratio > 1.9) {
                if (this.handCardLayerPlaceHolder) this.handCardLayerPlaceHolder.centerY = 580;
            } else {
                if (this.handCardLayerPlaceHolder) this.handCardLayerPlaceHolder.centerY = 550;
            }
        }

        private initEvent(): void {

            yalla.data.jackaro.JackaroUserService.instance.on(yalla.data.jackaro.EnumCmd.Game_QuitRoom, this, this.handleQuitGame);
            yalla.data.jackaro.JackaroUserService.instance.on(yalla.data.jackaro.EnumCmd.Game_Update_Player_Coin, this, this.handleUpdatePlayerCoin);
            yalla.data.jackaro.JackaroUserService.instance.on(yalla.data.jackaro.EnumCmd.Game_Chat, this, this.handleChat);

            this.on(Laya.Event.CLICK, this, this.onClickGameUI);
            this.giftAniBg.on(Laya.Event.CLICK, this, this.onClickGift);

            // yalla.event.YallaEvent.instance.on(yalla.data.jackaro.EnumCustomizeCmd.Exit_Back, this, this.onClickBack);
            yalla.event.YallaEvent.instance.on(yalla.data.jackaro.EnumCustomizeCmd.Game_Chat_My, this, this.handleChatMy);
            yalla.event.YallaEvent.instance.on(yalla.data.jackaro.EnumCustomizeCmd.Game_ChatOFF_My, this, this.handleChatOff);
            yalla.event.YallaEvent.instance.on(yalla.data.jackaro.EnumCustomizeCmd.Game_ChatOFF, this, this.setChatOff);
            yalla.event.YallaEvent.instance.on(yalla.data.jackaro.EnumCustomizeCmd.Jackaro_PlayMsg, this, this.handlePlayMsg);
            yalla.event.YallaEvent.instance.on(yalla.data.jackaro.EnumCustomizeCmd.Game_Chess_Move_End, this, this.onMoveEnd);
            yalla.event.YallaEvent.instance.on(yalla.data.jackaro.EnumCustomizeCmd.Game_Card_Discard_End, this, this.onCardDiscardEnd);
            yalla.event.YallaEvent.instance.on(yalla.data.jackaro.EnumCustomizeCmd.Game_Chess_Move_PerfectEnterEndPoint, this, this.playEndPointAni);
            yalla.event.YallaEvent.instance.on(yalla.data.jackaro.EnumCustomizeCmd.Game_Hide_ChatAndTopView, this, this.hideChatAndTopView);
            yalla.event.YallaEvent.instance.on(yalla.data.jackaro.EnumCustomizeCmd.Game_Move_Chess_Begin, this, this.onMoveChessBegin);
            yalla.event.YallaEvent.instance.on(yalla.data.jackaro.EnumCustomizeCmd.Game_Click_Invalid_Poker, this, this.onClickInvalidPoker);
            yalla.Voice.instance.on("change", this, this.voiceChange);
            yalla.Voice.instance.on("join", this, this.joinVoiceCallback);
            yalla.Native.instance.on(yalla.Native.instance.Event.SPEAKER, this, this.handleSpeakResponse);
            yalla.common.InteractiveGiftAnimation.Instance.on(yalla.common.InteractiveGiftAnimation.Instance.Event.UpdateGiftIcon, this, this.updateGiftIcon);
            yalla.Mute.event.on(yalla.Mute.MUTE, this, this.onMute)//监听举报
            yalla.Mute.event.on(yalla.Mute.UNMUTE, this, this.unMute);//监听取消举报

            yalla.Native.instance.on(yalla.Native.instance.Event.ONASSETSCHANGE, this, this.onAssetsChange);
        }

        private removeEvent(): void {
            yalla.data.jackaro.JackaroUserService.instance.off(yalla.data.jackaro.EnumCmd.Game_QuitRoom, this, this.handleQuitGame);
            yalla.data.jackaro.JackaroUserService.instance.off(yalla.data.jackaro.EnumCmd.Game_Update_Player_Coin, this, this.handleUpdatePlayerCoin);
            yalla.data.jackaro.JackaroUserService.instance.off(yalla.data.jackaro.EnumCmd.Game_Chat, this, this.handleChat);

            this.off(Laya.Event.CLICK, this, this.onClickGameUI);
            this.giftAniBg.off(Laya.Event.CLICK, this, this.onClickGift);

            // yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Exit_Back, this, this.onClickBack);
            yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Game_Chat_My, this, this.handleChatMy);
            yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Game_ChatOFF_My, this, this.handleChatOff);
            yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Game_ChatOFF, this, this.setChatOff);
            yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Jackaro_PlayMsg, this, this.handlePlayMsg);
            yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Game_Chess_Move_End, this, this.onMoveEnd);
            yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Game_Card_Discard_End, this, this.onCardDiscardEnd);
            yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Game_Chess_Move_PerfectEnterEndPoint, this, this.playEndPointAni);
            yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Game_Hide_ChatAndTopView, this, this.hideChatAndTopView);
            yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Game_Move_Chess_Begin, this, this.onMoveChessBegin);
            yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Game_Click_Invalid_Poker, this, this.onClickInvalidPoker);
            yalla.Voice.instance.off("change", this, this.voiceChange);
            yalla.Voice.instance.off("join", this, this.joinVoiceCallback);
            yalla.Native.instance.off(yalla.Native.instance.Event.SHARE, this, this.shareResponse);
            yalla.Native.instance.off(yalla.Native.instance.Event.SPEAKER, this, this.handleSpeakResponse);
            yalla.common.InteractiveGiftAnimation.Instance.off(yalla.common.InteractiveGiftAnimation.Instance.Event.UpdateGiftIcon, this, this.updateGiftIcon);

            yalla.Mute.event.off(yalla.Mute.MUTE, this, this.onMute)//监听举报
            yalla.Mute.event.off(yalla.Mute.UNMUTE, this, this.unMute);//监听取消举报
            yalla.Native.instance.off(yalla.Native.instance.Event.ONASSETSCHANGE, this, this.onAssetsChange);

            Laya.timer.clear(this, this.frameLoopName);
            Laya.timer.clearAll(this);
        }

        onClickGiftBox(e: Event) {
            if (e) e.stopPropagation();
        }

        set chessPanelId(val: number) {
            if (val && val != this.chessPanel_id) {
                var fevent = yalla.event.YallaEvent.instance;
                fevent.once(`BG_${val}.png`, this, () => {
                    if (this.destroyed) return;
                    if (this.bg) this.bg.skin = yalla.File.filePath + `BG_${val}.png`;
                })
                yalla.File.getFileByNative(`BG_${val}.png`)
                this.chessPanel_id = val;
                this._baseMap.chessPanelId = this.chessPanel_id;
                if (yalla.Skin.instance.hasFurniture(val)) {
                    fevent.once(`furniture_${val}.png`, this, () => {
                        if (this.destroyed) return;
                        if (this.furniture) {
                            this.resizeFurnitur()
                            this.furniture.skin = yalla.File.filePath + `furniture_${val}.png`;
                        }
                    })
                    yalla.File.getFileByNative(`furniture_${val}.png`)
                }
            }
        }
        public resizeFurnitur() {
            if (this.furniture) {
                var scale = Laya.stage.height / 1334;
                this.furniture.scale(scale, scale);
            }
        }
        private onMute(idx: number) {
            var player: JackaroGamePlayer = this.gamePlayer(idx);
            player && yalla.Voice.instance.muteRemoteAudioStream(idx, true, (val, uid) => {
                player = this.gamePlayer(uid);
                if (val == 0 && player) {
                    this.timer.clear(this, player.pause);
                    player.isMute = true;
                    player.playVoiceAni(0, false, "stop");
                }
            })
        }
        private unMute(idx: number) {
            var player: JackaroGamePlayer = this.gamePlayer(idx);
            if (player) {
                yalla.Voice.instance.muteRemoteAudioStream(idx, false, (val, uid) => {
                    player = this.gamePlayer(uid);
                    if (val == 0 && player) {
                        player.isMute = false;
                        player.pause();
                    }
                })
            }
        }

        private trustDely() {
            this.addTrustEvent();
        }

        private addTrustEvent() {
            this.bg.on(Laya.Event.CLICK, this, this.onTrust);
            this.bodyMc.on(Laya.Event.CLICK, this, this.onTrust);
            // this.trusteeship && this.trusteeship.on(Laya.Event.CLICK, this, this.onTrust);
        }
        private removeTrustEvent() {
            this.bg.off(Laya.Event.CLICK, this, this.onTrust);
            this.bodyMc.off(Laya.Event.CLICK, this, this.onTrust);
        }
        private onAssetsChange(data) {
            if (data) {
                //  * @param diamond 当前自己的钻石
                //  * @param money 当前自己的金币
                //  */
                if (data.money != undefined && this._user) {
                    this._user.gold = parseInt(data.money || 0);
                }
                if (data.diamond != undefined && this._user) {
                    this._user.diamond = parseInt(data.diamond || 0);
                }
                this.update();
            }
        }

        public update() {
            this._topView && this._topView.updateMoney();
            this._addTimeView && this._addTimeView.updateView();
            // this._myHanderView && this._myHanderView.updateView();
            this._user && yalla.common.InteractiveGift.Instance.updateMoney(this._user.gold, this._user.diamond);
        }
        private openFrameLoop(): void {
            Laya.timer.clear(this, this.frameLoopName);
            var isHonorRoyal = JackaroGamePlayerManager.instance.isHonorRoyal();
            if (isHonorRoyal) {
                Laya.timer.frameLoop(1, this, this.frameLoopName);
            }
        }
        private frameLoopName(): void {
            for (var k in this.playerHash) {
                var data = this.playerHash[k].data;
                var canShowRLNameAnimation = yalla.util.canShowJackaroRLNameAnimation(data);
                if (canShowRLNameAnimation) this.playerHash[k].frameLoopName();
            }

            if (this._resultView && this._resultView.displayedInStage) {
                this._resultView.frameLoopName();
            }
        }
        /**播放消息 */
        handlePlayMsg(msg: any, isPlayNext: boolean = true): void {
            !this._user && (this._user = yalla.data.jackaro.JackaroUserService.instance.user);
            !this._room && (this._room = yalla.data.jackaro.JackaroUserService.instance.room);
            if (!msg || !msg.command) return;
            let sortedPlayerIdxList;
            let totalLen;
            let selfGamePlayer;
            let curIdx = msg ? msg.idx : 0;
            switch (msg.command) {
                case Command.TICKET_LOGIN:
                    // yalla.Debug.log(msg);
                    JackaroGamePlayerManager.instance.resetOfflineData();
                    this._user = yalla.data.jackaro.JackaroUserService.instance.user;
                    this._room = yalla.data.jackaro.JackaroUserService.instance.room;
                    var color = JackaroGamePlayerManager.instance.getColorByIdx(yalla.Global.Account.idx);
                    this._baseMap.setGameAgainstType(this._room.againstType);
                    if (this._user && this._user.chessPanelId) {
                        this.chessPanelId = this._user.chessPanelId;
                    }
                    if (yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode())
                        JackaroCardReplenishManager.instance.dispose(true);
                    this._baseMap.color = color;
                    this.cProgress && this.cProgress.setMyColor(color, this._room.againstType)
                    var isSpectator = this._room.watchIdx > 0;
                    // if (!isSpectator) {
                    //     this._bottomView && this._bottomView.voiceInit();
                    // }
                    this.adapterUI();
                    this._bottomView && (this._bottomView.isSpectator = isSpectator);
                    yalla.Native.instance.removeMatchView();

                    let restoreGame = msg.restoreGame;
                    let totalPlayerIdx: Array<number> = [];
                    if (restoreGame && restoreGame.playShowInfo) {
                        let playerShowInfo = restoreGame.playShowInfo as Record<string, any>;
                        for (let idx in playerShowInfo) {
                            let playerInfo = playerShowInfo[idx].playerShowData as yalla.data.jackaro.PlayerShowInfoInterface;
                            if (playerInfo) {
                                let pokerSkinId = playerInfo.pokerSkinId;
                                if (pokerSkinId) {
                                    JackaroCardManger.instance.setCardBackSkin(parseInt(idx), pokerSkinId);
                                }
                            }
                            totalPlayerIdx.push(parseInt(idx));
                            JackaroGamePlayerManager.instance.updatePlayerByIdx(parseInt(idx));
                        }
                    }
                    this._room.isTrust = false;
                    //TODO::handleSyncGame 之前获取是否本轮回合
                    let isCurOutRound = false;
                    //处理玩家切换,如果有用户操作信息，需要清理上一轮用户cd。在handleSyncGame中再更新最新的用户cd表现动画
                    if (restoreGame && restoreGame.gameData && restoreGame.gameData.chessPanel) {
                        isCurOutRound = JackaroGamePlayerManager.instance.isCurOutRound(restoreGame.gameData.chessPanel.outRound);

                        JackaroGamePlayerManager.instance.handlePlayerChange(restoreGame.gameData.chessPanel);
                        if (restoreGame.gameData.chessPanel.killedResult) {
                            this._room.killedResult = restoreGame.gameData.chessPanel.killedResult;
                            //补充其它玩家的击杀数据，当其它玩家数据不存在时
                            if (this._room.killedResult) {
                                for (let idx of totalPlayerIdx) {
                                    if (!this._room.killedResult[idx]) {
                                        this._room.killedResult[idx] = { killChess: 0, chessKilled: 0 };
                                    }
                                }
                            }
                        }
                        // {{ YUM: [修复] - 修复抽牌奖励在断线重连后不触发的问题 ,设置断线后抽牌奖励 手牌数据}}
                        let isSelfTurn = (restoreGame.gameData.chessPanel.currentPlayer == yalla.Global.Account.idx);
                        if (isSelfTurn) {

                            // {{ YUM: [修复] - 修复抽牌奖励在断线重连后不触发的问题 ,设置断线后抽牌奖励 手牌数据}}
                            if (msg.restoreGame.drawRewardPoker && yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode()) {
                                JackaroCardReplenishManager.instance.setDrawRewardPoker(msg.restoreGame.drawRewardPoker);
                            }
                            // } else {
                            // JackaroCardManger.instance.createClientPreviewData();
                        }
                    }
                    this.updatePlayRuleMap(msg);
                    let isRestartServer = false;
                    //TODO::获取登录时当前发牌轮数，这个数值一直时累加的，用来判断是否自己回合结束，这里设置了当前回合curRound，那后面的逻辑不能根据本地的回合&服务器的回合
                    if (restoreGame.gameData && restoreGame.gameData.playerDataMap) {
                        isRestartServer = this.handleSyncGame(restoreGame.gameData, false);
                    }

                    //TODO:: 数据更新更新不能早于handleSyncGame，因handleSyncGame有记录离线前可操作用户&操作剩余时间；弃牌堆先清理handleSyncGame,再恢复restoreGame
                    this.restoreGame(msg, isRestartServer);

                    //恢复出牌
                    if (this.isNeedRestorePlayPoker) {
                        this.restorePlayPoker(msg);
                        this.isNeedRestorePlayPoker = false;
                    }

                    // JackaroChessManagerTest.instance.testShowSelectOperate();
                    this._topView && this._topView.updateMoney();
                    this._room.handleExtendTime(msg.selfGameData);
                    selfGamePlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);
                    if (msg.selfGameData && msg.selfGameData.trustSystem) {
                        selfGamePlayer && selfGamePlayer.setTrustSystem(true);
                    } else {
                        selfGamePlayer && selfGamePlayer.setTrustSystem(false);
                    }
                    this._room.consecutiveTrustCount = msg.selfGameData.consecutiveTrustCount;
                    if (msg.selfGameData && JackaroGamePlayerManager.instance.getCurOperatePlayerIdx() == yalla.Global.Account.idx) {
                        JackaroChessManager.instance.selfAddExp = msg.selfGameData.selfAddExp;
                    }
                    TipsViewManager.getInstance().hideTip(TipViewType.TRUST_SYSTEM);
                    if (this._addTimeView) {
                        this._addTimeView.visible = true;
                        this._addTimeView.updateView();
                    }
                    this.checkSetPlayerHash();
                    this._topView && this._topView.setRank();
                    var gamestatus = this._room.getGameStatus();
                    if (gamestatus > 0) {
                        yalla.data.jackaro.JackaroUserService.instance.initGameStart();
                        this.openFrameLoop();

                    }
                    //加入语音频道
                    // this.initVoiceChannel();
                    //如果之前是托管状态，断线回来托管一定是取消的，所以无论怎么样，托管都需要重置
                    this.resetTrust();
                    break;
                case Command.GAME_PLAYER_LOGIN_PUB:
                    let idx = msg.playerShowData.playerShowData.idx;
                    let pokerSkinId = msg.playerShowData.playerShowData.pokerSkinId;
                    if (pokerSkinId) {
                        JackaroCardManger.instance.setCardBackSkin(idx, pokerSkinId);
                    }
                    let talkSkinId = msg.playerShowData.playerShowData.talkSkinId;
                    if (talkSkinId) {
                        let gamePlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(idx);
                        gamePlayer && gamePlayer.updateBublele(talkSkinId);
                    }
                    JackaroGamePlayerManager.instance.updatePlayerByIdx(idx);
                    if (idx == yalla.Global.Account.idx) {
                        JackaroCardManger.instance.registerCardLayer(idx, this.handCardLayer);
                    } else {
                        JackaroCardManger.instance.registerCardLayer(idx, this.otherHandCardLayer);
                    }
                    JackaroCardManger.instance.registerPlayerHandCardView(idx);
                    this.checkSetPlayerHash();
                    break;
                case Command.DEAL_POKER:
                    yalla.data.jackaro.JackaroUserService.instance.playAllQueueMsg();
                    yalla.Debug.yum('Game  Command.DEAL_POKER 发牌流程 发牌开始', Date.now());
                    //收到发牌消息，应该立马清理上一个玩家的状态
                    JackaroGamePlayerManager.instance.clearOperatePlayer(JackaroGamePlayerManager.instance.curOperatePlayerIdx);
                    //1.4.4 有线上玩家当前轮最后一次弃牌后，然后触发下一轮发牌，然后再次轮到自己，此时需要重置状态
                    JackaroGamePlayerManager.instance.setForcedDiscard(false);

                    // 设置总牌数从服务端获取
                    if (msg.totalPokerCount) {
                        this.totalPokerCount = msg.totalPokerCount;
                    }

                    let dealPokerCallback = () => {
                        //TODO::发牌前的清理逻辑放入回掉方法中，且发牌前的洗牌动效计时器需要现清理
                        JackaroCardManger.instance.turnClear();
                        JackaroCardManger.instance.clearAllHandPoker();
                        TipsViewManager.getInstance().hideTip(TipViewType.DISCARD_ALL_CARD);
                        TipsViewManager.getInstance().hideTip(TipViewType.Discard_Self_ForceCard);
                        TipsViewManager.getInstance().hideTip(TipViewType.DISCARD_CARD);
                        sortedPlayerIdxList = JackaroGamePlayerManager.instance.getSortedPlayerIdxList();
                        totalLen = msg.handPoker.length;
                        this.onDealPokerStart(totalLen);
                        // {{ YUM: [重构] - 简化发牌音效和动画配置逻辑 }}
                        const is1V1 = yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode();
                        const is4Cards = totalLen == 4;

                        let dealingSoundUrl: string;
                        let totalAnimationDealPoker: number;

                        if (is4Cards) {
                            dealingSoundUrl = is1V1 ? 'Dealing_card_1v1' : 'Dealing_card';
                            totalAnimationDealPoker = is1V1 ? JackaroCardManger.totalAnimationDeal4Poker1v1 : JackaroCardManger.totalAnimationDeal4Poker;
                        } else {
                            dealingSoundUrl = is1V1 ? 'Dealing_card_5_1v1' : 'Dealing_card_5';
                            totalAnimationDealPoker = is1V1 ? JackaroCardManger.totalAnimationDeal5Poker1v1 : JackaroCardManger.totalAnimationDeal5Poker;
                        }

                        yalla.Sound.playSound(dealingSoundUrl);

                        // 添加处理失去焦点状态下的发牌逻辑
                        let noDelayStart = !yalla.Global.isFouce;
                        JackaroHandCardView.dealPokerStartTime = Date.now();
                        for (let j = 0; j < sortedPlayerIdxList.length; j++) {
                            let playerIdx = sortedPlayerIdxList[j];
                            this.handleDealPokerAnimation(playerIdx, msg.handPoker, totalLen, j, sortedPlayerIdxList.length, noDelayStart);
                        }

                        // 如果没有延迟，直接触发发牌结束事件
                        Laya.timer.clear(this, this.playNextMsg);
                        if (noDelayStart) {
                            Laya.timer.once(100, this, this.playNextMsg);
                        } else {
                            //发牌结束后 回调处理下一条消息
                            Laya.timer.once(totalAnimationDealPoker, this, this.playNextMsg);
                        }
                    }
                    //洗牌动画
                    if (msg.shuffleAnimation && yalla.Global.isFouce) {
                        JackaroCardManger.instance.turnClear();
                        dealPokerCallback();

                    } else {
                        dealPokerCallback();
                    }
                    break;
                case Command.GAME_START:
                    this.clearAllPlayerHandPoker();
                    // 设置总牌数为52
                    this.totalPokerCount = 52;
                    //根据玩家颜色排序发牌
                    sortedPlayerIdxList = JackaroGamePlayerManager.instance.getSortedPlayerIdxList();
                    let selfPlayerDataMap = msg.gameData.playerDataMap[yalla.Global.Account.idx];

                    let handPoker = selfPlayerDataMap.handPoker;
                    totalLen = handPoker.length;

                    // {{ YUM: [重构] - 简化发牌音效和动画配置逻辑 }}
                    const is1V1 = yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode();
                    const is4Cards = totalLen == 4;

                    let dealingSoundUrl: string;
                    let totalAnimationDealPoker: number;

                    if (is4Cards) {
                        dealingSoundUrl = is1V1 ? 'Dealing_card_1v1' : 'Dealing_card';
                        totalAnimationDealPoker = is1V1 ? JackaroCardManger.totalAnimationDeal4Poker1v1 : JackaroCardManger.totalAnimationDeal4Poker;
                    } else {
                        dealingSoundUrl = is1V1 ? 'Dealing_card_5_1v1' : 'Dealing_card_5';
                        totalAnimationDealPoker = is1V1 ? JackaroCardManger.totalAnimationDeal5Poker1v1 : JackaroCardManger.totalAnimationDeal5Poker;
                    }

                    yalla.Sound.playSound(dealingSoundUrl);
                    yalla.Debug.yum('Game  Command.GAME_START 发牌流程 发牌开始 playSound  ', dealingSoundUrl);

                    let dealerHandCount = 0;
                    this.onDealPokerStart(totalLen);
                    // 添加处理失去焦点状态下的发牌逻辑
                    let noDelayStart = !yalla.Global.isFouce;
                    yalla.Debug.yum('Game  Command.GAME_START 发牌流程 发牌开始', Date.now(), "noDelayStart " + noDelayStart.toString());
                    JackaroHandCardView.dealPokerStartTime = Date.now();
                    // this.multiTouch(true);
                    for (let j = 0; j < sortedPlayerIdxList.length; j++) {
                        let playerIdx = sortedPlayerIdxList[j];
                        let playerDataMapData = msg.gameData.playerDataMap[playerIdx];
                        if (!playerDataMapData) continue;
                        // JackaroChessManager.instance.setGridMap(playerDataMapData.gridMap);
                        let chessPosition = playerDataMapData.chessPosition;
                        let color = JackaroGamePlayerManager.instance.getColorByIdx(parseInt(playerIdx));
                        for (var k2 in chessPosition) {
                            JackaroChessManager.instance.createChess(parseInt(playerIdx), color, chessPosition[k2]);
                        }
                        let handPoker = playerDataMapData.handPoker;
                        let banker = playerDataMapData.banker;
                        JackaroGamePlayerManager.instance.updateBankerInfo(playerIdx, banker);
                        let curDealPoker;
                        if (playerIdx == yalla.Global.Account.idx) {
                            curDealPoker = handPoker[j];
                        } else {
                            curDealPoker = yalla.data.jackaro.Poker.NONE_POKER;
                        }
                        //庄家收牌数
                        if (j == 0) {
                            dealerHandCount = curDealPoker.length;
                        }

                        this.handleDealPokerAnimation(playerIdx, handPoker, totalLen, j, sortedPlayerIdxList.length, noDelayStart);
                    }

                    // 如果没有延迟，直接触发发牌结束事件
                    Laya.timer.clear(this, this.playNextMsg);
                    if (noDelayStart) {
                        Laya.timer.once(100, this, this.playNextMsg);
                    } else {
                        //发牌结束后 回调处理下一条消息
                        Laya.timer.once(totalAnimationDealPoker, this, this.playNextMsg);
                    }
                    this.checkSetPlayerHash();
                    this.openFrameLoop();
                    yalla.data.jackaro.JackaroUserService.instance.initGameStart();
                    break;
                case Command.CHANGE_PLAYER:
                    yalla.data.jackaro.JackaroUserService.instance.handleChangePlayerData(msg);
                    JackaroCardManger.instance.resetCheckDiscardPoker(msg);
                    break;
                case Command.SELECT_POKER:
                    // JackaroCardManger.instance.handleSelectPoker(msg);
                    break;
                case Command.FINISH_DISCARD_PUB:
                    if (msg && msg.discardedPoker && msg.discardedPoker.length > 0) {

                        //处理弃牌动画，如果弃牌数组大于 0，是弃牌多个，需要播放多张牌一起的动画
                        let curDiscarPokers = msg.discardedPoker;
                        let curPlayeridx = msg.discardPlayerId;
                        if (curDiscarPokers && curDiscarPokers.length == 1) {
                            JackaroCardManger.instance.handleDiscardPoker(curPlayeridx, { poker: curDiscarPokers[0] }, JackaroCardManger.novalidCardIndex, true);
                        } else if (curDiscarPokers) {
                            let curShowPoker = msg.currentPoker;
                            curDiscarPokers.splice(curDiscarPokers.indexOf(curShowPoker), 1);
                            curDiscarPokers.push(curShowPoker);
                            //展示弃牌多张动画
                            for (let i = 0; i < curDiscarPokers.length; i++) {
                                JackaroCardManger.instance.handleDiscardPoker(curPlayeridx, { poker: curDiscarPokers[i] }, JackaroCardManger.novalidCardIndex, i == curDiscarPokers.length - 1);
                            }
                        }
                        //弃牌多张，有时动画没有回调，导致消息队列没有执行出现例如发牌延迟
                        //弃牌一张基础时间200毫秒+牌索引*系数
                        if (yalla.Global.isFouce) {
                            Laya.timer.once(300, this, this.playNextMsg);
                        } else {
                            this.playNextMsg();
                        }
                    } else {
                        this.playNextMsg();
                    }
                    break;
                case Command.FLUSH_CURRENCY:
                    if (this._topView) {
                        this._topView.updateMoney();
                    }
                    this.update();
                    break;
                case Command.SETTLEMENT:
                    this.gameOver(msg, true);
                    break;
                case Command.SYNC_GAME_DATA:
                    this.handleSyncGame(msg.gameData, true);
                    //同步弃牌堆
                    this.syncDiscardPile(0, true, msg.discardPokers);
                    ////轮到此玩家时,此字段有值,当前玩家可出的牌及相关数据,key:扑克牌id,value为相关数据
                    if (msg.allowPokerData) {
                        this.syncAllowPokerData(msg.allowPokerData);
                    }
                    break;
                case Command.FORCED_DISCARD_PUB:
                    JackaroCardManger.instance.handleForcedDiscardPub(msg);
                    //如果没有走子的话，需要触发棋子移动结束动画事件通知
                    this.playNextMsg();
                    break;
                case Command.QUIT_ROOM:
                    this.handleQuitGame(msg);
                    break;
                case Command.EXTEND_TIME:
                    curIdx = yalla.Global.Account.idx
                    if (this._addTimeView) {
                        this._addTimeView.updateView();
                    }
                case Command.EXTEND_TIME_PUB:
                    var addOperateTime = yalla.data.jackaro.JackaroUserService.instance.addOperateTime
                    let addTime: number = Math.round(addOperateTime / 1000);

                    JackaroGamePlayerManager.instance.addWaitCdTime(curIdx, addOperateTime);
                    this.showRoundTime(curIdx, addTime);
                    this.cProgress && this.cProgress.addTime(addOperateTime);
                    this.playNextMsg();
                    break;
                case Command.PLAYER_CHAT_ALL:
                    this.handleChat(msg);
                    break;
                case Command.GAME_GIFT_CONFIG:
                    this.handleUpdateGiftCnf(msg);
                    break;
                case Command.GAME_GIFT_PUB:
                    this.handleRevGift(msg);
                    break;
                case Command.TRUST_PUB:
                    selfGamePlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);
                    if (selfGamePlayer) {
                        let isTrustSystem = selfGamePlayer.getTrustSystem();
                        this._room.isTrust = isTrustSystem;
                        this.updateTrust(selfGamePlayer, true);
                        this.addTrustEvent();
                        if (selfGamePlayer) {
                            // player.head.trustee = true;
                            // player.UI.playerBg.visible = false;
                            // this.throwBtn && this.throwBtn.on("click", this, this.onTrust);
                            selfGamePlayer.head.on("click", this, this.onTrust);
                        }
                    }
                    JackaroChessManager.instance.clearChooseTag();
                    // {{ YUM: [Optimize] - 使用统一的清除方法，避免与新动效管理器冲突 }}
                    JackaroChessManager.instance.removeAllChessOrbit();
                    JkChooseChessManager.ins.removeAll();
                    break;
                case Command.CANCEL_TRUST:
                    TipsViewManager.getInstance().hideTip(TipViewType.TRUST_SYSTEM);
                    // this.multiTouch(true);
                    selfGamePlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);
                    if (selfGamePlayer) {
                        let isTrustSystem = selfGamePlayer.getTrustSystem();
                        this._room.isTrust = isTrustSystem;
                        this.hideTrust(true);
                        // this.throwBtn && this.throwBtn.off("click", this, this.onTrust);
                        selfGamePlayer.head.off("click", this, this.onTrust);
                        this.updateTrust(selfGamePlayer, false);
                        // player.head.trustee = false;
                        // player.UI.playerBg.visible = true;
                        // this.activated_id != yalla.Global.Account.idx ? player.showName() : player.hideName();
                        //取消托管时，如果是别人让自己弃牌，恢复弃牌界面
                        JackaroCardManger.instance.checkShowDiscardView();
                    }
                    break;

                case Command.MOVE_CHESS_PUB:
                    JackaroChessManager.instance.handleMoveChessPub(msg);//奖励牌
                    break;
                case Command.EXCHANGE_CHESS_PUB:
                    JackaroChessManager.instance.handleExchangeChessPub(msg);
                    break;
                case Command.DRAW_POKER_PUB:
                    JackaroCardManger.instance.handleDrawPokerPub(msg);
                    if (yalla.Global.isFouce) {
                        Laya.timer.once(JackaroCardManger.totalAnimationPlayPoker + JackaroCardManger.totalAnimationDrawPoker, this, this.playNextMsg);
                    } else {
                        this.playNextMsg();
                    }
                    break;
                case Command.IMPACT_MOVE_PUB:
                    JackaroChessManager.instance.handleImpactMovePub(msg);//击杀移动 有奖励牌
                    break;
                case Command.POKER_SEVEN_MOVE_PUB:
                    JackaroChessManager.instance.handleMoveChessPub(msg);//出牌7 有奖励牌
                    break;
                case Command.TEST_EXCHANGE_POKER_PUB:
                    break;
                case Command.TEST_EXCHANGE_POKER:
                    break;
                case Command.PLAY_POKER:
                    yalla.data.jackaro.JackaroUserService.instance.playPokerResponse(msg);
                    break;
            }
        }
        /**
         * 处理发牌动画和延迟计算
         * @param playerIdx 玩家索引
         * @param pokerData 牌数据
         * @param totalLen 总牌数
         * @param playerPosition 玩家位置序号(j)
         * @param playerCount 玩家总数
         * @param noDelayStart 是否无延迟开始
         */
        private handleDealPokerAnimation(playerIdx: number, pokerData: any[], totalLen: number,
            playerPosition: number, playerCount: number, noDelayStart: boolean): void {

            for (let i = 0; i < totalLen; i++) {
                // 获取当前发牌数据
                let curDealPoker;
                if (playerIdx == yalla.Global.Account.idx) {
                    curDealPoker = pokerData[i];
                } else {
                    curDealPoker = yalla.data.jackaro.Poker.NONE_POKER;
                }

                // 计算每张牌的延迟时间
                let delayTime = 0;
                if (!noDelayStart) { // 如果需要动画延迟
                    if (playerPosition === 0) {
                        // 第一个玩家：简单线性延迟，每张牌间隔固定时间
                        delayTime = JackaroHandCardView.dealPokerInterval * i;
                    } else {
                        // 后续玩家的发牌延迟计算
                        const cardsBeforeNextPlayer = totalLen - 0; // 留最后3张牌与下一玩家发牌动画重叠
                        // 计算三部分延迟
                        const waitForPreviousPlayers = JackaroHandCardView.dealPokerInterval * cardsBeforeNextPlayer * playerPosition;
                        const previousPlayerAnimationTime = JackaroHandCardView.dealPokerTweenTime * playerPosition;
                        const currentCardDelay = JackaroHandCardView.dealPokerInterval * i;

                        delayTime = waitForPreviousPlayers + previousPlayerAnimationTime + currentCardDelay;
                    }
                }

                // 创建并显示卡牌
                let dealCardItem = JackaroCardManger.instance.addHandCardItem(playerIdx, curDealPoker);

                if (dealCardItem) {
                    let isFinalPoker = (playerPosition == playerCount - 1 && i == totalLen - 1);
                    dealCardItem.setUpdated(false);
                    // {{ YUM: [优化] - 若手牌已被重置，直接走noDelayStart=true分支 }}
                    if (dealCardItem.isHandCardReset()) {
                        noDelayStart = true;
                    }
                    if (noDelayStart) {
                        // 没有延迟，直接执行发牌
                        JackaroCardManger.instance.dealPoker(playerIdx, dealCardItem, totalLen, i, isFinalPoker);

                        // 更新牌数并检查
                        this.updatePokerCount();

                        // 如果是最后一张牌，播放翻牌动画
                        if (isFinalPoker) {
                            // this.multiTouch(false);
                            this.playAllSelfCardsAfterDealing(noDelayStart);
                        }
                    } else {
                        Laya.timer.once(delayTime, this, () => {
                            JackaroCardManger.instance.dealPoker(playerIdx, dealCardItem, totalLen, i, isFinalPoker);

                            // 更新牌数并检查
                            this.updatePokerCount();

                            // 如果是最后一张牌，播放翻牌动画
                            if (isFinalPoker) {
                                // this.multiTouch(false);
                                // 延迟一小段时间确保最后一张牌的动画已经完成
                                Laya.timer.once(JackaroHandCardView.dealPokerTweenTime, this, this.playAllSelfCardsAfterDealing);
                            }
                        }, null, false);
                    }
                }
            }
        }

        /**
         * 在所有牌发完后播放自己手牌的翻转动画
         * @param noDelayStart 是否无延迟开始
         */
        private playAllSelfCardsAfterDealing(noDelayStart: boolean = false): void {

            // 获取自己的手牌
            const selfIdx = yalla.Global.Account.idx;
            const selfCards = JackaroCardManger.instance.getPlayerAllPokerItem(selfIdx);
            noDelayStart = noDelayStart ? noDelayStart : !yalla.Global.isFouce;

            if (selfCards && selfCards.length > 0) {
                // 检查是否有已经翻转为正面的牌
                let hasUpdatedCard = false;
                for (let i = 0; i < selfCards.length && !hasUpdatedCard; i++) {
                    if (selfCards[i].isUpdated && selfCards[i].isUpdated()) {
                        hasUpdatedCard = true;
                    }
                }

                // 如果已有翻转为正面的牌、需要无延迟或翻牌动画被禁用，则所有牌直接显示为正面
                if (hasUpdatedCard || !yalla.Global.isFouce || !JackaroCardManger.enableCardFlipAnimation) {
                    for (let i = 0; i < selfCards.length; i++) {
                        const card = selfCards[i];
                        // 直接显示为正面，无动效
                        card.playCardFlip(true);
                    }
                    JackaroCardManger.instance.selfCanTouchCard = true;
                    // 播放完本家翻牌后刷新其他玩家手牌牌背皮肤 {{ YUM: [新增] - 翻牌结束后刷新其他玩家牌背 }}
                    JackaroCardManger.instance.refreshOtherPlayersCardBackSkin(false);
                } else {
                    // 否则正常播放翻牌动画
                    let flipCount = 0;
                    let totalNeedFlip = selfCards.length;
                    // 逐张播放翻牌动画，每张牌间隔100毫秒
                    for (let i = 0; i < selfCards.length; i++) {
                        const card = selfCards[i];
                        // 设置每张牌的延迟
                        Laya.timer.once(i * 100, this, () => {
                            card.playCardFlip(false, () => {
                                flipCount++;
                                if (flipCount === totalNeedFlip) {
                                    JackaroCardManger.instance.selfCanTouchCard = true;
                                }
                            });
                        });
                    }
                    // 播放完本家翻牌后刷新其他玩家手牌牌背皮肤 {{ YUM: [新增] - 翻牌结束后刷新其他玩家牌背 }}
                    JackaroCardManger.instance.refreshOtherPlayersCardBackSkin();
                }
            }
        }

        /**
         * 更新记牌器显示的牌数
         */
        private updatePokerCount(): void {
            if (this.totalPokerCount > 1) {
                this.totalPokerCount--;
                if (this.pokerCountText && this.pokerCountText.visible) {
                    this.pokerCountText.text = this.totalPokerCount.toString();
                }
            } else {
                // 清除记牌器动画
                this.onDealPokerAniClear();
            }
        }

        /**
         * 发牌牌堆默认单张动画
         */
        private onDealPokerStart(totalLen: number) {
            if (!this.dealPokerAni) return;

            // 在失去焦点状态下不显示发牌动画
            if (!yalla.Global.isFouce) {
                this.dealPokerAni.visible = false;
                return;
            }
            JackaroCardManger.instance.resetDealPokerPlaceholderGPos();
            this.dealPokerAni.visible = true;
            this.dealPokerAni.scale(1, 1);

            // 显示记牌器文本
            if (this.pokerCountText) {
                this.pokerCountText.visible = true;
                this.pokerCountText.text = this.totalPokerCount.toString();
            }

            // 清理之前的动画
            Laya.Tween.clearAll(this.dealPokerAni);

            // 第一阶段：0.8 -> 0.9 -> 0.8 (100ms)
            // const firstPhase = new Laya.TimeLine();
            // firstPhase
            //     .to(this.dealPokerAni, { scaleX: 1, scaleY: 1 }, 100);

            // 第二阶段：等待600ms

            // 第三阶段：0.8 -> 1.0 -> 0 并隐藏 (300ms)
            const thirdPhase = new Laya.TimeLine();
            // thirdPhase.to(this.dealPokerAni, { scaleX: 1.2, scaleY: 1.2 }, 200)
            thirdPhase.to(this.dealPokerAni, { scaleX: 0, scaleY: 0 }, 150)
                .on(Laya.Event.COMPLETE, this, () => {
                    this.dealPokerAni.visible = false;
                    // 隐藏记牌器文本
                    if (this.pokerCountText) {
                        this.pokerCountText.visible = false;
                    }
                });

            // 执行动画序列
            // firstPhase.play(0, false);
            let delayTime = 0;
            if (totalLen == 4) {
                delayTime = 2050;
            } else {
                delayTime = 3000;
            }
            if (yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode()) {
                delayTime = 1200;
            }
            this.dealPokerAniTimer = Laya.timer.once(delayTime, this, () => { // 100ms + 900ms
                thirdPhase.play(0, false);
            });
        }

        /**
         * 发牌动画清除
         */
        private onDealPokerAniClear() {
            Laya.Tween.clearAll(this.dealPokerAni);
            this.dealPokerAni.visible = false;
            if (this.dealPokerAniTimer) {
                Laya.timer.clear(this, this.dealPokerAniTimer);
            }
            // 隐藏记牌器文本
            if (this.pokerCountText) {
                this.pokerCountText.visible = false;
            }
        }
        private playNextMsg() {
            yalla.data.jackaro.JackaroUserService.instance.nextMsg();
        }



        /**
         * 播放所有自己手牌的翻转动画
         */
        private playAllSelfCardsFlipAnimation(): void {
            // 获取自己的手牌
            const selfIdx = yalla.Global.Account.idx;
            const selfHandCardView = JackaroCardManger.instance.playerHandCardView[selfIdx];

            if (selfHandCardView) {
                // 手牌视图中添加此方法，所以我们要先获取自己手牌
                const selfCards = JackaroCardManger.instance.getPlayerAllPokerItem(selfIdx);
                if (selfCards && selfCards.length > 0) {
                    // 延迟一小段时间后开始翻牌
                    Laya.timer.once(300, this, () => {
                        // 逐张播放翻牌动画，每张牌间隔60毫秒
                        for (let i = 0; i < selfCards.length; i++) {
                            const card = selfCards[i];
                            // 设置每张牌的延迟
                            Laya.timer.once(i * 60, this, () => {
                                card.playCardFlip();
                            });
                        }
                    });
                }
            }
        }

        private onMoveChessBegin(chess: JackaroChess) {
            if (chess) {
                this.checkShowAllRoal(chess);
            }
        }
        private checkSetPlayerHash() {
            let playerPool = JackaroGamePlayerManager.instance.getPlayerPool();
            if (Object.keys(playerPool).length == yalla.data.jackaro.JackaroUserService.instance.room.roomMaxNum) {
                this.playerHash = playerPool;
                //延迟一帧等玩家player 节点创建完成
                // Laya.timer.frameOnce(1, this, () => {
                //     yalla.data.jackaro.JackaroUserService.instance.getGiftCnfList();
                // });
            }
        }

        // private playSelfForcedDiscardAni() {
        //     if (yalla.Global.isFouce) {
        //         Laya.timer.once(333, this, () => {
        //             if (!this.selfForcedDiscard || !this.selfForcedDiscard.parent) return;
        //             this.selfForcedDiscard.visible = true;
        //             this.selfForcedDiscard.alpha = 0;

        //             if (!this.selfForceDiscardTimeLine) {
        //                 this.selfForceDiscardTimeLine = new Laya.TimeLine();
        //                 this.selfForceDiscardTimeLine.on(Laya.Event.COMPLETE, this, this.onPlaySelfForcedDiscardAniEnd);
        //             } else {
        //                 this.selfForceDiscardTimeLine.reset();
        //             }
        //             this.selfForceDiscardTimeLine.addLabel("0", 0).to(this.selfForcedDiscard, { alpha: 0 }, 0);
        //             this.selfForceDiscardTimeLine.addLabel("1", 0).to(this.selfForcedDiscard, { alpha: 0 }, 433, null);
        //             this.selfForceDiscardTimeLine.addLabel("2", 0).to(this.selfForcedDiscard, { alpha: 1 }, 580, null);
        //             // this.selfForceDiscardTimeLine.addLabel("3", 0).to(this.selfForcedDiscard, { alpha: 0 }, 200, null);
        //             this.selfForceDiscardTimeLine.play("0", false);
        //         });
        //     }
        // }
        // private testPlaySelfForceDiscardAni() {
        //     if (!this.selfForcedDiscard) {
        //         this.selfForcedDiscard = new Laya.Image();
        //         this.selfForcedDiscard.skin = yalla.Font.isRight() ? "jackaro/txt_discard_ar.png" : "jackaro/txt_discard.png";
        //         this.selfForcedDiscard.centerX = 0;
        //         this.selfForcedDiscard.bottom = 687;
        //         this.topLayer.addChild(this.selfForcedDiscard);
        //     }
        //     this.playSelfForcedDiscardAni();
        // }
        // private onPlaySelfForcedDiscardAniEnd() {

        // }
        private onCardDiscardEnd() {
            TipsViewManager.getInstance().hideTip(TipViewType.Discard_Self_ForceCard);
            TipsViewManager.getInstance().hideTip(TipViewType.DISCARD_CARD);
            TipsViewManager.getInstance().hideTip(TipViewType.DISCARD_ALL_CARD);
        }
        public showRoundTime(idx, addTime: number): void {
            if (!this.roundTimeBox.visible) {
                if (yalla.Global.game_state != yalla.data.GameState.State_Sleep) {
                    if (this.roundTimeTween) this.resetRoundTime();
                    Laya.timer.clear(this, this.resetRoundTime);
                    var playerPos = JackaroGamePlayerManager.instance.posMap[idx];
                    if (!playerPos) return;
                    var xx = playerPos.x + 60;
                    var yy = playerPos.y;

                    this.roundTimeBoxLabel.text = "+" + addTime + "s";
                    this.roundTimeBoxLabel.font = "roundTimeBmfont";
                    this.roundTimeBox.visible = true;
                    this.roundTimeBox.alpha = 0;
                    this.roundTimeBox.pos(xx, yy);

                    this.roundTimeTween = Laya.Tween.to(this.roundTimeBox, { alpha: 1, y: yy - 50 }, 500, null, Laya.Handler.create(this, () => {
                        Laya.timer.once(1000, this, this.resetRoundTime);
                    }), 0, true);
                } else {
                    this.resetRoundTime();
                }
            }
        }

        private resetRoundTime(): void {
            Laya.Tween.clearAll(this.roundTimeBox);
            this.roundTimeTween = null;
            this.roundTimeBox.visible = false;
        }

        private setChatOff(msg: any): void {
            var isChatOff = msg.open;
            if (this._user && this._user.idx == msg.idx) {
                this._user.updateChatOff(isChatOff ? 1 : 0);
            }
            var player: JackaroGamePlayer = this.gamePlayer(msg.idx);
            if (player) player.isInChatOff = isChatOff;
        }
        /**
            * 判断是否是服务器重启,用玩家的思考时间来判断，如果存在有玩家思考时间和剩余时间大于0，
            * 说明正在进行玩家回合，这时候就不是重启，如果是重启的话，需要强刷
            * chessPanel 的version对比不一致也表示重启或者 新开一局游戏
            */
        private isServeRestart(gameData: any): boolean {
            let chessPanel = gameData.chessPanel;
            if (chessPanel && chessPanel.hasOwnProperty('version')) {
                if (chessPanel.version != this.version) {
                    this.version = chessPanel.version;
                    return true;
                }
            }
            return false;
        }
        /**
         * 处理同步游戏，用于断线重连回来以及出牌失败时恢复牌
         * isForce 字段用于是否强刷，如果是出牌失败了，此时强刷
         */
        public handleSyncGame(gameData: any, isForce: boolean = false) {
            if (!gameData) return;
            let isServeRestart = this.isServeRestart(gameData);
            if (isServeRestart) {
                //服务器重启时 重置玩家回合
                this._room.turnClear();
                yalla.data.jackaro.JackaroUserService.instance.clearOperateDataMap();
                JackaroChessManager.instance.resetKillChessCallBack();
                isForce = true;
            }
            yalla.Debug.logMore("************** handleSyncGame ******************");
            //初始化牌局
            let playerDataMap = gameData.playerDataMap;
            // let curOffLineSelfOperateTime = JackaroGamePlayerManager.instance.getCurOffLineSelfOperateTime();
            // let curOffLineSelfOperateTimeString = yalla.System.getNowTimeString(curOffLineSelfOperateTime);
            // let curNowTime = yalla.System.getNowTime();
            // let curTime = yalla.System.getNowTimeString(curNowTime);
            // let curOfflinePlayerIdx = JackaroGamePlayerManager.instance.getCurOfflinePlayerIdx();
            // let playerShowInfoInterface: yalla.data.jackaro.PlayerShowInfoInterface = JackaroGamePlayerManager.instance.getPlayerByIdx(curOfflinePlayerIdx);
            // let myGamePlayer: JackaroGamePlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);
            // if (playerShowInfoInterface) {
            //     yalla.Debug.filterLogMore("Game handleSyncGameData 时玩家信息", playerShowInfoInterface.nickName);
            // }
            //TODO::无论是否自己回合，断线重连后，都需要更新player cd；重新注册手牌player
            // {{ YUM: [修复] - 断线重连后，清理补牌状态 }}
            if (yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode())
                JackaroCardReplenishManager.instance.onAppStateChange(false);
            let selfHandPokerNumber: number;
            let curRound = 0;
            let sendPokerRound = 0;
            if (gameData.chessPanel) {
                curRound = gameData.chessPanel.outRound;
                sendPokerRound = gameData.chessPanel.sendPokerRound;
            }
            this._user && this._user.checkUpdateExtendState(curRound);
            for (let k in playerDataMap) {
                let playerData = playerDataMap[k];
                if (parseInt(k) == yalla.Global.Account.idx) {
                    JackaroCardManger.instance.registerCardLayer(parseInt(k), this.handCardLayer);
                    JackaroGamePlayerManager.instance.setAllowMoveTeammateChess(playerData.allowMoveTeammateChess);
                    selfHandPokerNumber = playerData.allowMoveTeammateChess;
                    if (playerData.forcedDiscard) {
                        //设置被强制弃牌id
                        JackaroCardManger.instance.setCurForcedDiscardIdx(parseInt(k));
                        JackaroGamePlayerManager.instance.setForcedDiscard(playerData.forcedDiscard);
                    }

                } else {
                    JackaroCardManger.instance.registerCardLayer(parseInt(k), this.otherHandCardLayer);
                }
                JackaroCardManger.instance.registerPlayerHandCardView(parseInt(k));
                let playerStatus = playerData.playerStatus;
                //同步玩家的思考时间
                let currentPlayerView = JackaroGamePlayerManager.instance.getGamePlayerByIdx(parseInt(k));
                if (currentPlayerView) {
                    currentPlayerView.playerStatus = playerStatus;
                    if (playerStatus && playerStatus !== yalla.data.jackaro.PlayerStatus.OPERATE_END) {
                        let chessPanel = gameData.chessPanel;
                        if (chessPanel) {
                            let remainThinkingTime = yalla.data.jackaro.JackaroUserService.instance.getOperateTime(chessPanel.thinkingEndTime)
                            let totalThinkingTime = chessPanel.thinkingTotalTime * 1000;
                            if (totalThinkingTime && remainThinkingTime > 0) {
                                currentPlayerView.remainThinkingTime = remainThinkingTime;
                                currentPlayerView.totalThinkingTime = totalThinkingTime;
                                currentPlayerView.resetWaitCdTime(remainThinkingTime, totalThinkingTime);
                                yalla.Debug.zzf("handleSyncGame");
                                yalla.Debug.zzf(chessPanel);
                                yalla.Debug.zzf({ remainThinkingTime, totalThinkingTime });
                                this.cProgress && this.cProgress.showProgress(remainThinkingTime, totalThinkingTime, currentPlayerView.color);
                                //TODO::如果是自己回合，且是加了回合时间，需要记录已经加了回合时间
                                if (parseInt(k) == yalla.Global.Account.idx && (totalThinkingTime) > JackaroGamePlayerManager.operateTime) {
                                    this._user && this._user.updateExtendState(true);
                                }
                            }
                        }
                    }
                }
            }
            let curPlayPoker = JackaroCardManger.instance.getCurPlayPoker();
            //是否手牌不一致
            let isSelfHandPokerMismatch = false;
            if (playerDataMap && playerDataMap[yalla.Global.Account.idx] && playerDataMap[yalla.Global.Account.idx].handPoker) {
                isSelfHandPokerMismatch = JackaroCardManger.instance.isSelfAllHandPokerItemsSame(playerDataMap[yalla.Global.Account.idx].handPoker);
            }
            if (!isForce && isSelfHandPokerMismatch && JackaroGamePlayerManager.instance.isCurOutRound(curRound) && JackaroGamePlayerManager.instance.isMyActive() && curPlayPoker) {
                //尝试重新发送之前的数据，如果有的话
                yalla.data.jackaro.JackaroUserService.instance.resendOperateMsg();
            } else {
                this.onDealPokerAniClear();
                JackaroChessManager.instance.offLineClear();
                JackaroChessManager.instance.removeAllChessKillSpineAni();
                JackaroChessManager.instance.removeChessExchangeSpineAni();
                JackaroCardManger.instance.offLineClear();
                // this.multiTouch(false);
                //TODO::这里也清理弃牌堆（restore中的清理弃牌堆逻辑可能没执行）。 新手引导也有用这个方法，需要留意
                JackaroCardManger.instance.checkClearRecordAlreadyPlayPoker(sendPokerRound, isServeRestart);
                JackaroGamePlayerManager.instance.setOutRound(curRound);
                yalla.data.jackaro.JackaroUserService.instance.checkIsTriggerPlayCard();
                //如果是服务器重启等，需要清理托管弹窗
                TipsViewManager.getInstance().hideAll(!isServeRestart);
                JackaroCardManger.instance.selfCanTouchCard = true
                this.clearAllPlayerHandPoker();
                for (let k in playerDataMap) {
                    let chessPosition = playerDataMap[k].chessPosition;
                    for (let k2 in chessPosition) {
                        //协议红色默认为 0，0 数据传递过来会是没有任何数值
                        if (!chessPosition[k2].gridColor) {
                            chessPosition[k2].gridColor = 0;
                        }
                        JackaroChessManager.instance.createChess(parseInt(k), playerDataMap[k].color, chessPosition[k2]);
                    }

                    let handPokers = playerDataMap[k].handPoker;
                    if (handPokers && handPokers.length > 0) {
                        for (let k3 in handPokers) {
                            JackaroCardManger.instance.createCardItem(parseInt(k), handPokers[k3], handPokers.length);
                        }
                        JackaroCardManger.instance.printPlayerSelfHandCard("同步后：");
                    } else {
                        let otherHandPokerNums = playerDataMap[k].handPokerNumber;
                        JackaroCardManger.instance.createOtherPlayerHandPoker(parseInt(k), otherHandPokerNums);
                    }
                }
                this.isNeedRestorePlayPoker = true;
            }
            //如果是快速模式，队友已经完赛，自己出牌7 走完一颗棋子完赛，需要可以选择自己剩下的棋子，此时 不可以设置自己还可以走队友棋子,服务器此时返回可以走队友棋子
            let room = yalla.data.jackaro.JackaroUserService.instance.room;
            if (room && room.isQuickMode()) {
                let teamIdx = JackaroGamePlayerManager.instance.getTeamIdx();
                let isTeamWin = JackaroChessManager.instance.isSelfChessAllInEndArea(teamIdx);
                if (isTeamWin) {
                    JackaroGamePlayerManager.instance.setAllowMoveTeammateChess(false);
                }
            }

            // {{ YUM: Add - 断线重连后如果非本回合手牌置灰. Source: AURA-X 协议要求 }}
            // 如果不是自己的回合，将手牌置灰
            if (!JackaroGamePlayerManager.instance.isMyActive()) {
                yalla.Debug.logMore(this.logTag + "handleSyncGame: 断线重连后非本回合，将手牌置灰");
                JackaroCardManger.instance.resetAllHandPokerState(yalla.Global.Account.idx, yalla.view.jackaro._operatorState.CAN_PLAY);
                // {{ YUM: [Update] - 使用新的管理类清除断线重连后非本回合的手牌效果提示 }}
                JackaroCardEffectHintManager.instance.clearAllCardEffects();
            }
            //同步时刷新预览
            JackaroCardManger.instance.refreshChessOrbit();
            return isServeRestart;
        }
        private updatePlayRuleMap(msg: any) {
            //恢复玩法规则
            if (msg.restoreGame && msg.restoreGame.playRuleMap) {
                JackaroCardManger.instance.setPlayRuleMap(msg.restoreGame.playRuleMap);
            }
        }
        /**
         * 恢复游戏
         */
        private restoreGame(msg: any, isServeRestart = false) {
            //恢复经验配置
            if (msg.restoreGame.expConfig) {
                yalla.data.jackaro.JackaroUserService.instance.room.setExpConfig(msg.restoreGame.expConfig);
            }
            let chessPanel = msg.restoreGame.gameData.chessPanel;
            if (!chessPanel) {
                return;
            }
            //恢复弃牌堆
            yalla.Debug.filterLogMore(isServeRestart + " Game restoreGame 222222222222222222222222222222222222:", msg.restoreGame.discardPokers);
            this.syncDiscardPile(chessPanel.sendPokerRound, isServeRestart, msg.restoreGame.discardPokers);
            // let currentPlayer = chessPanel.currentPlayer;
            let forcedDiscardPokerPlayer = chessPanel.forcedDiscardPokerPlayer;
            if (forcedDiscardPokerPlayer && forcedDiscardPokerPlayer != yalla.Global.Account.idx) {
                let curForceDiscardPlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(forcedDiscardPokerPlayer);
                if (curForceDiscardPlayer) {
                    curForceDiscardPlayer.isDiscard = true;
                }
            }
            //TODO::4.12 这段逻辑放到synGameData之前。现清理上一个玩家，再刷新当前用户剩余cd
            // //处理玩家切换
            this._room.handleTrustConfig(msg.restoreGame.trustConfig);

        }
        /**
         * 同步弃牌堆
         */
        public syncDiscardPile(sendPokerRound: number, isForceSync: boolean = false, discardPokers: Array<yalla.data.jackaro.DiscardedPokerInterface>) {
            JackaroCardManger.instance.checkClearRecordAlreadyPlayPoker(sendPokerRound, isForceSync);
            JackaroCardManger.instance.generalAlreadyPlayPoker(discardPokers);
        }
        private syncAllowPokerData(allowPokerData: Record<string, yalla.data.jackaro.PokerMoveDataInterface>) {
            JackaroCardManger.instance.handleAllowPokerData(allowPokerData as Record<string, yalla.data.jackaro.PokerMoveDataInterface>, true);
        }

        /**
         * 
         * 恢复出牌，主要用于出牌7 第二阶段和复杂玩法被别人抽牌
         * 这里不需要判定是否本回合，因为可能上一个用户操作10，我已经断线，到自己回合连上来，也需要显示被弃牌提示
         */
        private restorePlayPoker(msg: any) {
            if (!msg || !msg.restoreGame || !msg.restoreGame.gameData) return;
            let chessPanel = msg.restoreGame.gameData.chessPanel;
            if (!chessPanel) {
                return;
            }
            //恢复允许出牌数据
            let currentPlayer = chessPanel.currentPlayer;
            let allowPokerData = msg.restoreGame.allowPokerData;
            let isSelfTurn = (currentPlayer == yalla.Global.Account.idx);

            //如果存在无效牌或者被别人使用10强制弃牌,此时需要恢复
            if (isSelfTurn) {
                if (allowPokerData) {
                    yalla.Debug.yum("Game restorePlayPoker 恢复允许出牌数据", JSON.stringify(allowPokerData));
                    this.syncAllowPokerData(allowPokerData);
                } else {
                    //如果此时被别人出牌10时强制弃牌，allowPokerData会存在没有数据的情况，此时再检测一下是否需要弹出强制弃牌界面
                    JackaroCardManger.instance.checkShowDiscardSelfForceCard();
                }

                // } else {
                // JackaroCardManger.instance.createClientPreviewData();
            }
            let isPlayPoker7Second = msg.restoreGame.sevenPokerFirstNumber && msg.restoreGame.sevenPokerFirstNumber > 0;
            let currentPoker = chessPanel.currentPoker;
            //杀端回来时，之前选择的牌,之前选的牌，直接丢到弃牌堆
            let selectedPoker = chessPanel.selectedPoker;
            let flyToChessPanel = chessPanel.flyToChessPanel;
            // yalla.Debug.logMore("[JackaroUserService] restorePoker:" + JSON.stringify(chessPanel));
            //只有丢到弃牌堆的牌才恢复出牌
            let curOrbitPokerItem = JackaroCardManger.instance.getCardItemByPoker(yalla.Global.Account.idx, selectedPoker);
            if (flyToChessPanel && curOrbitPokerItem) {
                let selectedItem = chessPanel.selectedItem;
                // yalla.Debug.logMore("丢弃牌  flyToChessPanel:  " + flyToChessPanel + "  curOrbitPokerItem: " + curOrbitPokerItem + "selectedItem:" + selectedItem);
                let curSelectChessData = chessPanel.chess;
                JackaroCardManger.instance.handleAddPokerToDiscardPoker(selectedPoker);
                let isMutilStepPlayPoker = JackaroCardManger.instance.isMutilStepPlayPoker(selectedPoker);
                if (selectedPoker) {
                    //像出牌A 10 以及复杂玩法K 需要创建选项弹窗
                    if (isMutilStepPlayPoker) {
                        let isComplexPlayPokerK = JackaroCardManger.instance.isComplexPlayPokerK(selectedPoker);
                        let isComplexPlayPoker2 = JackaroCardManger.instance.is1V1Poker2(selectedPoker);
                        let isPokerA = JackaroCardManger.instance.isPokerA(selectedPoker);
                        let isPoker10 = JackaroCardManger.instance.isPoker10(selectedPoker);
                        if (!selectedItem) {
                            if (isPokerA || isPoker10 || isComplexPlayPokerK || isComplexPlayPoker2) {
                                //如果是扑克牌A 此时需要弹窗时，不能走恢复模式,因为扑克牌A 弹窗是通用逻辑，扑克牌10 和 复杂玩法K 弹窗是单独逻辑
                                JackaroCardManger.instance.doPlayPoker(selectedPoker, false);
                            } else {
                                JackaroCardManger.instance.doPlayPoker(selectedPoker, true);
                            }
                        } else {
                            if (isPokerA || isPoker10 || isComplexPlayPokerK || isComplexPlayPoker2) {
                                JackaroCardManger.instance.doPlayPoker(selectedPoker, true);
                                //出牌A 因为有起飞数据，在出牌时会把基地的棋子选中，此时
                                JackaroChessManager.instance.setAllChessChoose(false);
                            } else {
                                JackaroCardManger.instance.doPlayPoker(selectedPoker, false);
                            }
                        }
                    } else {
                        JackaroCardManger.instance.doPlayPoker(selectedPoker, true);
                    }
                }
                //像出牌A 10 以及复杂玩法K ，selectedItem 有值，说明是到了选择棋子阶段
                if (isMutilStepPlayPoker && selectedItem) {
                    JackaroChessManager.instance.showSelectChessView();
                }
                //断线前选择的棋子 这种情况 只有出牌7或者出牌J 才会出现
                if (curSelectChessData) {
                    let isPoker7 = JackaroCardManger.instance.isPoker7(selectedPoker);
                    let isPokerJ = JackaroCardManger.instance.isPokerJ(selectedPoker);
                    let curSelectChess = JackaroChessManager.instance.getChessById(curSelectChessData.idx + "_" + curSelectChessData.order);
                    if (isPoker7 && !isPlayPoker7Second) {
                        if (curSelectChess) {
                            JackaroChessManager.instance.userActiveClickChess(curSelectChess);
                        }
                    } else if (isPokerJ) {
                        if (curSelectChess) {
                            JackaroChessManager.instance.userActiveClickChess(curSelectChess);
                            JackaroChessManager.instance.updatePlayJackResultChessChoose();
                        }
                    }
                }
            }
            //以下未断线重连回来，需要出牌的数据恢复
            //断线时出牌7时,当前玩家第1次选择的数值,如果大于0,表示玩家正在选择第2个棋子，此时服务器记录第一阶段选子
            if (isPlayPoker7Second && curOrbitPokerItem) {
                JackaroCardManger.instance.setCurPlayPoker(currentPoker);
                JackaroGamePlayerManager.instance.setAllowMoveTeammateChess(false);
                let sevenPokerFirstOrder = msg.restoreGame.sevenPokerFirstOrder;
                let curMoveChessPlayerIdx = msg.restoreGame.sevenPokerFirstChessUser;
                if (allowPokerData && allowPokerData[currentPoker] && allowPokerData[currentPoker].commonResult) {
                    JackaroChessManager.instance.setCommonResult(allowPokerData[currentPoker].commonResult.playResultData);
                }

                let chess = JackaroChessManager.instance.getChessById(curMoveChessPlayerIdx + "_" + sevenPokerFirstOrder);
                if (chess) {
                    JackaroChessManager.instance.addcTurnAlreadyMoveChessInfoes(chess, msg.restoreGame.sevenPokerFirstNumber);
                    JackaroChessManager.instance.setHasAlreadyTurnMoveEnd(chess);
                }
                JackaroChessManager.instance.restorePlayPoker7(curMoveChessPlayerIdx, sevenPokerFirstOrder, msg.restoreGame.sevenPokerFirstNumber);
            }
            // 被抽取的牌,用于断线重连,如果不为None_Poker,此玩家在这一轮要弃掉此牌 1.4.5
            // if (msg.selfGameData && msg.selfGameData.drawnPoker) {
            //     let curOrbitPokerItem = JackaroCardManger.instance.getCardItemByPoker(yalla.Global.Account.idx, msg.selfGameData.drawnPoker);
            //     if (curOrbitPokerItem) {
            //         yalla.data.jackaro.JackaroUserService.instance.sendDiscardPoker([msg.selfGameData.drawnPoker]);
            //     }
            // }
        }
        /**
         * 聊天返回
         */
        private handleChat(msg: any): void {
            if (yalla.Global.IsGameOver || !msg || yalla.Mute.muted(msg.idx)) return;
            if (!msg.msg && !msg.extension) return;
            if (msg.idx == this._user.idx || (this._user.isInChatOff)) return;
            if (yalla.Global.Account.isPrivate == 1 && !yalla.Mute.isShowMsgByLimit(msg)) return;

            var color: number = null;
            var jackaroPlayerInfo: yalla.data.jackaro.PlayerShowInfoInterface = JackaroGamePlayerManager.instance.getPlayerByIdx(msg.idx);
            if (!jackaroPlayerInfo) return;
            let jackaroPlayerShowGameData: yalla.data.jackaro.PlayerShowGameDataInterface = JackaroGamePlayerManager.instance.getPlayerShowGameDataByIdx(msg.idx);
            let playerInfo: playerShowInfo = {
                fPlayerInfo: {
                    country: jackaroPlayerInfo.country, faceId: jackaroPlayerInfo.faceId, faceUrl: jackaroPlayerInfo.faceUrl,
                    ftype: 0,
                    idx: jackaroPlayerInfo.idx,
                    level: jackaroPlayerInfo.level,
                    nikeName: jackaroPlayerInfo.nickName,
                    placeId: jackaroPlayerInfo.placeId,
                    viplevel: jackaroPlayerInfo.vipLevel,
                    winCount: jackaroPlayerInfo.winCount,
                    totalCount: jackaroPlayerInfo.totalCount,
                },
                talkSkinId: jackaroPlayerInfo.talkSkinId,
                isSignUp: 0,
                sitNum: 0,
                isLeft: jackaroPlayerShowGameData.left ? 1 : 0,
                score: 0,
                winIndex: jackaroPlayerShowGameData.winIndex,
                isFighted: 0,
                isInSysTrust: (JackaroGamePlayerManager.instance.getGamePlayerByIdx(msg.idx).getTrustSystem() ? 1 : 0),
                prettyId: jackaroPlayerInfo.prettyId,
                roylevel: jackaroPlayerInfo.royalLevel
            };
            var insit = playerInfo.sitNum > -1;
            if (this._bottomView.chat && insit && !this._bottomView.chat.showLive) {//弹幕被打开后不显示气泡
                var player: JackaroGamePlayer = this.gamePlayer(msg.idx);
                if (player) {
                    player.showChat(msg);
                    color = player.color;
                }
            }
            msg.msg = msg.msg;
            this._bottomView && this._bottomView.onChat(msg, playerInfo, yalla.Global.Account.idx, color);
        }

        /**
         * 调用语音
         */
        private handleSpeakResponse(data: Array<number>): void {
            if (yalla.Global.isFouce) {
                for (let i = 0; i < data.length; i++) {
                    let player = this.gamePlayer(data[i]);
                    if (player) player.play();
                }
            }
        }

        private voiceChange(isOpen: boolean) {
            if (yalla.Global.Account.idx) {
                var player = this.gamePlayer(yalla.Global.Account.idx);
                player && player.playVoiceAni(0, false, isOpen ? "pause" : "stop");
            }
        }

        /**
         * zogo 情况下android 加入语音房后立刻执行举报，可能会失败；和原生沟通后延迟1s执行，同ludo
         */
        public joinVoiceCallback() {
            this._bottomView.joinVoiceCallback();
            JackaroGamePlayerManager.instance.forEach((player) => {
                if (player) {
                    var idx = player.idx;
                    if (yalla.Mute.muted(idx)) {
                        yalla.Voice.instance.muteRemoteAudioStream(idx, true, (val, uid) => {
                            if (val == 0) {
                                var cbPlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(uid);
                                if (cbPlayer) {
                                    cbPlayer.playVoiceAni(0, false, "stop");
                                    cbPlayer.isMute = true;
                                }
                            } else {
                                yalla.Voice.instance.muteRemoteAudioStream(uid, false, (val, uid) => {
                                    if (val == 0) {
                                        yalla.Mute.remove(uid);
                                    }
                                });
                            }
                        })
                    }
                }
            });
        }
        /**
         * 自己发送消息，不等返回先显示
         * 需要屏蔽敏感词
         */
        private handleChatMy(msg: any): void {
            if (!msg.msg && !msg.extension) return;
            msg['idx'] = yalla.data.jackaro.JackaroUserService.instance.user.idx;
            var player: JackaroGamePlayer = this.gamePlayer(msg.idx);
            if (player) {
                player.showChat(msg);
                this._bottomView && this._bottomView.onChat(msg, player.data, yalla.Global.Account.idx, player.color);
            }
        }

        private handleChatOff(msg: any): void {
            var player: JackaroGamePlayer = this.gamePlayer(msg.idx);
            player && (player.isInChatOff = msg.open);
            if (this._topView && this._topView.setting) this._topView.setting.updateChatSwitch();
        }

        /**
         * 金币更新
         * @param msg 
         */
        private handleUpdatePlayerCoin(msg: any): void {
            this.update();
        }

        private handleUpdateGiftCnf(msg: any): void {
            yalla.common.InteractiveGift.Instance.resData = msg;
            var posHash = {};
            for (var key in this.playerHash) {
                if (this.playerHash.hasOwnProperty(key)) {
                    var player: JackaroGamePlayer = this.playerHash[key];
                    if (player) {
                        // posHash[key] = JackaroGamePlayerManager.instance.posMap[player.idx];
                        //TODO::创建用户时，用户刚创建，还有适配操作，这里获取礼物消息，重新计算用户位置,key=player_idx
                        posHash[key] = player.head.localToGlobal(new Laya.Point());
                        JackaroGamePlayerManager.instance.posMap[String(player.idx)] = posHash[key];

                        if (player.head) player.head.isSendGift = !player.offLine;
                        if (player.data.giftId > 0) {
                            var giftData = yalla.common.InteractiveGift.Instance.getGiftItemData(player.data.giftId);
                            if (giftData) player.head.showGift(giftData.id, giftData.icon);
                        } else {
                            player.head.btn_gift.visible = player.head.isSendGift;
                        }
                    }
                }
            }
            yalla.common.InteractiveGift.Instance.handleUpdateGiftCnf(msg, posHash, this.giftAniBox, this.setGiftAniBgVisible.bind(this));
        }

        private setGiftAniBgVisible(v) {
            this.giftAniBg.visible = v;
        }

        private handleRevGift(msg: any): void {
            var msgList = yalla.common.InteractiveGift.Instance.msgList;
            if (msgList.length <= 1) yalla.common.InteractiveGift.Instance.handleRevGift(msg);
        }
        private updateGiftIcon(sendData: any): void {
            for (var key in this.playerHash) {
                if (key == 'player_' + sendData.recIdx) {
                    var player: JackaroGamePlayer = this.playerHash[key];
                    if (player) {
                        if (sendData.giftId > 0) {
                            player.head.showGift(sendData.giftId, sendData.icon);
                        }
                    }
                }
            }
        }
        private playEndPointAni(endPointInfo: any) {
            if (!yalla.Global.isFouce) return;
            if (!endPointInfo) return;
            let chess = endPointInfo.chess;
            let grid = endPointInfo.grid;
            let isPerfectEnter = endPointInfo.isPerfectEnter;
            let chessBeyondIdx = chess.beyondIdx;
            let myIdx = yalla.Global.Account.idx;
            let teamIdx = JackaroGamePlayerManager.instance.getTeamIdx();
            let curOperatePlayerIdx = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx();
            let isShowOldExp = JackaroChessManager.instance.isNewExpPlayLogic();
            // let isShowOldExp = true;
            let isCanShowPerfectEnter: boolean = false;
            //如果操作者是我，无论是队友或者自己，完美顶格都播放awesome
            let selfOperatePerfectEnter = ((chessBeyondIdx == myIdx || chessBeyondIdx == teamIdx) && curOperatePlayerIdx == myIdx);
            //如果操作者是队友，队友操作我的棋子，完美顶格，也是播放awesome
            let teamOperatePerfectEnter = ((chessBeyondIdx == myIdx) && curOperatePlayerIdx == teamIdx);
            if (isPerfectEnter && isShowOldExp && (selfOperatePerfectEnter || teamOperatePerfectEnter)) {
                isCanShowPerfectEnter = true;
            }
            if (chess && grid) {
                Laya.timer.once(80, this, () => {
                    yalla.Sound.playSound("firework");
                    if (isCanShowPerfectEnter) {
                        let endPointAni = this.getChessEndPointSpineAni();
                        this._baseMap.ani_box.addChild(endPointAni);
                        endPointAni.alpha = 0.7;
                        endPointAni.visible = true;
                        endPointAni.pos(grid.port.x, grid.port.y - JackaroChess.chessHeight);
                        if (yalla.Font.isRight()) {
                            endPointAni.play("2", false);
                        } else {
                            endPointAni.play("1", false);
                        }
                        Laya.timer.clear(this, this.delayReclycle);
                        Laya.timer.once(1500, this, this.delayReclycle);
                    } else {
                        this.endPointNormalAni.alpha = 0.7;
                        this.endPointNormalAni.visible = true;
                        this.endPointNormalAni.pos(grid.port.x, grid.port.y);
                        this.endPointNormalAni.play("idle", false);
                    }
                }, null, false);
            }
        }
        protected onMoveEnd(chess: JackaroChess) {
            //进终点的同时，也存在吃子
            if (chess) {
                if (chess.wayGrid.inEndArea) {
                    if ((chess.isPerfectEnterEndPoint || chess.isNormalEnterEndPoint)) {
                        if (!chess.getIsFirstTimeEnterEnd()) {
                            chess.setIsFirstTimeEnterEnd(true);
                            var myIdx = yalla.Global.Account.idx
                            if (!chess.expPlayer &&
                                JackaroGamePlayerManager.instance.getCurOperatePlayerIdx() == myIdx &&
                                (chess.beyondIdx == myIdx || (chess.beyondIdx == JackaroGamePlayerManager.instance.getTeamIdx() && !JackaroChessManager.instance.isSelfChessAllInEndArea(myIdx)))
                                // &&!yalla.data.jackaro.JackaroUserService.instance.room.notAddExpInTrust(myIdx)
                            ) {//自己出牌7进终点 因为消息不是服务端给的导致缺失expPlayer
                                chess.expPlayer = myIdx;
                            }
                            JackaroChessManager.instance.onChessEvent(chess, null, yalla.data.jackaro.ExpType.ENTER_END_AREA);

                        }
                    }
                    if (yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode()) {
                        // {{ YUM: [新增] - 使用封装的进终点抽牌奖励方法 }}
                        const currentRound = JackaroGamePlayerManager.instance.getOutRound();
                        const playerId = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx();
                        if (chess.moveStartGrid && !chess.moveStartGrid.inEndArea) {
                            // {{ YUM: [新增] - 检查当前玩家是否是最后一个进入终点位置的，如果是则不触发重点翻牌奖励 }}
                            const willCompleteGame = JackaroChessManager.instance.isSelfChessAllInEndArea(playerId);
                            if (!willCompleteGame) {
                                JackaroCardReplenishManager.instance.tryTriggerDrawRewardOnChessEndpoint(playerId, currentRound, chess.id);
                            } else {
                                yalla.Debug.yum('[抽牌奖励] 玩家完赛，跳过进终点抽牌奖励', {
                                    playerId,
                                    currentRound,
                                    chessId: chess.id
                                });
                            }
                        }
                    }
                }
                if (chess.playEmoji) {
                    /**todo:: 吃子表情层级从baseMap 的anibox 调整至 operateLayer，之前会被手牌遮挡*/
                    var emojiName = chess.moveType == yalla.data.jackaro.JackarooMoveType.MOVE_BE_HIT ? "cry" : "cool"
                    let pos = chess.localToGlobal(new Laya.Point(), true);
                    pos.x += chess.width * chess.scaleX / 2;
                    pos.y += chess.height * chess.scaleY / 2;
                    //TODO::记录bubble原因，快速模式使用扑克牌7，一步进入终点，剩余步数吃自己自然后复位，复位太快，棋盘上还残留表情（棋子复位同时移除bubble）
                    var bubble = this._chessBubbleManager.play(this.otherHandCardLayer, emojiName, pos);
                    JackaroChessManager.instance.addChessBubble(chess, bubble);
                    chess.playEmoji = false;
                }
                this.checkShowAllRoal(chess);
            }
        }
        private checkShowAllRoal(chess: JackaroChess) {
            let totalColor = [yalla.data.jackaro.JackarooColor.RED, yalla.data.jackaro.JackarooColor.GREEN, yalla.data.jackaro.JackarooColor.YELLOW, yalla.data.jackaro.JackarooColor.BLUE];
            let curRoalColor: number = -1;
            for (let color of totalColor) {
                //从-1开始，-4结束
                for (let i = -1; i > -5; i--) {
                    let endGridIdx = i;
                    if (JackaroChessManager.instance.checkShowRoal(color, endGridIdx)) {
                        curRoalColor = color;
                        if (!this.chessRoalArr[color]) {
                            this.chessRoalArr[color] = new Array<Laya.Image>();
                        }
                        if (!this.chessRoalArr[color][endGridIdx]) {
                            this.chessRoalArr[color][endGridIdx] = new Laya.Image();
                        }
                        this.chessRoalArr[color][endGridIdx].skin = `jackaro/icon_road${color}.png`;
                        if (!this.chessRoalArr[color][endGridIdx].parent) {
                            chess.parent.addChild(this.chessRoalArr[color][endGridIdx]);
                        }
                        this.chessRoalArr[color][endGridIdx].visible = true;
                        let curStation = { area: color, gridPosition: endGridIdx };
                        let curGrid: LudoGrid = JackaroBoardData.getGridByStation(curStation);
                        if (curGrid) {
                            this.chessRoalArr[color][endGridIdx].pos(curGrid.port.x - this.chessRoalArr[color][endGridIdx].width / 2, curGrid.port.y - this.chessRoalArr[color][endGridIdx].height / 2);
                        }
                    } else {
                        if (this.chessRoalArr[color] && this.chessRoalArr[color][endGridIdx]) {
                            this.chessRoalArr[color][endGridIdx].visible = false;
                        }
                    }
                }
            }

            // if (curRoalColor > -1) {
            //     JackaroChessManager.instance.updateChessZorderAfterShowRoal(curRoalColor);
            // }
        }
        public exitHander() {
            let msg = "Are you sure to quit the game? You will lose your bets and will not be able to team up with others for a period of time."
            yalla.common.Confirm.instance.isExit = true;
            yalla.common.Confirm.instance.showConfirm(msg,
                Laya.Handler.create(this, () => {
                    let room = yalla.data.jackaro.JackaroUserService.instance.room;
                    yalla.util.clogDBAmsg('10366', { gameType: room.gameType, gameId: GameType.JACKARO }, 1);
                    yalla.common.Confirm.instance.hideConfirm();
                    // yalla.common.Confirm.instance.connect.offAll()
                    yalla.data.jackaro.JackaroUserService.instance.quitRoom();

                    yalla.util.sendExitType(ExitType.INITIATIVE_QUIT);
                    // this.timer.clear(this, this.exitNow);
                    // 此处延时返回大厅 为了处理服务端没有返回退出或锦标赛结算的消息的情况下无法返回大厅问题
                    // var time = yalla.Global.Account.isPrivate == 3 ? 4000 : 2000;
                    // this.timer.once(time, this, this.exitNow);
                    // this.buryPointExit("Confirm");
                }),
                Laya.Handler.create(this, () => {
                    yalla.common.Confirm.instance.close();
                    // this.buryPointExit("Cancel");
                }), ["Confirm", "Cancel"]);
        }
        private testGameOver() {
            //let msg = `{"winMoney1":1900,"winMoney2":1900,"player":[{"idx":********,"faceId":0,"faceUrl":"https://file.yallaludo.com/DefaultHeadPicture/New/defaultPhoto_11.png","nickName":"Caesar5678","placeId":0,"friendType":0,"country":19,"level":7,"vipLevel":0,"winCount":0,"totalCount":0,"segId":0,"starNum":0,"prettyId":"**********","openChatOff":false,"royalLevel":0,"royalInvisible":false,"royalLevelNameAnimation":true,"giftId":0,"banTalkData":"","voiceType":"ZEGO","openGameGift":true,"exp":196,"vipExp":0},{"idx":********,"faceId":0,"faceUrl":"unkown","nickName":"test********","placeId":0,"friendType":0,"country":0,"level":0,"vipLevel":0,"winCount":0,"totalCount":0,"segId":0,"starNum":0,"prettyId":"********","openChatOff":false,"royalLevel":0,"royalInvisible":false,"royalLevelNameAnimation":false,"giftId":0,"banTalkData":"","voiceType":"ZEGO","openGameGift":true,"exp":0,"vipExp":0},{"idx":87568520,"faceId":0,"faceUrl":"unkown","nickName":"test87568520","placeId":0,"friendType":0,"country":0,"level":0,"vipLevel":0,"winCount":0,"totalCount":0,"segId":0,"starNum":0,"prettyId":"87568520","openChatOff":false,"royalLevel":0,"royalInvisible":false,"royalLevelNameAnimation":false,"giftId":0,"banTalkData":"","voiceType":"ZEGO","openGameGift":true,"exp":0,"vipExp":0},{"idx":87568532,"faceId":0,"faceUrl":"unkown","nickName":"test87568532","placeId":0,"friendType":0,"country":0,"level":0,"vipLevel":0,"winCount":0,"totalCount":0,"segId":0,"starNum":0,"prettyId":"87568532","openChatOff":false,"royalLevel":0,"royalInvisible":false,"royalLevelNameAnimation":false,"giftId":0,"banTalkData":"","voiceType":"ZEGO","openGameGift":true,"exp":0,"vipExp":0}]} `
            let msg = `{
            "winMoney1": 950,
                "player": [{
                    "idx": 87568686,
                    "faceUrl": "https://file.yallaludo.com/DefaultHeadPicture/New/defaultPhoto_3.png",
                    "nickName": "Caesar1001",
                    "country": 19,
                    "level": 40,
                    "prettyId": "8866857624",
                    "royalLevelNameAnimation": true,
                    "voiceType": "ZEGO",
                    "openGameGift": true,
                    "exp": 155620,
                    "talkSkinId": 12000,
                    "pokerSkinId": 50000
                }, {
                    "idx": 87568690,
                    "faceUrl": "unkown",
                    "nickName": "test87568690",
                    "prettyId": "87568690",
                    "voiceType": "ZEGO",
                    "openGameGift": true,
                    "talkSkinId": 12000,
                    "pokerSkinId": 50000
                }]
        }`;
            msg = JSON.parse(msg);
            this.gameOver(msg, true);
        }
        /**
         * 游戏结束
         *通知网关断流
         */
        private gameOver(msg: any, showfail: boolean): void {
            yalla.Native.instance.closeAllNativeView();
            this.playNextMsg();
            Global.IsGameOver = true;
            yalla.Global.IsGameOver = true;
            // UserService.instance.clearStream();
            yalla.Sound.stopMusic(yalla.Sound.audios.jackaroo_bg);
            yalla.data.jackaro.JackaroUserService.instance.clearQueue();
            this.resetCdProgress();
            if (msg.msgindex) {
                yalla.Global.ProtocolMsgIndex = msg.msgindex;
                yalla.Global.ProtoEnterRoomMsgIndex = 0;
            }
            var isPrivate = yalla.data.jackaro.JackaroUserService.instance.room.isPrivate;
            // let isPrivate = GameRoomType.CHAMPION;
            Laya.timer.clear(this, this.forceQuit);
            // yalla.Native.instance.mobClickEvent(buryPoint.DOMINO_SETTLE);
            // UserService.instance.recordPointQuit(yalla.data.ExitType.GAMEOVER_QUIT);

            yalla.Native.instance.closeUserProfile();
            yalla.Native.instance.closeReportMsg();

            yalla.util.closeAllDialog();
            this._bottomView && this._bottomView.hide();
            yalla.common.InteractiveGift.Instance.hideGiftView();
            this.cProgress && this.cProgress.hideProgress();
            TipsViewManager.getInstance().hideAll();
            // 检查游戏界面是否还在舞台上，如果已经被移除（比如玩家已退出），则不显示结算界面
            if (!this.displayedInStage) {
                yalla.Debug.log("游戏界面已被移除，不显示结算界面");
                return;
            }

            if (!this._resultView) {
                if (this._room && this._room.is1V1Mode()) {
                    this._resultView = new yalla.view.game.jackaro.JackaroResult(msg, false, showfail);
                } else {
                    this._resultView = new yalla.view.game.jackaro.JackaroTeamResult(msg, showfail);
                }
                this._resultView.name = 'resultView';
                this._resultView.backHandler = new Laya.Handler(this, this.backHallClear, null, true);
                var resultSound = this._resultView.isWin ? 'cheer' : 'ludo_fail';
                yalla.Voice.instance.gameOverSound(resultSound);
                // yalla.Sound.playSound(resultSound, Laya.Handler.create(this, () => { yalla.data.jackaro.JackaroUserService.instance.leaveVoiceRoom(); }));
            }
            if (this._resultView && !this._resultView.displayedInStage) Laya.stage.addChild(this._resultView);

            if (isPrivate == GameRoomType.CHAMPION) {
                if (yalla.util.IsBrowser) yalla.net.Client.instance.clear();//避免已经结算了，但是长时间未操作，已经还是收到game is finished
                else yalla.net.NativeClient.instance.clear();
            }
            yalla.Native.instance.removeGameRulesView();
            yalla.Native.instance.closeKeyBoard();

            // //棋子事件等取消
            // for (var key in this._chessHash) {
            //     this._chessHash[key].clear();
            // }
        }

        /**取消托管UI */
        public hideTrust(removeEvent: boolean) {
            // this.multiTouch(false);
            this.timer.clear(this, this.trustDely);
            removeEvent && this.removeTrustEvent();
        }
        // public multiTouch(isEnable: boolean) {
        //     yalla.Debug.logMore("=====multiTouch=====     新的 去除多点", isEnable);
        //     // Laya.MouseManager.multiTouchEnabled = isEnable;
        // }

        /**
         * 取消托管事件监听
         */
        public onTrust() {
            if (yalla.Global.IsGameOver) return;
            yalla.data.jackaro.JackaroUserService.instance.sendCancelTrust();
            this.timer.once(3000, this, this.trustDely);
            this.removeTrustEvent();
        }

        private updateTrust(player: JackaroGamePlayer, isTrust: boolean): void {
            player.head.trustee = isTrust;
        }
        /**
         * 重置玩家的托管状态
         */
        private resetTrust(): void {
            if (!this.playerHash) return;
            for (var key in this.playerHash) {
                var player: JackaroGamePlayer = this.playerHash[key];
                if (player) {
                    player.setTrustSystem(false);
                    this.updateTrust(player, false);
                }
            }
        }

        /**
         * 主动退出游戏，quitRoom未响应，则强制退出
         * 但是需要再结算界面弹出时候把这个计时器清掉
         */
        private forceQuit(isPrivate: number): void {
            yalla.data.jackaro.JackaroUserService.instance.clearQueue();
            if (isPrivate != GameRoomType.CHAMPION) {
                yalla.data.jackaro.JackaroUserService.instance.backHall(true);
            } else {
                if (!this._resultView) yalla.Native.instance.event(yalla.Native.instance.Event.BACKCHAMPION, [{ winnerId: -1 }]);
            }
        }

        /**
         * 主动发起退出房间的人才返回ready界面，否则等到gameevent=5结束游戏
         * @param msg 
         */
        private handleQuitGame(msg: any): void {
            // if (msg) {
            // yalla.Debug.filterLogMore(this.logTag + "=====主动退出游戏啦 handleQuitGame=====", msg);
            // }
            // if (msg.idx == yalla.data.jackaro.JackaroUserService.instance.user.idx) {

            // 检查结算界面是否已经显示，如果已显示则不立即返回大厅
            // 等待用户在结算界面手动关闭后再返回大厅
            if (this._resultView && this._resultView.displayedInStage) {
                yalla.Debug.log("结算界面已显示，收到退出消息但不立即返回大厅，等待用户手动关闭结算界面");
                return;
            }

            yalla.data.jackaro.JackaroUserService.instance.backHall(true, true, 500);
            // }
        }

        public onBackPressed() {
            this.exitHander();
        }

        /** 结算分享 */
        public share() {
            yalla.data.jackaro.JackaroUserService.instance.leaveVoiceRoom();
            yalla.Native.instance.shortScreenNative();
            yalla.util.clogShare(yalla.data.jackaro.JackaroUserService.instance.room.gameType);
            yalla.Native.instance.on(yalla.Native.instance.Event.SHARE, this, this.shareResponse);
        }
        private shareResponse(): void {
            yalla.data.jackaro.JackaroUserService.instance.backHall();
            yalla.Native.instance.off(yalla.Native.instance.Event.SHARE, this, this.shareResponse);
        }

        private onClickGift(e: Laya.Event): void {
            yalla.common.InteractiveGift.Instance.hideGiftView();
        }

        /** 点击gameUI 需要隐藏UI */
        private onClickGameUI(e: Laya.Event): void {
            yalla.Debug.logMore(this.logTag + "onClickGameUI");
            this._topView && this._topView.onClickGameUI();
            this._bottomView && this._bottomView.onClickGameUI();
            //TODO::这里就不隐藏互动礼物面板
            // yalla.common.InteractiveGift.Instance.hideGiftView();
            this._addTimeView.onClickGameUI();
            let player = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx)
            if (player && player.getTrustSystem()) {
                yalla.data.jackaro.JackaroUserService.instance.sendCancelTrust();
            }

            const cardManager = JackaroCardManger.instance;

            // 如果有卡牌正在触摸或拖动中，不重置手牌状态
            // if (cardManager.isTouchActive()) {
            //     yalla.Debug.logMore(this.logTag + "onClickGameUI: 有卡牌正在拖动中，不重置手牌状态");
            //     return;
            // }

            if (cardManager.selfCanTouchCard) {
                cardManager.selectCardList = [];
                cardManager.clearSelectPoker();
                cardManager.resetCardUpState(yalla.Global.Account.idx);
                cardManager.clearAllState();
                TipsViewManager.getInstance().hideTip(TipViewType.RULE_CARD);
                JackaroCardManger.instance.refreshChessOrbit();
            }

            if (this._topView) {
                this._topView.pageHander();
            }
        }
        private onClickInvalidPoker(poker: yalla.data.jackaro.Poker): void {
            JackaroCardManger.instance.refreshChessOrbit();
        }

        private resetCdProgress(): void {
            for (var key in this.playerHash) {
                var player: JackaroGamePlayer = this.playerHash[key];
                if (player) player.waitCdEnd()
            }
        }
        private hideChatAndTopView(): void {
            this._bottomView && this._bottomView.chat && this._bottomView.chat.hideAllChat();
            if (this._topView) {
                this._topView.pageHander();
            };
        }
        private _restHandStart = null;
        private _restDiscardPoker = null;
        public onForce() {//获得焦点
            yalla.Sound.stopAllSound();

            // 清理之前的定时器
            if (this._restHandStart) {
                clearTimeout(this._restHandStart);
                this._restHandStart = 0;
            }
            // 清理之前的定时器
            if (this._restDiscardPoker) {
                clearTimeout(this._restDiscardPoker);
                this._restDiscardPoker = 0;
            }

            // 确保清理所有触摸状态，防止焦点恢复时状态残留
            JackaroCardManger.instance.clearAllState();



            JackaroChessManager.instance.playerExpIndex = 0;
            //TODO::互动礼物切后台，只显示最后一条
            yalla.common.InteractiveGift.Instance.onBlur();
            var recvGiftList = yalla.common.InteractiveGift.Instance.msgList;
            if (recvGiftList && recvGiftList.length > 0) {
                yalla.common.InteractiveGift.Instance.eventSender(recvGiftList[recvGiftList.length - 1]);
                yalla.common.InteractiveGift.Instance.msgList = [];
            }
            yalla.Debug.logMore("~~~~~~~~~~~~~~~~~~Game onForce~~~~~~~~~~~~~~~~");
            // {{ YUM: [修复] - 应用获得焦点时，清理补牌状态 }}
            if (yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode())
                JackaroCardReplenishManager.instance.onAppStateChange(true);
            // 恢复手牌
            this._restHandStart = setTimeout(() => {
                let posHash = {};
                yalla.Debug.yum("~~~~~~~~~~~~~~~~~~Game resetPlayerHandCardView ~~~~~~~~~~~~~~~~");
                for (var key in this.playerHash) {
                    var player: JackaroGamePlayer = this.playerHash[key];
                    if (player) {
                        JackaroCardManger.instance.resetPlayerHandCardView(player.idx);

                        posHash[key] = player.head.localToGlobal(new Laya.Point());
                        JackaroGamePlayerManager.instance.posMap[String(player.idx)] = posHash[key];
                        JackaroCardManger.instance.refreshChessOrbit();
                    }
                }
                yalla.Debug.filterLogMore("=切到前台player位置==", posHash);
                yalla.common.InteractiveGift.Instance.posHash = posHash;
            }, 5);
            // 弃牌堆相关
            this._restDiscardPoker = setTimeout(() => {
                JackaroCardManger.instance.resetDiscardPoker(true);
            }, 10);;
            Laya.timer.callLater(this, () => {
                for (var key in this.playerHash) {
                    var player: JackaroGamePlayer = this.playerHash[key];
                    if (player) {
                        player.resetHeadScale();

                    }
                }
                for (var key in this._chessHash) {
                    this._chessHash[key].show();
                }
            })

            // this._baseMap && this._baseMap.startAni();
        }

        public onBlur() {//失去焦点
            yalla.Sound.stopAllSound();
            // 清理定时器
            if (this._restHandStart) {
                clearTimeout(this._restHandStart);
                this._restHandStart = 0;
            }
            yalla.Debug.yum("~~~~~~~~~~~~~~~~~~Game onBlur~~~~~~~~~~~~~~~~");
            // 确保清理所有触摸状态，防止焦点丢失时状态残留
            JackaroCardManger.instance.clearAllState();
            // {{ YUM: [修复] - 应用失去焦点时，清理补牌状态 }}
            if (yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode())
                JackaroCardReplenishManager.instance.onAppStateChange(false);
            // this._baseMap && this._baseMap.stopAni();
            var queue = yalla.data.jackaro.JackaroUserService.instance.queue;
            queue.items.forEach(msgs => {
                this.handlePlayMsg(msgs['msg'], false);
            });
            yalla.data.jackaro.JackaroUserService.instance.isQueue = false;

            for (var key in this.playerHash) {
                var player: JackaroGamePlayer = this.playerHash[key];
                if (player) player.stop();
            }

            for (var key in this._chessHash) {
                this._chessHash[key].hide();
            }
            if (JKCHtailing._ins) {
                JKCHtailing._ins.releaseTailing();
            }
        }

        gamePlayer(idx): JackaroGamePlayer {
            if (!this.playerHash) this.playerHash = {};
            if (this.playerHash['player_' + idx]) return this.playerHash['player_' + idx];
        }

        private clearAllPlayerHandPoker() {
            if (!this.playerHash) return;
            for (var key in this.playerHash) {
                var player: JackaroGamePlayer = this.playerHash[key];
                JackaroCardManger.instance.deleteOtherPlayerAllPokerItem(player.idx);
            }
        }

        /**
         * 移除选择棋子的事件
         */
        public removeChessEvent() {
            for (var key in this._chessHash) {
                this._chessHash[key].choose = false;
            }
        }

        public clearPlayer() {
            if (!this.playerHash) return;
            for (var key in this.playerHash) {
                if (this.playerHash.hasOwnProperty(key)) {
                    var player: JackaroGamePlayer = this.playerHash[key];
                    if (player) {
                        player.clear();
                        player = null;
                    }
                }
            }
            this.playerHash = null;
        }

        public backHallClear(): void {
            yalla.Debug.log('=====Game.backHallClear=======');
            // 清理定时器
            if (this._restHandStart) {
                clearTimeout(this._restHandStart);
                this._restHandStart = 0;
            }
            this.removeEvent();
            // this._testView && this._testView.clear();
            JackaroCardManger._instance && JackaroCardManger.instance.clear();
            // {{ YUM: [新增] - 清理补牌管理器资源，防止内存泄漏 }}
            if (yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode())
                JackaroCardReplenishManager.instance.dispose(false);
            JackaroChessOrbitSpecial._instance && JackaroChessOrbitSpecial.instance.clear();
            JackaroChessOrbitManager._instance && JackaroChessOrbitManager.instance.clear();
            JackaroChessManager._instance && JackaroChessManager.instance.clear();
            JackaroGamePlayerManager._instance && JackaroGamePlayerManager.instance.clear();
            EffectAniManager._instance && EffectAniManager.instance.clear();
            TipsViewManager._instance && TipsViewManager.getInstance().clear();
            LudoBuffManager._instance && LudoBuffManager.instance.clear();
            JkChooseChessManager._ins && JkChooseChessManager.ins.clear();
            JKCHtailing._ins && JKCHtailing.ins.clear()
            if (this._resultView) {
                Laya.timer.clearAll(this._resultView);
                this._resultView.clear();
            }
            this.clearEndpointAniArr();
            if (this.endPointNormalAni) {
                this.endPointNormalAni.clear();
                this.endPointNormalAni = null;
            }
            this._topView && this._topView.clear();
            this._bottomView && this._bottomView.clear();
            // this._baseMap && this._baseMap.clear();
            this.clearPlayer();
            yalla.SkAnimation.instance.clear();
            this._topView = null;
            this._bottomView = null;
            this._resultView = null;

            this._baseMap = null;
            this.playerHash = null;
            this.chessRoalArr = [];
            this.onDealPokerAniClear();
            // 清理定时器
            if (this._restHandStart) {
                clearTimeout(this._restHandStart);
                this._restHandStart = 0;
            }

            for (var key in this._chessHash) {
                this._chessHash[key].clear();
            }

            this._chessHash = null;
            this.resetRoundTime();
            ToastManager.clear();
        }

        public clear(isRemove: boolean = false): void {
            this.backHallClear();
            this.removeSelf();
            this.destroy(true);
        }
        private getChessEndPointSpineAni(): SpineAniPlayer {
            let endPointAni = Laya.Pool.getItemByClass('endPointAni', SpineAniPlayer);
            endPointAni.load(yalla.getSkeleton("jackaro/jinzhongdian/jinzhongdian"));
            this.endPointAniArr.push(endPointAni);
            return endPointAni;
        }
        /**
         * 延迟回收
         */
        public delayReclycle() {
            this.endPointAniArr.forEach(item => {
                if (item) {
                    item.removeSelf();
                    !item.destroyed && Laya.Pool.recover('endPointAni', item);
                }
            });
            this.endPointAniArr = [];
        }
        private clearEndpointAniArr() {
            this.delayReclycle();
            Laya.Pool.clearBySign("endPointAni");
        }
        /**
         * 保存翻牌动画设置
         * @param enabled 是否启用翻牌动画
         */
        public saveCardFlipAnimationSetting(enabled: boolean): void {
            JackaroCardManger.enableCardFlipAnimation = enabled;
        }

        /**
         * {{ YUM: [新增] - 初始化语音频道，处理Token缓存和获取逻辑 }}
         * 优先使用缓存的Token，如果无效则主动获取新Token
         */
        private initVoiceChannel(): void {
            try {
                const playerSelfGameData = JackaroGamePlayerManager.instance.getPlayerSelfGameData();

                // {{ YUM: [优化] - 检查是否为观战者，观战者不需要加入语音频道 }}
                const isSpectator = this._room.watchIdx > 0;
                if (isSpectator) {
                    yalla.Debug.log("[语音] 观战者跳过语音频道加入");
                    return;
                }

                // {{ YUM: [优化] - 检查语音类型是否有效 }}
                const voiceType = yalla.Global.Account.voiceType;
                if (!voiceType || (voiceType !== VoiceType.Agora && voiceType !== VoiceType.Zego)) {
                    yalla.Debug.log("[语音] 无效的语音类型:", String(voiceType));
                    return;
                }

                // {{ YUM: [修复] - 优先使用缓存的Token，确保token和类型匹配 }}
                if (this.isValidCachedVoiceToken(playerSelfGameData)) {
                    yalla.Debug.log("[语音] 使用缓存的语音Token加入频道");
                    yalla.data.jackaro.JackaroUserService.instance.voiceTokenInit(
                        voiceType,
                        playerSelfGameData.voiceToken,
                        playerSelfGameData.voiceCName
                    );
                } else {
                    // {{ YUM: [修复] - 当没有缓存的语音Token时，主动获取新的Token }}
                    yalla.Debug.log("[语音] 缓存Token无效，主动获取新Token");
                    yalla.data.jackaro.JackaroUserService.instance.getTokenVice();
                }
            } catch (error) {
                yalla.Debug.log("[语音] 语音频道初始化失败:", error);
                // {{ YUM: [兜底] - 出错时尝试获取新Token }}
                yalla.data.jackaro.JackaroUserService.instance.getTokenVice();
            }
        }

        /**
         * {{ YUM: [新增] - 检查缓存的语音Token是否有效 }}
         * @param playerSelfGameData 玩家自身游戏数据
         * @returns 是否有效
         */
        private isValidCachedVoiceToken(playerSelfGameData: yalla.data.jackaro.PlayerSelfGameDataInterface): boolean {
            return !!(playerSelfGameData &&
                playerSelfGameData.voiceToken &&
                playerSelfGameData.voiceToken.trim() !== '' &&
                playerSelfGameData.voiceCName &&
                playerSelfGameData.voiceCName.trim() !== '');
        }



    }
}