class JackaroMain extends BaseMain {
    private _client;
    private _gameBox: Laya.Box;
    private isFouce = true;

    constructor(type: number) {
        super();
        Laya.stage.bgColor = "#0e744d";
        if (this._gameView) this.clear();
        yalla.Global.Account.connectType = 2;
        if (!this._gameView) {
            // Laya.MouseManager.multiTouchEnabled = false;
            this.initBmpFont();
            this.initGameBox();
            if (type == 0) {
                this._gameView = new yalla.view.game.jackaro.Game();
                if (yalla.util.IsBrowser()) {
                    this._client = yalla.ClientBrower.instance;
                } else {
                    this._client = yalla.Client.instance;
                }
            }
            else this._gameView = new JackaroNoviceGame();
            this._gameView.name = 'gameView';
            this._gameBox.addChild(this._gameView);
            yalla.Global.onlineMode = (type == 0) ? true : false;
        }
    }
    private initBmpFont(): void {
        let mBitmapFont = new Laya.BitmapFont();

        let fontUrl = 'bmpfont/roundTimeBmfont.fnt';
        mBitmapFont.loadFont(fontUrl, new Laya.Handler(this, () => {
            mBitmapFont.setSpaceWidth(10);
            Laya.Text.registerBitmapFont("roundTimeBmfont", mBitmapFont);
        }));
    }
    public onNoviceGame(gameid, faceurl, name, realRoyLevel: string, viplevel: string) {
        this._gameView.init && this._gameView.init(gameid, faceurl, name, realRoyLevel, viplevel);
    }
    private initGameBox(): void {
        this._gameBox = new Laya.Box();
        this._gameBox.size(Laya.stage.width, Laya.stage.height);
        Laya.stage.addChild(this._gameBox);
    }

    onResize() {
        this._gameBox && !this._gameBox.destroyed && this._gameBox.size(Laya.stage.width, Laya.stage.height);
        this._gameView && !this._gameView.destroyed && this._gameView.onResize();
    }

    /**
     * 切入后台
     */
    public onBlur(): void {
        super.onBlur();
        this.isFouce = false;
        yalla.Global.game_state = yalla.data.GameState.State_Sleep;
        yalla.Sound.stopAllSound();

        if (this._gameView) {
            this._gameView.onBlur();
            yalla.common.InteractiveGift.Instance.onBlur();
        }
        if (!yalla.Global.IsGameOver) yalla.Sound.playMusic(yalla.Sound.audios.jackaroo_bg, 2);
    }

    /**
     * 切到前台
     */
    public onForce(): void {
        if (yalla.Global.IsGameOver) return;
        if (this.isFouce) {
            //TODO::android onLogin 会连续两次执行切前台行为；导致发牌动作被打断； Native中 onResume 其实可以return，但是考虑影响面，只在Jackaroo中修改
            return;
        }
        this.isFouce = true;

        super.onForce();
        yalla.Debug.log(yalla.Global.game_state + "===切到前台了，当前队列有多少消息===" + yalla.Global.isFouce);
        var size = yalla.data.jackaro.JackaroUserService.instance.queue.size();
        // if (size >= 5) {
        //     yalla.Global.isFouce = false;
        //     for (var i = 0; i < size; i++){
        //         yalla.data.jackaro.JackaroUserService.instance.nextMsg();
        //     }
        //     yalla.Global.isFouce = true;
        // }
        // yalla.Debug.log("当前队列长度" + yalla.data.jackaro.JackaroUserService.instance.queue.size())
        if (yalla.Global.game_state != yalla.data.GameState.State_Wake && yalla.Global.game_state.length > 0) {
            yalla.Global.game_state = yalla.data.GameState.State_Wake;
            if (this._gameView) this._gameView.onForce();

            if (yalla.Sound.canPlayMusic) {
                yalla.Global.isFouce = true;
                yalla.Sound.playMusic(yalla.Sound.audios.jackaroo_bg, 1);
            }
        }

        //重新心跳时间，发送心跳包
        if (this._client) this._client.resumeHeart();
    }

    /**
     * 登陆游戏
     * @param msg 
     */
    public onLogin(msg: any): void {
        super.onLogin(msg);
        yalla.Debug.filterLogMore('===Jackaro====onLogin', msg);
        if (msg) {
            yalla.data.jackaro.JackaroUserService.instance.clear();

            this.restartGame();
            yalla.data.jackaro.JackaroUserService.instance.user.update(msg);
            if (yalla.util.IsBrowser()) {
                //浏览器走这个逻辑，1.3.1使用原生 则不用主动发起
                yalla.ClientBrower.instance.init(Laya.Handler.create(this, () => {
                    yalla.data.jackaro.JackaroUserService.instance.ticketLogin();
                }));
            } else {
                //现在改为主动调起原生soket连接（因锦标赛）
                Laya.timer.once(yalla.Global.reconnect_interval, this, this.checkConnect);
            }
        }
    }
    private checkConnect() {
        this._client && this._client.checkConnect();
        // yalla.net.Jackaro.NativeClient.instance.checkConnect();
    }

    /**
     * 再来一局匹配成功
     */
    public restartGame(): void {
        super.restartGame();
        yalla.Global.game_state = yalla.data.GameState.State_Playing;
        yalla.data.jackaro.JackaroUserService.instance.register();
    }

    /**
     * onKickedOut
     * 被顶号
     * 音效关闭
     */
    public onKickedOut() {
        super.onKickedOut();
        yalla.Sound.canPlayMusic = false;
        yalla.Global.IsGameOver = true;
        yalla.Sound.stopAll();
        yalla.Debug.log("=======JackaroMain onKickedOut=======");
        this.clear(false);
    }
    /**
     * 断线重连
     * socket 断掉
     * game 的event 和 tween停止
     * 执行连接success 逻辑
     */
    public onReconnect(): void {
        if (yalla.Global.IsGameOver) return;
        super.onReconnect();
        Laya.timer.clearAll(this);

        if (this._client) this._client.clear();
        if (yalla.util.IsBrowser()) {
            //浏览器走这个逻辑，1.3.1使用原生 则不用主动发起
            yalla.ClientBrower.instance.init(Laya.Handler.create(this, () => {
                yalla.data.jackaro.JackaroUserService.instance.ticketLogin(true);
            }));
        } else {
            yalla.NativeWebSocket.instance.sendNetReConnect();
        }
    }

    /**
     * 重连倒计时结束 手动结束socket连接
     */
    public onReTimeOver() {
        super.onReTimeOver();
        this._client && this._client.clear();
    }

    /**
     * 退出游戏(接受其他玩家请求加入游戏,并结束当前游戏 ps:被动返回大厅)
     * @param msg 
     */
    public onQuitGame(msg: any): void {
        super.onQuitGame(msg);
        yalla.data.jackaro.JackaroUserService.instance.quitRoom();
        // yalla.data.Jackaro.UserService.instance.recordPointQuit(yalla.data.ExitType.JOIN_OTHER_GAME_QUIT);
    }
    private _timeOut = null;
    public onWebSocketOpen() {
        super.onWebSocketOpen();
        Laya.timer.clear(this, this.checkConnect);
        yalla.Debug.log('======JackaroMain.onWebSocketOpen====IsGameOver=' + yalla.Global.IsGameOver);
        if (yalla.Global.IsGameOver || !yalla.Global.onlineMode) return; //TODO::因为蛇棋现在新的网关链接，又因原生把蛇棋和联赛共用一个websocket链接，即便游戏结束 websocket也不会断，这里通过gameOver判定

        yalla.Client.instance.init(Laya.Handler.create(this, () => {
            yalla.data.jackaro.JackaroUserService.instance.ticketLogin();
        }));
        // yalla.common.connect.ReconnectControl.instance.connectSucss();
    }

    public updateMoney() {
        yalla.data.jackaro.JackaroUserService.instance.user.gold = yalla.Global.Account.currentGoldNum;
        yalla.data.jackaro.JackaroUserService.instance.user.diamond = yalla.Global.Account.currentDiamondNum;
        yalla.data.jackaro.JackaroUserService.instance.event(yalla.data.jackaro.EnumCmd.Game_Update_Player_Coin);
    }

    private removeInstance(): void {
        Laya.timer.clear(this, this.checkConnect);
        this._timeOut && clearTimeout(this._timeOut);
        Laya.timer.clearAll(this);
        yalla.data.jackaro.JackaroUserService.instance.clear();
        yalla.data.jackaro.JackaroUserService._instance = null;
        if (this._client) {
            this._client.removeEvent();
            this._client.clear();
        }
        yalla.Client.instance.firstLogin = true;
    }

    public backHallClear(remove: boolean = false): void {
        super.backHallClear();
        yalla.Global.game_state = '';
        yalla.Sound.canPlayMusic = false;
        this.removeInstance();
        if (!!this._gameView) {
            if (!remove) {
                this._gameView.backHallClear();
            } else {
                this._gameView.clear();
                this._gameView = null;
                if (this._gameBox) {
                    this._gameBox.removeSelf();
                    this._gameBox.destroy(true);
                    this._gameBox = null;
                }
            }

            Laya.Pool.clearBySign('JackaroCardItem');
        }
        // Laya.MouseManager.multiTouchEnabled = true;
    }

    public clear(removeView: Boolean = true): void {
        this.backHallClear(true);
        super.clear();
        yalla.Global.Account.connectType = 0;
    }
}