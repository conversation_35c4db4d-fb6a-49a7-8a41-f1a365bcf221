class JackaroMap extends ui.jackaro.mapUI {
    // public _map: Maps = null;
    static _instance: Map = null;
    public _color: number = 0;
    private chessPanel_id: number = yalla.data.jackaro.ChessBoardId;
    private _againstType: yalla.data.jackaro.GameAgainstType = null;
    constructor() {
        super();
        this.mouseThrough = false;
        this.visible = false;
    }
    set chessPanelId(val: number) {
        if (val && val != this.chessPanel_id) {
            this.chessPanel_id = val;
            var fevent = yalla.event.YallaEvent.instance;
            fevent.once(`Base_${val}.png`, this, () => {
                !this.destroyed && this.setSkin(this.chessPanel_id);
            })
            yalla.File.getFileByNative(`Base_${val}.png`);
            if (this._againstType == yalla.data.jackaro.GameAgainstType.ONE_VS_ONE) {
                fevent.once(`Checkerboard_2_${val}.png`, this, () => {
                    !this.destroyed && this.setSkin(this.chessPanel_id);
                })
                yalla.File.getFileByNative(`Checkerboard_2_${val}.png`);
            } else {
                fevent.once(`Checkerboard_${val}.png`, this, () => {
                    !this.destroyed && this.setSkin(this.chessPanel_id);
                })
                yalla.File.getFileByNative(`Checkerboard_${val}.png`);

            }
        }
    }

    private setSkin(chessPanel_id: number) {
        var isOneVOne = this._againstType == yalla.data.jackaro.GameAgainstType.ONE_VS_ONE;
        var [basePath, checkPath] = [yalla.File.cachePath + `/Base_${chessPanel_id}.png`,
        isOneVOne ? yalla.File.cachePath + `/Checkerboard_2_${chessPanel_id}.png` : yalla.File.cachePath + `/Checkerboard_${chessPanel_id}.png`];
        if (yalla.File.existed(checkPath)) {
            if (isOneVOne) {
                this.check.skin = yalla.File.filePath + `Checkerboard_2_${chessPanel_id}.png`;
            } else {
                this.check.skin = yalla.File.filePath + `Checkerboard_${chessPanel_id}.png`;
            }
            if (yalla.File.existed(basePath)) {//基础图要在棋盘已经成功的时候替换 否则隐藏（很多棋盘没有基础图，不是必须条件）
                this.bg.skin = yalla.File.filePath + `Base_${chessPanel_id}.png`;
                this.bg.visible = true;
            } else {
                this.bg.visible = false;
            }
        }
    }
    public setGameAgainstType(type: yalla.data.jackaro.GameAgainstType) {
        this._againstType = type;
        if (this.chessPanel_id == yalla.data.jackaro.ChessBoardId) {
            if (type == yalla.data.jackaro.GameAgainstType.ONE_VS_ONE) {
                this.check.skin = `jackaro/Checkerboard_2_52000.png`;
            } else {
                this.check.skin = `jackaro/Checkerboard_52000.png`;
            }
        }
    }
    showKillTip() { }
    hideVisibility() { }
    removeMask() { }
    showGolbleEye() { }
    clear() {
        this.destroy(true);
    }
    private addTurnSign(colors: number[]) {
        if (!this.visible) {
            JKCHtailing.ins.init(this.props_box);
            colors.forEach(color => {
                var side = this._color === color ? 0 : 2;
                var turnSign = new TurnGrid(side);
                var port = JackaroBoardData.getGridByName(`${color}_21`).port;
                turnSign.dataSource = port;
                this.sign_box.addChild(turnSign);
                JackaroBoardData.createBezier(color, side);
            })
        }
    }
    set color(color: number) {
        this._color = color;
        JackaroBoardData.setColor(color, this._againstType);
        if (this._againstType == yalla.data.jackaro.GameAgainstType.ONE_VS_ONE) {
            this.addTurnSign([0, 1]);
            if (color == 1) this.check.rotation = 180;
        } else {
            var rotation = color * 90
            this.check.rotation = rotation;
        }
        this.visible = true;
    }
}