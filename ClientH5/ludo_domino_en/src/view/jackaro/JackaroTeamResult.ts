module yalla.view.game.jackaro {
    export class JackaroTeamResult extends ui.ludo.resultPage.teamResultUI {
        private templet: Laya.Templet;
        private sk: Laya.Skeleton;
        public isWin: boolean = true;
        private winnerId: number = 0;
        private inSit: boolean = false;
        static instance: Result = null;
        private _nameItemHash: Object = {};
        public propNum: number = 0;
        public backHandler: Laya.Handler = null;
        private logTag: string = "JackaroTeamResult:";

        constructor(msg: any, showFali: boolean) {
            super();
            yalla.util.swapPos(this.again_btn, this.back_btn, "centerX");
            this.showInfo(msg, showFali);
            this.templet = new Laya.Templet();
            this.templet.on(Laya.Event.COMPLETE, this, this.parseComplete);
            this.templet.on(Laya.Event.ERROR, this, this.onError);
            this.bg_mask.size(Laya.stage.width, Laya.stage.height);
            this.addEvent();
            this.timer.once(10000, this, this.backHall);
            // this.timer.once(400, this, () => {
            this.showItem();
            // })
            yalla.Global.IsGameOver = true;
            yalla.util.closeAllDialog();
            this.name = "resultView";
            yalla.common.ChatManager.instance.clearChatMsgAni();
            if (yalla.Global.Account.roomid) yalla.util.clogDBAmsg('200019', { roomid: yalla.Global.Account.roomid }, 3);
            this.again_btn.label = "Play Now";
            yalla.util.isLocked = false;
        }
        private addEvent() {
            this.again_btn.once(Laya.Event.CLICK, this, this.playAgain);
            this.back_btn.on(Laya.Event.CLICK, this, this.backHander);
            if (this.isWin) {
                this.templet.loadAni(yalla.getSkeleton("result/win"));
            } else {
                this.templet.loadAni(yalla.getSkeleton("result/lose"));
                this.result_tx.color = "#ffffff";
                this.result_tx.strokeColor = "#297bc5";
            }
        }
        private backHander = yalla.util.withLock(() => {
            yalla.Native.instance.mobClickEvent(buryPoint.GAME_BACK);//埋点 结算返回大厅
            yalla.Global.Account.roomid &&
                yalla.util.clogDBAmsg('10269', { roomid: yalla.Global.Account.roomid });
            this.backHall();
        }
        )
        public backHall(e = null) {
            this.leaveVoiceRoom();
            this.removeEvent();
            yalla.data.jackaro.JackaroUserService.instance.backHall(true, false, 500);
            this.gameOverBack();
            // yalla.Native.instance.backHall(yalla.Native.instance.isBack, this.inSit ? { to: yalla.backHall_direction[yalla.Global.Account.isPrivate] } : null, 500);
        }
        private removeEvent() {
            this.timer.clearAll(this);
            this.back_btn.offAll();
            this.again_btn.offAll();
            yalla.Native.instance.off(yalla.Native.instance.Event.SHARE, this, this.backHall);
        }
        private leaveVoiceRoom() {
            //TODO::1.4.4.1 android playagai 等先退出语音房，音playsound 没有回掉（声网）
            // if (yalla.Voice.instance.joinSuccess)
            //         yalla.Voice.instance.levelGameRoom();
            yalla.Voice.instance.levelGameRoom_android();
        }
        public clear() {
            if (this.templet) {
                this.templet.offAll();
                this.templet.destroy();
                this.templet = null;
            }
            this.sk && this.sk.destroy();
            this.removeEvent();
            this.removeSelf();
            this.destroy(true);
        }

        /**
         * 1.4.4.1 只有ios 结算的back playAgain 才先关闭界面，避免下一次进入游戏有上一局的界面
         */
        public gameOverBack() {
            if (yalla.Native.instance.deviceType == DeviceType.IOS) {
                this.backHandler && this.backHandler.run();
                this.backHandler = null;
            }
        }

        private playAgain = yalla.util.withLock(() => {
            yalla.Global.IsGameOver = true;
            let room = yalla.data.jackaro.JackaroUserService.instance.room;
            let roomBets = room.getBets()
            let gameType = room.getGamePlayAgainGameType();
            let roomMaxNum = room.roomMaxNum;
            let roomId = room.roomId;
            this.leaveVoiceRoom();
            this.timer.clearAll(this);
            yalla.Native.instance.playAgain({
                gamePay: roomBets,
                gameId: GameType.JACKARO,
                gameType: gameType,
                playerNum: roomMaxNum,
                gameGroup: 0,
                roomId: roomId,
                isPrivate: 0
            });
            this.gameOverBack();
            yalla.util.clogDBAmsg('10419', { gameId: GameType.JACKARO, gameType }, 1);
        });

        // private share() {
        //     this.leaveVoiceRoom();
        //     this.timer.clearAll(this);
        //     yalla.util.clogShare(yalla.data.jackaro.JackaroUserService.instance.room.gameType);
        //     yalla.Native.instance.shortScreenNative();
        //     this.timer.clear(this, this.backHall);
        //     yalla.Native.instance.off(yalla.Native.instance.Event.SHARE, this, this.backHall);
        //     yalla.Native.instance.on(yalla.Native.instance.Event.SHARE, this, this.backHall);
        // }
        private parseComplete() {
            if (this.templet) {
                this.sk = this.templet.buildArmature(0);
                this.skBox.addChild(this.sk);
                this.skBox.setChildIndex(this.sk, 0);
                this.sk.pos(this.skBox.width / 2, this.skBox.height / 2);
                this.sk.play(0, false);
                Laya.Tween.to(this.result_tx, { alpha: 1 }, 200, null, null, 300);
                this.sk.on(Laya.Event.STOPPED, this, () => {
                    this.sk.play(1, true);
                });
            }
        }
        private showItem() {
            var _X = Laya.stage.width / 2;
            for (var i = 0; i < 2; i++) {
                Laya.Tween.to(this['team_' + i], {
                    x: _X
                }, 500, Laya.Ease.backInOut, null, i * 80)
            }
            this.timer.once(600, this, () => {
                this.mode1.play(0, false);
            })
            this.timer.once(700, this, () => {
                this.mode2.play(0, false);
            })
        }
        private onError() { }
        private showAwardInfo(champion: boolean, gold: number, prop: number, isQuit: boolean) {
            return new AwardInfo(champion, gold, prop, isQuit);
        }
        private showInfo(msg: any, showFali) {
            // this.gold_0.text = "+" + yalla.util.filterNum(msg.winMoney1);
            // this.gold_1.text = "+" + yalla.util.filterNum(msg.winMoney2);
            var players: Array<yalla.data.jackaro.PlayerShowInfoInterface> = msg.player;
            var len = players.length;
            // if (len > 0 && len < 4) {
            //     var btn_y = this["info_" + (len - 1)].y + 290;
            //     this.back_btn.y = btn_y;
            //     this.again_btn.y = btn_y;
            // }
            for (let i = 0; i < len; i++) {
                var playerInfo: yalla.data.jackaro.PlayerShowInfoInterface = msg.player[i];
                playerInfo.realRoyLevel = playerInfo.royalLevel;
                var idx = playerInfo.idx;
                var isMe = idx == yalla.Global.Account.idx;
                if (isMe) {
                    this.inSit = true;
                    if (i < 2) {
                        this.result_tx.text = "You Win";
                        this.self_0.visible = true;
                    } else {
                        this.result_tx.text = "You Lose";
                        this.isWin = false;
                        this.self_1.visible = true;
                    }
                    this.propNum = playerInfo.propNum;
                }

                var info: Laya.Image = this["info_" + i];
                if (!info) break;
                let isQuit = false;
                if (msg.leftData && msg.leftData[idx]) {
                    isQuit = true;
                }
                var award = this.showAwardInfo(i < 2, msg[`winMoney${i + 1}`], isMe ? playerInfo.propNum : 0, isQuit);
                info.addChild(award.view);
                var nameLb = this["nameItem_" + i];
                var nameItem: yalla.jackaro.NameItem = this._nameItemHash[idx];
                if (!nameItem && nameLb) {
                    nameItem = new yalla.jackaro.NameItem(nameLb, this['nikeName_' + i]);
                    this._nameItemHash[idx] = nameItem;
                }
                nameItem && nameItem.resultName(playerInfo, yalla.util.filterName(playerInfo.nickName, 13, "m"));
                // nameLb && yalla.Emoji.lateLable(yalla.util.filterName(playerInfo.fPlayerInfo.nikeName, 13, "m"), nameLb);
                var head: yalla.jackaro.Head = new yalla.jackaro.Head();
                head.mouseEnabled = false;
                head.pos(52, 30);
                head.showFaceBox(playerInfo);
                info.addChild(head);

            }
            if (!this.inSit) {
                this.result_tx.fontSize = 50;
            }
        }
        public frameLoopName(): void {
            for (var key in this._nameItemHash) {
                var nameItem = this._nameItemHash[key];
                nameItem.frameLoopName();
            }
        }
    }
}