module yalla.view.game.jackaro {
    /**
     * 房间排名奖励信息显示UI 依赖于ludoData Ludo.Room.ts中的数据
     */
    export class RoomBattleRecordInfo extends Laya.Box {
        private infoView: any = null;
        private _labelPropList = ['color', 'fontSize', 'stroke', 'strokeColor'];
        private isRecordUpdate: boolean = false;
        private _room: yalla.data.jackaro.Room = yalla.data.jackaro.JackaroUserService.instance.room;
        constructor() {
            super();
            this.size(460, 290);
            this.init();
        }
        private init() {
            if (yalla.Font.isRight()) {//阿语
                this.infoView = new ui.jackaro.sub.teamBattleRecordInfoarUI();
                this.arInit();
            } else {
                this.infoView = new ui.jackaro.sub.teamBattleRecordInfoenUI();
                this.enInit();
            }
            if (this._room && this._room.is1V1Mode()) {
                this.update1v1UI();
            }
            this.updateBattleRecord();
            this.addChild(this.infoView);
        }
        /**
         * 更新1v1UI
         */
        private update1v1UI() {
            if (this.infoView) {
                this.infoView.bg.height = 236;
                this.infoView.recordInfoBg1.height = 45;
                this.infoView.recordInfoBg2.height = 45;
                this.infoView.recordInfoBg2.y = 129;
                this.infoView.img_teamA_self_bg.y = 75;
                this.infoView.teamATxt.y = 81;
                this.infoView.teamBTxt.y = 138;
                this.infoView.recordInfo0.y = 74;
                this.infoView.recordInfo1.y = 132;
                this.infoView.recordInfo2.visible = false;
                this.infoView.recordInfo3.visible = false;
                this.infoView.rank_roomId.y = 187;
                if (yalla.Font.isRight()) {//阿语
                    this.infoView.rank_roomType.x = 365;
                } else {
                    this.infoView.rank_roomType.x = 110;
                }
                this.infoView.teamATxt.visible = false;
                this.infoView.teamBTxt.visible = false;
            }
        }
        private enInit() {
            this.left = 30;
            this.top = 95;
        }
        private arInit() {
            this.left = 220;
            this.top = 95;
        }
        public updateBattleRecord() {//组队模式UI和数据绑定

            if (this.infoView && this._room && this._room.killedResult) {
                let roomTypeSkin = yalla.Font.isRight() ? "jackaro/img_gameTitle" + this._room.gameType + "_ar.png" : "jackaro/img_gameTitle" + this._room.gameType + ".png";
                this.infoView.rank_roomType.skin = roomTypeSkin;
                this.infoView.rank_1.text = yalla.util.filterNum(this._room.getBets() * 2 * 0.95);
                if (yalla.Font.isRight()) {
                    this.infoView.rank_roomId.text = yalla.util.getLangText("ROOM ID") + ": " + this._room.roomId;
                    this.infoView.rank_roomType.scale(0.85, 0.85);
                } else {
                    this.infoView.rank_roomId.text = yalla.util.getLangText("ROOM ID") + ": " + this._room.roomId;
                }
                let killedResult = this._room.killedResult;
                if (!killedResult) {
                    return;
                }
                let myIdx = yalla.Global.Account.idx;
                //首先筛选A队数据
                let teamAInfoes: Array<{
                    idx: number, killChess: number,
                    chessKilled: number
                }> = [];

                let teamBInfoes: Array<{
                    idx: number, killChess: number,
                    chessKilled: number
                }> = [];
                let is1V1 = this._room && this._room.is1V1Mode();
                for (let playerIdx in killedResult) {
                    let info = killedResult[playerIdx];
                    let team = JackaroGamePlayerManager.instance.getTeam(parseInt(playerIdx));
                    if (!is1V1) {
                        if (team == "A") {
                            teamAInfoes.push({
                                idx: parseInt(playerIdx),
                                killChess: info.killChess,
                                chessKilled: info.chessKilled
                            })
                        } else {
                            teamBInfoes.push({
                                idx: parseInt(playerIdx),
                                killChess: info.killChess,
                                chessKilled: info.chessKilled
                            })
                        }
                    } else {
                        teamAInfoes.push({
                            idx: parseInt(playerIdx),
                            killChess: info.killChess,
                            chessKilled: info.chessKilled
                        })
                    }
                }
                if (!is1V1) {
                    let selfTeamInfo = JackaroGamePlayerManager.instance.getTeam(myIdx);
                    if (selfTeamInfo == "A") {
                        if (teamAInfoes[0] && teamAInfoes[0].idx != myIdx) {
                            teamAInfoes.reverse();
                        }
                        this.infoView.img_teamA_self_bg.visible = true;
                    } else {
                        if (teamBInfoes[0] && teamBInfoes[0].idx != myIdx) {
                            teamBInfoes.reverse();
                        }
                        this.infoView.img_teamB_self_bg.visible = true;
                    }
                } else {
                    if (teamAInfoes[0] && teamAInfoes[0].idx != myIdx) {
                        teamAInfoes.reverse();
                    }
                    this.infoView.img_teamA_self_bg.visible = true;
                    this.infoView.img_teamB_self_bg.visible = false;
                }

                let totalTeamInfoes: any[] = teamAInfoes.concat(teamBInfoes);
                //如果只有两条数据，说明是1v1模式，自己的数据排在前面
                if (is1V1) {
                    totalTeamInfoes.sort((a, b) => {
                        if (a.idx == myIdx) {
                            return -1;
                        }
                        if (b.idx == myIdx) {
                            return 1;
                        }
                        return 0;
                    })
                }
                for (let i = 0; i < totalTeamInfoes.length; i++) {
                    let teamInfo = totalTeamInfoes[i];
                    let infoView = this.infoView["recordInfo" + i];
                    let chessColor = JackaroGamePlayerManager.instance.getColorByIdx(teamInfo.idx);
                    infoView.teamIcon.skin = `jackaro/chess_50000_${chessColor}.png`;
                    infoView.teamIcon.visible = true;
                    if (is1V1) {
                        if (yalla.Font.isRight()) {
                            infoView.teamIcon.x = 469
                            infoView.playerNameLabel.x = 278;
                        } else {
                            infoView.teamIcon.x = -47;
                            infoView.playerNameLabel.x = 15;
                        }
                    }

                    let playerShowInfoInterface: yalla.data.jackaro.PlayerShowInfoInterface = JackaroGamePlayerManager.instance.getPlayerByIdx(teamInfo.idx);
                    if (playerShowInfoInterface && !this.isRecordUpdate) {
                        if (teamInfo.idx == myIdx) {
                            infoView.playerNameLabel.color = "#ffffff";
                        }
                        yalla.Emoji.lateLable(yalla.util.filterName(playerShowInfoInterface.nickName), infoView.playerNameLabel, this._labelPropList);
                        // infoView.playerNameLabel.text = yalla.util.filterName(playerShowInfoInterface.nickName);
                    }
                    infoView.killNumLabel.text = teamInfo.killChess || 0;
                    infoView.beKilledLabel.text = teamInfo.chessKilled || 0;
                }

                this.isRecordUpdate = true;

            }
        }

    }
}