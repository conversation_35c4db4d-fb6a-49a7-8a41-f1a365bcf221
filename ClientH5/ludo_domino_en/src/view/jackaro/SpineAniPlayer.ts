
class SpineAniPlayer extends Laya.Box {
    private sk: Laya.Skeleton;
    private tm: Laya.Templet = new Laya.Templet();
    private nextPlayAniName: string;
    private nextIsLoop: boolean;
    private playCompleteCallback: Function;
    private curResPath: string;
    private curPlayAniName: string;
    private nextBlendMode: string;
    private nextSpeed: number;
    private curAniMode: number;
    //默认显示，有时加载中，加载完成后会被设置true,有时需要加载完成设置成默认值
    private curVisible: boolean = true;
    constructor(resPath: string = null) {
        super();
        this.tm.once(Laya.Event.COMPLETE, this, () => {
            if (!this.tm) return;
            if (this.curAniMode) {
                this.sk = this.tm.buildArmature(this.curAniMode);
            } else {
                this.sk = this.tm.buildArmature(0);
            }
            this.addChild(this.sk);
            // yalla.Debug.logMore("SpineAniPlayer tm.on(Laya.Event.COMPLETE 123, this, () => {", "this.sk:", this.sk, "this.curResPath:", this.curResPath, "this.curPlayAniName:", this.curPlayAniName, "this.nextPlayAniName:", this.nextPlayAniName);
            this.sk.on(Laya.Event.STOPPED, this, () => {
                this.visible = false;
                // yalla.Debug.logMore("SpineAniPlayer end", "parent:", this.parent, this.curResPath, "this.curPlayAniName:", this.curPlayAniName, yalla.getTimeHMS());
                this.playCompleteCallback && this.playCompleteCallback();
                this.playCompleteCallback = null;
            });
            if (this.nextPlayAniName != null) {
                // yalla.Debug.logMore("SpineAniPlayer tm.on(Laya.Event.COMPLETE 456, this, () => {", "this.nextPlayAniName:", this.nextPlayAniName, "this.nextIsLoop:", this.nextIsLoop, "this.playCompleteCallback:", this.playCompleteCallback);
                if (this.nextBlendMode) {
                    this.setAniblendMode(this.nextBlendMode);
                }
                if (this.nextSpeed) {
                    this.setSpeed(this.nextSpeed);
                }
                this.play(this.nextPlayAniName, this.nextIsLoop, this.playCompleteCallback);
                this.nextPlayAniName = null;
            }
        });
        if (resPath) {
            this.load(resPath);
        }
    }
    setAniblendMode(blendMode: string) {
        if (this.sk && this.sk.getStyle()) {
            this.sk.blendMode = blendMode;
        } else {
            this.nextBlendMode = blendMode;
            // yalla.Debug.logMore("SpineAniPlayer setAniblendMode blendMode:", blendMode, "this.sk:", this.sk, "this.curResPath:", this.curResPath, "this.curPlayAniName:", this.curPlayAniName, "this.nextPlayAniName:", this.nextPlayAniName);
        }
    }
    setAniMode(aniMode: number) {
        this.curAniMode = aniMode;
    }
    load(resPath: string) {
        //使用this.curResPath判断是否已经加载过，避免重复加载
        if (resPath && this.tm && !this.sk && !this.curResPath) {
            this.curResPath = resPath;
            this.tm.loadAni(resPath);
        }
    }
    clear() {
        this.curVisible = true;
        if (this.tm) {
            this.tm.destroy();
            this.tm = null;
        }
        if (this.sk) {
            this.sk.destroy();
            this.sk = null;
        }
        this.nextPlayAniName = null;
        this.playCompleteCallback = null;
        this.curAniMode = 0;
        this.curResPath = null;
    }
    stop() {
        if (!!this.sk) {
            this.sk.stop();
        }
    }
    setSpeed(speed: number) {
        if (this.sk) {
            this.sk.playbackRate(speed);
        } else {
            this.nextSpeed = speed;
        }
    }
    setCurVisible(visible: boolean) {
        this.curVisible = visible;
        this.visible = this.curVisible;
    }
    play(aniName: string = "", isLoop: boolean = false, playCompleteCallback: Function = null) {
        if (!!this.sk) {
            if (aniName == "") {
                this.sk.play(0, isLoop);
            } else {
                this.sk.play(aniName, isLoop);
            }
        } else {
            this.nextPlayAniName = aniName;
            this.nextIsLoop = isLoop;
        }
        this.visible = this.curVisible;
        this.curPlayAniName = aniName;
        this.playCompleteCallback = playCompleteCallback;
        // yalla.Debug.logMore("SpineAniPlayer play aniName:", aniName, "isLoop:", isLoop, "this.sk:", this.sk, "curResPath:", this.curResPath, "this.nextPlayAniName:", this.nextPlayAniName);
    }
    getAniName() {
        return this.curPlayAniName;
    }
}