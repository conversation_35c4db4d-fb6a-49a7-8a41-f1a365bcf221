module yalla.view.game.jackaro {

    export class TestView {
        private _client;
        constructor(ui: JackaroMap) {

            if (yalla.util.IsBrowser()) {
                this._client = yalla.ClientBrower.instance;
            } else {
                this._client = yalla.Client.instance;
            }

            if (ui.close_socket) {
                (ui.close_socket as <PERSON>a.But<PERSON>).visible = true
                ui.close_socket.on("click", this, () => {
                    yalla.Native.instance.sendNetTyrClose();
                    yalla.NativeWebSocket.instance.sendNetClose();

                    if (yalla.Native.instance.deviceType == DeviceType.Android) {
                        this.handleSocketClose();
                    }
                    yalla.Debug.logMore("交换牌 %%%%%%%% 触发断线 %%%%%%%");
                });//测试网络掉线
            }
            if (ui.debug_log) {
                (ui.debug_log as Laya.Button).visible = true;
                ui.debug_log.label = "8.28 15:32";
                ui.debug_log.on("click", this, () => {
                    yalla.Debug.logMore("交换牌 ********日志打点*********");
                });//测试网络掉线
            }
            this.initEvent();
        }
        private initEvent() {
            this._client.on('Game_SocketClose', this, this.handleSocketClose);
        }

        /**
         * tyr 主动发起断线，这里需要自己主动去重连 sendNetReConnect
         */
        private handleSocketClose() {
            yalla.Debug.logMore("**Jackaroo TestView Game_SocketClose**");
            yalla.common.connect.ReconnectControl.instance.connect(true, false);
            yalla.NativeWebSocket.instance.sendNetReConnect();
        }

        public clear() {
            if (this._client) {
                yalla.Debug.logMore("**Jackaroo TestView clear **");
                this._client.off('Game_SocketClose', this, this.handleSocketClose);
            }
        }
    }
}