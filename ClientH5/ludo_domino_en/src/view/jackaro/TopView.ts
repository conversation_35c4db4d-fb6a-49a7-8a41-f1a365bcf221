module yalla.view.game.jackaro {
    /**
     * topMc
     */
    export class TopView {
        private _gameUI: ui.jackaro.gameUI;
        private _topUI: Laya.View;
        private _fillNum: number;
        private _playerNums: number = 0;
        public setting: yalla.common.Setting;
        private rank: jackaro.RoomBattleRecordInfo = null;
        private _leftShow: number = 3000;
        private _handActiveLeftShow: number = 8000;//手动主动点击战绩，展示8秒

        constructor(gameUI: ui.jackaro.gameUI) {
            this._gameUI = gameUI;
            if (yalla.Font.lan == 'en') {
                this._topUI = new ui.jackaro.item.game_top_enUI();
            } else {
                this._topUI = new ui.jackaro.item.game_top_arUI();
            }

            gameUI.topLayer.addChild(this._topUI);
            // gameUI.addChild(this._topUI);
            this.initEvent();
        }
        public set isSpectator(val: boolean) {
            var isShow = (!val && Global.Account.sameSkinBuySwitch);
            this._topUI['store_btn'] && (this._topUI['store_btn'].visible = isShow);
            // val && this.initSpectatorEvent();
        }

        public set audienceNum(val: number) {
            (this._topUI['audience_num'] as Laya.Label).text = val < 1000 ? String(val) : "999+";
        }

        // private initSpectatorEvent() {
        //     this._topUI['titleSpectator'].on(Laya.Event.CLICK, this, this.showSpectator);
        // }
        private showSpectator() {
            Audience.instance.popup(true, true);
        }
        private initEvent(): void {
            this._topUI['settingBtn'].on(Laya.Event.CLICK, this, this.onClickSetting);
            this._topUI['winBtn'].on(Laya.Event.CLICK, this, this.onClickPrize);
            this._topUI['store_btn'].on(Laya.Event.CLICK, this, this.showSameStore);
            yalla.event.YallaEvent.instance.on("watchNumChange", this, this.onWatchNumChange);
        }

        private onWatchNumChange(n: number) {
            this.audienceNum = n || 0
        }

        public init(): void {
            this.onClickPrize();
        }
        public updateMoney() {

        }

        public updateTitle(room: yalla.data.Room): void {

            if (room.isPrivate == GameRoomType.VIP) {
                this._gameUI.topImg.skin = yalla.getPublic('top-bg');
            } else {
                this._gameUI.topImg.skin = yalla.getDomino('top-bg');
            }
        }

        public initFillNum(num: number): void {
            this._fillNum = num;
            this.updateFillNum(0);
        }

        public updateFillNum(subNum: number): void {
            this._fillNum -= subNum;
            if (this._fillNum < 0) this._fillNum = 0;
        }
        /**观战的chaton 不显示 */
        public setWatchSetting(): void {
            if (!this.setting) this.setting = new yalla.common.Setting(this._topUI);
            this.setting.setWatchSetting(yalla.Font.lan == 'en' ? true : false);
        }
        private showSameStore() {//显示快速商店
            let curUser = yalla.data.jackaro.JackaroUserService.instance.user;
            if (!curUser) return;
            let playerPool = JackaroGamePlayerManager.instance.getPlayerPool();
            if (!playerPool) return;
            let players = [];
            var base64Encode = laya.utils.Browser.window.Base64.encode;
            for (const key in playerPool) {
                if (playerPool.hasOwnProperty(key)) {
                    const element = playerPool[key];
                    if (!element) continue;
                    if (element.idx == yalla.Global.Account.idx) continue;
                    let curPlayerColor = element.color;
                    // //调换蓝绿颜色，因为和ludo 正好相反
                    if (curPlayerColor == 1) {
                        curPlayerColor = 3;
                    } else if (curPlayerColor == 3) {
                        curPlayerColor = 1;
                    }
                    players.push({
                        idx: element.idx,
                        faceUrl: base64Encode(element.dataSource.faceUrl),
                        nickName: base64Encode(element.dataSource.nickName),
                        color: curPlayerColor
                    })
                }
            }
            //默认玩家颜色 红 蓝 黄 绿 对应数字 0 1 2 3
            //按照红绿黄蓝排序
            players.sort((a, b) => {
                //颜色映射表 红0->0, 绿3->1, 黄2->2, 蓝1->3
                const colorMap = { 0: 0, 3: 3, 2: 2, 1: 1 };
                return colorMap[a.color] - colorMap[b.color];
            });
            yalla.Native.instance.showSameStyleStore(players, curUser.diamond, curUser.gold);
        }


        private onClickSetting(e: Laya.Event): void {
            if (e) {
                yalla.Sound.playSound('click');
                e.stopPropagation();
            }

            if (!this.setting) this.setting = new yalla.common.Setting(this._topUI);
            this.setSystemVisible(!this._topUI['set'].visible);
            if (this._topUI['set'].visible) this.setting.update();
            this.setting.setGameUI(this._gameUI);
            this.pageHander();
            yalla.Native.instance.mobClickEvent(buryPoint.DGAME_SETTING);
            yalla.event.YallaEvent.instance.event('click_gameTopview');
        }

        /**
         * 奖励信息 显示隐藏
         */
        private onClickPrize(e: Laya.Event = null): void {
            if (e) {
                yalla.Sound.playSound('click');
                e.stopPropagation();
            }
            if (this.rank) {
                var visb = this.rank.visible;
                this.pageHander()
                !visb && this._topUI.timer.once(this._handActiveLeftShow, this, this.hideRank);
                this.setRankVisible(!visb);
            }
            this.setSystemVisible(false);

            yalla.Native.instance.mobClickEvent(buryPoint.DGAME_REWARD);
            yalla.event.YallaEvent.instance.event('click_gameTopview');
            // yalla.common.Tip.instance.showTip(" You have been banned from chat until 2022-03-06 06:2:35(GMT+3). Reason: spread pornographic/sexual conteent.Please note that the repeated behaviors against Yalla Ludo Conduct Regulations could lead to your account's permanent block.")
        }
        public setRank() {//设置榜单的信息
            if (!this.rank) {
                this.rank = new jackaro.RoomBattleRecordInfo();
                this._topUI.timer.callLater(this, () => {
                    if (this.rank) {
                        this._topUI['topMc'].addChild(this.rank);
                    }
                })
                if (this.rank) {
                    this._topUI.timer.once(this._leftShow, this, this.hideRank);
                }
            }
        }
        public hideRank() {
            if (this.rank && this._topUI) {
                this.setRankVisible(false);
                this._topUI.timer.clear(this._topUI, this.hideRank);
            }
        }
        public pageHander() {
            this.rank && this.hideRank();
        }

        public get topUI(): Laya.View {
            return this._topUI;
        }

        public onClickGameUI(): void {
            this.setSystemVisible(false);
        }

        /**设置面板能否可见 */
        public setSystemVisible(v) {
            this._topUI['set'].visible = v;
            if (v) {
                yalla.util.ViewExclusion.instance.add(yalla.util.ViewType.TopSetting, () => { this.setSystemVisible(false) });
            } else {
                yalla.util.ViewExclusion.instance.remove(yalla.util.ViewType.TopSetting)
            }
        }

        /**奖杯面板能否可见 */
        public setRankVisible(v) {
            this.rank && (this.rank.visible = v);
            if (v) {
                this.rank && this.rank.updateBattleRecord();
                yalla.util.ViewExclusion.instance.add(yalla.util.ViewType.TopRank, () => { this.hideRank() });
            } else {
                yalla.util.ViewExclusion.instance.remove(yalla.util.ViewType.TopRank)
            }
        }

        private removeEvent(): void {
            this._topUI.offAll();
            this._topUI['settingBtn'].off(Laya.Event.CLICK, this, this.onClickSetting);
            this._topUI['winBtn'].off(Laya.Event.CLICK, this, this.onClickPrize);
            this._topUI['store_btn'].off(Laya.Event.CLICK, this, this.showSameStore);
            this._topUI['titleSpectator'].off(Laya.Event.CLICK, this, this.showSpectator);
            yalla.event.YallaEvent.instance.off("watchNumChange", this, this.onWatchNumChange);
            Laya.timer.clearAll(this);
        }

        public clear(): void {
            this.removeEvent();
            this.setting && this.setting.clear();
            if (this._topUI) {
                this._topUI.removeSelf();
                this._topUI.destroy(true);
            }
            this.setting = null;
            if (this.rank) {
                this.rank.destroy(true);
                this.rank = null;
            }
        }
    }
}