/**
 *  起飞棋子 移动多少步 弃牌
 */
module yalla.view.jackaro {
    export enum CardOperateType {
        TAKEOFF_CHESS = 1,//起飞棋子
        MOVE_CHESS = 2,//移动棋子
        DISCARD_POKER = 3,//弃牌
    }
    export interface CardOperateItemDefine {
        type: CardOperateType;
        operatorKey: string;//对应移动步数，如果是弃牌操作，则为 null
        title: string; //按钮上有一个标题
        titlePosYOffset?: number;//标题的Y轴偏移
        operateDesc: string; //按钮上有一个描述,具体有图片描述或者文字描述
        desc: string; //按钮上有一个描述
        btnSkin: string;
        titleFontSize: number;
        contentFontSize: number;
        callback: Function;
        isConditionRequired: Function;
    }
    export class CardOperateDefine {
        public static CardNameDefine: string[] = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13"];
        public static getCardOperateDefine(poker: yalla.data.jackaro.Poker) {
            let isPokerA = JackaroCardManger.instance.isPokerA(poker);
            let isPoker10 = JackaroCardManger.instance.isPoker10(poker);
            let isPokerK = JackaroCardManger.instance.isPokerK(poker);
            let isPoker2 = JackaroCardManger.instance.isPoker2(poker);
            if (JackaroCardManger.instance.isComplexPlay(poker)) {
                if (isPokerA) return CardComplexGameplayOperateDefine.pokerA;
                if (isPoker10) return CardComplexGameplayOperateDefine.poker10;
                if (isPokerK) return CardComplexGameplayOperateDefine.pokerK;
            } else if (isPoker2 && JackaroCardManger.instance.isComplex1V1Play(poker)) {
                return Card1v1GameplayOperateDefine.poker2;
            }
            else {
                if (isPokerA) return CardOperateDefine.pokerA;
                if (isPoker10) return CardOperateDefine.poker10;
            }
            return null;
        }
        public static getCardIntroRule(poker: yalla.data.jackaro.Poker) {
            let isPokerA = JackaroCardManger.instance.isPokerA(poker);
            let isPoker2 = JackaroCardManger.instance.isPoker2(poker);
            let isPoker3 = JackaroCardManger.instance.isPoker3(poker);
            let isPoker4 = JackaroCardManger.instance.isPoker4(poker);
            let isPoker5 = JackaroCardManger.instance.isPoker5(poker);
            let isPoker6 = JackaroCardManger.instance.isPoker6(poker);
            let isPoker7 = JackaroCardManger.instance.isPoker7(poker);
            let isPoker8 = JackaroCardManger.instance.isPoker8(poker);
            let isPoker9 = JackaroCardManger.instance.isPoker9(poker);
            let isPoker10 = JackaroCardManger.instance.isPoker10(poker);
            let isPoker11 = JackaroCardManger.instance.isPokerJ(poker);
            let isPoker12 = JackaroCardManger.instance.isPokerQ(poker);
            let isPoker13 = JackaroCardManger.instance.isPokerK(poker);
            if (JackaroCardManger.instance.isComplexPlay(poker)) {
                if (isPokerA) {
                    return CardComplexGameplayOperateDefine.PokerRules["A"];
                } else if (isPoker2) {
                    return CardComplexGameplayOperateDefine.PokerRules["2"];
                } else if (isPoker3) {
                    return CardComplexGameplayOperateDefine.PokerRules["3"];
                } else if (isPoker4) {
                    return CardComplexGameplayOperateDefine.PokerRules["4"];
                } else if (isPoker5) {
                    return CardComplexGameplayOperateDefine.PokerRules["5"];
                } else if (isPoker6) {
                    return CardComplexGameplayOperateDefine.PokerRules["6"];
                } else if (isPoker7) {
                    return CardComplexGameplayOperateDefine.PokerRules["7"];
                } else if (isPoker8) {
                    return CardComplexGameplayOperateDefine.PokerRules["8"];
                } else if (isPoker9) {
                    return CardComplexGameplayOperateDefine.PokerRules["9"];
                } else if (isPoker10) {
                    return CardComplexGameplayOperateDefine.PokerRules["10"];
                } else if (isPoker11) {
                    let pokerColor = "";
                    if (poker == yalla.data.jackaro.Poker.CLUB_JACK || poker == yalla.data.jackaro.Poker.SPADE_JACK) {
                        pokerColor = "black";
                    } else {
                        pokerColor = "red";
                    }
                    if (pokerColor != "") {
                        return CardComplexGameplayOperateDefine.PokerRules["11_" + pokerColor];
                    } else {
                        return CardComplexGameplayOperateDefine.PokerRules["11"];
                    }
                } else if (isPoker12) {
                    let pokerColor = "";
                    if (poker == yalla.data.jackaro.Poker.CLUB_QUEEN || poker == yalla.data.jackaro.Poker.SPADE_QUEEN) {
                        pokerColor = "black";
                    } else {
                        pokerColor = "red";
                    }
                    if (pokerColor != "") {
                        return CardComplexGameplayOperateDefine.PokerRules["12_" + pokerColor];
                    } else {
                        return CardComplexGameplayOperateDefine.PokerRules["12"];
                    }
                } else if (isPoker13) {
                    return CardComplexGameplayOperateDefine.PokerRules["13"];
                }
            } else if (JackaroCardManger.instance.isComplex1V1Play(poker)) {
                return Card1v1GameplayOperateDefine.PokerRules["2_1v1"];
            } else {
                if (isPokerA) {
                    return CardOperateDefine.PokerRules["A"];
                } else if (isPoker2) {
                    return CardOperateDefine.PokerRules["2"];
                } else if (isPoker3) {
                    return CardOperateDefine.PokerRules["3"];
                } else if (isPoker4) {
                    return CardOperateDefine.PokerRules["4"];
                } else if (isPoker5) {
                    return CardOperateDefine.PokerRules["5"];
                } else if (isPoker6) {
                    return CardOperateDefine.PokerRules["6"];
                } else if (isPoker7) {
                    return CardOperateDefine.PokerRules["7"];
                } else if (isPoker8) {
                    return CardOperateDefine.PokerRules["8"];
                } else if (isPoker9) {
                    return CardOperateDefine.PokerRules["9"];
                } else if (isPoker10) {
                    return CardOperateDefine.PokerRules["10"];
                } else if (isPoker11) {
                    return CardOperateDefine.PokerRules["11"];
                } else if (isPoker12) {
                    return CardOperateDefine.PokerRules["12"];
                } else if (isPoker13) {
                    return CardOperateDefine.PokerRules["13"];
                }
            }
        }
        public static pokerA: CardOperateItemDefine[] = [{
            type: CardOperateType.TAKEOFF_CHESS,
            operatorKey: "0",
            title: "Place",
            operateDesc: "jackaro/img_takeOff.png",
            desc: "New Stone",
            titleFontSize: 32,
            contentFontSize: 26,//30,
            btnSkin: "jackaro/btn_operate.png",
            callback: () => {
                // console.log("起飞棋子");
            },
            isConditionRequired: () => {
                return true;
            }
        }, {
            type: CardOperateType.MOVE_CHESS,
            operatorKey: "1",
            title: "Move",
            operateDesc: "1",
            desc: "Step",
            titleFontSize: 32,
            contentFontSize: 26,//30,
            btnSkin: "jackaro/btn_operate.png",
            callback: () => {
                // console.log("移动棋子1步");
            },
            isConditionRequired: () => {
                return true;
            }
        }, {
            type: CardOperateType.MOVE_CHESS,
            operatorKey: "11",
            title: "Move",
            operateDesc: "1 1",
            desc: "Steps",
            titleFontSize: 32,
            contentFontSize: 26,//30,
            btnSkin: "jackaro/btn_operate.png",
            callback: () => {
                // console.log("移动棋子11步");
            },
            isConditionRequired: () => {
                return true;
            }
        }]
        public static poker10: CardOperateItemDefine[] = [{
            type: CardOperateType.DISCARD_POKER,
            title: "Discard",
            operateDesc: "jackaro/img_discard.png",
            desc: "Next Player",
            operatorKey: null,
            titleFontSize: 32,
            contentFontSize: 26,//30,
            btnSkin: "jackaro/btn_operate.png",
            callback: () => {
                // console.log("弃牌");
                yalla.data.jackaro.JackaroUserService.instance.sendAssignNextPlayerDiscard(JackaroCardManger.instance.getCurPlayPoker());
            },
            isConditionRequired: () => {
                return JackaroCardManger.instance.isCanNextPlayerDiscard();
            }
        }, {
            type: CardOperateType.MOVE_CHESS,
            operatorKey: "10",
            title: "Move",
            operateDesc: "1 0",
            desc: "Steps",
            titleFontSize: 32,
            contentFontSize: 26,//30,
            btnSkin: "jackaro/btn_operate.png",
            callback: () => {
                // console.log("移动棋子10步");
            },
            isConditionRequired: () => {
                return true;
            }
        }]
        public static PokerRules = {
            "A": "A.png",
            "2": "2.png",
            "3": "3.png",
            "4": "4.png",
            "5": "5.png",
            "6": "6.png",
            "7": "7.png",
            "8": "8.png",
            "9": "9.png",
            "10": "10.png",
            "11": "J.png",
            "12": "Q.png",
            "13": "K.png",

        }
    }
    /**
     * 牌复杂玩法定义
     */
    export class CardComplexGameplayOperateDefine {
        public static pokerA: CardOperateItemDefine[] = [{
            type: CardOperateType.TAKEOFF_CHESS,
            operatorKey: "0",
            title: "Place",
            operateDesc: "jackaro/img_takeOff.png",
            desc: "New Stone",
            titleFontSize: 32,
            contentFontSize: 26,//30,
            btnSkin: "jackaro/btn_operate.png",
            callback: () => {
                // console.log("起飞棋子");
            },
            isConditionRequired: () => {
                return true;
            }
        }, {
            type: CardOperateType.MOVE_CHESS,
            operatorKey: "1",
            title: "Move",
            operateDesc: "1",
            desc: "Step",
            titleFontSize: 32,
            contentFontSize: 26,//30,
            btnSkin: "jackaro/btn_operate.png",
            callback: () => {
                // console.log("移动棋子1步");
            },
            isConditionRequired: () => {
                return true;
            }
        }]

        public static pokerK: CardOperateItemDefine[] = [{
            type: CardOperateType.TAKEOFF_CHESS,
            operatorKey: "0",
            title: "Place",
            operateDesc: "jackaro/img_takeOff.png",
            desc: "New Stone",
            titleFontSize: 32,
            contentFontSize: 26,//30,
            btnSkin: "jackaro/btn_operate.png",
            callback: () => {
                // console.log("起飞棋子");
            },
            isConditionRequired: () => {
                return JackaroCardManger.instance.isCanTakeOff();
            }
        }, {
            type: CardOperateType.MOVE_CHESS,
            operatorKey: "13",
            title: "Move & Kill",
            titlePosYOffset: 4,
            operateDesc: "jackaro/img_dash.png",
            desc: "Kill All",
            titleFontSize: 24,
            contentFontSize: 26,//30,
            btnSkin: "jackaro/btn_operate.png",
            callback: () => {
                // console.log("移动棋子13步");
            },
            isConditionRequired: () => {
                return true;
            }
        }]
        public static poker10: CardOperateItemDefine[] = [{
            type: CardOperateType.DISCARD_POKER,
            operatorKey: null,
            title: "Discard",
            operateDesc: "jackaro/img_discard.png",
            desc: "Next Player",
            titleFontSize: 32,
            contentFontSize: 26,//30,
            btnSkin: "jackaro/btn_operate.png",
            callback: () => {
                // console.log("弃牌");
                yalla.data.jackaro.JackaroUserService.instance.sendAssignNextPlayerDiscard(JackaroCardManger.instance.getCurPlayPoker());
            },
            isConditionRequired: () => {
                return JackaroCardManger.instance.isCanNextPlayerDiscard();
            }
        },
        {
            type: CardOperateType.MOVE_CHESS,
            operatorKey: "10",
            title: "Move",
            operateDesc: "1 0",
            desc: "Steps",
            titleFontSize: 32,
            contentFontSize: 26,//30,
            btnSkin: "jackaro/btn_operate.png",
            callback: () => {
                // console.log("移动棋子10步");
            },
            isConditionRequired: () => {
                return true;
            }
        }]
        public static PokerRules = {
            "A": "A_c.png",
            "2": "2.png",
            "3": "3.png",
            "4": "4.png",
            "5": "5.png",
            "6": "6.png",
            "7": "7.png",
            "8": "8.png",
            "9": "9.png",
            "10": "10.png",
            "11_black": "J.png",
            "11_red": "J_c.png",
            "12_black": "Q.png",
            "12_red": "Q_c.png",
            "13": "K_c.png",
        }
    }

    // 在现有文件中新增1v1玩法类
    export class Card1v1GameplayOperateDefine {
        // 继承复杂玩法的大部分定义
        public static pokerA = CardComplexGameplayOperateDefine.pokerA;
        public static pokerK = CardComplexGameplayOperateDefine.pokerK;
        public static poker10 = CardComplexGameplayOperateDefine.poker10;

        // 只重新定义poker2的特殊规则
        public static poker2: CardOperateItemDefine[] = [{
            type: CardOperateType.TAKEOFF_CHESS,
            operatorKey: "0",
            title: "Place",
            operateDesc: "jackaro/img_takeOff.png",
            desc: "New Stone",
            titleFontSize: 32,
            contentFontSize: 26,//30,
            btnSkin: "jackaro/btn_operate.png",
            callback: () => {
                // console.log("起飞棋子");
            },
            isConditionRequired: () => {
                return true;
            }
        }, {
            type: CardOperateType.MOVE_CHESS,
            operatorKey: "2",
            title: "Move",
            operateDesc: "2",
            desc: "Step",
            titleFontSize: 32,
            contentFontSize: 26,//30,
            btnSkin: "jackaro/btn_operate.png",
            callback: () => {
                // console.log("移动棋子1步");
            },
            isConditionRequired: () => {
                return true;
            }
        }]

        public static PokerRules = {
            "A": "A_c.png",
            "2": "2.png",
            "2_1v1": "1v1_2.png",
            "3": "3.png",
            "4": "4.png",
            "5": "5.png",
            "6": "6.png",
            "7": "7.png",
            "8": "8.png",
            "9": "9.png",
            "10": "10.png",
            "11_black": "J.png",
            "11_red": "J_c.png",
            "12_black": "Q.png",
            "12_red": "Q_c.png",
            "13": "K_c.png",
        }
    }

}