/**
 * {{ YUM: [New] - Jackaroo 卡牌效果提示管理器 }}
 * 负责检测和管理卡牌的击杀、终点等效果提示
 */
class JackaroCardEffectHintManager {
    private static _instance: JackaroCardEffectHintManager;

    public static get instance(): JackaroCardEffectHintManager {
        if (!this._instance) {
            this._instance = new JackaroCardEffectHintManager();
        }
        return this._instance;
    }

    private logTag: string = "JackaroCardEffectHintManager: ";

    /**
     * 检测并显示卡牌效果提示
     * 在玩家回合时检测每张可出的牌是否能击杀对手或到达终点，并在卡牌上显示相应提示
     */
    public checkAndShowCardEffectHints(allowPokerData: Record<string, yalla.data.jackaro.PokerMoveDataInterface>): void {
        // 只在自己回合检测，不显示片面说明和击杀展示，不显示在线模式

        if (!JackaroGamePlayerManager.instance.isMyActive() ||
            JackaroCardManger.instance.isSelfNeedShowSelfForceCard() ||
            !yalla.Global.onlineMode) {
            this.clearAllCardEffects();
            return;
        }

        this.yum("checkAndShowCardEffectHints", "开始检测手牌效果提示", JSON.stringify(allowPokerData));
        // 清除之前的效果提示 1.4.5
        this.clearAllCardEffects();
        // 遍历所有可出的牌
        for (const pokerStr in allowPokerData) {
            const poker = parseInt(pokerStr) as yalla.data.jackaro.Poker;
            const moveData = allowPokerData[pokerStr];

            // 检测该牌的效果
            const effectInfo = this.detectCardEffect(poker, moveData);

            // 如果有效果，显示在对应的卡牌上
            if (effectInfo !== yalla.view.jackaro.CardEffectType.NONE) {
                const cardItem = this.getCardItemByPoker(yalla.Global.Account.idx, poker);
                if (cardItem) {
                    // {{ YUM: [修改] - 直接使用枚举类型 }}
                    cardItem.setEffectInfo(effectInfo);
                    this.yum("checkAndShowCardEffectHints", `牌 ${yalla.data.jackaro.PokerChinesDefine[poker]} 显示效果提示: ${effectInfo}`);
                }
            }
        }
    }

    /**
     * 清除所有卡牌的效果提示
     */
    public clearAllCardEffects(): void {
        const playerCards = JackaroCardManger.instance.getPlayerAllPokerItem(yalla.Global.Account.idx);
        if (playerCards) {
            playerCards.forEach(cardItem => {
                if (cardItem.getEffectInfo() !== yalla.view.jackaro.CardEffectType.NONE) {
                    cardItem.setEffectInfo(yalla.view.jackaro.CardEffectType.NONE);
                }
            });
        }
    }

    /**
     * 检测单张牌的效果
     * 分析牌的移动数据，检测是否有击杀或终点效果
     */
    private detectCardEffect(poker: yalla.data.jackaro.Poker, moveData: yalla.data.jackaro.PokerMoveDataInterface): yalla.view.jackaro.CardEffectType {
        // {{ YUM: [修改] - 简化代码，使用枚举类型 }}

        // 检测普通牌的效果
        if (moveData.commonResult && moveData.commonResult.playResultData) {
            const effectType = this.getEffectFromResult(
                this.checkCommonResultForKill(poker, moveData.commonResult.playResultData),
                this.checkCommonResultForFinish(moveData.commonResult.playResultData),
                "普通牌"
            );
            if (effectType !== yalla.view.jackaro.CardEffectType.NONE) {
                return effectType;
            }
        }

        // 检测复杂K牌的效果
        if (moveData.complexKing && (moveData.complexKing.impactResult || moveData.complexKing.flyData)) {
            const effectType = this.getEffectFromResult(
                this.checkComplexKingForKill(moveData.complexKing),
                this.checkComplexKingForFinish(moveData.complexKing.impactResult),
                "复杂K牌"
            );
            if (effectType !== yalla.view.jackaro.CardEffectType.NONE) {
                return effectType;
            }
        }

        // J牌交换暂不检测特殊效果
        return yalla.view.jackaro.CardEffectType.NONE;
    }

    /**
     * 根据击杀和终点效果获取最终的效果类型
     * {{ YUM: [新增] - 提取公共逻辑，避免代码重复 }}
     */
    private getEffectFromResult(hasKill: boolean, hasFinish: boolean, cardType: string): yalla.view.jackaro.CardEffectType {
        // 当既能击杀又能进终点时，优先显示进终点标识
        if (hasKill && hasFinish) {
            this.yum("getEffectFromResult", `${cardType}检测到既能击杀又能进终点，优先显示进终点`, {
                hasKill: hasKill,
                hasFinish: hasFinish
            });
            return yalla.view.jackaro.CardEffectType.END; // 优先显示进终点
        } else if (hasFinish) {
            return yalla.view.jackaro.CardEffectType.END;
        } else if (hasKill) {
            return yalla.view.jackaro.CardEffectType.KILL;
        }

        return yalla.view.jackaro.CardEffectType.NONE;
    }

    /**
     * {{ YUM: [新增] - 获取当前动态最终格位置 }}
     * 根据终点区域棋子分布动态确定最终格：
     * - 如果-4位置有棋子，则-3是最终格
     * - 如果-4和-3都有棋子，则-2是最终格
     * - 以此类推...
     */
    private getCurrentFinalGridIndex(playerIdx: number): number {
        const playerColor = JackaroGamePlayerManager.instance.getColorByIdx(playerIdx);

        // 从-4开始检查，找到第一个没有棋子的位置
        for (let gridIndex = -4; gridIndex <= -1; gridIndex++) {
            const station = { area: playerColor, gridPosition: gridIndex };
            const grid: LudoGrid = JackaroBoardData.getGridByStation(station);

            if (grid) {
                const chess = JackaroChessManager.instance.getChessByGrid(grid);
                if (!chess) {
                    // 找到第一个空位置，这就是当前的最终格
                    this.yum("getCurrentFinalGridIndex", `玩家${playerIdx}当前最终格位置：${gridIndex}`, {
                        playerColor: playerColor,
                        finalGridIndex: gridIndex
                    });
                    return gridIndex;
                }
            }
        }

        // 如果所有位置都有棋子，返回-1（理论上不应该发生，因为游戏应该已经结束）
        this.yum("getCurrentFinalGridIndex", `玩家${playerIdx}终点区域已满，返回-1`, { playerColor: playerColor });
        return -1;
    }

    /**
     * 检测普通牌结果中的击杀效果
     * {{ YUM: [重构] - 简化逻辑结构，将A牌判断提取到独立方法 }}
     */
    private checkCommonResultForKill(poker: yalla.data.jackaro.Poker, playResultData: Record<string, yalla.data.jackaro.PlayResultDataInterface>): boolean {
        const myIdx = yalla.Global.Account.idx;
        const isPokerA = JackaroCardManger.instance.isPokerA(poker);

        // {{ YUM: [简化] - A牌使用专门方法处理 }}
        if (isPokerA) {
            return this.checkPokerAKillEffect(playResultData, myIdx);
        }

        // {{ YUM: [简化] - 非A牌直接遍历检查 }}
        for (const stepStr in playResultData) {
            const resultData = playResultData[stepStr];
            if (resultData.result) {
                for (const moveResult of resultData.result) {
                    if (this.checkKillAction(moveResult, myIdx)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * {{ YUM: [新增] - 检测A牌的击杀效果，根据步数组合应用不同规则 }}
     */
    private checkPokerAKillEffect(playResultData: Record<string, yalla.data.jackaro.PlayResultDataInterface>, myIdx: number): boolean {
        this.yum("checkPokerAKillEffect", `检查A牌击杀效果，数据：${JSON.stringify(playResultData)}`);

        // 收集所有棋子的移动数据
        const chessDataMap = this.collectChessDataForPokerA(playResultData);

        // 检查每个棋子的击杀效果
        for (const chessId in chessDataMap) {
            if (this.checkSingleChessKillForPokerA(chessDataMap[chessId], chessId, myIdx)) {
                return true;
            }
        }
        return false;
    }

    /**
     * {{ YUM: [新增] - 收集A牌的棋子移动数据 }}
     */
    private collectChessDataForPokerA(playResultData: Record<string, yalla.data.jackaro.PlayResultDataInterface>): Record<string, any> {
        const chessMap: Record<string, { steps: Record<number, any>, availableSteps: number[] }> = {};

        for (const stepStr in playResultData) {
            const step = parseInt(stepStr);
            const resultData = playResultData[stepStr];

            if (resultData.result) {
                for (const moveResult of resultData.result) {
                    if (moveResult.fromGrid?.chess && moveResult.fromGrid?.grid) {
                        let color = moveResult.fromGrid.grid.color || 0;
                        const chessId = `${moveResult.fromGrid.chess.idx}_${moveResult.fromGrid.grid.index}_${color}`;

                        if (!chessMap[chessId]) {
                            chessMap[chessId] = { steps: {}, availableSteps: [] };
                        }

                        chessMap[chessId].steps[step] = moveResult;
                        if (chessMap[chessId].availableSteps.indexOf(step) === -1) {
                            chessMap[chessId].availableSteps.push(step);
                        }
                    }
                }
            }
        }
        return chessMap;
    }

    /**
     * {{ YUM: [新增] - 检查单个棋子的A牌击杀效果 }}
     */
    private checkSingleChessKillForPokerA(chessData: any, chessId: string, myIdx: number): boolean {
        const availableSteps = chessData.availableSteps.sort((a, b) => a - b);
        const stepKey = availableSteps.join(',');

        // 检查各步数的击杀情况
        const killResults = {
            step0: chessData.steps[0] ? this.checkKillAction(chessData.steps[0], myIdx) : false,
            step1: chessData.steps[1] ? this.checkKillAction(chessData.steps[1], myIdx) : false,
            step11: chessData.steps[11] ? this.checkKillAction(chessData.steps[11], myIdx) : false
        };

        // {{ YUM: [新增] - 详细调试日志，追踪A牌击杀提示偶现不显示问题 }}
        this.yum("checkSingleChessKillForPokerA", `A牌棋子${chessId}步数[${stepKey}]击杀检测详情`, {
            killResults: killResults,
            availableSteps: availableSteps,
            step0Data: chessData.steps[0] ? {
                hasKillFlag: chessData.steps[0].kill,
                toGridChess: chessData.steps[0].toGrid?.chess,
                fromGridChess: chessData.steps[0].fromGrid?.chess
            } : null,
            step1Data: chessData.steps[1] ? {
                hasKillFlag: chessData.steps[1].kill,
                toGridChess: chessData.steps[1].toGrid?.chess,
                fromGridChess: chessData.steps[1].fromGrid?.chess
            } : null,
            step11Data: chessData.steps[11] ? {
                hasKillFlag: chessData.steps[11].kill,
                toGridChess: chessData.steps[11].toGrid?.chess,
                fromGridChess: chessData.steps[11].fromGrid?.chess
            } : null
        });

        // {{ YUM: [简化] - 使用简化的规则映射 }}
        const shouldShow = this.getPokerAKillDisplayRule(stepKey, killResults);

        this.yum("checkSingleChessKillForPokerA", `A牌棋子${chessId}步数[${stepKey}]显示判断结果: ${shouldShow}`, {
            shouldShow: shouldShow,
            killResults: killResults
        });

        if (shouldShow) {
            this.yum("checkSingleChessKillForPokerA", `A牌棋子${chessId}步数[${stepKey}]满足击杀显示`, killResults);
            return true;
        }
        return false;
    }

    /**
     * {{ YUM: [修改] - A牌击杀显示规则映射 1.4.6版本}}
     * （0）（1）（11）：任意击杀对手 显示
     * （0,1）（0,11）：任意击杀对手 显示
     * （1,11）：任意击杀对手 显示
     * （0,1,11）：任意击杀对手 显示
     */
    private getPokerAKillDisplayRule(stepKey: string, killResults: { step0: boolean, step1: boolean, step11: boolean }): boolean {
        const rules: Record<string, () => boolean> = {
            // 单步数：任意击杀显示
            '0': () => killResults.step0,
            '1': () => killResults.step1,
            '11': () => killResults.step11,

            // 包含0步的组合：任意击杀显示
            '0,1': () => killResults.step0 || killResults.step1,
            '0,11': () => killResults.step0 || killResults.step11,

            // 1和11组合：任意击杀显示
            '1,11': () => killResults.step1 || killResults.step11,

            // 全步数组合：任意击杀显示
            '0,1,11': () => killResults.step0 || killResults.step1 || killResults.step11
        };

        // 使用规则表，未知组合使用默认策略
        return rules[stepKey] ? rules[stepKey]() : (killResults.step0 || killResults.step1 || killResults.step11);
    }

    /**
     * 检测普通牌结果中的终点效果
     * {{ YUM: [修改] - 动态最终格判定：根据终点区域棋子分布确定当前最终格位置 }}
     */
    private checkCommonResultForFinish(playResultData: Record<string, yalla.data.jackaro.PlayResultDataInterface>): boolean {
        for (const stepStr in playResultData) {
            const resultData = playResultData[stepStr];
            if (resultData.result) {
                for (const moveResult of resultData.result) {
                    // 检测是否到达终点：通过服务器网格数据获取实际格子信息
                    if (moveResult.toGrid && moveResult.toGrid.grid && moveResult.fromGrid && moveResult.fromGrid.grid) {
                        const toGrid = moveResult.toGrid.grid;
                        const fromGrid = moveResult.fromGrid.grid;
                        const toIndex = toGrid.index || 0;
                        const fromIndex = fromGrid.index || 0;

                        // 获取移动棋子的玩家索引
                        const movingChess = moveResult.fromGrid.chess;
                        const movingPlayerIdx = movingChess ? movingChess.idx : yalla.Global.Account.idx;

                        // {{ YUM: [新逻辑] - 动态最终格判定 }}
                        const currentFinalGridIndex = this.getCurrentFinalGridIndex(movingPlayerIdx);

                        // 条件1：移动到当前的动态最终格
                        if (toIndex === currentFinalGridIndex) {
                            this.yum("checkCommonResultForFinish", `移动到动态最终格 from:${fromIndex} to:${toIndex} (当前最终格:${currentFinalGridIndex})`);
                            return true;
                        }

                        // 条件2：首次进入终点区域（从非终点区域进入-1到-4）
                        if (toIndex >= -4 && toIndex <= -1 && fromIndex > -1) {
                            this.yum("checkCommonResultForFinish", `首次进入终点区域 from:${fromIndex} to:${toIndex} (当前最终格:${currentFinalGridIndex})`);
                            return true;
                        }

                        // {{ YUM: [排除] - 在终点区域内的移动，但不是移动到最终格的情况不显示Home }}
                    }
                }
            }
        }
        return false;
    }

    /**
     * 检测复杂K牌结果中的击杀效果
     * {{ YUM: [Fix] - 只有击杀敌方棋子时才显示击杀标识，不包括队友棋子，flyData也支持击杀检测 }}
     */
    private checkComplexKingForKill(complexKing: any): boolean {
        const myIdx = yalla.Global.Account.idx;
        // impactResult 检查
        if (complexKing && Array.isArray(complexKing.impactResult)) {
            this.yum("checkComplexKingForKill", `检查复杂K牌impactResult击杀效果:${JSON.stringify(complexKing.impactResult)}`);
            for (const impact of complexKing.impactResult) {
                const hasKillAction = this.checkKillActionForComplexK(impact, myIdx);
                if (hasKillAction) {
                    return true;
                }
            }
        }
        // flyData 检查（单个对象）
        if (complexKing && complexKing.flyData) {
            this.yum("checkComplexKingForKill", `检查复杂K牌flyData击杀效果:${JSON.stringify(complexKing.flyData)}`);
            if (this.checkKillActionForFlyData(complexKing.flyData, myIdx)) {
                return true;
            }
        }
        return false;
    }

    /**
     * {{ YUM: [Optimized] - 检查普通牌移动结果是否包含击杀敌方棋子的动作 }}
     */
    private checkKillAction(moveResult: any, myIdx: number): boolean {
        // 优先检查服务器的 kill 标志，如果没有则通过位置推断
        const hasKillFlag = moveResult.kill;
        const hasTargetChess = moveResult.toGrid && moveResult.toGrid.chess &&
            moveResult.fromGrid && moveResult.fromGrid.chess;

        // {{ YUM: [新增] - 检查impactedChess数组中的击杀情况，解决A牌同时击杀自己和对手的偶现问题 }}
        const hasImpactedChess = moveResult.impactedChess && Array.isArray(moveResult.impactedChess) && moveResult.impactedChess.length > 0;

        // 如果既没有 kill 标志也没有目标棋子，也没有impactedChess，则不是击杀
        if (!hasKillFlag && !hasTargetChess && !hasImpactedChess) {
            return false;
        }

        // {{ YUM: [新增] - 优先检查impactedChess数组，这里包含了所有被影响的棋子 }}
        if (hasImpactedChess) {
            let hasValidKill = false;
            for (const impactedChess of moveResult.impactedChess) {
                if (impactedChess.chess && impactedChess.chess.idx !== undefined) {
                    const killedChessIdx = impactedChess.chess.idx;
                    const isTeammate = JackaroGamePlayerManager.instance.isSameTeam(killedChessIdx);
                    const isValidKill = killedChessIdx !== myIdx && !isTeammate;

                    if (isValidKill) {
                        hasValidKill = true;
                        this.yum("checkKillAction", "impactedChess中发现有效击杀", {
                            killedChessIdx: killedChessIdx,
                            myIdx: myIdx,
                            isTeammate: isTeammate,
                            isValidKill: isValidKill
                        });
                        break; // 找到一个有效击杀就足够了
                    }
                }
            }

            if (hasValidKill) {
                return true;
            }
        }

        // 如果有目标棋子，验证击杀的有效性
        if (hasTargetChess) {
            const killedChessIdx = moveResult.toGrid.chess.idx;
            const isTeammate = JackaroGamePlayerManager.instance.isSameTeam(killedChessIdx);

            // 击杀有效性检查：
            // 1. 被击杀的不是自己的棋子  
            // 2. 被击杀的不是队友的棋子
            const isValidKill = killedChessIdx !== myIdx && !isTeammate;

            this.yum("checkKillAction", "toGrid击杀检测", {
                hasKillFlag: hasKillFlag,
                killedChessIdx: killedChessIdx,
                myIdx: myIdx,
                isTeammate: isTeammate,
                isValidKill: isValidKill
            });

            return isValidKill;
        }

        // 如果只有 kill 标志但没有具体棋子信息，信任服务器判断
        if (hasKillFlag) {
            this.yum("checkKillAction", "仅有kill标志，信任服务器判断");
            return true;
        }

        return false;
    }

    /**
     * {{ YUM: [修改] - 检查复杂K牌移动结果是否包含击杀敌方棋子的动作，使用impactedChess数据 }}
     */
    private checkKillActionForComplexK(impact: yalla.data.jackaro.ImpactChessResultInterface, myIdx: number): boolean {
        // 优先检查服务器的 kill 标志
        const hasKillFlag = impact.kill;
        // {{ YUM: [修改] - 检查impactedChess数组中的被击杀棋子数据 }}
        const hasImpactedChess = impact.impactedChess && impact.impactedChess.length > 0;

        this.yum("checkKillActionForComplexK", "复杂K牌击杀检测1", {
            hasKillFlag: hasKillFlag,
            hasImpactedChess: hasImpactedChess,
            impactedChessCount: impact.impactedChess ? impact.impactedChess.length : 0,
            impact: impact
        });

        // 如果既没有 kill 标志也没有被撞击的棋子，则不是击杀
        if (!hasKillFlag && !hasImpactedChess) {
            return false;
        }

        // {{ YUM: [修改] - 检查impactedChess中的被击杀棋子，只有击杀敌方棋子才显示kill效果 }}
        if (hasImpactedChess) {
            let hasValidKill = false;
            for (const impactedChess of impact.impactedChess) {
                if (impactedChess.from && impactedChess.from.chess) {
                    const killedChessIdx = impactedChess.from.chess.idx;
                    const isTeammate = JackaroGamePlayerManager.instance.isSameTeam(killedChessIdx);

                    // 击杀有效性检查：
                    // 1. 被击杀的不是自己的棋子  
                    // 2. 被击杀的不是队友的棋子
                    const isValidKill = killedChessIdx !== myIdx && !isTeammate;

                    this.yum("checkKillActionForComplexK", "复杂K牌击杀检测2", {
                        hasKillFlag: hasKillFlag,
                        killedChessIdx: killedChessIdx,
                        myIdx: myIdx,
                        isTeammate: isTeammate,
                        isValidKill: isValidKill
                    });

                    // 记录是否有有效击杀
                    if (isValidKill) {
                        hasValidKill = true;
                    }
                }
            }
            // {{ YUM: [修改] - 只有存在有效击杀（敌方棋子）时才返回true }}
            return hasValidKill;
        }

        // {{ YUM: [修改] - 如果只有kill标志但没有impactedChess数据，不能确定击杀对象，返回false避免误判 }}
        if (hasKillFlag) {
            this.yum("checkKillActionForComplexK", "仅有kill标志但无impactedChess数据，无法验证击杀对象，返回false");
            return false;
        }

        return false;
    }

    /**
     * {{ YUM: [新增] - 检查flyData的击杀效果，flyData不包含impactedChess数据 }}
     */
    private checkKillActionForFlyData(flyData: any, myIdx: number): boolean {
        // 优先检查服务器的 kill 标志
        const hasKillFlag = flyData.kill;
        // flyData使用toGrid.chess来检查被击杀的棋子
        const hasTargetChess = flyData.toGrid && flyData.toGrid.chess &&
            flyData.fromGrid && flyData.fromGrid.chess;

        this.yum("checkKillActionForFlyData", "flyData击杀检测", {
            hasKillFlag: hasKillFlag,
            hasTargetChess: hasTargetChess,
            flyData: flyData
        });

        // 如果既没有 kill 标志也没有目标棋子，则不是击杀
        if (!hasKillFlag && !hasTargetChess) {
            return false;
        }

        // {{ YUM: [修改] - 如果有目标棋子，验证击杀的有效性，只有击杀敌方棋子才显示kill效果 }}
        if (hasTargetChess) {
            const killedChessIdx = flyData.toGrid.chess.idx;
            const isTeammate = JackaroGamePlayerManager.instance.isSameTeam(killedChessIdx);

            // 击杀有效性检查：
            // 1. 被击杀的不是自己的棋子  
            // 2. 被击杀的不是队友的棋子
            const isValidKill = killedChessIdx !== myIdx && !isTeammate;

            this.yum("checkKillActionForFlyData", "flyData击杀有效性检查", {
                hasKillFlag: hasKillFlag,
                killedChessIdx: killedChessIdx,
                myIdx: myIdx,
                isTeammate: isTeammate,
                isValidKill: isValidKill
            });

            // {{ YUM: [修改] - 只有击杀敌方棋子时才返回true，击杀队友不显示kill效果 }}               
            return isValidKill;
        }

        // {{ YUM: [修改] - 如果只有kill标志但没有目标棋子数据，不能确定击杀对象，返回false避免误判 }}
        if (hasKillFlag) {
            this.yum("checkKillActionForFlyData", "flyData仅有kill标志但无目标棋子数据，无法验证击杀对象，返回false");
            return false;
        }

        return false;
    }

    /**
     * 检测复杂K牌结果中的终点效果
     * {{ YUM: [修改] - 动态最终格判定：根据终点区域棋子分布确定当前最终格位置 }}
     */
    private checkComplexKingForFinish(impactResult: Array<yalla.data.jackaro.ImpactChessResultInterface>): boolean {
        for (const impact of impactResult) {
            // 检测是否到达终点
            if (impact.toGrid && impact.toGrid.grid && impact.fromGrid && impact.fromGrid.grid) {
                const toGrid = impact.toGrid.grid;
                const fromGrid = impact.fromGrid.grid;
                const toIndex = toGrid.index || 0;
                const fromIndex = fromGrid.index || 0;

                // 获取移动棋子的玩家索引
                const movingChess = impact.fromGrid.chess;
                const movingPlayerIdx = movingChess ? movingChess.idx : yalla.Global.Account.idx;

                // {{ YUM: [新逻辑] - 动态最终格判定 }}
                const currentFinalGridIndex = this.getCurrentFinalGridIndex(movingPlayerIdx);

                // 条件1：移动到当前的动态最终格
                if (toIndex === currentFinalGridIndex) {
                    this.yum("checkComplexKingForFinish", `复杂K牌移动到动态最终格 from:${fromIndex} to:${toIndex} (当前最终格:${currentFinalGridIndex})`);
                    return true;
                }

                // 条件2：首次进入终点区域（从非终点区域进入-1到-4）
                if (toIndex >= -4 && toIndex <= -1 && fromIndex > -1) {
                    this.yum("checkComplexKingForFinish", `复杂K牌首次进入终点区域 from:${fromIndex} to:${toIndex} (当前最终格:${currentFinalGridIndex})`);
                    return true;
                }

                // {{ YUM: [排除] - 在终点区域内的移动，但不是移动到最终格的情况不显示Home }}
            }
        }
        return false;
    }

    /**
     * 获取指定玩家的指定扑克牌卡牌项
     */
    private getCardItemByPoker(idx: number, poker: yalla.data.jackaro.Poker): yalla.view.jackaro.JackaroCardItem {
        return JackaroCardManger.instance.getPlayerHandPoker(idx, poker);
    }

    /**
     * 日志输出
     */
    private yum(context: string, message: string, ...params: any[]): void {
        // yalla.Debug.yum(this.logTag + context, message, ...params);
    }
}
