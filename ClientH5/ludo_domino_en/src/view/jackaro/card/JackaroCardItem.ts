module yalla.view.jackaro {
    /**
     * 杰克罗牌组件
     * 负责单张卡牌的显示、交互和动画效果
     * 包含卡牌的点击、拖拽、移动等交互操作，以及相关动画处理
     */

    /**
     * 杰克罗牌的显示状态枚举
     * 定义卡牌在游戏中的不同位置和显示状态
     * IN_HAND: 在手牌中，玩家可以操作
     * ON_TABLE: 在桌面上，通常是牌堆或已打出的牌
     * IN_PLAY: 在出牌区，正在进行出牌操作的牌
     * INVALID: 无效状态，通常是不能使用的牌（被禁用或规则不允许出）
     */
    export enum JackaroCardItemState {
        IN_HAND = 1,
        ON_TABLE = 2,
        IN_PLAY = 3,
        INVALID = 4
    }

    /**
     * 牌的操作状态枚举
     * 定义卡牌当前是否可以被操作的状态
     * DEFAULT: 默认状态，初始化时的状态
     * CAN_PLAY: 可以出牌，符合当前游戏规则可以打出
     * CANNOT_PLAY: 不能出牌，不符合当前游戏规则无法打出
     */
    export enum _operatorState {
        DEFAULT = 0,
        CAN_PLAY = 1,
        CANNOT_PLAY = -1
    }

    /**
     * 卡牌效果类型枚举
     * 定义卡牌的特殊效果提示类型
     * NONE: 无效果
     * KILL: 击杀效果
     * END: 终点效果
     */
    export enum CardEffectType {
        NONE = 0,
        KILL = 1,
        END = 2
    }
    export enum PokerSize {
        selfWidth = 140,
        selfHeight = 200,
        otherWidth = 44,
        otherHeight = 62
    }

    export class JackaroCardItem extends ui.jackaro.item.cardItemUI {
        // 静态日志控制方法
        /**
         * 设置日志输出的级别
         * @param enableDebug 是否启用调试日志
         * @param enableVerbose 是否启用详细日志
         */
        public static setLogLevel(enableDebug: boolean, enableVerbose: boolean = false): void {
            JackaroCardLogger.instance.setLogEnabled('cardItem', enableDebug, enableVerbose);
        }

        // ====== 卡牌基础数据与状态 ======
        private _data: JackaroCardDataInterface;         // 卡牌的核心数据，包含牌值、花色等基本信息
        private _operatorState: _operatorState = _operatorState.DEFAULT;  // 牌的操作状态，决定牌是否可以被打出
        private _showState: JackaroCardItemState = JackaroCardItemState.IN_HAND;  // 牌的显示状态，如在手中、在桌上等
        private _isUpdate: boolean = false;                //是否被重置过，已翻转为正面

        // {{ YUM: [Add] - 扑克牌效果提示状态管理 }}
        private _currentEffectInfo: CardEffectType = CardEffectType.NONE;         // 当前显示的效果信息

        // ====== 外观与显示 ======
        public static Default_Skin_Id: number = 50000;                 // 默认皮肤id
        private skin_id: number = JackaroCardItem.Default_Skin_Id;                 // 皮肤ID，用于确定卡牌使用的皮肤资源
        private _skinIsLoaded: boolean = false;          // 皮肤是否已加载完成的标志，用于确保图片资源已就绪
        public static normalScale: number = 1;           // 正常比例，用于自己手牌的显示大小
        public static otherScale: number = 0.3;          // 其他玩家手牌的显示比例，较小
        public static pushScaleDown: number = 1.0;       // 下压时的缩放比例，卡牌被压下的效果
        public static pushScale: number = 0.8;           // 打出时的缩放比例，卡牌被打出的效果
        public static dealPokerScale: number = 1.0;      // 发牌时的缩放比例
        public static cardHeight: number = 200;          // 手牌高度，单位像素，用于布局计算
        public pokerStartSpineAni: SpineAniPlayer;

        // ====== 位置与层级 ======
        private firstzInex = 60;                         // 初始层级索引，决定卡牌在显示层中的前后顺序
        private initPosX: number = null;                 // 初始X坐标位置，用于重置位置回到原点
        private initPosY: number = null;                 // 初始Y坐标位置，用于重置位置回到原点
        private nowpos: Laya.Point;                      // 当前位置点，保存卡牌的当前坐标，用于动画和位置恢复

        // ====== 交互状态标志 ======
        private _isUp: boolean = false;                  // 是否处于上升状态（选中状态时卡牌会上移）
        public isPlayOut: boolean = false;              // 是否已出牌，防止重复出牌和操作
        private isResetting: boolean = false;            // 是否正在重置位置中，防止动画冲突
        private _isAnimating: boolean = false;           // 是否正在执行动画，避免多个动画同时进行
        private isTouchStarted: boolean = false;         // 触摸是否已开始，用于跟踪触摸状态
        private isMoved = false;                         // 是否已移动，区分点击和拖拽操作

        // ====== 触摸相关参数 ======
        private sendTouchY: number = 1170;               // 出牌判定的Y坐标阈值，拖拽超过此高度视为出牌意图
        private startNodeX = 0;                          // 触摸开始时节点的X坐标，记录起始位置
        private startNodeY = 0;                          // 触摸开始时节点的Y坐标，记录起始位置
        private touchStartX = 0;                         // 触摸开始的X坐标（屏幕坐标系），用于计算移动距离
        private touchStartY = 0;                         // 触摸开始的Y坐标（屏幕坐标系），用于计算移动距离
        private displacementX = 0;                       // X方向位移，用于计算拖拽时的位置偏移
        private displacementY = 0;                       // Y方向位移，用于计算拖拽时的位置偏移
        private readonly MOVE_THRESHOLD = 10;            // 移动阈值，超过此值才认为是拖拽而非点击，避免误触

        // ====== 边界限制参数 ======
        /**
         * 边界限制常量，防止卡牌被拖出可视区域
         * MIN_X/MAX_X: 水平方向的边界限制
         * MIN_Y/MAX_Y: 垂直方向的边界限制
         */
        private static readonly BOUNDARY = {
            MIN_X: 30,                                   // 左边界最小X坐标值
            MAX_X: 720,                                  // 右边界最大X坐标值
            MIN_Y: 30,                                   // 上边界最小Y坐标值
            MAX_Y: 1590                                  // 下边界最大Y坐标值
        };

        // ====== 回调函数 ======
        private handleCompleteCb: () => void;            // 动画完成回调函数，用于处理动画结束后的逻辑

        // ====== 动画相关变量 ======
        private _downCardTw: Laya.Tween;                 // 卡牌下降动画的Tween对象，用于控制和清理动画
        private _toggleCardPositionTwDown: Laya.Tween;   // 卡牌位置切换动画的Tween对象，用于控制和清理动画
        private _playCardItemTweenDown: Laya.Tween;     // 卡牌出牌动画的Tween对象，用于控制和清理动画
        private _playCardItemTweenMove: Laya.Tween;     // 卡牌出牌动画的Tween对象，用于控制和清理动画
        private _isHandCardReset: boolean = false;      // 手牌是否已被重置

        /**
         * 日志管理方法
         * @param level 日志级别："info" | "verbose" | "debug" | "error"
         * @param context 日志上下文，通常是方法名
         * @param message 日志消息
         * @param data 额外数据
         */
        private log(level: string, context: string, message: string, data?: any): void {
            const pokerInfo = this._data && this._data.poker ? `[牌: ${this._data.poker}]` : '';
            const formattedMessage = `${context} ${pokerInfo} - ${message}`;

            switch (level) {
                case "error":
                    JackaroCardLogger.instance.logError('cardItem', 'JackaroCardItem', formattedMessage, data);
                    break;
                case "info":
                    JackaroCardLogger.instance.log('cardItem', 'JackaroCardItem', formattedMessage);
                    break;
                case "verbose":
                    JackaroCardLogger.instance.logVerbose('cardItem', 'JackaroCardItem', formattedMessage, data);
                    break;
                case "debug":
                default:
                    JackaroCardLogger.instance.logMore('cardItem', 'JackaroCardItem', formattedMessage, data);
                    break;
            }
        }

        constructor() {
            super();
            this.initUI();
        }

        /**
         * 初始化UI组件
         */
        private initUI(): void {
            this.initBoundary();
        }

        /**
         * 初始化边界
         */
        initBoundary(): void {
            // 动态设置边界值，两边各收缩30像素
            let width = Laya.stage.width;
            let height = Laya.stage.height;
            JackaroCardItem.BOUNDARY.MIN_X = 5;
            JackaroCardItem.BOUNDARY.MAX_X = width - 5;
            JackaroCardItem.BOUNDARY.MIN_Y = 5;
            JackaroCardItem.BOUNDARY.MAX_Y = height - 5;
            this.log("debug", "initBoundary", "初始化边界", {
                width: width,
                height: height,
                boundary: JackaroCardItem.BOUNDARY
            });
        }

        /**
         * 初始化事件监听
         * 设置卡牌的触摸事件监听
         */
        private initEvent(): void {
            this.onTouchEvent();
        }

        /**
         * 移除事件监听
         * 清理卡牌的触摸事件监听
         */
        private removeEvent(): void {
            this.offTouchEvent();
        }

        /**
         * 设置卡牌是否已出牌状态
         * @param isOut 是否已出牌
         */
        public setPlayOut(isOut: boolean): void {
            this.isPlayOut = isOut;
            if (isOut) {
                this.isTouchStarted = false; // 如果设置为已出牌，重置触摸开始标志
            }
        }

        /**
         * 设置卡牌初始位置
         * @param posX X坐标
         * @param posY Y坐标
         * @param isResetting 是否正在重置位置
         */
        public setInitPos(posX: number, posY: number, isResetting: boolean = false): void {
            this.initPosX = posX == null ? this.initPosX : posX;
            this.initPosY = posY == null ? this.initPosY : posY;
            if (this.data) {
                this.log("debug", "setInitPos", "设置初始位置", {
                    x: this.initPosX,
                    y: this.initPosY,
                    isResetting
                });
            }
            // 动态设置出牌判定Y轴值为初始位置减去卡牌高度
            // 如果卡牌被拖拽到初始位置上方超过卡牌高度的位置，视为出牌意图
            this.sendTouchY = this.initPosY - JackaroCardItem.cardHeight;
            this.isResetting = isResetting;
            this.initBoundary();
        }

        /**
         * 设置卡牌操作状态
         * @param state 操作状态
         * @param ignoreMyTurn 是否忽略自己回合
         */
        public setState(state: _operatorState, ignoreMyTurn: boolean = false): void {
            this._operatorState = state;
            let isMyActive = ignoreMyTurn ? true : JackaroGamePlayerManager.instance.isMyActive();
            switch (state) {
                case _operatorState.DEFAULT:
                    this.cardMask.visible = false;
                    this.log("verbose", "setState", "设置操作状态", {
                        state: "DEFAULT",
                        poker: this._data && this._data.poker ? this._data.poker : undefined
                    });
                    break;
                case _operatorState.CAN_PLAY:
                    this.cardMask.visible = !isMyActive;
                    this.log("verbose", "setState", "设置操作状态", {
                        state: "CAN_PLAY",
                        poker: this._data && this._data.poker ? this._data.poker : undefined
                    });
                    break;
                case _operatorState.CANNOT_PLAY:
                    this.cardMask.visible = true;
                    this.log("verbose", "setState", "设置操作状态", {
                        state: "CANNOT_PLAY",
                        poker: this._data && this._data.poker ? this._data.poker : undefined
                    });
                    break;
            }
        }

        /**
         * 设置卡牌的显示状态
         * @param state 显示状态枚举值
         */
        public setShowState(state: JackaroCardItemState, noScale: boolean = false): void {
            if (this._showState != state) {
                this._skinIsLoaded = false;
            }
            this._showState = state;
            switch (state) {
                case JackaroCardItemState.IN_HAND:
                    if (this.data && this.data.idx == yalla.Global.Account.idx) {
                        //自己显示正面
                        this.scale(JackaroCardItem.normalScale, JackaroCardItem.normalScale);
                    } else if (noScale) {// 补牌时不缩放

                    } else {
                        //别人显示背面
                        this.scale(JackaroCardItem.otherScale, JackaroCardItem.otherScale);
                    }
                    this.setSkin("card");
                    break;
                case JackaroCardItemState.ON_TABLE:
                    this.setSkin("card");
                    break;
                case JackaroCardItemState.IN_PLAY:
                    this.setSkin("card");
                    break;
                case JackaroCardItemState.INVALID:
                    //置灰
                    this.cardSkinImg.gray = true;
                    break;
            }
        }

        /**
         * 绑定触摸事件
         * 只有当前玩家的卡牌才会绑定触摸事件
         */
        onTouchEvent(): void {
            if ((!this.data || this.data && this.data.idx != yalla.Global.Account.idx)) {
                return;
            }
            this.offTouchEvent();
            this.on(Laya.Event.MOUSE_DOWN, this, this.onTouchStart);
            this.on(Laya.Event.CLICK, this, this.onClick);
            this.on(Laya.Event.MOUSE_UP, this, this.onTouchEnd);
            this.on(Laya.Event.MOUSE_OUT, this, this.onTouchEnd);
            this.on(Laya.Event.MOUSE_MOVE, this, this.onTouchMove);

        }

        /**
         * 解绑触摸事件
         * 移除所有触摸相关的事件监听
         */
        offTouchEvent(): void {
            this.off(Laya.Event.MOUSE_DOWN, this, this.onTouchStart);
            this.off(Laya.Event.MOUSE_UP, this, this.onTouchEnd);
            this.off(Laya.Event.CLICK, this, this.onClick);
            this.off(Laya.Event.MOUSE_OUT, this, this.onTouchEnd);
            this.off(Laya.Event.MOUSE_MOVE, this, this.onTouchMove);

        }

        /**
         * 检查触摸操作是否有效
         * @param context 调用上下文
         * @returns 触摸是否有效
         */
        private isTouchValid(context: string): boolean {
            const valid = !this.isResetting && !this._isAnimating && yalla.Global.isFouce && JackaroCardManger.instance.selfCanTouchCard;

            if (!valid) {
                this.log("debug", context, "触摸操作无效", {
                    isResetting: this.isResetting,
                    _isAnimating: this._isAnimating,
                    isFouce: yalla.Global.isFouce,
                    selfCanTouchCard: JackaroCardManger.instance.selfCanTouchCard
                });
            }

            return valid;
        }

        /**
         * 触摸开始事件处理
         * 记录触摸起始点和卡牌初始位置，为后续拖拽操作做准备
         * @param touch 触摸事件对象
         */
        onTouchStart(touch: Laya.Event): void {
            touch.stopPropagation(); // 阻止事件冒泡，防止触发父节点的触摸事件

            // 检查触摸操作是否有效（动画中、重置中或无焦点状态都视为无效）
            if (!this.isTouchValid("onTouchStart")) {
                return;
            }

            // 检查是否已有其他卡牌正在被触摸
            const cardManager = JackaroCardManger.instance;
            if (cardManager.isTouchActive() && cardManager.getActiveTouchCardItem() !== this) {
                // this.log("debug", "onTouchStart", "另一张卡牌正在被触摸，忽略此触摸", {
                //     currentPoker: this._data.poker,
                //     activePoker: cardManager.getActiveTouchCardItem() ?.data ?.poker
                // });
                return;
            }

            // 记录原始层级和位置信息
            this.firstzInex = this.zOrder;
            this.startNodeX = this.x;
            this.startNodeY = this.y;

            // 记录触摸开始的屏幕坐标，用于计算移动距离
            this.touchStartX = touch.stageX;
            this.touchStartY = touch.stageY;

            this.log("debug", "onTouchStart", "触摸开始", {
                stageX: touch.stageX,
                stageY: touch.stageY
            });

            // 计算触摸点与卡牌锚点的偏移量，用于在移动时保持手指相对位置
            this.displacementX = touch.stageX - this.x;
            this.displacementY = touch.stageY - this.y;

            // 将卡牌置于顶层，确保可见
            this.parent.addChildAt(this, this.parent.numChildren - 1);

            // // {{ YUM: [新增] - 在拖拽开始时重置其他卡牌的选中状态 }}
            // cardManager.resetCardUpState(yalla.Global.Account.idx, this);

            // 标记触摸已开始
            this.isTouchStarted = true;

            // 设置全局触摸状态
            cardManager.setTouchActive(true, this);
        }

        /**
         * 触摸移动事件处理
         * 处理卡牌拖拽过程中的位置更新和边界检测
         * @param touch 触摸事件对象
         */
        onTouchMove(touch: Laya.Event): void {
            touch.stopPropagation(); // 阻止事件冒泡
            const isMyActive = JackaroGamePlayerManager.instance.isMyActive();
            // 如果触摸未开始、状态无效或蒙版可见（不可出的牌），忽略移动事件
            if (!this.isTouchStarted || !this.isTouchValid("onTouchMove")) {
                return;
            }

            if (isMyActive) {
                if (this.cardMask.visible)
                    return;
            }

            // 检查此卡牌是否是当前活动触摸的卡牌
            const cardManager = JackaroCardManger.instance;
            if (!cardManager.isTouchActive() || cardManager.getActiveTouchCardItem() !== this) {
                // this.log("debug", "onTouchMove", "此卡牌不是当前活动触摸的卡牌，忽略移动", {
                //     currentPoker: this._data.poker,
                //     activeTouchCardItem: cardManager.getActiveTouchCardItem() ?.data ?.poker
                // });
                return;
            }

            // 计算从触摸开始点到当前位置的移动距离
            const moveDistanceX = Math.abs(touch.stageX - this.touchStartX);
            const moveDistanceY = Math.abs(touch.stageY - this.touchStartY);

            // 只有当移动距离超过阈值时才认为是真正的移动操作，避免误识别为点击
            if (!this.isMoved && (moveDistanceX > this.MOVE_THRESHOLD || moveDistanceY > this.MOVE_THRESHOLD)) {
                this.isMoved = true;
                // if (!isMyActive && !this.cardMask.visible) {
                //     TipsViewManager.getInstance().showTip(TipViewType.RULE_CARD, {
                //         poker: this._data.poker
                //     });
                //     // 重置其他卡牌的抬起状态，确保同一时间只有一张卡牌处于抬起状态
                //     JackaroCardManger.instance.resetCardUpState(yalla.Global.Account.idx, this);
                // }

                this.log("verbose", "onTouchMove", "开始移动", {
                    moveDistanceX,
                    moveDistanceY
                });
            }

            // 如果没有真正移动，则不更新位置
            if (!this.isMoved) return;

            // 更新卡牌位置，跟随手指移动
            this.x = touch.stageX - this.displacementX;
            this.y = touch.stageY - this.displacementY;
            yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_Show_ChatTouchMaskView, touch);
            this.log("debug", "onTouchMove  ", "选择扑克牌 JackaroCardManger.instance.getCurSelectPoker()", {
                curSelectPoker: JackaroCardManger.instance.getCurSelectPoker(),
                poker: this._data.poker
            });
            // 检查当前选中的扑克牌是否与此卡牌不同
            if (JackaroCardManger.instance.getCurSelectPoker() != this._data.poker) {
                // {{ YUM: [新增] - 重置其他卡牌的选中状态，确保同一时间只有一张卡牌处于选中状态 }}
                JackaroCardManger.instance.resetCardUpState(yalla.Global.Account.idx, this);

                // 发送选择扑克牌的网络请求
                yalla.data.jackaro.JackaroUserService.instance.sendSelectPoker(this._data.poker);
                // 更新当前选中的扑克牌
                JackaroCardManger.instance.setCurSelectPoker(this._data.poker);
                TipsViewManager.getInstance().showTip(TipViewType.RULE_CARD, {
                    poker: this._data.poker
                });
                this.setSelectBgVisible(true);
                this.log("debug", "onTouchMove  getCurSelectPoker2", "选择扑克牌", {
                    poker: this._data.poker
                });

                if (!JackaroGamePlayerManager.instance.isMyActive()) {
                    this.log("debug", "onTouchMove", "非我的回合，新棋子预览 this._data.poker", this._data.poker);
                    JackaroCardManger.instance.setCurShowOrbitPoker(this._data.poker);
                    JackaroCardManger.instance.refreshChessOrbit();
                } else {
                    this.log("debug", "onTouchMove", "我的回合，刷新棋子预览");
                    if (JackaroCardManger.instance.getAllowPokerData(this._data.poker)) {
                        this.log("debug", "onTouchMove", "我的回合，刷新棋子预览，允许出牌,this._data.poker", this._data.poker);
                        JackaroCardManger.instance.showChessOrbit(this._data.poker);
                    } else {
                        JackaroChessManager.instance.removeAllChessOrbit();
                    }
                }
                // {{ YUM: [新增] - 设置当前预览轨道牌并刷新棋子预览 }}

            }

            // 检测是否超出边界，超出则触发相应处理
            if (this.isOutOfBounds(this.x, this.y)) {
                this.handleBoundaryTouch(touch);
                return;
            }
        }

        /**
         * 处理边界触摸情况
         */
        private handleBoundaryTouch(touch: Laya.Event): void {
            this.log("debug", "handleBoundaryTouch", "超出边界");

            if (touch.stageY < this.sendTouchY && this._operatorState == _operatorState.CAN_PLAY && !JackaroCardManger.instance.isCurPlayPoker()) {
                this.handleDragToPlay(true);
            } else if (JackaroCardManger.instance.isCurPlayPoker()) {
                this.resetPosition();
            } else {
                this.resetPosition();
            }

            this.resetTouchState();
        }

        /**
         * 触摸结束事件处理
         * 处理触摸抬起时的逻辑，包括出牌、取消等
         * @param touch 触摸事件对象
         */
        private onTouchEnd(touch: Laya.Event): void {
            touch.stopPropagation(); // 阻止事件冒泡
            // ===== 日志记录 =====
            this.log("debug", "onTouchEnd", "触摸结束", {
                type: touch.type,
                isPlayOut: this.isPlayOut,
                isMoved: this.isMoved,
                isTouchStarted: this.isTouchStarted,
                isTouchValid: this.isTouchValid("onTouchEnd"),
                isFouce: yalla.Global.isFouce
            });
            yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_Show_ChatTouchMaskView, touch);
            // ===== 无效状态快速返回 =====
            // 全局焦点丢失或触摸状态无效，强制重置并返回
            if (!yalla.Global.isFouce || !this.isTouchStarted || !this.isTouchValid("onTouchEnd")) {
                this.log("debug", "onTouchEnd", !yalla.Global.isFouce ? "全局焦点丢失" : "触摸未开始或状态无效", {
                    isFouce: yalla.Global.isFouce,
                    isTouchStarted: this.isTouchStarted,
                    isTouchValid: this.isTouchValid("onTouchEnd")
                });
                if (!yalla.Global.isFouce)
                    this.resetPosition(true);
                JackaroCardManger.instance.resetTouchState();
                return;
            }

            // ===== 特殊状态处理 =====
            // 已出牌或不可出的牌（蒙版可见）
            if (this.isPlayOut || (this.cardMask.visible && JackaroGamePlayerManager.instance.isMyActive())) {
                this.log("debug", "onTouchEnd", this.isPlayOut ? "已出牌" : "点击蒙版（不可出的牌）");

                // 如果是蒙版可见，显示规则提示
                if (this.cardMask.visible && JackaroGamePlayerManager.instance.isMyActive()) {
                    this.handleClickMask();
                }

                this.resetTouchState(false);
                JackaroCardManger.instance.resetTouchState();
                return;
            }

            // ===== 未移动状态处理 =====
            if (!this.isMoved) {
                this.log("debug", "onTouchEnd", "没有移动，直接返回");
                this.resetTouchState(false);
                return;
            }

            // ===== 拖拽出牌判断 =====
            const canPlayCard = touch.stageY < this.sendTouchY &&
                this._operatorState == _operatorState.CAN_PLAY &&
                !JackaroCardManger.instance.isCurPlayPoker();

            this.log("debug", "onTouchEnd", "拖拽处理", {
                stageY: touch.stageY,
                sendTouchY: this.sendTouchY,
                canPlayCard: canPlayCard,
                _operatorState: this._operatorState,
                isCurPlayPoker: JackaroCardManger.instance.isCurPlayPoker()
            });

            // 拖拽判断和处理
            if (canPlayCard) {
                // 满足出牌条件：处理出牌
                this.log("debug", "onTouchEnd", "拖拽到出牌区域");
                this.handleDragToPlay(true);
            } else {
                // 所有其他情况：重置位置
                this.log("debug", "onTouchEnd", JackaroCardManger.instance.isCurPlayPoker() ?
                    "已出过手牌：重置位置" : "拖拽未到出牌区域，重置位置");
                this.resetPosition();
            }

            // ===== 最终状态重置 =====
            this.resetTouchState();
            JackaroCardManger.instance.resetTouchState();
        }

        /**
         * 处理点击蒙版
         * 显示牌的提示信息
         */
        private handleClickMask(): void {
            TipsViewManager.getInstance().showTip(TipViewType.RULE_CARD, {
                poker: this._data.poker
            });
            this.log("debug", "handleClickMask", "点击蒙版显示牌提示", {
                poker: this._data && this._data.poker ? this._data.poker : undefined
            });
            yalla.Sound.playSound('select');
            this.setSelectBgVisible(true);
            JackaroCardManger.instance.resetCardUpState(yalla.Global.Account.idx, this);
            yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_Click_Invalid_Poker, this);
        }

        /**
         * 处理拖拽出牌逻辑
         */
        private handleDragToPlay(isTouchEnd: boolean = false): void {
            this.log("debug", "handleDragToPlay", "处理拖拽出牌逻辑", {
                isTouchEnd: isTouchEnd,
                isMyActive: JackaroGamePlayerManager.instance.isMyActive()
            });
            if (!JackaroGamePlayerManager.instance.isMyActive()) {
                if (isTouchEnd)
                    ToastManager.showToast("It's not your turn");
                this.resetPosition(true);
                return;
            }
            JackaroCardManger.instance.selectCardList.push(this._data.poker);
            let result = JackaroCardManger.instance.handleClickPoker(true, this._data.poker);
            if (!result) {
                this.log("debug", "handleDragToPlay", "出牌操作无效：重置位置");
                this.resetPosition(true);
                return;
            }
            this.isTouchStarted = false;
        }

        /**
         * 重置卡牌位置到初始状态
         */
        public resetPosition(isNotTurn: boolean = false): void {
            if (this.selectBg) {
                this.selectBg.visible = false;
            }

            TipsViewManager.getInstance().hideTip(TipViewType.RULE_CARD);
            this.isResetting = true;
            this.setAnimating(true, "resetPosition");
            this.resetTouchState(false);
            // 重置全局触摸状态和抬起锁
            JackaroCardManger.instance.clearAllState();

            this.log("debug", "resetPosition", "开始重置位置动画", {
                fromX: this.x,
                fromY: this.y,
                toX: this.initPosX,
                toY: this.initPosY
            });

            const completeCb = () => {
                this.isResetting = false;
                this.setAnimating(false, "resetPosition.completeCb");
                this._isUp = false;
                JackaroCardManger.instance.clearSelectPoker(isNotTurn ? null : this._data ? this._data.poker : null);
                this.log("debug", "resetPosition", "位置重置完成");
            };

            JackaroCardManger.instance.resetCardUpState(yalla.Global.Account.idx, this);

            // 新增：如果全局焦点丢失（如切换用户/回合），直接设置最终位置
            if (yalla.Global.isFouce === false) {
                this.x = this.initPosX;
                this.y = this.initPosY;
                completeCb();
                return;
            }

            this.executePositionAnimation(this.initPosX, this.initPosY, 300, completeCb, "resetPosition");
            JackaroCardManger.instance.resetShowOrbitPokerByPlayPoker();
            JackaroCardManger.instance.refreshChessOrbit();
        }

        /**
         * 处理点击事件
         * 处理卡牌的点击逻辑，包括选中和双击出牌
         * @param e 点击事件对象
         * @param type 事件类型
         */
        private onClick(e: Laya.Event, type: string): void {
            e.stopPropagation(); // 阻止事件冒泡
            const isMyActive = JackaroGamePlayerManager.instance.isMyActive();
            this.log("debug", "onClick", "点击事件", {
                isMoved: this.isMoved,
                _isAnimating: this._isAnimating,
                isFouce: yalla.Global.isFouce,
                cardMaskVisible: this.cardMask.visible,
                isMyActive: isMyActive,
                selfCanTouchCard: JackaroCardManger.instance.selfCanTouchCard
            });
            // 如果移动过、动画中、无焦点或蒙版可见，忽略点击
            if (this.isMoved || this._isAnimating || !yalla.Global.isFouce || (this.cardMask.visible && isMyActive) || !JackaroCardManger.instance.selfCanTouchCard) return;

            // 检查是否已有其他卡牌正在被触摸，如果有则忽略点击
            const cardManager = JackaroCardManger.instance;
            if (cardManager.isTouchActive() && cardManager.getActiveTouchCardItem() !== this) {
                this.log("debug", "onClick", "其他卡牌正在触摸中，忽略点击");
                return;
            }

            // 检查是否有卡牌正在执行抬起动作，如果有则忽略点击
            // 注意：这里我们不检查当前卡牌的状态，因为双击同一张牌是允许的
            if (cardManager.isCardRaisingLocked() && !this._isUp) {
                this.log("debug", "onClick", "其他卡牌正在抬起中，忽略此次点击");
                return;
            }
            yalla.Sound.playSound('select'); // 播放选择音效

            this.log("debug", "onTouchStart", "播放点击音效 ");
            if (!yalla.data.jackaro.JackaroUserService.instance || !this._data) return;

            // 显示牌规则提示
            TipsViewManager.getInstance().showTip(TipViewType.RULE_CARD, {
                poker: this._data.poker
            });

            // 如果牌不可出，不处理选中逻辑
            if (this._operatorState == _operatorState.CANNOT_PLAY) {
                this.log("debug", "onClick", "牌不可出，不处理选中逻辑");
                return;
            }

            // 添加到选牌列表
            JackaroCardManger.instance.selectCardList.push(this._data.poker);

            // 清理可能存在的切换动画
            this.clearToggleAnimation();
            var selectArr = JackaroCardManger.instance.selectCardList;
            this.log("debug", "onClick", "选牌数组", { selectArr: selectArr });

            // 检查是否是双击相同牌（快速点击两次同一张牌）
            var len = selectArr.length;
            var isDouble = false;
            if (selectArr.length >= 2 && selectArr[len - 2] == selectArr[len - 1] && selectArr[len - 2] == this._data.poker) {
                this.log("debug", "onClick", "检测到双击相同牌");
                isDouble = true;
            } else {
                // 单击时切换卡牌位置（上升或下降）
                this.log("verbose", "onClick", "单击牌", { isMoved: this.isMoved });
                if (!this.isMoved)
                    this.toggleCardPosition();
            }

            // 如果没有移动，处理选牌逻辑
            if (!this.isMoved) {
                this.log("debug", "onClick", "没有移动，处理选牌逻辑");
                this.selectCard(isDouble);
            }

            // 重置全局触摸状态
            JackaroCardManger.instance.resetTouchState();
        }

        /**
         * 选择卡牌
         * @param isDouble 是否双击
         */
        private selectCard(isDouble: boolean = false): void {
            this.log("debug", "selectCard", "选择卡牌", { isDouble });

            if (isDouble && !JackaroGamePlayerManager.instance.isMyActive()) {
                // // ToastManager.showToast("It's not your turn");
                // TipsViewManager.getInstance().hideTip(TipViewType.RULE_CARD);
                this.resetPosition(true);
                return;
            }
            if (isDouble && JackaroCardManger.instance.isCurPlayPoker()) {
                this.log("debug", "selectCard", "已出过手牌：重置位置");
                this.resetPosition(true);
                return;
            }

            if (!isDouble) {
                this.selectBg.visible = true;
                this.log("debug", "selectCard", "显示选中背景");
            }

            let result = JackaroCardManger.instance.handleClickPoker(isDouble, this._data.poker);
            if (!result) {
                this.log("debug", "selectCard", "点击操作无效：重置位置");
                this.resetPosition(true);
            }
        }

        /**
         * 设置卡牌数据
         * @param data 卡牌数据
         * @param playCardFunc 可选的出牌回调函数
         */
        public setCardData(data: JackaroCardDataInterface, playCardFunc?: Function): void {
            if (!data) {
                this.log("error", "setCardData", "数据为空");
                return;
            }

            this._data = data;
            this.log("debug", "setCardData", "设置卡牌数据", {
                poker: data.poker,
                idx: data.idx,
                showState: data.showState
            });

            if (this._data.idx == yalla.Global.Account.idx) {
                this.initEvent();
            }
            this.setShowState(data.showState);
            this.visible = true;
        }
        public setSkinId(skin_id: number): void {
            this.skin_id = skin_id;
        }
        /**
         * 加载自定义皮肤
         * @param skinElement 要设置皮肤的元素
         * @param skinPath 皮肤路径
         */
        private loadCustomSkin(skinElement: any, skinPath: string): void {
            if (this._skinIsLoaded) {
                if (skinElement) skinElement.skin = skinPath;
            } else {
                const skinFileName = `j_pk_${this.skin_id}.png`;
                yalla.event.YallaEvent.instance.once(skinFileName, this, () => {
                    this._skinIsLoaded = true;
                    if (this.destroyed) return;
                    if (skinElement) skinElement.skin = skinPath;
                });
                yalla.File.getFileByNative(skinFileName);
            }
        }

        /**
         * 构建皮肤路径
         * @param baseName 基础名称
         * @param suffix 后缀
         * @param poker 牌值
         * @returns 完整皮肤路径
         */
        private buildSkinPath(baseName: string, suffix: string = "", poker?: number): string {
            const suffixPart = suffix ? `_${suffix}` : "";
            const pokerPart = poker !== undefined ? `_${poker}` : "";
            return `jackaroCard/${baseName}${suffixPart}_52000${pokerPart}.png`;
        }

        /**
         * 设置牌背皮肤
         */
        private setBackgroundSkin(): void {
            if (!this.backBg) return;

            if (this.skin_id !== JackaroCardItem.Default_Skin_Id) {
                const customSkinPath = `${yalla.File.filePath}j_pk_${this.skin_id}.png`;
                this.loadCustomSkin(this.backBg, customSkinPath);

            } else {
                // 设置默认牌背皮肤
                this.backBg.skin = this.buildSkinPath("card_back");
            }
        }

        /**
         * 设置手牌状态皮肤
         * @param name 皮肤名称
         */
        private setHandCardSkin(name: string): void {
            if (this.data && this.data.idx === yalla.Global.Account.idx) {
                // 自己显示正面
                this.log("verbose", "setSkin", "设置自己牌的正面皮肤");
                const suffix = JackaroCardManger.instance.getPokerTypeSuffix(this.data.poker);
                this.cardSkinImg.skin = this.buildSkinPath(`${name}_top`, suffix, this.data.poker);
            } else {
                // 别人显示背面
                this.setOtherPlayerCardSkin();
            }
        }

        /**
         * 设置其他玩家卡牌皮肤
         */
        private setOtherPlayerCardSkin(): void {
            if (this.skin_id !== JackaroCardItem.Default_Skin_Id) {
                const customSkinPath = `${yalla.File.filePath}j_pk_${this.skin_id}.png`;
                this.loadCustomSkin(this.backBg, customSkinPath);
            } else {
                if (this.backBg) {
                    this.backBg.skin = this.buildSkinPath("card_back");
                }
            }
        }

        /**
         * 设置出牌状态皮肤
         * @param name 皮肤名称
         */
        private setPlayCardSkin(name: string): void {
            const room = yalla.data.jackaro.JackaroUserService.instance.room;
            this.log("debug", "setSkin", "更新弃牌皮肤", {
                isComplexMode: room?.isComplexMode(),
                isComplexPlaySpecialPokerColor: JackaroCardManger.instance.isComplexPlaySpecialPokerColor(this.data.poker)
            });
            const suffix = JackaroCardManger.instance.getPokerTypeSuffix(this.data.poker);
            this.cardSkinImg.skin = this.buildSkinPath(`${name}_top`, suffix, this.data.poker);
        }

        /**
         * 设置卡牌皮肤
         * 根据不同的显示状态和玩家设置不同的卡牌皮肤
         * @param name 皮肤名称
         */
        public setSkin(name: string): void {
            this.log("debug", "setSkin", "设置卡牌皮肤", {
                showState: this._showState,
                poker: this.data.poker,
                skin_id: this.skin_id
            });

            // 设置牌背
            this.setBackgroundSkin();

            // 根据显示状态设置正面皮肤
            switch (this._showState) {
                case JackaroCardItemState.IN_HAND:
                    this.setHandCardSkin(name);
                    break;

                case JackaroCardItemState.ON_TABLE:
                    this.cardSkinImg.skin = this.buildSkinPath("card_back");
                    break;

                case JackaroCardItemState.IN_PLAY:
                    this.setPlayCardSkin(name);
                    break;
            }
        }

        public set data(v) {
            this._data = v;
        }

        /**
         * 获取卡牌数据
         * @returns 返回卡牌的数据接口
         */
        public get data(): JackaroCardDataInterface {
            return this._data;
        }

        public get operatorState(): _operatorState {
            return this._operatorState;
        }

        /**
         * 获取卡牌是否处于抬起状态
         * @returns 返回卡牌是否处于抬起状态
         */
        public get isUp(): boolean {
            return this._isUp;
        }
         /**
         * 获取卡牌是否处于抬起状态
         * @returns 返回卡牌是否处于抬起状态
         */
        public get isMove(): boolean {
            return this.isMoved;
        }


        // /**
        //  * 设置卡牌抬起状态
        //  * @param value 是否抬起
        //  */
        // public set isUp(value: boolean) {
        //     this._isUp = value;
        //     this.log("debug", "setIsUp", "设置卡牌抬起状态", {
        //         poker: this._data && this._data.poker ? this._data.poker : undefined,
        //         isUp: value
        //     });
        // }

        /**
         * 获取卡牌宽度
         * 根据不同的显示状态返回对应比例的宽度
         * @returns 返回计算后的卡牌宽度
         */
        public getCardWidth(): number {
            if (this._showState == JackaroCardItemState.IN_HAND) {
                if (this.data.idx == yalla.Global.Account.idx) {
                    return PokerSize.selfWidth;
                } else {
                    return PokerSize.otherWidth;
                }
            }
            return PokerSize.selfWidth;
        }

        /**
         * 设置选中背景的可见性
         * @param visible 是否可见
         */
        public setSelectBgVisible(visible: boolean): void {
            if (this.selectBg) {
                this.selectBg.visible = visible;
                // this.log("debug", "setSelectBgVisible", "设置选中背景可见性", {
                //     poker: this._data && this._data.poker ? this._data.poker : undefined,
                //     visible: this.selectBg.visible
                // });
            }
        }

        /**
         * 执行位置动画
         * @param targetX 目标X坐标
         * @param targetY 目标Y坐标
         * @param duration 动画时长
         * @param complete 完成回调
         * @param method 调用方法名称
         */
        private executePositionAnimation(targetX: number, targetY: number, duration: number, complete?: Function, method: string = "executePositionAnimation"): void {
            // 当全局焦点状态为false时，直接设置最终位置
            if (yalla.Global.isFouce === false) {
                this.x = targetX;
                this.y = targetY;
                this.setAnimating(false, `${method}.isFouce=false`);
                complete && complete();
                return;
            }

            this.setAnimating(true, `${method}.startAnimation`);

            // 创建并执行动画
            Laya.Tween.to(this,
                { x: targetX, y: targetY },
                duration,
                Laya.Ease.strongOut,
                Laya.Handler.create(this, () => {
                    this.setAnimating(false, `${method}.animationComplete`);
                    complete && complete();
                })
            );
        }

        /**
         * 卡牌下降动画
         * 使卡牌回到初始位置或下降20个单位
         */
        public downCard(): void {
            this._isUp = false; // 设置状态为下降
            const targetY = this.initPosY; // 计算目标位置，下降20个单位
            const targetX = this.initPosX;

            // 解除卡牌抬起锁状态
            JackaroCardManger.instance.setCardRaisingLock(false);

            // 同步设置选中背景状态
            this.setSelectBgVisible(false);
            // {{ YUM: [修复] - 下降动画时重置奖励背景状态 }}
            this.setBgRewardVisible(false);

            // 检查当前位置是否已经是目标位置（考虑1像素的误差）
            const positionAlreadyMatched = Math.abs(this.y - targetY) <= 1 && Math.abs(this.x - targetX) <= 1;

            if (positionAlreadyMatched) {
                this.log("debug", "downCard", "当前位置已是目标位置，跳过动画");
                this.setAnimating(false, "downCard.positionAlreadyMatched");
                this.isResetting = false;
                this.resetTouchState();
                return;
            }

            // 清理现有动画 - 修复：清理所有相关动画，包括抬起动画
            this.clearAnimation(this._downCardTw, "downCard.clearTween");
            this.clearAnimation(this._toggleCardPositionTwDown, "downCard.clearToggleTween");
            this._downCardTw = null;
            this._toggleCardPositionTwDown = null;

            if (!this.isPlayOut) {
                JackaroCardManger.instance.clearSelectPoker(this._data.poker);
                this.setAnimating(true, "downCard.startAnimation");
                // 记录动画对象的引用
                this._downCardTw = Laya.Tween.to(this,
                    { x: targetX, y: targetY },
                    100,
                    Laya.Ease.strongOut,
                    Laya.Handler.create(this, this.downCardFinish)
                );
            }

            this.resetTouchState();
            this.isResetting = false;
        }
        private downCardFinish() {
            this.setAnimating(false, "downCard.animationComplete");
            this.event("downCardFinish", [this]);
            this.log("debug", "downCard", "下降动画完成");
        }

        /**
         * 切换卡牌位置
         * 将卡牌上下切换位置
         */
        private toggleCardPosition(): void {
            // 如果当前要将卡牌抬起，先检查是否有其他卡牌正在抬起
            if (!this._isUp) {
                const cardManager = JackaroCardManger.instance;
                // 如果当前已有卡牌抬起或正在抬起中，则忽略此次抬起操作
                if (cardManager.isCardRaisingLocked()) {
                    this.log("debug", "toggleCardPosition", "其他卡牌正在抬起中，忽略此次抬起");
                    return;
                }
                // 设置抬起锁
                cardManager.setCardRaisingLock(true);
            } else {
                // 如果是放下操作，解除抬起锁
                JackaroCardManger.instance.setCardRaisingLock(false);
            }
            this.log("debug", "toggleCardPosition", "切换卡牌位置  1/2", {
                isUp: this._isUp
            });

            this._isUp = !this._isUp; // 切换状态

            // 计算目标位置
            const targetY = this._isUp ? this.initPosY - 20 : this.initPosY; // 上升或下降20个单位

            // 检查当前位置是否已经是目标位置（考虑1像素的误差）
            const positionAlreadyMatched = Math.abs(this.y - targetY) <= 1;

            this.setSelectBgVisible(this._isUp);
            this.log("debug", "toggleCardPosition", "切换卡牌位置 2/2", {
                isUp: this._isUp
            });

            // 如果当前卡牌要抬起，需要重置其他卡牌的抬起状态
            if (this._isUp) {
                // 重置其他卡牌的抬起状态，确保同一时间只有一张卡牌处于抬起状态
                JackaroCardManger.instance.resetCardUpState(yalla.Global.Account.idx, this);
            }

            if (positionAlreadyMatched) {
                this.log("debug", "toggleCardPosition", "当前位置已是目标位置，跳过动画");
                this.setAnimating(false, "toggleCardPosition.positionAlreadyMatched");
                // 如果位置已经匹配但仍然是放下操作，需要解除抬起锁
                if (!this._isUp) {
                    JackaroCardManger.instance.setCardRaisingLock(false);
                }
                return;
            }

            // 清理现有动画
            this.clearAnimation(this._toggleCardPositionTwDown, "toggleCardPosition.clearTween");
            this._toggleCardPositionTwDown = null;

            if (!this.isPlayOut) {
                this.setAnimating(true, "toggleCardPosition.startAnimation");
                // 记录动画对象的引用
                this._toggleCardPositionTwDown = Laya.Tween.to(this,
                    { y: targetY },
                    100,
                    Laya.Ease.strongOut,
                    Laya.Handler.create(this, () => {
                        this.setAnimating(false, "toggleCardPosition.animationComplete");
                        this.log("verbose", "toggleCardPosition", "位置切换动画完成");
                        // 动画完成后，如果是放下操作，解除抬起锁
                        if (!this._isUp) {
                            JackaroCardManger.instance.setCardRaisingLock(false);
                        }
                    }),
                    0,
                    true
                );
            }
        }

        /**
         * 清理卡牌状态
         * 完全重置卡牌到初始状态，用于回收或重新使用卡牌组件
         * 会清理所有动画、事件监听、视觉状态和数据状态
         */
        public clear(): void {
            this.log("debug", "clear", "清理卡牌状态", {
                poker: this._data && this._data.poker ? this._data.poker : undefined
            });

            // 清理所有动画
            this.clearAllAnimations();

            // 重置所有状态标志
            this.removeEvent();
            this._operatorState = _operatorState.DEFAULT;
            this._showState = JackaroCardItemState.IN_HAND;
            this._skinIsLoaded = false;
            this.setSelectBgVisible(false);
            this.setBgRewardVisible(false); // {{ YUM: [修复] - 清理时重置奖励背景状态 }}
            this.setState(_operatorState.DEFAULT);
            this.rotation = 0;
            this.setPlayOut(false);
            this.resetTouchState(true);
            this.isResetting = false;
            this.visible = true;
            this._isHandCardReset = false;

            // 重置所有全局状态（触摸状态和抬起锁）
            JackaroCardManger.instance.clearAllState();
            //回收时，重置牌背的皮肤 
            this._skinIsLoaded = false;
            this.skin_id = JackaroCardItem.Default_Skin_Id;
            if (this.backBg && !this.backBg.destroyed) {
                this.backBg.skin = `jackaroCard/card_back_52000.png`;
                this.backBg.visible = false;
                this.backBg.scale(1, 1);
            }
            if (this.cardSkinImg && !this.cardSkinImg.destroyed) {
                this.cardSkinImg.skin = '';
                this.cardSkinImg.scale(1, 1);
            }
            if (this.getEffectInfo() !== CardEffectType.NONE) {
                this.setEffectInfo(CardEffectType.NONE);
            }
            this.log("debug", "clear", "重置所有状态完成");
        }

        /**
         * 重连后重置部分手牌状态
         * 在网络重连后恢复卡牌的基本状态，但保留卡牌的基础数据
         */
        public resetCardState(): void {
            this.setSelectBgVisible(false);
            this.setBgRewardVisible(false); // {{ YUM: [修复] - 重连后重置奖励背景状态 }}
            this.setPlayOut(false);
            this.isResetting = false;
            this._isUpdate = false; // 重置翻转状态
            this.resetTouchState(true);
        }

        /**
         * 清理切换动画
         * 专门用于清理卡牌上下切换相关的动画，避免动画冲突
         */
        public clearToggleAnimation(): void {
            this.clearAnimation(this._toggleCardPositionTwDown, "clearToggleAnimation.afterClearTween");
            this.clearAnimation(this._downCardTw, "clearToggleAnimation.afterClearDownTween");
            this._toggleCardPositionTwDown = null;
            this._downCardTw = null;
        }

        /**
         * 设置动画状态并记录日志
         * @param state 是否在动画中
         * @param method 调用此方法的函数名
         */
        public setAnimating(state: boolean, method: string): void {
            this._isAnimating = state;
            this.log("debug", method, `设置_isAnimating=${state}`, {
                poker: this._data && this._data.poker ? this._data.poker : undefined
            });
        }

        public getPokerStarAnimation(): Laya.Box {
            return this.starAnimationBox;
        }


        /**
         * 重置触摸和交互状态
         * @param resetSelection 是否重置选中状态
         */
        private resetTouchState(resetSelection: boolean = true): void {
            this.isTouchStarted = false;
            this.isMoved = false;

            if (resetSelection) {
                this._isUp = false;
                this.setSelectBgVisible(false);
            }
        }

        /**
         * 清理指定动画对象
         * 安全地清理Tween动画，并更新动画状态
         * @param tweenObject 需要清理的动画对象
         * @param methodName 调用此方法的函数名，用于日志记录
         */
        private clearAnimation(tweenObject: Laya.Tween, methodName: string): void {
            if (tweenObject) {
                Laya.Tween.clearTween(tweenObject);
                this.setAnimating(false, methodName);
            }
        }

        /**
         * 清理所有动画
         * 清理所有与卡牌相关的动画，包括上下移动、位置重置等
         */
        private clearAllAnimations(): void {
            // 清理所有动画对象
            this.clearAnimation(this._downCardTw, "clearAllAnimations");
            this.clearAnimation(this._toggleCardPositionTwDown, "clearAllAnimations");
            this._downCardTw = null;
            this._toggleCardPositionTwDown = null;

            // 清理播放相关的动画
            if (this.playCardItemTweenDown) {
                this.playCardItemTweenDown.complete();
                this.playCardItemTweenDown = null;
            }
            if (this.playCardItemTweenMove) {
                this.playCardItemTweenMove.complete();
                this.playCardItemTweenMove = null;
            }

            // 清理所有Tween和定时器
            Laya.Tween.clearAll(this);
            Laya.timer.clearAll(this);
            this.setAnimating(false, "clearAllAnimations");
        }

        /**
         * 检查坐标是否超出规定边界
         * 用于防止卡牌被拖出屏幕可视区域
         * @param x X坐标
         * @param y Y坐标
         * @returns 是否超出边界的布尔值
         */
        private isOutOfBounds(x: number, y: number): boolean {
            // 获取卡牌的宽度和高度
            const cardWidth = this.width / 2;
            const cardHeight = this.height / 2;

            // 计算卡牌的边缘坐标
            const leftEdge = x - cardWidth;
            const rightEdge = x + cardWidth;
            const topEdge = y - cardHeight;
            const bottomEdge = y + cardHeight;

            this.log("verbose", "isOutOfBounds", "检查坐标是否超出规定边界", {
                poker: this._data && this._data.poker ? this._data.poker : undefined,
                x: x,
                y: y,
                cardWidth: cardWidth,
                cardHeight: cardHeight,
                leftEdge: leftEdge,
                rightEdge: rightEdge,
                topEdge: topEdge,
                bottomEdge: bottomEdge,
                boundary: JackaroCardItem.BOUNDARY
            });

            // 判断卡牌的任意边缘是否超出边界
            return leftEdge < JackaroCardItem.BOUNDARY.MIN_X ||
                rightEdge > JackaroCardItem.BOUNDARY.MAX_X ||
                topEdge < JackaroCardItem.BOUNDARY.MIN_Y ||
                bottomEdge > JackaroCardItem.BOUNDARY.MAX_Y;
        }

        /**
         * 销毁卡牌对象
         * 完全销毁卡牌，释放所有资源和引用，在卡牌被移除场景时调用
         * 重写destroy方法，确保清理所有资源和事件监听
         */
        public destroy(): void {
            this.log("verbose", "destroy", "销毁卡牌对象");

            // 清理所有动画和事件
            this.clearAllAnimations();
            this.offTouchEvent();

            // 清理数据
            this._data = null;
            this.initPosX = 0;
            this.initPosY = 0;
            this._toggleCardPositionTwDown = null;
            this._downCardTw = null;
            this._isUp = false;
            this.isMoved = false;
            this.isResetting = false;
            this._isAnimating = false;
            this._isHandCardReset = false;
            this.startNodeX = 0;
            this.startNodeY = 0;

            super.destroy();
        }

        public get playCardItemTweenDown(): Laya.Tween {
            return this._playCardItemTweenDown;
        }

        public get playCardItemTweenMove(): Laya.Tween {
            return this._playCardItemTweenMove;
        }

        public set playCardItemTweenDown(tween: Laya.Tween) {
            this._playCardItemTweenDown = tween;
        }

        public set playCardItemTweenMove(tween: Laya.Tween) {
            this._playCardItemTweenMove = tween;
        }
        public playCardFlip(isNow: boolean = false, onComplete?: Function): void {
            if (isNow) {
                this.setBackBgVisible(false);
                this._isUpdate = true; // 标记已翻转为正面
                onComplete && onComplete();
                return;
            }

            if (this._isUpdate) {
                this.setBackBgVisible(false);
                onComplete && onComplete();
                return;
            }
            let duration = 180
            // 先显示背面,隐藏正面
            this.setBackBgVisible(true);

            if (this.CardFlip) {
                this.CardFlip.play(0, false);
                this.CardFlip.on(Laya.Event.COMPLETE, this, () => {
                    this._isUpdate = true; // 标记已翻转为正面
                    this.log("debug", "playCardFlip", "CardFlip动画完成 总发牌时间", {
                        time: Date.now() - JackaroHandCardView.dealPokerStartTime
                    });
                    onComplete && onComplete();
                });
            }
            if (this.pokerStartSpineAni) {
                this.pokerStartSpineAni.visible = true;
                this.pokerStartSpineAni.scaleX = Math.random() < 0.5 ? 1 : -1; // 随机方向
                this.pokerStartSpineAni.scaleY = Math.random() < 0.5 ? 1 : -1; // 随机方向
                this.pokerStartSpineAni.play("xingxing", false);
                this.pokerStartSpineAni.on(Laya.Event.COMPLETE, this, () => {
                    this.pokerStartSpineAni.visible = false;
                    this.pokerStartSpineAni.removeSelf();
                    this.pokerStartSpineAni.destroy
                });
            }
        }
        public setBackBgVisible(visible: boolean, isSelf: boolean = true): void {
            this.backBg.scaleX = visible ? 1 : 0;
            this.backBg.visible = visible
            if (isSelf) {
                this.setBaiseVisible(visible);
            }
            // this.cardSkinImg.scaleX = visible ? 0 : 1;
        }
        setBaiseVisible(visible: boolean) {
            this.baise.visible = visible;
            this.baise.alpha = visible ? 1 : 0;
            this.yinying.visible = visible;
            this.yinying.scaleX = visible ? 1 : 0;
        }
        public setBackBgVisibleR(visible: boolean): void {
            this.backBg.scaleX = visible ? 1 : 0.01;
            this.backBg.visible = visible
            this.baise.visible = visible;
            this.yinying.visible = visible;
        }
        public setBackBgVisibleDiscard(visible: boolean): void {
            this.backBg.visible = visible
        }

        /**
         * 设置卡牌是否已更新为正面状态
         * @param updated 是否已更新为正面
         */
        public setUpdated(updated: boolean): void {
            this._isUpdate = updated;
        }

        /**
         * 获取卡牌是否已更新为正面状态
         * @returns 是否已更新为正面
         */
        public isUpdated(): boolean {
            return this._isUpdate;
        }

        /**
 * 设置扑克牌效果提示
 * @param effectType 效果类型枚举
 */
        public setEffectInfo(effectType: CardEffectType): void {
            this._currentEffectInfo = effectType;

            if (effectType !== CardEffectType.NONE) {
                // {{ YUM: [修改] - 采用info_sp节点图片展示，根据语言环境切换英语和阿语 }}
                const language = yalla.Font.isRight() ? 'ar' : 'en';
                let imagePath = '';

                switch (effectType) {
                    case CardEffectType.KILL:
                        imagePath = `jackaroCard/card_KILL_${language}.png`;
                        break;
                    case CardEffectType.END:
                        imagePath = `jackaroCard/card_END_${language}.png`;
                        break;
                    default:
                        if (this.info_sp) {
                            this.info_sp.visible = false;
                        }
                        return;
                }

                if (this.info_sp) {
                    this.info_sp.skin = imagePath;
                    this.info_sp.visible = true;
                }

                this.log("debug", "setEffectInfo", "设置效果提示图片", {
                    effectType: effectType,
                    language: language,
                    imagePath: imagePath
                });
            } else {
                if (this.info_sp) {
                    this.info_sp.visible = false;
                }
            }
        }

        /**
         * 获取扑克牌效果提示
         * @returns 效果类型枚举
         */
        public getEffectInfo(): CardEffectType {
            return this._currentEffectInfo;
        }

        public setHandCardReset(val: boolean) {
            this._isHandCardReset = val;
        }
        public isHandCardReset(): boolean {
            return this._isHandCardReset;
        }

        public setBgRewardVisible(visible: boolean): void {
            this.bg_reward.visible = visible;
        }
    }
}