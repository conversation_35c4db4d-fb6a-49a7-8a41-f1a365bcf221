

/**
 * Jackaro卡牌管理器
 * 负责管理游戏中所有与卡牌相关的操作和状态
 */
class JackaroCardManger {

    /**
     * 单例相关
     */
    public static _instance: JackaroCardManger;
    public static get instance() {
        return this._instance || (this._instance = new JackaroCardManger());
    }

    /**
     * 控制是否播放翻牌动画的开关
     * true: 正常播放翻牌动画
     * false: 发牌时直接显示正面，不播放翻牌动效
     */
    public static enableCardFlipAnimation: boolean = true;

    /**
     * 界面层级相关
     */
    private cardLayerMap: Record<string, Laya.Box> = {};    // 玩家卡牌层映射
    private dealPokerPlaceholder: Laya.Box;                 // 发牌占位框
    private playCardLayer: Laya.Box;                        // 出牌层
    private shuffleSpineLayer: Laya.Box;                    // 洗牌动画层
    private replenishCardLayer: Laya.Box;                    // 牌补全管理器层     

    /**
     * 玩家手牌相关
     */
    private playerHardCard: Record<string, Array<yalla.view.jackaro.JackaroCardItem>> = {};  // 玩家手牌项
    public playerHandCardView: Record<string, JackaroHandCardView> = {};                     // 玩家手牌视图
    public selectCardList: Array<number> = [];                                               // 选中的卡牌列表
    private curSelectPoker: yalla.data.jackaro.Poker;                                        // 当前选中的牌
    private curPlayPoker: yalla.data.jackaro.Poker;                                          // 当前出的牌
    private curTouchCardItem: yalla.view.jackaro.JackaroCardItem;                            // 当前触摸的卡牌项
    private _selfCanTouchCard: boolean = false;                                              // 自己是否可以出牌
    private _isTouchActive: boolean = false;                                                 // 是否有卡牌正在被触摸
    private _activeTouchCardItem: yalla.view.jackaro.JackaroCardItem = null;                 // 当前正在触摸的卡牌
    private _cardRaisingLock: boolean = false;                                              // 卡牌抬起锁，防止多张卡牌同时抬起
    private _lastClickTime: number = 0;                                                     // 上次点击时间，用于防止快速多次点击
    private _drawRewardPoker: yalla.data.jackaro.Poker = null;                                // 抽取奖励牌

    /**
     * 出牌相关
     */
    private playCardItemList: Array<yalla.view.jackaro.JackaroCardItem> = [];                // 出牌列表
    private allowPokerData: Record<string, yalla.data.jackaro.PokerMoveDataInterface> = {};  // 允许出牌数据
    private curShowOrbitPoker: yalla.data.jackaro.Poker;                                     // 当前显示轨道的牌
    // private recordDiscardPoker: Array<yalla.data.jackaro.Poker> = [];                        // 记录弃牌
    private isCurTurnPlayPoker: boolean;                                                     // 是否当前回合已出牌
    private recordAlreadyPlayPoker: Array<yalla.data.jackaro.DiscardedPokerInterface> = [];  // 记录已经出过的牌
    private curForcedDiscardIdx: number;                                                     // 当前强制弃牌的玩家索引
    private curDrawnPokerIdx: number;                                                        // 当前抽取牌的玩家索引
    private drawnPokerIndex: number;                                                         // 抽取牌的索引
    public static novalidCardIndex: number = -1;                                             // 无效卡牌索引
    private sendPokerRound: number = 0;                                                      // 已发牌次数

    /**
     * 动画相关
     */
    private shuffleSpineAni: SpineAniPlayer;                                                // 洗牌动画
    private curTotalPokerWithAni: Array<yalla.view.jackaro.JackaroCardItem> = [];           // 记录当前正在动画中的牌
    private _isShuffleAniFirstLoad: boolean = false;                                        // 是否是第一次加载洗牌动画，用于解决第一次洗牌动画加载延迟问题

    /**
     * 规则相关
     */
    private playRuleMap;     // 出牌规则映射

    /**
     * 断线重连相关
     */
    private curPlayerOfflineHandCardData: Array<yalla.data.jackaro.Poker> = [];             // 记录自己手牌数据，用于断线重连时判断棋盘是否发生变化
    private curPickUpCardItem: yalla.view.jackaro.JackaroPickUpCardItem;                    // 当前拾取的卡牌项

    /**
     * 日志相关
     */
    private _logTag: string = "JackaroCardManger :";                                         // 日志标签

    /**
     * 日志管理方法
     */
    private log(message: string, ...params: any[]): void {
        JackaroCardLogger.instance.log('cardManager', this._logTag, message);
    }

    private logMore(message: string, ...params: any[]): void {
        // 处理参数
        if (params.length > 0) {
            JackaroCardLogger.instance.logMore('cardManager', this._logTag, message, params);
        } else {
            JackaroCardLogger.instance.logMore('cardManager', this._logTag, message);
        }
    }

    private filterLogMore(message: string, ...params: any[]): void {
        if (params.length > 0) {
            let concatenatedMessage = message;
            params.forEach(param => {
                concatenatedMessage += ' ' + (typeof param === 'object' ? JSON.stringify(param) : param);
            });
            JackaroCardLogger.instance.filterLogMore('cardManager', this._logTag, concatenatedMessage);
        } else {
            JackaroCardLogger.instance.filterLogMore('cardManager', this._logTag, message);
        }
    }
    private _user: yalla.data.jackaro.User;

    /**
     * 储存玩家的扑克牌牌背皮肤资源
     */
    private _cardBackSkin: Record<string, number> = {};

    /**
     * 发4张牌动画时间
     */

    public static totalAnimationDeal4Poker: number = 3750;

    /**
     * 发5张牌动画时间
     */

    public static totalAnimationDeal5Poker: number = 4200;
    /**
    * 发4张牌动画时间
    */

    public static totalAnimationDeal4Poker1v1: number = 2400;

    /**
     * 发5张牌动画时间
     */

    public static totalAnimationDeal5Poker1v1: number = 2650;

    /**
     * 出牌动画时间
     */
    public static totalAnimationPlayPoker: number = 400;


    /**
     * 抽牌动画时间
     */
    public static totalAnimationDrawPoker: number = 700;

    /**
     * 弃所有牌的计时器，控制弱网情况下发送弃所有牌，但是消息未及时返回，快速拖动手牌问题
     */
    private discardAllTimeout = null;

    private clientChessPreviewData: Record<string, Record<string, yalla.data.jackaro.PlayResultDataInterface> | yalla.data.jackaro.ComplexPlayKingResultInterface> = {};  // 客户端预览数据

    /**
     * 记录玩家手动出牌数据
     */
    private selfActivePlayPoker: yalla.data.jackaro.Poker = null;



    /**
     * 初始化
     */
    init() {
        // {{ YUM: [触发] - 初始化奖励 }}
        JackaroCardReplenishManager.instance.init(this);
    }
    /**
     * 自己是否可以出牌
     */
    public set selfCanTouchCard(value: boolean) {
        yalla.Debug.yum("selfCanTouchCard", value);
        this._selfCanTouchCard = value;
    }
    public get selfCanTouchCard(): boolean {
        return this._selfCanTouchCard;
    }

    public get isShuffleAniFirstLoad(): boolean { // Add this getter
        return this._isShuffleAniFirstLoad;
    }

    public registerPlayerHandCardView(idx: number) {
        if (!this.playerHandCardView[idx]) {
            this.logMore("resetPlayerHandCardView ~~~~~~~~~1111", idx, 'pos:', this.getPlayerHandCardPos(idx), 'width:', this.getPlayerHandCardWidth(idx));
            this.playerHandCardView[idx] = new JackaroHandCardView(idx, this.getPlayerHandCardPos(idx), this.getPlayerHandCardWidth(idx));
        }
    }
    //重置手牌坐标
    public resetPlayerHandCardView(idx: number) {
        this.logMore("resetPlayerHandCardView -----------------重置手牌坐标 ！！！！！！", idx);
        this.clearSelectPoker();
        TipsViewManager.getInstance().hideTip(TipViewType.RULE_CARD);
        this.resetHandUpdateCardPos(idx);
    }
    public resetHandUpdateCardPos(idx) {
        if (this.playerHandCardView[idx]) {
            this.resetHandCardView(idx);
            this.playerHandCardView[idx].updateCardPos(false);
        } else {
            this.resetHandCardView(idx);
            this.playerHandCardView[idx].updateCardPos(false);
        }
    }
    //重置手牌坐标独立方法
    public resetHandCardView(idx: number) {
        if (this.playerHandCardView[idx]) {
            this.logMore("resetHandCardView ~~~~~~~~ 已有手牌区域节点，idx：", idx, 'pos:', this.getPlayerHandCardPos(idx), 'width:', this.getPlayerHandCardWidth(idx));
            this.playerHandCardView[idx].resetHandCardPos(this.getPlayerHandCardPos(idx), this.getPlayerHandCardWidth(idx))
        } else {
            this.logMore("resetHandCardView ~~~~~~~~无手牌区域节点 重新创建 idx:", idx, 'pos:', this.getPlayerHandCardPos(idx), 'width:', this.getPlayerHandCardWidth(idx));
            this.playerHandCardView[idx] = new JackaroHandCardView(idx, this.getPlayerHandCardPos(idx), this.getPlayerHandCardWidth(idx));
            this.playerHandCardView[idx].resetHandCardPos(this.getPlayerHandCardPos(idx), this.getPlayerHandCardWidth(idx))
        }
    }
    public registerCardLayer(idx: number, cardLayer: Laya.Box) {
        this.cardLayerMap[idx] = cardLayer;
    }
    public registerPlayCardLayer(playCardLayer: Laya.Box) {
        this.playCardLayer = playCardLayer;
    }
    //
    public registerShuffleSpineLayer(shuffleSpineLayer: Laya.Box) {
        this.shuffleSpineLayer = shuffleSpineLayer;
    }

    public resetReplenishCardLayer() {
        // {{ YUM: [修改] - 遍历所有子节点，清除 JackaroCardItem 类型的组件并回收到对象池 }}
        if (!this.replenishCardLayer) {
            return;
        }
        this.logMore("resetReplenishCardLayer ~~~~~~~~~~~~~~ 重置补牌层 this.replenishCardLayer.numChildren", this.replenishCardLayer.numChildren);
        // 从后往前遍历，避免删除时索引变化的问题
        for (let i = this.replenishCardLayer.numChildren - 1; i >= 0; i--) {
            const child = this.replenishCardLayer.getChildAt(i) as any;

            // 检查是否为 JackaroCardItem 类型
            if (child && child instanceof yalla.view.jackaro.JackaroCardItem) {
                const pokerItem = child as yalla.view.jackaro.JackaroCardItem;

                // {{ YUM: [清理] - 按照参考代码进行清理和回收 }}
                this.logMore("resetReplenishCardLayer ~~~~~~~~~~~~~~ 重置补牌层 清除子节点", pokerItem);
                pokerItem.clear();
                Laya.Pool.recover('JackaroCardItem', pokerItem);
                pokerItem.removeSelf();
            }
        }
    }
    public getReplenishCardLayer(): Laya.Box {
        return this.replenishCardLayer;
    }
    public registerReplenishCardLayer(replenishCardLayer: Laya.Box) {
        this.replenishCardLayer = replenishCardLayer;
    }
    public resetReplenishCardLayerPos() {
        this.replenishCardLayer = this.replenishCardLayer
    }
    public getCardLayer(idx: number): Laya.Box {
        return this.cardLayerMap[idx];
    }
    public registerDealPokerPlaceholder(placeholder: Laya.Box) {
        this.dealPokerPlaceholder = placeholder;
    }
    public getDealPokerPlaceholderGPos(): Laya.Point {
        return this.dealPokerPlaceholder && this.dealPokerPlaceholder.localToGlobal(new Laya.Point(0, 0));
    }
    public resetDealPokerPlaceholderGPos() {
        this.logMore("resetDealPokerPlaceholderGPos11111111111");
        this.dealPokerPlaceholder = this.dealPokerPlaceholder;
    }

    public getPlayerHandCardPos(idx: number): Laya.Point {//这里可以每局开始的时候记录一下 不用每次都计算
        let player = JackaroGamePlayerManager.instance.getGamePlayerByIdx(idx);
        return player && player.handCard_box ? player.handCard_box.localToGlobal(new Laya.Point(0, 0)) : new Laya.Point(0, 0);
    }
    public getPlayerHandCardWidth(idx: number): number {
        let player = JackaroGamePlayerManager.instance.getGamePlayerByIdx(idx);
        return player && player.handCard_box ? player.handCard_box.width : 0;
    }
    public createCardItem(idx: number, poker: yalla.data.jackaro.Poker, totalLen: number) {
        if (idx == yalla.Global.Account.idx && this.getCardItemByPoker(idx, poker)) {
            this.logMore("createCardItem is exist idx:", idx, "poker");
            return;
        } else if (idx != yalla.Global.Account.idx && this.playerHardCard[idx] && totalLen == this.playerHardCard[idx].length
        ) {
            this.logMore("other createCardItem is exist idx:", idx, "poker");
            return;
        }
        let cardItem = this.getCardItem();
        let cardData: JackaroCardDataInterface = {
            poker: poker,
            idx: idx,
            showState: yalla.view.jackaro.JackaroCardItemState.IN_HAND
        }
        if (idx == yalla.Global.Account.idx) {
            this.logMore("createCardItem idx == yalla.Global.Account.idx", idx, yalla.data.jackaro.PokerChinesDefine[poker]);
        }
        let skinId = this.getCardBackSkin(idx);
        cardItem.setSkinId(skinId);
        cardItem.setCardData(cardData);
        // if (idx == yalla.Global.Account.idx) {
        // yalla.Debug.logMore("玩家自己创建手牌：", idx, yalla.data.jackaro.PokerChinesDefine[poker]);
        // }
        this.cardLayerMap[idx].addChild(cardItem);
        this.playerHandCardView[idx].addCardItem(cardItem);
        if (!this.playerHardCard[idx]) {
            this.playerHardCard[idx] = [];
        }
        let pos = this.playerHandCardView[idx].getDealPokerInitPos(cardItem.getCardWidth(), totalLen, this.playerHardCard[idx].length);
        cardItem.x = pos.x;
        cardItem.y = pos.y;
        cardItem.setInitPos(pos.x, pos.y);
        cardItem.setBackBgVisible(idx != yalla.Global.Account.idx, idx == yalla.Global.Account.idx);
        this.playerHardCard[idx].push(cardItem);
        return cardItem;
    }
    public printPlayerSelfHandCard(logFrex?: string) {
        // let tempLogFrex = logFrex ? logFrex : "";
        // if (this.cardLayerMap[yalla.Global.Account.idx]) {
        //     for (let i = 0; i < this.cardLayerMap[yalla.Global.Account.idx].numChildren; i++) {
        //         let item = this.cardLayerMap[yalla.Global.Account.idx].getChildAt(i) as yalla.view.jackaro.JackaroCardItem;
        //         if (item && item.data && item.data.poker) {
        //             yalla.Debug.logMore(tempLogFrex + "cardLayerMap 玩家当前手牌:", item.data.poker, yalla.data.jackaro.PokerChinesDefine[item.data.poker]);
        //         } else {
        //             yalla.Debug.logMore(tempLogFrex + "cardLayerMap 玩家当前手牌为空");
        //         }
        //     }
        // }
        // if (this.playerHardCard[yalla.Global.Account.idx]) {
        //     for (let index = 0; index < this.playerHardCard[yalla.Global.Account.idx].length; index++) {
        //         const item = this.playerHardCard[yalla.Global.Account.idx][index];
        //         if (item && item.data && item.data.poker) {
        //             yalla.Debug.logMore(tempLogFrex + "playerHardCard 玩家当前手牌:", item.data.poker, yalla.data.jackaro.PokerChinesDefine[item.data.poker]);
        //         }
        //     }
        // }
    }
    /**
     * 设置玩家牌背扑克牌皮肤id
     * @param idx
     */
    public setCardBackSkin(idx: number, skinId: number) {
        if (!this._cardBackSkin[idx]) {
            this._cardBackSkin[idx] = skinId;
        }
    }

    /**
     * 获取玩家牌背扑克牌皮肤id
     * @param idx
     */
    public getCardBackSkin(idx: number): number {
        if (this._cardBackSkin[idx]) {
            return this._cardBackSkin[idx];
        }
        return yalla.view.jackaro.JackaroCardItem.Default_Skin_Id;
    }

    /**
     * 用于发牌，先把自己添加到自己的牌层中，再添加到自己的手牌视图中
     * @param idx 
     * @param poker 
     * @returns 
     */
    public addHandCardItem(idx: number, poker: yalla.data.jackaro.Poker): yalla.view.jackaro.JackaroCardItem {
        if (!idx) {
            return null;
        }
        let cardItem = this.getCardItem();
        let cardData: JackaroCardDataInterface = {
            poker: poker,
            idx: idx,
            showState: yalla.view.jackaro.JackaroCardItemState.IN_HAND
        }
        // 在发牌初始阶段，仅为自己设置牌背皮肤；其他玩家统一使用默认牌背
        // 刷新到各玩家自定义牌背将在发牌结束后统一处理 {{ YUM: [修改] - 发牌阶段只为本家设置牌背皮肤 }}
        let curCardBackSkinId = this.getCardBackSkin(idx);
        if (idx == yalla.Global.Account.idx && curCardBackSkinId) {
            cardItem.setSkinId(curCardBackSkinId);
        }
        cardItem.setCardData(cardData);
        // 设置初始不可见
        cardItem.visible = false;

        if (this.cardLayerMap[idx]) {
            this.cardLayerMap[idx].addChild(cardItem);
        } else {
            this.logMore("JackaroCardManger.dealPoker:cardLayerMap[idx] is null", idx);
        }
        this.playerHandCardView[idx].addCardItem(cardItem);
        return cardItem;
    }
    /**
     * 发牌操作
     * @param idx 玩家索引
     * @param cardItem 卡牌项
     * @param totalLen 总长度
     * @param idx2 索引2
     * @param isFinalPoker 是否为最后一张牌
     */
    public dealPoker(idx: number, cardItem: yalla.view.jackaro.JackaroCardItem, totalLen: number, idx2: number, isFinalPoker: boolean = false, noDelayStart: boolean = false) {
        if (!cardItem) {
            this.logMore("dealPoker is null", idx);
            return;
        }
        if (idx == yalla.Global.Account.idx) {
            let playerShowInfoInterface: yalla.data.jackaro.PlayerShowInfoInterface = JackaroGamePlayerManager.instance.getPlayerByIdx(idx);
            if (playerShowInfoInterface) {
                this.logMore("玩家发牌", idx, playerShowInfoInterface.nickName, yalla.data.jackaro.PokerChinesDefine[cardItem.data.poker]);
            } else {
                this.logMore("JackaroCardManger.dealPoker:idx == yalla.Global.Account.idx", idx, yalla.data.jackaro.PokerChinesDefine[cardItem.data.poker]);
            }
        }
        this.playerHandCardView[idx].dealPoker(cardItem, totalLen, idx2, isFinalPoker, noDelayStart);
        if (!this.playerHardCard[idx]) {
            this.playerHardCard[idx] = [];
        }
        this.playerHardCard[idx].push(cardItem);
    }
    /**
     * 播放洗牌动画
     */
    private initializeShuffleSpineAni() {
        // 初始化洗牌动画对象并添加到播放层
        this.shuffleSpineAni = new SpineAniPlayer(yalla.getSkeleton("jackaro/xipai/xipai"));
        this.shuffleSpineLayer.addChild(this.shuffleSpineAni);
        this.updateShuffleSpineAniPosition();
        // this._isShuffleAniFirstLoad = true; // Set flag to true here
    }
    /**
     * 更新洗牌动画的位置
     */
    private updateShuffleSpineAniPosition() {
        // 重置并更新洗牌动画的位置
        if (this.shuffleSpineAni) {
            this.resetDealPokerPlaceholderGPos();
            let startPos = this.getDealPokerPlaceholderGPos();
            this.logMore("updateShuffleSpineAniPosition startPos", startPos.x, startPos.y);
            this.shuffleSpineAni.pos(startPos.x, startPos.y);
        }
    }
    /**
     * 播放洗牌动画
     */
    public playShuffleAnimation(callBack: Function) {
        // this._isShuffleAniFirstLoad = false; // Reset flag here
        // 记录日志信息
        if (!this.shuffleSpineAni) {
            // 如果洗牌动画对象不存在，则初始化
            this.initializeShuffleSpineAni();
        }

        // 设置动画可见并更新位置

        this.shuffleSpineAni.visible = true;
        this.updateShuffleSpineAniPosition();
        // 设置动画缩放和速度
        this.shuffleSpineAni.scale(0.86, 0.86);
        this.shuffleSpineAni.setSpeed(1);
        // 播放动画并在结束时调用回调函数
        this.logMore("playShuffleAnimation begin");
        this.shuffleSpineAni.play("", false, callBack);
    }
    /**
     * 重置洗牌动画
     */
    public resetShuffleSpineAni() {
        if (this.shuffleSpineAni) {
            // 如果洗牌动画对象存在，则更新其位置
            this.updateShuffleSpineAniPosition();
        }
    }

    public discardAllCard() {
        this.logMore("JackaroCardManger.discardAllCard");
        let allPoker = [];
        this.playerHardCard[yalla.Global.Account.idx].forEach(item => {
            allPoker.push(item.data.poker);
        });

        //TODO::弱网情况下，触发了所有弃牌，但是服务端没有返回，或者延迟返回，自己的手牌是不能操作的
        this.clearDiscardTimeOut();
        this.selfCanTouchCard = false;
        this.log("==发送清理所有手牌==");
        this.discardAllTimeout = setTimeout(() => {
            this.selfCanTouchCard = true;
            this.log("==清理所有手牌=延时1s重置selfCanTouchCard=");
        }, 1000);
        yalla.data.jackaro.JackaroUserService.instance.sendDiscardPoker(allPoker, true);
    }
    private clearDiscardTimeOut() {
        if (this.discardAllTimeout) {
            clearTimeout(this.discardAllTimeout);
            this.discardAllTimeout = 0;
        }
    }
    public removeHandCardItem(idx: number, cardItem: yalla.view.jackaro.JackaroCardItem) {
        if (this.playerHardCard[idx]) {
            this.playerHardCard[idx].splice(this.playerHardCard[idx].indexOf(cardItem), 1);
        }
    }
    public getHandCardItemNum(idx: number): number {
        if (this.playerHardCard[idx]) {
            return this.playerHardCard[idx].length;
        }
        return 0;
    }
    /**
     * 每一个大回合清理
     */
    public turnClear() {
        this.logMore("JackaroCardManger.turnClear");
        //清理中间牌堆
        this.clearDiscardPoker();
        // this.recordDiscardPoker = [];
        this.curPlayPoker = null;
        this.isCurTurnPlayPoker = false;
        TipsViewManager.getInstance().hideTip(TipViewType.RULE_CARD);
        this.recordAlreadyPlayPoker = [];
        this.selfActivePlayPoker = null;
    }
    /**
     * 清理弃牌堆
     */
    public clearDiscardPoker() {

        this.logMore("yum clearDiscardPoker this.playCardItemList.length", this.playCardItemList.length);
        //清理中间牌堆
        for (let idx in this.playCardItemList) {
            this.playCardItemList[idx].clear();
            Laya.Pool.recover('JackaroCardItem', this.playCardItemList[idx]);
        }
        //注意是否有其他node 常驻的添加在这里
        this.playCardLayer.removeChildren();
        this.playCardItemList = [];
    }
    /**
     * 重置弃牌堆（包含洗牌动效）
     */
    public resetDiscardPoker(isResetShuffle: boolean = false) {
        this.resetDealPokerPlaceholderGPos();
        if (isResetShuffle) {
            this.resetShuffleSpineAni();
        }

        var endPos = this.getDealPokerPlaceholderGPos();
        if (!endPos) {
            this.logMore("resetDiscardPoker endPos is null");
            return;
        }
        this.logMore("yum resetDiscardPoker this.playCardItemList.length", this.playCardItemList.length, "isResetShuffle", isResetShuffle);
        this.playCardItemList.forEach(item => {
            this.logMore("yum resetDiscardPoker item", item.data.poker, item.x, item.y, 'endPos', endPos.x, endPos.y);
            let rotation = this.generateRandomRotation();
            let scale = yalla.view.jackaro.JackaroCardItem.pushScale;
            let goal = { x: endPos.x, y: endPos.y, scaleX: scale, scaleY: scale };
            item.anchorX = item.anchorY = 0.5;
            item.x = goal.x;
            item.y = goal.y;
            item.scaleX = goal.scaleX;
            item.scaleY = goal.scaleY;
            // 重置牌背背景和奖励背景 针对抽牌直接打出后的问题 1.4.5
            item.setBackBgVisible(false);
            item.setBgRewardVisible(false);
        });
    }


    /**
     * 重置检查弃牌堆最后一张牌（最后出的手牌）
     */
    public resetCheckDiscardPoker(msg) {
        let element: yalla.view.jackaro.JackaroCardItem = this.playCardLayer._childs[this.playCardLayer.numChildren - 1];
        if (element && JackaroGamePlayerManager.instance.getCurOperatePlayerIdx() != yalla.Global.Account.idx) {
            if (msg.chessPanel && msg.chessPanel.currentPoker && element.data) {
                let curPoker = msg.chessPanel.currentPoker;
                if (this.isComplexPlay(curPoker) && curPoker == yalla.data.jackaro.Poker.HEART_QUEEN || curPoker == yalla.data.jackaro.Poker.DIAMOND_QUEEN) {
                    return;
                }
                if (curPoker != element.data.poker) {
                    let cardData: JackaroCardDataInterface = {
                        poker: curPoker,
                        idx: 0,
                        showState: yalla.view.jackaro.JackaroCardItemState.IN_HAND
                    }
                    element.data = cardData;

                    let nextElement: yalla.view.jackaro.JackaroCardItem = this.playCardLayer._childs[this.playCardLayer.numChildren - 2];
                    if (nextElement && nextElement.data.poker == curPoker) {
                        element.setState(nextElement.operatorState);
                        nextElement.clear();
                        Laya.Pool.recover('JackaroCardItem', nextElement);
                        nextElement.removeSelf();
                    }
                    element.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_PLAY);
                    // {{ YUM: [新增] - 清除卡牌效果提示，防止弃牌堆显示效果信息 }}
                    element.setEffectInfo(yalla.view.jackaro.CardEffectType.NONE);
                }
            }
        }
    }
    public changePlayerClear() {
        if (JackaroGamePlayerManager.instance.isMyActive())
            this.selectCardList = [];
        this.curPlayPoker = null;
        this.curSelectPoker = null;
        this.selfActivePlayPoker = null;
        this._drawRewardPoker = null;
        if (JackaroGamePlayerManager.instance.getCurOperatePlayerIdx() != yalla.Global.Account.idx) {
            this.resetAllHandPokerState(yalla.Global.Account.idx, yalla.view.jackaro._operatorState.CAN_PLAY);
            // {{ YUM: [Update] - 使用新的管理类清除手牌效果提示 }}
            JackaroCardEffectHintManager.instance.clearAllCardEffects();
        }
        //操作者轮到自己，才清理自己的手牌向上选中效果
        if (JackaroGamePlayerManager.instance.getCurOperatePlayerIdx() == yalla.Global.Account.idx) {
            this.resetCardUpState(yalla.Global.Account.idx);
            JackaroCardManger.instance.refreshChessOrbit();
        }
        this.isCurTurnPlayPoker = false;
    }
    public resetClientChessPreviewData() {
        this.clientChessPreviewData = {};
    }
    /**
     * 清除选中的扑克牌
     * @param poker 要清除的扑克牌，为null时清除所有选中状态
     */
    public clearSelectPoker(poker: yalla.data.jackaro.Poker = null) {
        if (poker === null) {
            this.logMore("JackaroCardManger.clearSelectPoker:poker is null");
            // 如果传入的poker为null，清空所有选中牌
            this.selectCardList = [];
            if (this.curSelectPoker) {
                //发送给服务器 取消选牌
            }
            this.curSelectPoker = null;
        } else {
            // 否则只移除指定的poker
            const index = this.selectCardList.indexOf(poker);
            if (index > -1) {
                this.selectCardList.splice(index, 1);
                this.logMore("JackaroCardManger.clearSelectPoker: this.selectCardList", this.selectCardList);
            }
        }
    }
    /**
     * 重置卡牌上升状态
     * @param idx 玩家索引
     * @param exclude 要排除的卡牌项
     */
    public resetCardUpState(idx: number, exclude?: yalla.view.jackaro.JackaroCardItem) {

        if (idx !== yalla.Global.Account.idx) {
            return;
        }
        if (!this.playerHardCard[idx]) {
            return;
        }
        this.log("resetCardUpState 重置牌的选中向上状态");
        this.playerHardCard[idx].forEach(item => {
            if (item === exclude) {
                return;
            }
            item.downCard();
            // {{ YUM: [新增] - 重置卡牌选中状态时清除对应的预览数据 }}
            if (this.curShowOrbitPoker === item.data?.poker) {
                this.setCurShowOrbitPoker(null);
            }
        });
        
      
    }
    /**
     * 
     */
    offLineClear() {
        this.isCurTurnPlayPoker = false;
        // 确保在离线状态下清理所有触摸状态
        this.clearAllState();
    }
    public clear() {
        this.logMore("######  JackaroCardManger.clear  #######");
        this.clearAllHandPoker();
        for (let idx in this.playerHandCardView) {
            this.playerHandCardView[idx].clear();
        }
        for (let idx in this.cardLayerMap) {
            this.cardLayerMap[idx].removeChildren();
        }
        for (let idx in this.playerHardCard) {
            this.playerHardCard[idx].forEach(item => {
                item.clear();
            });
            this.playerHardCard[idx] = [];
        }
        if (this.playCardLayer) {
            this.playCardLayer.removeChildren();
        }
        Laya.Pool.clearBySign('JackaroCardItem');
        Laya.Pool.clearBySign('JackaroPickUpCardItem');
        if (this.shuffleSpineAni) {
            this.shuffleSpineAni.clear();
            this.shuffleSpineAni = null;
        }

        this.isCurTurnPlayPoker = false;
        this.setCurTouchCardItem(null);
        this.curTotalPokerWithAni = [];
        this.selfCanTouchCard = false;
        this.clearAllState(); // 使用新方法清理所有状态
        JackaroCardManger._instance = null;
    }

    public getCardItem() {
        var cardItem;
        cardItem = Laya.Pool.getItemByClass('JackaroCardItem', yalla.view.jackaro.JackaroCardItem);
        // AURA-X: Modify - Ensure pooled card items are fully reset before reuse，防止复用对象残留选中/蒙版等状态
        if (cardItem) {
            // 统一重置状态，避免出现发牌后非自己回合仍高亮/未置灰等问题  Source: Debug analysis of highlight issue
            cardItem.clear();
        }
        return cardItem;
    }
    public isCanNextPlayerDiscard() {
        let poker = this.curPlayPoker;
        return this.allowPokerData[poker] && this.allowPokerData[poker].pokerTenDiscard;
    }
    public isCanTakeOff() {
        let poker = this.curPlayPoker;
        return this.allowPokerData[poker] && this.allowPokerData[poker].complexKing && this.allowPokerData[poker].complexKing.flyData;
    }
    public isCurPlayPoker() {
        return this.isCurTurnPlayPoker;
    }
    public getCurSelectPoker() {
        return this.curSelectPoker;
    }
    /**
     * 设置当前选中的扑克牌
     * @param poker 要选中的扑克牌
     */
    public setCurSelectPoker(poker: yalla.data.jackaro.Poker) {
        this.curSelectPoker = poker;
        if (!this.curSelectPoker) {
            return;
        }
        this.logMore("setCurSelectPoker curSelectPoker:", this.curSelectPoker);
    }
    /**
     * 牌点击和出牌处理
     */
    public handleClickPoker(isDouble: boolean, poker: yalla.data.jackaro.Poker): boolean {
        this.curShowOrbitPoker = !isDouble ? poker : null;
        this.log("handleClickPoker 发送出牌流程 " +
            "isDouble:" + isDouble +
            " poker:" + yalla.data.jackaro.PokerChinesDefine[poker] +
            " poker:" + poker +
            " yalla.Global.isFouce:" + yalla.Global.isFouce +
            " this.curPlayPoker:" + this.curPlayPoker +
            " this.curSelectPoker:" + this.curSelectPoker +
            " this.isCurTurnPlayPoker:" + this.isCurTurnPlayPoker +
            " this.getCardItemByPoker(yalla.Global.Account.idx, this.curSelectPoker):" + this.getCardItemByPoker(yalla.Global.Account.idx, this.curSelectPoker) +
            " !allowPokerData && !JackaroGamePlayerManager.instance.getPlayerCanDiscard(yalla.Global.Account.idx):" + (!this.allowPokerData[poker] && !JackaroGamePlayerManager.instance.getPlayerCanDiscard(yalla.Global.Account.idx)) +
            " this.curSelectPoker != poker:" + (this.curSelectPoker != poker));

        if (!JackaroGamePlayerManager.instance.isMyActive() && !isDouble) {
            //非自己回合点击牌，展示客户端预览棋子
            this.refreshChessOrbit();
        }
        if (this.isCurTurnPlayPoker) {
            this.log("handleClickPoker 已出牌，不能再点击牌 this.isCurTurnPlayPoker:", this.isCurTurnPlayPoker);
            return isDouble ? false : true;
        }
        //1.4.5 针对出牌后 闪断重连 还能出牌的问题 根据服务端的状态判断
        if (this.isPlayerInOperateEndState()) {
            this.log("handleClickPoker 已出牌，不能再点击牌,状态不对");
            return isDouble ? false : true;
        }
        if (TipsViewManager.getInstance().isShow(TipViewType.CHOOSE_CHESS_OPERATE)) {
            this.log("handleClickPoker 已选择棋子，不能再点击牌");
            return isDouble ? false : true
        }

        let allowPokerData = this.allowPokerData[poker];
        if (!allowPokerData && !JackaroGamePlayerManager.instance.getPlayerCanDiscard(yalla.Global.Account.idx)) {
            this.log("handleClickPoker 牌不允许出牌");
            return isDouble ? false : true;
        }
        if (isDouble && this.curPlayPoker == poker) {
            this.log("handleClickPoker 双击相同牌，不能出牌");
            return isDouble ? false : true;
        }
        if (JackaroGamePlayerManager.instance.isMyActive() && allowPokerData && allowPokerData.showPreResult) {
            this.showChessOrbit(poker);
        }
        // if (this.curSelectPoker != poker) {
        yalla.data.jackaro.JackaroUserService.instance.sendSelectPoker(poker);
        // }
        this.curSelectPoker = poker;
        if (isDouble) {
            this.logMore("handleClickPoker  设置isCurTurnPlayPoker 双击牌", poker);
            this.isCurTurnPlayPoker = true;
            this.log("handleClickPoker 发送出牌流程 成功： 进度1/3 poker:", poker);
            JackaroChessManager.instance.resetCurTurnAlreadyMoveChessInfoes();
            this.log("handleClickPoker 发送出牌流程 成功： 进度2/3 poker:", poker);
            let isCanDisCard = JackaroGamePlayerManager.instance.getPlayerCanDiscard(yalla.Global.Account.idx);
            this.log("handleClickPoker 发送出牌流程 成功： 进度3/3 poker:", poker);
            this.selfActivePlayPoker = poker;
            if (isCanDisCard) {
                this.log("JandleClickPoker 发送出牌流程 成功： 弃牌");
                //1.4.5 触发弃牌时，后续无法加时
                yalla.data.jackaro.JackaroUserService.instance.setIsTriggerPlayCard(true);
                // 客户端先展示弃牌效果，然后再发送消息给服务端,第4个参数表示是无效牌，需要置灰显示
                let discardPokerSuc = this.handleDiscardPoker(yalla.Global.Account.idx, { poker: poker }, JackaroCardManger.novalidCardIndex, true, () => {
                    // 完成客户端展示后，发送弃牌消息给服务端
                    yalla.data.jackaro.JackaroUserService.instance.sendDiscardPoker([poker]);
                });
                if (discardPokerSuc) {
                    this.setCurPlayPoker(poker);
                }
            } else {
                this.log("JandleClickPoker 发送出牌流程 成功： 出牌");
                this.doPlayPoker(poker);
            }
        }
        return true;
    }
    // 提取为独立的状态检查方法   1.4.5 针对出牌后 闪断重连 还能出牌的问题 根据服务端的状态判断
    private isPlayerInOperateEndState(): boolean {
        if (!yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode) {
            return false;
        }

        const currentPlayerView = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);

        return currentPlayerView?.playerStatus === yalla.data.jackaro.PlayerStatus.OPERATE_END;
    }


    public setCurPlayPoker(poker: yalla.data.jackaro.Poker) {
        this.curPlayPoker = poker;
    }

    /**
     * 执行出牌操作 玩家自出牌的时候调用 其它几处调用是恢复出牌调用
     * @param poker 要出的扑克牌
     * @param isRestore 是否恢复模式 只有恢复的时候部分牌型会试试true
     */
    public doPlayPoker(poker: yalla.data.jackaro.Poker, isRestore: boolean = false) {
        this.log("doPlayPoker poker:" + poker + yalla.data.jackaro.PokerChinesDefine[poker] + " isRestore:" + isRestore);
        this.curPlayPoker = poker;
        let allowPokerData = this.allowPokerData[poker];
        if (!allowPokerData) {
            this.logMore("doPlayPoker allowPokerData is null", poker);
            return;
        }
        let isPoker10 = this.isPoker10(poker);
        yalla.data.jackaro.JackaroUserService.instance.sendSelectPoker(poker, true);
        let commonResult = allowPokerData.commonResult;
        let jackResult = allowPokerData.jackResult;
        let complexKing = allowPokerData.complexKing;//复杂玩法出牌 k 数据
        JackaroChessManager.instance.setImpactedChessTotalData({});
        if (commonResult && !allowPokerData.pokerTenDiscard && !allowPokerData.waitDrawnPokerCount) {
            let playResultData = commonResult.playResultData as Record<string, yalla.data.jackaro.PlayResultDataInterface>;
            JackaroChessManager.instance.setCommonResult(playResultData);
            let playPokerParams = this.getPlayPokerParams(poker);
            if (playPokerParams) {
                //只有单个棋子可以走，前端自行先走子，然后发送给服务器
                if (!isRestore) {
                    JackaroCardManger.instance.handlePlayPoker(yalla.Global.Account.idx, this.curPlayPoker, false, true, () => {
                        JackaroChessManager.instance.handleMoveChessBySelf(playResultData[playPokerParams.number].result[0], playPokerParams.number);
                        if (this.isPoker7(poker)) {
                            yalla.data.jackaro.JackaroUserService.instance.sendFreeMoveChess(poker, { operatedChess: 0, firstData: playPokerParams });
                        } else {
                            yalla.data.jackaro.JackaroUserService.instance.sendPlayPoker(poker, playPokerParams);
                        }
                    });
                } else {
                    JackaroChessManager.instance.handleMoveChessBySelf(playResultData[playPokerParams.number].result[0], playPokerParams.number);
                    if (this.isPoker7(poker)) {
                        yalla.data.jackaro.JackaroUserService.instance.sendFreeMoveChess(poker, { operatedChess: 0, firstData: playPokerParams });
                    } else {
                        yalla.data.jackaro.JackaroUserService.instance.sendPlayPoker(poker, playPokerParams);
                    }
                }
            } else {
                if (!isRestore) {
                    JackaroCardManger.instance.handlePlayPoker(yalla.Global.Account.idx, this.curPlayPoker, false, true, () => {
                        //处理多个步骤可以走子时，包括多个棋子起飞
                        JackaroChessManager.instance.handlePlayResult(playResultData, poker, allowPokerData.pokerTenDiscard, true, false);
                    });
                } else {
                    //直接处理多个步骤可以走子时，包括多个棋子起飞
                    JackaroChessManager.instance.handlePlayResult(playResultData, poker, allowPokerData.pokerTenDiscard, true, true);
                }
            }
        } else if (jackResult) {
            //先出牌然后再弹交换棋子界面
            if (!isRestore) {
                JackaroCardManger.instance.handlePlayPoker(yalla.Global.Account.idx, this.curPlayPoker, false, true, () => {
                    JackaroChessManager.instance.handlePlayJackResult(jackResult);
                });
            } else {
                JackaroChessManager.instance.handlePlayJackResult(jackResult);
            }
        } else if (isPoker10) {
            if (!isRestore) {
                JackaroCardManger.instance.handlePlayPoker(yalla.Global.Account.idx, this.curPlayPoker, false, true, () => {
                    this.logMore("isPoker10 --->>>", allowPokerData);
                    if (allowPokerData.pokerTenDiscard) {
                        if (commonResult && commonResult.playResultData) {
                            JackaroChessManager.instance.setCommonResult(commonResult.playResultData);
                            JackaroChessManager.instance.createMutilOperateSteps(commonResult.playResultData as Record<string, yalla.data.jackaro.PlayResultDataInterface>, poker, allowPokerData.pokerTenDiscard);
                        } else {
                            yalla.data.jackaro.JackaroUserService.instance.sendAssignNextPlayerDiscard(JackaroCardManger.instance.getCurPlayPoker());
                        }
                    }
                });
            } else {
                if (allowPokerData.pokerTenDiscard) {
                    if (commonResult && commonResult.playResultData) {
                        JackaroChessManager.instance.setCommonResult(commonResult.playResultData);
                    } else {
                        yalla.data.jackaro.JackaroUserService.instance.sendAssignNextPlayerDiscard(JackaroCardManger.instance.getCurPlayPoker());
                    }
                }
            }
        } else if (complexKing) {
            let complexKingData = complexKing as yalla.data.jackaro.ComplexPlayKingResultInterface;
            let impactResult = complexKingData.impactResult;
            this.logMore("impactResult", impactResult);
            if (impactResult) {
                JackaroChessManager.instance.setImpactResult(impactResult);
                let impactedChessTotalData: Record<string, Array<yalla.data.jackaro.ImpactedChessDataInterface>> = {};
                for (let index = 0; index < impactResult.length; index++) {
                    const element = impactResult[index];
                    let fromGrid = element.fromGrid;
                    if (fromGrid && fromGrid.chess) {
                        let moveChessInfo = fromGrid.chess.idx + "_" + fromGrid.chess.order;
                        impactedChessTotalData[moveChessInfo] = element.impactedChess;
                    }
                }
                JackaroChessManager.instance.setCurPlayPokerKMoveChessPlayer(yalla.Global.Account.idx);
                JackaroChessManager.instance.setImpactedChessTotalData(impactedChessTotalData);
                if (!isRestore) {
                    JackaroCardManger.instance.handlePlayPoker(yalla.Global.Account.idx, poker, false, true, () => {
                        JackaroChessManager.instance.handlerPokerKPlayPoker(complexKingData, poker, isRestore);
                    });
                } else {
                    JackaroChessManager.instance.handlerPokerKPlayPoker(complexKingData, poker, isRestore);
                }
            }

        } else if (allowPokerData.waitDrawnPokerCount > 0) {
            if (allowPokerData.waitDrawnPokerCount == 1) {
                JackaroCardManger.instance.handlePlayPoker(yalla.Global.Account.idx, poker, false, true, () => {
                    yalla.data.jackaro.JackaroUserService.instance.sendDrawNextPlayerPoker(poker, 0);
                });
            } else {
                JackaroCardManger.instance.handlePlayPoker(yalla.Global.Account.idx, poker, false, true, () => {
                    //1.4.5 触发抽取下家一张牌时，后续无法加时
                    TipsViewManager.getInstance().showTip(TipViewType.PICKUP_CARD, { poker: poker, waitDrawnPokerCount: allowPokerData.waitDrawnPokerCount, operateTime: JackaroGamePlayerManager.instance.getOperateTime() });
                });
            }
        }
    }

    /**
     * 出牌信息查询
     */
    public getCurPlayPoker() {
        return this.curPlayPoker;
    }

    public removeCardChessOrbit() {
        JackaroChessManager.instance.removeAllChessOrbit();
    }
    /**
     * 根据打出去的牌重置预览牌
     * @param idx 
     * @param poker 
     */
    public resetShowOrbitPokerByPlayPoker() {
        this.curShowOrbitPoker = null;
    }
    
    /**
     * {{ YUM: [新增] - 设置当前预览轨道牌 }}
     * @param poker 要设置的扑克牌，null表示清除预览
     */
    public setCurShowOrbitPoker(poker: yalla.data.jackaro.Poker | null) {
        this.curShowOrbitPoker = poker;
    }
    public showChessOrbit(poker: yalla.data.jackaro.Poker) {
        JackaroChessOrbitManager.instance.refreshChessSnapshot();
        let allowPokerData = this.allowPokerData[poker];
        if (allowPokerData) {
            if (allowPokerData.commonResult) {
                JackaroChessManager.instance.showChessOrbitByLength(poker, allowPokerData.commonResult.playResultData);
            } else if (allowPokerData.complexKing) {
                this.logMore("showChessOrbit allowPokerData.commonResult is null");
                JackaroChessManager.instance.showPokerKChessOrbit(poker, allowPokerData.complexKing);
            } else {
                JackaroChessManager.instance.removeAllChessOrbit();
            }
        }
    }
    public refreshChessOrbit(ignoreChessId?: string) {
        //如果预览的牌已经不在手里了，也不能再次预览
        //当前手牌是否是抬起状态，只有抬起状态才能刷新预览
        let curOrbitPokerItem = this.getCardItemByPoker(yalla.Global.Account.idx, this.curShowOrbitPoker);
        let isUp = false;
        let isMove = false;
        if (curOrbitPokerItem) {
            isUp = curOrbitPokerItem.isUp;
            isMove = curOrbitPokerItem.isMove;
        }
        if (this.curShowOrbitPoker && curOrbitPokerItem && (isUp||isMove)) {
            this.createClientPreviewData(ignoreChessId);
            //非自己回合点击牌，展示客户端预览棋子
            this.showClientChessOrbit(this.curShowOrbitPoker);
        } else {
            this.removeCardChessOrbit();
        }
    }
    /**
     * 显示客户端预览数据
     * @param poker 
     * @returns 
     */
    private showClientChessOrbit(poker: yalla.data.jackaro.Poker) {
        if (this.clientChessPreviewData[poker]) {
            if (JackaroCardManger.instance.isComplexPlayPokerK(poker) && this.clientChessPreviewData[poker]) {
                JackaroChessManager.instance.showPokerKChessOrbit(poker, this.clientChessPreviewData[poker] as yalla.data.jackaro.ComplexPlayKingResultInterface, 0, true);
            } else {
                JackaroChessManager.instance.showChessOrbitByLength(poker, this.clientChessPreviewData[poker] as Record<string, yalla.data.jackaro.PlayResultDataInterface>, 0, true);
            }
        }
    }

    private createClientPreviewData(ignoreChessId?: string) {
        this.clientChessPreviewData[this.curShowOrbitPoker] = JackaroChessOrbitManager.instance.setCurPlayPoker(this.curShowOrbitPoker, ignoreChessId);
    }
    /**
     * 补牌后 当前牌是否有效
     * @param poker 
     * @returns 
     */
    /**
     * 检查交换牌是否可以交换
     * @param poker 交换牌
     * @returns 是否可以交换
     */
    private isExchangePokerValid(poker: yalla.data.jackaro.Poker): boolean {
        // 获取当前玩家索引
        let selfIdx = yalla.Global.Account.idx;
        if (!selfIdx && selfIdx !== 0) {
            return false;
        }

        // 检查JackaroChessManager实例是否存在
        if (!JackaroChessManager.instance) {
            return false;
        }

        // 检查自己的棋子：如果都在基地或终点赛道，不支持交换
        let selfChesses = JackaroChessManager.instance.getChessesByPlayerIdx(selfIdx);
        if (!selfChesses || selfChesses.length === 0) {
            return false;
        }

        let selfValidChesses = selfChesses.filter(chess => {
            // 确保棋子和wayGrid存在
            if (!chess || !chess.wayGrid) {
                return false;
            }
            // 自己的棋子：不在基地且不在终点区域的才能交换
            return !chess.wayGrid.inHome && !chess.wayGrid.inEndArea;
        });

        // 如果自己没有可交换的棋子，返回false
        if (selfValidChesses.length === 0) {
            return false;
        }

        // 检查其他玩家的棋子
        let hasValidOtherChess = false;

        // 遍历所有棋子，查找其他玩家的可交换棋子
        JackaroChessManager.instance.each((chess: JackaroChess) => {
            // 确保棋子和相关属性存在
            if (!chess || !chess.wayGrid || chess.beyondIdx === undefined || chess.color === undefined) {
                return;
            }

            // 跳过自己的棋子
            if (chess.beyondIdx === selfIdx) {
                return;
            }

            // 确保wayGrid.station存在
            if (!chess.wayGrid.station) {
                return;
            }

            // 其他玩家的棋子：不能在基地、终点区域或自己的起飞点
            if (!chess.wayGrid.inHome &&
                !chess.wayGrid.inEndArea &&
                JackaroBoardData.getGridName(chess.wayGrid.station) !== (chess.color + "_2")) {
                hasValidOtherChess = true;
            }
        });

        return hasValidOtherChess;
    }
    /**
     * 是否可以抽牌
     * @returns 
     */
    private isCanPickup() {
        //获取该玩家所有手牌
        let pickUpPlayerIdx = JackaroGamePlayerManager.instance.getNextPlayerIdx();
        let playerHandPokerItems: Array<yalla.view.jackaro.JackaroCardItem> = JackaroCardManger.instance.getPlayerAllPokerItem(pickUpPlayerIdx);
        if (!playerHandPokerItems || playerHandPokerItems.length == 0) {
            return false;
        }
        return true;
    }
    public isValidPokerAfterReplenish(poker: yalla.data.jackaro.Poker) {
        //如果是交换牌，需要单独检测是否可以交换
        if (poker == yalla.data.jackaro.Poker.SPADE_JACK || poker == yalla.data.jackaro.Poker.CLUB_JACK) {
            return this.isExchangePokerValid(poker);
        }
        //如果是红色Q，需要检测下家有牌可以弃
        if (poker == yalla.data.jackaro.Poker.HEART_QUEEN || poker == yalla.data.jackaro.Poker.DIAMOND_QUEEN) {
            return this.isCanPickup();
        }

        //调用计算预览的方法，检测当前牌是否有效
        this.clientChessPreviewData[poker] = JackaroChessOrbitManager.instance.setCurPlayPoker(poker);
        if (this.clientChessPreviewData[poker] && Object.keys(this.clientChessPreviewData[poker]).length > 0) {
            return true;
        }
        return false;
    }


    public getPlayResultData(poker: yalla.data.jackaro.Poker) {
        if (this.allowPokerData&&this.allowPokerData[poker]) {
            let allowPokerData = this.allowPokerData[poker];
            if (allowPokerData && allowPokerData.commonResult) {
                return allowPokerData.commonResult.playResultData;
            }
        }
        return null;
    }
    public getAllowPokerData(poker: yalla.data.jackaro.Poker) {
        if (this.allowPokerData&&this.allowPokerData[poker]) {
            return this.allowPokerData[poker];
        }
        return null;
    }
    /**
     * 卡牌查询和操作
     */
    public getCardItemByPoker(idx: number, poker: yalla.data.jackaro.Poker) {
        if (!this.playerHardCard[idx]) {
            yalla.Debug.logMore("getCardItemByPoker playerHardCard[idx] is null", idx);
            return null;
        }
        for (const item of this.playerHardCard[idx]) {
            // yalla.Debug.logMore("getCardItemByPoker item.data.poker", item.data.poker, yalla.data.jackaro.PokerChinesDefine[item.data.poker], "poker:", poker, yalla.data.jackaro.PokerChinesDefine[poker]);
            if (item.data.poker == poker) {
                return item;
            }
        }
        return null;
    }

    /**
     * 网络断线时记录自己的手牌数据
     */
    public recordCurPlayerHandCardData() {
        if (!this.playerHardCard[yalla.Global.Account.idx]) {
            return;
        }
        this.curPlayerOfflineHandCardData = this.playerHardCard[yalla.Global.Account.idx].map(item => item.data.poker);
    }

    /**
     * 清理所有玩家的手牌
     */
    public clearAllHandPoker() {
        for (let idx in this.playerHardCard) {
            this.deleteOtherPlayerAllPokerItem(parseInt(idx));
        }
        this.clearDiscardTimeOut();
    }

    /**
     * 当前玩家可以出的手牌
     */
    public setCurrentPlayerAllowPoker(idx: number, allowPoker: Array<yalla.data.jackaro.Poker>) {
        if (idx !== yalla.Global.Account.idx) {
            return;
        }
        if (!this.playerHardCard[idx]) {
            return;
        }
        if (JackaroGamePlayerManager.instance.getPlayerCanDiscard(yalla.Global.Account.idx) || (idx == yalla.Global.Account.idx && allowPoker && allowPoker.length == 0)) {
            this.playerHardCard[idx].forEach(item => {
                item.onTouchEvent();
                item.setState(yalla.view.jackaro._operatorState.CAN_PLAY);
            });
        } else {
            this.playerHardCard[idx].forEach(item => {
                item.onTouchEvent();
                if (allowPoker && allowPoker.indexOf(item.data.poker) != -1) {
                    item.setState(yalla.view.jackaro._operatorState.CAN_PLAY);
                } else {
                    item.setState(yalla.view.jackaro._operatorState.CANNOT_PLAY);
                }
            });
        }
    }

    public resetAllHandPokerState(idx: number, state: yalla.view.jackaro._operatorState) {
        if (!this.playerHardCard[idx]) {
            this.logMore("resetAllHandPokerState playerHardCard[idx] is null", idx);
            return;
        }
        // this.logMore("resetAllHandPokerState idx:", idx, "state:", state);
        this.playerHardCard[idx].forEach(item => {
            item.setState(state);
        });
    }

    /**
     * 可出牌数据处理
     */
    public handleAllowPokerData(allowPokerData: Record<string, yalla.data.jackaro.PokerMoveDataInterface>, isForce: boolean = false) {
        this.allowPokerData = allowPokerData;
        //更新可以出的牌
        let allAllowPlayPoker = Object.keys(allowPokerData).map(item => Number(item));
        this.setCurrentPlayerAllowPoker(yalla.Global.Account.idx, allAllowPlayPoker);
        this.checkShowDiscardView(isForce);

        // {{ YUM: [Update] - 使用新的管理类检测并显示卡牌效果提示 }}
        JackaroCardEffectHintManager.instance.checkAndShowCardEffectHints(allowPokerData);
    }


    /*
    * 根据可以出的牌判断是否显示弃牌界面
    */
    public checkShowDiscardView(isForce: boolean = false) {
        // this.logMore("checkShowDiscardView:allowPokerData:", this.allowPokerData);
        if (!this.allowPokerData) {
            this.log("checkShowDiscardView: this.allowPokerData");
            return;
        }
        if (!JackaroGamePlayerManager.instance.isMyActive()) {
            this.log("checkShowDiscardView: !JackaroGamePlayerManager.instance.isMyActive()");
            return;
        }
        //主要解决当前回合已经弃牌后，如果此时托管回来，会检测是否需要弹出弃牌界面
        if (this.isCurTurnPlayPoker) {
            this.log("checkShowDiscardView: this.isCurTurnPlayPoker");
            return;
        }
        if (this.isSelfNeedShowSelfForceCard()) {
            // yalla.Debug.logMore("弃牌日志1   checkShowDiscardSelfForceCard:");
            this.checkShowDiscardSelfForceCard();
            //会出现一张场景，就是别的玩家出牌10 让自己弃牌时，此时自己都是无效牌
            this.log("如果此时正在展示出牌10 强制弃牌界面，无需展示无效牌界面");
            return;
        }
        let allAllowPlayPoker = Object.keys(this.allowPokerData).map(item => Number(item));
        let allAllowPlayPokerLength = allAllowPlayPoker.length;
        let curRound = yalla.data.jackaro.JackaroUserService.instance.room.curRound;
        //根据情况显示弃牌
        if (this.getHandCardItemNum(yalla.Global.Account.idx) == 0) {
            this.log("checkShowDiscardView: 手牌为0");
            return;
        }
        if (allAllowPlayPokerLength == 0 && (curRound == 1 || isForce)) {
            JackaroGamePlayerManager.instance.setForcedDiscard(true);
            TipsViewManager.getInstance().showTip(TipViewType.DISCARD_ALL_CARD, {
                tip: "No valid cards",
                isForceDiscard: false,
                operateTime: JackaroGamePlayerManager.instance.getOperateTime()
            });
        } else if (allAllowPlayPokerLength == 0 && curRound > 1) {
            JackaroGamePlayerManager.instance.setForcedDiscard(true);
            if (TipsViewManager.getInstance().isShow(TipViewType.Discard_Self_ForceCard)) return;
            if (this.getHandCardItemNum(yalla.Global.Account.idx) == 1) {
                TipsViewManager.getInstance().showTip(TipViewType.DISCARD_ALL_CARD, {
                    tip: "No valid cards",
                    isForceDiscard: false,
                    operateTime: JackaroGamePlayerManager.instance.getOperateTime()
                });
            } else {
                TipsViewManager.getInstance().showTip(TipViewType.DISCARD_CARD, {
                    tip: "No valid cards",
                    isForceDiscard: false,
                    operateTime: JackaroGamePlayerManager.instance.getOperateTime()
                });
            }
        }
    }
    /**
     * 是否自己需要展示强制弃牌界面（被别人出牌10时）
     * @returns 
     */
    public isSelfNeedShowSelfForceCard() {
        let curOperatePlayerIdx = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx();
        let curForcedDiscardIdx = this.getCurForcedDiscardIdx();
        this.log("checkShowDiscardSelfForceCard:curForcedDiscardIdx:" + curForcedDiscardIdx + " curOperatePlayerIdx:" + curOperatePlayerIdx + " yalla.Global.Account.idx:" + yalla.Global.Account.idx);
        //如果当前操作玩家是自己，轮到当前回合需要自己弃牌，需要弹出强制弃牌界面
        if (curForcedDiscardIdx == yalla.Global.Account.idx && curOperatePlayerIdx == yalla.Global.Account.idx) {
            return true;
        }
        return false;
    }
    /**
     * 检查是否显示别人出牌10，让自己强制弃牌
     */
    public checkShowDiscardSelfForceCard() {
        //如果当前操作玩家是自己，轮到当前回合需要自己弃牌，需要弹出强制弃牌界面
        if (this.isSelfNeedShowSelfForceCard()) {

            TipsViewManager.getInstance().hideTip(TipViewType.DISCARD_CARD);
            TipsViewManager.getInstance().hideTip(TipViewType.DISCARD_ALL_CARD);

            JackaroGamePlayerManager.instance.setForcedDiscard(true);
            if (TipsViewManager.getInstance().isShow(TipViewType.Discard_Self_ForceCard)) {
                this.log("checkShowDiscardSelfForceCard:TipsViewManager.getInstance().isShow(TipViewType.Discard_Self_ForceCard)");
                return;
            }
            // yalla.Debug.logMore("弃牌日志3   checkShowDiscardSelfForceCard:");
            TipsViewManager.getInstance().showTip(TipViewType.Discard_Self_ForceCard, {
                tip: " Please discard a card.",
                isForceDiscard: true,
                operateTime: JackaroGamePlayerManager.instance.getOperateTime()
            });
            // this.resetCurForcedDiscardIdx();
        }
    }
    // public setRecordDiscardPoker(poker: Array<yalla.data.jackaro.Poker>) {
    //     this.recordDiscardPoker = poker;
    // }

    // public getRecordDiscardPoker() {
    //     return this.recordDiscardPoker;
    // }

    /**
     * 出牌数据查询
     */
    public getPlayPokerParams(poker: yalla.data.jackaro.Poker) {
        if (this.allowPokerData[poker]) {
            let allowPokerData = this.allowPokerData[poker];
            let commonResult = allowPokerData.commonResult;
            if (!commonResult || !commonResult.playResultData) {
                return null;
            }
            let playResultData = commonResult.playResultData as Record<string, yalla.data.jackaro.PlayResultDataInterface>;
            let onlyHasOneMoveData = Object.keys(playResultData).length == 1 && playResultData[Object.keys(playResultData)[0]].result.length == 1;
            if (onlyHasOneMoveData) {
                let curMoveStep = Object.keys(playResultData)[0];
                let order = playResultData[curMoveStep].result[0].fromGrid.chess.order;
                return { idx: playResultData[curMoveStep].result[0].fromGrid.chess.idx, order: order, number: parseInt(curMoveStep) };
            } else {
                return null;
            }
        }
        return null;
    }

    public getPlayPokerMoveStep(poker: yalla.data.jackaro.Poker, chessId: number): number {
        if (this.allowPokerData[poker]) {
            let allowPokerData = this.allowPokerData[poker];
            let commonResult = allowPokerData.commonResult;
            let complexKingData = allowPokerData.complexKing;
            if (commonResult && commonResult.playResultData) {
                let commonResultRecord = commonResult.playResultData as Record<string, yalla.data.jackaro.PlayResultDataInterface>;
                for (const key in commonResultRecord) {
                    let playResultData = commonResultRecord[key];
                    let result = playResultData.result;
                    for (const item of result) {
                        if (item.fromGrid.chess.order == chessId) {
                            return parseInt(key);
                        }
                    }
                }
            } else if (complexKingData) {
                //这里主要用于杀端重进时，需要获取移动步数
                //说明是 复杂玩法 出牌K 移动13步
                return 13;
            } else {
                this.logMore("getPlayPokerMoveStep poker :", poker, "chessId", chessId, "can not get move step");
            }
        }
        return 0;
    }

    /**
     * 出牌(主要处理服务器返回的数据)
     * @param isMoveChess 是否移动棋子
     */
    public handlePlayPoker(idx: number, poker: yalla.data.jackaro.Poker, isMoveChess: boolean = true, isValidate: boolean = true, playPokerEndCallback: Function = null) {

        // let playerShowInfoInterface: yalla.data.jackaro.PlayerShowInfoInterface = JackaroGamePlayerManager.instance.getPlayerByIdx(idx);
        // if (!playerShowInfoInterface) {
        //     yalla.Debug.logMore("handlePlayPoker 出牌 idx:", idx, "playerShowInfoInterface is null")
        // } else {
        //     yalla.Debug.logMore("handlePlayPoker 出牌 idx:", idx, "nickName:", playerShowInfoInterface.nickName, "poker", poker, yalla.data.jackaro.PokerChinesDefine[poker], "isMoveChess:", isMoveChess);
        // }
        let curIdx = idx;
        if (!curIdx) {
            this.logMore("handlePlayPoker 出牌 idx 为空");
            return;
        }
        if (idx == yalla.Global.Account.idx && poker == this.curShowOrbitPoker) {
            this.resetShowOrbitPokerByPlayPoker();
        }

        let cardItem = this.getCardItemByPoker(curIdx, poker);
        if (!cardItem && idx == yalla.Global.Account.idx) {
            this.logMore("自己没有手牌");
            playPokerEndCallback && playPokerEndCallback();
            this.checkResetDiscardPokerAfterPlay({ poker: poker, valid: isValidate });
            return;
        }
        if (idx == yalla.Global.Account.idx && isMoveChess) {
            if (this.allowPokerData[poker]) {
                let allowPokerData = this.allowPokerData[poker];
                if (!allowPokerData) {
                    this.logMore("handlePlayPoker 出牌错误 allowPokerData is null", poker);
                    return;
                }
                let jackResult = allowPokerData.jackResult;

                // PlayCommonResultData commonResult = 3; //当出的牌为A,K,Q,2,3,4,5,6,7,8,9,10时返回的值
                // PlayJackResultData jackResult = 4; //当出牌为J时的返回值
                if (jackResult) {
                    //出J 显示交换牌
                    JackaroChessManager.instance.handlePlayJackResult(jackResult);
                } else {
                    //出A,K,Q,2,3,4,5,6,7,8,9,10 显示移动棋子
                    let commonResult = allowPokerData.commonResult;
                    let haveKeys = commonResult && Object.keys(commonResult.playResultData).length > 0;
                    if (haveKeys) {
                        let commonResultRecord = commonResult.playResultData as Record<string, yalla.data.jackaro.PlayResultDataInterface>;
                        JackaroChessManager.instance.handlePlayResult(commonResultRecord, poker, allowPokerData.pokerTenDiscard, false, false);
                    }
                }
            }
        } else if (idx != yalla.Global.Account.idx) {
            if (this.playerHardCard[idx] && this.playerHardCard[idx].length > 0) {
                cardItem = this.playerHardCard[idx][this.playerHardCard[idx].length - 1];
                cardItem.data.poker = poker;
            } else {
                this.logMore("handlePlayPoker 没有手牌数据 idx:", idx, "poker:", poker);
            }
        }
        if (cardItem) {
            if (!isValidate) {
                cardItem.setState(yalla.view.jackaro._operatorState.CANNOT_PLAY);
                this.addrecordAlreadyPlayPoker({ poker: poker, valid: false });
            } else {
                this.addrecordAlreadyPlayPoker({ poker: poker, valid: true });
            }
            cardItem.setSelectBgVisible(false);
            if (curIdx == yalla.Global.Account.idx) {
                this.logMore("handlePlayPoker 出牌完成 设置isCurTurnPlayPoker idx:", idx, "poker:", poker);
                this.isCurTurnPlayPoker = true;
            }
            this.removeHandCardItem(curIdx, cardItem);
            this.playerHandCardView[curIdx].removeCardItem(cardItem);
            this.playerHandCardView[curIdx].playCard(cardItem, (curIdx, cardItem) => {
                // if (playerShowInfoInterface) {
                //     this.logMore("handlePlayPoker 出牌完成 idx:", idx, "nickName:", playerShowInfoInterface.nickName, yalla.data.jackaro.PokerChinesDefine[poker]);
                // } else {
                //     this.logMore("handlePlayPoker 出牌完成 idx:", idx, "poker:", poker);
                // }
                cardItem.setBackBgVisible(false);
                this.cardLayerMap[curIdx].removeChild(cardItem);
                this.playCardLayer.addChild(cardItem);
                this.playCardItemList.push(cardItem);
                this.playerHandCardView[curIdx].updateCardPos();
                var endPos = JackaroCardManger.instance.getDealPokerPlaceholderGPos();
                if (endPos) {
                    cardItem.x = endPos.x;
                    cardItem.y = endPos.y;
                }
                playPokerEndCallback && playPokerEndCallback();
                if (curIdx == yalla.Global.Account.idx) {
                    TipsViewManager.getInstance().hideTip(TipViewType.RULE_CARD);
                }
                // JackaroCardManger.instance.removeTotalPokerWithAni(cardItem);

                if (JackaroGamePlayerManager.instance.isMyActive()) {
                    //出牌之后移除所有预览
                    JackaroChessManager.instance.removeAllChessOrbit();
                }
            });
        } else {
            this.checkResetDiscardPokerAfterPlay({ poker: poker, valid: isValidate });
            this.logMore("handlePlayPoker 没有找到手牌----idx:", idx, "poker:", poker);
        }
    }
    /**
     * 记录已经打出的牌
     * @param poker 打出的扑克牌信息
     */
    public addrecordAlreadyPlayPoker(poker: yalla.data.jackaro.DiscardedPokerInterface) {
        this.recordAlreadyPlayPoker.push(poker);
    }
    /**
     * 设置发牌回合数
     * @param round 回合数
     */
    public setSendPokerRound(round: number) {
        this.sendPokerRound = round;
    }
    public getSendPokerRound(): number {
        return this.sendPokerRound;
    }
    /**
     * 检查并清理已记录的出牌数据
     * @param round 当前回合数
     * @param isForceClear 是否强制刷新，服务器是否重启或者同步时会刷新逻辑
     */
    public checkClearRecordAlreadyPlayPoker(round: number, isForceClear: boolean = false) {
        if ((this.sendPokerRound && round > this.sendPokerRound) || isForceClear) {
            JackaroCardManger.instance.clearDiscardPoker();
            this.recordAlreadyPlayPoker = [];
        }
    }
    /**
      * 牌局恢复牌堆中的牌
      */
    public generalAlreadyPlayPoker(pokers: Array<yalla.data.jackaro.DiscardedPokerInterface> = []) {
        //TODO::服务器重启recordAlreadyPlayPoker长度被置为0，其他断线重连情况，如果有已经出的牌，但是未选棋子，服务端返回的弃牌堆为null，这里不能清理自己的弃牌堆
        if (!pokers || pokers.length == 0) {
            if (!this.recordAlreadyPlayPoker || this.recordAlreadyPlayPoker.length < 1) {
                JackaroCardManger.instance.clearDiscardPoker();
                this.recordAlreadyPlayPoker = [];
            }
            return;
        }
        let recordLen = this.recordAlreadyPlayPoker.length;
        //当前记录的牌等于牌堆中的牌，此牌堆中的牌已经播放过

        let isSame = recordLen === pokers.length && this.recordAlreadyPlayPoker.every((v, i) => v.poker === pokers[i].poker);
        if (isSame) {
            this.updateAlreadyPlayPoker(pokers, recordLen);
            return;
        }
        let tempPokers: Array<yalla.data.jackaro.DiscardedPokerInterface>;
        if (recordLen > 0 && pokers && pokers.length > recordLen) {
            //牌堆中的牌大于记录的牌，则将牌堆中的牌添加到记录的牌中
            let morePokers = pokers.splice(recordLen, pokers.length - recordLen);//pokers.filter(item => !this.recordAlreadyPlayPoker.some(poker => poker.poker == item.poker));
            tempPokers = morePokers;
            //TODO:: recordLen长度的牌重新检测牌面是否正确
            this.updateAlreadyPlayPoker(pokers, recordLen);
            pokers = pokers.concat(morePokers);
            this.recordAlreadyPlayPoker = pokers;//this.recordAlreadyPlayPoker.concat(morePokers);
        } else {
            //自己本地的牌比服务端返回的牌多，先return不处理； TODO：：如果本地弃牌堆在连上后和服务端的牌一样多，但是最后一张牌不同需要更新弃牌堆
            if (pokers && pokers.length < recordLen) {
                this.recordAlreadyPlayPoker = pokers;
                //本地弃牌堆比服务端返回的牌多，需要隐藏本地多余的牌
                for (let index = 0; index < pokers.length; index++) {
                    let pokerItem: yalla.view.jackaro.JackaroCardItem = this.playCardLayer._childs[index];
                    if (pokerItem && pokers[index] && pokerItem.data.poker != pokers[index].poker) {
                        pokerItem.clear();
                        Laya.Pool.recover('JackaroCardItem', pokerItem);
                        pokerItem.removeSelf();
                    }
                }
                return;
            }

            if (pokers.length == recordLen && !isSame) {
                this.updateAlreadyPlayPoker(pokers, recordLen);
                this.recordAlreadyPlayPoker = pokers;
                return;
            }
            tempPokers = pokers;

            this.recordAlreadyPlayPoker = pokers;
        }
        if (!tempPokers) {
            return;
        }

        for (let index = 0; index < tempPokers.length; index++) {
            const element = tempPokers[index];
            this.createDiscardPoker(element);
        }
        this.recordAlreadyPlayPoker = pokers;
    }
    /**
     * 创建弃牌堆手牌
     */
    private createDiscardPoker(element: yalla.data.jackaro.DiscardedPokerInterface): void {
        let endPos = JackaroCardManger.instance.getDealPokerPlaceholderGPos();
        if (!endPos) return;
        let scale = yalla.view.jackaro.JackaroCardItem.pushScale;
        let cardItem = this.getCardItem();
        let cardData: JackaroCardDataInterface = {
            poker: element.poker,
            idx: 0,
            showState: yalla.view.jackaro.JackaroCardItemState.IN_HAND
        }
        cardItem.setCardData(cardData);
        if (element.valid) {
            cardItem.setState(yalla.view.jackaro._operatorState.CAN_PLAY, true);
        } else {
            cardItem.setState(yalla.view.jackaro._operatorState.CANNOT_PLAY);
        }

        this.playCardLayer && this.playCardLayer.addChild(cardItem);

        this.playCardItemList.push(cardItem);
        cardItem.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_PLAY);
        // {{ YUM: [新增] - 清除卡牌效果提示，防止弃牌堆显示效果信息 }}
        cardItem.setEffectInfo(yalla.view.jackaro.CardEffectType.NONE);

        let rotation = this.generateRandomRotation();
        let goal = { x: endPos.x, y: endPos.y, scaleX: scale, scaleY: scale, rotation: rotation };
        cardItem.anchorX = cardItem.anchorY = 0.5;
        cardItem.x = goal.x;
        cardItem.y = goal.y;
        cardItem.scaleX = goal.scaleX;
        cardItem.scaleY = goal.scaleY;
        cardItem.rotation = goal.rotation;
    }
    /**
     * 每次出牌时，如果没有手牌，需要校验当前牌，是否是弃牌堆顶上的手牌
     */
    private checkResetDiscardPokerAfterPlay(curPokerData: yalla.data.jackaro.DiscardedPokerInterface) {
        if (!curPokerData) return;
        if (this.playCardLayer && !this.playCardLayer.destroyed) {
            let isNeedCreate = false;
            let element: yalla.view.jackaro.JackaroCardItem;
            //检查弃牌堆是否存在牌
            if (this.playCardLayer.numChildren > 0) {
                element = this.playCardLayer._childs[this.playCardLayer.numChildren - 1];
                if (element && element.data && element.data.poker != curPokerData.poker) {
                    isNeedCreate = true;
                }
            } else {
                //不存在
                isNeedCreate = true;
            }
            if (isNeedCreate) {
                this.createDiscardPoker(curPokerData);
                this.recordAlreadyPlayPoker && this.recordAlreadyPlayPoker.push(curPokerData);
            }
        }
    }
    /**
     * 如果弃牌堆牌和自己本地存储的弃牌堆长度一致，但是牌信息不一致，则直接更新牌面图片
     * @param pokers 服务器返回的扑克牌数据
     * @param recordLen 本地记录的牌长度
     */
    public updateAlreadyPlayPoker(pokers, recordLen) {
        let totalChild = this.playCardLayer.numChildren;
        let recordIndex = recordLen - 1;
        for (let index = totalChild - 1; index >= 0; index--) {
            //TODO::playCardItemList只有push没有remove，在不停出牌断线重连后，List中中存储的卡牌数据肯定多余服务端弃牌堆数据，导致最上层牌并未被更新
            let element = this.playCardLayer._childs[index];
            // const element = this.playCardItemList[index];
            if (element && pokers[recordIndex]) {
                let cardData: JackaroCardDataInterface = {
                    poker: pokers[recordIndex].poker,
                    idx: 0,
                    showState: yalla.view.jackaro.JackaroCardItemState.IN_HAND
                }
                element.setCardData(cardData);
                if (pokers[recordIndex].valid) {
                    element.setState(yalla.view.jackaro._operatorState.CAN_PLAY, true);
                } else {
                    element.setState(yalla.view.jackaro._operatorState.CANNOT_PLAY);
                }
                element.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_PLAY);
                let scale = yalla.view.jackaro.JackaroCardItem.pushScale;
                element.scaleX = scale;
                element.scaleY = scale;
                recordIndex--;
            }
        }

        // for (let index = 0; index < recordLen; index++) {
        //     //TODO::playCardItemList只有push没有remove，在不停出牌断线重连后，List中中存储的卡牌数据肯定多余服务端弃牌堆数据，导致最上层牌并未被更新
        //     let element = this.playCardLayer._childs[index];
        //     // const element = this.playCardItemList[index];
        //     if (element && pokers[index]) {
        //         let cardData: JackaroCardDataInterface = {
        //             poker: pokers[index],
        //             idx: 0,
        //             showState: yalla.view.jackaro.JackaroCardItemState.IN_HAND
        //         }
        //         element.data = cardData;
        //         if (pokers[index].valid) {
        //             element.setState(yalla.view.jackaro._operatorState.CAN_PLAY, true);
        //         } else {
        //             element.setState(yalla.view.jackaro._operatorState.CANNOT_PLAY);
        //         }
        //         element.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_PLAY);
        //     }
        // }
    }
    /**
     * 手动添加一张牌到弃牌区域，主要为了用于杀端重进时，玩家之前已经出的牌，换回到弃牌堆
     */
    public handleAddPokerToDiscardPoker(poker: yalla.data.jackaro.Poker) {
        // 获取自己的卡牌项
        const selfIdx = yalla.Global.Account.idx;
        let cardItem = this.getCardItemByPoker(selfIdx, poker);

        // 如果没有找到卡牌，记录日志并返回
        if (!cardItem) {
            this.logMore("handleAddPokerToDiscardPoker cardItem is null");
            return;
        }

        // 从手牌中移除该卡牌
        this.removeHandCardItem(selfIdx, cardItem);
        this.playerHandCardView[selfIdx].removeCardItem(cardItem);

        // 更新卡牌渲染层级
        this.cardLayerMap[selfIdx].removeChild(cardItem);
        this.playCardLayer.addChild(cardItem);
        this.playCardItemList.push(cardItem);

        // 设置卡牌状态为出牌状态
        cardItem.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_PLAY);

        // {{ YUM: [修复] - 清除卡牌效果提示，防止弃牌堆显示效果信息 }}
        cardItem.setEffectInfo(yalla.view.jackaro.CardEffectType.NONE);

        // 设置卡牌的最终位置和样式
        let endPos = JackaroCardManger.instance.getDealPokerPlaceholderGPos();
        let rotation = this.generateRandomRotation();
        let scale = yalla.view.jackaro.JackaroCardItem.pushScale;

        // 应用位置和变换
        cardItem.anchorX = cardItem.anchorY = 0.5;
        cardItem.x = endPos.x;
        cardItem.y = endPos.y;
        cardItem.scaleX = cardItem.scaleY = scale;
        cardItem.rotation = rotation;

        // 更新其他手牌位置
        this.playerHandCardView[selfIdx].updateCardPos(false);

        // 记录已出的牌
        this.addrecordAlreadyPlayPoker({ poker: poker, valid: true });
    }
    /**
     * 动态删除玩家手牌
     * @param idx 玩家索引
     * @param num 删除数量
     */
    public dynamicDiscardPoker(idx: number, num: number) {

        let playerAllPoker = this.getPlayerAllPoker(idx);
        if (!playerAllPoker || playerAllPoker.length == 0) {
            this.logMore("dynamicDiscardPoker playerAllPoker is empty");
            return;
        }
        let len = playerAllPoker.length
        if (num > len) {
            this.logMore("dynamicDiscardPoker num is greater than playerAllPoker.length");
            return;
        }
        for (let index = 0; index < num; index++) {
            let cardItem = this.getCardItemByPoker(idx, playerAllPoker[len - index - 1]);
            if (!cardItem) {
                this.logMore("dynamicDiscardPoker cardItem is null");
                continue;
            }
            this.removeHandCardItem(idx, cardItem);
            this.playerHandCardView[idx].removeCardItem(cardItem);
            this.cardLayerMap[idx].removeChild(cardItem);

        }
        this.playerHandCardView[idx] && this.playerHandCardView[idx].updateCardPos();
    }
    /**
     * 
     * @param idx 
     * @param msg 
     * @returns 
     */
    // public updatePlayerHandCardPosition(idx: number) {
    //     this.playerHandCardView[idx] && this.playerHandCardView[idx].updateCardPos(Laya.Ease.cubicOut, false);
    // }
    /**
     * 弃牌
     * @param idx 
     * @param msg 
     * @param cardIndex 
     * @param isFinalPoker 
     * @param callBack 
     */
    public handleDiscardPoker(idx: number, msg: any, cardIndex: number = JackaroCardManger.novalidCardIndex, isFinalPoker: boolean = false, callBack?: Function): boolean {
        // 获取弃牌信息
        let curDiscardPoker = msg.poker || msg.currentPoker;
        let cardItem = this.getCardItemByPoker(idx, curDiscardPoker);
        if (idx == yalla.Global.Account.idx && curDiscardPoker == this.curShowOrbitPoker) {
            this.resetShowOrbitPokerByPlayPoker();
        }
        yalla.Debug.logMore("handleDiscardPoker", "idx:", idx, "curDiscardPoker:", curDiscardPoker);
        // 处理卡牌查找逻辑
        if (!cardItem) {
            if (idx == yalla.Global.Account.idx) {
                // 自己没有找到卡牌，触发弃牌结束事件
                this.logMore("handleDiscardPoker cardItem is null", curDiscardPoker);
                yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_Card_Discard_End);
                return false;
            } else {
                // 其他玩家卡牌处理
                if (cardIndex == JackaroCardManger.novalidCardIndex) {
                    // 无效索引，使用最后一张牌
                    if (this.playerHardCard[idx] && this.playerHardCard[idx].length > 0) {
                        cardItem = this.playerHardCard[idx][this.playerHardCard[idx].length - 1];
                    }
                } else {
                    // 使用指定索引的牌
                    if (this.playerHardCard[idx] && this.playerHardCard[idx].length > cardIndex) {
                        cardItem = this.playerHardCard[idx][cardIndex];
                    }
                }

                // 设置卡牌数据
                if (cardItem) {
                    cardItem.data.poker = curDiscardPoker;
                }
            }
        }

        // 如果仍然没有找到卡牌，记录日志并返回
        if (!cardItem) {
            this.checkResetDiscardPokerAfterPlay({ poker: curDiscardPoker, valid: false });
            return false;
        }

        // 隐藏弃牌提示
        TipsViewManager.getInstance().hideTip(TipViewType.DISCARD_CARD);

        // 从手牌中移除卡牌
        this.removeHandCardItem(idx, cardItem);
        this.playerHandCardView[idx].removeCardItem(cardItem);

        // 记录弃牌
        this.addrecordAlreadyPlayPoker({ poker: curDiscardPoker, valid: false });

        // 设置当前回合已出牌标记
        if (idx == yalla.Global.Account.idx) {
            this.logMore("handleDiscardPoker 弃牌 设置isCurTurnPlayPoker idx:", idx, "curDiscardPoker:", curDiscardPoker);
            this.isCurTurnPlayPoker = true;
        }
        //弃牌的同时，去掉玩家头像上的弃牌标识
        JackaroGamePlayerManager.instance.resetPlayerForcedDiscard(idx);
        //弃牌的同时，应该同时置灰牌
        cardItem.setState(yalla.view.jackaro._operatorState.CANNOT_PLAY);
        //抽下家牌时，此时下家的牌是隐藏的，导致托管时，下家的牌一直不可见，需要显示一下
        cardItem.visible = true;
        // 执行弃牌动画
        this.playerHandCardView[idx].discardCard(cardItem, cardIndex, (curIdx, cardItem) => {
            // 更新卡牌状态和位置
            this.cardLayerMap[curIdx].removeChild(cardItem);
            this.playCardLayer.addChild(cardItem);
            this.playCardItemList.push(cardItem);
            cardItem.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_PLAY);
            cardItem.setEffectInfo(yalla.view.jackaro.CardEffectType.NONE);
            this.playerHandCardView[curIdx].updateCardPos();

            // 隐藏规则卡提示
            if (curIdx == yalla.Global.Account.idx) {
                TipsViewManager.getInstance().hideTip(TipViewType.RULE_CARD);
            }

            // 自己弃牌完成后触发事件
            if (idx == yalla.Global.Account.idx) {
                yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_Card_Discard_End);
            }

            // 设置最终位置
            var endPos = JackaroCardManger.instance.getDealPokerPlaceholderGPos();
            if (endPos) {
                cardItem.x = endPos.x;
                cardItem.y = endPos.y;
            }

            this.logMore("handleDiscardPoker 弃牌完成 idx: cardItem", cardItem.x, cardItem.y);

            // 执行回调
            callBack && callBack();
        }, isFinalPoker);

        return true;
    }
    /**
     * 处理强制弃牌事件
     * @param msg 消息数据
     */
    public handleForcedDiscardPub(msg: any) {
        let curForcedDiscardIdx = msg.forcedDiscardIdx;
        let currentPoker = msg.currentPoker;
        let playPokerPlayerId = msg.playPokerPlayerId;
        if (currentPoker && playPokerPlayerId) {
            //谁出的牌
            this.handlePlayPoker(playPokerPlayerId, currentPoker, false, true);
        }
        if (curForcedDiscardIdx == yalla.Global.Account.idx) {
            JackaroGamePlayerManager.instance.setForcedDiscard(true);
        }
        this.setCurForcedDiscardIdx(curForcedDiscardIdx);
    }
    public setCurForcedDiscardIdx(forcedDiscardIdx: number) {
        this.curForcedDiscardIdx = forcedDiscardIdx;
        this.logMore("setCurForcedDiscardIdx forcedDiscardIdx:" + forcedDiscardIdx);
    }
    public getCurForcedDiscardIdx(): number {
        return this.curForcedDiscardIdx;
    }
    public resetCurForcedDiscardIdx() {
        this.curForcedDiscardIdx = 0;
    }
    /**
     * 处理抽牌事件
     * @param msg 消息数据
     */
    public handleDrawPokerPub(msg: any) {
        this.curDrawnPokerIdx = msg.drawnPokerIdx;//被抽取牌的玩家id
        let currentPoker = msg.currentPoker;
        let playPokerPlayerId = msg.playPokerPlayerId;
        if (currentPoker && playPokerPlayerId) {
            if (JackaroGamePlayerManager.instance.isMyActive()) {
                let curPlayPoker = JackaroCardManger.instance.getCurPlayPoker();
                if (curPlayPoker && curPlayPoker != currentPoker) {
                    //如果当前是自己的回合，服务器返回的出牌 和 当前出牌不一致，需要同步
                    yalla.data.jackaro.JackaroUserService.instance.sendSyncGameDataReq();
                    return;
                }
            }
            JackaroCardManger.instance.setCurPlayPoker(currentPoker);
            //谁出的牌
            this.handlePlayPoker(playPokerPlayerId, currentPoker, false, true);
        }
        let drawnPoker = msg.drawnPoker;
        let refreshChessOrbitCallBack = () => {
            if (this.curDrawnPokerIdx == yalla.Global.Account.idx) {
                JackaroCardManger.instance.refreshChessOrbit();
            }
        };
        if (drawnPoker && this.curDrawnPokerIdx) {
            msg.poker = drawnPoker;
            if (yalla.Global.isFouce) {
                Laya.timer.once(JackaroCardManger.totalAnimationDrawPoker, this, () => {
                    this.handleDiscardPoker(this.curDrawnPokerIdx, msg, this.drawnPokerIndex, true);
                    this.drawnPokerIndex = 0;
                    refreshChessOrbitCallBack();
                });
            } else {
                this.handleDiscardPoker(this.curDrawnPokerIdx, msg, this.drawnPokerIndex, true);
                this.drawnPokerIndex = 0;
                refreshChessOrbitCallBack();
            }
        }
    }
    // public getDrawnPokerIdx(): number {
    //     return this.curDrawnPokerIdx;
    // }
    public handleTestExchangePoker(idx: number, sourcePoker: number, targetPoker: number) {
        let sourceCardItem = this.getCardItemByPoker(idx, sourcePoker);

        if (!sourceCardItem) {
            this.logMore("handleTestExchangePoker cardItem is null");
            return;
        }
        if (sourceCardItem) {
            let cardData: JackaroCardDataInterface = {
                poker: targetPoker,
                idx: idx,
                showState: yalla.view.jackaro.JackaroCardItemState.IN_HAND
            }
            sourceCardItem.setCardData(cardData);
            sourceCardItem.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_HAND);
        }
    }
    /**
     * 获取玩家所有手牌
     */
    public getPlayerAllPoker(idx: number): Array<yalla.data.jackaro.Poker> {
        return this.playerHardCard[idx].map(item => item.data.poker);
    }
    public setPlayRuleMap(playRuleMap) {
        this.playRuleMap = playRuleMap;
    }
    public getPlayRule(poker: yalla.data.jackaro.Poker) {
        return this.playRuleMap && this.playRuleMap[poker];
    }
    public isComplexPlay(poker: yalla.data.jackaro.Poker): boolean {
        return this.getPlayRule(poker) && this.getPlayRule(poker).playMethod == yalla.data.jackaro.PlayMethod.COMPLEX_PLAY;
    }
    public isComplex1V1Play(poker: yalla.data.jackaro.Poker): boolean {
        return this.getPlayRule(poker) && this.getPlayRule(poker).playMethod == yalla.data.jackaro.PlayMethod.COMPLEX_ONE_VS_ONE_PLAY;
    }
    /**
     * 获取牌型对应资源后缀
     * @param poker '
     * @returns 
     */
    public getPokerTypeSuffix(poker: yalla.data.jackaro.Poker): string {
        let playRule = this.getPlayRule(poker);
        if (playRule) {
            if (playRule.playMethod == yalla.data.jackaro.PlayMethod.COMPLEX_PLAY) {
                return "complex";
            } else if (playRule.playMethod == yalla.data.jackaro.PlayMethod.COMPLEX_ONE_VS_ONE_PLAY) {
                return "1v1";
            }
        }
        return "";
    }
    public isComplexPlaySpecialPokerColor(poker: yalla.data.jackaro.Poker) {
        return poker == yalla.data.jackaro.Poker.SPADE_ACE
            || poker == yalla.data.jackaro.Poker.SPADE_JACK
            || poker == yalla.data.jackaro.Poker.SPADE_QUEEN
            || poker == yalla.data.jackaro.Poker.SPADE_KING
            || poker == yalla.data.jackaro.Poker.HEART_ACE
            || poker == yalla.data.jackaro.Poker.HEART_JACK
            || poker == yalla.data.jackaro.Poker.HEART_QUEEN
            || poker == yalla.data.jackaro.Poker.HEART_KING
            || poker == yalla.data.jackaro.Poker.DIAMOND_ACE
            || poker == yalla.data.jackaro.Poker.DIAMOND_JACK
            || poker == yalla.data.jackaro.Poker.DIAMOND_QUEEN
            || poker == yalla.data.jackaro.Poker.DIAMOND_KING
            || poker == yalla.data.jackaro.Poker.CLUB_ACE
            || poker == yalla.data.jackaro.Poker.CLUB_JACK
            || poker == yalla.data.jackaro.Poker.CLUB_QUEEN
            || poker == yalla.data.jackaro.Poker.CLUB_KING

        //  1 || 11 || 12 || 13 || 14 || 24 || 25 || 26 || 27 || 37 || 38 || 39 || 40 || 50 || 51 || 52;
    }
    /**
     * 是否多步操作可选出牌逻辑
     */
    public isMutilStepPlayPoker(poker: yalla.data.jackaro.Poker) {
        let isPokerA = JackaroCardManger.instance.isPokerA(poker);
        let isPoker10 = JackaroCardManger.instance.isPoker10(poker);
        let isPokerK = JackaroCardManger.instance.isPokerK(poker);
        let isPoker2 = JackaroCardManger.instance.isPoker2(poker);
        if (isPoker2 && JackaroCardManger.instance.isComplex1V1Play(poker)) {
            return true;
        } else if (isPokerA || isPoker10 || (isPokerK && this.isComplexPlay(poker))) {
            return true;
        }
        return false;
    }
    /**
     * 获取玩家所有显示手牌
     */
    public getPlayerAllPokerItem(idx: number): Array<yalla.view.jackaro.JackaroCardItem> {
        return this.playerHardCard[idx];
    }

    /**
     * 获取玩家自己的手牌，包括已经出的牌
     */
    public getPlayerAllPokerItemIncludeOut(): Array<yalla.data.jackaro.Poker> {
        let playerHandPokerItems: Array<yalla.view.jackaro.JackaroCardItem> = this.getPlayerAllPokerItem(yalla.Global.Account.idx);
        let selfAllHandPokerItems: Array<yalla.data.jackaro.Poker> = [];
        if (playerHandPokerItems && playerHandPokerItems.length > 0) {
            for (let index = 0; index < playerHandPokerItems.length; index++) {
                const element = playerHandPokerItems[index];
                if (element && element.data) {
                    selfAllHandPokerItems.push(element.data.poker);
                }
            }
        }
        if (this.selfActivePlayPoker && selfAllHandPokerItems.indexOf(this.selfActivePlayPoker) < 0) {
            selfAllHandPokerItems.push(this.selfActivePlayPoker);
        }
        return selfAllHandPokerItems;
    }
    /**
     * 检查当前手牌是否一致
     */
    public isSelfAllHandPokerItemsSame(selfAllHandPokerItems: yalla.data.jackaro.Poker[]) {
        let curPlayerAllPokers = this.getPlayerAllPokerItemIncludeOut();
        if (selfAllHandPokerItems && selfAllHandPokerItems.length > 0) {
            //检查两个数组中的东西是否一致
            if (selfAllHandPokerItems.length != curPlayerAllPokers.length) {
                return false;
            }
            //检查两个数组中的东西是否一致
            for (let index = 0; index < selfAllHandPokerItems.length; index++) {
                const element = selfAllHandPokerItems[index];
                if (curPlayerAllPokers.indexOf(element) < 0) {
                    return false;
                }
            }
        }
        return true;
    }
    /**
     * 切换玩家时，检查玩家的手牌数量和服务器记录的数量是否一致
     */
    public checkPlayerHandPokersNum(serveHandPokerAmount: Record<number, number>) {
        if (!serveHandPokerAmount) {
            return;
        }
        let selfIdx = yalla.Global.Account.idx;
        for (let idx in serveHandPokerAmount) {
            let curIdx = parseInt(idx);
            if (!this.playerHardCard[idx]) continue;
            let curHandPokerNum = this.playerHardCard[idx].length;
            let serveHandPokerNum = serveHandPokerAmount[idx];
            if (serveHandPokerNum != curHandPokerNum) {
                // yalla.Debug.logMore("checkPlayerHandPokers serveHandPokerNum != curHandPokerNum idx:", idx, "serveHandPokerNum:",
                //     serveHandPokerNum, "curHandPokerNum:", curHandPokerNum);
                // this.logMore("checkPlayerHandPokersNum idx:", idx,
                //     "serveHandPokerNum:", serveHandPokerNum,
                //     "curHandPokerNum:", curHandPokerNum,
                //     "curIdx:", curIdx,
                //     "selfIdx:", selfIdx);
                if (curIdx == selfIdx) {
                    //如果玩家是自己时，请求同步服务器获取最新的自己手牌数据
                    this.printPlayerSelfHandCard("校验手牌： 和服务器手牌数量不一致时：");
                    yalla.data.jackaro.JackaroUserService.instance.sendSyncGameDataReq();
                    break;
                } else {
                    //删除旧手牌
                    this.deleteOtherPlayerAllPokerItem(curIdx);
                    //重建新手牌
                    this.createOtherPlayerHandPoker(curIdx, serveHandPokerNum);
                }
            }
        }
    }

    /**
     * 删除其他玩家的所有手牌项
     * @param idx 要保留手牌的玩家索引
     */
    public deleteOtherPlayerAllPokerItem(idx: number) {
        if (!this.playerHardCard[idx]) {
            yalla.Debug.logMore("deleteOtherPlayerAllPokerItem playerHardCard is null idx:", idx);
            return;
        }

        for (let i = this.playerHardCard[idx].length - 1; i >= 0; i--) {
            let item = this.playerHardCard[idx][i];
            // yalla.Debug.logMore("deleteOtherPlayerAllPokerItem idx:", idx, " item:", yalla.data.jackaro.PokerChinesDefine[item.data.poker]);
            // this.removeHandCardItem(idx, item);
            // this.playerHandCardView[idx].removeCardItem(item);
            item.clear();
            Laya.Pool.recover('JackaroCardItem', item);
        }
        this.playerHandCardView[idx].reset();
        this.playerHardCard[idx] = [];
        this.cardLayerMap[idx].removeChildren();
    }
    /**
     * 创建其它玩家的所有手牌
     * @param idx 
     * @param poker 
     * @returns 
     */
    createOtherPlayerHandPoker(idx: number, otherHandPokerNums: number) {
        if (otherHandPokerNums < 0) return;
        for (let i = 0; i < otherHandPokerNums; i++) {
            JackaroCardManger.instance.createCardItem(idx, yalla.data.jackaro.Poker.NONE_POKER, otherHandPokerNums);
        }
    }

    public getPlayerHandPoker(idx: number, poker: yalla.data.jackaro.Poker): yalla.view.jackaro.JackaroCardItem {
        if (this.playerHardCard[idx])
            for (const item of this.playerHardCard[idx]) {
                if (item.data.poker == poker) {
                    return item;
                }
            }
        return null;
    }
    /**
     * 添加正在执行动画的卡牌项
     * @param cardItem 卡牌项
     */
    public addTotalPokerWithAni(cardItem: yalla.view.jackaro.JackaroCardItem) {
        this.curTotalPokerWithAni && this.curTotalPokerWithAni.push(cardItem);
    }

    /**
     * 获取当前正在动画中的卡牌数量
     * @returns 动画中的卡牌数量
     */
    public getTotalPokerWithAni(): number {
        return this.curTotalPokerWithAni.length;
    }
    public setCurTouchCardItem(touchCardItem: yalla.view.jackaro.JackaroCardItem) {
        this.curTouchCardItem = touchCardItem;
    }
    public setDrawnPokerIndex(cardIndex: number) {
        this.drawnPokerIndex = cardIndex;
    }
    /**
     * 是否复杂玩法出牌K，出牌K可以走13步，出牌K可以连续击杀自己和他人棋子
     */
    public isComplexPlayPokerK(poker: yalla.data.jackaro.Poker): boolean {
        if (JackaroCardManger.instance.isComplexPlay(poker) && JackaroCardManger.instance.isPokerK(poker)) {
            return true;
        }
        return false;
    }
    /**
     * 是否是1v1 玩法牌2
     * @param poker 
     * @returns 
     */
    public is1V1Poker2(poker: yalla.data.jackaro.Poker): boolean {
        if (JackaroCardManger.instance.isComplex1V1Play(poker) && JackaroCardManger.instance.isPoker2(poker)) {
            return true;
        }
        return false;
    }

    public isPokerA(poker: yalla.data.jackaro.Poker): boolean {
        return poker == yalla.data.jackaro.Poker.SPADE_ACE || poker == yalla.data.jackaro.Poker.HEART_ACE || poker == yalla.data.jackaro.Poker.CLUB_ACE || poker == yalla.data.jackaro.Poker.DIAMOND_ACE;
    }
    public isPoker2(poker: yalla.data.jackaro.Poker): boolean {
        return poker == yalla.data.jackaro.Poker.SPADE_TWO || poker == yalla.data.jackaro.Poker.HEART_TWO || poker == yalla.data.jackaro.Poker.CLUB_TWO || poker == yalla.data.jackaro.Poker.DIAMOND_TWO;
    }
    public isPoker3(poker: yalla.data.jackaro.Poker): boolean {
        return poker == yalla.data.jackaro.Poker.SPADE_THREE || poker == yalla.data.jackaro.Poker.HEART_THREE || poker == yalla.data.jackaro.Poker.CLUB_THREE || poker == yalla.data.jackaro.Poker.DIAMOND_THREE;
    }
    public isPoker4(poker: yalla.data.jackaro.Poker): boolean {
        return poker == yalla.data.jackaro.Poker.SPADE_FOUR || poker == yalla.data.jackaro.Poker.HEART_FOUR || poker == yalla.data.jackaro.Poker.CLUB_FOUR || poker == yalla.data.jackaro.Poker.DIAMOND_FOUR;
    }
    public isPoker5(poker: yalla.data.jackaro.Poker): boolean {
        return poker == yalla.data.jackaro.Poker.SPADE_FIVE || poker == yalla.data.jackaro.Poker.HEART_FIVE || poker == yalla.data.jackaro.Poker.CLUB_FIVE || poker == yalla.data.jackaro.Poker.DIAMOND_FIVE;
    }

    public isPoker6(poker: yalla.data.jackaro.Poker): boolean {
        return poker == yalla.data.jackaro.Poker.SPADE_SIX || poker == yalla.data.jackaro.Poker.HEART_SIX || poker == yalla.data.jackaro.Poker.CLUB_SIX || poker == yalla.data.jackaro.Poker.DIAMOND_SIX;
    }
    public isPoker7(poker: yalla.data.jackaro.Poker): boolean {
        return poker == yalla.data.jackaro.Poker.SPADE_SEVEN || poker == yalla.data.jackaro.Poker.HEART_SEVEN || poker == yalla.data.jackaro.Poker.CLUB_SEVEN || poker == yalla.data.jackaro.Poker.DIAMOND_SEVEN;
    }
    public isPoker8(poker: yalla.data.jackaro.Poker): boolean {
        return poker == yalla.data.jackaro.Poker.SPADE_EIGHT || poker == yalla.data.jackaro.Poker.HEART_EIGHT || poker == yalla.data.jackaro.Poker.CLUB_EIGHT || poker == yalla.data.jackaro.Poker.DIAMOND_EIGHT;
    }
    public isPoker9(poker: yalla.data.jackaro.Poker): boolean {
        return poker == yalla.data.jackaro.Poker.SPADE_NINE || poker == yalla.data.jackaro.Poker.HEART_NINE || poker == yalla.data.jackaro.Poker.CLUB_NINE || poker == yalla.data.jackaro.Poker.DIAMOND_NINE;
    }
    public isPoker10(poker: yalla.data.jackaro.Poker): boolean {
        return poker == yalla.data.jackaro.Poker.SPADE_TEN || poker == yalla.data.jackaro.Poker.HEART_TEN || poker == yalla.data.jackaro.Poker.CLUB_TEN || poker == yalla.data.jackaro.Poker.DIAMOND_TEN;
    }
    public isPokerJ(poker: yalla.data.jackaro.Poker): boolean {
        return poker == yalla.data.jackaro.Poker.SPADE_JACK || poker == yalla.data.jackaro.Poker.HEART_JACK || poker == yalla.data.jackaro.Poker.CLUB_JACK || poker == yalla.data.jackaro.Poker.DIAMOND_JACK;
    }
    public isPokerQ(poker: yalla.data.jackaro.Poker): boolean {
        return poker == yalla.data.jackaro.Poker.SPADE_QUEEN || poker == yalla.data.jackaro.Poker.HEART_QUEEN || poker == yalla.data.jackaro.Poker.CLUB_QUEEN || poker == yalla.data.jackaro.Poker.DIAMOND_QUEEN;
    }
    public isPokerK(poker: yalla.data.jackaro.Poker): boolean {
        return poker == yalla.data.jackaro.Poker.SPADE_KING || poker == yalla.data.jackaro.Poker.HEART_KING || poker == yalla.data.jackaro.Poker.CLUB_KING || poker == yalla.data.jackaro.Poker.DIAMOND_KING;
    }

    /**
     * 生成不包含0度的随机角度，范围在(-30, 0)或(0, 30)之间
     * @returns 随机角度
     */
    private generateRandomRotation(): number {
        return Math.random() < 0.5 ?
            Math.random() * -30 : // 生成(-30, 0)的角度
            Math.random() * 30 + 0.1; // 生成(0, 30)的角度，加0.1确保不为0
    }

    /**
     * 设置当前是否有触摸正在进行
     * @param active 是否有触摸正在进行
     * @param cardItem 被触摸的卡牌
     */
    public setTouchActive(active: boolean, cardItem: yalla.view.jackaro.JackaroCardItem = null): void {
        // 如果当前已有卡牌触摸且想设置为触摸状态，但指定的卡牌不同，先解决冲突
        if (active && this._isTouchActive && this._activeTouchCardItem !== null &&
            this._activeTouchCardItem !== cardItem && cardItem !== null) {
            this.logMore("setTouchActive: 检测到触摸冲突，先重置现有触摸");
            // 直接重置全局触摸状态，而不尝试直接访问卡牌的private方法
            this.resetTouchState();
        }

        this._isTouchActive = active;
        this._activeTouchCardItem = active ? cardItem : null;
        this.logMore("setTouchActive: " + active +
            (cardItem ? ", card: " + (cardItem.data ? cardItem.data.poker : "无数据") : ""));
    }

    /**
     * 检查是否有触摸正在进行
     * @returns 是否有触摸正在进行
     */
    public isTouchActive(): boolean {
        // 增加防护逻辑：如果有活动的触摸卡牌但标志位为false，修正状态
        if (!this._isTouchActive && this._activeTouchCardItem !== null) {
            this._isTouchActive = true;
            this.logMore("isTouchActive: 检测到触摸状态不一致，已修正");
        }

        return this._isTouchActive;
    }

    /**
     * 获取当前正在触摸的卡牌
     * @returns 当前正在触摸的卡牌，如果没有返回null
     */
    public getActiveTouchCardItem(): yalla.view.jackaro.JackaroCardItem {
        return this._activeTouchCardItem;
    }

    /**
     * 重置触摸状态
     */
    public resetTouchState(): void {
        this._isTouchActive = false;
        this._activeTouchCardItem = null;
    }

    /**
     * 获取卡牌抬起锁状态
     * @returns 是否有卡牌正在执行抬起动作
     */
    public isCardRaisingLocked(): boolean {
        // 检查是否在锁定冷却期内（300毫秒内不允许连续抬起操作）
        const now = Date.now();
        if (now - this._lastClickTime < 50) {
            this.logMore("isCardRaisingLocked: 点击过于频繁，忽略此次点击");
            return true;
        }
        return this._cardRaisingLock;
    }

    /**
     * 设置卡牌抬起锁状态
     * @param locked 是否锁定
     */
    public setCardRaisingLock(locked: boolean): void {
        this._cardRaisingLock = locked;
        if (locked) {
            this._lastClickTime = Date.now();
        }
        this.logMore("setCardRaisingLock: " + locked);
    }

    /** 
     * 检查是否有正在进行的发牌动画
     * @returns true if there are deal poker animations in progress
     */
    public hasDealPokerAnimationInProgress(): boolean {
        return this.curTotalPokerWithAni.length > 0;
    }





    /**
     * 清理所有状态，包括触摸和卡牌抬起锁
     */
    public clearAllState(): void {
        this.resetTouchState();
        this.setCardRaisingLock(false);
    }
    /**
     * 4. Discard all 按钮出现时机根据对局场景适配
     */
    public getDiscardAllBtnVisible(): boolean {
        /**
         * 情况1：玩家的棋子全部都在基地且手中无有效牌（无扑克牌5）， discard all 按钮出现
         * 情况2：玩家的棋子全部都在基地且手中无有效牌（有扑克牌5）， discard all 按钮不出现
         * 情况2：玩家在非基地区和非终点赛道区有≥1颗棋子，如手中无有效牌，discard all 按钮不出现，玩家只能选择弃掉单张扑克牌
         * 情况3：玩家已经完成游戏，帮队友操作棋子时，需要判断队友的棋子情况，逻辑一致
         */
        //只有一张牌时，也需要展示discard all 按钮
        // let selfPlayerHandPokerItems: Array<yalla.view.jackaro.JackaroCardItem> = JackaroCardManger.instance.getPlayerAllPokerItem(yalla.Global.Account.idx);
        // if (selfPlayerHandPokerItems && selfPlayerHandPokerItems.length == 1) {
        //     return false;
        // }
        let curMoveChessPlayerIdx = JackaroGamePlayerManager.instance.getMoveChessPlayerIdx();
        let chesses = JackaroChessManager.instance.getChessesByPlayerIdx(curMoveChessPlayerIdx);
        let chessInHomeNum: number = 0;
        let chessInEndArea: number = 0;
        for (let i = 0; i < chesses.length; i++) {
            let chess = chesses[i];
            if (!chess) continue;
            if (chess.wayGrid.inHome || chess.goalGrid.inHome) {
                chessInHomeNum++;
            } else if (chess.wayGrid.inEndArea) {
                chessInEndArea++;
            }
        }
        //是否有手牌5
        let selfHandPokerItems: Array<yalla.view.jackaro.JackaroCardItem> = JackaroCardManger.instance.getPlayerAllPokerItem(yalla.Global.Account.idx);
        let hasHandPoker5: boolean = false;
        for (let i = 0; i < selfHandPokerItems.length; i++) {
            let pokerItem = selfHandPokerItems[i];
            if (!pokerItem) continue;
            if (pokerItem.data && this.isPoker5(pokerItem.data.poker)) {
                hasHandPoker5 = true;
                break;
            }
        }
        if (hasHandPoker5) {
            return false;
        } else if (!hasHandPoker5 && (chessInHomeNum + chessInEndArea) != 4) {
            return false;
        }
        return true;
    }

    /**
     * 刷新其他玩家手牌的牌背皮肤，并可选播放刷新动效
     * 该方法应在发牌结束、本家翻牌动效播放完毕后调用
     * 低性能设备不播放刷新动效，但仍会立即更新牌背皮肤
     */
    public refreshOtherPlayersCardBackSkin(withAnimation: boolean = true): void {
        withAnimation = withAnimation && !yalla.Global.isLowPerformanceDevice;
        for (const idxStr in this.playerHardCard) {
            const idx = parseInt(idxStr);
            if (idx === yalla.Global.Account.idx) continue; // 跳过自己

            const handCards = this.playerHardCard[idx];
            if (!handCards || handCards.length === 0) continue; // 无手牌无需处理

            const skinId = this.getCardBackSkin(idx);

            const applySkinChange = () => {
                handCards.forEach(cardItem => {
                    cardItem.setSkinId(skinId);
                    cardItem.setSkin("card");
                });
            };

            if (withAnimation && this.playerHandCardView[idx]) {
                // 先播放刷新动效，60ms 后再真正替换牌背，保证动效可见 {{ YUM: [优化] - 牌背替换延迟 60ms }}
                this.playerHandCardView[idx].playRefreshAnimation(handCards.length);
                Laya.timer.once(60, this, applySkinChange);
            } else {
                applySkinChange();
            }
        }
    }



    /**
     * 设置抽取奖励牌
     * @param poker 抽取奖励牌
     */
    public setDrawRewardPoker(poker: yalla.data.jackaro.Poker): void {
        this.logMore("setDrawRewardPoker", poker);
        this._drawRewardPoker = poker;
    }

    public getDrawRewardPoker(): yalla.data.jackaro.Poker {
        return this._drawRewardPoker;
    }



    /**
     * 获取卡牌层（供补牌管理器使用）
     */
    public get cardLayer(): Laya.Box {
        return this.cardLayerMap[yalla.Global.Account.idx];
    }

    /**
     * 获取指定玩家的卡牌层（供补牌管理器使用）
     */
    public getPlayerCardLayer(idx: number): Laya.Box {
        return this.cardLayerMap[idx];
    }

    /**
     * 获取玩家手牌数组（供补牌管理器使用）
     */
    public getPlayerHandCards(idx: number): Array<yalla.view.jackaro.JackaroCardItem> {
        return this.playerHardCard[idx] || [];
    }

    /**
     * 添加卡牌到玩家手牌（供补牌管理器使用）
     */
    public addCardToPlayerHand(idx: number, cardItem: yalla.view.jackaro.JackaroCardItem): void {
        if (!this.playerHardCard[idx]) {
            this.playerHardCard[idx] = [];
        }
        this.playerHardCard[idx].push(cardItem);
    }
    /**
     * 显示手牌效果(hit end)提示
     * @returns 
     */
    public showCardEffectHints(): void {
        this.logMore("showCardEffectHints Yalla.JackaroTipsShow.isOpen()", yalla.JackaroTipsShow.isOpen());
        let allowPokerData = this.allowPokerData;
        if (!allowPokerData) {
            return;
        }
        // {{ YUM: [Update] - 使用新的管理类检测并显示卡牌效果提示 }}
        JackaroCardEffectHintManager.instance.checkAndShowCardEffectHints(allowPokerData);
    }


}

