/**
 * Jackaro卡牌补牌管理器
 * {{ YUM: [重构] - 重新排列方法顺序，提高代码可读性 }}
 */
class JackaroCardReplenishManager {
    // ==================== 静态属性和单例模式 ====================
    private static _instance: JackaroCardReplenishManager;

    public static get instance(): JackaroCardReplenishManager {
        if (!this._instance) {
            this._instance = new JackaroCardReplenishManager();
        }
        return this._instance;
    }

    // {{ YUM: [新增] - 动效对象池，用于复用动效对象 }}
    private static EFFECT_POOL = {
        upEffects: [] as any[],      // 上层动效对象池
        downEffects: [] as any[],    // 下层动效对象池
        numberEffects: [] as any[]   // 数字动效对象池
    };



    // {{ YUM: [简化] - 统一动画配置，所有时间单位为毫秒(ms) }}
    private static readonly CONFIG = {
        BORN_SCALE_DURATION: 150,    // 出生缩放动画时长：150ms（单次缩放）
        BORN_SCALE_SIZE: 1.57,       // 出生时最大缩放倍数：1.57倍
        OTHER_PLAYER_SCALE: 1.2,     // 其他玩家卡牌最大缩放：1.2倍
        FLIP_DURATION: 200,          // 翻牌单阶段时长：200ms（总翻牌时长=200*2=400ms）
        FINAL_DURATION: 200,         // 最终归位动画时长：200ms（旋转回正）
        ADJUST_DELAY: 400,           // 手牌展示时间：400ms（展示时间）
        ADJUST_TIME: 400,            // 手牌调整时间：400ms（飞向最终位置）
        EFFECT_DESTROY_DELAY: 1200,  // 特效自动销毁延迟：1200ms（1.5秒后清理）
        UP_DOWN_EFFECT_DURATION: 1500, // {{ YUM: [新增] - up/down动效播放时长：1500ms }}
        NUMBER_TWEEN_SCALE_UP_DURATION: 300,   // {{ YUM: [新增] - 数字放大动画时长：300ms }}
        NUMBER_TWEEN_SCALE_DOWN_DURATION: 200, // {{ YUM: [新增] - 数字缩小消失时长：200ms }}
        TEMPORARY_DURATION: 200,               // {{ YUM: [新增] - 临时时长：150ms }}
        // {{ YUM: [新增] - 延迟爆发补牌效果配置 }}
        REWARD_DELAY_KILL: 300,      // 击杀触发延迟：300ms
        REWARD_DELAY_ENDPOINT: 200,  // 进终点触发延迟：200ms
        REWARD_DELAY_TAKEOFF: 200    // 起飞触发延迟：200ms
    };

    // ==================== 实例属性 ====================
    private cardManager: JackaroCardManger;
    private upEffect: any = null;    // {{ YUM: [新增] - 上层动效 }}
    private downEffect: any = null;  // {{ YUM: [新增] - 下层动效 }}
    private numberTweenEffect: any = null; // {{ YUM: [新增] - 数字tween动效层 }}

    // {{ YUM: [新增] - 当前正在进行动画的卡牌项（每次只有一个） }}
    private _currentAnimatingCardItem: any = null;

    // {{ YUM: [新增] - 抽牌奖励系统相关字段 }}
    private _drawRewardPoker: yalla.data.jackaro.Poker = null; // 奖励扑克牌
    private _currentRoundTriggered: boolean = false; // 当前回合是否已触发奖励
    private _lastTriggerRound: number = -1; // 上次触发奖励的回合数

    // {{ YUM: [新增] - 跨回合状态管理变量 }}
    private _isDrawRewardInProgress: boolean = false; // 补牌是否正在进行中
    private _pendingDrawRewardPlayerId: number = -1; // 待补牌的玩家ID

    private _delayTimerFunctions: Function[] = []; // 延迟定时器函数列表，用于取消延迟补牌

    // {{ YUM: [新增] - 状态管理锁，防止竞态条件 }}
    private _isExecutingDrawReward: boolean = false;
    private _isResettingState: boolean = false;
    private _pendingDrawRewardPoker: yalla.data.jackaro.Poker = null; // 待补牌的奖励扑克牌（触发时保存）

    // ==================== 工具方法 ====================
    /**
     * {{ YUM: [迁移] - 日志方法迁移到 JackaroCardLogger }}
     */
    private get logger(): JackaroCardLogger {
        return JackaroCardLogger.instance;
    }

    private logWithTime(message: string, ...args: any[]): void {
        this.logger.logWithTime(message, ...args);
    }

    private errorWithTime(message: string, ...args: any[]): void {
        this.logger.errorWithTime(message, ...args);
    }

    /**
     * 计算动效持续时间
     * @returns 动效持续时间（毫秒）
     */
    private calculateAnimationDuration(): number {
        // {{ YUM: [修正] - 所有玩家抽牌动效整体时间统一计算. Source: 用户反馈 }}
        const config = JackaroCardReplenishManager.CONFIG;
        // 当前玩家实际流程：出生动效 + 手牌调整延迟 + 翻牌动效
        // 出生动效：BORN_SCALE_DURATION × 2（缩放动画）
        // 手牌展示时间：ADJUST_DELAY （展示时间）
        // 手牌调整时间：ADJUST_TIME （手牌调整时间）
        // 最终归位与翻牌并行执行，不增加总时长
        const totalDuration = (config.BORN_SCALE_DURATION * 2) +
            config.ADJUST_DELAY +
            config.ADJUST_TIME +
            config.TEMPORARY_DURATION;

        this.logWithTime(`计算动画时长: ${totalDuration}ms (统一时长)`);
        return totalDuration;
    }

    // ==================== 初始化和配置方法 ====================
    init(cardManager: JackaroCardManger) {
        // {{ YUM: [新增] - 添加重复初始化检查，但允许handCardView根据idx变化 }}
        if (this.cardManager === cardManager) {
            this.logWithTime('[JackaroCardReplenishManager] 跳过重复初始化 - 相同的cardManager');
            return;
        }

        // {{ YUM: [新增] - 如果是全新的初始化或cardManager发生变化，清理之前的资源 }}
        if (this.cardManager && this.cardManager !== cardManager) {
            this.logWithTime('[JackaroCardReplenishManager] 检测到cardManager变化，清理之前的资源');
            this.dispose(false);
        }

        this.cardManager = cardManager;
        this.logWithTime('[JackaroCardReplenishManager] 初始化完成');
    }

    // ==================== 对象池管理方法 ====================
    /**
     * {{ YUM: [新增] - 从对象池获取动效对象 }}
     * @param poolType 对象池类型：'up' | 'down' | 'number'
     * @returns 动效对象
     */
    private getEffectFromPool(poolType: 'up' | 'down' | 'number'): any {
        try {
            let pool: any[];
            let effectName: string;

            switch (poolType) {
                case 'up':
                    pool = JackaroCardReplenishManager.EFFECT_POOL.upEffects;
                    effectName = 'up动效';
                    break;
                case 'down':
                    pool = JackaroCardReplenishManager.EFFECT_POOL.downEffects;
                    effectName = 'down动效';
                    break;
                case 'number':
                    pool = JackaroCardReplenishManager.EFFECT_POOL.numberEffects;
                    effectName = '数字动效';
                    break;
                default:
                    this.logWithTime(`[动效对象池] 未知的对象池类型: ${poolType}`);
                    return new Laya.Box();
            }

            // 从对象池获取或创建新对象
            let effect = pool.length > 0 ? pool.pop() : new Laya.Box();

            // 重置对象状态
            if (effect) {
                effect.removeChildren();
                effect.pos(0, 0);
                effect.scale(1, 1);
                effect.alpha = 1;
                effect.rotation = 0;
                effect.visible = true;

                this.logWithTime(`[动效对象池] 从${effectName}对象池获取对象，池剩余: ${pool.length}`);
            }

            return effect;
        } catch (error) {
            this.logWithTime(`[动效对象池] 获取${poolType}动效对象失败:`, error);
            return new Laya.Box();
        }
    }

    /**
     * {{ YUM: [新增] - 将动效对象回收到对象池 }}
     * @param poolType 对象池类型：'up' | 'down' | 'number'
     * @param effect 动效对象
     */
    private recycleEffectToPool(poolType: 'up' | 'down' | 'number', effect: any): void {
        try {
            if (!effect) return;

            let pool: any[];
            let effectName: string;
            const maxPoolSize = 3; // 每个对象池最大容量

            switch (poolType) {
                case 'up':
                    pool = JackaroCardReplenishManager.EFFECT_POOL.upEffects;
                    effectName = 'up动效';
                    break;
                case 'down':
                    pool = JackaroCardReplenishManager.EFFECT_POOL.downEffects;
                    effectName = 'down动效';
                    break;
                case 'number':
                    pool = JackaroCardReplenishManager.EFFECT_POOL.numberEffects;
                    effectName = '数字动效';
                    break;
                default:
                    this.logWithTime(`[动效对象池] 未知的对象池类型: ${poolType}`);
                    return;
            }

            // {{ YUM: [修复] - 检查对象状态，防止重复回收已销毁的对象 }}
            if (effect.destroyed) {
                this.logWithTime(`[动效对象池] ${effectName}对象已销毁，跳过回收`);
                return;
            }

            // {{ YUM: [安全移除] - 从父容器安全移除但不销毁 }}
            try {
                if (effect.parent) {
                    effect.parent.removeChild(effect);
                }
            } catch (removeError) {
                this.logWithTime(`[动效对象池] 移除${effectName}对象失败:`, removeError);
            }

            // {{ YUM: [安全清理] - 安全清理子对象和动画 }}
            try {
                effect.removeChildren();
                Laya.Tween.clearAll(effect);
                // {{ YUM: [重置状态] - 重置对象状态防止状态污染 }}
                effect.pos(0, 0);
                effect.scale(1, 1);
                effect.alpha = 1;
                effect.rotation = 0;
                effect.visible = true;
            } catch (cleanError) {
                this.logWithTime(`[动效对象池] 清理${effectName}对象状态失败:`, cleanError);
            }

            // {{ YUM: [容量检查] - 检查对象池容量，防止内存泄漏 }}
            if (pool.length < maxPoolSize) {
                pool.push(effect);
                this.logWithTime(`[动效对象池] ${effectName}对象已回收，池容量: ${pool.length}/${maxPoolSize}`);
            } else {
                // {{ YUM: [安全销毁] - 对象池已满时安全销毁对象 }}
                try {
                    effect.destroy();
                    this.logWithTime(`[动效对象池] ${effectName}对象池已满，直接销毁对象`);
                } catch (destroyError) {
                    this.logWithTime(`[动效对象池] 销毁${effectName}对象失败:`, destroyError);
                }
            }
        } catch (error) {
            this.logWithTime(`[动效对象池] 回收${poolType}动效对象失败:`, error);
            // 出错时直接销毁对象
            if (effect) {
                effect.destroy();
            }
        }
    }

    /**
     * {{ YUM: [新增] - 清空所有对象池 }}
     */
    public clearAllEffectPools(): void {
        try {
            // 销毁所有池中的对象
            [...JackaroCardReplenishManager.EFFECT_POOL.upEffects,
            ...JackaroCardReplenishManager.EFFECT_POOL.downEffects,
            ...JackaroCardReplenishManager.EFFECT_POOL.numberEffects].forEach(effect => {
                if (effect && effect.destroy) {
                    effect.destroy();
                }
            });

            // 清空所有对象池
            JackaroCardReplenishManager.EFFECT_POOL.upEffects.length = 0;
            JackaroCardReplenishManager.EFFECT_POOL.downEffects.length = 0;
            JackaroCardReplenishManager.EFFECT_POOL.numberEffects.length = 0;

            this.logWithTime('[动效对象池] 所有对象池已清空');
        } catch (error) {
            this.errorWithTime('[动效对象池] 清空对象池失败:', error);
        }
    }

    // ==================== 当前动画卡牌项管理方法 ====================

    /**
     * {{ YUM: [精简] - 设置当前动画卡牌项 }}
     */
    private setCurrentAnimatingCardItem(cardItem: any): void {
        this._currentAnimatingCardItem = cardItem;
        this.logWithTime(`[动画卡牌] 设置当前动画卡牌项`);
    }

    /**
     * {{ YUM: [精简] - 清除当前动画卡牌项 }}
     */
    private clearCurrentAnimatingCardItem(): void {
        this._currentAnimatingCardItem = null;
        this.logWithTime(`[动画卡牌] 清除当前动画卡牌项`);
    }

    /**
     * {{ YUM: [精简] - 隐藏当前动画卡牌项 }}
     */
    private hideCurrentAnimatingCardItem(): void {
        if (this._currentAnimatingCardItem && !this._currentAnimatingCardItem.destroyed) {
            // this._currentAnimatingCardItem.visible = false;
            this.logWithTime(`[动画卡牌] 隐藏当前动画卡牌项`);
        }
    }

    /**
     * {{ YUM: [精简] - 显示当前动画卡牌项 }}
     */
    private showCurrentAnimatingCardItem(): void {
        if (this._currentAnimatingCardItem && !this._currentAnimatingCardItem.destroyed) {
            this._currentAnimatingCardItem.visible = true;
            this.logWithTime(`[动画卡牌] 显示当前动画卡牌项`);
        }
    }

    // ==================== 公共接口方法 ====================
    /**
     * {{ YUM: [简化] - 主要补牌入口}}
     * @param playerId 玩家ID
     * @param cardData 卡牌数据
     * @param isCurrentPlayer 是否为当前玩家
     */
    public reGetCard(playerId: number, cardData: any, isCurrentPlayer: boolean = true): void {
        try {
            cardData.playerId = playerId;

            // {{ YUM: [新增] - 检查前台状态，如果非前台直接发放手牌不播放动效 }}
            if (!yalla.Global.isFouce) {
                this.logWithTime('[补牌管理器] 应用在后台，直接发放手牌不播放动效');
                return;
            }

            if (isCurrentPlayer) {
                this.handleCurrentPlayerCard(cardData);
            } else {
                this.handleOtherPlayerCard(playerId, cardData);
            }
        } catch (error) {
            this.logWithTime('[补牌失败]', { playerId, error });
            // {{ YUM: [新增] - 补牌失败时的完整动效清理 }}
            this.cleanupManager.clearEffects();

            // {{ YUM: [兜底] - 异常情况下直接发放手牌，确保游戏流程不中断 }}
            try {
                this.logWithTime('[补牌失败] 已通过兜底机制发放手牌');
                // {{ YUM: [修复] - 兜底机制发放手牌后，如果是当前玩家且是自己回合，需要恢复触摸状态 }}
                if (isCurrentPlayer && JackaroGamePlayerManager.instance.isMyActive()) {
                    JackaroCardManger.instance.selfCanTouchCard = true;
                    this.logWithTime('[补牌失败] 兜底机制恢复触摸状态');
                }
            } catch (fallbackError) {
                this.errorWithTime('[补牌失败] 兜底机制也失败:', fallbackError);
                // {{ YUM: [修复] - 兜底机制失败时，如果是当前玩家且是自己回合，需要恢复触摸状态 }}
                if (isCurrentPlayer && JackaroGamePlayerManager.instance.isMyActive()) {
                    JackaroCardManger.instance.selfCanTouchCard = true;
                    this.logWithTime('[补牌失败] 兜底机制失败后恢复触摸状态');
                }
            }
        }
    }

    /**
     * {{ YUM: [新增] - 处理当前玩家补牌 }}
     */
    private handleCurrentPlayerCard(cardData: any): void {
        const cardItem = this.createCardItem(cardData, false);
        if (!cardItem) {
            // {{ YUM: [修复] - 创建卡牌失败时不设置 selfCanTouchCard = false，避免状态不一致 }}
            this.logWithTime('[补牌失败] 创建卡牌失败，跳过补牌流程');
            return;
        }

        // {{ YUM: [精简] - 设置当前动画卡牌项 }}
        this.setCurrentAnimatingCardItem(cardItem);

        // {{ YUM: [新增] - 玩家补牌时，设置为不能触摸 }}
        JackaroCardManger.instance.selfCanTouchCard = false;
        this.setupInitialCardState(cardItem);
        this.addCardToLayer(cardItem, cardData.playerId);

        // {{ YUM: [修复] - 使用嵌套回调替代Promise链 }}
        this.playBornEffect(cardItem, false, () => {
            this.addToHandAndAdjust(cardItem, cardData.playerId, () => {

                this.playFlipAndFinalAnimation(cardItem, cardData, () => {
                    this.logWithTime(' [当前玩家补牌完成]');
                    // {{ YUM: [精简] - 动画完成后清除当前动画卡牌项 }}
                    this.clearCurrentAnimatingCardItem();
                });
            });
        });
    }

    /**
     * {{ YUM: [新增] - 处理其他玩家补牌 }}
     */
    private handleOtherPlayerCard(playerId: number, cardData: any): void {
        const cardItem = this.createCardItem(cardData, false);
        if (!cardItem) return;

        // {{ YUM: [精简] - 设置当前动画卡牌项 }}
        this.setCurrentAnimatingCardItem(cardItem);

        this.setupInitialCardState(cardItem);
        this.addCardToLayer(cardItem, playerId);

        // {{ YUM: [修复] - 为非本玩家创建透明下层动效，解决手牌层级问题 }}
        this.createTransparentDownEffect(cardItem, playerId);

        // {{ YUM: [修复] - 使用嵌套回调替代Promise链 }}
        this.playBornEffect(cardItem, true, () => {
            this.addToHandAndAdjust(cardItem, playerId, () => {

                Laya.timer.once(JackaroCardReplenishManager.CONFIG.ADJUST_DELAY, this, () => {
                    this.logWithTime('[其他玩家补牌完成] 开始播放最终动画');
                    this.playFinalAnimation(cardItem, false);
                    // {{ YUM: [精简] - 动画完成后清除当前动画卡牌项 }}
                    this.clearCurrentAnimatingCardItem();
                });

                this.logWithTime(' [其他玩家补牌完成]');
            });
        });
    }



    /**
     * {{ YUM: [统一] - 设置卡牌初始状态 }}
     */
    private setupInitialCardState(cardItem: any): void {
        const startPos = this.cardManager.getDealPokerPlaceholderGPos() || this.getDefaultDeckPosition();
        cardItem.pos(startPos.x, startPos.y);
        cardItem.rotation = -30;
        cardItem.scale(1, 1);
        cardItem.setBackBgVisible(true);
        cardItem.setBgRewardVisible(true);
    }

    /**
     * {{ YUM: [简化] - 统一的特效动画，使用回调方式 }}
     */
    private playBornEffect(cardItem: any, isOtherPlayer: boolean = false, callback?: () => void): void {
        try {
            const { BORN_SCALE_DURATION, BORN_SCALE_SIZE, OTHER_PLAYER_SCALE } = JackaroCardReplenishManager.CONFIG;
            const targetScale = isOtherPlayer ? OTHER_PLAYER_SCALE : BORN_SCALE_SIZE;
            // {{ YUM: [新增] - 在卡牌添加到手牌后创建up/down动效 }}
            this.createUpDownEffects(cardItem);
            // {{ YUM: [新增] - 补牌音效 }}
            yalla.Sound.playSound('card_extra');
            // {{ YUM: [优化] - 一次性动画，减少嵌套 }}
            Laya.Tween.to(cardItem, { scaleX: targetScale, scaleY: targetScale, rotation: -15 }, BORN_SCALE_DURATION, null,
                Laya.Handler.create(this, () => {
                    this.logWithTime('[特效动画完成] playBornEffect11111111111');
                    Laya.Tween.to(cardItem, { scaleX: 1, scaleY: 1 }, BORN_SCALE_DURATION, null,
                        Laya.Handler.create(this, () => {
                            this.logWithTime('[特效动画完成 playBornEffect2222222222');
                            callback && callback();
                        }));
                }));
        } catch (error) {
            this.logWithTime('[特效动画失败]', error);
            callback && callback(); // 确保回调执行
        }
    }

    /**
     * {{ YUM: [新增] - 添加到手牌并调整位置 }}
     */
    private addToHandAndAdjust(cardItem: any, playerId: number, callback?: () => void): void {
        if (this.cardManager == null) return;
        // {{ YUM: [新增] - 重置手牌视图 }}
        this.logWithTime('[补牌] 重置手牌视图', { playerId ,cardItem});
        this.cardManager.resetHandCardView(playerId);
        this.cardManager.addCardToPlayerHand(playerId, cardItem);

        // {{ YUM: [修复] - 只添加新的cardItem到cardItemList，避免重复添加 }}
        const handCardLayer = this.cardManager.getCardLayer(playerId)
        if (handCardLayer) {
            handCardLayer.addChild(cardItem);
        }
        const handCardView = this.cardManager.playerHandCardView[playerId];
        if (handCardView) {
            handCardView.addCardItem(cardItem);
        }



        this.adjustHandCards(playerId);
        callback && callback();
    }

    /**
     * {{ YUM: [修复] - 翻牌和最终动画，保持原有时序 }}
     * 动画时序解析：
     * 1. 等待200ms(ADJUST_DELAY) - 等待手牌位置调整完成
     * 2. 同时播放翻牌动画和旋转回正动画
     * 3. 翻牌总时长：400ms (200ms缩小 + 200ms放大)
     * 4. 旋转回正时长：200ms (FINAL_DURATION)
     */
    private playFlipAndFinalAnimation(cardItem: any, cardData: any, callback?: () => void): void {
        // 等待手牌调整动画完成后再开始翻牌
        Laya.timer.once(JackaroCardReplenishManager.CONFIG.ADJUST_DELAY, this, () => {
            // 翻牌动画：400ms总时长（两阶段各200ms）
            this.playFlipAnimation(cardItem, cardData);
            // 旋转回正动画：200ms，与翻牌同时进行
            this.playFinalAnimation(cardItem, true);
            callback && callback();
        });
    }

    /**
     * {{ YUM: [修复] - 翻牌动画，恢复原有的状态切换逻辑 }}
     * 翻牌动画详细时序：
     * 阶段1 (0-200ms): X轴缩放从1→0，卡牌逐渐变窄至消失
     * 切换时刻 (200ms): 在卡牌完全变窄时切换正反面状态
     * 阶段2 (200-400ms): X轴缩放从0→1，卡牌重新显示为正面
     * 总时长: 400ms (FLIP_DURATION * 2)
     */
    private playFlipAnimation(cardItem: yalla.view.jackaro.JackaroCardItem, cardData: any): void {
        this.logWithTime(' 游戏核心日志 自己重新获得卡牌，执行卡牌翻转切换。');

        const { FLIP_DURATION } = JackaroCardReplenishManager.CONFIG; // 200ms

        // {{ YUM: [恢复] - 先设置背面可见，模拟原代码逻辑 }}
        cardItem.setBackBgVisibleR(true);
        // {{ YUM: [新增] - 补牌奖励翻转完成后，根据卡牌有效性设置高亮或置灰效果 }}
        let isValidCard = this.applyCardValidityEffect(cardData);
        // 第一阶段：X轴缩放到0（卡牌变窄直到消失）- 耗时200ms
        Laya.Tween.to(cardItem, { scaleX: 0.01 }, FLIP_DURATION, Laya.Ease.linearNone,
            Laya.Handler.create(this, () => {
                // {{ YUM: [恢复] - 在卡牌完全变窄时切换状态和纹理，模拟Unity版本的IsBack状态切换 }}
                // 关键时刻：卡牌在最窄状态下进行正反面切换
                if (cardData) {
                    if (isValidCard) {
                        // 有效牌：设置高亮效果（移除置灰，确保正常显示）
                        cardItem.setState(yalla.view.jackaro._operatorState.CAN_PLAY);
                    } else {
                        // 无效牌：设置置灰效果
                        cardItem.setState(yalla.view.jackaro._operatorState.CANNOT_PLAY);
                    }
                    cardData.IsBack = true;  // 先标记为背面
                    cardItem.setBackBgVisibleR(false); // 切换显示为正面
                    cardItem.setBgRewardVisible(false)
                    cardData.IsBack = false; // 最终状态为正面
                }

                // 第二阶段：X轴缩放回1（卡牌重新显示为正面）- 耗时200ms
                Laya.Tween.to(cardItem, { scaleX: 1 }, FLIP_DURATION, Laya.Ease.linearNone,
                    Laya.Handler.create(this, () => {
                        // 动画完成后，确保卡牌恢复正常显示状态
                        this.logWithTime(' **************执行卡牌翻转切换完成。**************');
                    }));
            }));
    }

    /**
     * {{ YUM: [新增] - 根据卡牌有效性应用显示效果 }}
     * @param cardItem 卡牌项
     * @param cardData 卡牌数据
     */
    private applyCardValidityEffect(cardData: any): boolean {
        try {
            if (!cardData || !cardData.poker) {
                this.logWithTime('applyCardValidityEffect: 参数无效');
                return false;
            }

            const poker = cardData.poker;
            const isValidCard = this.isCardValid(poker);

            this.logWithTime(`补牌奖励卡牌有效性检查: 牌${yalla.data.jackaro.PokerChinesDefine[poker]} (${poker}) 是否有效: ${isValidCard}`);
            return isValidCard

        } catch (error) {

            this.errorWithTime('applyCardValidityEffect 执行失败:', error);
            return false;
        }
    }

    /**
     * {{ YUM: [新增] - 检查卡牌是否为有效牌 }}
     * @param poker 扑克牌
     * @returns 是否为有效牌
     */
    private isCardValid(poker: yalla.data.jackaro.Poker): boolean {
        try {
            const cardManager = JackaroCardManger.instance;
            // 使用新添加的公共方法检查卡牌是否在allowPokerData中
            const isValid = cardManager.isValidPokerAfterReplenish(poker);
            this.logWithTime(`卡牌有效性检查: 牌${poker} 是否有效: ${isValid}`);
            return isValid;
        } catch (error) {
            this.errorWithTime('isCardValid 检查失败:', error);
            return false;
        }
    }

    /**
     * {{ YUM: [修复] - 最终动画，动画效果 }}
     * 旋转回正
     */
    private playFinalAnimation(cardItem: any, isCurrentPlayer: boolean): void {
        if (!isCurrentPlayer) {
            // {{ YUM: [修复] - 其他玩家补牌时，隐藏奖励背景 }}
            cardItem.setBgRewardVisible(false)
        }
        // {{ YUM: [保留] - 当前玩家：只做旋转回正}}
        Laya.Tween.to(cardItem, {
            rotation: 0,
        }, JackaroCardReplenishManager.CONFIG.FINAL_DURATION);
    }

    /**
     * {{ YUM: [简化] - 手牌调整 }}
     */
    private adjustHandCards(playerId: number): void {
        try {
            const handCardView = this.cardManager.playerHandCardView[playerId];
            if (handCardView) {
                if (!yalla.Global.isFouce) {
                    this.logWithTime('[手牌调整] 应用已切到后台，跳过手牌动画避免冲突111111 ', { playerId });
                    this.triggerNumberDisappear();
                    return;
                }
                Laya.timer.once(JackaroCardReplenishManager.CONFIG.ADJUST_DELAY, this, () => {
                    // {{ YUM: [修复] - 检查前台状态，防止与 onForce 中的 resetPlayerHandCardView 冲突 }}
                    if (!yalla.Global.isFouce) {
                        this.logWithTime('[手牌调整] 应用已切到后台，跳过手牌动画避免冲突22222', { playerId });
                        this.triggerNumberDisappear();
                        return;
                    }

                    // {{ YUM: [新增] - 在手牌开始移动时触发数字消失动效 }}
                    this.triggerNumberDisappear();
                    this.logWithTime('[手牌调整] 开始播放最终动画 自己手牌调整动画');
                    // {{ YUM: [修复] - 补牌时，根据玩家ID判断是否需要设置为不能触摸,自己补牌不能触摸 }}
                    handCardView.updateCardPos(true, JackaroCardReplenishManager.CONFIG.ADJUST_TIME, true, playerId != yalla.Global.Account.idx);
                });
            } else {
                // {{ YUM: [新增] - 手牌视图不存在时的兜底处理，防止数字残留 }}
                this.logWithTime('[手牌调整] 未找到玩家手牌视图，触发数字清理兜底机制', { playerId });
                this.triggerNumberDisappear();
            }
        } catch (error) {
            this.logWithTime('[手牌调整失败]', { playerId, error });
            // {{ YUM: [新增] - 异常情况下的兜底清理 }}
            this.triggerNumberDisappear();
        }
    }

    /**
     * {{ YUM: [简化] - 卡牌创建 }}
     */
    private createCardItem(cardData: any, showFront: boolean = false): any {
        try {
            const cardItem: yalla.view.jackaro.JackaroCardItem = this.cardManager.getCardItem();

            cardItem.setCardData({
                poker: cardData.poker ?cardData.poker: 0,
                idx: cardData.playerId || 0,
                showState: 1
            });
            cardItem.setSkinId(JackaroCardManger.instance.getCardBackSkin(cardData.playerId));
            cardItem.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_HAND);
            // cardItem.setSkin("card");

            return cardItem;
        } catch (error) {
            this.logWithTime('[创建卡牌失败]', error);
            return null;
        }
    }

    /**
     * {{ YUM: [简化] - 添加卡牌到层级 }}
     */
    private addCardToLayer(cardItem: any, playerId: number): void {

        // 本玩家的手牌正常添加到对应层级
        const cardLayer = this.cardManager.getReplenishCardLayer();
        if (cardLayer) {
            cardLayer.addChild(cardItem);
        }
    }

    /**
     * {{ YUM: [简化] - 获取默认牌堆位置 }}
     */
    private getDefaultDeckPosition(): { x: number, y: number } {
        return {
            x: Laya.stage.width / 2,
            y: Laya.stage.height / 2 - 100
        };
    }


    // ==================== 动画和特效方法 ====================
    /**
     * {{ YUM: [重构] - 通用动效层级设置方法，合并重复逻辑 }}
     * @param effect 动效对象
     * @param name 动效名称
     * @param x 位置X坐标
     * @param y 位置Y坐标
     * @param referenceItem 参考对象（用于计算层级）
     * @param layerOffset 层级偏移量（正数表示在参考对象上方，负数表示下方）
     */
    private addEffectToLayer(effect: Laya.Box, name: string, x: number, y: number, referenceItem: any, layerOffset: number, playerId?: number): void {
        try {
            effect.name = name;
            effect.pos(x, y);

            // {{ YUM: [修复] - 根据playerId选择正确的卡牌层级 }}
            const targetCardLayer =
                this.cardManager.getReplenishCardLayer();

            if (targetCardLayer) {
                // {{ YUM: [修复] - 对于非本玩家的透明下层动效，添加到handCardLayer确保层级正确 }}
                if (name === "transparentDownEffect" && playerId !== undefined && playerId !== yalla.Global.Account.idx) {
                    // 将非本玩家的透明下层动效添加到本玩家的handCardLayer，确保在最底层
                    const currentPlayerLayer = this.cardManager.getReplenishCardLayer();
                    if (currentPlayerLayer) {
                        currentPlayerLayer.addChildAt(effect, 0); // 添加到最底层
                        this.logWithTime(`透明下层动效添加到本玩家层级底部，playerId: ${playerId}`);
                        return;
                    }
                }

                targetCardLayer.addChild(effect);

                // 计算并设置层级
                const referenceIndex = targetCardLayer.getChildIndex(referenceItem);
                const targetIndex = layerOffset >= 0 ?
                    referenceIndex + layerOffset :
                    Math.max(0, referenceIndex + layerOffset);

                targetCardLayer.setChildIndex(effect, targetIndex);
            }
        } catch (error) {
            this.logWithTime(`[JackaroCardReplenishManager] 添加${name}到层级失败:`, error);
        }
    }

    /**
     * {{ YUM: [新增] - 为非本玩家创建透明下层动效 }}
     * @param cardItem 卡牌对象，用于获取位置
     * @param playerId 玩家ID，用于确定正确的层级
     */
    private createTransparentDownEffect(cardItem: any, playerId: number): void {
        try {
            // 获取卡牌当前位置
            const cardX = cardItem.x;
            const cardY = cardItem.y;

            // {{ YUM: [修复] - 从对象池获取透明下层动效对象 }}
            const transparentDownEffect = this.getEffectFromPool('down');
            transparentDownEffect.alpha = 0; // 设置为透明

            // {{ YUM: [修复] - 使用playerId参数确保动效添加到正确的玩家层级 }}
            this.addEffectToLayer(transparentDownEffect, "transparentDownEffect", cardX, cardY, cardItem, -10, playerId);

            // {{ YUM: [修复] - 延迟回收透明动效 }}
            Laya.timer.once(JackaroCardReplenishManager.CONFIG.EFFECT_DESTROY_DELAY, this, () => {
                this.recycleEffectToPool('down', transparentDownEffect);
            });

            this.logWithTime(' 透明下层动效创建成功，playerId:', playerId);
        } catch (error) {
            this.logWithTime('创建透明下层动效失败:', error);
        }
    }

    /**
     * {{ YUM: [重构] - 创建补牌up/down动效，使用对象池复用动效对象 }}
     * @param cardItem 卡牌对象，用于获取位置
     */
    private createUpDownEffects(cardItem: any): void {
        try {
            // 获取卡牌当前位置
            const cardX = cardItem.x;
            const cardY = cardItem.y;

            // {{ YUM: [修复] - 获取当前玩家ID，确保动效添加到正确层级 }}
            const currentPlayerId = (window as any).yalla?.Global?.Account?.idx || 0;

            // {{ YUM: [优化] - 从对象池获取下层动效对象 }}
            this.downEffect = this.getEffectFromPool('down');
            this.addEffectToLayer(this.downEffect, "reGetCardDownEffect", cardX, cardY, cardItem, -1, currentPlayerId);

            // {{ YUM: [优化] - 从对象池获取上层动效对象 }}
            this.upEffect = this.getEffectFromPool('up');
            this.addEffectToLayer(this.upEffect, "reGetCardUpEffect", cardX, cardY, cardItem, 1, currentPlayerId);

            // {{ YUM: [优化] - 从对象池获取数字tween动效对象 }}
            this.numberTweenEffect = this.getEffectFromPool('number');
            this.addEffectToLayer(this.numberTweenEffect, "reGetCardNumberTweenEffect", cardX, cardY, this.upEffect, 1, currentPlayerId);

            // {{ YUM: [新增] - 播放动效并自动回收 }}
            this.playUpDownEffects();

            this.logWithTime(' up/down动效创建成功（使用对象池）');
        } catch (error) {
            this.logWithTime('创建up/down动效失败:', error);
        }
    }

    /**
     * {{ YUM: [新增] - 播放up/down动效 }}
     */
    private playUpDownEffects(): void {
        try {
            // {{ YUM: [新增] - 检查应用状态，如果在后台则跳过动效播放 }}
            if (!yalla.Global.isFouce) {
                this.logWithTime(' 应用在后台，跳过动效播放');
                this.destroyUpDownEffects();
                return;
            }

            // {{ YUM: [修改] - 播放下层spine动效 }}
            if (this.downEffect) {
                try {
                    // 创建下层spine动画
                    const downSpine = new SpineAniPlayer(yalla.getSkeleton("jackaro/jianglika/jianglika_down"));
                    this.downEffect.addChild(downSpine);

                    // 播放spine动画
                    downSpine.play("down", false); // false表示不循环播放

                    // 监听动画完成事件（可选）
                    downSpine.once(Laya.Event.COMPLETE, this, () => {
                        this.logWithTime(' 下层spine动画播放完成');
                        // {{ YUM: [优化] - 使用统一的回收方法，避免重复清理 }}
                        if (this.downEffect) {
                            this.recycleEffectToPool('down', this.downEffect);
                            this.downEffect = null;
                        }
                        this.checkAndCleanupNumberEffect();
                    });
                } catch (downError) {
                    this.logWithTime(' 下层spine动画创建失败:', downError);
                    // {{ YUM: [优化] - 异常时也使用统一的回收方法 }}
                    if (this.downEffect) {
                        this.recycleEffectToPool('down', this.downEffect);
                        this.downEffect = null;
                    }
                }
            }

            // {{ YUM: [修改] - 播放上层spine动效 }}
            if (this.upEffect) {
                try {
                    // 创建上层spine动画
                    const upSpine = new SpineAniPlayer(yalla.getSkeleton("jackaro/jianglika/jianglika_up"));
                    this.upEffect.addChild(upSpine);

                    // 播放spine动画
                    upSpine.play("up", false); // false表示不循环播放

                    // 监听动画完成事件（可选）
                    upSpine.once(Laya.Event.COMPLETE, this, () => {
                        this.logWithTime(' 上层spine动画播放完成');
                        // {{ YUM: [优化] - 使用统一的回收方法，避免重复清理 }}
                        if (this.upEffect) {
                            this.recycleEffectToPool('up', this.upEffect);
                            this.upEffect = null;
                        }
                        this.checkAndCleanupNumberEffect();
                    });
                } catch (upError) {
                    this.logWithTime(' 上层spine动画创建失败:', upError);
                    // {{ YUM: [优化] - 异常时也使用统一的回收方法 }}
                    if (this.upEffect) {
                        this.recycleEffectToPool('up', this.upEffect);
                        this.upEffect = null;
                    }
                }
            }

            // {{ YUM: [新增] - 播放数字tween动效（与up动效同时开始） }}
            this.playNumberTweenEffect();

            // {{ YUM: [保留] - 自动回收动效（作为保险机制，防止spine动画异常时无法回收） }}
            const autoDestroyFunction = () => {
                this.destroyUpDownEffects();
            };
            this._delayTimerFunctions.push(autoDestroyFunction);
            Laya.timer.once(JackaroCardReplenishManager.CONFIG.UP_DOWN_EFFECT_DURATION, this, autoDestroyFunction);

            this.logWithTime(' spine动效开始播放');
        } catch (error) {
            this.logWithTime(' 播放spine动效失败:', error);
            // {{ YUM: [容错] - 播放失败时也要清理资源 }}
            this.cleanupManager.clearEffects();
        }
    }

    /**
     * {{ YUM: [新增] - 播放数字tween动效 }}
     * 显示+1数字，从0到1放大，等待手牌移动时缩小消失
     */
    private playNumberTweenEffect(): void {
        try {
            if (!this.numberTweenEffect) return;

            // {{ YUM: [修改] - 创建+1数字图片 }}
            const numberImage = new Laya.Image();
            numberImage.skin = "jackaro/1plus.png";

            // {{ YUM: [修改] - 设置图片位置（相对于动效容器中心） }}
            numberImage.pivot(numberImage.width / 2, numberImage.height / 2);
            numberImage.pos(60, 60); // 右下↘️

            // {{ YUM: [修改] - 初始状态：缩放为0，透明度为0 }}
            numberImage.scale(0, 0);
            numberImage.alpha = 0;

            this.numberTweenEffect.addChild(numberImage);

            // {{ YUM: [修改] - 第一阶段：从0到1放大并显示 }}
            const { NUMBER_TWEEN_SCALE_UP_DURATION } = JackaroCardReplenishManager.CONFIG;
            Laya.Tween.to(numberImage, {
                scaleX: 1,
                scaleY: 1,
                alpha: 1
            }, NUMBER_TWEEN_SCALE_UP_DURATION, Laya.Ease.backOut, Laya.Handler.create(this, () => {
                this.logWithTime(' 数字+1动画放大完成');

                // {{ YUM: [新增] - 添加超时保护机制，防止数字残留 }}
                const timeoutDuration = 3000; // 3秒超时保护
                const timeoutFunction = () => {
                    if (this.numberTweenEffect && this.numberTweenEffect.numChildren > 0) {
                        this.logWithTime(' 数字+1超时保护触发，强制清理残留数字');
                        this.destroyNumberTweenEffect();
                    }
                };
                this._delayTimerFunctions.push(timeoutFunction);
                Laya.timer.once(timeoutDuration, this, timeoutFunction);
            }));

            this.logWithTime(' 数字tween动效开始播放');
        } catch (error) {
            this.logWithTime(' 播放数字tween动效失败:', error);
        }
    }

    /**
     * {{ YUM: [新增] - 检查并清理数字动效的兜底方法 }}
     * 当up/down动效完成时，检查数字动效是否还存在，如果存在则清理
     */
    private checkAndCleanupNumberEffect(): void {
        try {
            // 检查是否up和down动效都已经销毁
            if (!this.upEffect && !this.downEffect && this.numberTweenEffect) {
                this.logWithTime(' 检测到up/down动效已完成但数字动效仍存在，执行兜底清理');
                // 延迟一小段时间再清理，给手牌调整留出时间
                Laya.timer.once(500, this, () => {
                    if (this.numberTweenEffect) {
                        this.destroyNumberTweenEffect();
                    }
                });
            }
        } catch (error) {
            this.logWithTime(' 检查数字动效清理失败:', error);
        }
    }

    /**
     * {{ YUM: [修改] - 触发数字消失动效 }}
     * 在手牌移动时调用，让+1数字先放大到1.2再缩小消失
     */
    public triggerNumberDisappear(): void {
        try {
            if (!this.numberTweenEffect) return;

            const numberImage = this.numberTweenEffect.getChildAt(0) as Laya.Image;
            if (!numberImage) return;

            // {{ YUM: [新增] - 清除超时保护定时器，避免重复清理 }}
            Laya.timer.clear(this, this.destroyNumberTweenEffect);

            // {{ YUM: [修改] - 第一阶段：从1放大到1.2 }}
            const { NUMBER_TWEEN_SCALE_DOWN_DURATION } = JackaroCardReplenishManager.CONFIG;
            const scaleUpDuration = NUMBER_TWEEN_SCALE_DOWN_DURATION * 0.3; // 放大阶段占总时长的30%
            const scaleDownDuration = NUMBER_TWEEN_SCALE_DOWN_DURATION * 0.7; // 缩小阶段占总时长的70%

            Laya.Tween.to(numberImage, {
                scaleX: 1.2,
                scaleY: 1.2
            }, scaleUpDuration, Laya.Ease.backOut, Laya.Handler.create(this, () => {
                this.logWithTime(' 数字+1放大到1.2完成，开始缩小消失');

                // {{ YUM: [修改] - 第二阶段：从1.2缩小消失到0 }}
                Laya.Tween.to(numberImage, {
                    scaleX: 0,
                    scaleY: 0,
                    alpha: 0
                }, scaleDownDuration, Laya.Ease.backIn, Laya.Handler.create(this, () => {
                    this.logWithTime(' 数字+1消失动画完成');
                    this.destroyNumberTweenEffect();
                }));
            }));

            this.logWithTime(' 数字消失动画开始：1 -> 1.2 -> 0');
        } catch (error) {
            this.logWithTime(' 数字消失动画失败:', error);
            // {{ YUM: [新增] - 异常情况下强制清理 }}
            this.destroyNumberTweenEffect();
        }
    }

    /**
     * {{ YUM: [重构] - 销毁up/down动效，回收对象到对象池 }}
     */
    private destroyUpDownEffects(): void {
        try {
            // {{ YUM: [优化] - 回收下层动效对象到对象池 }}
            if (this.downEffect) {
                this.recycleEffectToPool('down', this.downEffect);
                this.downEffect = null;
            }

            // {{ YUM: [优化] - 回收上层动效对象到对象池 }}
            if (this.upEffect) {
                this.recycleEffectToPool('up', this.upEffect);
                this.upEffect = null;
            }

            // {{ YUM: [优化] - 回收数字tween动效对象到对象池 }}
            if (this.numberTweenEffect) {
                this.recycleEffectToPool('number', this.numberTweenEffect);
                this.numberTweenEffect = null;
            }

            this.logWithTime(' up/down动效回收到对象池成功');
        } catch (error) {
            this.logWithTime(' 回收up/down动效失败:', error);
        }
    }
    /**
     * {{ YUM: [重构] - 销毁数字tween动效层的专用方法 }}
     * 数字动效不使用对象池，直接销毁
     */
    private destroyNumberTweenEffect(): void {
        try {
            if (this.numberTweenEffect) {
                if (this.numberTweenEffect.parent) {
                    this.numberTweenEffect.parent.removeChild(this.numberTweenEffect);
                }
                this.numberTweenEffect.destroy();
                this.numberTweenEffect = null;
                this.logWithTime(' 数字tween动效层已销毁');
            }
        } catch (error) {
            this.logWithTime(' 销毁数字tween动效层失败:', error);
        }
    }

    /**
     * {{ YUM: [新增] - 应用前后台状态变化时的动效清理 }}
     * 当应用进入后台或从后台恢复时，清理可能残留的动效
     */
    public onAppStateChange(isForeground: boolean): void {
        try {
            if (!isForeground) {
                // {{ YUM: [后台清理] - 应用进入后台时，立即清理所有动效避免残留 }}
                this.logWithTime(' 应用进入后台，清理所有动效');
                this.destroyUpDownEffects();

                // {{ YUM: [精简] - 隐藏当前正在进行动画的卡牌项 }}
                this.hideCurrentAnimatingCardItem();

                // {{ YUM: [后台清理] - 清理所有定时器，避免后台执行 }}
                this.cleanupManager.clearTimersAndEffects();

                // {{ YUM: [修复] - 如果补牌正在进行中，取消并直接发放手牌避免卡住 }}
                if (this._isDrawRewardInProgress) {
                    this.logWithTime(' 应用进入后台时补牌正在进行中，取消并直接发放手牌');
                    this.cancelPendingDrawReward();
                }
            } else {
                // {{ YUM: [前台恢复] - 应用恢复前台时，检查并清理可能的残留动效 }}
                this.logWithTime(' 应用恢复前台，检查动效状态');
                this.cleanupManager.clearTimersAndEffects();
                // {{ YUM: [精简] - 显示当前动画卡牌项 }}
                // this.showCurrentAnimatingCardItem();

                // {{ YUM: [修复] - 检查是否有卡住的补牌状态并清理 }}
                if (this._isDrawRewardInProgress) {
                    this.logWithTime(' 应用恢复前台时发现补牌状态异常，强制清理');
                    this.forceCancelPendingDrawReward();
                }

                // {{ YUM: [修复] - 前台恢复时清理所有补牌相关的定时器，防止与 onForce 中的 resetPlayerHandCardView 冲突 }}
                this.logWithTime(' 应用恢复前台，清理补牌定时器避免与手牌重置冲突');
                this.cleanupManager.clearTimers();
            }
        } catch (error) {
            this.logWithTime(' 应用状态变化处理失败:', error);
        }
    }



    /**
     * {{ YUM: [简化] - 全面检查并清理所有可能残留的动效（调用统一清理管理器） }}
     */
    private checkAndCleanupAllEffects(): void {
        this.cleanupManager.clearEffects();
        this.cleanupManager.clearTimers();
    }

    /**
     * {{ YUM: [简化] - 完整的资源清理（调用统一清理管理器） }}
     */
    public dispose(isResetState: boolean = false): void {
        this.logWithTime(' 开始清理资源');
        this.cleanupManager.fullCleanup(isResetState);
    }

    /**
     * {{ YUM: [新增] - 设置奖励扑克牌 }}
     * @param poker 奖励扑克牌数据
     */
    public setDrawRewardPoker(poker: any): void {
        if (!poker || poker == 0) {
            this.logWithTime(' 设置奖励扑克牌失败，参数无效');
            return;
        }
        this._drawRewardPoker = poker;
        this.logWithTime(' 设置奖励扑克牌:', poker);
    }

    /**
     * {{ YUM: [新增] - 获取奖励扑克牌 }}
     */
    public getDrawRewardPoker(): any {
        return this._drawRewardPoker;
    }

    /**
     * {{ YUM: [新增] - 新回合开始时重置触发状态 }}
     * @param roundNumber 当前回合数
     */
    public onNewRoundStart(roundNumber: number): void {
        // {{ YUM: [修复] - 确保新回合开始时重置触发状态，防止跨回合触发问题 }}
        if (roundNumber !== this._lastTriggerRound) {
            this._currentRoundTriggered = false;
            this.logWithTime(` 新回合开始，重置触发状态 - 回合:${roundNumber}, 上次触发回合:${this._lastTriggerRound}`);
            this.cleanupManager.clearTimersAndEffects();
        } else {
            this.logWithTime(` 同一回合继续，保持触发状态 - 回合:${roundNumber}`);
        }



        // {{ YUM: [新增] - 新回合开始时检查并清理可能残留的动效 }}
        this.cleanupManager.clearEffects();
    }

    /**
     * {{ YUM: [修复] - 尝试触发抽牌奖励，增加特殊K击杀延迟逻辑 }}
     * @param playerId 玩家ID
     * @param roundNumber 当前回合数
     * @param isKillTrigger 是否为击杀触发（可选）
     * @param currentPoker 当前出牌（可选，用于K牌判断）
     * @returns 是否成功触发奖励
     */
    public tryTriggerDrawReward(playerId: number, roundNumber: number, delayTime: number, isKillTrigger: boolean = false, currentPoker?: yalla.data.jackaro.Poker): boolean {
        try {
            this.logWithTime(`[${yalla.getTimeHMS()}] 尝试触发奖励`, {
                playerId,
                roundNumber,
                isKillTrigger,
                currentTriggered: this._currentRoundTriggered,
                lastRound: this._lastTriggerRound,
                hasRewardPoker: !!this._drawRewardPoker
            });

            // {{ YUM: [修复] - 只在当前玩家触发时检查是否有奖励扑克牌 }}
            if (this._currentRoundTriggered && this._lastTriggerRound === roundNumber) {
                this.logWithTime(`[${yalla.getTimeHMS()}] 当前回合已触发过奖励，跳过 - 玩家:${playerId}, 回合:${roundNumber}, 击杀触发:${isKillTrigger}`);
                return false;
            }

            // {{ YUM: [修复] - 只在当前玩家触发时检查是否有奖励扑克牌 }}
            if (playerId === yalla.Global.Account.idx && (this._drawRewardPoker === null || this._drawRewardPoker === undefined)) {
                this.logWithTime(`[${yalla.getTimeHMS()}] 当前玩家没有奖励扑克牌，跳过`);
                return false;
            }

            // {{ YUM: [新增] - 检查是否为特殊K击杀，需要延迟触发 }}
            if (isKillTrigger && this.isSpecialKKill(currentPoker)) {
                this.logWithTime(`[${yalla.getTimeHMS()}] 检测到特殊K击杀，延迟触发抽牌奖励`);
                // {{ YUM: [修复] - 先标记当前回合已触发，防止重复触发 }}
                this._currentRoundTriggered = true;
                this._lastTriggerRound = roundNumber;
                this.delayTriggerForSpecialK(playerId, roundNumber);
                return true;
            }

            // {{ YUM: [触发] - 执行抽牌奖励 }}
            this.executeDrawReward(playerId, roundNumber, delayTime);
            // {{ YUM: [更新] - 标记当前回合已触发 }}
            this._currentRoundTriggered = true;
            this._lastTriggerRound = roundNumber;

            this.logWithTime(`[${yalla.getTimeHMS()}] 成功触发奖励 - 回合:`, roundNumber);
            return true;

        } catch (error) {
            this.errorWithTime(`[${yalla.getTimeHMS()}] 触发奖励失败:`, error);
            return false;
        }
    }


    /**
     * {{ YUM: [修复] - 执行抽牌奖励 }}
     * @param playerId 玩家ID
     * @param roundNumber 回合数
     * @param delayMs 延迟时间（毫秒），默认为0立即执行
     */
    private executeDrawReward(playerId: number, roundNumber: number, delayMs: number = 0): void {
        // {{ YUM: [检查] - 如果补牌正在进行中，取消之前的补牌 }}
        if (this._isDrawRewardInProgress) {
            this.logDrawReward(`补牌正在进行中，取消之前的补牌 - 当前玩家:${this._pendingDrawRewardPlayerId}, 新玩家:${playerId}`);
            this.cancelPendingDrawReward();
        }

        // {{ YUM: [设置] - 标记补牌状态并保存触发时的奖励信息 }}
        this._isDrawRewardInProgress = true;
        this._pendingDrawRewardPlayerId = playerId;

        this._pendingDrawRewardPoker = this._drawRewardPoker; // 保存触发时的奖励扑克牌

        // {{ YUM: [新增] - 记录触发开始时间戳 }}
        const executeStartTime = Date.now();

        // {{ YUM: [新增] - 计算动效结束时间戳 }}
        const isCurrentPlayer = playerId === yalla.Global.Account.idx;
        const animationDuration = this.calculateAnimationDuration();
        const animationEndTime = yalla.Global.isFouce ? executeStartTime + animationDuration + delayMs : 0;

        // {{ YUM: [记录] - 保存动效结束时间戳到棋子管理器 }}
        JackaroChessManager.instance.setDrawRewardAnimationEndTime(animationEndTime);
        this.logWithTime('**************  开始执行抽牌奖励   记录动效结束时间戳 **************', {
            executeStartTime,
            animationDuration,
            animationEndTime,
            isCurrentPlayer,
            delayMs
        });

        if (delayMs > 0 && yalla.Global.isFouce) {
            // {{ YUM: [延迟执行] - 使用Laya.timer.once延迟执行并保存定时器函数 }}
            const timerFunction = () => {
                this.executeDrawRewardInternal(playerId, roundNumber);
            };
            // {{ YUM: [新增] - 保存定时器函数到数组中以便管理 }}
            this._delayTimerFunctions.push(timerFunction);
            Laya.timer.once(delayMs, this, timerFunction);
        } else {
            this.executeDrawRewardInternal(playerId, roundNumber);
        }
    }

    /**
     * {{ YUM: [新增] - 内部执行抽牌奖励的具体逻辑 }}
     * @param playerId 玩家ID
     * @param roundNumber 回合数
     * @param triggerStartTime 触发开始时间戳
     * @param delayMs 实际延迟时间
     */
    private executeDrawRewardInternal(playerId: number, roundNumber: number): void {
        try {
            // {{ YUM: [新增] - 状态锁检查，防止重复执行 }}
            if (this._isExecutingDrawReward) {
                this.logDrawReward(`补牌正在执行中，跳过重复执行 - 玩家:${playerId}, 回合:${roundNumber}`);
                return;
            }
            this._isExecutingDrawReward = true;

            // {{ YUM: [新增] - 检查当前回合是否与触发回合一致，如果不一致则清除手牌和动效 }}
            const currentRound = JackaroGamePlayerManager.instance.getOutRound();
            if (currentRound !== roundNumber) {
                this.logDrawReward(`回合已变化，清除手牌和奖励动效 - 触发回合:${roundNumber}, 当前回合:${currentRound}, 玩家:${playerId}`);

                // {{ YUM: [清除] - 清除当前奖励牌状态 }}
                this._drawRewardPoker = null;
                this._pendingDrawRewardPoker = null;
                this.logDrawReward(`已清除当前奖励牌状态 - 玩家:${playerId}`);

                // {{ YUM: [清除] - 清除正在补牌的 carditem }}
                if (this._currentAnimatingCardItem && !this._currentAnimatingCardItem.destroyed) {
                    this._currentAnimatingCardItem.clear();
                    this._currentAnimatingCardItem.removeSelf();
                    this.logDrawReward(`已清除正在补牌的 carditem - 玩家:${playerId}`);
                }
                this.clearCurrentAnimatingCardItem();

                // {{ YUM: [清除] - 清除奖励动效 }}
                this.cleanupManager.clearEffects();
                this.logDrawReward(`已清除所有奖励动效`);

                // {{ YUM: [重置] - 重置补牌状态 }}
                this.cleanupManager.resetDrawRewardState();
                this._isExecutingDrawReward = false;
                return;
            }

            // {{ YUM: [修复] - 检查补牌状态是否仍然有效，防止应用前后台切换导致的状态异常 }}
            if (!this._isDrawRewardInProgress) {
                this.logDrawReward(`补牌状态已被重置，跳过执行 - 玩家:${playerId}, 回合:${roundNumber}`);
                this._isExecutingDrawReward = false;
                return;
            }

            const isCurrentPlayer = playerId === yalla.Global.Account.idx;

            // {{ YUM: [修复] - 只有当前玩家才需要检查_drawRewardPoker，其他玩家只需展示奖励效果 }}
            if (isCurrentPlayer && !this._drawRewardPoker) {
                this.logDrawReward(`没有奖励扑克牌可发放 - 玩家:${playerId}, 回合:${roundNumber}`);
                // {{ YUM: [重置] - 重置补牌状态 }}
                this.cleanupManager.resetDrawRewardState();
                this._isExecutingDrawReward = false;
                return;
            }

            // {{ YUM: [修改] - 检查当前玩家手牌是否已存在相同的扑克牌，如果存在则不执行抽牌奖励 }}
            if (isCurrentPlayer && this._drawRewardPoker) {
                if (this.hasPlayerHandSamePoker(playerId, this._drawRewardPoker)) {
                    this.logDrawReward(`当前玩家手牌已存在相同扑克牌，跳过抽牌奖励 - 玩家:${playerId}, 奖励牌:${this._drawRewardPoker}`);
                    // {{ YUM: [重置] - 重置补牌状态 }}
                    this._drawRewardPoker = null;
                    this.cleanupManager.resetDrawRewardState();
                    this._isExecutingDrawReward = false;
                    return;
                }
            }
            // {{ YUM: [执行] - 调用补牌方法发放奖励扑克牌 }}
            // {{ YUM: [修复] - 修复玩家ID错误问题：确保每个玩家都使用正确的奖励扑克牌 }}
            let rewardPoker;
            if (isCurrentPlayer) {
                // 当前玩家使用 _drawRewardPoker 或 _pendingDrawRewardPoker
                rewardPoker = this._drawRewardPoker || this._pendingDrawRewardPoker;
            } else {
                // 其他玩家只使用 _drawRewardPoker，不使用 _pendingDrawRewardPoker（那是当前玩家专用的）
                rewardPoker = this._drawRewardPoker;
            }

            const cardData = {
                poker: rewardPoker,
                playerId: playerId,
                skinId: JackaroCardManger.instance.getCardBackSkin(playerId)
            };
            this.logWithTime('手牌数据executeDrawReward', cardData);

            // {{ YUM: [调用] - 使用现有的补牌方法发放奖励 }}
            this.reGetCard(playerId, cardData, isCurrentPlayer);

            // {{ YUM: [清空] - 奖励发放后清空，避免重复使用 }}
            this._drawRewardPoker = null;

            // {{ YUM: [重置] - 使用统一清理管理器重置补牌状态 }}
            this.cleanupManager.resetDrawRewardState();

            this.logDrawReward(`奖励发放完成 - 玩家:${playerId}, 回合:${roundNumber}`);

            // {{ YUM: [释放] - 释放状态锁 }}
            this._isExecutingDrawReward = false;

        } catch (error) {
            this.errorWithTime(' 执行奖励失败:', error);
            // {{ YUM: [重置] - 出错时也要使用统一清理管理器重置状态 }}
            this.cleanupManager.resetDrawRewardState();
            // {{ YUM: [释放] - 释放状态锁 }}
            this._isExecutingDrawReward = false;
        }
    }



    /**
     * {{ YUM: [简化] - 清理定时器的公共方法（调用统一清理管理器） }}
     */
    private clearAllTimers(): void {
        this.cleanupManager.clearTimers();
    }

    /**
     * {{ YUM: [新增] - 抽牌奖励专用日志记录方法 }}
     */
    private logDrawReward(message: string): void {
        this.logWithTime(`[抽牌奖励] ${message}`);
    }

    /**
     * {{ YUM: [新增] - 抽牌奖励专用错误日志记录方法 }}
     */
    private errorDrawReward(message: string, error?: any): void {
        this.errorWithTime(`[抽牌奖励] ${message}`, error);
    }

    /**
     * {{ YUM: [重构] - 统一的清理管理器 }}
     */
    private cleanupManager = {
        // {{ YUM: [统一] - 清理所有定时器 }}
        clearTimers: () => {
            try {
                Laya.timer.clearAll(this);
                this.logDrawReward(`清理所有定时器`);
                if (this._delayTimerFunctions && this._delayTimerFunctions.length > 0) {
                    this.logDrawReward(`清理 ${this._delayTimerFunctions.length} 个延迟定时器函数`);
                    this._delayTimerFunctions.length = 0;
                }
            } catch (error) {
                this.errorDrawReward('清理定时器失败:', error);
                this._delayTimerFunctions = [];
            }
        },

        // {{ YUM: [统一] - 清理所有动效 }}
        clearEffects: () => {
            try {
                this.logDrawReward(`清理所有动效`);
                if (this.upEffect || this.downEffect || this.numberTweenEffect) {
                    this.destroyUpDownEffects();
                }
                this.clearCurrentAnimatingCardItem();
            } catch (error) {
                this.logWithTime('清理动效失败:', error);
            }
        },

        clearTimersAndEffects: () => {
            try {
                this.logDrawReward(`清理所有定时器和动效`);
                // 清理所有定时器
                Laya.timer.clearAll(this);
                if (this._delayTimerFunctions && this._delayTimerFunctions.length > 0) {
                    this._delayTimerFunctions.length = 0;
                }

                // 清理所有动效
                if (this.upEffect || this.downEffect || this.numberTweenEffect) {
                    this.destroyUpDownEffects();
                }

                // {{ YUM: [新增] - 在清理当前动画卡牌项之前，先停止其所有动效 }}
                if (this._currentAnimatingCardItem && !this._currentAnimatingCardItem.destroyed) {
                    this._currentAnimatingCardItem.clearAllAnimations();

                }
                this.clearCurrentAnimatingCardItem();
                this.cardManager.resetReplenishCardLayer();
            } catch (error) {
                // 错误处理
                this.errorDrawReward('清理定时器失败:', error);
                this.logWithTime('清理动效失败:', error);
                this._delayTimerFunctions = [];
            }
        },
        // {{ YUM: [优化] - 重置补牌状态，提升性能和可维护性 }}
        resetDrawRewardState: () => {
            // {{ YUM: [优化] - 防重入检查，避免重复重置 }}
            if (this._isResettingState) {
                this.logDrawReward('状态重置正在进行中，跳过重复重置');
                return;
            }
            this._isResettingState = true;

            try {
                // {{ YUM: [优化] - 记录重置前的状态，便于调试 }}
                const beforeState = {
                    isDrawRewardInProgress: this._isDrawRewardInProgress,
                    pendingPlayerId: this._pendingDrawRewardPlayerId,
                    isExecuting: this._isExecutingDrawReward
                };
                
                // {{ YUM: [优化] - 统一重置所有补牌相关状态，提高代码可维护性 }}
                this._isDrawRewardInProgress = false;
                this._pendingDrawRewardPlayerId = -1;
                this._pendingDrawRewardPoker = null;
                this._isExecutingDrawReward = false;
                
                // {{ YUM: [优化] - 详细日志记录，便于问题排查 }}
                this.logDrawReward(`补牌状态已重置 - 前状态: 进行中=${beforeState.isDrawRewardInProgress}, 待补牌玩家=${beforeState.pendingPlayerId}, 执行中=${beforeState.isExecuting}`);
            } catch (error) {
                // {{ YUM: [优化] - 增强错误处理，确保状态重置的可靠性 }}
                this.errorDrawReward('重置补牌状态时发生错误', error);
                // {{ YUM: [优化] - 即使出错也要确保关键状态被重置 }}
                this._isDrawRewardInProgress = false;
                this._isExecutingDrawReward = false;
            } finally {
                this._isResettingState = false;
            }
        },

        // {{ YUM: [统一] - 完整清理（用于dispose等场景） }}
        fullCleanup: (isResetState: boolean = false) => {
            try {
                // 清理事件监听
                yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Game_Chess_Move_End, this, null);

                // 清理动效和对象池
                this.cleanupManager.clearEffects();
                this.clearAllEffectPools();

                // 清理定时器和动画
                this.cleanupManager.clearTimers();
                Laya.Tween.clearAll(this);

                // 取消待执行的补牌
                if (this._isDrawRewardInProgress) {
                    this.cancelPendingDrawReward();
                }

                // 重置所有状态变量
                this._drawRewardPoker = null;
                this._currentRoundTriggered = false;
                this._lastTriggerRound = -1;
                this._isDrawRewardInProgress = false;
                this._pendingDrawRewardPlayerId = -1;
                this._pendingDrawRewardPoker = null;
                this._delayTimerFunctions.length = 0;
                // 
                if (!isResetState) {
                    this.cardManager = null;
                }


                this.logWithTime('完整清理完成');
            } catch (error) {
                this.logWithTime('完整清理失败:', error);
            }
        }
    };

    /**
     * {{ YUM: [简化] - 重置补牌状态的公共方法（调用统一清理管理器） }}
     */
    private resetDrawRewardState(): void {
        this.cleanupManager.resetDrawRewardState();
    }

    /**
     * {{ YUM: [新增] - 检查玩家手牌是否已存在相同花色 }}
     * @param playerId 玩家ID
     * @param targetSuit 目标花色
     * @returns 是否存在相同花色
     */
    private hasPlayerHandSamePoker(playerId: number, targetPoker: yalla.data.jackaro.Poker): boolean {
        try {
            // 获取玩家手牌
            const playerHandCards = this.cardManager.getPlayerHandCards(playerId);
            if (!playerHandCards || playerHandCards.length === 0) {
                return false;
            }

            // 检查手牌中是否存在相同的扑克牌
            for (const cardItem of playerHandCards) {
                if (cardItem && cardItem.data && cardItem.data.poker) {
                    if (cardItem.data.poker === targetPoker) {
                        this.logWithTime(`[扑克牌检查] 找到相同扑克牌 - 手牌:${cardItem.data.poker}, 目标扑克牌:${targetPoker}`);
                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            this.errorWithTime('[扑克牌检查] 检查玩家手牌扑克牌失败:', error);
            return false;
        }
    }

    /**
     * {{ YUM: [新增] - 检查是否为特殊K击杀 }}
     * @param poker 当前出牌（可选）
     * @returns 是否为特殊K击杀
     */
    private isSpecialKKill(poker?: yalla.data.jackaro.Poker): boolean {
        try {
            // {{ YUM: [优化] - 优先使用传入的牌，避免重复获取 }}
            const currentPoker = poker || JackaroCardManger.instance.getCurPlayPoker();

            // {{ YUM: [检查] - 是否为复杂玩法的K牌 }}
            const isComplexK = JackaroCardManger.instance.isPokerK(currentPoker)

            this.logWithTime(' 特殊K检查:', {
                isPokerK: JackaroCardManger.instance.isPokerK(currentPoker)
            });

            return isComplexK;
        } catch (error) {
            this.errorWithTime(' 检查特殊K失败:', error);
            return false;
        }
    }

    /**
     * {{ YUM: [新增] - 特殊K击杀延迟触发抽牌奖励 }}
     * @param playerId 玩家ID
     * @param roundNumber 回合数
     */
    private delayTriggerForSpecialK(playerId: number, roundNumber: number): void {
        try {
            this.logDrawReward(`开始监听K棋子移动完成事件 - 玩家:${playerId}, 回合:${roundNumber}`);
            // {{ YUM: [清理] - 先清理之前的监听器，避免重复监听 }}
            yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Game_Chess_Move_End, this, null);
            // {{ YUM: [监听] - 监听所有棋子移动结束事件 }}
            const checkAllChessMoveEnd = (chess: JackaroChess) => {
                try {
                    // {{ YUM: [修复] - 只处理实际移动的K棋子，排除被击杀的棋子 }}
                    if (!chess) {
                        this.logDrawReward(`棋子数据为空，跳过处理`);
                        return;
                    }

                    // {{ YUM: [判断] - 检查是否是被K击杀别人的棋子 运动终点为Home}
                    const isWayGet = chess.wayGrid.inHome;

                    this.logDrawReward(`棋子移动结束检查 - 棋子ID:${chess.idKey}, 玩家:${chess.beyondIdx}, 移动状态:${isWayGet}`);

                    // {{ YUM: [过滤] - 只处理当前玩家用K牌击杀别人的棋子 }}
                    if (isWayGet) {
                        this.logDrawReward(`跳过k动效处理 棋子终点为 Home  - 不满足K击杀条件`);
                        return;
                    }

                    this.logDrawReward(`K棋子移动完成，触发抽牌奖励,取消监听,延迟执行抽牌奖励`);

                    // {{ YUM: [移除] - 移除事件监听 }}
                    yalla.event.YallaEvent.instance.off(yalla.data.jackaro.EnumCustomizeCmd.Game_Chess_Move_End, this, checkAllChessMoveEnd);

                    // {{ YUM: [修复] - 使用原始回合数，确保同一回合只触发一次 }}
                    const currentRound = JackaroGamePlayerManager.instance.getOutRound();;
                    this.logWithTime(` K延迟触发执行 - 原回合:${roundNumber}, 当前回合:${currentRound}`);

                    // {{ YUM: [注释] - 暂时注释掉跨回合检查逻辑 }}
                    // if (currentRound !== roundNumber) {
                    //     this.logWithTime(` 回合已变化，取消K延迟触发 - 原回合:${roundNumber}, 当前回合:${currentRound}`);
                    //     // {{ YUM: [清理] - 清理待补牌状态 }}
                    //     this._isDrawRewardInProgress = false;
                    //     this._pendingDrawRewardPlayerId = -1;
                    //     this._pendingDrawRewardRound = -1;
                    //     this._pendingDrawRewardPoker = null;
                    //     return;
                    // }

                    // {{ YUM: [执行] - 使用原始回合数执行奖励 }}
                    this.executeDrawReward(playerId, roundNumber, 0);

                    // {{ YUM: [注意] - 触发状态已在tryTriggerDrawReward中标记，此处不重复标记 }}



                } catch (error) {
                    this.errorDrawReward(`K延迟触发检查棋子移动状态失败 - 棋子:${chess ? chess.idKey : 'null'}:`, error);
                }
            };

            // {{ YUM: [监听] - 监听棋子移动结束事件 }}
            yalla.event.YallaEvent.instance.on(yalla.data.jackaro.EnumCustomizeCmd.Game_Chess_Move_End, this, checkAllChessMoveEnd);



        } catch (error) {
            this.errorDrawReward(`K延迟触发设置失败 - 玩家:${playerId}, 回合:${roundNumber}:`, error);
            // {{ YUM: [降级] - 出错时直接触发，触发状态已在tryTriggerDrawReward中标记 }}
            this.executeDrawReward(playerId, roundNumber, 0);
        }
    }

    /**
     * {{ YUM: [简化] - 取消待执行的补牌奖励（调用统一清理管理器） }}
     */
    private cancelPendingDrawReward(): void {
        try {
            // {{ YUM: [清理] - 使用统一清理管理器清理定时器 }}
            this.cleanupManager.clearTimers();

            // {{ YUM: [新增] - 如果有待发放的奖励扑克牌，直接发放到手里（无动效） }}
            if (this._pendingDrawRewardPoker && this._pendingDrawRewardPlayerId !== -1) {
                this.logDrawReward(`取消延迟补牌，直接发放触发时的奖励到手里（无动效） - 玩家ID:${this._pendingDrawRewardPlayerId}`);

                // 判断是否为当前玩家
                const isCurrentPlayer = this._pendingDrawRewardPlayerId === yalla.Global.Account.idx;

                // {{ YUM: [构造] - 构造完整的卡牌数据，使用触发时保存的奖励 }}
                const cardData = {
                    poker: this._pendingDrawRewardPoker,
                    playerId: this._pendingDrawRewardPlayerId,
                    skinId: JackaroCardManger.instance.getCardBackSkin(this._pendingDrawRewardPlayerId)
                };

                // 清空待发放的奖励扑克牌
                this._pendingDrawRewardPoker = null;
            }

            // {{ YUM: [重置] - 使用统一清理管理器重置状态 }}
            this.cleanupManager.resetDrawRewardState();

            this.logDrawReward(`已取消待执行的补牌奖励`);
        } catch (error) {
            this.errorDrawReward(`取消待执行补牌失败:`, error);
        }
    }




    /**
     * {{ YUM: [简化] - 强制清理卡住的补牌状态（调用统一清理管理器） }}
     */
    public forceCancelPendingDrawReward(): void {
        if (this._isDrawRewardInProgress) {
            this.logDrawReward(`强制清理卡住的补牌状态，不发放手牌`);

            try {
                // {{ YUM: [清理] - 使用统一清理管理器清理定时器 }}
                this.cleanupManager.clearTimers();

                // {{ YUM: [清理] - 清理待发放的奖励数据，不发放手牌 }}
                if (this._pendingDrawRewardPoker) {
                    this.logDrawReward(`清理待发放的奖励扑克牌数据 - 玩家ID:${this._pendingDrawRewardPlayerId}`);
                    this._pendingDrawRewardPoker = null;
                }

                // {{ YUM: [重置] - 使用统一清理管理器重置状态 }}
                this.cleanupManager.resetDrawRewardState();

                this.logDrawReward(`已强制清理卡住的补牌状态`);
            } catch (error) {
                this.errorDrawReward(`强制清理补牌状态失败:`, error);
                // {{ YUM: [兜底] - 确保状态被重置 }}
                this.cleanupManager.resetDrawRewardState();
            }
        }
    }

    /**
     * {{ YUM: [新增] - 棋子起飞时触发抽牌奖励 }}
     * 封装棋子从基地起飞时的抽牌奖励逻辑
     * @param playerId 玩家ID
     * @param currentRound 当前回合数
     * @returns 是否成功触发奖励
     */
    public tryTriggerDrawRewardOnChessTakeOff(playerId: number, currentRound: number): boolean {
        try {
            // {{ YUM: [检查] - 使用服务器数据判断是否可以触发奖励 }}
            const canTriggerReward = JackaroChessManager.instance.checkPreviewRewardPoker('FLY_FROM_BASE', playerId);
            yalla.Debug.yum('[抽牌奖励] 棋子起飞 - 使用服务器数据判断', { canTriggerReward });

            if (canTriggerReward) {
                // {{ YUM: [修改] - 传参方式延迟爆发补牌效果：起飞延迟200ms }}
                const triggered = this.tryTriggerDrawReward(playerId, currentRound, JackaroCardReplenishManager.CONFIG.REWARD_DELAY_TAKEOFF)
                if (triggered) {
                    yalla.Debug.yum('[抽牌奖励] 基地飞出延迟触发安排成功', {
                        playerId,
                        currentRound,
                        delay: JackaroCardReplenishManager.CONFIG.REWARD_DELAY_TAKEOFF
                    });
                }
                return triggered;
            } else {
                yalla.Debug.yum('[抽牌奖励] 基地飞出不触发', {
                    playerId,
                    currentRound
                });
                return false;
            }
        } catch (error) {
            yalla.Debug.yum('[抽牌奖励] 基地飞出触发失败:', error);
            return false;
        }
    }

    /**
     * {{ YUM: [新增] - 棋子进终点时触发抽牌奖励 }}
     * 封装棋子进入终点时的抽牌奖励逻辑
     * @param playerId 玩家ID
     * @param currentRound 当前回合数
     * @param chessId 棋子ID
     * @param isPerfect 是否为完美进终点
     * @returns 是否成功触发奖励
     */
    public tryTriggerDrawRewardOnChessEndpoint(playerId: number, currentRound: number, chessId: number): boolean {
        try {
            // {{ YUM: [检查] - 使用服务器数据判断是否可以触发奖励 }}
            const canTriggerReward = JackaroChessManager.instance.checkPreviewRewardPoker('ENDPOINT', playerId);
            yalla.Debug.yum('[抽牌奖励] 棋子进终点 - 使用服务器数据判断', { canTriggerReward });

            if (canTriggerReward) {
                // {{ YUM: [修改] - 传参方式延迟爆发补牌效果：进终点延迟200ms }}
                const triggered = this.tryTriggerDrawReward(playerId, currentRound, JackaroCardReplenishManager.CONFIG.REWARD_DELAY_ENDPOINT);
                if (triggered) {
                    yalla.Debug.yum('[抽牌奖励] 进终点延迟触发安排成功', {
                        playerId,
                        chessId,
                        currentRound,
                        delay: JackaroCardReplenishManager.CONFIG.REWARD_DELAY_ENDPOINT
                    });
                }
                return triggered;
            } else {
                yalla.Debug.yum('[抽牌奖励] 进终点不触发', {
                    playerId,
                    currentRound
                });
                return false;
            }
        } catch (error) {
            yalla.Debug.yum('[抽牌奖励] 进终点触发失败:', error);
            return false;
        }
    }

    /**
     * {{ YUM: [新增] - 击杀棋子时触发抽牌奖励 }}
     * 封装击杀棋子时的抽牌奖励逻辑
     * @param playerId 玩家ID
     * @param currentRound 当前回合数
     * @param currentPoker 当前出牌
     * @returns 是否成功触发奖励
     */
    public tryTriggerDrawRewardOnChessKill(playerId: number, currentRound: number, currentPoker?: yalla.data.jackaro.Poker): boolean {
        try {
            // {{ YUM: [检查] - 使用服务器数据判断是否可以触发奖励 }}
            const canTriggerReward = JackaroChessManager.instance.checkPreviewRewardPoker('KILL', playerId);
            yalla.Debug.yum('[抽牌奖励] 棋子击杀 - 使用服务器数据判断', { canTriggerReward });

            if (canTriggerReward) {
                // {{ YUM: [修改] - 传参方式延迟爆发补牌效果：击杀延迟300ms }}
                const triggered = this.tryTriggerDrawReward(playerId, currentRound, JackaroCardReplenishManager.CONFIG.REWARD_DELAY_KILL, true, currentPoker);
                if (triggered) {
                    yalla.Debug.yum('[抽牌奖励] 击杀延迟触发安排成功', {
                        playerId,
                        currentRound,
                        delay: JackaroCardReplenishManager.CONFIG.REWARD_DELAY_KILL
                    });
                }
                return triggered;
            } else {
                yalla.Debug.yum('[抽牌奖励] 击杀不触发', {
                    playerId,
                    currentRound
                });
                return false;
            }
        } catch (error) {
            yalla.Debug.yum('[抽牌奖励] 击杀触发失败:', error);
            return false;
        }
    }
}