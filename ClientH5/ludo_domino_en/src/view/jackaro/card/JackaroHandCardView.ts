/**
 * Jackaro 游戏手牌视图类
 * 管理单个玩家手牌的显示、动画（发牌、出牌、弃牌）和位置更新
 */
class JackaroHandCardView {
    /** 手牌区域的起始点 */
    private handCardPoint: Laya.Point;
    /** 手牌区域的总宽度 */
    private handCardWidth: number;
    /** 玩家座位索引 */
    private idx: number;
    /** 玩家方位 (用于区分不同方向的布局) */
    private side: number;
    /** @deprecated 弃牌动画向上移动的缓动实例 */
    private _playCardItemTweenUp: Laya.Tween;
    /** @deprecated 弃牌动画移动到弃牌区的缓动实例 */
    private _playCardItemTweenMove: Laya.Tween;
    /** @deprecated 弃牌动画落下的缓动实例 */
    private _playCardItemTweenDown: Laya.Tween;
    /** 发牌动画的缓动实例 */
    private _playCardItemTween: Laya.Tween;
    /** 标记当前是否正在执行出牌/弃牌动画 */
    private _isPlayCard: boolean = false;
    /** 发牌动画的间隔时间（毫秒） */
    public static dealPokerInterval: number = 64;//80*0.8
    /** 发牌动画的运行时长（毫秒） */
    public static dealPokerTweenTime: number = 360;//450*0.8
    /** 开始发牌时间 */
    public static dealPokerStartTime: number = 0;
    /** 存储手牌 CardItem 实例的数组 */
    public cardItemList: Array<yalla.view.jackaro.JackaroCardItem> = [];
    /** 日志标签 */
    private _logTag: string = "JackaroHandCardView :";

    /** 刷新牌背光效动画相关 */
    private _refreshSk: SpineAniPlayer;


    /**
     * 日志管理方法
     */
    private log(message: string, ...params: any[]): void {
        JackaroCardLogger.instance.log('handCardView', this._logTag, message);
    }

    private logMore(message: string, ...params: any[]): void {
        // 处理参数
        if (params.length > 0) {
            JackaroCardLogger.instance.logMore('handCardView', this._logTag, message, params);
        } else {
            JackaroCardLogger.instance.logMore('handCardView', this._logTag, message);
        }
    }

    /**
     * 构造函数
     * @param idx 玩家座位索引
     * @param handCardPoint 手牌区域的起始点
     * @param handCardWidth 手牌区域的总宽度
     */
    constructor(idx: number, handCardPoint: Laya.Point, handCardWidth: number) {
        this.idx = idx;
        this.handCardPoint = handCardPoint;
        this.handCardWidth = handCardWidth;
        let currentPlayerView = JackaroGamePlayerManager.instance.getGamePlayerByIdx(idx);
        this.side = currentPlayerView.side;
    }

    /**
     * 重置手牌区域的坐标和宽度
     * @param handCardPoint 新的手牌区域起始点
     * @param handCardWidth 新的手牌区域宽度
     */
    public resetHandCardPos(handCardPoint: Laya.Point, handCardWidth: number) {
        this.handCardPoint = handCardPoint;
        this.handCardWidth = handCardWidth;
    }

    /**
     * 向手牌列表中添加一张牌
     * @param cardItem 要添加的牌实例
     */
    public addCardItem(cardItem: yalla.view.jackaro.JackaroCardItem): void {
        this.logMore('cardItemList addCardItem！！！！！！！！！！！', cardItem.data.idx);
        this.cardItemList.push(cardItem);
    }

    /**
     * 从手牌列表中移除一张牌
     * @param cardItem 要移除的牌实例
     */
    public removeCardItem(cardItem: yalla.view.jackaro.JackaroCardItem): void {
        for (let i = 0; i < this.cardItemList.length; i++) {
            if (this.cardItemList[i] == cardItem) {
                this.cardItemList.splice(i, 1);
                break;
            }
        }
    }

    /**
     * 重置手牌视图，清空手牌列表
     */
    public reset(): void {
        this.cardItemList = [];
    }

    /**
     * 清空手牌视图，清空手牌列表
     */
    public clear(): void {
        this.cardItemList = [];

        // 释放刷新动效资源
        if (this._refreshSk && !this._refreshSk.destroyed) {
            this._refreshSk.removeSelf();
            this._refreshSk.destroy(true);
            this._refreshSk = null;
        }


    }

    /**
     * 执行发牌动画
     * @param cardItem 要发的牌实例
     * @param totalLen 当前手牌总数（发完这张后）
     * @param idx 这张牌在手牌中的索引
     * @param isFinalPoker 是否是本轮发的最后一张牌
     */
    public dealPoker(cardItem: yalla.view.jackaro.JackaroCardItem, totalLen: number, idx: number, isFinalPoker: boolean = false, noDelayStart: boolean = false): void {
        let scale;
        let width = cardItem.getCardWidth();
        if (cardItem.data.idx == yalla.Global.Account.idx) {
            // 如果是自己的卡牌且翻牌动画被禁用，则直接显示正面
            if (!JackaroCardManger.enableCardFlipAnimation) {
                cardItem.setBackBgVisible(false);
            } else {
                cardItem.setBackBgVisible(true);
            }
            scale = yalla.view.jackaro.JackaroCardItem.normalScale;
        } else {
            scale = yalla.view.jackaro.JackaroCardItem.otherScale;
            cardItem.setBackBgVisible(true, false);
        }
        let scaleStart = yalla.view.jackaro.JackaroCardItem.dealPokerScale;
        cardItem.scale(scaleStart, scaleStart);
        width = width * scale;
        let endPos = this.getDealPokerInitPos(width, totalLen, idx);
        var goal = { x: endPos.x, y: endPos.y, scaleX: scale, scaleY: scale };
        cardItem.rotation = 0;
        if (cardItem.data.idx == yalla.Global.Account.idx) {
            JackaroCardManger.instance.selfCanTouchCard = false;
        }
        this._isPlayCard = true;
        this.logMore("dealPoker 开始发牌 poker " + cardItem.data.poker + " 坐标：goal " + goal.x);

        // 检查如果是自己的牌，看前面的牌是否已经是正面
        let hasUpdatedCard = false;
        if (cardItem.data.idx == yalla.Global.Account.idx && this.cardItemList.length > 0) {
            for (let i = 0; i < this.cardItemList.length; i++) {
                if (this.cardItemList[i].isUpdated && this.cardItemList[i].isUpdated()) {
                    hasUpdatedCard = true;
                    break;
                }
            }
        }

        // 检查是否需要直接显示正面（翻牌动画被禁用）
        let shouldShowFrontDirectly = !JackaroCardManger.enableCardFlipAnimation && cardItem.data.idx == yalla.Global.Account.idx;

        if (!noDelayStart) {
            if (isFinalPoker) {
                JackaroCardManger.instance.addTotalPokerWithAni(cardItem);
            }
            JackaroCardManger.instance.resetDealPokerPlaceholderGPos();
            var startPos = JackaroCardManger.instance.getDealPokerPlaceholderGPos();
            this.logMore("dealPoker startPos " + startPos.x + " " + startPos.y + " poker " + cardItem.data.poker + " carditem.x " + cardItem.x);
            cardItem.x = startPos.x;
            cardItem.y = startPos.y;

            cardItem.visible = true;
            cardItem.setInitPos(goal.x, goal.y);

            let self = this;
            Laya.Tween.to(cardItem, goal, JackaroHandCardView.dealPokerTweenTime, Laya.Ease.linearIn, Laya.Handler.create(this, (oCardItem) => {
                cardItem.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_HAND);
                this._isPlayCard = false;
                cardItem.x = goal.x;
                cardItem.y = goal.y;
                cardItem.scaleX = goal.scaleX;
                cardItem.scaleY = goal.scaleY;

                // 如果已经有正面牌或者不在焦点状态，直接显示为正面
                if (cardItem.data.idx == yalla.Global.Account.idx) {
                    if (!cardItem.pokerStartSpineAni) {
                        cardItem.pokerStartSpineAni = new SpineAniPlayer("res/sk/jackaro/xing/xingxing.sk");
                    }

                    let explodeBox = cardItem.getPokerStarAnimation();
                    explodeBox.addChild(cardItem.pokerStartSpineAni);
                    cardItem.pokerStartSpineAni.pos(explodeBox.width / 2, explodeBox.height / 2);
                    // 设置随机方向
                    cardItem.pokerStartSpineAni.scaleX = Math.random() < 0.5 ? 1 : -1;

                    // 如果前面的牌已经是正面、不在焦点状态或动画被禁用，直接设置为正面
                    if (hasUpdatedCard || !yalla.Global.isFouce || shouldShowFrontDirectly) {
                        cardItem.playCardFlip(true);
                    }
                }

                let playerShowInfoInterface: yalla.data.jackaro.PlayerShowInfoInterface = JackaroGamePlayerManager.instance.getPlayerByIdx(idx);
                if (playerShowInfoInterface) {
                    this.logMore("dealPoker 发牌结束 idx: " + cardItem.data.idx + " " + playerShowInfoInterface.nickName + " " + yalla.data.jackaro.PokerChinesDefine[cardItem.data.poker] + " poker: " + cardItem.data.poker + " isFinalPoker: " + isFinalPoker);
                } else {
                    this.logMore("dealPoker 发牌结束 idx: " + cardItem.data.idx + " " + yalla.data.jackaro.PokerChinesDefine[cardItem.data.poker] + " poker: " + cardItem.data.poker);
                }
                if (isFinalPoker) {
                    this.logMore('dealPoker 发牌结束  发牌流程 发牌结束 最后一张 ' + Date.now() + " 总发牌时间 " + totalLen + " 张 " + (Date.now() - JackaroHandCardView.dealPokerStartTime));
                    // JackaroCardManger.instance.removeTotalPokerWithAni(cardItem);
                    this.logMore("dealPoker 发牌结束 设置可以点击");

                }
            }, [cardItem]));
        } else {
            if (isFinalPoker) {
                this.logMore("dealPoker end  设置可以点击");
                JackaroCardManger.instance.selfCanTouchCard = true;
            }
            if (this.idx != yalla.Global.Account.idx) {
                cardItem.setSkin("card");
            } else {
                cardItem.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_HAND);
                // 如果前面的牌已经是正面或动画被禁用，直接设置为正面
                if (hasUpdatedCard || shouldShowFrontDirectly) {
                    cardItem.playCardFlip(true);
                }
            }
            this.logMore("dealPoker 直接结束 idx: " + cardItem.data.idx + " poker: " + cardItem.data.poker);
            cardItem.visible = true;
            cardItem.x = goal.x;
            cardItem.y = goal.y;
            cardItem.scaleX = goal.scaleX;
            cardItem.scaleY = goal.scaleY;
            cardItem.setBackBgVisible(cardItem.data.idx != yalla.Global.Account.idx, cardItem.data.idx == yalla.Global.Account.idx);

            cardItem.onTouchEvent();
            this._isPlayCard = false;
        }
    }

    /** @deprecated 时间线动画实例 */
    private _timeLine: Laya.TimeLine = null;
    /** 动画完成后的回调函数 */
    private _callBack: Function;

    /**
     * 执行出牌或弃牌动画
     * @param cardItem 要出/弃的牌实例
     * @param isDiscard 是否是弃牌动画（false为出牌）
     * @param cardIndex 在弃牌动画中的索引（用于时间计算）
     * @param callback 动画完成后的回调函数
     * @param isFinalPoker 是否是本轮出/弃的最后一张牌
     */
    public playOrDiscardCard(cardItem: yalla.view.jackaro.JackaroCardItem, isDiscard: boolean = false,
        cardIndex: number = 0, callback: Function, isFinalPoker: boolean = false): void {
        // 初始化变量
        const actionType = isDiscard ? 'discardCard' : 'playCard';
        const timeStr = this.getTimeStr();
        this.logMore(`${actionType} ${cardItem.data.idx} "" ${cardItem.data.poker} ${timeStr}`);

        this._callBack = callback;

        // 获取目标位置和缩放参数
        const endPos = JackaroCardManger.instance.getDealPokerPlaceholderGPos();
        // 添加小随机偏移，模拟Unity中的随机位置
        // endPos.x += Math.random() * 10 - 5; // -5到5的随机偏移
        // endPos.y += Math.random() * 10 - 5;

        // 统一使用0.8的最终缩放比例，与Unity保持一致
        const finalScale = 0.8;

        // 设置卡牌初始状态
        cardItem.anchorX = cardItem.anchorY = 0.5;

        // 设置卡牌状态
        if (this.idx == yalla.Global.Account.idx) {
            cardItem.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_PLAY);
        } else {
            Laya.timer.once(30, this, () => {
                cardItem.setBackBgVisible(false, false);
                cardItem.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_PLAY);
            });
        }
        cardItem.offTouchEvent();
        cardItem.setSelectBgVisible(false);
        cardItem.setPlayOut(true);

        // 生成随机旋转角度 (-45到45度)
        const rotation = Math.random() * 90 - 45;

        // 合并最终位置参数，用于非焦点状态处理
        const finalPos = {
            x: endPos.x,
            y: endPos.y,
            scaleX: finalScale,
            scaleY: finalScale,
            rotation: rotation
        };
        if (cardItem.getEffectInfo() !== yalla.view.jackaro.CardEffectType.NONE) {
            cardItem.setEffectInfo(yalla.view.jackaro.CardEffectType.NONE);
        }
        // 非焦点状态下直接设置最终位置
        if (!yalla.Global.isFouce) {
            this.logMore(`${actionType} 非动画状态直接设置最终位置`);
            this.handleNonFocusState(cardItem, finalPos, actionType, callback);
            return;
        }

        // 焦点状态下执行动画
        // 如果是最后一张牌则添加到总牌动画
        if (isFinalPoker) {
            JackaroCardManger.instance.addTotalPokerWithAni(cardItem);
        }

        // 清理之前的动画
        cardItem.clearToggleAnimation();
        Laya.Tween.clearAll(cardItem);
        const self = this;
        // 统一缩放动画时间为1.5秒(1500毫秒)，与Unity保持一致
        const scaleTime = 1.2;
        // 统一移动动画时间为0.25秒(250毫秒)，与Unity保持一致
        const moveTime = 250 * scaleTime;

        this.logMore(`${actionType} 开始移动动画 ${cardItem.data.idx} "" ${cardItem.data.poker} ${timeStr}`);
        // 弃牌时设置牌背可见
        cardItem.setBackBgVisibleDiscard(false);
        // 用于管理所有动画的完成状态
        let animationComplete = false;
        const onComplete = () => {
            //如果此时牌已经不再节点了，不需要再次回调
            if (animationComplete || (cardItem && !cardItem.displayedInStage)) return;
            animationComplete = true;
            self._isPlayCard = false;
            self.logMore(`${actionType} 移动动画完成 ${cardItem.data.idx} "" ${cardItem.data.poker} ${self.getTimeStr()}`);
            callback && callback(self.idx, cardItem);
        };

        // 同步执行移动、旋转和缩放动画
        // 1. 移动动画
        cardItem.playCardItemTweenMove = Laya.Tween.to(cardItem,
            { x: endPos.x, y: endPos.y },
            moveTime,
            Laya.Ease.cubicOut, // 对应Unity的OutCubic
            Laya.Handler.create(self, onComplete));

        // 2. 同时执行旋转动画 (0.33秒，与Unity保持一致)
        // 存储在本地变量中，不直接修改cardItem
        let rotationTween = Laya.Tween.to(cardItem,
            { rotation: rotation },
            330 * scaleTime,
            Laya.Ease.quartInOut); // 对应Unity的InOutQuart

        // 3. 同时执行缩放动画 (0.2秒，与Unity保持一致)
        // 存储在本地变量中，不直接修改cardItem
        let scaleTween = Laya.Tween.to(cardItem,
            { scaleX: finalScale, scaleY: finalScale },
            200 * scaleTime,
            Laya.Ease.cubicOut); // 对应Unity的OutCubic
    }

    /**
     * 执行出牌动画
     * @param cardItem 要出的牌实例
     * @param callback 动画完成后的回调函数
     */
    public playCard(cardItem: yalla.view.jackaro.JackaroCardItem, callback: Function): void {
        this.playOrDiscardCard(cardItem, false, 0, callback, false);
    }

    /**
     * 执行弃牌动画（与出牌动画类似，但参数和部分逻辑可能不同）
     * @param cardItem 要弃的牌实例
     * @param cardIndex 卡牌在弃牌序列中的索引
     * @param callback 动画完成后的回调函数
     * @param isFinalPoker 是否是本轮弃的最后一张牌
     */
    public discardCard(cardItem: yalla.view.jackaro.JackaroCardItem, cardIndex: number, callback: Function, isFinalPoker: boolean = false): void {
        this.playOrDiscardCard(cardItem, true, cardIndex, callback, isFinalPoker);
    }

    /**
     * 清理弃牌前动效
     * @deprecated 弃牌动画已重构，此方法不再需要
     */
    public clearDiscardCardAni(): void {
        if (this._playCardItemTweenMove) Laya.Tween.clearTween(this._playCardItemTweenMove);
        if (this._playCardItemTweenUp) Laya.Tween.clearTween(this._playCardItemTweenUp);
        if (this._playCardItemTweenDown) Laya.Tween.clearTween(this._playCardItemTweenDown);
    }
    /**
     * 生成不包含0度的随机角度，范围在(-30, 0)或(0.1, 30)之间
     * @returns 随机角度
     */
    private generateRandomRotation(): number {
        return Math.random() < 0.5 ?
            Math.random() * -30 : // 生成(-30, 0)的角度
            Math.random() * 30 + 0.1; // 生成(0.1, 30)的角度，确保不为0
    }
    /** 
    * 根据手牌数量和手牌宽度，在手牌区域宽度，水平均匀排列牌位置，记录每张牌的x坐标，考虑手牌区域过小，牌要重叠排列
    * @param cardWid    每张手牌宽度
    * @param cardLen    手牌数量
    */
    private createCardInitPos(cardWid: number, cardLen: number): Array<number> {
        let cardPosList = new Array<number>(cardLen);

        // 计算不重叠时的总宽度
        let totalWidth = cardWid * cardLen;
        let overlap = 0;

        // 如果总宽度超过可用宽度，需要重叠显示
        if (this.idx != yalla.Global.Account.idx) {
            // 计算需要重叠的宽度
            // let overlap = cardLen > 1 ? (totalWidth - this.handCardWidth) / (cardLen - 1) : 0;
            // 确保最小间距
            let spacing = 15; // 至少显示30%的牌宽

            for (let i = 0; i < cardLen; i++) {
                if (this.side === 3) {
                    cardPosList[i] = spacing * i;
                } else {
                    cardPosList[i] = -spacing * i;
                }

            }
        } else {
            // 不需要重叠时，均匀分布
            let startX = (this.handCardWidth - totalWidth) / 2 + cardWid / 2;
            for (let i = 0; i < cardLen; i++) {
                cardPosList[i] = startX + cardWid * i;
                overlap += cardWid;
            }
        }

        return cardPosList;
    }
    public getDealPokerInitPos(cardWid: number, cardLen: number, idx: number): Laya.Point {
        var list = this.createCardInitPos(cardWid, cardLen);
        var xx = list[idx];
        return new Laya.Point(xx + this.handCardPoint.x, this.handCardPoint.y);
    }


    /**
     * 更新手牌位置
     * 优化：提前计算所有卡牌目标位置，合并动画与非动画分支，提升可读性
     */
    public updateCardPos(isAni: boolean = true, moveTime: number = 60, isFromReplenish: boolean = false, isselfCanTouchCard: boolean = true): void {
        if (this.cardItemList.length == 0) {
            return;
        }
        this.logMore("updateCardPos " + this.idx
            + " isAni: " + isAni
            + " moveTime: " + moveTime
            + " isFromReplenish: " + isFromReplenish
            + " isselfCanTouchCard: " + isselfCanTouchCard);
        // {{ YUM: [新增] - 标记手牌已被重置 }}
        if (isselfCanTouchCard)
            JackaroCardManger.instance.selfCanTouchCard = true;
        // 重置所有状态，确保更新位置时没有卡牌处于触摸或抬起状态
        JackaroCardManger.instance.clearAllState();

        // 计算基础参数
        let cardWid = this.cardItemList[0].getCardWidth();
        let cardLen = this.cardItemList.length;
        let scale = (this.idx == yalla.Global.Account.idx)
            ? yalla.view.jackaro.JackaroCardItem.normalScale
            : yalla.view.jackaro.JackaroCardItem.otherScale;
        let skinId = JackaroCardManger.instance.getCardBackSkin(this.idx);
        // 提前计算所有卡牌的目标x坐标
        const cardPosList = this.createCardInitPos(cardWid, cardLen);

        for (let i = 0; i < cardLen; i++) {
            let xx = cardPosList[i];
            let cardItem = this.cardItemList[i];
            cardItem.visible = true;

            // 非补牌时，缩放牌
            if (!isFromReplenish) {
                cardItem.scale(scale, scale)
            }
            this.logMore("updateCardPos start " + this.idx +
                "  cardItem: " + cardItem.data.idx +
                "  cardItem.x: " + cardItem.x +
                "  cardItem.y: " + cardItem.y +
                "  cardItem.x To: " + xx + this.handCardPoint.x +
                "  cardItem.y To: " + this.handCardPoint.y +
                "  cardItem.poker: " + cardItem.data.poker +
                "  isFromReplenish: " + isFromReplenish)

                ;
            Laya.Tween.clearAll(cardItem);
            cardItem.clearToggleAnimation();
            cardItem.resetCardState();

            // 公共设置：皮肤、状态
            if (this.idx != yalla.Global.Account.idx) {
                cardItem.setSkinId(skinId);
                cardItem.setSkin("card");
                cardItem.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_HAND, isFromReplenish);
            } else {
                cardItem.onTouchEvent();
            }
             cardItem.setBackBgVisible(cardItem.data.idx != yalla.Global.Account.idx, cardItem.data.idx == yalla.Global.Account.idx);
            cardItem.setUpdated(true);
            // 直接设置最终位置（无动画或非焦点状态）
            if (!yalla.Global.isFouce || !isAni) {
                cardItem.x = xx + this.handCardPoint.x;
                cardItem.y = this.handCardPoint.y;
                cardItem.scaleX = scale;
                cardItem.scaleY = scale;
                cardItem.rotation = 0;
                cardItem.setBgRewardVisible(false);
                cardItem.setInitPos(xx + this.handCardPoint.x, this.handCardPoint.y)
                // {{ YUM: [新增] - 标记手牌已被重置 }}
                cardItem.setHandCardReset(true);
            } else {

                let self = this;
                // 焦点状态且需要动画时，执行动画过渡
                cardItem.setInitPos(xx + this.handCardPoint.x, this.handCardPoint.y, true);
                cardItem.setAnimating(true, "updateCardPos");
                // {{ YUM: [简化] - 防止干扰 奖励抽牌 其他玩家 飞动缩放效果，本玩家手牌不需要会干扰翻牌}}
                let finalPos = this.idx == yalla.Global.Account.idx
                    ? { x: xx + this.handCardPoint.x, y: this.handCardPoint.y, rotation: 0 }
                    : { x: xx + this.handCardPoint.x, y: this.handCardPoint.y, scaleX: scale, scaleY: scale, rotation: 0 };
                Laya.Tween.to(cardItem,
                    finalPos,
                    moveTime,
                    Laya.Ease.cubicOut,
                    Laya.Handler.create(this, (cardItem) => {
                        cardItem.setAnimating(false, "updateCardPos");
                        // 检查动画结束时是否仍在焦点状态
                        if (!yalla.Global.isFouce) {
                            // 如果已不在焦点状态，直接设置最终位置
                            Laya.Tween.clearAll(cardItem);
                            cardItem.x = xx + self.handCardPoint.x;
                            cardItem.y = self.handCardPoint.y;
                            cardItem.scaleX = scale;
                            cardItem.scaleY = scale;
                            cardItem.rotation = 0;
                            cardItem.setInitPos(xx + self.handCardPoint.x, self.handCardPoint.y, false);
                        } else {
                            // 正常完成动画
                            cardItem.setInitPos(null, null, false);
                        }
                        // {{ YUM: [新增] - 标记手牌已被重置 }}
                        cardItem.setBgRewardVisible(false);
                    }, [cardItem]),
                    0,
                    true
                );
            }
        }
    }

    /**
     * 处理非焦点状态下的卡牌位置设置和回调
     * @param cardItem 卡牌实例
     * @param finalPos 最终位置参数
     * @param actionType 动作类型描述（用于日志）
     * @param callback 完成后的回调
     */
    private handleNonFocusState(
        cardItem: yalla.view.jackaro.JackaroCardItem,
        finalPos: { x: number, y: number, scaleX: number, scaleY: number, rotation: number },
        actionType: string,
        callback: Function
    ): void {
        // 直接设置最终位置并回调
        Laya.Tween.clearAll(cardItem);
        this.logMore(`${actionType} clearAll ${cardItem.data.idx} "" ${cardItem.data.poker}`);
        cardItem.clearToggleAnimation();

        // 清除已知的动画实例引用
        cardItem.playCardItemTweenMove = null;
        if (cardItem.playCardItemTweenDown) {
            cardItem.playCardItemTweenDown = null;
        }

        cardItem.x = finalPos.x;
        cardItem.y = finalPos.y;
        cardItem.scaleX = finalPos.scaleX;
        cardItem.scaleY = finalPos.scaleY;
        cardItem.rotation = finalPos.rotation;
        this._isPlayCard = false;
        callback && callback(this.idx, cardItem);
    }

    /**
     * 获取当前时间的格式化字符串，用于日志输出
     */
    private getTimeStr(): string {
        let now = new Date();
        return `${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}:${now.getMilliseconds()}`;
    }

    /**
     * 播放手牌刷新光效动画（仅非本家玩家）
     * @param totalCardLen 当期牌数，用于区分资源（4/5 张）
     */
    public playRefreshAnimation(totalCardLen: number): void {
        // 跳过本家 & 低性能设备
        if (this.idx === yalla.Global.Account.idx || yalla.Global.isLowPerformanceDevice) {
            return;
        }

        const resKey = totalCardLen === 5 ? "5" : "4";

        this._refreshSk = new SpineAniPlayer(yalla.getSkeleton("jackaro/fapai_sweep/fapai_sweep"));
        this._refreshSk.addChild(this._refreshSk);
        this.addRefreshSkToLayer(resKey);
    }

    /**
     * 将刷新 Skeleton 添加到父层并播放指定动画
     */
    private addRefreshSkToLayer(resKey: string): void {
        if (!this._refreshSk) return;

        const parentLayer = JackaroCardManger.instance.getCardLayer(this.idx);
        if (!parentLayer) return;

        let handCenterSpX = 0;
        if (this.side === 3) {
            handCenterSpX = 20;
            this._refreshSk.scaleX = -1;
        } else {
            handCenterSpX = -20;
            this._refreshSk.scaleX = 1;
        }

        const handCenterX = this.handCardPoint.x + handCenterSpX;
        const handCenterY = this.handCardPoint.y;

        this._refreshSk.pos(handCenterX, handCenterY);
        this._refreshSk.setAniblendMode("lighter");
        parentLayer.addChild(this._refreshSk);

        this._refreshSk.visible = true;
        this._refreshSk.play(resKey, false, () => {
            if (this._refreshSk) {
                this._refreshSk.visible = false;
            }
        });
    }

    /**
     * 增强的手牌整理方法 - 支持自定义动画时间和居中排列
     * 用于特效补牌后的手牌重新排列
     * @param withAnimation 是否播放动画
     * @param animationTime 动画时间（毫秒）
     * @param callback 完成回调
     */
    public adjustCardPositions(withAnimation: boolean = true, animationTime: number = 400, callback?: Function): void {
        if (this.cardItemList.length === 0) {
            callback && callback();
            return;
        }

        this.logMore("adjustCardPositions 开始整理手牌", this.idx, "牌数:", this.cardItemList.length, "动画:", withAnimation);

        // 计算基础参数
        const cardWid = this.cardItemList[0].getCardWidth();
        const cardLen = this.cardItemList.length;
        const scale = (this.idx === yalla.Global.Account.idx)
            ? yalla.view.jackaro.JackaroCardItem.normalScale
            : yalla.view.jackaro.JackaroCardItem.otherScale;

        // 计算所有卡牌的目标位置
        const cardPosList = this.createCardInitPos(cardWid, cardLen);
        let completedAnimations = 0;
        const totalAnimations = cardLen;

        const onAnimationComplete = () => {
            completedAnimations++;
            if (completedAnimations >= totalAnimations) {
                this.logMore("adjustCardPositions 手牌整理完成", this.idx);
                callback && callback();
            }
        };

        // 为每张卡牌设置目标位置
        for (let i = 0; i < cardLen; i++) {
            const cardItem = this.cardItemList[i];
            const targetX = cardPosList[i] + this.handCardPoint.x;
            const targetY = this.handCardPoint.y;

            // 清除现有动画
            Laya.Tween.clearAll(cardItem);
            cardItem.clearToggleAnimation();

            // 设置缩放
            cardItem.scaleX = scale;
            cardItem.scaleY = scale;

            if (withAnimation && yalla.Global.isFouce) {
                // 播放移动动画
                cardItem.setAnimating(true, "adjustCardPositions");
                Laya.Tween.to(cardItem, {
                    x: targetX,
                    y: targetY
                }, animationTime, Laya.Ease.strongOut, Laya.Handler.create(this, () => {
                    cardItem.setAnimating(false, "adjustCardPositions");
                    cardItem.setInitPos(targetX, targetY);
                    onAnimationComplete();
                }));
            } else {
                // 直接设置位置
                cardItem.x = targetX;
                cardItem.y = targetY;
                cardItem.setInitPos(targetX, targetY);
                onAnimationComplete();
            }
        }
    }

    /**
     * 计算居中等间距的卡牌位置（用于特效补牌）
     * @param cardWidth 卡牌宽度
     * @param totalCards 总卡牌数
     * @param spacing 卡牌间距
     * @returns 位置数组
     */
    public calculateCenteredCardPositions(cardWidth: number, totalCards: number, spacing: number = 60): number[] {
        const positions: number[] = [];
        const totalWidth = (totalCards - 1) * spacing;
        const startX = -totalWidth / 2;

        for (let i = 0; i < totalCards; i++) {
            positions[i] = startX + i * spacing;
        }

        return positions;
    }



}