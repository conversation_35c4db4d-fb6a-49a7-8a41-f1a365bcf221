module yalla.view.jackaro {
    /**
     * 杰克罗抽牌 牌
     */
    export class JackaroPickUpCardItem extends ui.jackaro.item.cardItemUI {
        private _data: JackaroCardDataInterface;
        private _operatorState: _operatorState = _operatorState.DEFAULT;      //0默认状态  1可以出牌  -1不可出牌
        private _showState: JackaroCardItemState = JackaroCardItemState.IN_HAND;      //0默认状态  1显示  -1不显示
        private skin_id: number = JackaroCardItem.Default_Skin_Id;
        private _skinIsLoaded: boolean = false;
        public static normalScale: number = 1;
        public static otherScale: number = 0.3;
        public static pushScale: number = 0.6;
        private curShowIdx: number;
        private curOpPoker: yalla.data.jackaro.Poker;
        private clickCallback: Function;
        constructor() {
            super();
            this.initUI();
            // this.initEvent();
        }

        private initUI(): void {

        }

        public initEvent(): void {
            this.on(Laya.Event.CLICK, this, this.onClick);
        }

        public removeEvent(): void {
            this.off(Laya.Event.CLICK, this, this.onClick);
        }

        public setShowState(state: JackaroCardItemState) {
            this._showState = state;
            switch (state) {
                case JackaroCardItemState.IN_HAND:
                    this.scale(JackaroCardItem.otherScale, JackaroCardItem.otherScale);
                    this.setSkin();
                    break;
                case JackaroCardItemState.ON_TABLE:
                    this.setSkin();
                    break;
                case JackaroCardItemState.IN_PLAY:
                    this.setSkin();
                    break;
                case JackaroCardItemState.INVALID:
                    //置灰
                    this.cardSkinImg.gray = true;
                    break;
            }
        }
        public setShowIdx(idx: number) {
            this.curShowIdx = idx;
        }
        public setOpPoker(poker: yalla.data.jackaro.Poker) {
            this.curOpPoker = poker;
        }

        private onClick(e: Laya.Event): void {
            e.stopPropagation();
            if (!yalla.data.jackaro.JackaroUserService.instance) return;
            if (this.clickCallback) {
                this.clickCallback(this.data.poker, this.curShowIdx);
                this.clickCallback = null;
                return;
            }
            JackaroCardManger.instance.setDrawnPokerIndex(this.curShowIdx);
            yalla.data.jackaro.JackaroUserService.instance.sendDrawNextPlayerPoker(this.curOpPoker, this.curShowIdx);
            (TipsViewManager.getInstance().getTipView(TipViewType.PICKUP_CARD) as PickUpCardView).drawPokerComplete();
        }
        public setClickCallback(callback: Function) {
            this.clickCallback = callback;
        }

        public setCardData(data: JackaroCardDataInterface, playCardFunc: Function = null): void {
            this._data = data;
            if (this._data.idx == yalla.Global.Account.idx) {
                this.initEvent();
            }
            this.setShowState(data.showState);
        }
        public setSkinId(skin_id: number): void {
            this.skin_id = skin_id;
        }
        private setSkin() {
            this.backBg && (this.backBg.visible = true);
            //更新牌背资源
            if (this.skin_id != JackaroCardItem.Default_Skin_Id) {
                if (this._skinIsLoaded) {
                    if (this.backBg) this.backBg.skin = `${yalla.File.filePath}j_pk_${this.skin_id}.png`;
                } else {
                    yalla.event.YallaEvent.instance.once(`j_pk_${this.skin_id}.png`, this, () => {
                        this._skinIsLoaded = true;
                        if (this.destroyed) return;
                        this.backBg.skin = `${yalla.File.filePath}j_pk_${this.skin_id}.png`;
                    });
                    yalla.File.getFileByNative(`j_pk_${this.skin_id}.png`);
                }
            } else {
                if (this.backBg) this.backBg.skin = `jackaroCard/card_back_52000.png`;
            }
        }

        public get data(): JackaroCardDataInterface {
            return this._data;
        }
        public getCardWidth(): number {
            if (this._showState == JackaroCardItemState.IN_HAND) {
                return this.cardSkinImg.width * JackaroCardItem.normalScale;
            }
            return this.cardSkinImg.width;
        }

        public clear(): void {
            this.removeEvent();
            this._operatorState = 0;
            this._showState = JackaroCardItemState.IN_HAND;
            this._skinIsLoaded = false;
            this.skin_id = JackaroCardItem.Default_Skin_Id;
            this.curOpPoker = 0;
            if (this.backBg && !this.backBg.destroyed) {
                this.backBg.skin = `jackaroCard/card_back_52000.png`;
            }
            this.clickCallback = null;
        }
    }
}