/// <reference path="../../../view/ludo/ludoData/LudoGrid.ts" />
/// <reference path="../data/game/JackaroUserService.ts" />
/// <reference path="TransferGridProcessor.ts" />

// 类型定义
interface Station {
    area: number;
    gridPosition: number;
    gridColor?: number;
    position?: number;
}

interface port {
    x: number;
    y: number;
}

/**
 * 网格数据转换器 - 统一数据转换管道
 * 优化架构设计：将数据转换逻辑从业务逻辑中分离，提高代码的可维护性和可测试性
 */
class GridDataTransformer {
    private static _instance: GridDataTransformer;
    public static get instance() {
        return this._instance || (this._instance = new GridDataTransformer());
    }

    /**
     * 清理单例实例 - 用于内存管理和测试环境重置
     */
    public static clearInstance(): void {
        this._instance = null;
        // 同时清理依赖的单例
        TransferGridProcessor.clearInstance();
    }

    private transferProcessor = TransferGridProcessor.instance;

    /**
     * 转换为普通游戏结果项数据
     * @param pathGridData 路径网格数据
     * @returns 游戏结果项数据
     */
    public transformToPlayResultItemData(pathGridData: EnhancedPathGridData): yalla.data.jackaro.PlayResultItemDataInterface {
        const fromGrid = pathGridData.fromGrid;
        let toGrid = pathGridData.toGrid;
        let transferGrid = null;

        // 处理传送点逻辑
        if (pathGridData.transferInfo && pathGridData.transferInfo.isTransfer) {
            // 构建transferGrid为传送点本身
            transferGrid = pathGridData.transferInfo.transferGrid;
            // 更新toGrid为传送后的目标格子
            toGrid = pathGridData.transferInfo.transferTarget || toGrid;
        }

        return {
            fromGrid: fromGrid,
            toGrid: toGrid,
            transferGrid: transferGrid,
            kill: !!pathGridData.killChessInfo
        };
    }

    /**
     * 转换为复杂玩法K预览数据
     * @param pathGridData 路径网格数据
     * @returns 复杂玩法K预览数据
     */
    public transformToComplexPlayPokerKPreviewData(pathGridData: EnhancedPathGridData): yalla.data.jackaro.ImpactChessResultInterface {
        let toGrid = pathGridData.toGrid;
        let transferGrid = null;

        // 处理传送点逻辑
        if (pathGridData.transferInfo && pathGridData.transferInfo.isTransfer) {
            // 构建transferGrid为传送点本身
            transferGrid = pathGridData.transferInfo.transferGrid;
            // 更新toGrid为传送后的目标格子
            toGrid = pathGridData.transferInfo.transferTarget || toGrid;
        }

        const complexPlayPokerKPreviewData: yalla.data.jackaro.ImpactChessResultInterface = {
            fromGrid: pathGridData.fromGrid,
            toGrid: toGrid,
            transferGrid: transferGrid,
            kill: false,
            impactedChess: []
        };

        // 处理击杀棋子信息
        if (pathGridData.chesses.length > 0) {
            complexPlayPokerKPreviewData.kill = true;
            for (let index = 0; index < pathGridData.chesses.length; index++) {
                const chessInfo = pathGridData.chesses[index];
                const chess = JackaroChessManager.instance.getChessById(chessInfo);
                if (chess) {
                    const fromGrid = {
                        grid: {
                            index: chess.wayGrid.gridPosition,
                            color: chess.wayGrid.color,
                            type: 0
                        },
                        chess: {
                            idx: chess.beyondIdx,
                            order: chess.id,
                            color: chess.color
                        }
                    };
                    complexPlayPokerKPreviewData.impactedChess.push({
                        from: fromGrid,
                        bornIndex: chess.id
                    });
                }
            }
        }

        return complexPlayPokerKPreviewData;
    }

    /**
     * 增强路径网格数据，添加传送信息
     * @param basePathGridData 基础路径网格数据
     * @param endGrid 终点格子
     * @returns 增强的路径网格数据
     */
    public enhancePathGridData(
        basePathGridData: {
            curChessInfo: string;
            hopeStepNum: number;
            stepNum: number;
            fromGrid: yalla.data.jackaro.GridDataInterface;
            toGrid: yalla.data.jackaro.GridDataInterface;
            chesses: Array<string>;
            killChessInfo: string;
        },
        endGrid: LudoGrid
    ): EnhancedPathGridData {
        const enhanced: EnhancedPathGridData = {
            ...basePathGridData
        };

        // 处理传送点逻辑
        const transferResult = this.transferProcessor.processTransfer(endGrid, basePathGridData.chesses);

        if (transferResult.isTransfer) {
            // 更新击杀信息和路径棋子信息
            enhanced.killChessInfo = transferResult.killChessInfo;
            enhanced.chesses = transferResult.updatedPathChessInfo;

            // 添加传送信息
            enhanced.transferInfo = {
                isTransfer: true,
                transferGrid: this.transferProcessor.createTransferGridData(
                    endGrid,
                    basePathGridData.toGrid.chess
                ),
                transferTarget: transferResult.transferTarget ?
                    this.transferProcessor.createTargetGridData(
                        transferResult.transferTarget,
                        basePathGridData.toGrid.chess
                    ) : undefined
            };
        } else {
            enhanced.transferInfo = {
                isTransfer: false
            };
        }

        return enhanced;
    }

    /**
     * 批量转换路径网格数据为游戏结果数据
     * @param pathGridDataArr 路径网格数据数组
     * @returns 游戏结果数据记录
     */
    public batchTransformToPlayResultData(
        pathGridDataArr: Array<EnhancedPathGridData>
    ): Record<string, yalla.data.jackaro.PlayResultDataInterface> {
        const result: Record<string, yalla.data.jackaro.PlayResultDataInterface> = {};

        for (const pathGridData of pathGridDataArr) {
            const stepNum = pathGridData.hopeStepNum;
            const playResultItemData = this.transformToPlayResultItemData(pathGridData);

            if (result[stepNum] && result[stepNum].result) {
                result[stepNum].result.push(playResultItemData);
            } else {
                result[stepNum] = { result: [playResultItemData] };
            }
        }

        return result;
    }

    /**
     * 批量转换路径网格数据为复杂玩法K预览数据
     * @param pathGridDataArr 路径网格数据数组
     * @returns 复杂玩法K预览数据
     */
    public batchTransformToComplexPlayPokerKPreviewData(
        pathGridDataArr: Array<EnhancedPathGridData>
    ): yalla.data.jackaro.ComplexPlayKingResultInterface {
        const complexPlayKingResult: yalla.data.jackaro.ComplexPlayKingResultInterface = {
            flyData: null,
            impactResult: []
        };

        // 遍历路径网格数据数组
        for (const pathGridData of pathGridDataArr) {
            // 如果fromGrid.grid.index > 100，说明是起飞数据，填充到flyData中
            if (pathGridData.fromGrid && pathGridData.fromGrid.grid.index > 100) {
                if (!complexPlayKingResult.flyData || (complexPlayKingResult.flyData.fromGrid && complexPlayKingResult.flyData.fromGrid.grid.index > pathGridData.fromGrid.grid.index)) {
                    complexPlayKingResult.flyData = this.transformToPlayResultItemData(pathGridData);
                }
            } else {
                // 其他情况填充到impactResult中
                const impactData = this.transformToComplexPlayPokerKPreviewData(pathGridData);
                complexPlayKingResult.impactResult.push(impactData);
            }
        }

        return complexPlayKingResult;
    }
}