
class JKCHtailing {
    public static _ins: J<PERSON>CHtailing = null;
    public static get ins() {
        return this._ins || (this._ins = new JKCHtailing());
    }
    private parentNode: Laya.Box = null;
    public init(parentNode: Laya.Box) {
        this.parentNode = parentNode
    }
    private getFire(): Laya.Image {
        var img = new Laya.Image();
        img.name = "jack_tailing_item";
        img.skin = "jackaro/fire2.png";
        img.anchorX = img.anchorY = 0.5;
        return img
    }
    private getItem(): Laya.Image {
        return Laya.Pool.getItemByCreateFun("jack_tailing", this.getFire, this);
    }
    private releaseItem(item: Laya.Image) {
        if (item) {
            Laya.Tween.clearAll(item);
            item.removeSelf();
            Laya.Pool.recover("jack_tailing", item);
        }
    }
    public addTailing(pos: { x: number, y: number }) {
        var tailing = this.getItem();
        if (tailing && this.parentNode) {
            tailing.scale(1, 1);
            this.parentNode.addChild(tailing);
            tailing.pos(pos.x, pos.y);
            tailing.rotation = Math.random() * 360;
            Laya.Tween.to(tailing, { scaleX: 0, scaleY: 0 }, 400, null, Laya.Handler.create(this, this.releaseItem, [tailing]), 200);
        }
    }
    public releaseTailing() {
        if (this.parentNode) {
            for (let i = this.parentNode.numChildren - 1; i >= 0; i--) {
                const child = this.parentNode.getChildAt(i) as Laya.Image;
                if (child && child.name == "jack_tailing_item") {
                    this.releaseItem(child);
                }
            }
        }
    }
    public clear() {
        this.releaseTailing();
        Laya.Pool.clearBySign("jack_tailing");
        this.parentNode = null;
        JKCHtailing._ins = null;
    }
}