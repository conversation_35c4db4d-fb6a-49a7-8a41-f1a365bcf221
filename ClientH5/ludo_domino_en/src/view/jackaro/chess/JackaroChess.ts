
/**
 * 棋子移动结束状态枚举
 */
enum ChessMoveEndStatus {
    /** 正常 */
    Normal = 0,
    /** 被击杀 */
    Killed = 1,
    /** 击杀别人 */
    KillOthers = 2
}

/**
 * 棋子移动失败原因枚举
 */
enum ChessMoveFailReason {
    /** 移动成功 */
    SUCCESS = 0,
    /** 无效的目标格子 */
    INVALID_GRID = 1,
    /** 目标位置与当前目标格子相同 */
    SAME_AS_GOAL_GRID = 2,
    /** 目标位置与当前所在格子相同 */
    SAME_AS_WAY_GRID = 3,
    /** 快速模式胜利状态下在家中无法移动 */
    FAST_MODE_WIN_IN_HOME = 4,
    /** 移动步数超过13步限制 */
    MOVE_STEPS_EXCEED_LIMIT = 5
}

/**
 * 杰克罗棋子
 */
class JackaroChess extends ui.jackaro.sub.jackarochessUI {
    public _color: number;
    private _homeGrid: LudoGrid = null;//出生点
    private _lastMoveStartGrid: LudoGrid = null;//上一次移动开始格子
    private _wayGrid: LudoGrid = null;//当前所在的格子
    private _goalGrid: LudoGrid = null;//移动要去的目标格子,当移动结束的时候 _goalGrid和_wayGrid是同一个格子
    public id: number = null;
    /**所属 */
    private _beyondIdx: number = null;
    public stepNum: number = 0; //当前棋子的移动步数
    private moving: boolean = false;
    public isEndChess: boolean = false;
    private timeLine: Laya.TimeLine;
    private takeOffMoveTimeLine: Laya.TimeLine;
    private _choose: boolean = false
    private skin_id: number = null;
    private _skinIsLoaded: boolean = false;
    // private _soundTimeOut = null;
    private _tw: Laya.Tween = null;
    private _chessData: any = null;
    private _lineGirds: LudoGrid[] = [];
    public moveType: yalla.data.jackaro.JackarooMoveType = null;
    public idKey: string = null;
    public expPlayer: number = null;
    public killedIdx: number = null;
    public debugText: Laya.Text = null;
    private _exchangeChess: boolean = false;
    private _exchangeChessImg: Laya.Image = null;
    private isFirstTimeEnterEnd: boolean = false;
    public _playEmoji: boolean = false;//是否播放棋子表情
    private _isFastModeWinGame: boolean = false;//用于快速模式，标记是否胜利，这个棋子需要返回到起飞点
    private _isPerfectEnterEndPoint: boolean = false; //标记是否完美进入终点
    private _isNormalEnterEndPoint: boolean = false; //标记进入终点
    static moveTime: number = 165;//170;//155; //棋子移动时间 TODO 原本棋子移动的时间是160毫秒，这个时间是换算动画的时间，产品说是需要在棋子将要落下时播放震动和声音，所以间隔改成145
    static realMoveTime: number = 160;//165;//150; //160棋子实际移动一格的时长
    static chessMoveScale: number = 1;//棋子移动缩放比例,原始小大
    static _normalScale: number = 0.86;//正常大小
    static chessMoveSlowSpeedFactor: number = 1.3; //最后两格移动速度放慢系数
    static moveHomeTime: number = 200;//棋子移动到出生点的时间
    static transferBeginGridPosition: number = 21;//传送的开始grid位置
    static transferEndGridPosition: number = 7;//传送的结束grid位置
    static chessHeight: number = 40;//棋子高度
    static transferMoveTime: number = 750;//传送移动时间

    public isAddTrustExp: boolean = false;
    /**
     * 是否是交换棋子第二次移动，第二次移动之后才手动更新预览
     */
    private isExchangeSecondMove: boolean = false;
    /**
     * 棋子所在的层级
     */
    private chessBoxLayer: Laya.Box = null;
    /**
     * 棋子移动动画所在的层级
     */
    private aniBoxLayer: Laya.Box = null;
    /*
    * 棋子移动结束后的状态，是被击杀移动还是击杀别人
    */
    public moveEndStatus: ChessMoveEndStatus = ChessMoveEndStatus.Normal;
    private logTag: string = "JackaroChess :";
    private _resName: string;
    public get resName(): string {
        return this._resName;
    }
    public set resName(value: string) {
        this._resName = value;
    }
    set playEmoji(val: boolean) {
        this._playEmoji = val;
    }
    get playEmoji(): boolean {
        return this._playEmoji;
    }
    getMoveEndStatus(): ChessMoveEndStatus {
        return this.moveEndStatus;
    }
    setMoveEndStatus(val: ChessMoveEndStatus) {
        this.moveEndStatus = val;
    }
    constructor(skinId: number, color: number) {
        super();
        this.color = color;
        this.skinId = (!!skinId && skinId > 50000) ? skinId : 50000;
        this.chessImg.visible = true;
        this.timeLineInit();

    }
    set skinId(val: number) {
        this.skin_id = val;
        this.setSkin("chess");
    }
    get skinId(): number {
        return this.skin_id;
    }
    set choose(boo: boolean) {
        this._choose = boo;
        if (!this.choose) {
            // this.updateChooseCircleAureole();
            this.removeClickEvent();
            JkChooseChessManager.ins.removeWidhIdKey(this.idKey);
        }
    }
    /**
     * 更新选择圈特效 和 点击事件
     */
    updateEventListener() {
        let boo = this._choose;

        this.removeClickEvent();
        if (boo) this.addClickEvent();
    }
    get choose(): boolean {
        return this._choose;
    }
    set isFastModeWinGame(boo: boolean) {
        this._isFastModeWinGame = boo;
    }
    get isFastModeWinGame(): boolean {
        return this._isFastModeWinGame;
    }
    get isPerfectEnterEndPoint(): boolean {
        return this._isPerfectEnterEndPoint;
    }
    get isNormalEnterEndPoint(): boolean {
        return this._isNormalEnterEndPoint;
    }
    private addClickEvent() {
        if (this.clickArea) {
            // this.updateClickArea();
            this.clickArea.on("click", this, this.clickChess);
        }
    }
    /**
     * 更新棋子点击区域大小
     */
    // private updateClickArea() {
    //     if (JackaroChessManager.instance.isExchangeChessMove()) {
    //         this.clickArea.size(60, 60);
    //     } else {
    //         this.clickArea.size(40, 40);
    //     }
    // }
    public setAniBoxLayer(aniBoxLayer: Laya.Box) {
        this.aniBoxLayer = aniBoxLayer;
    }
    public setChessBoxLayer(chessBoxLayer: Laya.Box) {
        this.chessBoxLayer = chessBoxLayer;
    }
    /**
     * 棋子开始移动时提升层级，提升到ani_box层
     */
    public chessStartMoveLayerUpdate(isKill: boolean = this.moveType == yalla.data.jackaro.JackarooMoveType.MOVE_BE_HIT) {
        if (this.chessBoxLayer && !this.destroyed && this.chessBoxLayer.contains(this)) {
            this.chessBoxLayer.setChildIndex(this, this.chessBoxLayer.numChildren - (isKill ? 2 : 1));
        }
    }
    /**
     * 棋子移动结束时恢复层级
     */
    // public chessEndMoveLayerUpdate() {
    //     if (this.chessBoxLayer && this.aniBoxLayer) {
    //         this.aniBoxLayer.removeChild(this);
    //         this.chessBoxLayer.addChild(this);
    //     }
    // }

    public removeClickEvent() {
        if (this.clickArea) {
            this.clickArea.off("click", this, this.clickChess);
        }
    }
    private clickChess(e: Laya.Event) {
        e && e.stopPropagation();
        JackaroChessManager.instance.userActiveClickChess(this);
    }
    private timeLineInit() {
        this.timeLine = new Laya.TimeLine();
        this.timeLine.on(Laya.Event.COMPLETE, this, this.moveComplete);
        this.timeLine.on(Laya.Event.LABEL, this, this.onLabel);
    }
    private onLabel(index: string) {
        if (!index) return;
        if (index.indexOf(":") > -1) {
            let moveIndex = Number(index.split(":")[1]);
            let grid = this._lineGirds[moveIndex];
            if (grid) {
                JackaroChessManager.instance.handleMoveOneStep(this, grid);
                let isLastStep = (moveIndex == (this.stepNum - 1));
                if (JackaroChessManager.instance.isPerfectEnterEndPoint2(this, grid) && isLastStep) {
                    this._isPerfectEnterEndPoint = true;
                    yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_Chess_Move_PerfectEnterEndPoint, { chess: this, grid: this._lineGirds[moveIndex], isPerfectEnter: true, beyondIdx: this.beyondIdx });
                } else if (grid && grid.inEndArea && isLastStep) {
                    if (JackaroChessManager.instance.isNewExpPlayLogic()) {
                        let startGrid = this._lineGirds[0];
                        if (startGrid) {
                            startGrid = JackaroBoardData.getGridByName(startGrid.lastName);
                        }
                        if (startGrid && !startGrid.inEndArea) {
                            this._isNormalEnterEndPoint = true;
                            yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_Chess_Move_PerfectEnterEndPoint, { chess: this, grid: this._lineGirds[moveIndex], isPerfectEnter: false, beyondIdx: this.beyondIdx });
                        }
                    }
                }
            }
        }
    }
    private moveComplete() {
        if (this.destroyed) return;
        if (this.timeLine) {
            this.timeLine.reset();
        }
        if (this.ransferTimeLine) {
            this.ransferTimeLine.reset();
        }
        this.moveEnd();
    }
    public move(station: Station, step: number = 0, type: yalla.data.jackaro.JackarooMoveType = yalla.data.jackaro.JackarooMoveType.MOVE_NORMAL): ChessMoveFailReason {
        var grid: LudoGrid = JackaroBoardData.getGridByStation(station);
        // yalla.Debug.logMore(this.logTag + ` [${yalla.getTimeHMS()}]` + `move ${this.beyondIdx}-${this.id},type:${type},step:${step},---start:${JSON.stringify(this._goalGrid.station)}--end:${JSON.stringify(station)}`);
        if (!grid) return ChessMoveFailReason.INVALID_GRID;
        if (grid.atGrid(this._wayGrid.gridName)) {
            return ChessMoveFailReason.SAME_AS_WAY_GRID;
        }
        if (grid.atGrid(this._goalGrid.gridName)) {
            return ChessMoveFailReason.SAME_AS_GOAL_GRID;
        }

        if ((this.wayGrid.inHome || this._goalGrid.inHome) && this._isFastModeWinGame) {
            return ChessMoveFailReason.FAST_MODE_WIN_IN_HOME;
        }

        // let selfColor = JackaroGamePlayerManager.instance.getColorByIdx(this.beyondIdx);
        if (this.wayGrid.inHome && JackaroBoardData.getGridName(station) == (this.color + "_2")) {
            type = yalla.data.jackaro.JackarooMoveType.MOVE_TAKEOFF;
        }

        if ((this.wayGrid.inHome || this._goalGrid.inHome) && this._isFastModeWinGame) {
            return ChessMoveFailReason.FAST_MODE_WIN_IN_HOME;
        }
        this.isAddTrustExp = !JackaroChessManager.instance.isTrustNotExp();
        this.stepNum = 0;
        this.moveType = type;
        this.chessStartMoveLayerUpdate();
        this._wayGrid = this._goalGrid;
        this._lastMoveStartGrid = this._wayGrid;
        //step 等于是直接移动到目标点，如果不等于 0，是距离目标点之间的步数
        // if (step == 0) {
        this._goalGrid = grid;

        this.choose = false;
        this.moveEndStatus = ChessMoveEndStatus.Normal;
        switch (type) {
            case yalla.data.jackaro.JackarooMoveType.MOVE_HOME:
            // JackaroChessManager.instance.recordPathNeedRefresh(this);
            case yalla.data.jackaro.JackarooMoveType.MOVE_BE_HIT:
                JackaroChessManager.instance.removeChessBubble(this);
                if (yalla.Global.isFouce) {
                    this.moveHome(grid);
                } else {
                    this.moveEnd();
                }

                break;
            case yalla.data.jackaro.JackarooMoveType.MOVE_TAKEOFF:
                JackaroChessManager.instance.setKillChessCallBack(this);
                // JackaroChessManager.instance.recordPathNeedRefresh(this);
                if (yalla.Global.isFouce) {
                    this.takeOffMove(station);
                } else {
                    this.moveEnd();
                }
                break;
            case yalla.data.jackaro.JackarooMoveType.MOVE_FORWARD_TRANSFER:
            case yalla.data.jackaro.JackarooMoveType.MOVE_BACKWARD_TRANSFER:
                JackaroChessManager.instance.setKillChessCallBack(this);
                if (yalla.Global.isFouce) {
                    this.moveTransfer(type);
                } else {
                    this.moveEnd();
                }
                break;
            default:
                JackaroChessManager.instance.setKillChessCallBack(this);
                // JackaroChessManager.instance.recordPathNeedRefresh(this);
                if (yalla.Global.isFouce) {
                    let moveResult = this.lineEffectMove(step, type != yalla.data.jackaro.JackarooMoveType.MOVE_BACKWARD_WIND);
                    /** 异常移动超过13步 */
                    if (!moveResult) return ChessMoveFailReason.MOVE_STEPS_EXCEED_LIMIT;
                } else {
                    let lineGirds = JackaroChessManager.instance.getPathsPos(this.goalGrid, this._wayGrid, type != yalla.data.jackaro.JackarooMoveType.MOVE_BACKWARD_WIND, this.color);
                    if (step > 0) {
                        this.stepNum = step;
                        if (lineGirds[step - 1]) {
                            this._goalGrid = lineGirds[step - 1];
                        }
                    } else {
                        this.stepNum = lineGirds.length;
                    }
                    JackaroChessManager.instance.addcTurnAlreadyMoveChessInfoes(this, this.stepNum);
                    this.moveEnd();
                }
                break;

        }
        JackaroChessManager.instance.handleMoveChessBegin(this, type);
        return ChessMoveFailReason.SUCCESS;
    }
    private moveHome(grid: LudoGrid) {
        grid.port && this.jumpMove(grid.port);
    }
    public get goalStation(): Station { return this._goalGrid.station; }
    public set homeGrid(grid: LudoGrid) { this._homeGrid = grid; }
    public get homeGrid() { return this._homeGrid; }
    public get homeStation() { return this._homeGrid.station; }
    public get goalGrid() { return this._goalGrid; }
    public set goalGrid(grid: LudoGrid) { this._goalGrid = grid };
    public get wayGrid() { return this._wayGrid; }
    public get moveStartGrid() { return this._lastMoveStartGrid; }
    public get wayStation(): Station { return this._wayGrid.station; }
    set color(color: number) { this._color = color; }
    get color(): number { return this._color; }
    setChessData(idx: number, val: any) {
        // if (val.gridPosition == -6) val.gridPosition = 95 - this.homeStation.gridPosition;
        this._beyondIdx = idx;
        this._chessData = val;
        this.id = val.order;
        this.name = String(this.id);
        var grid: LudoGrid = JackaroBoardData.getGridByStation(val);
        // if (!grid) {
        //     yalla.Debug.logMore(this.logTag + "JackaroChess.setChessData:", val);
        // }
        this._lastMoveStartGrid = null;
        this._wayGrid = this._goalGrid = grid;
        // this.clearTimeLine();
        !this.moving && this.moveEnd();
        if (this.isFirstTimeEnterEnd) this.isFirstTimeEnterEnd = grid.inEndArea;
        this._isFastModeWinGame = false;
        this.idKey = `${idx}_${this.id}`;
        this.text_chess_tag.visible = false;
    }
    get data(): any { return this._chessData; }
    get beyondIdx(): number { return this._beyondIdx; }

    public setChessTag(tag: string) {
        if (tag == "") {
            this.text_chess_tag.visible = false;
        } else {
            this.text_chess_tag.skin = `jackaro/n${tag}.png`;
            this.text_chess_tag.visible = true;
        }
    }
    private setSkin(name: string) {
        this.resName = name;
        this.chessImg.skin = `jackaro/${name}_50000_${this.color}.png`;
    }
    public reset() {
        this._lastMoveStartGrid = null;
        this._goalGrid = this._wayGrid = this._homeGrid;
        this.choose = false;
        let port: port = this._wayGrid.port;
        this.pos(port.x, port.y);
        this.clickArea.offAll();
    }
    public onBlur() {
        this.clearTimeLine();
    }

    private jumpMove(port: port, duration: number = JackaroChess.moveHomeTime) {//直接移动到目标点
        this.moving = true;
        this.choose = false;
        if (yalla.Global.isFouce) {
            this._tw = Laya.Tween.to(this, {
                x: port.x,
                y: port.y,
                update: null
            }, duration, Laya.Ease.linearInOut, Laya.Handler.create(this, () => {
                this._tw && this._tw.clear();
                this.moveEnd();
            }))
        } else {
            this.pos(port.x, port.y);
            this.moveEnd();
        }
    }
    /**
     * 是否在目标点
     * @returns 
     */
    public isAtGoalPos(station: Station): boolean {
        let grid: LudoGrid = JackaroBoardData.getGridByStation(station);
        this.moveType = yalla.data.jackaro.JackarooMoveType.MOVE_EXCHANGE;
        if (!grid) return false;

        if (grid.atGrid(this._goalGrid.gridName)) {
            return true;
        }
        if (grid.atGrid(this._wayGrid.gridName)) {
            return true;
        }
        return false;
    }
    public moveExchangeChess(station: Station, duration: number = JackaroChess.moveHomeTime, isSecondMove: boolean = false): boolean {
        var grid: LudoGrid = JackaroBoardData.getGridByStation(station);
        this.moveType = yalla.data.jackaro.JackarooMoveType.MOVE_EXCHANGE;
        if (!grid) return false;

        if (grid.atGrid(this._goalGrid.gridName)) {
            return false;
        }
        if (grid.atGrid(this._wayGrid.gridName)) {
            return false;
        }
        this.moving = true;
        this._wayGrid = this._goalGrid;
        this._lastMoveStartGrid = this._wayGrid;
        // JackaroChessManager.instance.recordPathNeedRefresh(this);
        this._goalGrid = grid;
        this.choose = false;
        if (!yalla.Global.isFouce) { this.moveEnd(); return; }
        this.setExchangeChess(false);
        this.scale(JackaroChess.chessMoveScale, JackaroChess.chessMoveScale);
        this.visible = false;
        this.isExchangeSecondMove = isSecondMove;
        JackaroChessManager.instance.handleMoveChessBegin(this, yalla.data.jackaro.JackarooMoveType.MOVE_EXCHANGE);
        EffectAniManager.instance.playExchangeEffAni(this, station, duration, () => {
            this.visible = true;
            this.scale(JackaroChess._normalScale, JackaroChess._normalScale);
            this.moveEnd();
        });
        return true;
    }
    /**
   * 获取交换棋子移动时间
   * @param goalStation 目标站点
   */
    public getExchangeMoveTime(goalStation: Station): number {
        if (!goalStation) {
            return;
        }
        let startPos = new Laya.Point(this._goalGrid.port.x, this._goalGrid.port.y);
        let endPos = new Laya.Point(JackaroBoardData.getGridByStation(goalStation).port.x, JackaroBoardData.getGridByStation(goalStation).port.y);

        let distance = yalla.util.getDistanceNum(startPos, endPos);
        let moveSpeed = yalla.data.jackaro.JackaroGameConfig.JackaroChessExchangeSpeed;
        //目前根据美术廖祥义标注的交换距离，一共有5中交换移动距离 分别：
        //  666  158  344 678 480
        if (distance <= 95) {
            //距离4格时，两个棋子之间的距离是95，所以当距离小于95时，移动速度放慢
            moveSpeed *= 0.5;
        } else if (distance <= 158) {
            moveSpeed *= 0.7;
        } else if (distance <= 344) {
            moveSpeed *= 1;
        } else if (distance <= 480) {
            moveSpeed *= 1.1;
        } else if (distance <= 678) {
            moveSpeed *= 1.3;
        }
        //移动时间
        var moveTime = distance / moveSpeed;
        return moveTime;
    }
    public calculateExchangeMoveTime(distance) {
        // 基础时间参数
        const baseDistance = 360;
        const baseTime = 0.8;
        const minTime = 0.4;
        const maxTime = 1.2;

        // 非线性调整系数
        const exponent = 0.7; // 越小，时间变化随距离增长越慢

        // 计算时间（非线性插值）
        let time;
        if (distance > baseDistance) {
            // 超过基础距离时，使用非线性函数减缓时间增长
            const normalizedDistance = (distance - baseDistance) / baseDistance;
            const ratio = Math.pow(normalizedDistance, exponent);
            time = baseTime + ratio * (maxTime - baseTime);
        } else {
            // 小于基础距离时，时间从0.8s减少到最少0.4s
            const normalizedDistance = (baseDistance - distance) / baseDistance;
            const ratio = Math.pow(normalizedDistance, exponent);
            time = baseTime - ratio * (baseTime - minTime);
        }
        // 确保时间在有效范围内
        return Math.max(minTime, Math.min(time, maxTime));
    }
    /*
    * 起飞
    */
    public takeOffMove(station: Station) {//起飞移动
        var grid: LudoGrid = JackaroBoardData.getGridByStation(station);
        if (!grid) return;
        let port: port = grid.port;
        var bubble = this.getChildByName("bubble");
        bubble && bubble.removeSelf();
        this.moving = true;
        this._isFastModeWinGame = false;
        this.stepNum = 0;
        if (yalla.Global.isFouce) {
            JackaroChessManager.instance.addcTurnAlreadyMoveChessInfoes(this, 0);
            Laya.timer.once(150, this, () => {
                this.scale(JackaroChess.chessMoveScale, JackaroChess.chessMoveScale);
                if (!this.takeOffMoveTimeLine) {
                    this.takeOffMoveTimeLine = new Laya.TimeLine();
                    this.takeOffMoveTimeLine.on(Laya.Event.COMPLETE, this, this.moveComplete);
                    this.takeOffMoveTimeLine.on(Laya.Event.LABEL, this, this.onTakeOffMoveLabel);
                } else {
                    this.takeOffMoveTimeLine.reset();
                }
                // let totalTime: number = 466;//466 * 0.8;//根据动效参数总帧数算出需要 600 毫秒，演示视频是 30 帧每秒。动效参数一共是18 帧，计算 18除以 30，然后乘1000，算出总时长
                // let offsetTime: number = 66;//根据动效实际表现换算,播放整个动效结束需要 666 毫秒，误差是 66 毫秒
                let actureTotalTime = 300;//totalTime * 0.6 - offsetTime;
                this.takeOffMoveTimeLine.addLabel("0", 0).to(this, { scaleX: 1, scaleY: 1 }, 0);
                this.takeOffMoveTimeLine.addLabel("1", 0).to(this, { x: port.x, y: port.y, scaleX: 1.35, scaleY: 1.35 }, actureTotalTime, null);
                this.takeOffMoveTimeLine.addLabel("2", 0).to(this, { scaleX: 1, scaleY: 1 }, 100);
                this.takeOffMoveTimeLine.addLabel("3", 0).to(this, { scaleX: JackaroChess._normalScale, scaleY: JackaroChess._normalScale }, 166);
                // this.takeOffMoveTimeLine.addLabel("0", 0).to(this, { scaleX: 1, scaleY: 1 }, 0);
                // this.takeOffMoveTimeLine.addLabel("1", 0).to(this, port, actureTotalTime, null);
                // this.takeOffMoveTimeLine.addLabel("2", 0).to(this, { scaleX: 1.35, scaleY: 1.35 }, 150);
                // this.takeOffMoveTimeLine.addLabel("3", 0).to(this, { scaleX: 1, scaleY: 1 }, 100);
                this.takeOffMoveTimeLine.play("0", false);
            });
        } else {
            this.moveEnd();
        }
    }
    private onTakeOffMoveLabel(label: string) {
        //产品的意思是起飞动作还没完成时，就需要播放动效表现，所以需要提前播放动效
        if (label == "2") {
            JackaroChessManager.instance.playTakeOffSpineAni(this);
        }
    }
    private loopTimer: LoopTime = null;
    private ransferTimeLine: Laya.TimeLine = null;
    private moveTransfer(moveType: yalla.data.jackaro.JackarooMoveType) {
        var gridName = this._wayGrid.station.area + "_21";
        var grid = JackaroBoardData.getGridByName(gridName);
        this._lineGirds = JackaroChessManager.instance.getPathsPos(grid, this._wayGrid, moveType != yalla.data.jackaro.JackarooMoveType.MOVE_BACKWARD_TRANSFER, this.color);
        var len = this._lineGirds.length;
        var bezier = JackaroBoardData.getBezier(gridName);
        this.moving = true;
        if (len > 0) {
            this.stepNum = this._lineGirds.length;
            if (!this.ransferTimeLine) {
                this.ransferTimeLine = new Laya.TimeLine();
                this.ransferTimeLine.on(Laya.Event.LABEL, this, this.onLabel);
            }
            this.ransferTimeLine.off(Laya.Event.COMPLETE, this, this.moveBezier);
            this.scale(JackaroChess.chessMoveScale, JackaroChess.chessMoveScale);
            var lastGrid = this.wayGrid;
            for (var i = 0; i < len; i++) {
                var grid: any = this._lineGirds[i];
                if (grid) {
                    if (i == len - 1) {
                        var x = grid.port.x, y = grid.port.y;
                        var diffPort = yalla.util.calculateDifference(grid.port, lastGrid.port);
                        this.ransferTimeLine.addLabel("2:" + i.toString(), 0)
                            .to(this, { x: diffPort.x, y: diffPort.y, scaleX: 1.2, scaleY: 1.2 }, JackaroChess.realMoveTime / 2)
                        this.ransferTimeLine.addLabel("2:" + len.toString(), 0).to(this, { scaleX: 1, scaleY: 1, x, y }, JackaroChess.realMoveTime / 2)
                    } else {
                        this.ransferTimeLine.addLabel("2:" + i.toString(), 0).to(this, grid.port, JackaroChess.realMoveTime);
                    }
                    lastGrid = grid;
                }
            }
            // this.ransferTimeLine.addLabel("2:" + len.toString(), 0).to(this, this.goalGrid.port, 1000);
            this.ransferTimeLine.once(Laya.Event.COMPLETE, this, this.moveBezier, [bezier]);
            this.ransferTimeLine.play("2:0", false);
            this.playsound(len);
            JackaroChessManager.instance.addcTurnAlreadyMoveChessInfoes(this, this.stepNum);
        } else {
            this.moveEnd();
        }
        yalla.util.clogDBAmsg('10420', null, 1);
        return true;
    }
    private moveBezier(bezier: QuadraticBezier) {
        if (yalla.Global.isFouce) {
            // console.log("moveBezier", Date.now());
            if (!this.loopTimer) this.loopTimer = new LoopTime();
            this.loopTimer.stop();
            var tailing = JKCHtailing.ins;
            var addSine = true;
            this.loopTimer.start(JackaroChess.transferMoveTime, 50,
                Laya.Handler.create(this, (t) => {
                    if (yalla.Global.isFouce) {
                        var path = bezier.getPointAt(t);
                        this.pos(path.x, path.y);
                        addSine = !addSine;
                        addSine && tailing.addTailing(path)
                    }
                }, null, false),
                Laya.Handler.create(this, this.moveComplete));
        } else {
            this.moveEnd()
        }
    }
    private lineEffectMove(step: number, forward: boolean) {//直线有缩放效果的移动
        this.moving = true;
        this._lineGirds = JackaroChessManager.instance.getPathsPos(this.goalGrid, this._wayGrid, forward, this.color);

        if (this._lineGirds.length > 13) {
            yalla.Debug.logMore("lineEffectMove 13 以上");
            // return false;
        }
        if (this._lineGirds.length > 0 && this.timeLine) {
            this.scale(JackaroChess.chessMoveScale, JackaroChess.chessMoveScale);
            // this._soundTimeOut && clearTimeout(this._soundTimeOut);
            if (step == 0) {
                this.stepNum = this._lineGirds.length;
                for (let index = 0; index < this._lineGirds.length; index++) {
                    const element = this._lineGirds[index];
                    //最后两格时速度放慢
                    if (index >= this._lineGirds.length - 2) {
                        this.timeLine.addLabel("2:" + index, 0).to(this, element.port, JackaroChess.realMoveTime * JackaroChess.chessMoveSlowSpeedFactor);
                    } else {
                        this.timeLine.addLabel("2:" + index, 0).to(this, element.port, JackaroChess.realMoveTime);
                    }

                }
                this.playsound(this._lineGirds.length);
            } else {
                if (this._lineGirds[step - 1]) {
                    this._goalGrid = this._lineGirds[step - 1];
                }
                this.stepNum = step;
                for (let i = 0; i < step; i++) {
                    if (this._lineGirds[i]) {
                        const element = this._lineGirds[i];
                        //最后两格时速度放慢
                        if (i >= step - 2) {
                            this.timeLine.addLabel("2:" + i.toString(), 0).to(this, element.port, JackaroChess.realMoveTime * JackaroChess.chessMoveSlowSpeedFactor);
                        } else {
                            this.timeLine.addLabel("2:" + i.toString(), 0).to(this, element.port, JackaroChess.realMoveTime);
                        }
                    }
                }
                this.playsound(step);
            }
            this.timeLine.play("0", false);

            //添加校验数据，用于校验和服务器返回的走子信息是否一致，不一致的话，需要请求同步接口，主要为了解决临近托管时玩家出牌走子请求被服务器那边忽略 导致同时走托管走子和自行移动走子
            JackaroChessManager.instance.addCheckMoveChessData({
                grid: {
                    index: this._goalGrid.gridPosition,
                    color: this._goalGrid.color,
                },
                chess: {
                    idx: this.beyondIdx,
                    order: this.id,
                    color: this.color
                }
            });
            JackaroChessManager.instance.addcTurnAlreadyMoveChessInfoes(this, this.stepNum);
        } else {
            this.moveEnd();
        }
        return true;
    }
    // private findMidpoint(point1: { x: number, y: number }, point2: { x: number, y: number }): { x: number, y: number } {
    //     let midpoint = {
    //         x: (point1.x + point2.x) / 2,
    //         y: (point1.y + point2.y) / 2
    //     };
    //     return midpoint;
    // }
    /**
     * 是否向终点移动（出牌7，在快速模式，如果目标点是终点，移动棋子的第一个参数 operatedChess 传0，只传递firstdata，参考协议里面的字段：operatedChess）
     */
    public isMoveForwardToEnd(): boolean {
        if (this._goalGrid && this._goalGrid.station.gridPosition == -4) return true;
        return false;
    }
    public moveEnd(stepNum: number = this.stepNum) {//移动结束事件
        this.moving = false;
        //移动结束时不再重置步数 ，防止切到后台 读取stepNum 为0
        this._wayGrid = this._goalGrid;
        let port: port = this._goalGrid.port;
        this.pos(port.x, port.y);
        // yalla.Debug.logMore(this.logTag + `[${yalla.getTimeHMS()}]` + "JackaroChess.ts moveEnd: chessId->" + `${this.beyondIdx}-${this.id}` + "gridName:" + this._goalGrid.gridName + "inEnd:" + this._goalGrid.inEnd + "this.moveType:" + this.moveType);
        if (this._wayGrid.inHome) {
            this.scale(JackaroChess.chessMoveScale, JackaroChess.chessMoveScale);
        } else {
            this.scale(JackaroChess._normalScale, JackaroChess._normalScale);
        }
        JackaroChessManager.instance.handleMoveChessEnd(this, this.moveType);
        this.moveType = null;
        this.playEmoji = false;
        this.isExchangeSecondMove = false;
        this.parent && this.parent.setChildIndex(this, 0);
    }
    getIsMoving(): boolean {
        return this.moving;
    }
    setExchangeChess(boo: boolean) {
        this._exchangeChess = boo;
        if (boo != this._exchangeChess) {
            this._exchangeChess = boo;
        }
        if (!this._exchangeChessImg) {
            this._exchangeChessImg = new Laya.Image();
            this._exchangeChessImg.skin = "jackaro/img_exchange.png";
            this.box.addChild(this._exchangeChessImg);
            this._exchangeChessImg.anchorX = 0.5;
            this._exchangeChessImg.anchorY = 0.5;
            this._exchangeChessImg.pos(27, 25);
        }
        this._exchangeChessImg.visible = boo;
    }
    public clearTimeLine() {
        if (this.timeLine) {
            this.timeLine.pause();
            this.timeLine.reset();
        }
        if (this.takeOffMoveTimeLine) {
            this.takeOffMoveTimeLine.pause();
            this.takeOffMoveTimeLine.reset();
        }
        if (this.ransferTimeLine) {
            this.ransferTimeLine.pause();
            this.ransferTimeLine.reset();
        }
        // clearTimeout(this._soundTimeOut);
        this.moveEnd();
    }
    public playsound(n: number) {
        if (!n || !yalla.Global.isFouce) { return; }
        if (n <= 13) {
            yalla.Sound.playSound("Stone_Move" + n);
            return;
        }
        // n--;
        // yalla.Sound.playSound("Stone_Move");
        // this._soundTimeOut = setTimeout(() => {
        //     clearTimeout(this._soundTimeOut);
        //     this.playsound(n);
        // }, JackaroChess.moveTime);
    }
    // private phoneVibrate() {
    //     if (yalla.PhoneVibrate.isOpen()) {
    //         if (JackaroGamePlayerManager.instance.isMyActive() || yalla.Global.Account.idx == this.beyondIdx) {
    //             yalla.Native.instance.phoneVibrate();
    //         }
    //     }
    // }
    public getGlobalPos(): Laya.Point {
        return this.chessImg.localToGlobal(new Laya.Point(0, 0));
    }
    public getChessUiPos(): Laya.Point {
        return new Laya.Point(this.x, this.y);
    }
    public setIsFirstTimeEnterEnd(val: boolean) {
        this.isFirstTimeEnterEnd = val;
        this._isPerfectEnterEndPoint = false;
    }
    public getIsFirstTimeEnterEnd() {
        return this.isFirstTimeEnterEnd;
    }
    public clear() {
        this.offAll();
        Laya.timer.clearAll(this);
        if (this._exchangeChessImg) {
            this._exchangeChessImg.visible = false;
        }
        this._exchangeChess = false;
        if (this.timeLine) {
            this.timeLine.offAll()
            this.timeLine.pause();
            this.timeLine.destroy();
            this.timeLine = null;
        }
        if (this.takeOffMoveTimeLine) {
            this.takeOffMoveTimeLine.offAll()
            this.takeOffMoveTimeLine.pause();
            this.takeOffMoveTimeLine.destroy();
            this.takeOffMoveTimeLine = null;
        }
        if (this._tw) {
            this._tw.complete();
            this._tw.clear();
            this._tw = null;
        }
        // this._soundTimeOut && clearTimeout(this._soundTimeOut);
        if (this.displayedInStage) {
            this.removeSelf();
            this.destroy(true);
        }
        this.isFirstTimeEnterEnd = false;
        this._isFastModeWinGame = false;
        this.playEmoji = false;
        this._isPerfectEnterEndPoint = false;
        this._isNormalEnterEndPoint = false;
        // this.circleAureole && this.circleAureole.clear();
        this.removeClickEvent();
        this.scale(JackaroChess.chessMoveScale, JackaroChess.chessMoveScale);
        this.moveEndStatus = ChessMoveEndStatus.Normal;
        this._lastMoveStartGrid = null;
        // yalla.Debug.log('棋子 -------->>>>>> clear');
    }
}