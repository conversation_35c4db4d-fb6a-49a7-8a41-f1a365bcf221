
/**
 * 杰克罗棋子1v1模式冲突处理器
 * 专门处理1v1模式下的棋子移动冲突和穿越逻辑
 */
class JackaroChess1V1ConflictHandler {
    private chessManager: any; // JackaroChessManager实例的引用

    constructor(chessManager: any) {
        this.chessManager = chessManager;
    }

    /**
     * 处理1v1模式下的冲突移动
     * 当两个棋子移动发生冲突时，按照特定规则处理移动顺序
     * @returns {boolean} 是否成功处理冲突
     */
    public handle1V1ConflictMove(): boolean {
        // 检查是否有等待处理的扑克7棋子移动数据
        if (this.chessManager.collectPoker7WaitingChess.length < 2) {
            return false;
        }

        // 获取两个冲突的棋子数据
        const firstChessData = this.chessManager.collectPoker7WaitingChess[0];
        const secondChessData = this.chessManager.collectPoker7WaitingChess[1];
        if (!firstChessData || !secondChessData) {
            return false;
        }
        // 获取对应的棋子实例
        const firstChess = this.chessManager.getChessById(`${firstChessData.idx}_${firstChessData.order}`);
        const secondChess = this.chessManager.getChessById(`${secondChessData.idx}_${secondChessData.order}`);

        if (!firstChess || !secondChess) {
            yalla.Debug.logMore('handle1V1ConflictMove: 找不到对应的棋子');
            return false;
        }

        // 计算两个棋子的最终位置和移动时间
        const firstChessEndInfo = this.chessManager.calculateChessEndPositionAndTime(firstChess, firstChessData.number);
        const secondChessEndInfo = this.chessManager.calculateChessEndPositionAndTime(secondChess, secondChessData.number);

        // 检查是否移动到相同目标点
        if (!firstChessEndInfo || !secondChessEndInfo || !firstChessEndInfo.endGrid || !secondChessEndInfo.endGrid) {
            return false;
        }

        if (firstChessEndInfo.endGrid.gridPosition == JackaroChess.transferEndGridPosition && firstChess.wayGrid.color != firstChessEndInfo.endGrid.color) {
            let sortedChessData: any[] = [secondChessData, firstChessData];
            this.chessManager.executeOrderedChessMove(sortedChessData);
            // 执行排序后的棋子移动
            this.chessManager.isHasFreeMove = false;
            return true;
        } else {
            return false;
        }
    }

    // /**
    //  * 处理1v1模式下的穿越移动
    //  * 当棋子移动路径穿越传送点时的特殊处理逻辑
    //  * @returns {boolean} 是否成功处理穿越移动
    //  */
    // public handle1V1CutAcrossMove(): boolean {
    //     // 检查是否有等待处理的扑克7棋子移动数据
    //     if (this.chessManager.collectPoker7WaitingChess.length < 2) {
    //         return false;
    //     }
    //     // 获取两个棋子数据
    //     const firstChessData = this.chessManager.collectPoker7WaitingChess[0];
    //     const secondChessData = this.chessManager.collectPoker7WaitingChess[1];
    //     if (!firstChessData || !secondChessData) {
    //         return false;
    //     }
    //     // 获取对应的棋子实例
    //     const firstChess = this.chessManager.getChessById(`${firstChessData.idx}_${firstChessData.order}`);
    //     const secondChess = this.chessManager.getChessById(`${secondChessData.idx}_${secondChessData.order}`);

    //     if (!firstChess || !secondChess) {
    //         yalla.Debug.logMore('handle1V1CutAcrossMove: 找不到对应的棋子');
    //         return false;
    //     }

    //     // 检查第一个棋子是否会穿越传送点
    //     const firstEndInfo = this.chessManager.calculateChessEndPositionAndTime(firstChess, firstChessData.number);
    //     const secondEndInfo = this.chessManager.calculateChessEndPositionAndTime(secondChess, secondChessData.number);

    //     // 检查是否移动到相同目标点
    //     if (!firstEndInfo || !secondEndInfo) {
    //         return false;
    //     }
    //     //如果第一颗棋子是穿过传送点 ,第二颗棋子是大于7，而且是其它颜色 ，此时需要先移动第二颗棋子
    //     if (firstEndInfo && firstEndInfo.endGrid && secondEndInfo.endGrid && secondChessData.wayGrid && firstEndInfo.endGrid.gridPosition == JackaroChess.transferEndGridPosition
    //         && firstEndInfo.endGrid.color == secondEndInfo.endGrid.color && secondEndInfo.endGrid.gridPosition > JackaroChess.transferEndGridPosition && secondChessData.wayGrid.gridPosition <= JackaroChess.transferEndGridPosition) {
    //         this.chessManager.collectPoker7WaitingChess.reverse();
    //         this.chessManager.executeOrderedChessMove(this.chessManager.collectPoker7WaitingChess);
    //         this.chessManager.isHasFreeMove = false;
    //         return true;
    //     }
    //     return false;
    // }
}