
/**
 * 杰克罗棋子管理器
 */
class JackaroChessManager {
    static _instance: JackaroChessManager = null;
    static _runwayScale: number = 0.8;//跑道上的大小
    static _normalScale: number = 1;//正常大小
    static _pileScale: number = 0.6;//叠子时候的大小
    static _chooseScale: number = 1;//叠子时候的大小
    static _poker7Step: number = 7;
    static get instance() {
        return this._instance ? this._instance : this._instance = new JackaroChessManager();
    }
    public _pool = {};
    public _poolColor = {};

    // {{ YUM: [新增] - 存储最近的移动数据，用于检查previewRewardPoker }}
    private recentMoveResultData: any = null;
    // {{ YUM: [新增] - 累积previewRewardPoker状态，避免被覆盖 }}
    private accumulatedPreviewRewardPoker: boolean = false;
    private drawRewardAnimationEndTime: number = 0; // {{ YUM: [新增] - 抽牌奖励动效结束时间戳 }}

    private mapLayer: Laya.Box = null;
    private topAniLayer: Laya.Box = null;
    // private _gridMap: Record<string, yalla.data.jackaro.GridDataInterface> = {}; //属于当前玩家的格子,key:格子的索引,
    // private _curMoveChessID: string = "";
    // private _curMoveChessGoalGrid: yalla.data.jackaro.GridDataInterface = null;
    //针对出牌 7，如果移动了一个棋子后，剩余棋子最大可移动的步数
    // private _curMoveChessMaxStep: number = 0;
    private _curAlreadyMoveStep: Array<number> = [];
    private _curAlreadyMoveChessStepInfo: Array<string> = [];
    private commonResult: Record<string, yalla.data.jackaro.PlayResultDataInterface> = {};
    private impactResult: Array<yalla.data.jackaro.ImpactChessResultInterface>;
    private jackResult: yalla.data.jackaro.PlayJackResultDataInterface = null;
    private isHasFreeMove: boolean = false;
    private isExchangeChess: boolean = false;
    private recordExchangeChess: Array<{ idx: number, order: number, number: number }> = [];
    private curKilledChesses: Array<yalla.data.jackaro.ChessInterface> = [];

    private chessOrbitItem: Array<Laya.Image> = [];//轨道预览静态静态



    private chessPlayPokerKKillSpineAniArr: Array<SpineAniPlayer> = [];
    private chessPlayPokerKillSpineAniArr: Array<SpineAniPlayer> = [];
    private chessExchagneSpineAni: Array<SpineAniPlayer> = [];


    private chessExchagneEndSpineAni: Array<SpineAniPlayer> = [];
    private freeMoveChessIds: Array<{ idx: number, order: number }> = [];
    private homeGridsIdx: number[] = [101, 102, 103, 104];
    private pokerSevenFirstMoveData: yalla.data.jackaro.MoveChessDataInterface = null;
    private pakerAOr10MutilMoveStep: number = 0;
    private curTurnAlreadyMoveChessInfoes: Array<{ chessInfo: string, moveStep: number, isMoveEnd: boolean }> = [];//当前回合已经移动过的棋子信息
    private curTurnAlreadyChooseChessInfoes: Array<string> = [];//当前回合已经选择过的棋子信息
    private isPlayExchangeEndSpinAni: boolean = false;
    private takeOffSpineAni: SpineAniPlayer = null;//起飞波纹动画
    private impactedChessTotalData: Record<string, Array<yalla.data.jackaro.ImpactedChessDataInterface>>;//出牌 k 被撞击的所有的棋子数据
    private curPlayPokerKMoveChessPlayer: number;
    private recordMoveToBornDataInterface: Array<yalla.data.jackaro.MoveToBornDataInterface>;
    private jackaroChooseChessTag: JackaroChooseChessTag;
    // private logTag: string = "JackaroChessManager :";
    private lastClickMinChess: JackaroChess;
    private curChessAreaAllChess: Array<JackaroChess> = [];
    private chessChooseAniMap: Record<string, SpineAniPlayer> = {};
    public playerExpIndex: number = 0;
    private curTurnMaxChooseChessNum: number = 1;

    private chessEndPointAniPath: Array<string> = [yalla.getSkeleton("jackaro/guiji/hong/hong"), yalla.getSkeleton("jackaro/guiji/lan/lan"), yalla.getSkeleton("jackaro/guiji/huang/huang"), yalla.getSkeleton("jackaro/guiji/lv/lv")];
    private gridColorSort: Array<number> = [0, 3, 2, 1]; //0 红 3 蓝 2 黄 1 绿

    /*校验棋子移动数据 */
    private checkMoveChessData: Array<ToGridInterface> = [];
    /**
     * 记录当前可选择的棋子，用于断线重连恢复可选棋子
     */
    private recordCanChooseChess: Array<string> = [];

    private killChessCallBackInfo: Object = {};
    private curTurnAlreadyKillChess: Array<string> = [];//当前回合已经击杀的棋子

    public selfAddExp: boolean = false; //自己是否可以加经验

    private curTurnAlreadyPlayExpIdxs: Array<{ expPlayer: number, expType: number }> = [];
    /**
     * 是否出牌7 只有两颗棋子可以移动，如果是这种情况的话，需要切换玩家时延迟切换
     */
    private isPokerSevenOnlyTwoChessMove: boolean = false;


    //收集出牌7 等待移动的棋子
    private collectPoker7WaitingChess: Array<yalla.data.jackaro.MoveChessDataInterface> = [];

    // 1v1模式冲突处理器
    /** 1v1模式冲突处理器 */
    private conflictHandler: JackaroChess1V1ConflictHandler = null;

    /** 记录棋子绑定的bubble表情，在棋子飞回出生点的话，这个棋子的bubble表情则删除
     * {beyondIdx_chessId:bubble}
    */
    private _chessBubbleHash = {};

    public init() {
        this.initSpineAni();
        this.initView();
        // 初始化1v1冲突处理器
        this.conflictHandler = new (window as any).JackaroChess1V1ConflictHandler(this);
    }
    private initView() {
        if (!this.jackaroChooseChessTag) {
            this.jackaroChooseChessTag = new JackaroChooseChessTag();
            /**TODO:: 棋子序号层级加入tipLayer，同tipManager */
            this.mapLayer.parent.parent.getChildByName("tipLayer").addChild(this.jackaroChooseChessTag);
        }
        this.jackaroChooseChessTag.hide();
    }
    private initSpineAni() {
        //低配置手机不初始化起飞动效
        JkChooseChessManager.ins.init();
        // if (yalla.Global.isLowPerformanceDevice) {
        //     this.chessChooseAniPath = [yalla.getSkeleton("jackaro/choose/mini/Choose_red/Choose_red"), yalla.getSkeleton("jackaro/choose/mini/Choose_blue/Choose_blue"), yalla.getSkeleton("jackaro/choose/mini/Choose_yellow/Choose_yellow"), yalla.getSkeleton("jackaro/choose/mini/Choose_green/Choose_green")];
        // } else {
        //     this.chessChooseAniPath = [yalla.getSkeleton("jackaro/choose/Choose_red/Choose_red"), yalla.getSkeleton("jackaro/choose/Choose_blue/Choose_blue"), yalla.getSkeleton("jackaro/choose/Choose_yellow/Choose_yellow"), yalla.getSkeleton("jackaro/choose/Choose_green/Choose_green")];
        // }
        if (!yalla.Global.isLowPerformanceDevice && !this.takeOffSpineAni) {
            this.takeOffSpineAni = new SpineAniPlayer(yalla.getSkeleton("jackaro/fly/fly_1"));
            this.mapLayer.getChildByName("chess_box").addChild(this.takeOffSpineAni);
        }
        // {{ YUM: [Init] - 初始化预览动效管理器 }}
        JackaroChessOrbitEffectManager.instance.init(this.mapLayer.getChildByName("ani_box"), this.mapLayer.getChildByName("props_box"), this.chessEndPointAniPath);
    }
    public playTakeOffSpineAni(chess: JackaroChess) {
        //低配置手机不播放起飞动效
        if (yalla.Global.isLowPerformanceDevice) return;
        if (yalla.Global.isFouce) {
            //
            //检查起飞点有棋子的话，不用播放起飞特效,因为此时起飞还没完成，所以用goalGrid检查
            let chessInTakeOffPoint = this.getChessByGrid(chess.goalGrid);
            if (chessInTakeOffPoint) {
                return;
            }
            if (this.takeOffSpineAni) {
                this.takeOffSpineAni.visible = true;
                this.takeOffSpineAni.pos(chess.goalGrid.port.x, chess.goalGrid.port.y);
                Laya.timer.frameOnce(1, this, () => {
                    this.takeOffSpineAni.play("idle", false);
                });
            }
        }
    }
    public setMapLayer(layer: Laya.Box) {
        this.mapLayer = layer;
        this.init();
    }
    public setTopAniLayar(layer: Laya.Box) {
        this.topAniLayer = layer;
    }
    /**
     * 点击棋子遮罩
     * @param e 
     */
    public clickChessMask(e: Laya.Event) {
        if (e) {
            let [mouseX, mouseY] = [e.stageX, e.stageY];

            if (TipsViewManager.getInstance().isShow(TipViewType.SELECT_STEP_NUM)) {
                return;
            }
            if (!TipsViewManager.getInstance().isShow(TipViewType.EXCHANGE_CHESS) && !TipsViewManager.getInstance().isShow(TipViewType.CHOOSE_CHESS_OPERATE)) {
                return;
            }
            if (TipsViewManager.getInstance().isShow(TipViewType.TRUST_SYSTEM)) {
                return;
            }
            let canMoveChesses = this.getCanChooseChess();
            if (!canMoveChesses || canMoveChesses.length < 1) {
                return;
            }
            canMoveChesses = canMoveChesses.filter(ch => {
                return ch.choose && this.freeMoveChessIds.indexOf({ idx: ch.beyondIdx, order: ch.id }) == -1 && !this.recordExchangeChess.some(item => (item.idx == ch.beyondIdx));
            });
            let curCanMoveChessNum = canMoveChesses.length;

            // {{ YUM: [Optimize] - 使用圆形碰撞检测和距离优先级，防止相邻棋子误触 }}
            let chessClickRadius = this.getChessClickRadius();
            let validTouchChesses: Array<{ chess: JackaroChess, distance: number }> = [];

            // 收集所有在触摸范围内的棋子
            for (var index = 0; index < curCanMoveChessNum; index++) {
                var ch = canMoveChesses[index];
                let location = ch.getGlobalPos();
                var [x, y] = [location.x, location.y];

                // 计算触摸点到棋子中心的距离
                let distance = Math.sqrt((mouseX - x) * (mouseX - x) + (mouseY - y) * (mouseY - y));

                // 使用圆形碰撞检测
                if (distance <= chessClickRadius) {
                    validTouchChesses.push({ chess: ch, distance: distance });
                }
            }

            // 如果有多个棋子在触摸范围内，选择距离最近的
            if (validTouchChesses.length > 0) {
                // 按距离排序，选择最近的
                validTouchChesses.sort((a, b) => a.distance - b.distance);
                let selectedChess = validTouchChesses[0].chess;
                selectedChess.clickArea.event("click");
                return;
            }
        }
    }

    /**
     * 获取棋子点击半径 - 优化版本
     * {{ YUM: [Enhance] - 使用圆形检测半径，可根据需要调整触摸精度 }}
     */
    private getChessClickRadius(): number {
        // 基础触摸半径
        let baseRadius = 65;

        // 根据不同状态调整半径
        if (this.isExchangeChessMove()) {
            // 交换棋子模式下稍微放大触摸区域
            return baseRadius + 10;
        }

        // 可以根据棋子密度或设备类型进一步调整
        // if (yalla.Global.isMobile) {
        //     return baseRadius + 5; // 移动设备触摸区域稍大
        // }

        return baseRadius;
    }


    /**
     *
     * 是否交换棋子的模式
     */
    public isExchangeChessMove() {
        return this.isExchangeChess;
    }

    private chessClickChess(clickMinChess: JackaroChess, canMoveChesses: JackaroChess[]) {
        if (clickMinChess) {
            if (this.lastClickMinChess != clickMinChess) {
                this.clearChooseTag();
            }
            if (this.isHasAlreadyTurnMove(clickMinChess)) {
                return;
            }
            if (this.isBeyondTurnMaxChooseChessNum()) {
                return;
            }
            if (this.checkShowChooseChessTagView(clickMinChess, canMoveChesses, (clickMinChess) => { this.chooseChess(clickMinChess, canMoveChesses) })) {
            } else {
                this.chooseChess(clickMinChess, canMoveChesses);
            }
            this.lastClickMinChess = clickMinChess;
        }
    }
    public userActiveClickChess(clickChess: JackaroChess) {
        if (!clickChess) return;
        let canMoveChesses = this.getCanChooseChess();
        if (!canMoveChesses || canMoveChesses.length < 1) {
            return;
        }
        canMoveChesses = canMoveChesses.filter(ch => {
            return ch.choose && this.freeMoveChessIds.indexOf({ idx: ch.beyondIdx, order: ch.id }) == -1 && !this.recordExchangeChess.some(item => (item.idx == ch.beyondIdx));
        });

        this.chessClickChess(clickChess, canMoveChesses);
    }
    private chooseChess(minChess: JackaroChess, canMoveChesses: JackaroChess[]) {
        //出牌 7自由选择移动步数
        if (this.isHasFreeMove) {
            if (TipsViewManager.getInstance().isShow(TipViewType.SELECT_STEP_NUM)) {
                return;
            }
            this.addCurTurnAlreadyChooseChessInfoes(minChess);
            this.hanlderPlayPoker7(minChess, canMoveChesses);
        } else if (this.isExchangeChess) {
            if (this.recordExchangeChess.length > 2) {
                return;
            }
            if (this.recordExchangeChess.some(item => item.idx == minChess.beyondIdx)) {
                return;
            }
            let curMoveChessPlayerIdx = JackaroGamePlayerManager.instance.getMoveChessPlayerIdx();
            if (this.recordExchangeChess.length == 0 && minChess.beyondIdx != curMoveChessPlayerIdx) {
                return;
            }
            this.addCurTurnAlreadyChooseChessInfoes(minChess);

            let chessColor = JackaroGamePlayerManager.instance.getColorByIdx(minChess.beyondIdx);
            yalla.data.jackaro.JackaroUserService.instance.sendSelectChess({ idx: minChess.beyondIdx, order: minChess.id, color: chessColor });

            this.recordExchangeChess.push({ idx: minChess.beyondIdx, order: minChess.id, number: 0 });
            minChess.setExchangeChess(true);
            this.playExchangeStartSpinAni(minChess);
            if (this.recordExchangeChess.length == 2) {
                //开始交换棋子时，隐藏棋子选择动效
                // this.hideAllChessChoose();
                JkChooseChessManager.ins.removeAll();
                TipsViewManager.getInstance().hideTip(TipViewType.EXCHANGE_CHESS);
                if (this.recordExchangeChess[0].idx != curMoveChessPlayerIdx) {
                    //交换棋子
                    [this.recordExchangeChess[0], this.recordExchangeChess[1]] = [this.recordExchangeChess[1], this.recordExchangeChess[0]];
                }
                //如果自己的回合时间只剩下3秒，则不进行交换，等服务器推送消息
                let operateTime: number = JackaroGamePlayerManager.instance.getOperateTime();
                if (operateTime >= 3000) {
                    this.executeChessExchange(true, true);
                }
            } else {
                this.updatePlayJackResultChessChoose();
            }
        } else {
            //移动棋子
            let order = minChess.id;
            let chessBeyondId: number = minChess.beyondIdx;
            let curPlayPoker = JackaroCardManger.instance.getCurPlayPoker();
            if (JackaroCardManger.instance.isPoker7(curPlayPoker)) {
                return;
            }
            if (curPlayPoker) {
                this.addCurTurnAlreadyChooseChessInfoes(minChess);
                TipsViewManager.getInstance().hideTip(TipViewType.CHOOSE_CHESS_OPERATE);

                let moveNumber = this.pakerAOr10MutilMoveStep ? this.pakerAOr10MutilMoveStep : JackaroCardManger.instance.getPlayPokerMoveStep(curPlayPoker, order);
                //客服端先行直接移动棋子
                let resultItem = JackaroChessManager.instance.getPlayResultItemByNumberAndOrder(moveNumber, order, chessBeyondId);
                if (resultItem) {
                    JackaroChessManager.instance.handleMoveChessBySelf(resultItem, moveNumber);
                }
                yalla.data.jackaro.JackaroUserService.instance.sendPlayPoker(curPlayPoker, { idx: chessBeyondId, order: order, number: moveNumber });
            }

        }
    }
    /**
     * 是否显示棋子选择序号界面
     * @param curChess 
     */
    public checkShowChooseChessTagView(curChess: JackaroChess, canMoveChesses: JackaroChess[], callBack?: Function): boolean {
        // let curChessAreaAllChess = canMoveChesses
        let curChessAreaAllChess = this.getCurChessAreaAllChess(curChess);
        if (curChessAreaAllChess.length == 0) {
            return false;
        }
        curChessAreaAllChess.push(curChess);
        this.jackaroChooseChessTag.hide();
        curChessAreaAllChess.sort((a, b) => {//按照前进方向排序
            if (a && b) {
                var aStation = a.goalGrid.station;
                var bStation = b.goalGrid.station;
                var aArea = aStation.area,
                    aGridPosition = Math.abs(aStation.gridPosition),
                    bArea = bStation.area,
                    bGridPosition = Math.abs(bStation.gridPosition);
                // return aArea == bArea ? aGridPosition - bGridPosition : (bArea || 4) - aArea;
                return aArea == bArea ? aGridPosition - bGridPosition : (aArea - aGridPosition) - (bArea - bGridPosition);
            }
            return 0;
        });
        this.curChessAreaAllChess = curChessAreaAllChess;
        let setChessTag = (tag?: string) => {
            for (let i = 0; i < curChessAreaAllChess.length; i++) {
                let jackaroChess: JackaroChess = curChessAreaAllChess[i];

                let tagString = tag ? tag : "" + (i + 1);
                jackaroChess.setChessTag(tagString);
            }
        }
        setChessTag();
        this.topAniLayer.addChild(this.jackaroChooseChessTag);
        this.jackaroChooseChessTag.show(curChessAreaAllChess, (idx: number) => {
            callBack && callBack(idx);
            for (let i = 0; i < curChessAreaAllChess.length; i++) {
                let jackaroChess: JackaroChess = curChessAreaAllChess[i];
                jackaroChess.setChessTag("");
            }
            this.curChessAreaAllChess = [];
        });
        return true;
    }
    /**
     * 设置所有棋子是否可以选中，目前主要用于杀端回来，出牌A时，有起飞数据导致基地棋子被选中，此时需要设置所有棋子不可以选中
     * @param canChoose 
     */
    public setAllChessChoose(canChoose: boolean) {
        for (let key in this._pool) {
            let chess = this._pool[key];
            if (chess) {
                chess.choose = canChoose;
            }
        }
    }
    private getCurChessAreaAllChess(curChess: JackaroChess): Array<JackaroChess> {
        let curChessAreaChesses: Array<JackaroChess> = [];
        if (!curChess) {
            return curChessAreaChesses;
        }
        let canMoveChesses = this.getCanChooseChess();
        canMoveChesses = canMoveChesses.filter(ch => {
            return ch.choose && this.freeMoveChessIds.indexOf({ idx: ch.beyondIdx, order: ch.id }) == -1 && !this.recordExchangeChess.some(item => (item.idx == ch.beyondIdx));
        });
        let wayGrid = curChess.wayGrid;
        let reCordWayGrid = curChess.wayGrid;
        while (wayGrid && wayGrid.nextName) {
            let isHas = false;
            for (let i = 0; i < canMoveChesses.length; i++) {
                let chess = canMoveChesses[i];
                if (chess.wayGrid.atGrid(wayGrid.nextName)) {
                    curChessAreaChesses.push(chess);
                    wayGrid = chess.wayGrid;
                    isHas = true;
                    break;
                }
            }
            if (!isHas) break;
        }

        while (reCordWayGrid && reCordWayGrid.lastName) {
            let isHas = false;
            for (let i = 0; i < canMoveChesses.length; i++) {
                let chess = canMoveChesses[i];
                if (chess.wayGrid.atGrid(reCordWayGrid.lastName)) {
                    curChessAreaChesses.push(chess);
                    reCordWayGrid = chess.wayGrid;
                    isHas = true;
                    break;
                }
            }
            if (!isHas) break;
        }

        //如果超过5个棋子排在一起，只显示距离当前棋子最近的5个棋子
        if (curChessAreaChesses.length > 5) {
            let curChessIdx = curChessAreaChesses.indexOf(curChess);
            let startIdx = Math.max(0, curChessIdx - 2);
            let endIdx = Math.min(curChessAreaChesses.length - 1, curChessIdx + 2);
            curChessAreaChesses = curChessAreaChesses.slice(startIdx, endIdx + 1);
        }

        return curChessAreaChesses;
    }
    /**
     * 选择出牌7的处理逻辑
     */
    public hanlderPlayPoker7(minChess: JackaroChess, canMoveChesses: Array<JackaroChess>) {
        if (this.freeMoveChessIds && this.freeMoveChessIds.length > 2) {
            return;
        }
        let curCanMoveStep = this.getCanMoveChessStep(minChess.id, minChess.beyondIdx);
        let curCanMoveMaxStep = this.getCurMoveChessMaxStep();
        let curMoveChessMaxStep = Math.min(curCanMoveMaxStep, curCanMoveStep);
        if (this.freeMoveChessIds.length == 1) {
            //针对已经选了一个棋子了，这是第二个棋子
            if (curMoveChessMaxStep <= 0) {
                return;
            }
            this.isHasFreeMove = false;
            TipsViewManager.getInstance().hideTip(TipViewType.CHOOSE_CHESS_OPERATE);
            let curAlreadyMoveSteps = this.curAlreadyMoveSteps();
            if ((curMoveChessMaxStep + curAlreadyMoveSteps) != JackaroChessManager._poker7Step) {
                return;
            }
            if (!this.pokerSevenFirstMoveData) {
                return;
            }
            //然后走第二颗棋子
            this.addCollectPoker7WaitingChess({ idx: minChess.beyondIdx, order: minChess.id, number: curMoveChessMaxStep });
            return;
        }
        //只有一步可走时，直接走子 或者 刚好存在两颗棋子可以移动，直接移动，不用选择步数
        let totalMoveStep = this.getTotalChessMoveStep();
        if (curMoveChessMaxStep == 1) {
            this.pokerSevenFirstMoveData = { idx: minChess.beyondIdx, order: minChess.id, number: curMoveChessMaxStep };

            this.addCollectPoker7WaitingChess({ idx: minChess.beyondIdx, order: minChess.id, number: curMoveChessMaxStep });
            this.freeMoveChessIds.push({ idx: minChess.beyondIdx, order: minChess.id });

            this.updatePlayPoker7ChessChoose();
            let canMoveChesses = this.getCanChooseChess();
            //如果只有1步可走时，检测剩下的棋子是否只有一颗可以走，如果是，直接收集数据
            if (canMoveChesses && canMoveChesses.length == 1) {
                let chess = canMoveChesses[0];
                this.addCollectPoker7WaitingChess({ idx: chess.beyondIdx, order: chess.id, number: 6 });
                this.freeMoveChessIds.push({ idx: chess.beyondIdx, order: chess.id });
                //Todo 检测收集7的等待数据
            } else {
                if (TipsViewManager.getInstance().isShow(TipViewType.CHOOSE_CHESS_OPERATE)) {
                    (TipsViewManager.getInstance().getTipView(TipViewType.CHOOSE_CHESS_OPERATE) as ChooseChessOperateView).checkShowChessMask();
                } else {
                    let tips = `Choose a stone\nto move `;
                    TipsViewManager.getInstance().showTip(TipViewType.CHOOSE_CHESS_OPERATE, { operateTime: JackaroGamePlayerManager.instance.getOperateTime(), tips: tips });
                }
            }
        } else if (canMoveChesses.length == 2 && totalMoveStep == JackaroChessManager._poker7Step) {
            this.pokerSevenFirstMoveData = { idx: minChess.beyondIdx, order: minChess.id, number: curMoveChessMaxStep };
            for (let index = 0; index < canMoveChesses.length; index++) {
                const element = canMoveChesses[index];
                if (element.beyondIdx == this.pokerSevenFirstMoveData.idx) {
                    this.addCollectPoker7WaitingChess({ idx: element.beyondIdx, order: element.id, number: curMoveChessMaxStep });
                } else {
                    this.addCollectPoker7WaitingChess({ idx: element.beyondIdx, order: element.id, number: JackaroChessManager._poker7Step - curMoveChessMaxStep });
                }
                this.freeMoveChessIds.push({ idx: element.beyondIdx, order: element.id });
            }
            //Todo 检测收集7的等待数据
        } else {
            let curMoveStepRange = this.getChessMoveStepRange();
            let resultItem = this.getPlayResultItemByChessId(minChess.id, minChess.beyondIdx);
            let notAllowMoveNumber = resultItem ? resultItem.notAllowMoveNumber : null;
            let chessColor = JackaroGamePlayerManager.instance.getColorByIdx(minChess.beyondIdx);
            yalla.data.jackaro.JackaroUserService.instance.sendSelectChess({ idx: minChess.beyondIdx, order: minChess.id, color: chessColor });
            //出现弹窗后，如果只有一个选项，检测直接移动这个棋子后，是否还只有一颗棋子可走，如果是，两颗棋子一起走
            if (this.isOnlyMoveOneStepWithAllowMoveNumber(minChess, curMoveChessMaxStep, notAllowMoveNumber)) {
                return;
            }
            this.freeMoveChessIds.push({ idx: minChess.beyondIdx, order: minChess.id });
            //棋子移动步数小于当前可移动区间最大步数时，直接按照最大步数来选择移动步数
            if (curMoveChessMaxStep <= curMoveStepRange.maxStep) {
                TipsViewManager.getInstance().showTip(TipViewType.SELECT_STEP_NUM, {
                    maxStep: curMoveChessMaxStep,
                    chessID: minChess.id,
                    operateTime: JackaroGamePlayerManager.instance.getOperateTime(),
                    notAllowMoveNumber: notAllowMoveNumber
                });
            } else {
                let curMaxStepByExclude = this.getMaxMoveChessStepByExclude(minChess.id);
                //除了当前选择棋子，是否还有其它棋子可以走7步，如果可以的话，另其它棋子不用显示最小可移动步数
                if (curMaxStepByExclude == JackaroChessManager._poker7Step) {
                    TipsViewManager.getInstance().showTip(TipViewType.SELECT_STEP_NUM, {
                        maxStep: curMoveChessMaxStep,
                        chessID: minChess.id,
                        operateTime: JackaroGamePlayerManager.instance.getOperateTime(),
                        notAllowMoveNumber: notAllowMoveNumber
                    });
                } else {
                    TipsViewManager.getInstance().showTip(TipViewType.SELECT_STEP_NUM, {
                        minStep: JackaroChessManager._poker7Step - curMoveStepRange.minStep,
                        maxStep: curMoveChessMaxStep,
                        chessID: minChess.id,
                        operateTime: JackaroGamePlayerManager.instance.getOperateTime(),
                        notAllowMoveNumber: notAllowMoveNumber
                    });
                }

            }
        }
    }
    /**
     * 添加出牌7 走子收集的数据
     */
    private addCollectPoker7WaitingChess(collectData: yalla.data.jackaro.MoveChessDataInterface) {
        if (collectData) {
            let idx = collectData.idx;
            let isHas = false;
            for (let index = 0; index < this.collectPoker7WaitingChess.length; index++) {
                const element = this.collectPoker7WaitingChess[index];
                if (element.idx == idx && element.order == collectData.order) {
                    isHas = true;
                }
            }
            if (!isHas) {
                this.collectPoker7WaitingChess.push({ idx: collectData.idx, order: collectData.order, number: collectData.number });
                let chess = this.getChessById(collectData.idx + "_" + collectData.order);
                if (chess) {
                    let getKillInfo = this.getChessMoveXStepCanKillChess(chess, collectData.number);
                    if (getKillInfo) {
                        this.curTurnAlreadyKillChess.push(getKillInfo);
                    }
                    this.addAlreadyMoveChessStepInfo(chess, collectData.number);
                }
            }
        }
        this.chessMovePoker7WaitingChess();
    }
    /**
     * 处理1v1模式下两颗棋子移动到相同目标点的冲突情况
     * @returns {boolean} 是否处理了冲突移动
     */
    private handle1V1ConflictMove(): boolean {
        return this.conflictHandler.handle1V1ConflictMove();
    }

    // /**
    //  * 处理1v1模式下两颗棋子移动时存在穿越问题
    //  * @returns {boolean} 是否处理了冲突移动
    //  */
    // private handle1V1CutAcrossMove(): boolean {
    //     return this.conflictHandler.handle1V1CutAcrossMove();
    // }

    /**
     * 按顺序执行棋子移动，第二个棋子需要延迟执行
     * @param {Array<yalla.data.jackaro.MoveChessDataInterface>} sortedChessData 排序后的棋子数据
     */
    private executeOrderedChessMove(sortedChessData: Array<yalla.data.jackaro.MoveChessDataInterface>): void {
        sortedChessData.forEach((chessData, index) => {
            const resultItem = this.getPlayResultItemByChessId(chessData.order, chessData.idx);
            if (!resultItem) {
                yalla.Debug.logMore('executeOrderedChessMove: 找不到对应的棋子，idx:', chessData.idx, chessData.order);
                return;
            }

            if (index === 1) {
                let sendDataToServe = () => {
                    // 检查数组是否仍然有效，防止在延迟期间被清空
                    if (!sortedChessData || sortedChessData.length === 0) {
                        yalla.Debug.logMore('executeOrderedChessMove: sortedChessData已被清空，取消发送数据');
                        return;
                    }

                    // 发送移动数据到服务器
                    const curPlayPoker = JackaroCardManger.instance.getCurPlayPoker();
                    // 发送移动数据到服务器
                    const moveData: any = {
                        operatedChess: 0,
                        firstData: sortedChessData[0]
                    };

                    // 如果有第二个棋子数据，则添加到发送数据中
                    if (sortedChessData.length > 1) {
                        moveData.secondData = sortedChessData[1];
                    }

                    yalla.data.jackaro.JackaroUserService.instance.sendFreeMoveChess(curPlayPoker, moveData);
                }
                // 第二个棋子需要延迟执行，避免冲突
                if (yalla.Global.isFouce) {
                    Laya.timer.once(200, this, () => {
                        this.handleMoveChessBySelf(resultItem, chessData.number);
                        sendDataToServe();
                    });
                } else {
                    this.handleMoveChessBySelf(resultItem, chessData.number);
                    sendDataToServe();
                }
            } else {
                //1.4.5 出牌7 第一个棋子走时也无法加时
                yalla.data.jackaro.JackaroUserService.instance.setIsTriggerPlayCard(true);
                // 第一个棋子立即执行
                this.handleMoveChessBySelf(resultItem, chessData.number);
            }
        });
    }

    /**
     * 出牌7 根据收集的数据，检查是否满足可以走子
     * 
     */
    private chessMovePoker7WaitingChess() {
        if (!this.collectPoker7WaitingChess || this.collectPoker7WaitingChess.length < 1) return;
        let curPlayPoker = JackaroCardManger.instance.getCurPlayPoker();
        if (this.collectPoker7WaitingChess && this.collectPoker7WaitingChess.length == 1) {
            let collectData = this.collectPoker7WaitingChess[0];
            if (collectData.number == JackaroChessManager._poker7Step) {
                TipsViewManager.getInstance().hideTip(TipViewType.CHOOSE_CHESS_OPERATE);
                let resultItem = this.getPlayResultItemByNumberAndOrder(collectData.number, collectData.order, collectData.idx);
                if (resultItem) {
                    this.handleMoveChessBySelf(resultItem, collectData.number);
                }
                yalla.data.jackaro.JackaroUserService.instance.sendFreeMoveChess(curPlayPoker, { operatedChess: 0, firstData: collectData });
                this.isHasFreeMove = false;
            }
        } else {
            TipsViewManager.getInstance().hideTip(TipViewType.CHOOSE_CHESS_OPERATE);
            // 1v1模式下检测两颗棋子是否移动到相同目标点，如果是则按移动时间排序执行
            if (yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode() && this.handle1V1ConflictMove()) {
                return;
            }
            // 普通模式下直接按顺序执行棋子移动
            this.executeOrderedChessMove(this.collectPoker7WaitingChess);

            this.isHasFreeMove = false;
        }
    }
    public restorePlayPoker7(curMoveChessPlayerIdx: number, sevenPokerFirstOrder: number, stepNum: number) {
        let playPokerData = { idx: curMoveChessPlayerIdx, order: sevenPokerFirstOrder, number: stepNum };
        this.pokerSevenFirstMoveData = playPokerData;
        this.freeMoveChessIds.push({ idx: curMoveChessPlayerIdx, order: sevenPokerFirstOrder });
        this.isHasFreeMove = true;
        let chess = this.getChessById(this.pokerSevenFirstMoveData.idx + "_" + this.pokerSevenFirstMoveData.order);
        if (chess) {
            this.addAlreadyMoveChessStepInfo(chess, playPokerData.number);
        }
    }
    /**
     * 出牌7 出现弹窗后是否只能走一步，因为选择步数弹窗出现时，此时的步数也许只能走一步，所以只能走一步时，直接走,这种场景需要判定是否还剩下另外一颗棋子，如果剩下，直接走下一颗棋子
     * 
     */
    private isOnlyMoveOneStepWithAllowMoveNumber(minChess: JackaroChess, curMoveChessMaxStep: number, notAllowMoveNumber: Array<number>) {
        //如果有最小移动步数时，而且最小移动步数去除能走的步数后 只有一步可走时，这时需要直接移动这颗棋子
        let recordCanMoveSteps: Array<number> = [];
        for (let i = 0; i < curMoveChessMaxStep; i++) {
            let curStep = i + 1;
            if (notAllowMoveNumber && notAllowMoveNumber.indexOf(curStep) == -1) {
                recordCanMoveSteps.push(curStep);
            }
        }
        //此时只能走一步时，直接走棋子
        if (recordCanMoveSteps && recordCanMoveSteps.length == 1) {
            let curMoveStep = recordCanMoveSteps[0];
            this.pokerSevenFirstMoveData = { idx: minChess.beyondIdx, order: minChess.id, number: curMoveStep };
            //如果第二颗棋子
            this.addCollectPoker7WaitingChess({ idx: minChess.beyondIdx, order: minChess.id, number: curMoveStep });
            this.freeMoveChessIds.push({ idx: minChess.beyondIdx, order: minChess.id });
            this.updatePlayPoker7ChessChoose();
            let canMoveChesses = this.getCanChooseChess();
            //走了第一步后，如果还有一颗棋子可以选择，直接走这颗棋子
            if (canMoveChesses && canMoveChesses.length == 1) {
                //再移动第二颗棋子
                let curMoveChess = canMoveChesses[0];
                curMoveStep = JackaroChessManager._poker7Step - curMoveStep;
                this.addCollectPoker7WaitingChess({ idx: curMoveChess.beyondIdx, order: curMoveChess.id, number: curMoveStep });
                this.freeMoveChessIds.push({ idx: curMoveChess.beyondIdx, order: curMoveChess.id });
            } else {
                //剩下的棋子，需要刷新选择界面
                this.openOrUpdateChooseChessOperateView();
            }
            return true;
        }
        return false;
    }
    // public setGridMap(gridMap: Record<string, yalla.data.jackaro.GridDataInterface>) {
    //     this._gridMap = gridMap;
    // }

    public setPokerSevenFirstMoveData(data: yalla.data.jackaro.MoveChessDataInterface) {
        TipsViewManager.getInstance().hideTip(TipViewType.CHOOSE_CHESS_OPERATE);
        let curMoveChessPlayerIdx = JackaroGamePlayerManager.instance.getMoveChessPlayerIdx();
        this.pokerSevenFirstMoveData = data;
        this.addCollectPoker7WaitingChess(data);
        if (data.number == JackaroChessManager._poker7Step) {
            return;
        }
        if (this.freeMoveChessIds.length == 1) {
            this.updatePlayPoker7ChessChoose();
            let canMoveChesses = this.getCanChooseChess();
            for (let index = 0; index < canMoveChesses.length; index++) {
                const element = canMoveChesses[index];
            }

            if (canMoveChesses && canMoveChesses.length == 1) {
                let curChess = canMoveChesses[0];
                this.addCollectPoker7WaitingChess({ idx: curChess.beyondIdx, order: curChess.id, number: JackaroChessManager._poker7Step - data.number });
                //TODO 检测等待棋子
            } else {
                this.openOrUpdateChooseChessOperateView();
            }
        }
    }
    /**
     * 显示选择棋子操作界面
     */
    private openOrUpdateChooseChessOperateView() {
        if (TipsViewManager.getInstance().isShow(TipViewType.CHOOSE_CHESS_OPERATE)) {
            (TipsViewManager.getInstance().getTipView(TipViewType.CHOOSE_CHESS_OPERATE) as ChooseChessOperateView).checkShowChessMask();
        } else {
            let tips = `Choose a stone\nto move `;
            TipsViewManager.getInstance().showTip(TipViewType.CHOOSE_CHESS_OPERATE, { operateTime: JackaroGamePlayerManager.instance.getOperateTime(), tips: tips });
        }
    }
    /**
     * 出牌7 可选择棋子
     */
    private updatePlayPoker7ChessChoose() {
        if (!this.commonResult) {
            return;
        }
        let curAlreadyMoveSteps = this.curAlreadyMoveSteps();
        let curMoveChessPlayerIdx = JackaroGamePlayerManager.instance.getMoveChessPlayerIdx();
        let canMoveTeamChess = this.isCanMoveTeamChessTemporary2();
        for (let key in this.commonResult) {
            let result = this.commonResult[key];
            let curMoveStep = parseInt(key);
            result.result.forEach(item => {
                let canChoose = (curAlreadyMoveSteps + curMoveStep) >= JackaroChessManager._poker7Step ? true : false;
                let chess = this.getChessById(item.fromGrid.chess.idx + "_" + item.fromGrid.chess.order);
                if (chess) {
                    //已经选过的棋子不可再次选中
                    if (this.pokerSevenFirstMoveData && this.pokerSevenFirstMoveData.order == item.fromGrid.chess.order && this.pokerSevenFirstMoveData.idx == item.fromGrid.chess.idx) {
                        canChoose = false;
                    }
                    //不属于自己的棋子，如果此时不可以走队友的棋子，也是不可选中
                    if (chess.beyondIdx != curMoveChessPlayerIdx && !canMoveTeamChess) {
                        canChoose = false;
                    }
                    //已经击杀的棋子也不能再次被选中
                    if (this.curTurnAlreadyKillChess.indexOf(chess.beyondIdx + "_" + chess.id) != -1) {
                        canChoose = false;
                    }
                    chess.choose = canChoose;
                }
            })
        }
    }
    /**
     * 获取当前出牌7的棋子是否只有2个棋子可操作
     * @returns 
     */
    public getIsPokerSevenOnlyTwoChessMove() {
        return this.isPokerSevenOnlyTwoChessMove;
    }
    private curAlreadyMoveSteps(): number {
        let totalStep: number = 0;
        for (let index = 0; index < this._curAlreadyMoveStep.length; index++) {
            const element = this._curAlreadyMoveStep[index];
            totalStep += element;
        }
        return totalStep;
    }
    public createChess(idx: number, color: number, chessInfo: yalla.data.jackaro.PlayerChessInterface) {
        let chess = JackaroChessManager.instance.getChessById(idx + "_" + chessInfo.order);
        let chColor: number = color;
        let skinID: number = undefined;
        if (!chess) {
            chess = new JackaroChess(skinID, chColor);
            this.mapLayer.getChildByName("chess_box").addChild(chess);
            chess.setChessBoxLayer(this.mapLayer.getChildByName("chess_box") as Laya.Box);
            chess.setAniBoxLayer(this.mapLayer.getChildByName("ani_box") as Laya.Box);
        }
        chess.homeGrid = JackaroBoardData.getGridByStation({ area: chColor, gridPosition: JackaroBoardData.convertGridPosition(chessInfo.position) });
        this.updateChess(idx, chessInfo, chess);
        this.addNewChess(idx, chess);
        return chess;
    }
    public addNewChess(idx: number, chess: JackaroChess) {
        if (chess) this._pool[idx + "_" + chess.id] = chess;
    }
    public updateChess(idx: number, chessInfo: yalla.data.jackaro.PlayerChessInterface, chess: JackaroChess) {
        if (chess) {
            chess.setChessData(idx, chessInfo);
        }
    }

    get chessLen(): number {
        return Object.keys(this._pool).length;
    }
    public getChessById(chessID: string): JackaroChess {
        return this._pool[chessID];
    }
    public getChessesByColor(color: number): Array<JackaroChess> {
        if (!this._poolColor[color]) {
            var arr = [];
            this.each(chess => {
                if (chess.color == color) arr.push(chess);
            })
            if (arr.length == 4) {
                this._poolColor[color] = arr;
            } else {
                return [];
            }
        }
        return this._poolColor[color];
    }
    public getCanFlyChess(): JackaroChess {
        let color = JackaroGamePlayerManager.instance.getColorByIdx(yalla.Global.Account.idx);
        let chessArr = this.getChessesByColor(color).filter(ch => {
            return ch.goalGrid.inHome;
        })
        chessArr.sort((a, b) => {
            return a.id - b.id;
        })
        return chessArr[0];
    }
    /**
     * 通过 color 获取一个可以移动到出生点的格子
     */
    public getMoveHomeGrid(color: number): LudoGrid {
        for (let i = 0; i < this.homeGridsIdx.length; i++) {
            let gridIdx = this.homeGridsIdx[i];
            let grid = JackaroBoardData.getGridByStation({ area: color, gridPosition: gridIdx });
            if (grid) {
                let sameGridChesses = this.getSameGridChesses(grid);
                if (sameGridChesses.length == 0) {
                    return grid;
                }
            }
        }
        return null;
    }
    /**
     * 通过 color bornIndex获取一个可以移动到出生点的格子
     */
    public getMoveHomeGridByBornIndex(color: number, bornIndex: number): LudoGrid {
        let gridIdx = this.homeGridsIdx[bornIndex];
        let grid = JackaroBoardData.getGridByStation({ area: color, gridPosition: gridIdx });
        if (grid) {
            let sameGridChesses = this.getSameGridChesses(grid);
            if (sameGridChesses.length == 0) {
                return grid;
            }
        }
        return null;
    }
    public getWinsLenByColor(color: number): number {
        return this.getChessesByColor(color).filter(ch => {
            return ch.goalStation.gridPosition < -5;
        }).length
    }

    public allChessWin(color: number) {
        return this.getWinsLenByColor(color) == 4;
    }
    public getSameGridChesses(grid: LudoGrid, excludeChessId: number = null): JackaroChess[] {
        let result = []
        this.each((chess: JackaroChess) => {
            if (chess && chess.displayedInStage && grid.atGrid(chess.goalGrid.gridName) && chess.id != excludeChessId) {
                result.push(chess);
            }
        })
        return result;
    }
    public each(cb: Function) {
        for (var key in this._pool) {
            cb(this._pool[key])
        }
    }
    public isAllChessInHome(color: number): boolean {
        return this.getChessesByColor(color).every(ch => {
            return ch.goalGrid.inHome;
        })
    }
    public getCurMoveChessMaxStep(): number {
        return this.getCurPlayPokerAllMaxMoveStep() - this._curAlreadyMoveStep.reduce((total, cur) => {
            return total + cur;
        }, 0);
    }
    public offLineClear() {
        this.turnClear();
    }
    public clear() {
        this.clearRecordCanChooseChess();
        this.each(ch => {
            ch && ch.clear();
            ch = null;
        })
        this.clearPool();
        this._pool = {};
        this._poolColor = {};
        this.turnClear();
        Laya.timer.clearAll(this);
        this.clearChooseTag();
        this.lastClickMinChess = null;
        if (this.takeOffSpineAni) {
            this.takeOffSpineAni.clear();
        }
        this.takeOffSpineAni = null;
        for (let key in this.chessChooseAniMap) {
            let ani = this.chessChooseAniMap[key];
            if (ani) {
                ani.clear();
            }
        }
        this.chessChooseAniMap = {};
        this.resetRecordFastModeMoveChess();
        JackaroChessManager._instance = null;
        this.playerExpIndex = 0;
        JackaroChessMaskViewManager.clear();
        JackaroChessManager.instance.removeAllChessOrbit();
    }
    /**
     * 轮到自己时清理出牌7 已经走的步数
     */
    public resetCurAlreadyMoveStep() {
        this._curAlreadyMoveStep = [];
        this._curAlreadyMoveChessStepInfo = [];
    }
    /**
     * 每次切换玩家时 清理
     */
    public turnClear() {
        // {{ YUM: [新增] - 清理移动数据 }}
        this.recentMoveResultData = null;
        this.accumulatedPreviewRewardPoker = false;

        this.resetChess();
        this._curAlreadyMoveStep = [];
        this._curAlreadyMoveChessStepInfo = [];
        ////这个是服务器返回出牌时的消息，里面会包含移动棋子吃子的消息，在棋子正在移动进入后台时 正在移动的棋子，move end 需要很长时间回调，此时收到changeplayer消息就会把
        //此消息清空，导致切后台回来，本来需要吃子回去的棋子，就没法正常被吃回去
        // this.commonResult = null;
        this.jackResult = null;
        this.isHasFreeMove = false;
        this.isExchangeChess = false;
        this.recordExchangeChess = [];
        this.freeMoveChessIds = [];
        this.collectPoker7WaitingChess = [];
        // this.pokerSevenFirstMoveData = null;
        this.pakerAOr10MutilMoveStep = 0;
        this.curTurnAlreadyMoveChessInfoes = [];
        this.curTurnAlreadyChooseChessInfoes = [];
        // this.removeChessOrbitSpineAni();
        // this.removePlayPokerKKillSpineAni();
        this.isPlayExchangeEndSpinAni = false;
        //这个是服务器推送棋子移动消息，其中包含吃子的消息，此时如果清空了，在棋子进入后台时 正在移动的棋子，move end 需要很长时间回调，此时收到changeplayer消息就会把
        //此消息清空，导致切后台回来，本来需要吃子回去的棋子，就没法正常被吃回去
        // this.curKilledChesses = [];
        this.impactResult = null;
        this.clearChooseTag();
        this.lastClickMinChess = null;

        //切换玩家击杀动效存在播放的情况，需要延迟1秒后移除
        Laya.timer.once(1000, this, () => {
            this.removeAllChessKillSpineAni();
        });
        this.checkMoveChessData = [];
        // this.removeChessChooseSpineAni();
        JkChooseChessManager.ins.removeAll();
        // this.isPokerSevenOnlyTwoChessMove = false;
    }
    public addCurTurnAlreadyChooseChessInfoes(chess: JackaroChess) {
        if (!chess) return;
        if (this.curTurnAlreadyChooseChessInfoes && this.curTurnAlreadyChooseChessInfoes.indexOf(chess.beyondIdx + "-" + chess.id) < 0) {
            this.curTurnAlreadyChooseChessInfoes.push(chess.beyondIdx + "-" + chess.id);
        }
    }
    /**
     * 是否超出本回合可以移动棋子最大数量，除了出牌7 和 J，当前回合只能移动一颗棋子
     */
    public isBeyondTurnMaxChooseChessNum() {
        let chooseChessNum: number = this.curTurnAlreadyChooseChessInfoes.length;
        return chooseChessNum >= this.curTurnMaxChooseChessNum;
    }
    public addcTurnAlreadyMoveChessInfoes(chess: JackaroChess, moveStep: number) {
        if (!chess) return;
        let chessInfo = chess.beyondIdx + "_" + chess.id;
        if (this.isHasAlreadyTurnMove(chess)) {
            return;
        }
        if (this.curTurnAlreadyMoveChessInfoes) {
            this.curTurnAlreadyMoveChessInfoes.push({ chessInfo: chessInfo, moveStep: moveStep, isMoveEnd: false });
        }
    }
    /**
     * 设置棋子已经移动结束
     * @param chess 
     * @returns 
     */
    public setHasAlreadyTurnMoveEnd(chess: JackaroChess) {
        if (!chess) return false;
        if (this.curTurnAlreadyMoveChessInfoes) {
            for (let index = 0; index < this.curTurnAlreadyMoveChessInfoes.length; index++) {
                const element = this.curTurnAlreadyMoveChessInfoes[index];
                if (element.chessInfo == chess.beyondIdx + "_" + chess.id) {
                    element.isMoveEnd = true;
                }
            }
        }
    }
    public isHasAlreadyTurnMove(chess: JackaroChess): boolean {
        if (!chess) return false;
        if (this.curTurnAlreadyMoveChessInfoes) {
            for (let index = 0; index < this.curTurnAlreadyMoveChessInfoes.length; index++) {
                const element = this.curTurnAlreadyMoveChessInfoes[index];
                if (element.chessInfo == chess.beyondIdx + "_" + chess.id) {
                    return true;
                }
            }
        }
        return false;
    }
    /**
     * 
     * @returns 是否所有棋子都已经移动结束
     */
    public isAllAlreadyTurnMoveEnd(): boolean {
        let totalMoveStep = 0;
        if (this.curTurnAlreadyMoveChessInfoes) {
            for (let index = 0; index < this.curTurnAlreadyMoveChessInfoes.length; index++) {
                const element = this.curTurnAlreadyMoveChessInfoes[index];
                if (!element.isMoveEnd) {
                    return false;
                } else {
                    totalMoveStep += element.moveStep;
                }
            }
        }
        let curPlayPoker = JackaroCardManger.instance.getCurPlayPoker();
        if (JackaroCardManger.instance.isPoker7(curPlayPoker) && totalMoveStep < JackaroChessManager._poker7Step) {
            return false;
        }
        return true;
    }
    public resetCurTurnAlreadyMoveChessInfoes() {
        this.curTurnAlreadyMoveChessInfoes = [];
    }
    // /**
    //  * 当前移动的棋子是否存在吃子
    //  */
    // private isTurnAlreadyMoveChessHasKill() {
    //     if (this.curTurnAlreadyMoveChessInfoes) {
    //         for (let index = 0; index < this.curTurnAlreadyMoveChessInfoes.length; index++) {
    //             const element = this.curTurnAlreadyMoveChessInfoes[index];
    //             let curChess = this.getChessById(element.chessInfo);
    //             if (curChess && curChess.moveEndStatus == ChessMoveEndStatus.KillOthers) {
    //                 return true;
    //             }
    //         }
    //         return false;
    //     }
    // }
    /**
     * 添加已经移动的棋子步数信息,主要用于出牌7 时，需要检测总共移动步数
     * @param chess 
     * @param step 
     */
    private addAlreadyMoveChessStepInfo(chess: JackaroChess, step: number) {
        let stepInfo = chess.beyondIdx + "_" + chess.id;
        if (this._curAlreadyMoveChessStepInfo.indexOf(stepInfo) < 0) {
            this._curAlreadyMoveChessStepInfo.push(stepInfo);
            this._curAlreadyMoveStep.push(step);
        }
    }

    /**
     * 当前回合所有的棋子是否都已经移动结束
     */
    public checkAlreadyTurnMoveEnd(chess: JackaroChess) {
        if (this.curTurnAlreadyMoveChessInfoes) {
            this.setHasAlreadyTurnMoveEnd(chess);
            if (this.isAllAlreadyTurnMoveEnd()) {
                this.resetChess();
                this.doFastModeMoveChess();
                this.checkDoNextMsg(chess);
            }
        }
    }
    private checkDoNextMsg(chess: JackaroChess) {
        // {{ YUM: [修改] - 根据动效结束时间戳计算延迟时间 }}
        // {{ YUM: [修复] - 传递明确的触发类型参数，避免undefined奖励日志 }}
        if (this.checkPreviewRewardPoker('TURN_END_NEXT_MSG') && yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode()) {
            const currentTime = Date.now();
            const timeDiff = this.drawRewardAnimationEndTime - currentTime;
            const delayTime = Math.max(0, timeDiff); // 确保延迟时间不为负数

            yalla.Debug.yum('消息队列抽牌奖励时间计算:', {
                currentTime,
                animationEndTime: this.drawRewardAnimationEndTime,
                timeDiff,
                delayTime
            });
            Laya.timer.clear(this, this.checkDelayNextMsg);
            this.checkDelayNextMsg(delayTime);
            yalla.Debug.logMore("1111111111111111  checkAlreadyTurnMoveEnd 抽牌奖励延迟时间:", delayTime, "chess.beyondIdx:", chess.beyondIdx, "yalla.Global.isFouce", yalla.Global.isFouce);

        } else {
            yalla.Debug.logMore("222222222222222  checkAlreadyTurnMoveEnd 没有抽牌");
            yalla.data.jackaro.JackaroUserService.instance.nextMsg();
        }
    }
    private checkDelayNextMsg(delayTime: number) {
        if (yalla.Global.isFouce) {
            Laya.timer.once(delayTime, this, () => {
                yalla.Debug.yum('抽牌奖励，队列执行');
                yalla.data.jackaro.JackaroUserService.instance.nextMsg();
            });
        } else {
            yalla.Debug.logMore("333333333333333  checkAlreadyTurnMoveEnd 没有抽牌");
            yalla.data.jackaro.JackaroUserService.instance.nextMsg();
        }
    }

    /**
     * 检查是否需要展示路障
     * @returns 
     */
    public checkShowRoal(color: number, endGridIdx: number) {
        for (let key in this._pool) {
            let ch = this._pool[key];
            let curWayGrid = ch.wayGrid;
            let curGoalGrid = ch.goalGrid;
            let curStation = { area: color, gridPosition: endGridIdx };
            let curGrid: LudoGrid = JackaroBoardData.getGridByStation(curStation);
            let chessColor = JackaroGamePlayerManager.instance.getColorByIdx(ch.beyondIdx)
            let isInWayGrid = chessColor != color && curWayGrid && curWayGrid.inCorner && curWayGrid.myName == (color + "_0") && !this.getChessByGrid(curGrid);
            let isInGoalGrid = chessColor != color && curGoalGrid && curGoalGrid.inCorner && curGoalGrid.myName == (color + "_0") && !this.getChessByGrid(curGrid);
            if (isInWayGrid && isInGoalGrid) {
                return true;
            }
        }
        return false;

    }
    private resetChess() {
        for (let key in this._pool) {
            let chess = this._pool[key];
            chess.choose = false;
            chess.setExchangeChess(false);
        }
    }
    public getCanChooseChess(): JackaroChess[] {
        //获取所有选择中的棋子
        let result = [];
        for (let key in this._pool) {
            let chess = this._pool[key];
            if (chess.choose) {
                result.push(chess);
            }
        }
        return result;

    }
    public recordCurCanChooseChess() {
        for (let key in this._pool) {
            let chess = this._pool[key];
            if (chess.choose) {
                this.recordCanChooseChess.push(key);
            }
        }
    }
    public restoreCurCanChooseChess() {
        for (let key in this._pool) {
            let chess = this._pool[key];
            if (this.recordCanChooseChess.indexOf(key) > -1) {
                chess.choose = true;
            }
        }
    }
    public clearRecordCanChooseChess() {
        this.recordCanChooseChess = [];
    }
    /**
     * 
     * @param commonResult 服务器返回可以走子的信息
     * @param playingPoker 当前出牌
     * @param pokerTenDiscard 是否是出牌10弃牌
     * @param manualOperation 是否是手动操作，因为服务器返回出牌走子时，也会调用这个方法，所以需要区分
     * @returns 
     */
    handlePlayResult(commonResult: Record<string, yalla.data.jackaro.PlayResultDataInterface>, playingPoker: yalla.data.jackaro.Poker, pokerTenDiscard: boolean, manualOperation: boolean = false, isRestore: boolean) {

        if (!commonResult) return;
        //commonResult 的key 是棋子移动的步数，如果 所有的 key都是 0，则表示起飞一个棋子
        let isAllZeroStep = Object.keys(commonResult).every(key => {
            return parseInt(key) == 0;
        })
        // let isHasDisCard = Object.keys(commonResult).some(key => {
        //     return parseInt(key) == 10;
        // })
        let isHasFreeMove = JackaroCardManger.instance.isPoker7(playingPoker);
        //只有一个棋子移动
        let onlyHasOneMoveData = Object.keys(commonResult).length == 1 && commonResult[Object.keys(commonResult)[0]].result.length == 1;
        //相同步数有多个棋子可以移动
        let sameStepMutilChess = Object.keys(commonResult).length == 1 && commonResult[Object.keys(commonResult)[0]].result.length > 1;

        let curMoveChessPlayerIdx = JackaroGamePlayerManager.instance.getMoveChessPlayerIdx();
        this.commonResult = commonResult;
        this.isHasFreeMove = isHasFreeMove;
        if (this.isHasFreeMove) {
            //下次出牌7 时，需要清空当前回合已经击杀的棋子
            this.resetCurAlreadyMoveStep();
            this.curTurnAlreadyKillChess = [];
            this.curTurnMaxChooseChessNum = 2;
        } else {
            this.curTurnMaxChooseChessNum = 1;
        }
        //只有一个棋子移动时，直接操作该棋子
        if (onlyHasOneMoveData) {
            let curMoveStep = Object.keys(commonResult)[0];
            let result = commonResult[curMoveStep];
            if (result.result && result.result.length == 1) {
                this.handleMoveChessBySelf(result.result[0], parseInt(curMoveStep));
            }
        } else {
            //如果是出牌7 ，有两个棋子可以快速移动，直接移动
            if (this.isFastPlayPoker7MoveChess(playingPoker, commonResult)) {
                return;
            }

            //有多个棋子可以移动
            if (!sameStepMutilChess && JackaroCardManger.instance.isMutilStepPlayPoker(playingPoker) && !isRestore) {
                this.createMutilOperateSteps(commonResult, playingPoker, pokerTenDiscard);
            } else {
                for (let key in commonResult) {
                    let result = commonResult[key];
                    let moveStep = parseInt(key);
                    //key = 0 是起飞一个棋子
                    if (parseInt(key) == 0 && isAllZeroStep) {
                        let callBack = () => {
                            this.handleMoveChessBySelf(result.result[0], moveStep);
                        }
                        callBack();
                    } else {
                        let setMutiMoveCallBack = () => {
                            result.result.forEach(item => {
                                if (item.fromGrid && item.fromGrid.chess) {
                                    let chess = this.getChessById(item.fromGrid.chess.idx + "_" + item.fromGrid.chess.order);
                                    if (chess) {
                                        if (isHasFreeMove && curMoveChessPlayerIdx == yalla.Global.Account.idx && item.fromGrid.chess.idx != yalla.Global.Account.idx) {
                                            chess.choose = false;
                                        } else {
                                            chess.choose = true;
                                        }
                                    }
                                }
                            })
                        }
                        setMutiMoveCallBack();
                    }
                }
            }
        }
        if (manualOperation && !onlyHasOneMoveData && (!(JackaroCardManger.instance.isMutilStepPlayPoker(playingPoker) && !sameStepMutilChess))) {
            let tips: string = "Choose a stone\nto move "
            if (sameStepMutilChess) {
                let curMoveStep = Object.keys(commonResult)[0];
                tips = `Choose a stone\nto move ${curMoveStep} steps`;
            }
            TipsViewManager.getInstance().showTip(TipViewType.CHOOSE_CHESS_OPERATE, { operateTime: JackaroGamePlayerManager.instance.getOperateTime(), tips: tips });
        }

    }
    /**
     * 是否是快速出牌7(两颗棋子刚好移动7步，同属同一个玩家)
     */
    private isFastPlayPoker7MoveChess(playingPoker: yalla.data.jackaro.Poker, commonResult: Record<string, yalla.data.jackaro.PlayResultDataInterface>) {
        if (!commonResult) return false;
        if (!JackaroCardManger.instance.isPoker7(playingPoker)) return false;
        let totalMoveStep = 0;
        let chessNum = 0;
        let beyondIdxs: Array<{ step: number, beyondIdx: number, order: number }> = [];
        for (let key in commonResult) {
            let result = commonResult[key];
            let moveStep = parseInt(key);
            chessNum += result.result.length;
            for (let i = 0; i < result.result.length; i++) {
                totalMoveStep += moveStep;
                let item = result.result[i];
                if (item.fromGrid && item.fromGrid.chess) {
                    beyondIdxs.push({ step: moveStep, beyondIdx: item.fromGrid.chess.idx, order: item.fromGrid.chess.order });
                }
            }
        }
        let isSameBeyondIdx = false;
        if (beyondIdxs.length == 2) {
            isSameBeyondIdx = beyondIdxs[0].beyondIdx == beyondIdxs[1].beyondIdx;
        }
        if (totalMoveStep == JackaroChessManager._poker7Step && chessNum == 2) {
            for (let key in commonResult) {
                let result = commonResult[key];
                let moveStep = parseInt(key);
                this.handleMoveChessBySelf(result.result[0], moveStep);
            }
            let curIdx = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx();
            if (beyondIdxs[0] && beyondIdxs[0].beyondIdx != curIdx) {
                beyondIdxs.reverse();
            }
            yalla.data.jackaro.JackaroUserService.instance.sendFreeMoveChess(playingPoker, { operatedChess: 0, firstData: { idx: beyondIdxs[0].beyondIdx, order: beyondIdxs[0].order, number: beyondIdxs[0].step }, secondData: { idx: beyondIdxs[1].beyondIdx, order: beyondIdxs[1].order, number: beyondIdxs[1].step } });
            return true;
        }

        return false;
    }
    /**
     * 创建出牌 A 或者 10 或者 K 时操作步骤（包含复杂玩法）,1.4.5 增加1v1 出牌2
     */
    createMutilOperateSteps(commonResult: Record<string, yalla.data.jackaro.PlayResultDataInterface>, playingPoker: yalla.data.jackaro.Poker, pokerTenDiscard: boolean) {
        if (!commonResult) {
            return;
        }
        let isHasPokerA = JackaroCardManger.instance.isPokerA(playingPoker);
        let isHasPokerK = JackaroCardManger.instance.isPokerA(playingPoker);
        let isHasPoker10 = JackaroCardManger.instance.isPoker10(playingPoker);
        let operateCallBack: Record<string, Function> = {};
        let isAllZeroStep = Object.keys(commonResult).every(key => {
            return parseInt(key) == 0;
        })
        //相同步数有多个棋子可以移动
        let sameStepMutilChess = Object.keys(commonResult).length == 1 && commonResult[Object.keys(commonResult)[0]].result.length > 1;
        for (let key in commonResult) {
            let result = commonResult[key];
            let moveStep = parseInt(key);
            let isOnlyOneMove = Object.keys(result).length == 1 && result[Object.keys(result)[0]].length == 1;

            if (parseInt(key) == 0 && isAllZeroStep && !pokerTenDiscard) {
                let callBack = () => {
                    this.handleMoveChessBySelf(result.result[0], moveStep);
                }
                if (isHasPokerA || isHasPokerK) {
                    operateCallBack[key] = callBack;
                }
            } else {
                let setMutiMoveCallBack = () => {
                    result.result.forEach(item => {
                        if (item.fromGrid && item.fromGrid.chess) {
                            let chess = this.getChessById(item.fromGrid.chess.idx + "_" + item.fromGrid.chess.order);
                            if (chess) {
                                chess.choose = true;
                            }
                        }
                    })
                }
                if (isOnlyOneMove) {
                    operateCallBack[key] = () => {
                        this.handleMoveChessBySelf(result.result[0], moveStep);
                        yalla.data.jackaro.JackaroUserService.instance.sendPlayPoker(playingPoker, { idx: result.result[0].fromGrid.chess.idx, order: result.result[0].fromGrid.chess.order, number: moveStep });
                    }
                } else {
                    operateCallBack[key] = () => {
                        this.pakerAOr10MutilMoveStep = moveStep;
                        setMutiMoveCallBack();
                        let tips: string = "Choose a stone\nto move ";
                        if (sameStepMutilChess) {
                            let curMoveStep = Object.keys(commonResult)[0];
                            tips = `Choose a stone\nto move ${curMoveStep} steps`;
                            // let allowPokerData = JackaroCardManger.instance.getPlayResultData(playingPoker);
                            // if (isHasPokerA) {
                            //     if (allowPokerData) {
                            //         JackaroChessManager.instance.showChessOrbitByLength(playingPoker, allowPokerData, moveStep);
                            //     }
                            // }
                        }
                        TipsViewManager.getInstance().showTip(TipViewType.CHOOSE_CHESS_OPERATE, { operateTime: JackaroGamePlayerManager.instance.getOperateTime(), tips: tips });
                    }
                }
            }
        }
        TipsViewManager.getInstance().showTip(TipViewType.SELECT_OPERATE, { operateCallBack: operateCallBack, poker: playingPoker, operateTime: JackaroGamePlayerManager.instance.getOperateTime() });
    }
    /**
     * 更新选择棋子界面，主要用杀端重进时 选择 出牌A 出牌10 出牌A
     */
    showSelectChessView() {
        if (this.commonResult) {
            for (let key in this.commonResult) {
                let moveStep = parseInt(key);
                //此时这种杀端重进的时，如果是选择棋子界面恢复，需要踢出起飞棋子
                if (moveStep == 0) continue;
                let result = this.commonResult[key];
                result.result.forEach(item => {
                    if (item.fromGrid && item.fromGrid.chess) {
                        let chess = this.getChessById(item.fromGrid.chess.idx + "_" + item.fromGrid.chess.order);
                        if (chess) {
                            chess.choose = true;
                        }
                    }
                })
            }
        }

        if (this.impactResult) {
            this.impactResult.forEach(item => {
                if (item.fromGrid && item.fromGrid.chess) {
                    let chess = this.getChessById(item.fromGrid.chess.idx + "_" + item.fromGrid.chess.order);
                    if (chess) {
                        chess.choose = true;
                    }
                }
            })
        }
        let tips: string = "Choose a stone\nto move ";
        TipsViewManager.getInstance().showTip(TipViewType.CHOOSE_CHESS_OPERATE, { operateTime: JackaroGamePlayerManager.instance.getOperateTime(), tips: tips });
    }
    /**
     * 复杂玩法出牌K ，因为出牌K的数据结构和普通出牌不一样，所以单独处理
     * @param complexKingData 
     * @param poker 
     */
    handlerPokerKPlayPoker(complexKingData, poker: yalla.data.jackaro.Poker, isRestore: boolean) {
        let impactResult = complexKingData.impactResult;
        this.impactResult = impactResult;
        let operateCallBack: Record<string, Function> = {};
        if (complexKingData.flyData && complexKingData.flyData.fromGrid) {
            let callBack = () => {
                JackaroChessManager.instance.handleMoveChessBySelf(complexKingData.flyData, 0);
                yalla.data.jackaro.JackaroUserService.instance.sendPlayPoker(poker, { idx: complexKingData.flyData.fromGrid.chess.idx, order: complexKingData.flyData.fromGrid.chess.order, number: 0 });
            }
            operateCallBack["0"] = callBack;
        }

        let setMutiMoveCallBack = () => {
            impactResult.forEach(item => {
                if (item.fromGrid && item.fromGrid.chess) {
                    let chess = this.getChessById(item.fromGrid.chess.idx + "_" + item.fromGrid.chess.order);
                    if (chess) {
                        chess.choose = true;
                    }
                }
            })
        }
        let moveCallBack = () => {
            if (impactResult && impactResult.length == 1) {
                JackaroChessManager.instance.handleMoveChessBySelf(impactResult[0], 13);
                yalla.data.jackaro.JackaroUserService.instance.sendPlayPoker(poker, { idx: impactResult[0].fromGrid.chess.idx, order: impactResult[0].fromGrid.chess.order, number: 13 });
            } else {
                this.pakerAOr10MutilMoveStep = 13;
                setMutiMoveCallBack();
                let tips = `Choose a stone\nto move ${this.pakerAOr10MutilMoveStep} steps`;
                TipsViewManager.getInstance().showTip(TipViewType.CHOOSE_CHESS_OPERATE, { operateTime: JackaroGamePlayerManager.instance.getOperateTime(), tips: tips });
            }
        }
        if (impactResult && impactResult.length) {
            operateCallBack["13"] = moveCallBack;
        }
        if (operateCallBack["0"] && !operateCallBack["13"]) {
            //只能起飞时，直接起飞棋子
            operateCallBack["0"]();
        } else if (!operateCallBack["0"] && operateCallBack["13"] && impactResult && impactResult.length == 1) {
            JackaroChessManager.instance.handleMoveChessBySelf(impactResult[0], 13);
            yalla.data.jackaro.JackaroUserService.instance.sendPlayPoker(poker, { idx: impactResult[0].fromGrid.chess.idx, order: impactResult[0].fromGrid.chess.order, number: 13 });
        } else if (!operateCallBack["0"] && operateCallBack["13"] && impactResult && impactResult.length > 1) {
            moveCallBack && moveCallBack();
        } else {
            if (!isRestore) {
                TipsViewManager.getInstance().showTip(TipViewType.SELECT_OPERATE, { operateCallBack: operateCallBack, poker: poker, operateTime: JackaroGamePlayerManager.instance.getOperateTime() });
            }
        }
    }
    setCommonResult(commonResult: Record<string, yalla.data.jackaro.PlayResultDataInterface>) {
        this.commonResult = commonResult;
    }
    setImpactResult(impactResult: Array<yalla.data.jackaro.ImpactChessResultInterface>) {
        this.impactResult = impactResult;
    }
    /**
     * 准备棋子交换数据
     * @param fromChessInfo 源棋子信息
     * @param toChessInfo 目标棋子信息
     * @returns 是否可以执行交换
     */
    private prepareChessExchange(fromChessInfo: { idx: number, order: number }, toChessInfo: { idx: number, order: number }): boolean {
        // 清空之前的交换记录
        this.recordExchangeChess = [];

        // 添加交换棋子信息
        this.recordExchangeChess.push({ idx: fromChessInfo.idx, order: fromChessInfo.order, number: 0 });
        this.recordExchangeChess.push({ idx: toChessInfo.idx, order: toChessInfo.order, number: 0 });

        // 确保当前玩家的棋子在第一位
        let curMoveChessPlayerIdx = JackaroGamePlayerManager.instance.getMoveChessPlayerIdx();
        if (this.recordExchangeChess[0].idx != curMoveChessPlayerIdx) {
            [this.recordExchangeChess[0], this.recordExchangeChess[1]] = [this.recordExchangeChess[1], this.recordExchangeChess[0]];
        }

        // 检查操作时间
        let operateTime: number = JackaroGamePlayerManager.instance.getOperateTime();
        if (operateTime < 3000) {
            return false;
        }

        return true;
    }

    /**
     * 执行棋子交换
     * @param playAnimation 是否播放动画
     * @param sendToServer 是否发送到服务器
     */
    private executeChessExchange(playAnimation: boolean = true, sendToServer: boolean = true): void {
        if (this.recordExchangeChess.length !== 2) {
            return;
        }

        let fromChess = this.getChessById(this.recordExchangeChess[0].idx + "_" + this.recordExchangeChess[0].order);
        let toChess = this.getChessById(this.recordExchangeChess[1].idx + "_" + this.recordExchangeChess[1].order);

        if (!fromChess || !toChess) {
            return;
        }

        if (playAnimation) {
            // 播放开始动画
            this.playExchangeStartSpinAni(fromChess);
            this.playExchangeStartSpinAni(toChess);
            // 播放交换效果
            this.playExchangeEffect();
        }

        if (sendToServer) {
            // 发送交换请求到服务器
            let curPlayPoker = JackaroCardManger.instance.getCurPlayPoker();
            yalla.data.jackaro.JackaroUserService.instance.sendExchangeChess(curPlayPoker, this.recordExchangeChess);
        }
    }

    handlePlayJackResult(jackResult: yalla.data.jackaro.PlayJackResultDataInterface) {
        this.recordExchangeChess = [];
        this.jackResult = jackResult;
        this.isExchangeChess = true;
        this.curTurnMaxChooseChessNum = 2;
        //1.4.5 如果只有一颗棋子可以交换，则直接交换
        let isOnlyOneMoveExchange = false;
        if (jackResult && Object.keys(jackResult.playResultData).length == 1) {
            const playerData = jackResult.playResultData[Object.keys(jackResult.playResultData)[0]];
            if (playerData && playerData.result && playerData.result.length == 1) {
                // 实现直接交换逻辑
                isOnlyOneMoveExchange = true;
                const fromChessInfo = { idx: playerData.result[0].fromGrid.chess.idx, order: playerData.result[0].fromGrid.chess.order };
                const toChessInfo = { idx: playerData.result[0].toGrid.chess.idx, order: playerData.result[0].toGrid.chess.order };

                if (!this.prepareChessExchange(fromChessInfo, toChessInfo)) {
                    return;
                }

                let fromChess = this.getChessById(this.recordExchangeChess[0].idx + "_" + this.recordExchangeChess[0].order);
                let toChess = this.getChessById(this.recordExchangeChess[1].idx + "_" + this.recordExchangeChess[1].order);
                this.playExchangeStartSpinAni(fromChess);
                this.playExchangeStartSpinAni(toChess);
                this.executeChessExchange(true, true);
            }
        }

        if (!isOnlyOneMoveExchange) {
            this.updatePlayJackResultChessChoose();
            TipsViewManager.getInstance().showTip(TipViewType.EXCHANGE_CHESS, { operateTime: JackaroGamePlayerManager.instance.getOperateTime() });
            (TipsViewManager.getInstance().getTipView(TipViewType.EXCHANGE_CHESS) as ExChangeChessView).setCurStep(this.recordExchangeChess.length);
        }
    }
    public updatePlayJackResultChessChoose() {
        if (!this.jackResult) return;
        if (this.recordExchangeChess.length == 2) {
            return;
        }
        let curShowPlayerChessIdx: number;
        let curShowPlayerChessIdxExclude: number;
        let curMoveChessPlayerIdx = JackaroGamePlayerManager.instance.getMoveChessPlayerIdx();
        if (this.recordExchangeChess.length == 0) {
            curShowPlayerChessIdx = curMoveChessPlayerIdx;
        } else if (this.recordExchangeChess.length == 1) {
            curShowPlayerChessIdxExclude = curMoveChessPlayerIdx;
        }
        let playResultData = this.jackResult.playResultData;
        for (let key in playResultData) {
            let playResult = playResultData[key];
            let result = playResult.result;
            for (let i = 0; i < result.length; i++) {
                let item = result[i];
                let fromGridData = item.fromGrid;
                let toGridData = item.toGrid;

                let fromChessInfo = fromGridData.chess.idx + "_" + fromGridData.chess.order;
                let fromChess = this.getChessById(fromChessInfo);
                fromChess.choose = false;
                if (fromGridData.chess.idx == curShowPlayerChessIdx || (curShowPlayerChessIdxExclude && fromGridData.chess.idx != curShowPlayerChessIdxExclude)) {
                    fromChess.choose = true;
                }

                if (this.recordExchangeChess.length == 1) {
                    if (fromGridData.chess.idx == this.recordExchangeChess[0].idx && fromGridData.chess.order == this.recordExchangeChess[0].order) {
                        fromChess.choose = true;
                    }
                }
                if (!toGridData.chess) {
                    continue;
                }
                let toChessInfo = toGridData.chess.idx + "_" + toGridData.chess.order;
                let toChess = this.getChessById(toChessInfo);
                toChess.choose = false;
                if (toGridData.chess.idx == curShowPlayerChessIdx || (curShowPlayerChessIdxExclude && toGridData.chess.idx != curShowPlayerChessIdxExclude)) {
                    toChess.choose = true;
                }
            }
        }

        if (TipsViewManager.getInstance().isShow(TipViewType.EXCHANGE_CHESS)) {
            (TipsViewManager.getInstance().getTipView(TipViewType.EXCHANGE_CHESS) as ExChangeChessView).checkShowChessMask();
            (TipsViewManager.getInstance().getTipView(TipViewType.EXCHANGE_CHESS) as ExChangeChessView).setCurStep(this.recordExchangeChess.length);
        }
        if (this.recordExchangeChess.length == 1) {
            let chess = this.getChessById(this.recordExchangeChess[0].idx + "_" + this.recordExchangeChess[0].order);
            chess && JkChooseChessManager.ins.removeWidhIdKey(chess.idKey)
        }
    }
    getPlayResultItemByNumberAndOrder(number: number, order: number, idx: number): yalla.data.jackaro.PlayResultItemDataInterface | yalla.data.jackaro.ImpactChessResultInterface {
        let result = this.commonResult[number];
        if (result) {
            for (let i = 0; i < result.result.length; i++) {
                let item = result.result[i];
                if (item.fromGrid && item.fromGrid.chess && item.fromGrid.chess.idx == idx && item.fromGrid.chess.order == order) {
                    return item;
                }
            }
        }
        if (this.impactResult) {
            for (let index = 0; index < this.impactResult.length; index++) {
                const element = this.impactResult[index];
                if (element.fromGrid && element.fromGrid.chess && element.fromGrid.chess.idx == idx && element.fromGrid.chess.order == order) {
                    return element;
                }

            }
        }
        return null;
    }
    /**
     * 根据棋子轨迹路径，路径有长有短，同一个格子上优先显示击杀格子和终点格子，如果存在轨迹重复，优先距离轨迹开始近的格子
     * @param poker 
     * @param result 
     * @param stepNum 
     * @param isClientPreview 是否是客户端预览
     * @returns 
     */
    showChessOrbitByLength(poker: yalla.data.jackaro.Poker, result: Record<string, yalla.data.jackaro.PlayResultDataInterface>, stepNum: number = 0, isClientPreview: boolean = false) {
        if (!isClientPreview && (JackaroGamePlayerManager.instance.getPlayerCanDiscard(yalla.Global.Account.idx) || !JackaroGamePlayerManager.instance.isMyActive())) {
            this.removeAllChessOrbit();//此逻辑理解为防止高频点击误触
            return;
        }
        let totalLineGirds = this.createLineGirds(poker, result, stepNum, isClientPreview);
        // {{ YUM: [Enhanced] - 扩展 recorGrid 数据结构，支持被击杀棋子信息 }}
        let recorGrid: Record<string, { grid: LudoGrid, color: number, kill: boolean, isEnd: boolean, killedChessIdx?: number }> = {};
        for (let i = 0; i < totalLineGirds.length; ++i) {
            let moveGrids = totalLineGirds[i];
            // {{ YUM: [KillInfo] - 尝试获取被击杀棋子信息，从目标格子获取 }}
            let killedChessIdx: number | undefined = undefined;
            if (moveGrids.kill) {
                const lastGrid = moveGrids.grid[moveGrids.grid.length - 1];
                if (lastGrid) {
                    const gridInfo = JackaroChessOrbitManager.instance.getGridInfo(lastGrid.myName);
                    if (gridInfo && gridInfo.idx !== undefined) {
                        killedChessIdx = gridInfo.idx;
                        yalla.Debug.yum("JackaroChessManager", `击杀轨道数据 格子:${lastGrid.myName} 被击杀玩家:${killedChessIdx}`);
                    }
                }
            }

            for (let j = 0; j < moveGrids.grid.length; ++j) {
                let grid = moveGrids.grid[j];
                if (!recorGrid[grid.myName]) {
                    if (j == moveGrids.grid.length - 1) {
                        if (moveGrids.kill) {
                            recorGrid[grid.myName] = { grid: grid, color: moveGrids.color, kill: moveGrids.kill, isEnd: false, killedChessIdx: killedChessIdx };
                        } else {
                            recorGrid[grid.myName] = { grid: grid, color: moveGrids.color, kill: moveGrids.kill, isEnd: true };
                        }
                    } else {
                        recorGrid[grid.myName] = { grid: grid, color: moveGrids.color, kill: false, isEnd: false };
                    }
                } else {
                    if (j == moveGrids.grid.length - 1) {
                        if (moveGrids.kill) {
                            recorGrid[grid.myName] = { grid: grid, color: moveGrids.color, kill: moveGrids.kill, isEnd: false, killedChessIdx: killedChessIdx };
                        } else {
                            recorGrid[grid.myName] = { grid: grid, color: moveGrids.color, kill: moveGrids.kill, isEnd: true };
                        }
                    } else {
                        // recorGrid[grid.myName] = { grid: grid, color: moveGrids.color, kill: false, isEnd: false };
                    }
                }
            }
        }
        // {{ YUM: [Optimize] - 使用新的动效管理器智能更新轨道动效，避免闪烁。不再提前清除 }}
        const orbitData = JackaroChessOrbitEffectManager.instance.convertFromGridData(recorGrid);
        JackaroChessOrbitEffectManager.instance.updateOrbitEffects(orbitData);

        // {{ YUM: [Legacy] - 先清除旧的轨道图片，然后处理新的轨道图片 }}
        this.removeChessOrbitItem();

        let chessAniParent = this.mapLayer.getChildByName("props_box");
        for (let key in recorGrid) {
            let chessData = recorGrid[key];
            // let gridWithChess = this.getChessByGrid(chessData.grid);
            // if (gridWithChess && !chessData.kill) continue;
            var gridInfo = JackaroChessOrbitManager.instance.getGridInfo(key);
            if (gridInfo && !chessData.kill) {
                continue;
            }


            // 只处理普通轨道图片，动效已由新管理器处理
            if (!chessData.kill) {
                let chessOrbitItem = this.getChessOrbitItem();
                chessOrbitItem.skin = `jackaro/chess${chessData.color}_orbit.png`;
                this.chessOrbitItem.push(chessOrbitItem);
                chessAniParent.addChild(chessOrbitItem);
                chessOrbitItem.pos(chessData.grid.port.x, chessData.grid.port.y);
            }
        }

    }
    public createLineGirds(poker: yalla.data.jackaro.Poker, result: Record<string, yalla.data.jackaro.PlayResultDataInterface>, stepNum: number = 0, isClientPreview: boolean = false) {
        let myIdx = yalla.Global.Account.idx;
        let recordChessMoveStep: Record<string, number> = {};
        let totalLineGirds: Array<{ grid: LudoGrid[]; color: number; kill: boolean, beginGrid: LudoGrid, orbitName: string }> = [];
        if (result && Object.keys(result).length > 0) {
            for (let key in result) {
                let moveStep = parseInt(key);
                if (stepNum && stepNum != moveStep) {
                    continue;
                }
                let playResultData = result[key];
                for (let i = 0; i < playResultData.result.length; i++) {
                    let item = playResultData.result[i];
                    let fromGridData = item.fromGrid;
                    if (!fromGridData || !fromGridData.chess) {
                        continue;
                    }
                    let orbitName = fromGridData.chess.idx + "_" + fromGridData.chess.order;
                    //如果棋子正在移动，不生成预览
                    let curOrbitChess = this.getChessById(orbitName);
                    if (curOrbitChess && curOrbitChess.getIsMoving()) {
                        continue;
                    }
                    let curColor = fromGridData.chess.color || 0;
                    let toGridData = item.toGrid;
                    let kill = item.kill;
                    //1.4.5 如果存在传送点，终点格子是传送点
                    if (item.transferGrid) {
                        toGridData = item.transferGrid;
                    }
                    let fromGrid = fromGridData.grid;
                    let toGrid = toGridData.grid;

                    let nextColor = toGrid.color || 0;
                    let fromGridPosition = fromGrid.index;
                    //1.4.5 增加起飞预览，服务器的格子是301 401 这种格式的，和客户端的格子不统一
                    // 写199 是防止后续扩展格子
                    if (fromGridPosition > 199) {
                        fromGridPosition = 100 + fromGridPosition % 100;
                    }

                    let fromStation = { area: fromGrid.color, gridPosition: fromGridPosition };
                    let toStation = { area: nextColor, gridPosition: toGrid.index || 0 };
                    var fromLudoGrid: LudoGrid = JackaroBoardData.getGridByStation(fromStation);
                    let toLudoGrid: LudoGrid = JackaroBoardData.getGridByStation(toStation);
                    let moveGrids = this.getPathsPos(toLudoGrid, fromLudoGrid, moveStep >= 0 ? true : false, nextColor);
                    if (item.transferGrid) {
                        let endStation = { area: item.toGrid.grid.color || 0, gridPosition: item.toGrid.grid.index || 0 };
                        let endGrid = JackaroBoardData.getGridByStation(endStation);
                        if (endGrid && this.getChessByGrid(endGrid)) {
                            moveGrids.push(endGrid);
                        }
                    }
                    if (moveGrids && moveGrids.length > 0) {
                        //1.4.5 取消之前比如扑克A 有多步可以选择时，只需要最长的路径
                        // if (recordChessMoveStep[orbitName]) {
                        //     if (recordChessMoveStep[orbitName] < moveGrids.length) {
                        //         //删除旧记录
                        //         for (let j = 0; j < totalLineGirds.length; j++) {
                        //             let moveGrids = totalLineGirds[j];
                        //             if (moveGrids.grid.length == recordChessMoveStep[orbitName] && moveGrids.orbitName == orbitName) {
                        //                 totalLineGirds.splice(j, 1);
                        //                 break;
                        //             }
                        //         }
                        //         recordChessMoveStep[orbitName] = moveGrids.length;
                        //         totalLineGirds.push({ grid: moveGrids, color: curColor, kill: kill, beginGrid: moveGrids[0], orbitName: orbitName });
                        //     }
                        // } else {
                        totalLineGirds.push({ grid: moveGrids, color: curColor, kill: kill, beginGrid: moveGrids[0], orbitName: orbitName });
                        recordChessMoveStep[orbitName] = moveGrids.length;
                        // }
                    }
                }
            }
        }
        // else {
        // }
        /**
         * 	export enum JackarooColor {
            RED = 0,
            GREEN = 1,
            YELLOW = 2,
            BLUE = 3,
        }
         */
        //排序 因为棋盘是环形 默认逆时针 红色 蓝 黄 绿，但走子是顺时针 红色 绿 黄 蓝，所以根据颜色 和 每组起点grid 来 按照顺时针的走法排序一下
        totalLineGirds.sort((a, b) => {
            let aColor = a.color;
            let bColor = b.color;
            //如果颜色不一样，按照颜色排序,是顺时针走法，红色 绿 黄 蓝，形成环形
            //需要按照顺时针顺序排序：RED(0) -> GREEN(1) -> YELLOW(2) -> BLUE(3) -> RED(0)
            //当从蓝色区域跨到红色区域时，需要特殊处理这种环形结构
            if (aColor != bColor) {
                // 使用gridColorSort数组来映射颜色的顺序
                let aColorIndex = this.gridColorSort.indexOf(aColor);
                let bColorIndex = this.gridColorSort.indexOf(bColor);
                return aColorIndex - bColorIndex;
            }
            //如果颜色一样，就按照起点grid 来排序，谁的起点grid.station.gridPosition 小，谁就排前面
            let aBeginGrid = a.beginGrid;
            let bBeginGrid = b.beginGrid;
            return aBeginGrid.station.gridPosition - bBeginGrid.station.gridPosition;
        });

        return totalLineGirds;
    }
    /**
     * 显示Poker K 棋子预览轨道
     */
    showPokerKChessOrbit(poker: yalla.data.jackaro.Poker, complexKing: yalla.data.jackaro.ComplexPlayKingResultInterface, stepNum: number = 0, isClientPreview: boolean = false) {
        if (!isClientPreview && (JackaroGamePlayerManager.instance.getPlayerCanDiscard(yalla.Global.Account.idx) || !JackaroGamePlayerManager.instance.isMyActive())) {
            this.removeAllChessOrbit();//此逻辑理解为防止高频点击误触
            return;
        }
        if (!complexKing) {
            return;
        }
        // {{ YUM: [Enhanced] - 扩展 Poker K 的 recorGrid 数据结构，支持被击杀棋子信息 }}
        let recorGrid: Record<string, { grid: LudoGrid, color: number, kill: boolean, isEnd: boolean, killedChessIdx?: number }> = {};
        if (complexKing.flyData && complexKing.flyData.toGrid) {
            let toGridData = complexKing.flyData.toGrid;
            let nextColor = toGridData.grid.color || 0;
            let nextOrbitName = nextColor + "_" + toGridData.grid.index;
            let moveGrid = JackaroBoardData.getGridByName(nextOrbitName);
            recorGrid[nextOrbitName] = { grid: moveGrid, color: nextColor, kill: complexKing.flyData.kill, isEnd: true };
        }
        let impactResult = complexKing.impactResult;
        if (Object.keys(recorGrid).length > 0 || (impactResult && impactResult.length > 0)) {
            // {{ YUM: [Legacy] - 保留原有的复杂轨道逻辑记录，仅处理轨道图片 }}
            this.removeChessOrbitItem();

            if ((impactResult && impactResult.length > 0)) {
                for (let i = 0; i < impactResult.length; i++) {
                    let playResultData = impactResult[i];

                    let fromGridData = playResultData.fromGrid;
                    if (!fromGridData || !fromGridData.chess) continue;
                    let orbitName = fromGridData.chess.idx + "_" + fromGridData.chess.order;
                    let curColor = fromGridData.chess.color || 0;
                    let toGridData = playResultData.toGrid;
                    //1.4.5 如果存在传送点，终点格子是传送点
                    if (playResultData.transferGrid) {
                        toGridData = playResultData.transferGrid;
                    }
                    let fromGrid = fromGridData.grid;
                    let toGrid = toGridData.grid;
                    let nextColor = toGrid.color || 0;

                    let fromStation = { area: fromGrid.color, gridPosition: fromGrid.index };
                    let toStation = { area: nextColor, gridPosition: toGrid.index || 0 };
                    var fromLudoGrid: LudoGrid = JackaroBoardData.getGridByStation(fromStation);
                    let toLudoGrid: LudoGrid = JackaroBoardData.getGridByStation(toStation);
                    let moveGrids = this.getPathsPos(toLudoGrid, fromLudoGrid, true, nextColor);
                    if (playResultData.transferGrid) {
                        let endStation = { area: playResultData.toGrid.grid.color || 0, gridPosition: playResultData.toGrid.grid.index || 0 };
                        let endGrid = JackaroBoardData.getGridByStation(endStation);
                        if (endGrid && this.getChessByGrid(endGrid)) {
                            moveGrids.push(endGrid);
                        }
                    }
                    if (moveGrids && moveGrids.length > 0) {
                        for (let j = 0; j < moveGrids.length; j++) {
                            let grid = moveGrids[j];
                            let impactedChess = playResultData.impactedChess;
                            let canKill = false;
                            let killedChessIdx: number | undefined = undefined;
                            for (let k = 0; k < impactedChess.length; k++) {
                                let impactedChessData = impactedChess[k];
                                let impactedChessGrid = impactedChessData.from.grid;
                                let fromStation = { area: impactedChessGrid.color, gridPosition: impactedChessGrid.index };

                                var fromLudoGrid: LudoGrid = JackaroBoardData.getGridByStation(fromStation);
                                if (fromLudoGrid.atGrid(moveGrids[j].myName)) {
                                    canKill = true;
                                    // {{ YUM: [KillInfo] - Poker K 中获取被击杀棋子的玩家索引 }}
                                    if (impactedChessData.from && impactedChessData.from.chess) {
                                        killedChessIdx = impactedChessData.from.chess.idx;
                                        yalla.Debug.yum("JackaroChessManager", `PokerK击杀轨道数据 格子:${grid.myName} 被击杀玩家:${killedChessIdx}`);
                                    }
                                    break;
                                }
                            }

                            if (!recorGrid[grid.myName]) {
                                if (j == moveGrids.length - 1) {
                                    if (canKill) {
                                        recorGrid[grid.myName] = { grid: grid, color: curColor, kill: canKill, isEnd: false, killedChessIdx: killedChessIdx };
                                    } else {
                                        recorGrid[grid.myName] = { grid: grid, color: curColor, kill: canKill, isEnd: true };
                                    }
                                } else {
                                    recorGrid[grid.myName] = { grid: grid, color: curColor, kill: canKill, isEnd: false };
                                }
                            } else {
                                if (j == moveGrids.length - 1) {
                                    if (canKill) {
                                        recorGrid[grid.myName] = { grid: grid, color: curColor, kill: canKill, isEnd: false, killedChessIdx: killedChessIdx };
                                    } else {
                                        recorGrid[grid.myName] = { grid: grid, color: curColor, kill: canKill, isEnd: true };
                                    }
                                } else {
                                    // recorGrid[grid.myName] = { grid: grid, color: moveGrids.color, kill: false, isEnd: false };
                                }
                            }
                        }
                    }

                }
            }

            // {{ YUM: [Optimize] - 使用新的动效管理器处理Poker K复杂轨道，避免闪烁 }}
            const complexOrbitData = JackaroChessOrbitEffectManager.instance.convertFromGridData(recorGrid);
            JackaroChessOrbitEffectManager.instance.updateOrbitEffects(complexOrbitData);

            let chessAniParent = this.mapLayer.getChildByName("props_box");
            for (let key in recorGrid) {
                let chessData = recorGrid[key];
                // let gridWithChess = this.getChessByGrid(chessData.grid);
                // if (gridWithChess && !chessData.kill) continue;
                var gridInfo = JackaroChessOrbitManager.instance.getGridInfo(key);
                if (gridInfo && !chessData.kill) {
                    continue;
                }

                // 只处理普通轨道图片，动效已由新管理器处理
                if (!chessData.kill) {
                    let chessOrbitItem = this.getChessOrbitItem();
                    chessOrbitItem.skin = `jackaro/chess${chessData.color}_orbit.png`;
                    this.chessOrbitItem.push(chessOrbitItem);
                    chessAniParent.addChild(chessOrbitItem);
                    chessOrbitItem.pos(chessData.grid.port.x, chessData.grid.port.y);
                }
            }
        } else {
            this.removeAllChessOrbit();
        }
    }

    /**
     * 通过普通出牌吃掉路径上的棋子特效
     */
    private checkPlayEatChessEff(chess: JackaroChess) {
        if (!chess) {
            return;
        }
        let chessPlayPokerKillSpineAni = this.getChessKillSpineAni();
        if (chessPlayPokerKillSpineAni) {
            let chessWayGrid = chess.wayGrid;
            this.chessPlayPokerKillSpineAniArr.push(chessPlayPokerKillSpineAni);
            this.mapLayer.getChildByName("ani_box").addChild(chessPlayPokerKillSpineAni);
            chessPlayPokerKillSpineAni.pos(chessWayGrid.port.x, chessWayGrid.port.y);
            chessPlayPokerKillSpineAni.play("animation");
            chessPlayPokerKillSpineAni.name = chessWayGrid.myName;

        }
    }
    public removeChessKillSpineAni() {
        for (let index = 0; index < this.chessPlayPokerKillSpineAniArr.length; index++) {
            const element = this.chessPlayPokerKillSpineAniArr[index];
            element.stop();
            element.name = "";
            element.removeSelf();
            !element.destroyed && Laya.Pool.recover("ChessKillSpineAni", element);
        }
        this.chessPlayPokerKillSpineAniArr.length = 0;
    }
    /**
     * 获取棋子可以移动步数
     */
    getCanMoveChessStep(chessID: number, beyondIdx: number): number {
        if (this.commonResult) {
            for (let key in this.commonResult) {
                let result = this.commonResult[key];
                if (!result) continue;
                for (let i = 0; i < result.result.length; i++) {
                    let item = result.result[i];
                    if (item.fromGrid && item.fromGrid.chess && item.fromGrid.chess.order == chessID && item.fromGrid.chess.idx == beyondIdx) {
                        return parseInt(key);
                    }
                }
            }
        }
        return 0;
    }
    /**
     * 获取当前回合一共有多少颗棋子可以移动
     * @param beyondIdx 
     * @returns 
     */
    getCanTurnMoveChessNum(beyondIdx: number): number {
        let canMoveChessOrder: number[] = [];
        if (this.commonResult) {
            for (let key in this.commonResult) {
                let result = this.commonResult[key];
                if (!result) continue;
                for (let i = 0; i < result.result.length; i++) {
                    let item = result.result[i];
                    if (parseInt(key) > 0 && item.fromGrid && item.fromGrid.chess && item.fromGrid.chess.idx == beyondIdx) {
                        if (canMoveChessOrder.indexOf(item.fromGrid.chess.order) == -1) {
                            canMoveChessOrder.push(item.fromGrid.chess.order);
                        }
                    }
                }
            }
        }
        return canMoveChessOrder.length;
    }

    /**
    * 获取棋子可以移动步数
    */
    getMaxMoveChessStepByExclude(excludeChessID: number): number {
        let curMaxStep = 0;
        if (this.commonResult) {
            for (let key in this.commonResult) {
                let result = this.commonResult[key];
                if (!result) continue;
                for (let i = 0; i < result.result.length; i++) {
                    let item = result.result[i];
                    if (item.fromGrid && item.fromGrid.chess && item.fromGrid.chess.order != excludeChessID) {
                        let curChessStep = parseInt(key);
                        if (curMaxStep < curChessStep) {
                            curMaxStep = curChessStep;
                        }
                    }
                }
            }
        }
        return curMaxStep;
    }
    /**
     * 获取可以移动最大步数和最小步数
     */
    public getChessMoveStepRange(): { minStep: number, maxStep: number } {
        let minStep = JackaroChessManager._poker7Step;
        let maxStep = 0;
        if (this.commonResult) {
            for (let key in this.commonResult) {
                let result = this.commonResult[key];
                if (!result) continue;
                let curMoveStepNum = parseInt(key);
                if (curMoveStepNum < minStep) {
                    minStep = curMoveStepNum;
                }
                if (curMoveStepNum > maxStep) {
                    maxStep = curMoveStepNum;
                }
            }
        }
        if (maxStep == minStep && minStep == JackaroChessManager._poker7Step) {
            minStep = 0;
        }
        return { minStep: minStep, maxStep: maxStep };
    }

    public getTotalChessMoveStep(): number {
        let totalStep = 0;
        if (this.commonResult) {
            for (let key in this.commonResult) {
                let result = this.commonResult[key];
                if (!result) continue;
                let curMoveStepNum = parseInt(key);
                for (let i = 0; i < result.result.length; i++) {
                    totalStep += curMoveStepNum;
                }
            }
        }
        return totalStep;
    }

    /**
     * 
     */
    getPlayResultItemByChessId(chessID: number, beyondIdx: number): yalla.data.jackaro.PlayResultItemDataInterface {
        if (this.commonResult) {
            for (let key in this.commonResult) {
                let result = this.commonResult[key];
                if (!result) continue;
                for (let i = 0; i < result.result.length; i++) {
                    let item = result.result[i];
                    if (item.fromGrid && item.fromGrid.chess && item.fromGrid.chess.order == chessID && item.fromGrid.chess.idx == beyondIdx) {
                        return item;
                    }
                }
            }
        }
        return null;
    }
    /**
     * 获取当前出牌所有移动步数
     */
    getCurPlayPokerAllMaxMoveStep() {
        let curMaxStep = 0;
        if (this.commonResult) {
            for (var key in this.commonResult) {
                curMaxStep += parseInt(key);
            }
        }
        return Math.min(curMaxStep, JackaroChessManager._poker7Step);
    }
    /**
     * 获取当前出牌最大移动棋子数
     */
    getCurPlayPokerMaxMoveChessNum() {
        let allChessOrder = [];
        if (this.commonResult) {
            for (var key in this.commonResult) {
                let result = this.commonResult[key];
                result.result.forEach(item => {
                    if (item.fromGrid && item.fromGrid.chess && allChessOrder.indexOf(item.fromGrid.chess.order) == -1) {
                        allChessOrder.push(item.fromGrid.chess.order);
                    }
                })
            }
        }
        return allChessOrder.length;
    }


    /**
     * 客服端自行先行移动一个棋子
     */
    handleMoveChessBySelf(result: yalla.data.jackaro.PlayResultItemDataInterface | yalla.data.jackaro.ImpactChessResultInterface, curMoveStep: number) {
        // let selfColor = JackaroGamePlayerManager.instance.getSelfColor();
        // if (result.fromGrid.chess.color == selfColor) {
        // this._curMoveChessID = yalla.Global.Account.idx + "_" + result.fromGrid.chess.order;
        // this._curMoveChessGoalGrid = result.toGrid;
        // }
        this.updateRecentMoveResultData(result, curMoveStep);
        let chess = this.getChessById(result.fromGrid.chess.idx + "_" + result.fromGrid.chess.order);
        if (chess && this.isHasAlreadyTurnMove(chess)) {
            yalla.Debug.logMore("handleMoveChessBySelf", "this.isHasAlreadyTurnMove(chess)");
            return;
        }
        let color = result.toGrid.grid.color || 0;
        let endGridPosition = result.toGrid.grid.index || 0;
        // result.toGrid.grid.index = 7
        // result.toGrid.grid.color = (color + 1) % 2;
        // (result as any).transferGrid = {
        //     grid: {
        //         color: 1,
        //         index: 21,
        //         type: 5
        //     }
        // }
        if (chess) {
            let curMoveType;
            let curWayGrid = chess.wayGrid;
            let goalGridPosition = curWayGrid.gridPosition + curMoveStep;
            //如果目标点是21格子，而且该格子存在，说明是传送点
            if (goalGridPosition == JackaroChess.transferBeginGridPosition && JackaroBoardData.getGridByStation({ area: color, gridPosition: goalGridPosition })) {
                color = (result.fromGrid.grid.color + 1) % 2;
                endGridPosition = JackaroChess.transferEndGridPosition;
                curMoveType = curMoveStep < 0 ?
                    yalla.data.jackaro.JackarooMoveType.MOVE_BACKWARD_TRANSFER :
                    yalla.data.jackaro.JackarooMoveType.MOVE_FORWARD_TRANSFER;
            } else {
                curMoveType = curMoveStep < 0 ?
                    yalla.data.jackaro.JackarooMoveType.MOVE_BACKWARD_WIND :
                    yalla.data.jackaro.JackarooMoveType.MOVE_NORMAL;
            }
            this.resetExpPlayer(chess);
            chess.move({ area: color, gridPosition: endGridPosition }, Math.abs(curMoveStep), curMoveType);
        }
    }
    handleMoveChessPub(msg: any) {
        // {{ YUM: [新增] - 存储移动数据用于检查previewRewardPoker }}
        this.updateRecentMoveResultData(msg);

        this.isPokerSevenOnlyTwoChessMove = false;
        if (!this.checkPokerMovePub(msg)) {
            yalla.data.jackaro.JackaroUserService.instance.nextMsg();
            return;
        }
        TipsViewManager.getInstance().hideTip(TipViewType.No_Chess_Touch);
        this.setImpactedChessTotalData({});

        let curIdx = msg.moveChessPlayer;
        let isMoveChess = msg.moveChess;
        //玩家出牌表现
        let currentPoker = msg.currentPoker;
        let isPoker7 = JackaroCardManger.instance.isPoker7(currentPoker);
        if (currentPoker) {
            if (JackaroGamePlayerManager.instance.isMyActive()) {
                let curPlayPoker = JackaroCardManger.instance.getCurPlayPoker();
                if (curPlayPoker && curPlayPoker != currentPoker) {
                    //如果当前是自己的回合，服务器返回的出牌 和 当前出牌不一致，需要同步
                    // yalla.Debug.logMore("handleMoveChessPub 当前是自己的回合，服务器返回的出牌 和 当前出牌不一致，需要同步", curPlayPoker, currentPoker);
                    yalla.data.jackaro.JackaroUserService.instance.sendSyncGameDataReq();
                    // yalla.data.jackaro.JackaroUserService.instance.nextMsg();
                    return;
                }
            }
            JackaroCardManger.instance.handlePlayPoker(curIdx, currentPoker, isMoveChess, isMoveChess);
            JackaroCardManger.instance.setCurPlayPoker(currentPoker);
        }
        //重置吃子信息
        this.curKilledChesses = [];
        //以下主要是记录吃子数据
        if (isMoveChess) {
            let moveResultDatas: Array<any> = (Array.isArray(msg.moveResultData)) ? msg.moveResultData : [msg];
            let isCanMoveTeamChess = false;
            let moveResultDatasLen = moveResultDatas.length;
            if (moveResultDatasLen == 2) {
                this.isPokerSevenOnlyTwoChessMove = true;
                let teamIdx = JackaroGamePlayerManager.instance.getTeamIdxByOther(JackaroGamePlayerManager.instance.getCurOperatePlayerIdx());
                let moveTeamChessNum: number = 0;
                for (let index = 0; index < moveResultDatasLen; index++) {
                    const moveResultItem = moveResultDatas[index].moveResultData;
                    if (moveResultItem && moveResultItem.toGrid && moveResultItem.toGrid.chess && moveResultItem.toGrid.chess.idx == teamIdx) {
                        moveTeamChessNum++;
                    }
                }
                //两颗棋子可以走的话，有一个是队友的棋子，说明是我在走队友的棋子
                if (moveTeamChessNum == 1) {
                    isCanMoveTeamChess = true;
                }
                //如果可以移动的棋子有两颗，需要先移动自己的棋子，然后再走别人的棋子，因为在自己的终点区域时，先走别人的棋子，会形成路障，造成自己的棋子后面走，在路障下面移动的情况
                //优先移动棋子的索引
                let priorityMoveChessIndex = -1;
                let priorityMoveChessIndexArr: Array<{ idx: number, index: number }> = [];
                for (let index = 0; index < moveResultDatasLen; index++) {
                    const moveResultItem = moveResultDatas[index];
                    const moveResultData = moveResultItem.moveResultData;

                    if (moveResultData && moveResultData.toGrid) {
                        let toGrid = moveResultData.toGrid;
                        let color = toGrid.grid.color || 0;
                        let goalStation = { area: color, gridPosition: toGrid.grid.index || 0 };
                        let grid: LudoGrid = JackaroBoardData.getGridByStation(goalStation);
                        //如果目标点是终点区域，先把移动到终点区域的棋子放在前面
                        if (grid) {
                            if (grid.inEndArea) {
                                priorityMoveChessIndexArr.push({ idx: toGrid.chess.idx, index: index });
                            }
                        }
                    }
                }
                if (priorityMoveChessIndexArr.length > 0) {
                    let curIdx = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx();
                    let curIdxIdx = -1;
                    for (let i = 0; i < priorityMoveChessIndexArr.length; i++) {
                        if (priorityMoveChessIndexArr[i].idx == curIdx) {
                            curIdxIdx = priorityMoveChessIndexArr[i].index;
                            break;
                        }
                    }
                    priorityMoveChessIndex = curIdxIdx;
                }
                if (priorityMoveChessIndex != -1) {
                    //把优先移动的棋子放在前面
                    let priorityMoveChess = moveResultDatas[priorityMoveChessIndex];
                    moveResultDatas.splice(priorityMoveChessIndex, 1);
                    moveResultDatas.unshift(priorityMoveChess);
                }
            }

            let firstChessMoveStepNum = 0;
            let moveDelayTime = 0;
            let isPlayNextMsg = true;
            let curMoveChessFailNum = 0;
            for (let index = 0; index < moveResultDatasLen; index++) {
                const moveResultItem = moveResultDatas[index];
                const moveResultData = moveResultItem.moveResultData;

                if (moveResultData && moveResultData.toGrid) {
                    let killedChess = moveResultItem.killedChess;
                    let expPlayer = moveResultItem.expPlayer;
                    let toGrid = moveResultData.toGrid;
                    let color = toGrid.grid.color || 0;
                    let transferGrid = moveResultData.transferGrid && moveResultData.transferGrid.grid;
                    let isPoker4 = JackaroCardManger.instance.isPoker4(currentPoker);

                    let moveType = yalla.data.jackaro.JackarooMoveType.MOVE_NORMAL;
                    if (isPoker4) {
                        moveType = transferGrid ?
                            yalla.data.jackaro.JackarooMoveType.MOVE_BACKWARD_TRANSFER :
                            yalla.data.jackaro.JackarooMoveType.MOVE_BACKWARD_WIND;
                    } else if (transferGrid) {
                        moveType = yalla.data.jackaro.JackarooMoveType.MOVE_FORWARD_TRANSFER;
                    }
                    let chess = this.getChessById(toGrid.chess.idx + "_" + toGrid.chess.order);
                    if (chess) {
                        chess.killedIdx = null;
                        if (killedChess && killedChess.idx) {
                            this.curKilledChesses.push(killedChess);
                            chess.killedIdx = killedChess.idx;
                        }
                        chess.expPlayer = expPlayer;
                        let goalStation = { area: color, gridPosition: toGrid.grid.index || 0 };
                        moveDelayTime = yalla.data.jackaro.JackaroUserService.instance.delayMoveChessTime;
                        if (index == 1) {
                            moveDelayTime += (JackaroChess.realMoveTime + 50);
                            if (isCanMoveTeamChess && firstChessMoveStepNum > 0) {
                                //如果存在队友走子的话，需要需要自己的棋子走完，再走队友的棋子
                                moveDelayTime = firstChessMoveStepNum * JackaroChess.realMoveTime;
                            }
                        } else if (index == 0) {
                            if (isPoker7) {
                                firstChessMoveStepNum = this.calculateMoveStepNum(goalStation, 0, moveType, chess);
                            }
                        }
                        let checkPlayNextMsg = (isMoveResult: ChessMoveFailReason) => {
                            let isMoveSuc = (isMoveResult == ChessMoveFailReason.SUCCESS);
                            if (isMoveResult != ChessMoveFailReason.SUCCESS && isMoveResult != ChessMoveFailReason.SAME_AS_GOAL_GRID) {
                                curMoveChessFailNum++;
                            }
                            // yalla.Debug.logMore("handleMoveChessPub isMoveSuc:" + isMoveSuc + "isMoveResult:" + isMoveResult + "curMoveChessFailNum:" + curMoveChessFailNum);

                            if (!isMoveSuc && chess && chess.expPlayer && chess.wayGrid.atGrid(chess.goalGrid.gridName)) {
                                var expType: yalla.data.jackaro.ExpType = chess.killedIdx ? 2 : chess.goalGrid.inEndArea ? 1 : 5;
                                if (expType != yalla.data.jackaro.ExpType.ENTER_END_AREA) {
                                    if (!this.isTurnAlreadyPlayExpIdxs(chess.expPlayer, expType)) {
                                        JackaroChessManager.instance.replenishChessEvent(chess, expType);
                                    }
                                }
                            }
                            // yalla.Debug.logMore("curMoveChessFailNum:" + curMoveChessFailNum + ",moveResultDatasLen:" + moveResultDatasLen);
                            //TODO::如果有棋子移动失败，只需要执行一次nextMsg
                            if (curMoveChessFailNum == moveResultDatasLen) {
                                this.checkDoNextMsg(chess);
                                isPlayNextMsg = false;
                            }
                        }
                        if (yalla.Global.isFouce) {
                            Laya.timer.once(moveDelayTime, this, () => {
                                let isMoveResult: ChessMoveFailReason = chess.move(goalStation, 0, moveType);
                                if (isMoveResult == ChessMoveFailReason.SUCCESS) JackaroChessOrbitManager.instance.addIgnoreChessId(chess.idKey);
                                if (isPoker7) {
                                    this.addAlreadyMoveChessStepInfo(chess, chess.stepNum);
                                }
                                checkPlayNextMsg(isMoveResult);
                            });

                        } else {
                            let isMoveResult: ChessMoveFailReason = chess.move(goalStation, 0, moveType);
                            if (isMoveResult == ChessMoveFailReason.SUCCESS) JackaroChessOrbitManager.instance.addIgnoreChessId(chess.idKey);
                            if (isPoker7) {
                                this.addAlreadyMoveChessStepInfo(chess, chess.stepNum);
                            }
                            checkPlayNextMsg(isMoveResult);
                        }
                    }

                    if (moveResultData.quickFinish) {
                        //quick 模式 需要记录完成自己回合时，那些棋子需要返回到基地
                        this.doRecordFastModeMoveChess(moveResultData.toBornData);
                        //存在一种情况就是延迟收到服务器消息，当前回合已经没有棋子正在移动，说明棋子已经移动完毕，则进行快速模式移动
                        if (yalla.Global.isFouce) {
                            Laya.timer.clear(this, this.checkRecoverFastModeChess);
                            //因为棋子延迟移动，检测归位时也延迟棋子延迟移动时间检测，多加1毫秒只是为了错开
                            Laya.timer.once(yalla.data.jackaro.JackaroUserService.instance.delayMoveChessTime + 1, this, this.checkRecoverFastModeChess);
                        } else {
                            this.checkRecoverFastModeChess();
                        }
                    }
                }
            }
            // }
        } else {
            //如果没有走子的话，需要触发棋子移动结束动画事件通知
            if (yalla.Global.isFouce) {
                Laya.timer.once(JackaroCardManger.totalAnimationPlayPoker, this, () => {
                    //TODO::棋子移动结束执行下一条消息
                    yalla.Debug.logMore("handleMoveChessPub 棋子移动结束执行下一条消息 yalla.Global.isFouce");
                    yalla.data.jackaro.JackaroUserService.instance.nextMsg();
                });
            } else {
                yalla.Debug.logMore("handleMoveChessPub 棋子移动结束执行下一条消息");
                yalla.data.jackaro.JackaroUserService.instance.nextMsg();
            }
        }
    }
    /**
     * 检测快速模式，是否有棋子需要恢复归位
     */
    private checkRecoverFastModeChess() {
        if (this.isAllAlreadyTurnMoveEnd()) {
            this.doFastModeMoveChess();
        }
    }
    /**
     * 计算棋子移动步数
     * @param station 
     * @param step 
     * @param type 
     * @param curChess 
     * @returns 
     */
    private calculateMoveStepNum(station: Station, step: number = 0, type: yalla.data.jackaro.JackarooMoveType = yalla.data.jackaro.JackarooMoveType.MOVE_NORMAL, curChess: JackaroChess): number {
        if (!curChess) return 0;
        var grid: LudoGrid = JackaroBoardData.getGridByStation(station);
        if (!grid) return 0;

        if (grid.atGrid(curChess.goalGrid.gridName)) {
            return 0;
        }
        if (grid.atGrid(curChess.wayGrid.gridName)) {
            return 0;
        }

        let lineGirds = JackaroChessManager.instance.getPathsPos(grid, curChess.wayGrid, type != yalla.data.jackaro.JackarooMoveType.MOVE_BACKWARD_WIND, curChess.color);

        if (step == 0) {
            return lineGirds.length;
        } else {
            if (lineGirds[step - 1]) {
                return step;
            } else {
                return 0;
            }
        }
    }
    /**
     * 记录快速模式需要移动的棋子数据
     * @param moveResultData 
     */
    doRecordFastModeMoveChess(toBornData: any) {
        if (toBornData) {
            if (!this.recordMoveToBornDataInterface) {
                this.recordMoveToBornDataInterface = [];
            }
            this.recordMoveToBornDataInterface = this.recordMoveToBornDataInterface.concat(toBornData);
        }
    }
    /**
     * 处理快速模式的棋子移动
     */
    doFastModeMoveChess() {
        if (this.recordMoveToBornDataInterface && this.recordMoveToBornDataInterface.length) {
            for (let index = 0; index < this.recordMoveToBornDataInterface.length; index++) {
                const element = this.recordMoveToBornDataInterface[index];
                const fromGridData = element.fromGrid;
                const toGridData = element.toGrid;
                let fromChess = this.getChessById(fromGridData.chess.idx + "_" + fromGridData.chess.order);
                if (!fromChess || fromChess.wayGrid.inHome) continue;
                let gridPosition = toGridData.grid.index || 0;
                //服务器返回的起始点坐标会大于 104，实际游戏内起始点按照颜色 + 101-104，所以需要转换一下
                gridPosition = JackaroBoardData.convertGridPosition(gridPosition);
                fromChess.isFastModeWinGame = true;
                this.moveHome(fromChess, yalla.data.jackaro.JackarooMoveType.MOVE_HOME);
            }
            this.resetRecordFastModeMoveChess();
        }
    }
    private resetRecordFastModeMoveChess() {
        if (this.recordMoveToBornDataInterface) {
            this.recordMoveToBornDataInterface = [];
        }
    }
    // 撞击移动后的推送  OVE_PUB = 115,//7步移动后的推送
    handleImpactMovePub(msg: any) {
        // {{ YUM: [新增] - 存储移动数据用于检查previewRewardPoker }}
        this.updateRecentMoveResultData(msg);

        if (msg.impactMoveResult && msg.impactedChess) {
            let moveChessInfo = msg.impactMoveResult.toGrid.chess.idx + "_" + msg.impactMoveResult.toGrid.chess.order;
            if (!this.impactedChessTotalData || !this.impactedChessTotalData[moveChessInfo]) {
                let impactedChessTotalData: Record<string, Array<yalla.data.jackaro.ImpactedChessDataInterface>> = {};
                impactedChessTotalData[moveChessInfo] = msg.impactedChess;
                this.setImpactedChessTotalData(impactedChessTotalData);
            }
        }
        let expPlayer = msg.expPlayer;
        this.curPlayPokerKMoveChessPlayer = msg.moveChessPlayer;
        let currentPoker = msg.currentPoker;
        let isMoveChess = msg.moveChess;
        //玩家出牌表现
        if (currentPoker) {
            if (JackaroGamePlayerManager.instance.isMyActive()) {
                let curPlayPoker = JackaroCardManger.instance.getCurPlayPoker();
                if (curPlayPoker && curPlayPoker != currentPoker) {
                    //如果当前是自己的回合，服务器返回的出牌 和 当前出牌不一致，需要同步
                    yalla.data.jackaro.JackaroUserService.instance.sendSyncGameDataReq();
                    return;
                }
            }
            JackaroCardManger.instance.setCurPlayPoker(currentPoker);
            if (isMoveChess) {
                JackaroCardManger.instance.handlePlayPoker(this.curPlayPokerKMoveChessPlayer, currentPoker, isMoveChess);
            } else {
                JackaroCardManger.instance.handlePlayPoker(this.curPlayPokerKMoveChessPlayer, currentPoker, isMoveChess, false);
            }
        }
        if (isMoveChess) {
            if ((msg.impactMoveResult && msg.impactMoveResult.toGrid)) {
                let chess = this.getChessById(msg.impactMoveResult.toGrid.chess.idx + "_" + msg.impactMoveResult.toGrid.chess.order);
                let color = msg.impactMoveResult.toGrid.grid.color || 0;

                if (chess) {
                    chess.expPlayer = expPlayer;
                    let moveType = yalla.data.jackaro.JackarooMoveType.MOVE_NORMAL;
                    if (msg.impactMoveResult.transferGrid && msg.impactMoveResult.transferGrid.grid) {
                        moveType = yalla.data.jackaro.JackarooMoveType.MOVE_FORWARD_TRANSFER;
                    }
                    let isMove: ChessMoveFailReason = chess.move({ area: color, gridPosition: msg.impactMoveResult.toGrid.grid.index || 0 }, 0, moveType);
                    // yalla.Debug.logMore("handleImpactMovePub is  move result: " + isMove + " -> " + (msg.impactMoveResult.toGrid.chess.idx + "_" + msg.impactMoveResult.toGrid.chess.order));
                    /**移动失败，也需要执行下一条消息 */
                    if (isMove == ChessMoveFailReason.SUCCESS) JackaroChessOrbitManager.instance.addIgnoreChessId(chess.idKey);
                    if (isMove != ChessMoveFailReason.SUCCESS && isMove != ChessMoveFailReason.SAME_AS_GOAL_GRID) {
                        this.checkDoNextMsg(chess);
                    }
                } else {
                    yalla.Debug.logMore("handleImpactMovePub is   not chess");
                }
                if (msg.impactMoveResult && msg.impactMoveResult.quickFinish) {
                    this.doRecordFastModeMoveChess(msg.impactMoveResult.toBornData);
                }
            }
        } else {
            yalla.Debug.logMore("handleImpactMovePub is   null");
            //如果没有走子的话，需要触发棋子移动结束动画事件通知
            if (yalla.Global.isFouce) {
                Laya.timer.once(JackaroCardManger.totalAnimationPlayPoker, this, () => {
                    //TODO::棋子移动结束执行下一条消息
                    yalla.data.jackaro.JackaroUserService.instance.nextMsg();
                });
            } else {
                yalla.data.jackaro.JackaroUserService.instance.nextMsg();
            }
        }
    }
    setCurPlayPokerKMoveChessPlayer(idx: number) {
        this.curPlayPokerKMoveChessPlayer = idx;
    }
    setImpactedChessTotalData(impactedChessTotalData: Record<string, Array<yalla.data.jackaro.ImpactedChessDataInterface>>) {
        this.impactedChessTotalData = impactedChessTotalData;
    }
    private checkPokerMovePub(msg: any) {
        if (!JackaroGamePlayerManager.instance.isMyActive()) return true;
        if (this.checkMoveChessData.length > 0) {
            let moveResultData = msg.moveResultData;
            //开始校验数据，出牌7 一般会返回两段数据，代表两个走子，也有一次走子7步，其实只有一个走子
            if (moveResultData && moveResultData.length > 0) {
                for (let index = 0; index < moveResultData.length; index++) {
                    const element = moveResultData[index];
                    if (!element || !element.moveResultData) continue;
                    let moveResult = element.moveResultData;
                    let toGrid = moveResult.toGrid;

                    for (let i = this.checkMoveChessData.length - 1; i >= 0; i--) {
                        let checkData = this.checkMoveChessData[i];
                        if (checkData.grid.index == toGrid.grid.index && checkData.chess.idx == toGrid.chess.idx && checkData.chess.order == toGrid.chess.order && checkData.chess.color == toGrid.chess.color) {
                            //校验数据一致，则删除
                            this.checkMoveChessData.splice(i, 1);
                            break;
                        }
                    }

                }
                if (this.checkMoveChessData.length == 0) {
                    //所有校验数据一致
                    return true;
                } else {
                    // yalla.Debug.logMore("checkPokerMovePub 校验数据不一致1", this.checkMoveChessData);
                    //校验数据不一致，则请求同步接口
                    yalla.data.jackaro.JackaroUserService.instance.sendSyncGameDataReq();
                    return false;
                }
            } else {
                if (moveResultData) {
                    let toGrid = moveResultData.toGrid;
                    for (let i = this.checkMoveChessData.length - 1; i >= 0; i--) {
                        let checkData = this.checkMoveChessData[i];

                        if (checkData.grid.index == toGrid.grid.index && checkData.chess.idx == toGrid.chess.idx && checkData.chess.order == toGrid.chess.order && checkData.chess.color == toGrid.chess.color) {
                            //校验数据一致，则删除
                            this.checkMoveChessData.splice(i, 1);
                            break;
                        }
                    }
                }

                if (this.checkMoveChessData.length == 0) {
                    //所有校验数据一致
                    return true;
                } else {
                    // yalla.Debug.logMore("checkPokerMovePub 校验数据不一致2", this.checkMoveChessData);
                    //校验数据不一致，则请求同步接口
                    yalla.data.jackaro.JackaroUserService.instance.sendSyncGameDataReq();
                    return false;
                }
            }
        }
        return true;
    }
    handleExchangeChess(msg: any) {
        TipsViewManager.getInstance().hideTip(TipViewType.EXCHANGE_CHESS);
        this.isExchangeChess = false;
        if (msg.moveChessPlayer) {
            let currentPoker = msg.currentPoker;
            if (currentPoker) {
                if (JackaroGamePlayerManager.instance.isMyActive()) {
                    let curPlayPoker = JackaroCardManger.instance.getCurPlayPoker();
                    if (curPlayPoker && curPlayPoker != currentPoker) {
                        //如果当前是自己的回合，服务器返回的出牌 和 当前出牌不一致，需要同步
                        yalla.data.jackaro.JackaroUserService.instance.sendSyncGameDataReq();
                        return;
                    }
                }
                JackaroCardManger.instance.setCurPlayPoker(currentPoker);
                JackaroCardManger.instance.handlePlayPoker(msg.moveChessPlayer, currentPoker, false, true);
            }
        }

        if (msg.selfMoveResultData) {
            let moveChess = msg.moveChess;
            if (!moveChess) {
                //是否走子,false:没有走子,只播出牌动画,true:有走子,先播出牌动画,再播走子动画
                return;
            }
            let fromGrid = msg.selfMoveResultData.fromGrid;
            if (!fromGrid) {
                return;
            }
            let copyRecordExchangeChess = JSON.parse(JSON.stringify(this.recordExchangeChess));
            let result = this.checkPokerExchangePub(msg.selfMoveResultData, copyRecordExchangeChess);
            if (!result) {
                //校验数据不一致，则请求同步接口
                yalla.data.jackaro.JackaroUserService.instance.sendSyncGameDataReq();
                yalla.data.jackaro.JackaroUserService.instance.nextMsg();
                return;
            }

            let fromChess = this.getChessById(fromGrid.chess.idx + "_" + fromGrid.chess.order);
            let fromStation = { area: fromGrid.grid.color || 0, gridPosition: fromGrid.grid.index || 0 };

            let toGrid = msg.selfMoveResultData.toGrid;

            let toChess = this.getChessById(toGrid.chess.idx + "_" + toGrid.chess.order);
            let toStation = { area: toGrid.grid.color || 0, gridPosition: toGrid.grid.index || 0 };
            if (!fromChess) {
                return;
            }
            fromChess.moveType = yalla.data.jackaro.JackarooMoveType.MOVE_EXCHANGE;
            //自己的棋子
            if (!toGrid.chess) {
                return;
            }
            if (!toChess) {
                return;
            }
            toChess.moveType = yalla.data.jackaro.JackarooMoveType.MOVE_EXCHANGE;

            let isFromChessAtGoalPos = fromChess.isAtGoalPos(fromStation);
            let isToChessAtGoalPos = toChess.isAtGoalPos(toStation);
            if (isFromChessAtGoalPos || isToChessAtGoalPos) {
                yalla.data.jackaro.JackaroUserService.instance.nextMsg();
                return;
            }
            let chessMoveTime = fromChess.getExchangeMoveTime(fromStation);
            if (yalla.Global.isFouce) {
                let doExchange = () => {
                    yalla.Sound.playSound('Stone_Swap');
                    this.hideChessExchangeSpineAni();
                    let exchangeResult1 = fromChess.moveExchangeChess(fromStation, chessMoveTime);
                    let exchangeResult2 = toChess.moveExchangeChess(toStation, chessMoveTime, true);
                    if (!exchangeResult1 || !exchangeResult2) {
                        yalla.data.jackaro.JackaroUserService.instance.nextMsg();
                    }
                };

                if (!JackaroGamePlayerManager.instance.isMyActive()) {
                    this.playExchangeStartSpinAni(fromChess);
                    this.playExchangeStartSpinAni(toChess);
                    Laya.timer.once(200, this, () => {
                        doExchange();
                    });
                } else {
                    doExchange();
                }
            } else {
                this.hideChessExchangeSpineAni();
                fromChess.moveExchangeChess(fromStation, chessMoveTime);
                toChess.moveExchangeChess(toStation, chessMoveTime, true);
            }
        }
        this.recordExchangeChess = [];
    }
    /**
     * 校验交换棋子 是否是自己选择的棋子
     */
    checkPokerExchangePub(selfMoveResultData: any, recordExchangeChess: Array<{ idx: number, order: number, number: number }>) {
        if (!selfMoveResultData) return false;
        if (recordExchangeChess.length == 0) return true;
        let fromGrid = selfMoveResultData.fromGrid;
        let toGrid = selfMoveResultData.toGrid;
        let fromChessID = fromGrid.chess.idx + "_" + fromGrid.chess.order;
        let toChessID = toGrid.chess.idx + "_" + toGrid.chess.order;

        for (let i = recordExchangeChess.length - 1; i >= 0; i--) {
            let record = recordExchangeChess[i];
            let chessInfo = record.idx + "_" + record.order;
            if (chessInfo == fromChessID || chessInfo == toChessID) {
                recordExchangeChess.splice(i, 1);
            }
        }
        return recordExchangeChess.length == 0;
    }
    /**
     * 播放交换动画
     * @param chess 
     * @returns 
     */
    private playExchangeEffect() {
        if (this.recordExchangeChess && this.recordExchangeChess[0] && this.recordExchangeChess[1]) {
            let chess = this.getChessById(this.recordExchangeChess[0].idx + "_" + this.recordExchangeChess[0].order);
            let otherChess = this.getChessById(this.recordExchangeChess[1].idx + "_" + this.recordExchangeChess[1].order);
            if (chess && otherChess) {
                let chessMoveTime = chess.getExchangeMoveTime(otherChess.wayGrid.station);
                // let otherChessMoveTime = otherChess.getExchangeMoveTime(chess.wayGrid.station);
                yalla.Sound.playSound('Stone_Swap');
                this.hideChessExchangeSpineAni();
                chess.moveExchangeChess(otherChess.wayGrid.station, chessMoveTime);
                otherChess.moveExchangeChess(chess.wayGrid.station, chessMoveTime, true);
            }
        }
    }
    private playExchangeStartSpinAni(chess: JackaroChess) {
        if (!yalla.Global.isFouce) return;
        if (!this.mapLayer || !this.mapLayer.parent) return;
        let chessExchangeSpineAni = this.getChessExchangeStartSpineAni();
        //提升层级
        this.mapLayer.parent.parent.getChildByName("shuffleSpineLayer").addChild(chessExchangeSpineAni);
        let gPos = chess.getGlobalPos();
        let { width, height } = chess;

        chessExchangeSpineAni.visible = true;
        chessExchangeSpineAni.pos(gPos.x + width / 2 - 5, gPos.y + height / 2 - 5);//-4原因是spine中心点不够居中导致
        chessExchangeSpineAni.blendMode = 'lighter';
        chessExchangeSpineAni.alpha = 0.5;
        chessExchangeSpineAni.play("gaoliang", true);
    }
    private playExchangeEndSpinAni() {
        if (!yalla.Global.isFouce) {
            this.removeChessExchangeSpineAni();
            return;
        }
        if (this.isPlayExchangeEndSpinAni) return;

        for (let index = 0; index < this.chessExchagneSpineAni.length; index++) {
            const element = this.chessExchagneSpineAni[index];
            element.visible = false;
            let chessExchangeEndSpineAni = this.getChessExchangeEndSpineAni();
            //提升层级
            this.mapLayer.parent.parent.getChildByName("shuffleSpineLayer").addChild(chessExchangeEndSpineAni);
            chessExchangeEndSpineAni.visible = true;
            chessExchangeEndSpineAni.alpha = 0.7;
            chessExchangeEndSpineAni.pos(element.x, element.y);//-4原因是spine中心点不够居中导致
            chessExchangeEndSpineAni.play("idle", false);
            if (index == this.chessExchagneSpineAni.length - 1) {
                Laya.timer.once(1000, this, () => {
                    this.removeChessExchangeSpineAni();
                });
            }
        }
        this.isPlayExchangeEndSpinAni = true;
    }
    handleExchangeChessPub(msg: any) {
        this.isExchangeChess = false;
        this.isPlayExchangeEndSpinAni = false;
        this.handleExchangeChess(msg);
    }
    handleMoveOneStep(moveChess: JackaroChess, grid: LudoGrid, isMoveEnd: boolean = false) {
        if (!grid) return;
        //处理出牌K 移动13步 吃子
        if (this.impactedChessTotalData && moveChess && this.impactedChessTotalData[moveChess.idKey]) {
            let curImpactedChessTotalData = this.impactedChessTotalData[moveChess.idKey];
            for (let index = curImpactedChessTotalData.length - 1; index >= 0; index--) {
                const element = curImpactedChessTotalData[index];
                if (!element || !element.from || !element.from.chess) continue;
                let chess = this.getChessById(element.from.chess.idx + "_" + element.from.chess.order);
                if (chess && chess.wayGrid.atGrid(grid.gridName)) {
                    this.curTurnAlreadyKillChess.push(chess.beyondIdx + "_" + chess.id);
                    //被吃的不是自己的棋子
                    if (chess.beyondIdx !== moveChess.beyondIdx) {
                        this.addExpPlayer(moveChess, chess);
                        this.onChessEvent(moveChess, chess, yalla.data.jackaro.ExpType.KILL_CHESS);
                    } else if (chess.beyondIdx == moveChess.beyondIdx) {
                        this.playEatedChessEmoji(moveChess, chess);
                        //1.4.4 吃自己的最后一颗棋子，如果操作者是自己不播放
                        if (curImpactedChessTotalData.length == 1) {
                            let operateIdx = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx();
                            let curOperatePlayerTeamIdx = JackaroGamePlayerManager.instance.getTeamIdxByOther(operateIdx);
                            if (moveChess.beyondIdx == operateIdx || moveChess.beyondIdx == curOperatePlayerTeamIdx) {
                                moveChess.playEmoji = false;
                            }
                        }
                        // {{ YUM: [修改] - 调用独立的击杀抽牌奖励处理方法 }}
                        this.handleKillChessDrawReward(yalla.data.jackaro.ExpType.KILL_CHESS);
                    }
                    this.moveHomeByPlayPokerK(chess, element.bornIndex % 100, element.from.chess.color, yalla.data.jackaro.JackarooMoveType.MOVE_BE_HIT);
                    curImpactedChessTotalData.splice(index, 1);
                    break;
                }
            }
        }
        isMoveEnd && this.resetExpPlayer(moveChess);
    }
    /**
     * 发生了吃子 进终点 起飞
     */
    onChessEvent(moveChess: JackaroChess, killedChess: JackaroChess, expType: yalla.data.jackaro.ExpType, isMoveEnd: boolean = true) {
        var expPlayer = moveChess.expPlayer;
        if (expPlayer && this.isCanAddExp(expPlayer) && moveChess.isAddTrustExp) {
            // if (!moveChess.killedIdx) moveChess.expPlayer = null;
            let myIdx = yalla.Global.Account.idx;
            let curOperatePlayerIdx = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx();
            let curOperatePlayerTeamIdx = JackaroGamePlayerManager.instance.getTeamIdxByOther(curOperatePlayerIdx);
            let isCanAddExp: boolean = true;
            if (expType == yalla.data.jackaro.ExpType.KILL_CHESS && this.isNewExpPlayLogic() && killedChess && moveChess && ((curOperatePlayerIdx == myIdx || curOperatePlayerTeamIdx == curOperatePlayerTeamIdx) && (killedChess.beyondIdx == myIdx || killedChess.beyondIdx == curOperatePlayerTeamIdx))) {
                //TODO 1.4.4 互相为队友，不加经验
                isCanAddExp = false;
            }
            if (isCanAddExp) {
                this.showStar(moveChess.localToGlobal(new Laya.Point(), true), expType);
                this.curTurnAlreadyPlayExpIdxs.push({ expPlayer: expPlayer, expType: expType });
            }
        }
        yalla.Debug.yum('[抽牌奖励] 棋子事件 - 判定是否触发抽牌奖励启动 killedChess', killedChess);
        if (killedChess) {
            this.checkCanPlayEatChessEff(moveChess, killedChess);
            yalla.Sound.playSound("1kills");
            if (yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode()) {
                // {{ YUM: [修改] - 调用独立的击杀抽牌奖励处理方法 }}
                this.handleKillChessDrawReward(expType);
            }
        }
        isMoveEnd && this.resetExpPlayer(moveChess);
    }
    /**
     * 处理击杀棋子的抽牌奖励逻辑
     * {{ YUM: [新增] - 将击杀抽牌奖励逻辑提取为独立方法 }}
     */
    private handleKillChessDrawReward(expType: yalla.data.jackaro.ExpType): void {
        yalla.Debug.yum('[抽牌奖励] 棋子击杀 - 判定是否触发抽牌奖励启动');
        if (expType === yalla.data.jackaro.ExpType.KILL_CHESS
            && yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode()) {

            // {{ YUM: [新增] - 使用封装的击杀抽牌奖励方法 }}
            const currentRound = JackaroGamePlayerManager.instance.getOutRound();
            const playerId = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx();
            const currentPoker = JackaroCardManger.instance.getCurPlayPoker();
            JackaroCardReplenishManager.instance.tryTriggerDrawRewardOnChessKill(playerId, currentRound, currentPoker);
        }
    }

    /**
     * 同颜色吃子，只播放被吃表情
     */
    private playEatedChessEmoji(moveChess: JackaroChess, killedChess: JackaroChess): void {
        if (moveChess && killedChess && moveChess.beyondIdx == killedChess.beyondIdx) {
            killedChess.playEmoji = true;
        }
    }
    /**
     * 是否播放旧经验播放逻辑
     *  
     */
    public isNewExpPlayLogic(): boolean {
        let room = yalla.data.jackaro.JackaroUserService.instance.room;
        if (room && !room.isPlayOldExp()) {
            return true;
        }
        return false;
    }
    public isTurnAlreadyPlayExpIdxs(expPlayer: number, expType: number) {
        for (let index = 0; index < this.curTurnAlreadyPlayExpIdxs.length; index++) {
            const element = this.curTurnAlreadyPlayExpIdxs[index];
            if (element.expPlayer == expPlayer && element.expType == expType) {
                return true;
            }

        }
        return false;
    }
    /**
     * 记录已经播放经验的玩家，如果再次轮到自己或者队友，清空
     */
    public resetTurnAlreadyPlayExpIdxs() {
        this.curTurnAlreadyPlayExpIdxs = [];
    }
    resetExpPlayer(moveChess: JackaroChess) {
        if (moveChess) {
            moveChess.killedIdx = null;
            moveChess.expPlayer = null;
        }
    }
    addExpPlayer(moveChess: JackaroChess, killedChess: JackaroChess) {//当棋子走1步吃子的时候出现 没有加经验的问题
        if (moveChess && killedChess) {
            var myIdx = yalla.Global.Account.idx;
            if (!moveChess.expPlayer &&
                moveChess.beyondIdx == myIdx &&//自己的棋子
                killedChess.beyondIdx !== myIdx //被吃的不是自己的棋子
            ) {
                moveChess.expPlayer = myIdx;
            }
        }
    }

    //补充当棋子完成移动后收到服务端消息的情况 播放经验动画
    replenishChessEvent(moveChess: JackaroChess, expType: yalla.data.jackaro.ExpType) {
        this.onChessEvent(moveChess, null, expType)
    }
    /**
     * 是否可以添加经验
     * @param idx 
     * @returns 
     */
    public isCanAddExp(expPlayer: number): boolean {
        return expPlayer == yalla.Global.Account.idx && this.selfAddExp;
    }

    public isTrustNotExp() {//托管的时候是否不加经验
        var room = yalla.data.jackaro.JackaroUserService.instance.room;
        return room && room.isTrust && room.ignoreTrustExperience;
    }
    /**
     * 进入后台时扑克牌K 连续吃子,此时有些棋子会没有被吃回去，直接把所有的子都移动到基地
     */
    private checkPlayPokerKKill(moveChess: JackaroChess, moveType: yalla.data.jackaro.JackarooMoveType) {
        //如果该棋子移动结束了，不是被吃回去的，说明该回合棋子移动已经结束，针对复杂玩法出牌K
        if (moveType != yalla.data.jackaro.JackarooMoveType.MOVE_BE_HIT) {
            this.handleImpactedChesToHome(moveChess);
            this.impactedChessTotalData = {};
        }
    }
    handleImpactedChesToHome(moveChess) {
        if (!this.impactedChessTotalData) return;
        if (this.impactedChessTotalData && moveChess && this.impactedChessTotalData[moveChess.idKey]) {
            let curImpactedChessTotalData = this.impactedChessTotalData[moveChess.idKey];
            if (curImpactedChessTotalData && curImpactedChessTotalData.length > 0) {
                for (let index = curImpactedChessTotalData.length - 1; index >= 0; index--) {
                    const element = curImpactedChessTotalData[index];
                    if (!element || !element.from || !element.from.chess) continue;
                    let chess = this.getChessById(element.from.chess.idx + "_" + element.from.chess.order);
                    if (chess) {
                        this.moveHomeByPlayPokerK(chess, element.bornIndex % 100, element.from.chess.color, yalla.data.jackaro.JackarooMoveType.MOVE_BE_HIT);
                        curImpactedChessTotalData.splice(index, 1);
                    }
                }
                curImpactedChessTotalData = [];
            }
        }
    }
    /**
     * 正在移动的棋子如果切换到后台后，此时移动end 要延迟才会调用，此时已经收到changePlayer事件，导致等移动结束时撞击棋子数据被重置
     * 实际棋子并没有被撞击移动回去
     */
    handleChangePlayerChessMove() {
        if (yalla.Global.isFouce) return;
        this.doFastModeMoveChess();
        if (this.curTurnAlreadyMoveChessInfoes) {
            for (let index = this.curTurnAlreadyMoveChessInfoes.length - 1; index >= 0; index--) {
                const element = this.curTurnAlreadyMoveChessInfoes[index];
                let chess = this.getChessById(element.chessInfo);
                if (chess) {
                    if (chess.moveType != yalla.data.jackaro.JackarooMoveType.MOVE_BE_HIT && chess.moveType != yalla.data.jackaro.JackarooMoveType.MOVE_HOME) {
                        this.handleImpactedChesToHome(chess);
                    }
                    chess.clearTimeLine();
                }
            }
        }
    }

    handleMoveChessBegin(chess: JackaroChess, moveType: yalla.data.jackaro.JackarooMoveType) {
        if (TipsViewManager.getInstance().isShow(TipViewType.CHOOSE_CHESS_OPERATE)) {
            this.updateChessChoose();
        } else {
            JkChooseChessManager.ins.removeAll();
        }
        // }
        this.clearChooseTag();
        if (JackaroGamePlayerManager.instance.isMyActive()) {
            //移动中的棋子预览效果移除
            JackaroCardManger.instance.refreshChessOrbit();
        } else {
            //被吃回去的棋子 不更新预览
            if (moveType != yalla.data.jackaro.JackarooMoveType.MOVE_BE_HIT) {
                // //删除普通玩法旧路径
                JackaroCardManger.instance.refreshChessOrbit(chess.idKey);
            }
        }
        yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_Move_Chess_Begin, chess);
    }
    public clearChooseTag() {
        for (let i = 0; i < this.curChessAreaAllChess.length; i++) {
            let jackaroChess: JackaroChess = this.curChessAreaAllChess[i];
            if (jackaroChess) {
                jackaroChess.setChessTag("");
            }
        }
        this.curChessAreaAllChess = [];

        if (this.jackaroChooseChessTag) {
            this.jackaroChooseChessTag.hide();
        }
    }
    handleMoveChessEnd(chess: JackaroChess, moveType: yalla.data.jackaro.JackarooMoveType) {
        if (!chess) {
            return;
        }
        JackaroChessOrbitManager.instance.removeIgnoreChessId(chess.idKey);//移动结束先清理排除棋子
        JackaroCardManger.instance.refreshChessOrbit();
        if (moveType == yalla.data.jackaro.JackarooMoveType.MOVE_EXCHANGE) {
            this.playExchangeEndSpinAni();
            yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_Chess_Move_End, chess);
            yalla.data.jackaro.JackaroUserService.instance.nextMsg();
            return;
        }
        var beyondIdx = chess.beyondIdx;
        //起飞加经验

        if (moveType == yalla.data.jackaro.JackarooMoveType.MOVE_TAKEOFF) {
            var myIdx = yalla.Global.Account.idx
            if (!chess.expPlayer &&
                JackaroGamePlayerManager.instance.getCurOperatePlayerIdx() == myIdx &&
                chess.beyondIdx == myIdx
            ) {//自己出牌7进终点 因为消息不是服务端给的导致缺失expPlayer
                chess.expPlayer = myIdx;
            }
            this.onChessEvent(chess, null, yalla.data.jackaro.ExpType.FLY_CHESS, false);

            if (yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode()) {
                // {{ YUM: [新增] - 使用封装的棋子起飞抽牌奖励方法 }}
                const currentRound = JackaroGamePlayerManager.instance.getOutRound();
                const playerId = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx();
                JackaroCardReplenishManager.instance.tryTriggerDrawRewardOnChessTakeOff(playerId, currentRound);
            }
        }


        //检查棋子移动结束是否需要吃子
        let callBackKey = `${beyondIdx}_${chess.id}`;
        if (this.killChessCallBackInfo[callBackKey]) {
            this.killChessCallBackInfo[callBackKey](chess);
            this.killChessCallBackInfo[callBackKey] = null;
        }
        var playEmoji_moveEnd = chess.playEmoji;
        //TODO:1.4.3.1 移动结束，且吃子有经验动画，延迟1s执行复位逻辑
        this.handleMoveOneStep(chess, chess.wayGrid, true);
        yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_Chess_Move_End, chess);
        //此逻辑为了检测因切后台导致部分棋子因为出牌K 没有被吃回去时，再次检测把这些棋子移动回去
        this.checkPlayPokerKKill(chess, moveType);
        if (playEmoji_moveEnd && yalla.Global.isFouce) {
            Laya.timer.once(400, this, (chess) => {
                this.checkAlreadyTurnMoveEnd(chess);
            }, [chess]);
        } else {
            this.checkAlreadyTurnMoveEnd(chess);
        }
    }
    public resetKillChessCallBack() {
        this.killChessCallBackInfo = {};
    }
    /**
     * 
     * 设置棋子移动结束时 ，检查是否吃子 
     * */
    public setKillChessCallBack(moveChess: JackaroChess) {
        if (moveChess) {
            let callBackKey = `${moveChess.beyondIdx}_${moveChess.id}`;
            this.killChessCallBackInfo[callBackKey] = (chess: JackaroChess) => {
                if (this.curKilledChesses && this.curKilledChesses.length) {
                    for (let index = this.curKilledChesses.length - 1; index >= 0; index--) {
                        const element = this.curKilledChesses[index];
                        if (element.idx == moveChess.beyondIdx && element.order == moveChess.id) {
                            continue;
                        }
                        let killedChess = this.getChessById(element.idx + "_" + element.order);
                        if (!killedChess) continue;
                        if (killedChess.wayGrid.atGrid(chess.wayGrid.gridName)) {
                            this.checkPlayEatChessEff(killedChess);
                            this.onChessEvent(moveChess, killedChess, yalla.data.jackaro.ExpType.KILL_CHESS);
                            this.curKilledChesses.splice(index, 1);
                            this.curTurnAlreadyKillChess.push(killedChess.beyondIdx + "_" + killedChess.id);
                            this.moveHome(killedChess, yalla.data.jackaro.JackarooMoveType.MOVE_BE_HIT);
                            break;
                        }
                    }
                } else {
                    if (this.commonResult) {
                        let isHasKilled: boolean = false;
                        for (var key in this.commonResult) {
                            let result = this.commonResult[key];
                            // 使用 filter 创建新数组，保留不需要删除的项
                            result.result = result.result.filter(item => {
                                if (item.fromGrid &&
                                    item.fromGrid.chess &&
                                    item.fromGrid.chess.idx == chess.beyondIdx &&
                                    item.fromGrid.chess.order == chess.id &&
                                    item.kill) {

                                    for (let key in this._pool) {
                                        let ch = this._pool[key];
                                        if (!ch.getIsMoving() && ch.wayGrid.atGrid(chess.wayGrid.gridName) && (ch.beyondIdx != chess.beyondIdx || (ch.beyondIdx == chess.beyondIdx && ch.id != chess.id))) {
                                            this.curTurnAlreadyKillChess.push(ch.beyondIdx + "_" + ch.id);
                                            this.addExpPlayer(chess, ch);
                                            this.onChessEvent(chess, ch, yalla.data.jackaro.ExpType.KILL_CHESS);
                                            this.checkPlayEatChessEff(ch);
                                            this.moveHome(ch, yalla.data.jackaro.JackarooMoveType.MOVE_BE_HIT);
                                            isHasKilled = true;
                                            return false; // 需要删除的项
                                        }
                                    }
                                }
                                return true; // 保留的项
                            });
                        }
                        //没有服务器返回的吃子信息时，此时要判断是否符合出牌7 导致的吃子，因为出牌7需要两步走操作完成
                        let curPlayPoker = JackaroCardManger.instance.getCurPlayPoker();
                        let isPlayPoker7 = JackaroCardManger.instance.isPoker7(curPlayPoker);
                        if (!isHasKilled && isPlayPoker7) {
                            for (let key in this._pool) {
                                let ch = this._pool[key];
                                if (!(ch.wayGrid.inHome || ch.goalGrid.inHome) && ch.wayGrid.atGrid(chess.wayGrid.gridName) && ch.beyondIdx != chess.beyondIdx) {
                                    this.addExpPlayer(chess, ch);
                                    this.onChessEvent(chess, ch, yalla.data.jackaro.ExpType.KILL_CHESS);
                                    this.checkPlayEatChessEff(ch);
                                    this.moveHome(ch, yalla.data.jackaro.JackarooMoveType.MOVE_BE_HIT);
                                    this.curTurnAlreadyKillChess.push(ch.beyondIdx + "_" + ch.id);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /***
     *    自己回合未完成                  自己回合已完成
     *   操作自己吃队友    有表情        操作队友吃对家   有表情
     *   操作自己吃对家    有表情        操作对家吃对家   有表情
     *   操作队友吃对家    有表情
     *   操作队友吃队友    有表情
     *   操作对家吃队友    有表情
     *   操作对家吃对家    有表情
     * 
     */
    private checkCanPlayEatChessEff(moveChess: JackaroChess, killedChess: JackaroChess) {
        //当前操作是自己的时候适用
        //自己已完成游戏+队友棋子被吃 --->不播放表情；
        //自己未完成游戏+自己棋子被吃--->不播放表情;
        let myIdx = yalla.Global.Account.idx,
            operateIdx = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx();
        let playEmoji = true;
        if (operateIdx == myIdx) {
            let killedIdx = killedChess.beyondIdx,
                myGameWin = this.isSelfChessAllInEndArea(myIdx);
            playEmoji = myGameWin ?
                !(killedIdx == JackaroGamePlayerManager.instance.getTeamIdxByOther(myIdx)) :
                !(killedIdx == myIdx)
        }
        //1.4.3.1 异色播放吃子表情
        //是否出牌K 最后一颗吃子是自己的棋盘
        let pokerKEatSelfEmoji: boolean = false;
        if (this.impactedChessTotalData && moveChess && this.impactedChessTotalData[moveChess.idKey]) {
            let impactedChessData = this.impactedChessTotalData[moveChess.idKey];
            const element = impactedChessData[impactedChessData.length - 1];
            if (element && element.from && element.from.chess) {
                if (element.from.chess.idx == moveChess.beyondIdx) {
                    pokerKEatSelfEmoji = true;
                }
            }
        }
        //1.4.5 扑克牌K 路径上有击杀，且可以移动进终点时，最后的得意表情取消播放
        if (moveChess.goalGrid && moveChess.goalGrid.inEndArea) {
            playEmoji = false;
        }

        if (this.isNewExpPlayLogic()) {
            playEmoji = (moveChess.beyondIdx != killedChess.beyondIdx) && playEmoji;
            moveChess.playEmoji = pokerKEatSelfEmoji ? false : playEmoji;
            //1.4.3.1 同色播放被吃子表情
            killedChess.playEmoji = true;
        } else {
            moveChess.playEmoji = pokerKEatSelfEmoji ? false : playEmoji;
            killedChess.playEmoji = playEmoji;
        }

    }
    /**
     * 是否完美进入终点
     */
    public isPerfectEnterEndPoint(chess: JackaroChess): boolean {
        if (chess && chess.moveStartGrid && !chess.moveStartGrid.inEndArea && chess.wayGrid.inEndArea) {
            let curGrid = chess.wayGrid;
            if (curGrid && curGrid.station.gridPosition == -4) return true;
            let color = JackaroGamePlayerManager.instance.getColorByIdx(yalla.Global.Account.idx);
            let chessArr = this.getChessesByColor(color);
            for (let index = 0; index < 4; index++) {
                let nextGrid = JackaroBoardData.getGridByName(curGrid.nextName);
                if (nextGrid) {
                    let hasOne: boolean = false;
                    for (let index = 0; index < chessArr.length; index++) {
                        const ch = chessArr[index];
                        if (ch && ch.wayGrid.atGrid(nextGrid.gridName)) {
                            hasOne = true;
                        }
                    }
                    if (!hasOne) {
                        return false;
                    }
                    curGrid = nextGrid;
                }
            }
            return true;
        }
        return false;
    }
    public isPerfectEnterEndPoint2(beyondChess: JackaroChess, chessGrid: LudoGrid): boolean {
        if (!beyondChess) return false;

        if (chessGrid && chessGrid.inEndArea) {
            if (chessGrid && chessGrid.station.gridPosition == -4) return true;

            let emptyGrid;
            let color = JackaroGamePlayerManager.instance.getColorByIdx(beyondChess.beyondIdx);

            for (let i = -4; i < 0; i++) {
                let curStation = { area: color, gridPosition: i };
                let curGrid: LudoGrid = JackaroBoardData.getGridByStation(curStation);
                let ch = this.getChessByGrid(curGrid);
                if (!ch) {
                    emptyGrid = curGrid;
                    break;
                }
            }
            if (!emptyGrid) {
                return false;
            }
            if (chessGrid && chessGrid.atGrid(emptyGrid.gridName)) {
                return true;
            }
        }
        return false;
    }
    private moveHome(chess: JackaroChess, type: yalla.data.jackaro.JackarooMoveType = yalla.data.jackaro.JackarooMoveType.MOVE_NORMAL) {
        if (!chess) {
            return;
        }
        let homeGrid = this.getMoveHomeGrid(chess.color);
        if (homeGrid) {
            this.resetExpPlayer(chess)
            chess.move(homeGrid.station, 0, type);
        }
    }
    /**
     * 判断棋子移动指定步数时是否可以击杀，用于出牌7判断击杀的棋子不能再次选中 
     */
    private getChessMoveXStepCanKillChess(chess: JackaroChess, stepNum: number) {
        if (chess && !chess.wayGrid.inEndArea) {
            let curGrid = chess.wayGrid;
            let chessColor = chess.color;
            for (let index = 0; index < stepNum; index++) {
                if (curGrid) {
                    if (curGrid.inCorner && curGrid.color == chessColor) {
                        curGrid = JackaroBoardData.getGridByName(`${chessColor}_-1`);
                    } else {
                        let nextGrid = JackaroBoardData.getGridByName(curGrid.nextName);
                        if (nextGrid) {
                            curGrid = nextGrid;
                        } else {
                            curGrid = null;
                        }
                    }
                }
            }
            if (curGrid && !curGrid.inEndArea) {
                let ch = this.getChessByGrid(curGrid);
                if (ch) {
                    return ch.beyondIdx + "_" + ch.id;
                }
            }
            return null;
        }
        return null
    }

    /*
    * 计算棋子移动指定步数后的终点位置和移动总时间
     * 主要用于1v1模式，支持传送点逻辑
    * @param chess 要移动的棋子对象
    * @param stepNum 移动步数，必须大于0
    * @returns 返回包含终点格子和移动总时间的对象，如果无法移动则返回null
    */
    private calculateChessEndPositionAndTime(chess: JackaroChess, stepNum: number): { endGrid: LudoGrid | null, moveTotalTime: number } | null {
        // 参数验证
        if (stepNum <= 0 || !chess || !chess.wayGrid) {
            return null;
        }

        let currentGrid = chess.wayGrid;
        let currentChessColor = chess.wayGrid.color;

        let totalMoveTime = 0;

        // 模拟逐步移动
        for (let stepIndex = 0; stepIndex < stepNum; stepIndex++) {
            if (!currentGrid) {
                break; // 无法继续移动
            }
            // 处理拐角进入终点区域的逻辑
            if (currentGrid.inCorner && currentGrid.color === currentChessColor) {
                currentGrid = JackaroBoardData.getGridByName(`${currentChessColor}_-1`);
                totalMoveTime += JackaroChess.realMoveTime;
            } else {
                // 正常移动到下一个格子
                const nextGrid = JackaroBoardData.getGridByName(currentGrid.nextName);
                if (nextGrid) {
                    currentGrid = nextGrid;
                    totalMoveTime += JackaroChess.realMoveTime;
                } else {
                    currentGrid = null; // 无法继续移动
                }
            }
        }

        // 处理传送点逻辑（1v1模式特有）
        if (currentGrid && currentGrid.gridPosition === JackaroChess.transferBeginGridPosition) {
            currentChessColor = (currentChessColor + 1) % 2; // 切换到对方颜色区域
            const transferEndPosition = JackaroChess.transferEndGridPosition;
            const transferEndGrid = JackaroBoardData.getGridByStation({
                area: currentChessColor,
                gridPosition: transferEndPosition
            });

            if (transferEndGrid) {
                totalMoveTime += JackaroChess.transferMoveTime;
                currentGrid = transferEndGrid;
            }
        }

        return { endGrid: currentGrid, moveTotalTime: totalMoveTime };
    }


    /**
     * 通过出牌 k，吃掉路径上的棋子
     */
    private moveHomeByPlayPokerK(chess: JackaroChess, bornIndex: number, color: number, type: yalla.data.jackaro.JackarooMoveType = yalla.data.jackaro.JackarooMoveType.MOVE_NORMAL) {
        if (!chess) {
            return;
        }
        let curStation = { area: color, gridPosition: 100 + bornIndex };
        let curGrid: LudoGrid = JackaroBoardData.getGridByStation(curStation);
        let homeGrid = curGrid;
        if (homeGrid) {
            yalla.Sound.playSound("1kills");
            this.resetExpPlayer(chess);
            if (yalla.Global.isFouce) {
                let delayMoveTime = JackaroChess.realMoveTime / 2;
                Laya.timer.once(delayMoveTime, this, () => {
                    chess && !chess.destroyed && chess.move(homeGrid.station, 0, type);
                }, null, false);
            } else {
                chess.move(homeGrid.station, 0, type);
            }
        }

        let chessPlayPokerKKillSpineAni = this.getPlayPokerKKillSpineAni();
        if (chessPlayPokerKKillSpineAni) {
            let chessWayGrid = chess.wayGrid;
            this.chessPlayPokerKKillSpineAniArr.push(chessPlayPokerKKillSpineAni);
            this.mapLayer.getChildByName("chess_box").addChild(chessPlayPokerKKillSpineAni);
            chessPlayPokerKKillSpineAni.pos(chessWayGrid.port.x, chessWayGrid.port.y);
            chessPlayPokerKKillSpineAni.play("animation", false);
            chessPlayPokerKKillSpineAni.setAniblendMode("lighter");
            chessPlayPokerKKillSpineAni.setSpeed(1.5);
            chessPlayPokerKKillSpineAni.alpha = 0.5;
            chessPlayPokerKKillSpineAni.name = chessWayGrid.myName;

        }
    }
    /**
    * 加经验表现
    */
    public showStar(port: port, expType: number) {
        if (!this.mapLayer || !this.mapLayer.parent) return;
        let exp = yalla.data.jackaro.JackaroUserService.instance.room.getExpByType(expType);
        if (!exp) return;
        let idx = yalla.Global.Account.idx;
        let player: JackaroGamePlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(idx);
        if (player && port) {
            this.playerExpIndex++;
            //TODO::得经验后，飘星星动画层级调整 gameUI-〉operateLayer，避免遮挡聊天等view
            var starParent = this.mapLayer.parent.parent.getChildByName("operateLayer");
            var star: StarAni = LudoBuffManager.instance.getStarAni();
            var endpoint: port = player.head.localToGlobal(new Laya.Point(), true);
            var index = this.playerExpIndex > 0 ? this.playerExpIndex - 1 : 0;
            endpoint && star && star.fromTo(port, endpoint, index * 2, starParent, Laya.Handler.create(this, (exp) => {
                !player.destroyed && player.showEXP(exp);
                if (this.playerExpIndex > 0) this.playerExpIndex--;
            }, [exp]), 0.5, 180);
            // yalla.Sound.playSound("exp_add");

        }
    }
    public getPathsPos(goalGrid: LudoGrid, wayGrid: LudoGrid, forward: boolean = true, color: number) {//获取路径坐标
        let lineGirds = [];
        if (!goalGrid) {
            return lineGirds;
        }
        var len = forward ? JackaroBoardData.distanceStep(wayGrid, goalGrid) : JackaroBoardData.distanceStep(goalGrid, wayGrid);
        var endArea: boolean = goalGrid.inEndArea;

        for (var i = 0; i < len; i++) {
            var grid: LudoGrid;
            if (forward) {
                if (wayGrid.beforeEnd) {//在终点前-4的格子
                    grid = JackaroBoardData.getGridByName(`${color}_${-4}`);
                    goalGrid = grid;
                } else if (endArea && wayGrid.inCorner && wayGrid.station.area == color) {//在0的拐角位置
                    grid = JackaroBoardData.getGridByName(`${color}_-1`);
                } else {//其它情况
                    grid = JackaroBoardData.getGridByName(wayGrid.nextName);
                }
            } else {
                grid = JackaroBoardData.getGridByName(wayGrid.lastName);
            }
            if (grid) {
                lineGirds.push(grid)
                wayGrid = grid;
                if (wayGrid.atGrid(goalGrid.gridName)) {
                    return lineGirds;
                }//和目标点一致
            }
        }
        return lineGirds;
    }
    /**
     * 获取棋子轨道
     */
    private getChessOrbitItem() {
        var chessOrbitItem;
        chessOrbitItem = Laya.Pool.getItemByClass('JackaroOrbit', Laya.Image);
        // if (!chessOrbitItem) {
        //     chessOrbitItem = new Laya.Image();
        // }
        chessOrbitItem.size(32, 32);
        chessOrbitItem.anchorX = chessOrbitItem.anchorY = 0.5;
        chessOrbitItem.name = "";
        return chessOrbitItem;
    }
    /**
     * 移除所有棋子轨道预览效果
     */
    public removeAllChessOrbit() {
        // {{ YUM: [Optimize] - 使用新的动效管理器清空所有 Spine 动效 }}
        JackaroChessOrbitEffectManager.instance.clearAllEffects();

        // {{ YUM: [Restore] - 原有的轨道图片处理逻辑（新管理器不处理轨道图片） }}
        this.removeChessOrbitItem();


    }
    /**
     * 移除所有棋子击杀效果
     */
    public removeAllChessKillSpineAni() {
        this.removeChessKillSpineAni();
        this.removePlayPokerKKillSpineAni();
    }
    /**
     * 移除棋子预览效果，不移除spine特效
     */
    private removeChessOrbitItem() {
        this.chessOrbitItem.forEach(item => {
            if (item) {
                item.removeSelf();
                !item.destroyed && Laya.Pool.recover('JackaroOrbit', item);
            }
        })
        this.chessOrbitItem = [];
    }


    /**
     * 获取棋子普通击杀效果
     */
    public getChessKillSpineAni(): SpineAniPlayer {
        let chessOrbitSpineAni = Laya.Pool.getItemByClass("ChessKillSpineAni", SpineAniPlayer);
        chessOrbitSpineAni.anchorX = chessOrbitSpineAni.anchorY = 0.5;
        chessOrbitSpineAni.load(yalla.getSkeleton("jackaro/chizi/chiZi01/chiZi01"));
        return chessOrbitSpineAni;
    }
    /**
     * 获取棋子交换开始效果
     */
    public getChessExchangeStartSpineAni(): SpineAniPlayer {
        let chessOrbitSpineAni = Laya.Pool.getItemByClass('ChessExchangeSpineAni', SpineAniPlayer);
        chessOrbitSpineAni.anchorX = chessOrbitSpineAni.anchorY = 0.5;
        chessOrbitSpineAni.load(yalla.getSkeleton("jackaro/dhwz/dhwz"));
        this.chessExchagneSpineAni.push(chessOrbitSpineAni);
        return chessOrbitSpineAni;
    }
    /**
     * 获取棋子交换结束效果
     */
    public getChessExchangeEndSpineAni(): SpineAniPlayer {
        let chessOrbitSpineAni = Laya.Pool.getItemByClass('ChessExchangeEndSpineAni', SpineAniPlayer);
        chessOrbitSpineAni.anchorX = chessOrbitSpineAni.anchorY = 0.5;
        chessOrbitSpineAni.load(yalla.getSkeleton("jackaro/exchange/Exchange"));
        this.chessExchagneEndSpineAni.push(chessOrbitSpineAni);
        return chessOrbitSpineAni;
    }
    /**
     * 获取出牌K击杀效果
     */
    public getPlayPokerKKillSpineAni(): SpineAniPlayer {
        let chessOrbitSpineAni = Laya.Pool.getItemByClass('PlayPokerKKillSpineAni', SpineAniPlayer);
        chessOrbitSpineAni.anchorX = chessOrbitSpineAni.anchorY = 0.5;
        chessOrbitSpineAni.load(yalla.getSkeleton("jackaro/shouji/shouji"));
        return chessOrbitSpineAni;
    }
    public removePlayPokerKKillSpineAni() {
        this.chessPlayPokerKKillSpineAniArr.forEach(item => {
            if (item) {
                item.stop();
                item.removeSelf();
                !item.destroyed && Laya.Pool.recover('PlayPokerKKillSpineAni', item);
            }
        })
        this.chessPlayPokerKKillSpineAniArr = [];
    }
    private hideChessExchangeStartSpineAni() {
        this.chessExchagneSpineAni.forEach(item => {
            //todo 可能还没创建完成，导致创建后会显示出来
            item.setCurVisible(false);
            item.visible = false;
        });
    }
    public hideChessExchangeSpineAni() {
        this.hideChessExchangeStartSpineAni();
        this.chessExchagneEndSpineAni.forEach(item => {
            item.visible = false;
        });
    }
    public removeChessExchangeSpineAni() {
        this.chessExchagneSpineAni.forEach(item => {
            if (item) {
                item.removeSelf();
                !item.destroyed && Laya.Pool.recover('ChessExchangeSpineAni', item);
            }
        });
        this.chessExchagneSpineAni = [];
        this.chessExchagneEndSpineAni.forEach(item => {
            if (item) {
                item.removeSelf();
                !item.destroyed && Laya.Pool.recover('ChessExchangeEndSpineAni', item);
            }
        });
        this.chessExchagneEndSpineAni = [];
    }
    public getChessByGrid(grid: LudoGrid): JackaroChess {
        if (!grid) return null;
        for (let key in this._pool) {
            let ch = this._pool[key];
            if (ch.wayGrid.atGrid(grid.gridName)) {
                return ch;
            }
        }
        return null;
    }
    public isFastModeChessInEndArea(idx: number): boolean {
        for (let key in this._pool) {
            let ch = this._pool[key];
            if (ch && ch.beyondIdx == idx && ((ch.wayGrid && ch.wayGrid.station.gridPosition == -4) || (ch.goalGrid && ch.goalGrid.station.gridPosition == -4))) {
                return true;
            }
        }
        return false;
    }

    public getChessInEndAreaNum(idx: number): number {
        let num = 0;
        for (let key in this._pool) {
            let ch = this._pool[key];
            if (ch && ch.beyondIdx == idx && (ch.wayGrid.inEndArea || ch.goalGrid.inEndArea)) {
                num++;
            }
        }
        return num;
    }
    public isSelfChessAllInEndArea(idx: number): boolean {
        let room = yalla.data.jackaro.JackaroUserService.instance.room;
        if (room && room.isQuickBasicMode()) {
            return this.isFastModeChessInEndArea(idx);
        } else {
            return this.getChessInEndAreaNum(idx) == 4;
        }
    }
    /**
     * 是否满足完赛
     */
    public isRequireWinGame(): boolean {
        if (this.collectPoker7WaitingChess && this.collectPoker7WaitingChess.length > 0) {
            let chessData = this.collectPoker7WaitingChess[0];
            let moveStep = chessData.number;
            let chess = this.getChessById(chessData.idx + "_" + chessData.order);
            let curWayGrid = chess.wayGrid;
            //棋子移动指定步数可以到达的格子，用于判断是否可以完赛
            if (chess) {
                for (let index = 0; index < moveStep; index++) {
                    if (curWayGrid.inCorner) {
                        curWayGrid = JackaroBoardData.getGridByName(`${curWayGrid.color}_-1`);
                    } else {
                        curWayGrid = JackaroBoardData.getGridByName(curWayGrid.nextName);
                    }
                    if (!curWayGrid) {
                        break;
                    }
                }
            }
            let emptyGridInEndArea = this.getEmptyGridInEndArea();
            if (!emptyGridInEndArea) {
                //没有空格子，说明可以完赛
                return true;
            } else {
                if (curWayGrid) {
                    let room = yalla.data.jackaro.JackaroUserService.instance.room;
                    if (room && room.isQuickBasicMode()) {
                        if (emptyGridInEndArea.gridPosition == -4 && curWayGrid.atGrid(emptyGridInEndArea.gridName)) {
                            return true;
                        }
                    } else if (emptyGridInEndArea.gridPosition == -1 && curWayGrid.atGrid(emptyGridInEndArea.gridName)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }
    /**
     * 终点区域空的格子，从-4开始算
     * @param idx 
     * @returns 
     */
    public getEmptyGridInEndArea(): LudoGrid {
        let selfColor = JackaroGamePlayerManager.instance.getSelfColor();
        let emptyGrid;
        for (let i = -4; i < 0; i++) {
            let curStation = { area: selfColor, gridPosition: i };
            let curGrid: LudoGrid = JackaroBoardData.getGridByStation(curStation);
            let ch = this.getChessByGrid(curGrid);
            if (!ch) {
                emptyGrid = curGrid;
                break;
            }
        }
        return emptyGrid;
    }
    public isChessInCorner(idx: number): boolean {
        for (let key in this._pool) {
            let ch = this._pool[key];
            if (ch.wayGrid.inCorner && ch.beyondIdx == idx) {
                return true;
            }
        }
        return false;
    }
    /**
     * 更新所有棋子选择圈特效 和 点击事件
     * @param chess 
     */
    public updateChessChoose() {
        var player = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);
        var isTrust = player && player.getTrustSystem();
        isTrust ? JkChooseChessManager.ins.removeAll() : JkChooseChessManager.ins.playChessChoose(this.topAniLayer);
    }
    public addCheckMoveChessData(data: ToGridInterface) {
        if (JackaroGamePlayerManager.instance.isMyActive()) {
            this.checkMoveChessData.push(data);
        }
    }
    /**
     * 针对集中选完棋子后再移动，是否临时可以走队友的棋子
     * @returns 
     */
    private isCanMoveTeamChessTemporary2() {
        let curMoveChessPlayerIdx = JackaroGamePlayerManager.instance.getMoveChessPlayerIdx();
        if (curMoveChessPlayerIdx == yalla.Global.Account.idx) {
            //此时其实自己的回合在服务器阶段 还不可以走队友棋子，只是自己的回合在前端表现符合可以走队友棋子
            let isSelfTurnEnd = this.isRequireWinGame();
            return isSelfTurnEnd;
        } else {
            return false;
        }
    }
    public getChessesByPlayerIdx(playerIdx: number): Array<JackaroChess> {
        let ret: Array<JackaroChess> = [];
        for (var k in this._pool) {
            var chess = this._pool[k];
            if (chess.beyondIdx == playerIdx) ret.push(chess);

        }
        return ret;
    }

    public addChessBubble(chess: JackaroChess, bubble) {
        let key = chess.idKey;
        if (!this._chessBubbleHash) this._chessBubbleHash = {};
        this._chessBubbleHash[key] = bubble;
    }
    public removeChessBubble(chess: JackaroChess) {
        let key = chess.idKey;
        if (this._chessBubbleHash) {
            let bubble = this._chessBubbleHash[key];
            if (bubble && bubble.displayedInStage) bubble.removeSelf();
            delete this._chessBubbleHash[key];
        }
    }

    public clearPool() {
        this.removeAllChessOrbit();
        this.removeAllChessKillSpineAni();
        this.removeChessExchangeSpineAni();
        // this.removeChessChooseSpineAni();
        JkChooseChessManager.ins.removeAll();
        Laya.Pool.clearBySign("PlayPokerKKillSpineAni");
        Laya.Pool.clearBySign("ChessExchangeSpineAni");
        Laya.Pool.clearBySign("ChessExchangeEndSpineAni");
        Laya.Pool.clearBySign("JackaroOrbit");
        Laya.Pool.clearBySign("ChessOrbitSpineAni");

        let maxColorNum = 4;
        for (let i = 0; i < maxColorNum; i++) {
            Laya.Pool.clearBySign(`ChessChoose${i}SpineAni`);
            Laya.Pool.clearBySign(`ChessOrbitEnd${i}PointSpineAni`);
        }
    }
    public isPokerCanShowOrbit(poker: yalla.data.jackaro.Poker) {
        return JackaroCardManger.instance.isPoker7(poker) || JackaroCardManger.instance.isPoker10(poker) || JackaroCardManger.instance.isPoker5(poker) || JackaroCardManger.instance.isPoker4(poker);
    }
    /**
    * {{ YUM: [新增] - 统一更新移动数据并累积previewRewardPoker状态 }}
    */
    private updateRecentMoveResultData(result: any, moveStep?: number) {
        // 如果当前结果包含previewRewardPoker，累积到accumulatedPreviewRewardPoker中
        if (result && result.previewRewardPoker) {
            if (result.drawPokerMoveNumber && result.drawPokerMoveNumber.length > 0 || result.drawPokerCombination && result.drawPokerCombination[moveStep]) {
                if (result.drawPokerMoveNumber.indexOf(moveStep) >= 0) {
                    if (!this.accumulatedPreviewRewardPoker) {
                        this.accumulatedPreviewRewardPoker = result.previewRewardPoker;
                    }
                }
                if (!this.accumulatedPreviewRewardPoker && result.drawPokerCombination && result.drawPokerCombination[moveStep] && this.collectPoker7WaitingChess.length > 1) {
                    let combination = result.drawPokerCombination[moveStep];
                    let waitMoveChessIndex = -1;
                    for (let i = 0; i < this.collectPoker7WaitingChess.length; i++) {
                        let waitData = this.collectPoker7WaitingChess[i];
                        if (result && waitData && result.fromGrid && result.fromGrid.chess && result.fromGrid.chess.idx == waitData.idx && result.fromGrid.chess.order == waitData.order) {
                            waitMoveChessIndex = i;
                        }
                    }
                    if (waitMoveChessIndex != -1) {
                        let otherIndex = waitMoveChessIndex = waitMoveChessIndex == 0 ? 1 : 0;
                        let otherWaitData = this.collectPoker7WaitingChess[otherIndex];
                        if (otherWaitData && combination && combination.order) {
                            let orders = combination.order;
                            if (orders) {
                                for (let i = 0; i < orders.length; i++) {
                                    let order = orders[i];
                                    if (order == otherWaitData.order) {
                                        this.accumulatedPreviewRewardPoker = result.previewRewardPoker;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                if (!this.accumulatedPreviewRewardPoker) {
                    this.accumulatedPreviewRewardPoker = result.previewRewardPoker;
                }
            }
        }

        // 更新recentMoveResultData
        if (!this.recentMoveResultData) {
            this.recentMoveResultData = {};
        }
        this.recentMoveResultData[moveStep] = result;
    }
    /**
     * {{ YUM: [新增] - 检查服务器数据中的previewRewardPoker字段 }}
     * @param triggerType 触发类型：'KILL', 'ENDPOINT', 'FLY_FROM_BASE'
     * @param playerId 玩家ID
     * @returns 是否可以触发奖励
     */
    public checkPreviewRewardPoker(triggerType?: string, playerId?: number): boolean {
        try {
            if (!this.recentMoveResultData) {
                yalla.Debug.yum(`[抽牌奖励] 检查${triggerType}奖励 - 没有移动数据`);
                return false;
            }

            // {{ YUM: [修复] - 优先检查累积的previewRewardPoker，然后检查当前移动数据 }}
            let previewRewardPoker = false;

            // 首先检查累积的previewRewardPoker
            if (this.accumulatedPreviewRewardPoker) {
                // 检查累积对象是否包含有效的奖励数据
                previewRewardPoker = this.accumulatedPreviewRewardPoker;
            }

            yalla.Debug.yum(`[抽牌奖励] 检查${triggerType}奖励`, {
                playerId,
                previewRewardPoker,
                accumulatedData: this.accumulatedPreviewRewardPoker,
                recentMoveResultData: this.recentMoveResultData
            });

            return previewRewardPoker;
        } catch (error) {
            yalla.Debug.yum(`[抽牌奖励] 检查${triggerType}奖励失败:`, error);
            return false;
        }
    }

    /**
     * 设置抽牌奖励动效结束时间戳
     * @param endTime 动效结束时间戳
     */
    public setDrawRewardAnimationEndTime(endTime: number): void {
        this.drawRewardAnimationEndTime = endTime;
    }

}
