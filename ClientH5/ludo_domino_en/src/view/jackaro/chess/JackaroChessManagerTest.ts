
/**
 * 杰克罗棋子管理器测试
 */
class JackaroChessManagerTest {
    static _instance: JackaroChessManagerTest = null;
    public static get instance(): JackaroChessManagerTest {
        return this._instance ? this._instance : this._instance = new JackaroChessManagerTest();
    }
    /**
     * 测试用例
     */
    public testCase() {
        console.log("testCase");
        this.testShowChessOrbit();
        // this.testHandleMoveChessPub();
        // this.testShowChooseChessOperate();
        // this.testHandlePlayPoker4();
        // this.testShowExChangeChessView();
        // this.testToast();
        // this.testSelectStepNumView();
        // this.testShowChooseChessTagView();
    }
    public testShowChessOrbit() {
        console.log("testShowChessOrbit");
        let msg = `{
            "3": {
                "result": [{
                    "fromGrid": {
                        "grid": {
                            "index": 18,
                            "color": "2",
                            "type": "COMMON_POINT"
                        },
                        "chess": {}
                    },
                    "toGrid": {
                        "grid": {
                            "index": 2,
                            "color": "1",
                            "type": "FLY_POINT"
                        },
                        "chess": {
                            "idx": 10002,
                            "order": 1,
                            "color": "YELLOW"
                        }
                    }
                },{
                    "fromGrid": {
                        "grid": {
                            "index": 17,
                            "color": "2",
                            "type": "COMMON_POINT"
                        },
                        "chess": {}
                    },
                    "toGrid": {
                        "grid": {
                            "index": 2,
                            "color": "1",
                            "type": "FLY_POINT"
                        },
                        "chess": {
                            "idx": 10002,
                            "order": 2,
                            "color": "YELLOW"
                        }
                    }
                }]
            }
        }`;
        let parseMsg = JSON.parse(msg);
        //起始移动的棋子
        let moveChess = JackaroChessManager.instance.getChessById(parseMsg["3"].result[0].toGrid.chess.idx + "_" + parseMsg["3"].result[0].toGrid.chess.order);
        if (moveChess) {
            moveChess.move({ area: parseMsg["3"].result[0].fromGrid.grid.color, gridPosition: parseMsg["3"].result[0].fromGrid.grid.index }, ChessMoveType.MOVE_NORMAL);
        }
        //终点的棋子
        // let endPointChess = JackaroChessManager.instance.getChessById(parseMsg["3"].result[1].toGrid.chess.idx + "_" + parseMsg["3"].result[1].toGrid.chess.order);
        // if (endPointChess) {
        //     endPointChess.move({ area: parseMsg["3"].result[1].fromGrid.grid.color, gridPosition: parseMsg["3"].result[1].fromGrid.grid.index }, ChessMoveType.MOVE_NORMAL);
        // }
        Laya.timer.once(1000, this, () => {
            // JackaroChessManager.instance.showChessOrbitByLength(yalla.data.jackaro.Poker.CLUB_ACE, parseMsg);
            // JackaroChessOrbitManager.instance.createChessSnapshot();
            JackaroChessOrbitManager.instance.createPathGridData(moveChess, true, 7, 1);
        })
    }
    public testHandleMoveChessPub() {
        console.log("testHandleMoveChessPub");
        let msg = `{"moveResultData":{"fromGrid":{"grid":{"index":18,"color":"3","type":"COMMON_POINT"},"chess":{}},"toGrid":{"grid":{"index":2,"color":"2","type":"FLY_POINT"},"chess":{"idx":11245,"order":1,"color":"3"}}},"currentPoker":"DIAMOND_THREE","moveChess":true,"moveChessPlayer":11245}`;
        let parseMsg = JSON.parse(msg);
        let chess = JackaroChessManager.instance.getChessById(parseMsg.moveResultData.toGrid.chess.idx + "_" + parseMsg.moveResultData.toGrid.chess.order);

        if (chess) {
            chess.move({ area: parseMsg.moveResultData.fromGrid.grid.color, gridPosition: parseMsg.moveResultData.fromGrid.grid.index }, ChessMoveType.MOVE_NORMAL);
        }
        Laya.timer.once(3000, this, () => {
            JackaroChessManager.instance.handleMoveChessPub(parseMsg);
        })
    }
    public testHandlePlayPoker4() {
        console.log("testHandlePlayPoker4");
        let msg = `{"-4":{"result":[{"fromGrid":{"grid":{"index":2,"color":"3","type":"FLY_POINT"},"chess":{"idx":11405,"order":2,"color":"3"}},"toGrid":{"grid":{"index":17,"color":"3","type":"COMMON_POINT"}}}]}}`;
        let parseMsg = JSON.parse(msg);

        let chess = JackaroChessManager.instance.getChessById(parseMsg["-4"].result[0].toGrid.chess.idx + "_" + parseMsg["-4"].result[0].toGrid.chess.order);

        if (chess) {
            chess.move({ area: parseMsg["-4"].result[0].fromGrid.grid.color, gridPosition: parseMsg["-4"].result[0].fromGrid.grid.index }, ChessMoveType.MOVE_NORMAL);
        }
        Laya.timer.once(3000, this, () => {
            chess.move({ area: parseMsg["-4"].result[0].toGrid.grid.color, gridPosition: parseMsg["-4"].result[0].toGrid.grid.index }, ChessMoveType.MOVE_NORMAL);
        })
    }
    public testShowChooseChessOperate() {
        console.log("testShowChooseChessOperate");
        let chesss = JackaroChessManager.instance.getChessesByColor(JackaroGamePlayerManager.instance.getColorByIdx(yalla.Global.Account.idx));
        console.log("testShowChooseChessOperate chesss", chesss);
        if (chesss && chesss.length > 0) {
            for (let chess of chesss) {
                chess.choose = true;
            }
        }
        let tips: string = "Choose a stone\nto move "

        TipsViewManager.getInstance().showTip(TipViewType.CHOOSE_CHESS_OPERATE, { operateTime: JackaroGamePlayerManager.instance.getOperateTime(), tips: tips });
    }
    public testShowExChangeChessView() {
        console.log("testShowExChangeChessView");
        let chesss = JackaroChessManager.instance.getChessesByColor(JackaroGamePlayerManager.instance.getColorByIdx(yalla.Global.Account.idx));
        console.log("testShowExChangeChessView chesss", chesss);
        if (chesss && chesss.length > 0) {
            for (let chess of chesss) {
                chess.choose = true;
            }
        }
        let tips: string = "Choose a stone\nto move "

        TipsViewManager.getInstance().showTip(TipViewType.EXCHANGE_CHESS, { operateTime: JackaroGamePlayerManager.instance.getOperateTime(), tips: tips });
    }
    public testShowSelectOperate() {
        // TipsViewManager.getInstance().showTip(TipViewType.SELECT_OPERATE, { operateCallBack: [], isPokerA: true, isPoker10: false, operateTime: JackaroGamePlayerManager.instance.getOperateTime() });
        // TipsViewManager.getInstance().showTip(TipViewType.SELECT_OPERATE, { operateCallBack: [], isPokerA: false, isPoker10: true, operateTime: JackaroGamePlayerManager.instance.getOperateTime() });
    }
    public testToast() {
        Laya.timer.once(5000, this, () => {
            ToastManager.showToast("欢迎来到游戏世界！");
        })
    }
    public testSelectStepNumView() {
        TipsViewManager.getInstance().showTip(TipViewType.SELECT_STEP_NUM, {
            maxStep: 7,
            minStep: 6,
            chessID: 0,
            operateTime: JackaroGamePlayerManager.instance.getOperateTime(),
            notAllowMoveNumber: [1, 2, 3, 4, 5]
        });
    }
    public testShowChooseChessTagView() {
        console.log("testShowChooseChessTagView");
        let chesss = JackaroChessManager.instance.getChessesByColor(JackaroGamePlayerManager.instance.getColorByIdx(yalla.Global.Account.idx));
        console.log("testShowChooseChessTagView chesss", chesss);
        if (chesss && chesss.length > 0) {
            for (let chess of chesss) {
                chess.choose = true;
            }
        }
        JackaroChessManager.instance.checkShowChooseChessTagView(chesss[0], chesss);

    }
}
