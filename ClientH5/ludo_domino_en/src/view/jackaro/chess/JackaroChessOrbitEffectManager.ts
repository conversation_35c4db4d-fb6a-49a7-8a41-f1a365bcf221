/**
 * Jackaro棋子轨道动效管理器 - 优化版本
 * 解决同一位置重复删除创建导致的视觉闪烁问题
 * 只管理 Spine 动效，不处理轨道图片
 */
class JackaroChessOrbitEffectManager {
    private static _instance: JackaroChessOrbitEffectManager;
    public static get instance(): JackaroChessOrbitEffectManager {
        return this._instance || (this._instance = new JackaroChessOrbitEffectManager());
    }

    // {{ YUM: [Create] - 基于坐标位置的 Spine 动效缓存管理，避免重复创建删除 }}
    private orbitSpineAniMap: Record<string, SpineAniPlayer> = {}; // 轨道击杀动效缓存
    private endPointSpineAniMap: Record<string, SpineAniPlayer> = {}; // 终点动效缓存
    private endPointColorMap: Record<string, number> = {}; // {{ YUM: [Add] - 终点动效颜色记录，用于颜色变化检测 }}
    // {{ YUM: [Add] - 击杀动效类型记录，用于区分队友和敌方击杀 }}
    private killEffectTypeMap: Record<string, 'teammate' | 'enemy'> = {};
    // {{ YUM: [Add] - 队友击杀动效缓存，用于管理右上角的动效显示 }}
    private teammateKillEffectMap: Record<string, SpineAniPlayer> = {};

    private parentKillLayer: laya.display.Node = null;//chess_box
    private parentEndPointLayer: laya.display.Node = null;//props_box
    private endPointOrbitAniNames: Array<string> = ["hong", "lan", "huang", "lv"];//终点动效名称
    private chessEndPointAniPath: Array<string> = [];//终点动效路径
    
    // {{ YUM: [Level] - 层级管理：每次创建或复用动效都需要调整到最高层级 }}

    public init(parentKillLayer: laya.display.Node, parentEndPointLayer: laya.display.Node, endPointAniPath: Array<string>) {
        this.parentKillLayer = parentKillLayer;
        this.parentEndPointLayer = parentEndPointLayer;
        this.chessEndPointAniPath = endPointAniPath;
    }

    /**
     * {{ YUM: [Core] - 智能更新轨道动效，只处理 Spine 动效变化，支持队友击杀特殊显示 }}
     * @param newOrbitData 新的轨道数据
     */
    public updateOrbitEffects(newOrbitData: Array<{
        pos: { x: number, y: number },
        gridName: string,
        type: 'kill' | 'endpoint' | 'normal',
        color?: number,
        killedChessIdx?: number  // {{ YUM: [Add] - 被击杀棋子的玩家索引，用于判断是否为队友 }}
    }>) {
        // {{ YUM: [Debug] - 记录更新动效调用 }}
        this.yum("JackaroChessOrbitEffectManager", `更新轨道动效，数据条数：${newOrbitData.length}`);

        // {{ YUM: [Step1] - 收集新的 Spine 动效位置信息（忽略 normal 类型） }}
        const newKillPosMap: Record<string, boolean> = {};
        const newEndPointPosMap: Record<string, boolean> = {};

        for (let i = 0; i < newOrbitData.length; i++) {
            const data = newOrbitData[i];
            const posKey = this.getPosKey(data.pos.x, data.pos.y);

            if (data.type === 'kill') {
                newKillPosMap[posKey] = true;
            } else if (data.type === 'endpoint') {
                newEndPointPosMap[posKey] = true;
            }
            // {{ YUM: [Note] - 忽略 normal 类型，轨道图片由原管理器处理 }}
        }

        // {{ YUM: [Step2] - 删除不再需要的 Spine 动效 }}
        this.removeUnusedSpineEffects(newKillPosMap, newEndPointPosMap);

        // {{ YUM: [Step3] - 添加新的 Spine 动效（只处理不存在的位置） }}
        this.addNewSpineEffects(newOrbitData);

        // {{ YUM: [Step4] - 确保所有复用的击杀动效也调整到最高层级 }}
        this.ensureAllKillEffectsTopLevel();
    }

    /**
     * {{ YUM: [Level] - 确保所有击杀动效都在最高层级（包括复用的） }}
     */
    private ensureAllKillEffectsTopLevel() {
        const orbitKeys = Object.keys(this.orbitSpineAniMap);
        for (let i = 0; i < orbitKeys.length; i++) {
            const posKey = orbitKeys[i];
            const spine = this.orbitSpineAniMap[posKey];
            if (spine && spine.parent) {
                // {{ YUM: [Level] - 延迟1帧提升层级，确保复用动效也在最高层 }}
                Laya.timer.frameOnce(1, this, () => {
                    if (spine && spine.parent) {
                        this.parentKillLayer.setChildIndex(spine, this.parentKillLayer.numChildren - 1);
                        
                        // {{ YUM: [Add] - 如果有队友击杀动效，也要提升其层级到击杀动效之上 }}
                        const killEffect = this.teammateKillEffectMap[posKey];
                        if (killEffect && killEffect.parent) {
                            this.parentKillLayer.setChildIndex(killEffect, this.parentKillLayer.numChildren - 1);
                        }
                    }
                });
            }
        }
    }

    /**
     * {{ YUM: [Helper] - 生成位置唯一键 }}
     */
    private getPosKey(x: number, y: number): string {
        return `${x}_${y}`;
    }

    /**
     * {{ YUM: [Clean] - 移除不再使用的 Spine 动效 }}
     */
    private removeUnusedSpineEffects(
        newKillPosMap: Record<string, boolean>,
        newEndPointPosMap: Record<string, boolean>
    ) {
        // 移除不再需要的击杀动效
        const orbitKeys = Object.keys(this.orbitSpineAniMap);
        for (let i = 0; i < orbitKeys.length; i++) {
            const posKey = orbitKeys[i];
            if (!newKillPosMap[posKey]) {
                this.removeOrbitSpineAni(this.orbitSpineAniMap[posKey], posKey);
            }
        }

        // 移除不再需要的终点动效
        const endPointKeys = Object.keys(this.endPointSpineAniMap);
        for (let i = 0; i < endPointKeys.length; i++) {
            const posKey = endPointKeys[i];
            if (!newEndPointPosMap[posKey]) {
                this.removeEndPointSpineAni(this.endPointSpineAniMap[posKey], posKey);
            }
        }
        // {{ YUM: [Note] - 轨道图片由原管理器处理，这里不再管理 }}
    }

    /**
     * {{ YUM: [Create] - 添加新的 Spine 动效（支持终点动效颜色替换和队友击杀特殊显示） }}
     */
    private addNewSpineEffects(newOrbitData: Array<{
        pos: { x: number, y: number },
        gridName: string,
        type: 'kill' | 'endpoint' | 'normal',
        color?: number,
        killedChessIdx?: number  // {{ YUM: [Add] - 被击杀棋子的玩家索引 }}
    }>) {
        for (let i = 0; i < newOrbitData.length; i++) {
            const data = newOrbitData[i];
            const posKey = this.getPosKey(data.pos.x, data.pos.y);

            if (data.type === 'kill') {
                const isTeammateKill = this.isTeammateKill(data.killedChessIdx);
                const currentKillType = isTeammateKill ? 'teammate' : 'enemy';
                const existingSpine = this.orbitSpineAniMap[posKey];
                
                // {{ YUM: [Smart] - 需要创建新动效的情况：无动效或类型不匹配 }}
                if (!existingSpine || this.killEffectTypeMap[posKey] !== currentKillType) {
                    if (existingSpine) {
                        this.yum("JackaroChessOrbitEffectManager", `击杀类型变化 位置:(${data.pos.x},${data.pos.y}) 旧:${this.killEffectTypeMap[posKey]} 新:${currentKillType}`);
                        this.removeOrbitSpineAni(existingSpine, posKey);
                    }
                    this.createOrbitSpineAni(data.pos.x, data.pos.y, data.gridName, posKey, isTeammateKill);
                }
            } else if (data.type === 'endpoint') {
                // {{ YUM: [ColorCheck] - 检查终点动效是否需要创建或替换 }}
                const existingSpine = this.endPointSpineAniMap[posKey];
                const existingColor = this.endPointColorMap[posKey];
                
                if (!existingSpine) {
                    // {{ YUM: [Create] - 位置没有动效，直接创建 }}
                    this.createEndPointSpineAni(data.pos.x, data.pos.y, data.gridName, data.color, posKey);
                } else if (existingColor !== data.color) {
                    // {{ YUM: [Replace] - 位置有动效但颜色不同，先删除后创建 }}
                    this.yum("JackaroChessOrbitEffectManager", `终点动效颜色变化 位置:(${data.pos.x},${data.pos.y}) 旧颜色:${existingColor} 新颜色:${data.color}`);
                    this.removeEndPointSpineAni(existingSpine, posKey);
                    this.createEndPointSpineAni(data.pos.x, data.pos.y, data.gridName, data.color, posKey);
                }
                // {{ YUM: [Keep] - 位置有动效且颜色相同，保持不变 }}
            }
            // {{ YUM: [Note] - 忽略 normal 类型，轨道图片由原管理器处理 }}
        }
    }

    /**
     * {{ YUM: [Feature] - 判断被击杀的棋子是否为队友 }}
     */
    private isTeammateKill(killedChessIdx: number): boolean {
        return killedChessIdx != null && 
               (killedChessIdx === yalla.Global.Account.idx || 
                JackaroGamePlayerManager.instance.isSameTeam(killedChessIdx));
    }

    /**
     * {{ YUM: [Helper] - 获取被击杀棋子索引，带优先级和回退逻辑 }}
     */
    private getKilledChessIdx(chessData: any, fallbackIdx?: number, gridName?: string): number | undefined {
        // {{ YUM: [Safety] - 防止空数据访问 }}
        if (!chessData) {
            return fallbackIdx;
        }
        // {{ YUM: [Priority1] - 优先使用数据中的 killedChessIdx }}
        if (chessData.killedChessIdx != null) {
            return chessData.killedChessIdx;
        }
        // {{ YUM: [Priority2] - 其次使用参数传入的 fallbackIdx }}
        if (fallbackIdx != null) {
            return fallbackIdx;
        }
        // {{ YUM: [Priority3] - 最后尝试从格子信息获取 }}
        if (gridName) {
            const gridInfo = JackaroChessOrbitManager.instance.getGridInfo(gridName);
            return gridInfo ? gridInfo.idx : undefined;
        }
        return undefined;
    }

    /**
     * {{ YUM: [Factory] - 创建轨道击杀动效，支持队友击杀特殊显示 }}
     */
    private createOrbitSpineAni(x: number, y: number, gridName: string, posKey: string, isTeammateKill: boolean = false) {
        // {{ YUM: [Debug] - 记录击杀动效创建 }}
        this.yum("JackaroChessOrbitEffectManager", `创建击杀动效 位置:(${x},${y}) 格子:${gridName} 键:${posKey} 队友击杀:${isTeammateKill}`);

        if (!this.parentKillLayer) {
            this.yum("JackaroChessOrbitEffectManager", `跳过击杀动效创建 - 父容器:${!!this.parentKillLayer} `);
            return;
        }

        const spineAni = Laya.Pool.getItemByClass("ChessOrbitSpineAni", SpineAniPlayer);
        spineAni.anchorX = spineAni.anchorY = 0.5;
        spineAni.load(yalla.getSkeleton("jackaro/chizi/chizi/chizi"));
        spineAni.pos(x, y);
        spineAni.name = gridName;
        
        if (isTeammateKill) {
            // {{ YUM: [TeammateEffect] - 队友击杀特殊效果：使用不同的混合模式和透明度 }}  
            // 等待UI 出图
            spineAni.play("zhiying", true);
            this.killEffectTypeMap[posKey] = 'teammate';
        } else {
            // {{ YUM: [EnemyEffect] - 敌方击杀正常效果 }}
            spineAni.play("zhiying", true);
            this.killEffectTypeMap[posKey] = 'enemy';
        }

        this.parentKillLayer.addChild(spineAni);
        this.orbitSpineAniMap[posKey] = spineAni;

        // {{ YUM: [Add] - 队友击杀时添加右上角提示动效 }}
        if (isTeammateKill) {
            this.createTeammateKillIcon(spineAni, posKey);
        }

        // {{ YUM: [Level] - 延迟1帧提升层级 }}
        Laya.timer.frameOnce(1, this, () => {
            if (spineAni && spineAni.parent) {
                this.parentKillLayer.setChildIndex(spineAni, this.parentKillLayer.numChildren - 1);
                
                // {{ YUM: [Add] - 如果有队友击杀动效，也要提升其层级到击杀动效之上 }}
                const killEffect = this.teammateKillEffectMap[posKey];
                if (killEffect && killEffect.parent) {
                    this.parentKillLayer.setChildIndex(killEffect, this.parentKillLayer.numChildren - 1);
                }
            }
        });
    }

    /**
     * {{ YUM: [Factory] - 创建终点动效 }}
     */
    private createEndPointSpineAni(x: number, y: number, gridName: string, color: number, posKey: string) {
        // {{ YUM: [Debug] - 记录终点动效创建 }}
        this.yum("JackaroChessOrbitEffectManager", `创建终点动效 位置:(${x},${y}) 格子:${gridName} 颜色:${color} 键:${posKey}`);

        if (!this.parentEndPointLayer || yalla.Global.isLowPerformanceDevice) {
            this.yum("JackaroChessOrbitEffectManager", `跳过终点动效创建 - 父容器:${!!this.parentEndPointLayer} 低性能设备:${yalla.Global.isLowPerformanceDevice}`);
            return;
        }

        if (color < 0 || color >= this.chessEndPointAniPath.length) {
            this.yum("JackaroChessOrbitEffectManager", `错误：颜色索引 ${color} 无效，有效范围: 0-${this.chessEndPointAniPath.length - 1}`);
            return;
        }

        const spineAni = Laya.Pool.getItemByClass(`ChessOrbitEnd${color}PointSpineAni`, SpineAniPlayer);
        spineAni.load(this.chessEndPointAniPath[color]);
        spineAni.anchorX = spineAni.anchorY = 0.5;
        spineAni.pos(x, y);
        spineAni.name = gridName;
        spineAni.alpha = 0.5;

        if (color < this.endPointOrbitAniNames.length) {
            spineAni.play(this.endPointOrbitAniNames[color], true);
        }

        this.parentEndPointLayer.addChild(spineAni);
        this.endPointSpineAniMap[posKey] = spineAni;
        this.endPointColorMap[posKey] = color; // {{ YUM: [Record] - 记录颜色信息 }}
    }

    /**
     * {{ YUM: [Feature] - 创建队友击杀动效，显示在击杀动效右上角 }}
     */
    private createTeammateKillIcon(spineAni: SpineAniPlayer, posKey: string) {
        // {{ YUM: [Debug] - 记录队友击杀动效创建 }}
        this.yum("JackaroChessOrbitEffectManager", `创建队友击杀动效 键:${posKey}`);

        // 创建队友击杀提示动效
        const killEffect = Laya.Pool.getItemByClass("TeammateKillEffect", SpineAniPlayer);
        killEffect.anchorX = killEffect.anchorY = 0.5;
        killEffect.load(yalla.getSkeleton("jackaro/tishi/tishi"));
        
        // {{ YUM: [Position] - 定位到击杀动效的右上角 }}
        // 基于击杀动效的位置，向右上偏移
        killEffect.pos(spineAni.x + 20, spineAni.y - 20);
        
        // {{ YUM: [Layer] - 添加到动效的父容器中，确保层级高于击杀动效 }}
        this.parentKillLayer.addChild(killEffect);
        
        // {{ YUM: [Level] - 设置动效层级高于击杀动效 }}
        const spineIndex = this.parentKillLayer.getChildIndex(spineAni);
        this.parentKillLayer.setChildIndex(killEffect, spineIndex + 1);
        
        // {{ YUM: [Animation] - 播放提示动效 }}
        killEffect.play("", true);
        
        // 缓存动效引用
        this.teammateKillEffectMap[posKey] = killEffect;
        
        this.yum("JackaroChessOrbitEffectManager", `队友击杀动效创建完成 位置:(${killEffect.x},${killEffect.y}) 层级:${this.parentKillLayer.getChildIndex(killEffect)}`);
    }

    /**
     * {{ YUM: [Feature] - 删除队友击杀动效 }}
     */
    private removeTeammateKillIcon(posKey: string) {
        const killEffect = this.teammateKillEffectMap[posKey];
        if (killEffect) {
            // {{ YUM: [Debug] - 记录队友击杀动效删除 }}
            this.yum("JackaroChessOrbitEffectManager", `删除队友击杀动效 键:${posKey}`);
            
            // {{ YUM: [Animation] - 停止动效播放 }}
            killEffect.stop();
            killEffect.removeSelf();
            
            // {{ YUM: [Pool] - 回收到对象池 }}
            !killEffect.destroyed && Laya.Pool.recover("TeammateKillEffect", killEffect);
            delete this.teammateKillEffectMap[posKey];
        }
    }

    /**
     * {{ YUM: [Remove] - 移除单个击杀动效 }}
     */
    private removeOrbitSpineAni(spine: SpineAniPlayer, posKey: string) {
        // {{ YUM: [Debug] - 记录击杀动效删除 }}
        this.yum("JackaroChessOrbitEffectManager", `删除击杀动效 格子:${spine.name} 键:${posKey}`);

        // {{ YUM: [Add] - 删除对应的队友击杀动效 }}
        this.removeTeammateKillIcon(posKey);

        spine.stop();
        spine.name = "";
        spine.removeSelf();
        !spine.destroyed && Laya.Pool.recover("ChessOrbitSpineAni", spine);
        delete this.orbitSpineAniMap[posKey];
        delete this.killEffectTypeMap[posKey]; // {{ YUM: [Clean] - 清理击杀类型记录 }}
    }

    /**
     * {{ YUM: [Remove] - 移除单个终点动效 }}
     */
    private removeEndPointSpineAni(spine: SpineAniPlayer, posKey: string) {
        const originalName = spine.name;
        // {{ YUM: [Debug] - 记录终点动效删除 }}
        this.yum("JackaroChessOrbitEffectManager", `删除终点动效 格子:${originalName} 键:${posKey}`);

        spine.stop();
        spine.name = "";
        spine.removeSelf();
        !spine.destroyed && Laya.Pool.recover(`ChessOrbitEnd${originalName}PointSpineAni`, spine);
        delete this.endPointSpineAniMap[posKey];
        delete this.endPointColorMap[posKey]; // {{ YUM: [Clean] - 清理颜色记录 }}
    }

    /**
     * {{ YUM: [Clear] - 清空所有 Spine 动效 }}
     */
    public clearAllEffects() {
        // {{ YUM: [Debug] - 记录清空所有动效 }}
        const orbitCount = Object.keys(this.orbitSpineAniMap).length;
        const endPointCount = Object.keys(this.endPointSpineAniMap).length;
        this.yum("JackaroChessOrbitEffectManager", `清空所有动效 - 击杀动效:${orbitCount}个 终点动效:${endPointCount}个`);

        // 清空击杀动效
        const orbitKeys = Object.keys(this.orbitSpineAniMap);
        for (let i = 0; i < orbitKeys.length; i++) {
            const spine = this.orbitSpineAniMap[orbitKeys[i]];
            spine.stop();
            spine.removeSelf();
            !spine.destroyed && Laya.Pool.recover("ChessOrbitSpineAni", spine);
        }
        this.orbitSpineAniMap = {};
        this.killEffectTypeMap = {}; // {{ YUM: [Clean] - 清理击杀类型记录 }}

        // {{ YUM: [Add] - 清空队友击杀动效 }}
        const killEffectKeys = Object.keys(this.teammateKillEffectMap);
        for (let i = 0; i < killEffectKeys.length; i++) {
            const killEffect = this.teammateKillEffectMap[killEffectKeys[i]];
            killEffect.stop();
            killEffect.removeSelf();
            !killEffect.destroyed && Laya.Pool.recover("TeammateKillEffect", killEffect);
        }
        this.teammateKillEffectMap = {};

        // 清空终点动效
        const endPointKeys = Object.keys(this.endPointSpineAniMap);
        for (let i = 0; i < endPointKeys.length; i++) {
            const spine = this.endPointSpineAniMap[endPointKeys[i]];
            spine.stop();
            spine.removeSelf();
            !spine.destroyed && Laya.Pool.recover(`ChessOrbitEnd${spine.name}PointSpineAni`, spine);
        }
        this.endPointSpineAniMap = {};
        this.endPointColorMap = {}; // {{ YUM: [Clean] - 清理颜色记录 }}
        // {{ YUM: [Note] - 轨道图片由原管理器处理，这里不清空 }}
    }

    /**
     * {{ YUM: [Public] - 公共方法：生成位置唯一键 }}
     */
    public getPositionKey(x: number, y: number): string {
        return this.getPosKey(x, y);
    }

    /**
     * {{ YUM: [Remove] - 删除指定位置的 Spine 动效 }}
     */
    public removeEffectByPosition(x: number, y: number) {
        const posKey = this.getPosKey(x, y);

        // {{ YUM: [Debug] - 记录按位置删除动效 }}
        const hasOrbit = !!this.orbitSpineAniMap[posKey];
        const hasEndPoint = !!this.endPointSpineAniMap[posKey];
        this.yum("JackaroChessOrbitEffectManager", `按位置删除动效 位置:(${x},${y}) 键:${posKey} - 击杀动效:${hasOrbit} 终点动效:${hasEndPoint}`);

        // 删除击杀动效
        if (this.orbitSpineAniMap[posKey]) {
            this.removeOrbitSpineAni(this.orbitSpineAniMap[posKey], posKey);
        }

        // 删除终点动效
        if (this.endPointSpineAniMap[posKey]) {
            this.removeEndPointSpineAni(this.endPointSpineAniMap[posKey], posKey);
        }
        // {{ YUM: [Note] - 轨道图片由原管理器处理，这里不删除 }}
    }

    /**
     * {{ YUM: [Query] - 根据gridName查找动效 }}
     */
    public findOrbitSpineAniByGridName(gridName: string): SpineAniPlayer {
        const orbitKeys = Object.keys(this.orbitSpineAniMap);
        for (let i = 0; i < orbitKeys.length; i++) {
            const spine = this.orbitSpineAniMap[orbitKeys[i]];
            if (spine.name === gridName) {
                return spine;
            }
        }
        return null;
    }

    /**
     * {{ YUM: [Utility] - 根据现有数据转换为标准格式，支持被击杀棋子信息 }}
     */
    public convertFromGridData(recorGrid: Record<string, any>, killedChessIdx?: number): Array<{
        pos: { x: number, y: number },
        gridName: string,
        type: 'kill' | 'endpoint' | 'normal',
        color?: number,
        killedChessIdx?: number  // {{ YUM: [Add] - 被击杀棋子的玩家索引 }}
    }> {
        const result = [];
        const keys = Object.keys(recorGrid);
        for (let i = 0; i < keys.length; i++) {
            const key = keys[i];
            const chessData = recorGrid[key];
            const type = chessData.kill ? 'kill' : (chessData.isEnd ? 'endpoint' : 'normal');
            const resultItem: any = {
                pos: { x: chessData.grid.port.x, y: chessData.grid.port.y },
                gridName: chessData.grid.myName,
                type: type,
                color: chessData.color
            };
            
            // {{ YUM: [Feature] - 如果是击杀类型，添加被击杀棋子的信息 }}
            if (type === 'kill') {
                const killedIdx = this.getKilledChessIdx(chessData, killedChessIdx, chessData.grid.myName);
                if (killedIdx != null) {
                    resultItem.killedChessIdx = killedIdx;
                    this.yum("JackaroChessOrbitEffectManager", `转换击杀数据 格子:${chessData.grid.myName} 被击杀玩家:${killedIdx}`);
                }
            }
            
            result.push(resultItem);
        }
        return result;
    }

    /**
     * {{ YUM: [Utility] - 根据复杂 Poker K 数据转换为标准格式，支持被击杀棋子信息 }}
     */
    public convertFromPokerKData(impactResult: Array<any>): Array<{
        pos: { x: number, y: number },
        gridName: string,
        type: 'kill' | 'endpoint' | 'normal',
        color?: number,
        killedChessIdx?: number  // {{ YUM: [Add] - 被击杀棋子的玩家索引 }}
    }> {
        // {{ YUM: [Debug] - 记录数据转换 }}
        this.yum("JackaroChessOrbitEffectManager", `转换PokerK数据，影响结果数量：${impactResult.length}`);

        const result = [];
        for (let i = 0; i < impactResult.length; i++) {
            const playResultData = impactResult[i];
            const fromGridData = playResultData.fromGrid;
            if (!fromGridData || !fromGridData.chess) continue;

            const toGridData = playResultData.toGrid;
            const fromGrid = fromGridData.grid;
            const toGrid = toGridData.grid;
            const curColor = fromGridData.chess.color || 0;

            const isKill = toGridData.chess ?
                fromGridData.chess.beyondIdx !== toGridData.chess.beyondIdx :
                false;

            const isEndPoint = toGrid.inEndArea || (toGrid.station && toGrid.station.gridPosition === -4);

            const type = isKill ? 'kill' : (isEndPoint ? 'endpoint' : 'normal');

            let toStation = { area: toGrid.color || 0, gridPosition: toGrid.index || 0 };
            let toLudoGrid: LudoGrid = JackaroBoardData.getGridByStation(toStation);
            
            const resultItem: any = {
                pos: { x: toLudoGrid.port.x, y: toLudoGrid.port.y },
                gridName: toLudoGrid.myName,
                type: type,
                color: curColor
            };
            
            // {{ YUM: [Feature] - 如果是击杀类型，添加被击杀棋子的信息 }}
            if (type === 'kill' && toGridData.chess && toGridData.chess.idx != null) {
                resultItem.killedChessIdx = toGridData.chess.idx;
                this.yum("JackaroChessOrbitEffectManager", `转换PokerK击杀数据 格子:${toLudoGrid.myName} 被击杀玩家:${toGridData.chess.idx}`);
            }
            
            // {{ YUM: [Debug] - 记录转换的每个结果 }}
            this.yum("JackaroChessOrbitEffectManager", `转换项目 ${i}: 类型:${type} 格子:${toLudoGrid.myName} 颜色:${curColor} 位置:(${toLudoGrid.port.x},${toLudoGrid.port.y})`);

            result.push(resultItem);
        }
        return result;
    }
    private yum(context: string, message: string, ...params: any[]): void {
        // this.yum(this.logTag + context, message, ...params);
    }
} 