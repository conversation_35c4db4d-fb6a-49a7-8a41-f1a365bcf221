
/// <reference path="TransferGridProcessor.ts" />
/// <reference path="GridDataTransformer.ts" />

/**
 * 杰克罗棋子预览管理器 包括服务器预览  
 * 优化架构设计：集成TransferGridProcessor和GridDataTransformer，提高代码可维护性
 */
class JackaroChessOrbitManager {
    public static _instance: JackaroChessOrbitManager;
    public static get instance() {
        return this._instance || (this._instance = new JackaroChessOrbitManager());
    }
    //棋子快照数据结构记录，目前只记录棋子格子位置和颜色，用于判断是否可以移动和吃子
    private chessSnapshot2 = {};
    //移动路径上的格子数据结构记录，目前只记录开始格子、结束格子、路径上都有那些棋子（用于判断是否可以移动和吃子）
    //hopeStepNum:期望的步数，stepNum:实际可以走的步数
    private pathGridData: Array<{ hopeStepNum: number, stepNum: number, fromGrid: yalla.data.jackaro.GridDataInterface, toGrid: yalla.data.jackaro.GridDataInterface, chesses: Array<string>, killChessInfo: string }> = [];

    // 优化架构设计：引入专门的处理器
    private transferProcessor = TransferGridProcessor.instance;
    private gridDataTransformer = GridDataTransformer.instance;

    private curPlayPoker: yalla.data.jackaro.Poker;
    private teamIdx = -1;
    private ignoreChessHash = {};
    // 棋子越过逻辑开关：true-连续棋子不可越过（非连续可越过），false-使用原有逻辑
    private allowJumpOverConsecutiveChess: boolean = true;
    public resetIgnoreChessId() {//清理排除棋子的hash 切换玩家的时候 保底方案
        this.ignoreChessHash = {}
    }

    /**
     * 设置棋子越过逻辑开关
     * @param enable true-连续棋子不可越过（非连续可越过），false-使用原有逻辑
     */
    public setAllowJumpOverConsecutiveChess(enable: boolean): void {
        this.allowJumpOverConsecutiveChess = enable;
    }

    /**
     * 获取棋子越过逻辑开关状态
     * @returns 当前开关状态
     */
    public getAllowJumpOverConsecutiveChess(): boolean {
        return this.allowJumpOverConsecutiveChess;
    }
    public removeIgnoreChessId(ignoreChessId: string) {
        delete this.ignoreChessHash[ignoreChessId];
    }
    public addIgnoreChessId(ignoreChessId: string) {
        this.ignoreChessHash[ignoreChessId] = ignoreChessId;
    }
    public setCurPlayPoker(poker: yalla.data.jackaro.Poker, ignoreChessId?: string): Record<string, yalla.data.jackaro.PlayResultDataInterface> | yalla.data.jackaro.ComplexPlayKingResultInterface {
        this.curPlayPoker = poker;
        if (ignoreChessId) this.addIgnoreChessId(ignoreChessId);
        let curPlayPokerMoveNums = this.getStepNumByPoker();
        let pathGridDataArr: Array<EnhancedPathGridData> = [];
        let isForward = this.isPlayPoker4() ? false : true;
        let canMoveChesses: Array<JackaroChess>;
        // let selfIdx = yalla.Global.Account.idx;
        // console.log("setCurPlayPoker canMoveChesses:" + canMoveChesses);
        if (this.teamIdx == -1) {
            this.teamIdx = JackaroGamePlayerManager.instance.getTeamIdx();
        }
        for (let i = 0; i < curPlayPokerMoveNums.length; i++) {
            let stepNum = curPlayPokerMoveNums[i];
            let actualStepNum = stepNum;
            //stepNUm = 0 说明是起飞，起飞的话，可选择的棋子都在home，实际移动步数是1步
            if (stepNum == 0) {
                canMoveChesses = this.getInHomeChess();
                actualStepNum = 1;
            } else {
                canMoveChesses = this.getCanMoveChess(isForward);
            }
            for (let index = 0; index < canMoveChesses.length; index++) {
                const chess = canMoveChesses[index];
                // if (chess.beyondIdx != selfIdx) continue;
                let basePathGridData = this.createPathGridData(chess, isForward, actualStepNum, chess.color);
                if (basePathGridData && this.canMoveChess(basePathGridData)) {
                    // 优化架构设计：使用GridDataTransformer增强路径数据
                    // 获取终点格子的LudoGrid对象
                    let endGridStation = { area: basePathGridData.toGrid.grid.color || 0, gridPosition: basePathGridData.toGrid.grid.index || 0 };
                    let endGrid: LudoGrid = JackaroBoardData.getGridByStation(endGridStation);
                    let enhancedPathGridData = this.gridDataTransformer.enhancePathGridData(basePathGridData, endGrid);
                    pathGridDataArr.push(enhancedPathGridData);
                }
            }
        }
        if (pathGridDataArr.length > 0 && this.isPlayPoker7()) {
            //再次检测出牌7 是否有些棋子不能移动
            //判断出牌7是否可以走队友棋子
            pathGridDataArr = JackaroChessOrbitSpecial.instance.filterGridPathPoker7(pathGridDataArr, this.chessSnapshot2);
        }
        let curPreviewData: Record<string, yalla.data.jackaro.PlayResultDataInterface> = {};
        let impactResult: yalla.data.jackaro.ComplexPlayKingResultInterface;
        if (pathGridDataArr.length > 0) {
            //如果是复杂玩法出牌K
            if (JackaroCardManger.instance.isComplexPlayPokerK(poker)) {
                // 优化架构设计：使用GridDataTransformer批量转换
                impactResult = this.gridDataTransformer.batchTransformToComplexPlayPokerKPreviewData(pathGridDataArr);
            } else {
                //普通出牌走子
                //优化架构设计：使用GridDataTransformer批量转换，简化逻辑
                for (let index = 0; index < pathGridDataArr.length; index++) {
                    const pathGridData = pathGridDataArr[index];
                    let stepNum = this.isPlayPoker4() ? -pathGridData.hopeStepNum : pathGridData.hopeStepNum;
                    const playResultData = this.gridDataTransformer.transformToPlayResultItemData(pathGridData);

                    if (curPreviewData[stepNum] && curPreviewData[stepNum].result) {
                        curPreviewData[stepNum].result.push(playResultData);
                    } else {
                        curPreviewData[stepNum] = { result: [playResultData] };
                    }
                }
            }
        }
        if (impactResult) {
            return impactResult;
        }
        return curPreviewData;
    }
    // 优化架构设计：createPlayResultData方法已移至GridDataTransformer.transformToPlayResultData

    // 优化架构设计：createComplexPlayPokerKPreviewData方法已移至GridDataTransformer.transformToComplexPlayPokerKPreviewData

    /**
     * 过滤出牌7不能移动的棋子
     * @param pathGridDataArr 
     * @returns 
     */
    private filterCanNotMoveChess(pathGridDataArr: Array<EnhancedPathGridData>) {
        let totalStep = 0;
        //记录玩家棋子序号对应移动步数
        let totalStepMap = {};
        for (let index = 0; index < pathGridDataArr.length; index++) {
            const pathGridData = pathGridDataArr[index];
            totalStep += pathGridData.stepNum;
            totalStepMap[pathGridData.curChessInfo] = pathGridData.stepNum;
        }
        //如果总步数小于7步，则不能移动
        if (totalStep < JackaroChessManager._poker7Step) {
            return [];
        }
        //如果存在总步数大于7步，其中任意两颗棋子之和都小于7步，则不能移动
        //遍历所有可移动棋子
        let isCanMove = false;
        for (let index = 0; index < pathGridDataArr.length; index++) {
            const pathGridData = pathGridDataArr[index];
            //遍历所有可移动棋子
            for (let j = 0; j < pathGridDataArr.length; j++) {
                const pathGridData2 = pathGridDataArr[j];
                if (pathGridData.stepNum + pathGridData2.stepNum >= JackaroChessManager._poker7Step) {
                    isCanMove = true;
                }
            }
        }
        if (!isCanMove) {
            return [];
        }
        //记录当前被吃掉的棋子，导致不能走子的次数
        //如果出牌7，可以走多颗棋子，其中一颗棋子被吃掉了，剩下的棋子不满足可以走7步，则这颗棋子不能被吃，需要把移动的目标点退后一格,再次检测是否满足剩下的棋子可以走7步，如果满足的话，移动目标退后一格，同时去掉击杀kill标志
        //存在吃子后，导致一共移动步数不是7步时，记录这种数量，如果当前只有一种，则不能出牌7
        let killChessCount = 0;
        for (let index = 0; index < pathGridDataArr.length; index++) {
            const pathGridData = pathGridDataArr[index];
            let leftStepNum = JackaroChessManager._poker7Step - pathGridData.stepNum;
            if (pathGridData.killChessInfo && pathGridData.stepNum > 1 && leftStepNum > 0 && !this.filterCanNotMoveChessByKill(pathGridData, pathGridDataArr, leftStepNum)) {
                let isCanMoveBack = false;//是否可以退后一格
                for (let key in totalStepMap) {
                    if (key == pathGridData.curChessInfo) continue;
                    if (totalStepMap[key] >= JackaroChessManager._poker7Step - pathGridData.stepNum - 1) {
                        isCanMoveBack = true;
                        break;
                    }
                }
                if (isCanMoveBack) {
                    //可以退后一格
                    pathGridData.stepNum -= 1;
                    //此时需要考虑跨区域问题
                    if (pathGridData.toGrid.grid.index == 0) {
                        pathGridData.toGrid.grid.index = 18;
                        pathGridData.toGrid.grid.color == 3 ? pathGridData.toGrid.grid.color = 0 : pathGridData.toGrid.grid.color += 1;
                    } else {
                        pathGridData.toGrid.grid.index -= 1;
                    }
                    pathGridData.killChessInfo = null;
                } else {
                    killChessCount++;
                }
            }
        }
        //如果只有两颗棋子，其中一个吃掉另外一个，导致不能走7步，则不能出牌7
        if (killChessCount == 1 && pathGridDataArr.length == 2) {
            return [];
        }
        return pathGridDataArr;
    }
    /**
     * 出牌7 排除某些棋子被吃掉后，剩下的棋子是否还可以走7步
     */
    private filterCanNotMoveChessByKill(curPathGridData: EnhancedPathGridData, pathGridDataArr: Array<EnhancedPathGridData>, leftStepNum: number) {
        let killChessInfo = curPathGridData.killChessInfo;
        let curChessInfo = curPathGridData.curChessInfo;
        for (let index = 0; index < pathGridDataArr.length; index++) {
            const pathGridData = pathGridDataArr[index];
            //当前的棋子和被吃的棋子排除掉
            if (pathGridData.curChessInfo == curChessInfo || pathGridData.curChessInfo == killChessInfo) continue;
            if (pathGridData.stepNum >= leftStepNum) {
                return true;
            }
        }
        return false;
    }
    /**
     * 获取当前移动棋子的玩家，主要为了判断是否可以移动队友棋子
     */
    private getCurMoveChessPlayerIdx() {
        let isSelfTurnEnd = JackaroChessManager.instance.isSelfChessAllInEndArea(yalla.Global.Account.idx);
        return isSelfTurnEnd ? JackaroGamePlayerManager.instance.getTeamIdx() : yalla.data.jackaro.JackaroUserService.instance.getCurUserId();
    }
    /**
     * 生成出牌7 可以走队友棋子预览
     */
    public createTeamChessPathGridData(pathGridDataArr: Array<EnhancedPathGridData>) {
        //如果是出牌7，走其中一颗棋子到终点，则可以选择队友棋子
        let curMoveChessPlayerIdx = this.getCurMoveChessPlayerIdx();
        let selfColor = JackaroGamePlayerManager.instance.getColorByIdx(curMoveChessPlayerIdx);
        let isOnlyOneChessToEndArea = this.isOnlyOneChessToEndArea(curMoveChessPlayerIdx, selfColor);
        if (isOnlyOneChessToEndArea) {
            let chessToEndAreaStepNum = 0;
            for (let index = 0; index < pathGridDataArr.length; index++) {
                const pathGridData = pathGridDataArr[index];
                let toStation = { area: pathGridData.toGrid.grid.color, gridPosition: pathGridData.toGrid.grid.index };
                let toLudoGrid: LudoGrid = JackaroBoardData.getGridByStation(toStation);
                if (toLudoGrid && toLudoGrid.inEndArea) {
                    if (this.isQuickBasicMode() && pathGridData.toGrid.grid.index == -4) {
                        if (chessToEndAreaStepNum < pathGridData.stepNum) {
                            chessToEndAreaStepNum = pathGridData.stepNum;
                        }
                    }
                }
            }
            let teamIdx = JackaroGamePlayerManager.instance.getTeamIdxByOther(curMoveChessPlayerIdx);
            if (chessToEndAreaStepNum && !JackaroChessManager.instance.isSelfChessAllInEndArea(teamIdx)) {
                //剩下的步数可以走队友棋子
                // for (let key in this.chessSnapshot) {
                //     let chess = JackaroChessManager.instance.getChessById(key);
                // }
                JackaroChessManager.instance.each(chess => {
                    if (!chess.wayGrid.inHome && chess.beyondIdx == teamIdx) {
                        let leftStepNum = JackaroChessManager._poker7Step - chessToEndAreaStepNum;
                        let pathGridData = this.createPathGridData(chess, this.isPlayPoker4() ? false : true, leftStepNum, chess.color, true);
                        if (pathGridData && this.canMoveChess(pathGridData, true)) {
                            pathGridDataArr.push(pathGridData);
                        }
                    }
                })
            }
        }
    }
    /**
     * 是否是基础的快速玩法，基础的快速玩法，只要一个棋子进终点后，就可以走队友棋子
     * @returns 
     */
    private isQuickBasicMode() {
        let room = yalla.data.jackaro.JackaroUserService.instance.room;
        let isQuickMode = room && room.isQuickBasicMode();
        return isQuickMode;
    }
    /**
     * 是不是只剩下一颗棋子进终点就可以完赛
     */
    private isOnlyOneChessToEndArea(idx: number, color: number) {
        let isQuickMode = this.isQuickBasicMode();
        if (isQuickMode) {
            if (!JackaroChessManager.instance.isFastModeChessInEndArea(idx)) {
                return true;
            }
        } else {
            let curChessNum = 0;
            for (let i = -4; i < 0; i++) {
                let curStation = { area: color, gridPosition: i };
                let curGrid: LudoGrid = JackaroBoardData.getGridByStation(curStation);
                let ch = JackaroChessManager.instance.getChessByGrid(curGrid);
                if (!ch) {
                    break;
                } else {
                    curChessNum++;
                }
            }
            return curChessNum == 3;
        }
        return false;
    }

    /**
     * 是否可以移动（越过敌方或者自己一颗棋子，只要不是吃子，都不可以移动）
     */
    public canMoveChess(pathGridData: { curChessInfo: string, hopeStepNum: number, stepNum: number, fromGrid: yalla.data.jackaro.GridDataInterface, toGrid: yalla.data.jackaro.GridDataInterface, chesses: Array<string>, killChessInfo: string }, isMoveTeamChess: boolean = false) {
        if (pathGridData.hopeStepNum == pathGridData.stepNum) {
            return true;
        } else if (this.isPlayPoker7() && !isMoveTeamChess) {
            return true;
        }
        return false;
    }

    public refreshChessSnapshot() {
        this.chessSnapshot2 = {};//清空棋子快照数据结构记录
        JackaroChessManager.instance.each((chess: JackaroChess) => {
            if (chess && chess.goalGrid && !this.ignoreChessHash[chess.idKey]) {
                var goalGrid = chess.goalGrid
                this.chessSnapshot2[goalGrid.gridName] = {
                    idx: chess.beyondIdx,
                    chessId: chess.id,
                    chessColor: chess.color,
                    index: goalGrid.gridPosition,
                    gridColor: goalGrid.color
                };
            }
        })

    }
    private getCanMoveChess(isForward: boolean) {
        this.refreshChessSnapshot();

        let canMoveChess: Array<JackaroChess> = [];
        if (this.isPlayPoker5()) {
            JackaroChessManager.instance.each((chess: JackaroChess) => {
                if (chess && chess.goalGrid) {
                    let grid = chess.goalGrid;
                    if (!grid.inHome && !grid.inEndArea && !this.isChessInSelfTakeOffArea(chess)) {
                        !this.ignoreChessHash[chess.idKey] && canMoveChess.push(chess);
                    }
                }
            })
        } else {
            let curMoveChessPlayerIdx = this.getCurMoveChessPlayerIdx();
            JackaroChessManager.instance.each((chess: JackaroChess) => {
                if (chess && chess.goalGrid) {
                    let grid = chess.goalGrid;
                    // console.log(JSON.stringify(grid) + "====可以移动的棋子====ignoreChessId=" + ignoreChessId + " " + chess.idKey);
                    // console.log(!grid.inHome + " " + chess.beyondIdx + " " + curMoveChessPlayerIdx);
                    if (!grid.inHome && chess.beyondIdx == curMoveChessPlayerIdx) {
                        //出牌4 后退，不能从终点区域后退
                        if (!isForward && chess.wayGrid.inEndArea) return;
                        !this.ignoreChessHash[chess.idKey] && canMoveChess.push(chess);
                    }
                }
            })
        }
        return canMoveChess;
    }
    /**
     * 获取在基地的棋子
     */
    private getInHomeChess(): Array<JackaroChess> {
        this.refreshChessSnapshot();
        let canMoveChess: Array<JackaroChess> = [];
        let curMoveChessPlayerIdx = this.getCurMoveChessPlayerIdx();
        JackaroChessManager.instance.each((chess: JackaroChess) => {
            if (chess && chess.goalGrid) {
                let grid = chess.goalGrid;
                if (grid.inHome && chess.beyondIdx == curMoveChessPlayerIdx) {
                    !this.ignoreChessHash[chess.idKey] && canMoveChess.push(chess);
                }
            }
        })

        return canMoveChess;
    }
    /**
     * 是否棋子在自己的起飞点,排除自己的棋子在起飞点，主要用于出牌 5
     * @param chess 
     * @returns 
     */
    private isChessInSelfTakeOffArea(chess: JackaroChess): boolean {
        let curMoveChessPlayerIdx = this.getCurMoveChessPlayerIdx();
        return curMoveChessPlayerIdx != chess.beyondIdx && JackaroBoardData.getGridName(chess.wayGrid.station) == (chess.color + "_2");
    }
    public createPathGridData(moveChess: JackaroChess, forward: boolean, stepNum: number, chessColor: number, isMoveTeamChess: boolean = false) {
        let tempPathGridData: { curChessInfo: string, hopeStepNum: number, stepNum: number, fromGrid: yalla.data.jackaro.GridDataInterface, toGrid: yalla.data.jackaro.GridDataInterface, chesses: Array<string>, killChessInfo: string };
        if (!moveChess) return tempPathGridData;
        let curChessInfo = moveChess.beyondIdx + "_" + moveChess.id;
        let curWayGrid = moveChess.wayGrid;
        let startGrid = moveChess.wayGrid;
        let endGrid = curWayGrid;
        // let endGrid;
        //当前路径上的所有棋子
        let curPathChessInfo: Array<string> = [];
        //他人的棋子数据
        let otherChessInfo: Array<string> = [];
        let curStepNum = 0;
        //是否可以击杀
        //自己的棋子一颗都不能越过，别人的棋子可以越过1颗
        // let isMoveBreak = false;
        //是否后退一格,因为有些步数移动到此位置，不符合击杀条件，需要后退一格
        // let isMoveBackOneGrid = false;
        let curKillChessInfo: string;
        // console.log("createPathGridData stepNum:" + stepNum + "curWayGrid:" + curWayGrid.myName);


        var isComplexK = JackaroCardManger.instance.isComplexPlayPokerK(this.curPlayPoker);
        var isPlayPoker4 = this.isPlayPoker4();
        var isPlayPoker7 = this.isPlayPoker7();
        var hasChessGrids = [];


        for (let index = 0; index < stepNum; index++) {
            if (forward) {
                if (curWayGrid && curWayGrid.inCorner) {
                    if (curWayGrid.color == chessColor) {
                        //自己的棋子可以进终点或者队友的棋子可以进终点
                        if (moveChess.beyondIdx == yalla.Global.Account.idx || (moveChess.beyondIdx == this.teamIdx)) {
                            curWayGrid = JackaroBoardData.getGridByName(`${chessColor}_-1`);
                        } else {
                            curWayGrid = JackaroBoardData.getGridByName(curWayGrid.nextName);
                        }
                    } else {
                        curWayGrid = JackaroBoardData.getGridByName(curWayGrid.nextName);
                    }
                } else {
                    curWayGrid = JackaroBoardData.getGridByName(curWayGrid.nextName);
                }
            } else {
                curWayGrid = JackaroBoardData.getGridByName(curWayGrid.lastName);
            }
            if (!curWayGrid) {
                break;
            }
            endGrid = curWayGrid;
            curStepNum = index + 1;
            //检查当前位置有棋子存在
            var gridName = curWayGrid.gridName;
            var gridInfo = this.getGridInfo(gridName);


            if (gridInfo) {//当前位置有棋子
                //TODO：：击杀的key必须要路径的key 拼法一致，$符号会多出反斜杠
                var key = gridInfo.idx + "_" + gridInfo.chessId;//`${gridInfo.idx}"_"${gridInfo.chessId}`
                hasChessGrids.push(key);
                //如果该棋子在自己任何终点区域，是不可以击杀的 //如果存在棋子在自己的起飞点，此时不能越过和击杀
                if (curWayGrid.gridPosition < 0 || (curWayGrid.color == gridInfo.chessColor && curWayGrid.gridPosition == 2)) {
                    if (isPlayPoker7) {//不能击杀
                        // isMoveBackOneGrid = true;
                        //todo 7需要回滚一格 不能击杀
                        curStepNum = index;
                        endGrid = JackaroBoardData.getGridByName(curWayGrid.lastName);
                        break;
                    } else {
                        return false
                    }
                }

                // {{ YUM: [Fix] - 修复复杂 K 牌终点击杀检测逻辑 }}
                if (index == stepNum - 1) {
                    curKillChessInfo = key;
                    curPathChessInfo.push(key)
                    break;//最后一格有棋子 击杀
                }

                if (!isComplexK) {//排除复杂玩法K的情况
                    let nextGridName = forward ? curWayGrid.nextName : curWayGrid.lastName;
                    if (this.chessSnapshot2[nextGridName]) {//下一格有棋子
                        if (isPlayPoker7) {
                            //出牌7，可以击杀棋子
                            if (!isMoveTeamChess) {
                                curKillChessInfo = key;
                            }
                            break;
                        } else {
                            return false;
                        }
                    }
                    if (chessColor == gridInfo.chessColor) {
                        if (isPlayPoker7) {//可以击杀curKillChessInfo
                            curKillChessInfo = key;
                            break;
                        } else {
                            return false
                        }
                    }

                    // 使用新的棋子越过逻辑处理方法（支持开关控制）
                    // 这里处理的是原来第463-471行的逻辑：当路径上有多个棋子时的处理
                    const jumpResult = this.handleChessJumpLogic(hasChessGrids, isPlayPoker7, key, curWayGrid, forward);
                    if (!jumpResult.canPass) {
                        if (jumpResult.killChessInfo) {
                            curKillChessInfo = jumpResult.killChessInfo;
                            break;
                        } else {
                            return false;
                        }
                    }
                    /**
                     * 1. 如果有棋子在拐点处,且移动棋子颜色和格子上的棋子颜色一致，自己的棋子行走时，此时形成障碍，不可越过
                     * 2. 拐点位置的棋子颜色和自己不一样，但是下一个格子的颜色和自己棋子颜色一样，也是形成路障
                     */
                    if (!isPlayPoker4 && curWayGrid.inCorner) {
                        // console.log(moveChess.color + "===移动棋子颜色===" + gridInfo.chessColor);
                        if (moveChess.color != gridInfo.chessColor) {
                            var nextGrid: LudoGrid = JackaroBoardData.getGridByName(curWayGrid.nextName);
                            // console.log(moveChess.color + "====拐点是否是路障条件判定====" + JSON.stringify(nextGrid));
                            if (nextGrid && nextGrid.color == moveChess.color) {
                                if (isPlayPoker7) {
                                    curKillChessInfo = key;
                                    break;
                                } else {
                                    return false
                                }
                            }
                        } else {
                            if (isPlayPoker7) {
                                curKillChessInfo = key;
                                break;
                            } else {
                                return false
                            }
                        }

                    }
                }

                // {{ YUM: [Fix] - 复杂 K 牌也需要记录路径上的棋子信息 }}
                curPathChessInfo.push(key);
            }
        }
        let fromGrid = { grid: { index: startGrid.gridPosition, color: startGrid.color, type: 0 }, chess: { idx: moveChess.beyondIdx, order: moveChess.id, color: moveChess.color } };
        let toGrid = { grid: { index: endGrid.gridPosition, color: endGrid.color, type: 0 }, chess: { idx: moveChess.beyondIdx, order: moveChess.id, color: moveChess.color } };

        // 优化架构设计：传送点逻辑已移至TransferGridProcessor处理，此处只处理基础路径逻辑

        //stepNum 真实的移动步数 hopeStepNum 希望的移动步数
        tempPathGridData = { curChessInfo: curChessInfo, hopeStepNum: stepNum, stepNum: curStepNum, fromGrid: fromGrid, toGrid: toGrid, chesses: curPathChessInfo, killChessInfo: curKillChessInfo };
        return tempPathGridData;
    }
    /**
     * getGriddInfo
     */
    public getGridInfo(gridName: string) {
        return this.chessSnapshot2 ? this.chessSnapshot2[gridName] : null;
    }
    // /**
    //  * 是否棋子属于自己或者队友
    //  * @param chess 
    //  * @returns 
    //  */
    // private isChessBeyondMeOrTeam(chess: JackaroChess): boolean {
    //     let myIdx = yalla.Global.Account.idx;
    //     let chessIdx = chess.beyondIdx;
    //     return chessIdx == myIdx || chessIdx == this.teamIdx;
    // }
    /**
     * 是否是出牌5
     */
    private isPlayPoker5(): boolean {
        return JackaroCardManger.instance.isPoker5(this.curPlayPoker);
    }
    /**
     * 是否是出牌7
     */
    private isPlayPoker7(): boolean {
        return JackaroCardManger.instance.isPoker7(this.curPlayPoker);
    }
    /**
     * 是否是出牌4
     */
    private isPlayPoker4(): boolean {
        return JackaroCardManger.instance.isPoker4(this.curPlayPoker);
    }

    /**
     * 检查棋子是否连续排列
     * @param hasChessGrids 路径上已有的棋子位置数组
     * @param curWayGrid 当前格子
     * @param forward 是否前进方向
     * @returns 是否为连续排列
     */
    private areChessConsecutive(hasChessGrids: Array<string>, curWayGrid: any, forward: boolean): boolean {
        if (hasChessGrids.length < 2) return false;

        // 安全检查：确保curWayGrid存在
        if (!curWayGrid) return false;

        // 当第二颗棋子加进来时，需要检查当前棋子的前一格是否存在棋子
        // 判断连续性：检查当前格子的上一格（向后一格）是否有棋子
        let prevGridName: string;
        if (forward) {
            // 前进时，检查上一格（lastName）
            prevGridName = curWayGrid.lastName;
        } else {
            // 后退时，检查上一格（nextName）
            prevGridName = curWayGrid.nextName;
        }

        // 安全检查：确保prevGridName存在
        if (!prevGridName) return false;

        let prevWayGrid = JackaroBoardData.getGridByName(prevGridName);
        if (prevWayGrid) {
            // 检查上一个格子是否有棋子
            let prevGridInfo = this.getGridInfo(prevWayGrid.gridName);
            if (prevGridInfo) {
                // 上一个格子有棋子，说明棋子是连续排列的
                return true;
            }
        }

        // 如果上一格没有棋子，则不是连续排列
        return false;
    }

    /**
     * 处理棋子越过逻辑（根据开关状态执行不同逻辑）
     * @param hasChessGrids 路径上已有的棋子位置数组
     * @param isPlayPoker7 是否出牌7
     * @param key 当前棋子的key
     * @param curWayGrid 当前格子
     * @param forward 是否前进方向
     * @returns {canPass: boolean, killChessInfo?: string} 是否可以通过和击杀信息
     */
    private handleChessJumpLogic(hasChessGrids: Array<string>, isPlayPoker7: boolean, key: string, curWayGrid: any, forward: boolean): { canPass: boolean, killChessInfo?: string } {
        // 边界条件：如果路径上棋子数量<=1，直接通过
        if (hasChessGrids.length <= 1) {
            return { canPass: true };
        }

        // 安全检查：确保必要参数存在
        if (!curWayGrid || !key) {
            return { canPass: false };
        }

        // 开关关闭时，使用原有逻辑
        if (!this.allowJumpOverConsecutiveChess) {
            // 原有逻辑：出现第二个棋子不可越过，但是正好在终点可以击杀
            if (isPlayPoker7) {
                return { canPass: false, killChessInfo: key };
            } else {
                return { canPass: false };
            }
        }

        // 开关打开时，新逻辑：检查棋子是否连续排列
        const areConsecutive = this.areChessConsecutive(hasChessGrids, curWayGrid, forward);
        if (areConsecutive) {
            // 连续排列的棋子不可以越过
            if (isPlayPoker7) {
                let prevGridInfo = this.getGridInfo(curWayGrid.lastName);
                let lastKey = prevGridInfo.idx + "_" + prevGridInfo.chessId;
                return { canPass: false, killChessInfo: lastKey };
            } else {
                return { canPass: false }; // 新逻辑：连续棋子不可越过
            }
        } else {
            // 非连续排列，可以越过
            if (isPlayPoker7) {
                return { canPass: true, killChessInfo: key };
            } else {
                return { canPass: true }; // 新逻辑：非连续棋子可以越过
            }
        }
    }
    /**
     * 根据牌型获取可以移动的步数
     * @returns 
     */
    private getStepNumByPoker(): number[] {
        let room = yalla.data.jackaro.JackaroUserService.instance.room;
        let isComplexMode = JackaroCardManger.instance.isComplexPlay(this.curPlayPoker);
        if (JackaroCardManger.instance.isPokerA(this.curPlayPoker)) {
            if (isComplexMode) {
                return [0, 1];
            } else {
                return [0, 1, 11];
            }
        }
        else if (JackaroCardManger.instance.isPoker2(this.curPlayPoker)) {
            let is1V1PlayMode = JackaroCardManger.instance.isComplex1V1Play(this.curPlayPoker);
            if (is1V1PlayMode) {
                return [0, 2];
            } else {
                return [2];
            }
        } else if (JackaroCardManger.instance.isPoker3(this.curPlayPoker)) {
            return [3];
        } else if (JackaroCardManger.instance.isPoker4(this.curPlayPoker)) {
            return [4];
        } else if (JackaroCardManger.instance.isPoker5(this.curPlayPoker)) {
            return [5];
        } else if (JackaroCardManger.instance.isPoker6(this.curPlayPoker)) {
            return [6];
        } else if (JackaroCardManger.instance.isPoker7(this.curPlayPoker)) {
            return [7];
        } else if (JackaroCardManger.instance.isPoker8(this.curPlayPoker)) {
            return [8];
        } else if (JackaroCardManger.instance.isPoker9(this.curPlayPoker)) {
            return [9];
        } else if (JackaroCardManger.instance.isPoker10(this.curPlayPoker)) {
            return [10];
        } else if (JackaroCardManger.instance.isPokerJ(this.curPlayPoker)) {
            if (isComplexMode) {
                if (this.curPlayPoker == yalla.data.jackaro.Poker.SPADE_JACK || this.curPlayPoker == yalla.data.jackaro.Poker.CLUB_JACK) {
                    return [];
                } else {
                    return [11];
                }
            } else {
                return [];
            }
        }
        else if (JackaroCardManger.instance.isPokerQ(this.curPlayPoker)) {
            if (isComplexMode && (this.curPlayPoker == yalla.data.jackaro.Poker.HEART_QUEEN || this.curPlayPoker == yalla.data.jackaro.Poker.DIAMOND_QUEEN)) {
                return [];
            } else {
                return [12];
            }
        }
        else if (JackaroCardManger.instance.isPokerK(this.curPlayPoker)) {
            if (isComplexMode) {
                return [0, 13];
            } else {
                return [0];
            }
        }
    }
    public clear() {
        this.teamIdx = -1;
        this.curPlayPoker = null;
        // 清理依赖的单例实例
        GridDataTransformer.clearInstance();
        JackaroChessOrbitManager._instance = null;
    }
}
