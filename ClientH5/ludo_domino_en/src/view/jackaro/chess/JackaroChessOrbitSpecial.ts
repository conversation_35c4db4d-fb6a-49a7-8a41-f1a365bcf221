
/**
 * 杰克罗棋子预览 特殊牌  7
 */
class JackaroChessOrbitSpecial {
    public static _instance: JackaroChessOrbitSpecial;
    public static get instance() {
        return this._instance || (this._instance = new JackaroChessOrbitSpecial());
    }

    /**
     * 过滤出牌7不能移动的棋子
     * @param pathGridDataArr 
     * @returns
     * 1. 优先检测能到终点的优先路径 且需要考虑队友棋子预览路径
     *    >1 有终点棋子A，在加上另一个棋子B（可能自己、可能队友），总步数=7，刚好B的击杀棋子是A, 其实也是有效路径，需要先走A，再走B，这种情况服务端、客户端都有问题
     * 2. 所有路径累计步数>=7,但是任意两颗棋子之和都小于7步，则不能移动
     * 3. 击杀存在倒退格子，也需要考虑组合棋子是否满足7步；
     *    可以走多颗棋子，其中一颗棋子被吃掉了，剩下的棋子不满足可以走7步，则这颗棋子不能被吃，需要把移动的目标点退后一格,再次检测是否满足剩下的棋子可以走7步，如果满足的话，移动目标退后一格，同时去掉击杀kill标志
     *    存在吃子后，导致一共移动步数不是7步时，记录这种数量，如果当前只有一种，则不能出牌7
     * 4. 如果只有两颗棋子，其中一个吃掉另外一个，导致不能走7步，则不能出牌7
     * 5. 如果有自己棋子到达终点，但是队友棋子击杀达到终点的棋子，需要把击杀数据清除
     */
    public filterGridPathPoker7(pathGridDataArr: Array<{ curChessInfo: string, hopeStepNum: number, stepNum: number, fromGrid: yalla.data.jackaro.GridDataInterface, toGrid: yalla.data.jackaro.GridDataInterface, chesses: Array<string>, killChessInfo: string }>, chessSnapshot) {
        var totalStep = 0;
        //记录玩家棋子序号对应移动步数
        var totalStepMap = {};
        var tempPathGridDataHash = {};
        var tempPathGridDataArr: Array<{ curChessInfo: string, hopeStepNum: number, stepNum: number, fromGrid: yalla.data.jackaro.GridDataInterface, toGrid: yalla.data.jackaro.GridDataInterface, chesses: Array<string>, killChessInfo: string }> = [];
        var moveEndPathData = null;
        var endAreaChessNums = JackaroChessManager.instance.getChessInEndAreaNum(yalla.Global.Account.idx);
        // console.log("=普通模式已经进终点的棋子=endAreaChessNums:"+endAreaChessNums);

        let isCanMove = false;
        for (let index = 0; index < pathGridDataArr.length; index++) {
            var pathGridData = pathGridDataArr[index];
            // console.log("===每条路径==="+JSON.stringify(pathGridData));
            totalStep += pathGridData.stepNum;
            totalStepMap[pathGridData.curChessInfo] = pathGridData.stepNum;
            // 情况1，记录能到达终点的路径
            var canMoveEnd = this.getLastChessMoveEnd(pathGridData, endAreaChessNums);
            // console.log(canMoveEnd + "==棋子能否移动到终点===");
            if (canMoveEnd) {
                tempPathGridDataHash[pathGridData.curChessInfo] = pathGridData;
                moveEndPathData = pathGridData;
            }
            // if (pathGridData.toGrid && pathGridData.toGrid.grid.index == -4) {
            //     tempPathGridDataHash[pathGridData.curChessInfo] = pathGridData;
            //     moveEndPathData = pathGridData;
            // }
            //遍历所有可移动棋子,组合棋子移动步数是否 >= 7
            for (let j = 0; j < pathGridDataArr.length; j++) {
                const pathGridData2 = pathGridDataArr[j];
                if (pathGridData.stepNum + pathGridData2.stepNum >= JackaroChessManager._poker7Step) {
                    isCanMove = true;
                    tempPathGridDataHash[pathGridData.curChessInfo] = pathGridData;
                    tempPathGridDataHash[pathGridData2.curChessInfo] = pathGridData2;
                }
            }
        }

        // console.log(isCanMove + "==有没有终点的棋子==" + !!moveEndPathData);
        // console.log("====tempGride:" + JSON.stringify(tempPathGridDataHash));
        //TODO:: 情况2
        if (!isCanMove && !moveEndPathData) {
            return [];
        }

        //TODO::情况3,击杀后退
        var killChessCount = 0;
        for (var key in tempPathGridDataHash) {
            const pGridData = tempPathGridDataHash[key];
            tempPathGridDataArr.push(pGridData);
            var canKill = this.checkBackStepByKill(pGridData, totalStepMap, pathGridDataArr);
            // console.log(pathGridDataArr.length+"====canKill===="+canKill);
            if (canKill) killChessCount += 1;
            tempPathGridDataHash[key] = pGridData;
        }

        // 是否有最后一个棋子可以到达终点的棋子，考虑队友的棋子（快速模式一颗棋子，其他模式三个棋子进终点，还剩最后一个）
        var teamPathArr = this.checkLastChessMoveEnd(moveEndPathData, chessSnapshot, totalStep);
        if (teamPathArr && teamPathArr.length > 0) tempPathGridDataArr = teamPathArr.concat(tempPathGridDataArr);

        totalStep = this.getTotalStep(tempPathGridDataArr);
        // console.log(totalStep+" "+killChessCount+ "--" + tempPathGridDataArr.length+"====tempGride:"+JSON.stringify(tempPathGridDataArr));
        //TODO:: 情况2，加入队友的棋子路径，如果没有筛选出队友的路径，再次验证总步数
        if (totalStep < JackaroChessManager._poker7Step) {
            return [];
        }
        //TODO:: 情况4
        if (killChessCount == 1 && tempPathGridDataArr.length == 2) {
            return [];
        }

        // console.log("[JackaroChessOrbitManager.filterCanNotMoveChess] 111 tempPathGridDataArr:" + JSON.stringify(tempPathGridDataArr));
        tempPathGridDataArr = this.checkTotalStepNum(tempPathGridDataArr);
        // console.log("[JackaroChessOrbitManager.filterCanNotMoveChess] 222 tempPathGridDataArr:" + JSON.stringify(tempPathGridDataArr));
        return tempPathGridDataArr;
    }

    /** 最后校验组合步数 */
    private checkTotalStepNum(tempPathGridDataArr) {
        let arr = [];
        for (let index = 0; index < tempPathGridDataArr.length; index++) {
            var pathGridData = tempPathGridDataArr[index];
            //遍历所有可移动棋子,组合棋子移动步数是否 >= 7
            for (let j = 0; j < tempPathGridDataArr.length; j++) {
                const pathGridData2 = tempPathGridDataArr[j];
                if (pathGridData.stepNum + pathGridData2.stepNum >= JackaroChessManager._poker7Step) {
                    if (arr.indexOf(pathGridData) < 0) arr.push(pathGridData);
                    if (arr.indexOf(pathGridData2) < 0) arr.push(pathGridData2);
                }
            }
        }
        return arr;
    }

    /**
     * 检测快速模式下，有可以移动到终点的棋子（PS:其他模式也要考虑）
     * 当前路径的总步数不足7不，只考虑能进终点的路径
     */
    private checkLastChessMoveEnd(moveEndPathData, chessSnapshot, totalStep) {
        // 有可以到达终点的棋子，且快速模式，考虑队友的棋子
        var pathGridDataArr = [];
        // if (moveEndPathData && yalla.data.jackaro.JackaroUserService.instance.room.isQuickMode()) {
        if (moveEndPathData) {
            pathGridDataArr = this.addTeamPathGridData(moveEndPathData.stepNum, chessSnapshot);
            this.checkMoveEndInKill(moveEndPathData, pathGridDataArr);
            // console.log("===操作队友棋子的路径======" + JSON.stringify(pathGridDataArr));
            if (pathGridDataArr.length > 0 && totalStep < JackaroChessManager._poker7Step) {
                pathGridDataArr.unshift(moveEndPathData);
            }
        }
        return pathGridDataArr;
    }

    /**情况5 队友棋子的击杀棋子是我的终点棋子，需要把击杀数据清理 */
    private checkMoveEndInKill(moveEndPathData, teamPathGridDataArr: Array<any>) {
        for (var i = 0; i < teamPathGridDataArr.length; i++) {
            var pGridData = teamPathGridDataArr[i];
            // console.log(moveEndPathData.curChessInfo+"===队友棋子能否击杀======" + JSON.stringify(pGridData));
            if (pGridData.killChessInfo == moveEndPathData.curChessInfo) {
                // pGridData.killChessInfo = null;
                //TODO::改成和服务端一致，倒退一步；且进终点的棋子步数和倒退一步总步数肯定小于7，不成立
                teamPathGridDataArr.splice(i, 1);
            }
        }
        // console.log("==队友棋子列表："+JSON.stringify(teamPathGridDataArr));
    }

    /**快速模式，且自己有一个棋子可以进终点，可以考虑队友棋子 */
    private addTeamPathGridData(chessToEndAreaStepNum, chessSnapshot) {
        let curMoveChessPlayerIdx = this.getCurMoveChessPlayerIdx();
        let teamIdx = JackaroGamePlayerManager.instance.getTeamIdxByOther(curMoveChessPlayerIdx);
        let teamPathGridDataArr = [];
        if (chessToEndAreaStepNum && !JackaroChessManager.instance.isSelfChessAllInEndArea(teamIdx)) {
            //剩下的步数可以走队友棋子
            for (let k in chessSnapshot) {
                let key = chessSnapshot[k].idx + "_" + chessSnapshot[k].chessId;
                let chess = JackaroChessManager.instance.getChessById(key);
                if (!chess.wayGrid.inHome && chess.beyondIdx == teamIdx) {
                    let leftStepNum = JackaroChessManager._poker7Step - chessToEndAreaStepNum;
                    let pathGridData = JackaroChessOrbitManager.instance.createPathGridData(chess, true, leftStepNum, chess.color, true);
                    if (pathGridData && JackaroChessOrbitManager.instance.canMoveChess(pathGridData, true)) {
                        teamPathGridDataArr.push(pathGridData);
                    }
                }
            }
        }
        return teamPathGridDataArr;
    }

    /**击杀存在倒退格子，也需要考虑组合棋子是否满足7步；
     *可以走多颗棋子，其中一颗棋子被吃掉了，剩下的棋子不满足可以走7步，则这颗棋子不能被吃，需要把移动的目标点退后一格,
     再次检测是否满足剩下的棋子可以走7步，如果满足的话，移动目标退后一格，同时去掉击杀kill标志
     */
    private checkBackStepByKill(pathGridData, totalStepMap, pathGridDataArr) {
        var canKill = false;
        var leftStepNum = JackaroChessManager._poker7Step - pathGridData.stepNum;
        var moveChessByKill = !this.filterCanNotMoveChessByKill(pathGridData, pathGridDataArr, leftStepNum);
        // console.log(pathGridData.stepNum + ":" + leftStepNum + "==checkBackStepByKill==moveChessByKill=" + moveChessByKill);
        if (pathGridData.killChessInfo && leftStepNum > 0 && moveChessByKill) {
            let isCanMoveBack = false;//是否可以退后一格
            for (let key in totalStepMap) {
                if (key == pathGridData.curChessInfo) continue;
                if (totalStepMap[key] >= JackaroChessManager._poker7Step - pathGridData.stepNum - 1) {
                    isCanMoveBack = true;
                    break;
                }
            }
            // console.log("===isCanMoveBack===="+isCanMoveBack)
            if (isCanMoveBack) {
                //可以退后一格
                this.backOneStep(pathGridData);
            } else {
                canKill = true;
            }
        }
        return canKill;
    }
    /**倒退一个 */
    private backOneStep(pathGridData) {
        pathGridData.stepNum -= 1;
        //此时需要考虑跨区域问题
        if (pathGridData.toGrid.grid.index == 0) {
            pathGridData.toGrid.grid.index = 18;
            pathGridData.toGrid.grid.color == 3 ? pathGridData.toGrid.grid.color = 0 : pathGridData.toGrid.grid.color += 1;
        } else {
            pathGridData.toGrid.grid.index -= 1;
        }
        pathGridData.killChessInfo = null;
    }

    /**
     * 出牌7 排除某些棋子被吃掉后，剩下的棋子是否还可以走7步
     */
    private filterCanNotMoveChessByKill(curPathGridData: { curChessInfo: string, hopeStepNum: number, stepNum: number, fromGrid: yalla.data.jackaro.GridDataInterface, toGrid: yalla.data.jackaro.GridDataInterface, chesses: Array<string>, killChessInfo: string },
        pathGridDataArr: Array<{ curChessInfo: string, hopeStepNum: number, stepNum: number, fromGrid: yalla.data.jackaro.GridDataInterface, toGrid: yalla.data.jackaro.GridDataInterface, chesses: Array<string>, killChessInfo: string }>,
        leftStepNum: number) {
        let killChessInfo = curPathGridData.killChessInfo;
        let curChessInfo = curPathGridData.curChessInfo;
        let pathLen = pathGridDataArr.length;
        for (let index = 0; index < pathLen; index++) {
            const pathGridData = pathGridDataArr[index];
            //当前的棋子和被吃的棋子排除掉
            if (pathGridData.curChessInfo == curChessInfo || pathGridData.curChessInfo == killChessInfo) {
                continue;
            }
            if (pathGridData.stepNum >= leftStepNum) {
                return true;
            }
        }
        return false;
    }
    /**是否有最后一个棋子进终点就可以完赛
     * 快速模式只要一个棋子
     * 普通模式三个棋子已经在终点，最后一个棋子到-1的位置即可
    */
    private getLastChessMoveEnd(pathGridData: { curChessInfo: string, hopeStepNum: number, stepNum: number, fromGrid: yalla.data.jackaro.GridDataInterface, toGrid: yalla.data.jackaro.GridDataInterface, chesses: Array<string>, killChessInfo: string }, endAreaChessNums = 0) {
        if (pathGridData.toGrid) {
            if (pathGridData.toGrid.grid.index == -4 && yalla.data.jackaro.JackaroUserService.instance.room.isQuickBasicMode()) {
                return true;
            }
            if (endAreaChessNums == 3 && pathGridData.toGrid.grid.index == -1) {
                return true;
            }
        }
        return false;
    }

    private getTotalStep(pathGridDataArr) {
        let totalStep = 0;
        for (let index = 0; index < pathGridDataArr.length; index++) {
            const pathGridData = pathGridDataArr[index];
            totalStep += pathGridData.stepNum;
        }
        return totalStep;
    }


    /**
     * 获取当前移动棋子的玩家，主要为了判断是否可以移动队友棋子
     */
    private getCurMoveChessPlayerIdx() {
        let isSelfTurnEnd = JackaroChessManager.instance.isSelfChessAllInEndArea(yalla.Global.Account.idx);
        return isSelfTurnEnd ? JackaroGamePlayerManager.instance.getTeamIdx() : yalla.data.jackaro.JackaroUserService.instance.getCurUserId();
    }


    public clear() {
        JackaroChessOrbitSpecial._instance = null;
    }
}
