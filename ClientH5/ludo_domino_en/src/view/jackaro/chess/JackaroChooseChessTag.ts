/**
 * 选棋子序号
 */
class JackaroChooseChessTag extends ui.jackaro.sub.chooseChessTagUI {
    private tagWidth: number = 84;
    constructor() {
        super();
    }
    private pool: Array<Laya.Box> = [];
    show(chesses: Array<JackaroChess>, callBack: Function) {
        if (chesses && chesses.length < 2)
            return;
        var len = chesses.length;
        var firstCh = chesses[0], lastCh = chesses[len - 1];
        var firstX = firstCh.x, firstY = firstCh.y, lastX = lastCh.x, lastY = lastCh.y;
        var direction = firstX - lastX;
        //棋子竖向排列
        if (direction == 0) {
            //屏幕右上-1 左下1
            direction = firstY < 400 ? -1 : 1
        }
        var posChesss = firstCh;
        if (direction < 0) {
            for (let i = 0; i < len; i++) {
                let jackaroChess: JackaroChess = chesses[i];
                if (jackaroChess.y < posChesss.y) {
                    posChesss = jackaroChess;
                }
                yalla.Debug.logMore("JackaroChooseChessTag show jackaroChess.idx:", jackaroChess.beyondIdx, "chess id:", jackaroChess.id);
                let isDividingLineShow = !(i == len - 1);
                var btn: ui.jackaro.sub.chessTagUI = this.createChooseBtn(jackaroChess.color, i + 1, isDividingLineShow);
                btn.offAll();
                btn.x = i;
                this.nums_box.addChild(btn);
                btn.off("click", this, this.onClickBtn);
                btn.on("click", this, this.onClickBtn, [jackaroChess, callBack]);
            }
        } else {
            for (let i = len - 1; i >= 0; i--) {
                let jackaroChess: JackaroChess = chesses[i];
                if (jackaroChess.y < posChesss.y) {
                    posChesss = jackaroChess;
                }
                yalla.Debug.logMore("JackaroChooseChessTag show jackaroChess.idx:", jackaroChess.beyondIdx, "chess id:", jackaroChess.id);
                let isDividingLineShow = !(i == 0);
                var btn: ui.jackaro.sub.chessTagUI = this.createChooseBtn(jackaroChess.color, i + 1, isDividingLineShow);
                btn.offAll();
                btn.x = len - i;
                this.nums_box.addChild(btn);
                btn.off("click", this, this.onClickBtn);
                btn.on("click", this, this.onClickBtn, [jackaroChess, callBack]);
            }
        }
        let gPos = posChesss.getGlobalPos();
        this.pos(gPos.x + 14, gPos.y);
        this.poise(len);
        this.visible = true;
    }
    private onClickBtn(jackaroChess: JackaroChess, callBack: Function, e: Laya.Event) {
        e.stopPropagation();
        yalla.Sound.playSound("click");
        callBack && callBack(jackaroChess);
        this.hide();
    }
    //姿势
    poise(len) {
        this.rotation = 0;
        this.nums_bg.width = len * this.tagWidth;
        // this.nums_bg.skin = `game/Bubble_${color}.png`;
        // this.arrow_skin.skin = `game/Bubble_${color}_0.png`;
        var centerX = 0;
        if (this.x < this.nums_bg.width / 2) {
            centerX = len / 2 * this.tagWidth - this.x;
        } else if ((this.parent as Laya.Sprite).width - this.x < this.nums_bg.width / 2) {
            var offX = len / 2 * this.tagWidth - ((this.parent as Laya.Sprite).width - this.x);
            centerX = offX * -1;
        }

        this.rotation = 0;
        this.nums_bg.centerX = centerX;
        if (this.y < this.tagWidth) {
            this.anchorY = 0;
            this.arrow.rotation = 180;
        } else {
            this.anchorY = 1;
            this.arrow.rotation = 0;
        }
    }
    hide() {
        if (!this.visible) return;
        this.visible = false;
        this.nums_box._childs && this.nums_box._childs.forEach(node => {
            this.pool.push(node);
        });
        this.nums_box.removeChildren();
    }
    createChooseBtn(color: number, num: number, isShowDividingLine: boolean): ui.jackaro.sub.chessTagUI {
        let chessTag: ui.jackaro.sub.chessTagUI;
        if (this.pool.length > 0) {
            chessTag = this.pool.shift() as ui.jackaro.sub.chessTagUI;
            chessTag.img_chess_num.skin = `jackaro/choose_bg_${color}.png`;
            chessTag.img_chess_text.skin = `jackaro/n${num}.png`;
            chessTag.img_dividing_line.visible = isShowDividingLine;
        } else {
            chessTag = new ui.jackaro.sub.chessTagUI();
            chessTag.img_chess_num.skin = `jackaro/choose_bg_${color}.png`;
            chessTag.img_chess_text.skin = `jackaro/n${num}.png`;
            chessTag.img_dividing_line.visible = isShowDividingLine;
        }
        return chessTag;
    }
}