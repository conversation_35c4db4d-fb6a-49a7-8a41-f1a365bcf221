

class JkChooseChessManager {
    public static _ins: JkChooseChessManager = null;
    public static get ins() {
        return this._ins || (this._ins = new JkChooseChessManager());
    }
    private chessChooseAniPath: Array<string>;
    private tpls = {
        tpl_0: null,
        tpl_1: null,
        tpl_2: null,
        tpl_3: null
    }
    private refreshLater = false;
    private loadPro: number = 0;
    private parentBox = null;
    private chooseSpineAniHash = {};
    public init() {
        this.loadPro = 0;
        if (yalla.Global.isLowPerformanceDevice) {
            this.chessChooseAniPath = [yalla.getSkeleton("jackaro/choose/mini/Choose_red/Choose_red"), yalla.getSkeleton("jackaro/choose/mini/Choose_blue/Choose_blue"), yalla.getSkeleton("jackaro/choose/mini/Choose_yellow/Choose_yellow"), yalla.getSkeleton("jackaro/choose/mini/Choose_green/Choose_green")];
        } else {
            this.chessChooseAniPath = [yalla.getSkeleton("jackaro/choose/Choose_red/Choose_red"), yalla.getSkeleton("jackaro/choose/Choose_blue/Choose_blue"), yalla.getSkeleton("jackaro/choose/Choose_yellow/Choose_yellow"), yalla.getSkeleton("jackaro/choose/Choose_green/Choose_green")];
        }
        this.chessChooseAniPath.forEach((path, color) => {
            var tpl = new Laya.Templet();
            tpl.once(Laya.Event.COMPLETE, this, this.onComplete, [color]);
            tpl.once(Laya.Event.ERROR, this, this.onError);
            tpl.loadAni(path);
        });
    }
    private onComplete(color: number, tpl: Laya.Templet) {
        this.loadPro++;
        if (tpl && this.tpls) {
            tpl.offAll();
            this.tpls[`tpl_${color}`] = tpl;
            if (this.loadPro == 4 && this.refreshLater && this.parentBox && this.tpls)
                this.playChessChoose(this.parentBox);
        }
    }
    private getChessChooseSpineAni(color: number): JackarooChooseSpine {
        var jackcs: JackarooChooseSpine = Laya.Pool.getItemByClass(`ChessChoose${color}SpineAni`, JackarooChooseSpine);
        if (!jackcs.sk && this.tpls) {
            var tpl: Laya.Templet = this.tpls[`tpl_${color}`];
            jackcs.initSk(tpl.buildArmature(1), color);
        }
        return jackcs;
    }
    private onError() { }
    public playChessChoose(parent: Laya.Node) {
        this.parentBox = parent;
        this.removeAll();
        if (this.loadPro == 4) {
            JackaroChessManager.instance.each((chess: JackaroChess) => {
                if (chess && !chess.destroyed) {
                    chess.updateEventListener();
                    if (chess.choose) {
                        let gPos = chess.getGlobalPos();
                        let chooseSpineAni: JackarooChooseSpine = this.getChessChooseSpineAni(chess.color);
                        chooseSpineAni.pos(gPos.x + 16, gPos.y + 16);
                        chooseSpineAni.play();
                        this.chooseSpineAniHash[chess.idKey] = chooseSpineAni;
                        this.parentBox.addChild(chooseSpineAni);
                    } else {
                        this.removeWidhIdKey(chess.idKey);
                    }
                }
            })
            // chesses.forEach((chess: JackaroChess) => {
            // })
        } else {
            this.refreshLater = true;
        }
    }
    // public testShowAll(parent: Laya.Node, choose: boolean = true) {
    //     JackaroChessManager.instance.each((chess: JackaroChess) => {
    //         chess.choose = choose;
    //     })
    //     this.playChessChoose(parent);
    // }
    private recover(chooseSpineAni: JackarooChooseSpine) {
        if (chooseSpineAni) {
            chooseSpineAni.stop();
            chooseSpineAni.removeSelf();
            Laya.Pool.recover(`ChessChoose${chooseSpineAni.color}SpineAni`, chooseSpineAni);
        }
    }
    public removeWidhIdKey(idKey: string) {
        if (this.chooseSpineAniHash) {
            this.recover(this.chooseSpineAniHash[idKey]);
            delete this.chooseSpineAniHash[idKey];
        }
    }
    public removeAll() {
        this.refreshLater = false;
        for (var idKey in this.chooseSpineAniHash) {
            this.removeWidhIdKey(idKey);
        }
        this.chooseSpineAniHash = {};
    }
    public clear() {
        this.removeAll();
        // this.tpls = null;
        this.parentBox = null;
        Laya.Pool.clearBySign(`ChessChoose${0}SpineAni`);
        Laya.Pool.clearBySign(`ChessChoose${2}SpineAni`);
        Laya.Pool.clearBySign(`ChessChoose${3}SpineAni`);
        Laya.Pool.clearBySign(`ChessChoose${1}SpineAni`);
        JkChooseChessManager._ins = null;
    }
}


class JackarooChooseSpine extends Laya.Box {
    public sk: Laya.Skeleton = null;
    public color: number = null;
    initSk(sk: Laya.Skeleton, color: number) {
        this.color = color;

        this.sk = sk;
        this.addChild(sk)

        // var lb = new Laya.Label()
        // lb.text = String(color);
        // this.addChild(lb);
    }
    play() {
        this.sk && this.sk.play("idle", true);
    }
    stop() {
        this.sk && this.sk.stop();
    }
}
