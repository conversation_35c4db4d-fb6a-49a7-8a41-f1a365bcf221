/// <reference path="../../../view/ludo/ludoData/LudoGrid.ts" />
/// <reference path="../data/game/JackaroUserService.ts" />

// 类型定义
interface Station {
    area: number;
    gridPosition: number;
    gridColor?: number;
    position?: number;
}

interface port {
    x: number;
    y: number;
}

/**
 * 传送点处理器 - 统一处理传送点相关逻辑
 * 优化架构设计：将传送点逻辑从JackaroChessOrbitManager中抽离，提高代码可维护性和扩展性
 */
class TransferGridProcessor {
    private static _instance: TransferGridProcessor;
    public static get instance() {
        return this._instance || (this._instance = new TransferGridProcessor());
    }

    /**
     * 清理单例实例 - 用于内存管理和测试环境重置
     */
    public static clearInstance(): void {
        this._instance = null;
    }

    /**
     * 检查格子是否为传送点
     * @param grid 要检查的格子
     * @returns 是否为传送点
     */
    public isTransferGrid(grid: LudoGrid): boolean {
        return grid && !!grid.transferName;
    }

    /**
     * 获取传送目标格子
     * @param transferGrid 传送点格子
     * @returns 传送目标格子，如果不是传送点则返回null
     */
    public getTransferTarget(transferGrid: LudoGrid): LudoGrid | null {
        if (!this.isTransferGrid(transferGrid)) {
            return null;
        }
        return JackaroBoardData.getGridByName(transferGrid.transferName);
    }

    /**
     * 处理传送点逻辑，计算传送后的击杀信息
     * @param endGrid 原始终点格子
     * @param curPathChessInfo 当前路径棋子信息数组
     * @returns 传送处理结果
     */
    public processTransfer(endGrid: LudoGrid, curPathChessInfo: Array<string>): TransferProcessResult {
        const result: TransferProcessResult = {
            isTransfer: false,
            transferTarget: null,
            killChessInfo: null,
            updatedPathChessInfo: [...curPathChessInfo]
        };

        if (!this.isTransferGrid(endGrid)) {
            return result;
        }

        const transferTarget = this.getTransferTarget(endGrid);
        if (!transferTarget) {
            return result;
        }

        result.isTransfer = true;
        result.transferTarget = transferTarget;

        // 检查传送后的目标格子是否有棋子，重新计算击杀信息
        const chessOnTransferGrid = JackaroChessManager.instance.getChessByGrid(transferTarget);
        if (chessOnTransferGrid && chessOnTransferGrid.wayGrid && chessOnTransferGrid.goalGrid && chessOnTransferGrid.wayGrid.atGrid(chessOnTransferGrid.goalGrid.gridName)) {
            const transferKillChessInfo = chessOnTransferGrid.beyondIdx + "_" + chessOnTransferGrid.id;
            result.killChessInfo = transferKillChessInfo;

            // 将传送后被击杀的棋子添加到chesses数组中
            if (result.updatedPathChessInfo.indexOf(transferKillChessInfo) === -1) {
                result.updatedPathChessInfo.push(transferKillChessInfo);
            }
        }

        return result;
    }

    /**
     * 创建传送点网格数据
     * @param originalGrid 原始格子
     * @param chess 棋子信息
     * @returns 传送点网格数据
     */
    public createTransferGridData(originalGrid: LudoGrid, chess: { idx: number, order: number, color: number }): yalla.data.jackaro.GridDataInterface {
        return {
            grid: {
                index: originalGrid.gridPosition,
                color: originalGrid.color,
                type: 0
            },
            chess: {
                idx: chess.idx,
                order: chess.order,
                color: chess.color
            }
        };
    }

    /**
     * 创建目标格子数据（传送后的格子）
     * @param targetGrid 目标格子
     * @param chess 棋子信息
     * @returns 目标格子数据
     */
    public createTargetGridData(targetGrid: LudoGrid, chess: { idx: number, order: number, color: number }): yalla.data.jackaro.GridDataInterface {
        return {
            grid: {
                index: targetGrid.gridPosition,
                color: targetGrid.color,
                type: 0
            },
            chess: {
                idx: chess.idx,
                order: chess.order,
                color: chess.color
            }
        };
    }
}

/**
 * 传送处理结果接口
 */
interface TransferProcessResult {
    /** 是否为传送 */
    isTransfer: boolean;
    /** 传送目标格子 */
    transferTarget: LudoGrid | null;
    /** 击杀棋子信息 */
    killChessInfo: string | null;
    /** 更新后的路径棋子信息 */
    updatedPathChessInfo: Array<string>;
}

/**
 * 增强的路径网格数据接口
 * 优化架构设计：扩展原有数据结构，包含传送相关信息
 */
interface EnhancedPathGridData {
    /** 当前棋子信息 */
    curChessInfo: string;
    /** 期望步数 */
    hopeStepNum: number;
    /** 实际步数 */
    stepNum: number;
    /** 起始格子 */
    fromGrid: yalla.data.jackaro.GridDataInterface;
    /** 终点格子 */
    toGrid: yalla.data.jackaro.GridDataInterface;
    /** 路径上的棋子 */
    chesses: Array<string>;
    /** 击杀棋子信息 */
    killChessInfo: string;
    /** 传送信息 */
    transferInfo?: {
        /** 是否为传送 */
        isTransfer: boolean;
        /** 传送点格子 */
        transferGrid?: yalla.data.jackaro.GridDataInterface;
        /** 传送目标格子 */
        transferTarget?: yalla.data.jackaro.GridDataInterface;
    };
}