namespace yalla.data.jackaro {

    export const RectWid = 72;
    export const RectHei = 88.5//90;
    export const MapHei = 1032;
    export const Raw = 10;          //行
    export const Col = 10;          //列
    export const MoveTime = 100;    //毫秒
    export const BeginIndex = 0;    //初始位置
    export const Max_Num = 100;     //100格
    export const IsTestCode: boolean = false;

    export var Move_Base_Time = 180;//150;      /**单格子移动基础时间 */
    export var Move_Change_Time = 50;//100;     /**单格子移动浮动时间 */
    export var Step_Len = 45;//60;              /**爬梯一步 步长 */
    export var Step_Time = 130;//180;           /** 爬梯一步时间ms */
    export var Snake_Time = 190;//200;          /**每100像素移动需要200ms */
    export var Snake_Time_V = 1.5;//2;          /** 后半程变数系数 */

    export var JumpLadder = 10;        /**爬梯跳动幅度*/
    export var JumpHei = 1;             //60;  平移模式跳跃高度，暂时无用
    export var InitScale = 0.72;        /**起始点 & 棋子相遇同一个格子 缩放比例 */


    /**
     * 游戏中数据配置
     */
    export class JackaroGameConfig {
        public static Add_Exp = 5;                //结算增加经验
        public static Cost_Money = 500;           //进入房间消耗
        public static Ready_Max_Time = 600;       //匹配最大等候时间10分钟
        public static Dice_CD = 10000;              //掷骰子cd时间10s
        public static Face_List = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];//默认头像

        public static DiceTime: number = 400;//骰子转动时间
        public static WaitTime: number = 5000;//10000;//等待时间
        public static ResetTime: number = 2000;//重置时间

        public static Max_Player_Nums = 2;      //两人局
        public static Player_Pos = [{ "left": 4, "bottom": -30 }, { "right": 4, "bottom": -30 }];

        public static isShowCardRule: boolean = true;//是否显示片面说明和击杀展示 默认显示

        public static JackaroChessExchangeSpeed = 0.8365;  //jackaro 棋子交换时移动速度
    }


    export const ChessId = 51000;
    export const ChessBoardId = 52000;
    export const PokerId = 50000;
}