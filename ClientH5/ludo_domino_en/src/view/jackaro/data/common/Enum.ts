namespace yalla.data.jackaro {

	export class EnumCustomizeCmd {
		static Jackaro_RoundEnd = 'Jackaro_RoundEnd';	//每一轮跑结束
		static Jackaro_PlayMsg = "Jackaro_PlayMsg";     //消息播放
		static Exit_Back = 'ExitBack';			//返回上一层view
		static Server_Closed = 'Server_Closed';		//服务器断联
		static Server_Error = 'Server_Error';		//socket连接失败
		static Game_JoinAgora = 'Game_JoinAgora';	//加入语音聊天房间
		static Game_Chat_My = 'Game_70_My';        	 			//聊天
		static Game_ChatOFF_My = 'Game_ChatOFF_My';				//设置屏蔽聊天
		static Game_ChatOFF = 'Game_ChatOFF';				//设置屏蔽聊天
		static Game_Chess_Move_End = 'Game_Chess_Move_End';				//棋子移动结束
		static Game_Chess_Move_PerfectEnterEndPoint = 'Game_Chess_Move_PerfectEnterEndPoint';//棋子移动完美进终点
		static Game_Card_Discard_End = 'Game_Card_Discard_End';				//弃牌完成
		static Game_Play_Poker_Ani_End = 'Game_Play_Poker_Ani_End';			//打牌动画或者移动棋子动画完成
		static Game_Move_Chess_Begin = 'Game_Move_Chess_Begin';				//移动棋子开始
		static Game_Click_Invalid_Poker = 'Game_Click_Invalid_Poker';				//点击无效牌
		static Game_Hide_ChatAndTopView = 'Game_Hide_ChatAndTopView';				//隐藏聊天和顶部局内信息
		static Game_Show_ChatTouchMaskView = 'Game_Show_ChatTouchMaskView';				//显示聊天触摸遮挡层
	}
	//棋子位移类型
	export enum JackarooMoveType {
		MOVE_NORMAL = 0,//骰子点数移动
		MOVE_BE_HIT = 1,//撞击移动
		MOVE_FORWARD_TRANSFER = 6,//
		MOVE_BACKWARD_TRANSFER = 7,//
		MOVE_BACKWARD_WIND = 8, //向后的风,移动
		MOVE_TIGER_HIT = 9,//老虎撞击移动
		MOVE_HOME = 10,//前端玩家被离开时移动类型
		MOVE_EXCHANGE = 11,//交换棋子移动
		MOVE_TAKEOFF = 12,//起飞移动
		MOVE_QUICKMODE = 13,//快速模式移动到基地
	}
	export class EnumCmd {
		// 主命令
		static Game_Login = 'Game_10';            	//登录成功相应
		static Game_TicketLogin = 'Game_11';          //票据登陆
		static Game_GameDataInfo = 'Game_12';		//游戏相关信息 地图
		static Game_EnterRoom = 'Game_20';         	//进入房间
		static Game_QuitRoom = 'Game_21';         	//推出房间
		static Game_GameEvent = 'Game_31';        	//游戏操作响应
		static Game_GAME_OPERATE = 'Game_30';			//游戏操作
		static Game_GameResult = 'Game_49';		//游戏用户状态广播
		static Game_OutGame = 'Game_126';        		//顶号
		static Game_Update_Player_Level = 'Game_81';	//玩家升级
		static Game_Chat = 'Game_70';        	 		//聊天
		static Game_GetAgoraToken = 'Game_90';		//获取语音token
		static Game_GetZegoToken = 'Game_92';		//获取语音token
		static Game_Agora_Operate = 'Snake_91'; 		//进入语音聊天房间
		static Game_Update_Player_Coin = 'Game_80';				//刷新用户金币钻石
		static Game_Gift_Cnf_List = 'Game_111';					//获取游戏内互动礼物配置列表 1.3.1 add
		static Game_Gift_Send = 'Game_112';						//游戏内互动礼物赠送, 1.3.1 add
		static Game_Click_Poker = 'Game_Click_Poker';						//点击扑克
	}

	//游戏命令
	export enum Command {
		PLAYER_LOGIN = 10,//登陆
		TICKET_LOGIN = 1,//票据登陆
		SELECT_POKER = 3,//选牌
		PLAY_POKER = 4,//出牌
		// MOVE_CHESS = 5,//移动棋子
		EXCHANGE_CHESS = 6,//交换棋子
		EXTEND_TIME = 7,//延长思考时间
		DISCARD_POKER = 8,//弃牌
		QUIT_ROOM = 9,//退出房间
		FLUSH_CURRENCY = 10,//刷新货币 参考domino
		VOICE_TOKEN = 13,
		GAME_GIFT_CONFIG = 14, //游戏礼物配置 Tyr指令:212
		GAME_GIFT_SEND = 15,//赠送游戏礼物 Tyr指令:213
		TEST_EXCHANGE_POKER = 20,//测试交换扑克 Tyr指令:214
		MOVE_CHESS_PUB = 107,//移动棋子推送
		EXCHANGE_CHESS_PUB = 108,//交换棋子推送
		GAME_PLAYER_LOGIN_PUB = 100,//游戏玩家登录
		DEAL_POKER = 101,//发牌
		GAME_START = 102,//游戏开始
		CHANGE_PLAYER = 103,//切换玩家
		SETTLEMENT = 104,//游戏结束
		KICK_PLAYER = 105,//踢玩家
		TEST_EXCHANGE_POKER_PUB = 120,//测试交换扑克推送 Tyr指令:218
		SYNC_GAME_DATA = 11,//同步游戏数据
		CANCEL_TRUST = 12,//取消托管
		GAME_CHAT = 16,//游戏聊天
		ASSIGN_NEXT_PLAYER_DISCARD = 17,//指定下一个玩家弃牌
		DRAW_NEXT_PLAYER_POKER = 18,//抽取下一个玩家的牌,Tyr指令:216
		SET_CHAT = 19, //设置聊天,Tyr指令217

		TRUST_PUB = 106,
		EXTEND_TIME_PUB = 109,//延长思考时间推送
		QUIT_GAME_PUB = 110,//有玩家主动退出时的推送消息
		GAME_GIFT_PUB = 111,//游戏礼物推送
		GAME_CHAT_PUB = 112,//游戏聊天推送消息
		FINISH_DISCARD_PUB = 113,//完成弃牌后的推送
		FORCED_DISCARD_PUB = 114,//指定下一个玩家弃牌后的推送
		POKER_SEVEN_MOVE_PUB = 115,//7步移动后的推送
		IMPACT_MOVE_PUB = 116,//撞击移动后的推送
		DRAW_POKER_PUB = 117,//指定下一个玩家弃牌
		END_EXCEPTION_GAME_PUB = 118,//游戏异常结束,客户端收到此消息,则弹框提示,玩家点击确认后关闭游戏
		SET_CHAT_PUB = 119, //别人设置聊天开关的推送
		GAME_DATA_INFO = 12,//游戏相关信息
		SYNC_TIME = 23,//同步时间


		JOIN_ROOM = 22,//加入房间

		GAME_OPERATE = 30,//游戏操作
		GAME_EVENT = 31,//游戏操作
		GAMESTATUS = 40,//游戏状态

		GAME_PLAYER_COLOR_SELECT = 41,//颜色选择
		GAME_MAP_CHESS_MOVE = 46,//走棋子
		GAMERESULT = 49,//游戏结果
		GAMEPLAYERSTATUS = 50,//玩家状态 undo 投骰子 移动棋子开始结束
		PLAYER_ENTER = 60,//玩家进入

		PLAYER_CHAT_ALL = 70,//聊天
		UPDATA_PLAYER_COIN = 80,//刷新用户金币钻石
		UPDATA_PLAYER_LEVEL = 81,//玩家升级


		GET_AGORA_TOKEN = 90,//获取语音频道token
		AGORA_OPERATE = 91,//语音频道操作
		GET_ZEGO_TOKEN = 92,
		FRIEND_ADD_MSG = 100,//添加好友
		FRIEND_LIST = 101,//好友列表
		FRIEND_INVITE = 102,//邀请好友

		OUT_GAME = 126,//踢出服务
		HEART = 127,//心跳包
	}
	export enum GameType {
		SNAKEANDLADER = 10020,//蛇棋
		DOMINO = 10021,//多米诺
		SHEEP = 10022,//顶
		JACKAROO = 10023,//杰克罗
	}

	export enum JackarooColor {
		RED = 0,
		GREEN = 1,
		YELLOW = 2,
		BLUE = 3,
	}


	export enum PlayerColor {
		RED = 0,
		YELLOW = 2,
		BLUE = 3,
		GREEN = 1,
		ORANGE = 4,
	}

	//操作结果
	export enum OperateResult {
		FAILED = 0,//失败
		SUCCESS = 1,//成功
	}

	//游戏操作
	export enum GameOperate {
		SIT = 0,//入座
		UP = 1,//离开座位
		SIGN_UP = 2,//准备
		SIGN_CANCEL = 3,//取消准备
		THROW = 4,//掷骰子
		RESET_THROW = 5,//重置投掷
		CHOOSE_CHESS = 6,//选择棋子
		CHESS_MOVE = 7,//棋子移动
		SYSTEM_TRUST = 8,//系统托管
		SYSTEM_TRUST_CANCEL = 9,//取消系统托管
		CHAT_OFF = 23,//屏蔽聊天
		CHAT_OFF_CANCEL = 24,//取消屏蔽聊天
	}

	export enum GameEvent {
		GAME_STATE_CHANGE = 1,//游戏状态改变
		GAME_COLOR_SELECT = 2,//玩家颜色选择
		GAME_PLAYER_STATUS_CHANGE = 3,//用户状态改变
		GAME_PLAYER_THROW = 4,//玩家掷骰子
		GAME_SHOW_RESULT = 5,//游戏结果
		SAL_CHESS_MOVE = 7,//蛇棋棋子移动
		SAL_CHESS_MOVE_SNAKE = 8,//蛇棋🐍移动
		SAL_CHESS_MOVE_LADDER = 9,//蛇棋梯子移动
		SAL_CHESS_BORN = 10,//蛇棋棋子移动到初始点
	}

	export enum EnumPlayerTurnStatus {
		THROW_START = 0,				//开始投掷
		THROW_END = 1,					//投掷结束
		RETHROW_START = 2,				//是否重置开始
		RETHROW_END = 3,				//是否重置结束
		CHESSMOVE_START = 6,			//棋子移动开始
		CHESSMOVE_END = 7,			//棋子移动结束
	}

	export enum ChessEvent {
		BORN = 1,
		MOVE = 2,
		SNAKE = 3,
		LADDER = 4,
	}

	// 游戏模式类型，Ludo使用全部，Domino使用0和1
	export enum GameType {
		NORMAL = 0,//正常
		MASTER = 1,//大师
		QUICK = 2,//快速
		FIRE = 3,//火拼
		ARROW = 4, //箭头
		DUEL = 5,//决斗
		JUNGLE = 6, //丛林模式
		NIGHT = 7, //黑夜模式

		// Jackaroo
		JACKAROO_BASIC = 100,    // JACKAROO basic
		JACKAROO_COMPLEX = 101,    // JACKAROO complex
		JACKAROO_BASIC_QUICK = 102,   // JACKAROO basic quick
		JACKAROO_COMPLEX_QUICK = 103,   // JACKAROO complex quick
		JACKAROO_COMPLEX_ONE_VS_ONE = 104, // JACKAROO complex 1v1

	}

	// 游戏对战类型
	export enum GameAgainstType {
		MELEE = 0,   // 4人混战
		TWO_VS_TWO = 1,    // 2对2
		ONE_VS_ONE = 2,    // 1对1
	}

	//游戏状态
	export enum GameStatus {
		GAME_PREPARE = 0, //准备阶段
		GAMING = 1, //进行中
		GAME_RESULT = 2, //游戏结算
		GAME_END = 3,//游戏结束
	}

	//游戏道具类型
	export enum GameGroup {
		SPORTS_MODE = 0,//竞技
		PROP_MODE = 1,//道具
	}

	//格子类型
	export enum GridType {
		BORN_POINT = 0,//出生点
		BEFORE_END = 1, //终点区域的前一格
		FLY_POINT = 2, //起飞点
		END_POINT = 3, //终点区域的格子
		COMMON_POINT = 4, //普通区域
	}
	export const PokerChinesDefine = {
		1: "黑桃A",
		2: "黑桃2",
		3: "黑桃3",
		4: "黑桃4",
		5: "黑桃5",
		6: "黑桃6",
		7: "黑桃7",
		8: "黑桃8",
		9: "黑桃9",
		10: "黑桃10",
		11: "黑桃J",
		12: "黑桃Q",
		13: "黑桃K",
		14: "红桃A",
		15: "红桃2",
		16: "红桃3",
		17: "红桃4",
		18: "红桃5",
		19: "红桃6",
		20: "红桃7",
		21: "红桃8",
		22: "红桃9",
		23: "红桃10",
		24: "红桃J",
		25: "红桃Q",
		26: "红桃K",
		27: "方片A",
		28: "方片2",
		29: "方片3",
		30: "方片4",
		31: "方片5",
		32: "方片6",
		33: "方片7",
		34: "方片8",
		35: "方片9",
		36: "方片10",
		37: "方片J",
		38: "方片Q",
		39: "方片K",
		40: "梅花A",
		41: "梅花2",
		42: "梅花3",
		43: "梅花4",
		44: "梅花5",
		45: "梅花6",
		46: "梅花7",
		47: "梅花8",
		48: "梅花9",
		49: "梅花10",
		50: "梅花J",
		51: "梅花Q",
		52: "梅花K",
	}

	//扑克牌定义
	//快速获得点数,枚举的整数num,point=num%13,point=0表示K
	export enum Poker {
		NONE_POKER = 0, //占位符

		SPADE_ACE = 1, //黑桃A,spade表示黑桃
		SPADE_TWO = 2,
		SPADE_THREE = 3,
		SPADE_FOUR = 4,
		SPADE_FIVE = 5,
		SPADE_SIX = 6,
		SPADE_SEVEN = 7,
		SPADE_EIGHT = 8,
		SPADE_NINE = 9,
		SPADE_TEN = 10,
		SPADE_JACK = 11,
		SPADE_QUEEN = 12,
		SPADE_KING = 13,

		HEART_ACE = 14, //红桃A,heart表示红桃
		HEART_TWO = 15, //红桃2
		HEART_THREE = 16,
		HEART_FOUR = 17,
		HEART_FIVE = 18,
		HEART_SIX = 19,
		HEART_SEVEN = 20,
		HEART_EIGHT = 21,
		HEART_NINE = 22,
		HEART_TEN = 23,
		HEART_JACK = 24,
		HEART_QUEEN = 25,
		HEART_KING = 26, //红桃K

		DIAMOND_ACE = 27, //方片A
		DIAMOND_TWO = 28,
		DIAMOND_THREE = 29,
		DIAMOND_FOUR = 30,
		DIAMOND_FIVE = 31,
		DIAMOND_SIX = 32,
		DIAMOND_SEVEN = 33,
		DIAMOND_EIGHT = 34,
		DIAMOND_NINE = 35,
		DIAMOND_TEN = 36,
		DIAMOND_JACK = 37,
		DIAMOND_QUEEN = 38,
		DIAMOND_KING = 39, //方片K

		CLUB_ACE = 40, //梅花A
		CLUB_TWO = 41,
		CLUB_THREE = 42,
		CLUB_FOUR = 43,
		CLUB_FIVE = 44,
		CLUB_SIX = 45,
		CLUB_SEVEN = 46,
		CLUB_EIGHT = 47,
		CLUB_NINE = 48,
		CLUB_TEN = 49,
		CLUB_JACK = 50,
		CLUB_QUEEN = 51,
		CLUB_KING = 52, //梅花K
	}
	export enum PokerColor {
		Spade, // 表示黑桃
		Heart, // 表示红桃
		Diamond, // 表示方片
		Club // 表示梅花
	}
	export enum PlayMethod {
		BASIC_PLAY = 0,//基础玩法
		COMPLEX_PLAY = 1, //复杂玩法
		COMPLEX_ONE_VS_ONE_PLAY = 2, //1v1复杂玩法
	}
	//玩家状态
	export enum PlayerStatus {
		NONE_PLAYER_STATUS = 0,//初始状态
		WAIT_OUT = 1, //等待出牌
		WAIT_DISCARD = 2, //等待玩家弃牌
		WAIT_MOVE = 3, //等待玩家走子
		OPERATE_END = 1000, //操作结束
	}

	//操作结果
	export enum OperateResult {
		FAIL = 0, //失败
		SUC = 1, //成功
	}


	// 错误码定义
	export enum ErrorCode {
		UNDEFINED_ERROR_CODE = 0, // 未定义,占位符
		NONE_ERROR = 1, //无错误
		LOGIN_FAILD_PW = 101,//密码错误
		LOGIN_FAILD_ID = 102,//登陆ID错误
		LOGIN_FAILD_OLDSIGN = 103,//重复的签名
		LOGIN_FAILD_SIGN = 104,//签名错误
		LOGIN_FAILD_TIME = 105,//客户端时间错误
		QUICKSTART_FAILD_NOROOM = 203,//房间已满
		ENTER_ROOM_FAILD_NOROOM = 204,//房间已满
		ENTER_ROOM_FAILD_ROOMID = 205,//错误的房间号
		GAMEOPERATE_FAILD_SIT_NOSTOOL = 301,// 已有人
		GAMEOPERATE_FAILD_SIT_WRONGSTOOLNUM = 302,//错误的凳子ID
		GAMEOPERATE_FAILD_SIT_WRONGTIME = 303,//错误的操作时间
		NO_MONEY = 401,//余额不足
		NO_DIAMOND = 402,//钻石不足
		ACCOUNT_BE_FROZEN = 403,//1.2.2 add 账号被冻结
		MAX_LIMIT = 601,//超出限制
		JOIN_ROOM_FAILD_NOROOM = 701,//无此房间
		JOIN_ROOM_FAILD_MAX = 702,//房间人数已满
	}
	//经验类型定义
	export enum ExpType {
		NONE_EXP_TYPE = 0, //占位符
		ENTER_END_AREA = 1, //进入终点区域
		KILL_CHESS = 2,//击杀棋子
		FLY_CHESS = 5, //起飞棋子
	}

	//聊天消息类型
	export enum GameChatMessageType {
		CHAT_MESSAGE_NONE = 0,
		CHAT_MESSAGE_TEXT = 1,// 文本
		CHAT_MESSAGE_EMOJI = 2, // emoji表情
		CHAT_MESSAGE_PHRASE = 3,// 快捷短语
	}

	//聊天消息频道类型
	export enum GameChatChannelType {
		CHAT_CHANNEL_NONE = 0,
		CHAT_CHANNEL_ALL = 1, //所有
		CHAT_CHANNEL_TEAM = 2, //组队
	}

	//如果是起飞选项,让下家弃牌选项,都不用记录,因为玩家点击选就请求相关接口了
	export enum SelectItemType {
		NONE_ITEM_TYPE = 0, //占位符,无选项
		MOVE_ONE = 1, //出牌A,选择走1步
		MOVE_TEN = 2, //出牌10,选择走10步
		MOVE_ELEVEN = 3, //出牌A,选择走11步
		MOVE_THIRTEEN = 4, //复杂模式,出牌K,选择走13步
	}
}

