//出牌的通用结果数据
//当出的牌为A,K,Q,2,3,4,5,6,7,8,9,10时返回的结果类型
//这个对象中都是操作前的数据,
//例如:红色棋子1要从1位置,走到4位置,4位置没有棋子,则格子1中的棋子是红色棋子1,格子4中无棋子
// message PlayCommonResultData {
//     //Key:可走的步数,当步数为0时表示起飞,
//     //客户端根据这个map中的数据来判断是否需要选步数或选子
//     //因为客户端知道出的什么牌,客户端需要根据不同的牌展示不同的界面
//     map<int32, PlayResultData> playResultData = 1; //不同步数可以移动的棋子
// }

module yalla.data.jackaro {
    export interface PlayCommonResultDataInterface {
        playResultData: Record<string, PlayResultDataInterface>;
    }
}