//出牌后,可以移动的棋子的数据
// message PlayResultItemData {
//     GridData fromGrid = 1; //要移动的棋子所在的格子的数据,自己的棋子所在的格子
//     GridData toGrid = 2; //到达的格子所在的格子数据
//     bool kill = 3; //是否发生吃子
// }
module yalla.data.jackaro {
    export interface PlayResultItemDataInterface {
        fromGrid: GridDataInterface;
        toGrid: GridDataInterface;
        kill: boolean;
        transferGrid: GridDataInterface;
        notAllowMoveNumber?: Array<number>;
        quickFinish?: boolean; // 是否完成快速模式
        toBornData?: Array<any>; // 移动到出生点的数据
        previewRewardPoker?: boolean; // 是否预览奖励牌
        drawPokerMoveNumber?: Array<number>; // 针对出牌7有效,触发抽牌的移动步数
        drawPokerCombination?: Record<number, number>; //触发抽牌的移动组合。key:当前棋子走的步数,value:另一个棋子的序号。
    }
}