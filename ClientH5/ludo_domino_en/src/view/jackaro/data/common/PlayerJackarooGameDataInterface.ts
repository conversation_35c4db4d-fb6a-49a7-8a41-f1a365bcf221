/*
//以后扩展时,和游戏玩法、棋盘（即游戏系统）相关的玩家字段（字段的值对玩法有影响），其它玩家关注的字段,定义在这里,
//例如：玩家延时次数，延时花费,是否托管,就不用放在这里,因为,没有延时功能游戏可以继续进行,和玩法不是紧相关,
//是否托管,不能被别的玩家知道,没有托管功能游戏也可以继续进行,和玩法不是紧相关。
//和展示相关的玩家游戏数据字段（字段的值对玩法没有影响）定义在PlayerShowGameData
//和玩法不是紧相关的字段,且只有玩家自己可以看到的,定义在PlayerSelfGameData

message PlayerJackarooGameData {
    int32 idx = 1; //玩家id
    JackarooColor color = 2;//玩家的颜色,和玩家的棋子颜色是一样的
    map < int32, GridData > gridMap = 3; //属于当前玩家的格子,key:格子的索引,
    bool banker = 4; //是否是庄家,
    map < int32, PlayerChess > chessPosition = 5; //当前玩家所有棋子的位置,key:棋子的序号,所在方便客户端确定棋子的位置,此字段暂定
    repeated Poker handPoker = 6; //玩家的手牌
    PlayerStatus playerStatus = 7; //玩家的状态
    int32 remainThinkingTime = 8; //当前玩家剩余思考时间,精确到秒
    bool allowMoveTeammateChess = 9; //是否允许走队友的棋子

}
*/

module yalla.data.jackaro {
    export interface PlayerJackarooGameDataInterface {
        idx: number; //玩家id
        color: JackarooColor; //玩家的颜色,和玩家的棋子颜色是一样的
        gridMap: Record<string, yalla.data.jackaro.GridDataInterface>; //属于当前玩家的格子,key:格子的索引,
        banker: boolean; //是否是庄家,
        chessPosition: Record<string, yalla.data.jackaro.PlayerChessInterface>; //当前玩家所有棋子的位置,key:棋子的序号,所在方便客户端确定棋子的位置,此字段暂定
        handPoker: yalla.data.jackaro.Poker[]; //玩家的手牌
        playerStatus: yalla.data.jackaro.PlayerStatus; //玩家的状态
        remainThinkingTime: number; //当前玩家剩余思考时间,精确到秒
        allowMoveTeammateChess: boolean; //是否允许走队友的棋子
    }
}