
//可展示的游戏数据
//     message PlayerShowGameData {
//         JackarooColor color = 1; //玩家颜色,根据玩家的颜色客户端可以确定玩家的位置,去掉以前的位置字段
//         bool left = 2; //玩家是否离开,
//         int32 winIndex = 3; //结算时本局游戏获胜玩家的排名,-1:失败，
//         bool ready = 4; //是否准备,

// }

module yalla.data.jackaro {
    export interface PlayerShowGameDataInterface {
        color: JackarooColor; //玩家颜色,根据玩家的颜色客户端可以确定玩家的位置,去掉以前的位置字段
        left: boolean; //玩家是否离开,
        winIndex: number; //结算时本局游戏获胜玩家的排名,-1:失败，
        ready: boolean; //是否准备,
    }
}