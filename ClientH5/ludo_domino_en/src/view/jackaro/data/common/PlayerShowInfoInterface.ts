//玩家展示信息
// message PlayerShowData {
//     int32 idx = 1; //用户id
//     int32 faceId = 2; //头像框id
//     string faceUrl = 3; //头像框地址
//     string nickName = 4; //昵称
//     int32 placeId = 5; // 所在位置  0大厅 其他对应的gameid -1 离线
//     int32 friendType = 6; //好友类型
//     int32 country = 7; //国家
//     int32 level = 8; //等级
//     int32 vipLevel = 9; //vip等级
//     int32 winCount = 10; //获胜次数
//     int32 totalCount = 11; //总次数
//     int32 segId = 15; //段位
//     int32 starNum = 16; //传奇之星
//     int64 prettyId = 17; //显示id
//     bool openChatOff = 18; //是否开启屏蔽聊天, true:开启,false:关闭
//     bool quit = 19; //是否主动离开,true:主动离开,false:未离开
//     int32 royalLevel = 20; //皇室等级,
//     bool royalInvisible = 21; //是否不可见,false:所有人可见,true:仅自己可见
//     int32 royalLevelNameAnimation = 22; //昵称是否扫光,true:开启扫光,false:关闭扫光
//     int32 giftId = 23; //别人赠送的互动礼物id

//     string banTalkData = 24; //禁言数据,没有禁言时为空
//     VoiceType voiceType = 25; //语音房类别
//     bool openGameGift = 26; //是否开启互动表情
// }

module yalla.data.jackaro {

    export interface PlayerShowInfoInterface {
        idx: number; //用户id
        faceId: number; //头像框id
        faceUrl: string; //头像框地址
        nickName: string; //昵称
        placeId: number; // 所在位置  0大厅 其他对应的gameid -1 离线
        friendType: number; //好友类型
        country: number; //国家
        level: number; //等级
        vipLevel: number; //vip等级
        winCount: number; //获胜次数
        totalCount: number; //总次数
        segId: number; //段位
        starNum: number; //传奇之星
        prettyId: number; //显示id
        openChatOff: boolean; //是否开启屏蔽聊天, true:开启,false:关闭
        quit: boolean; //是否主动离开,true:主动离开,false:未离开
        realRoyLevel: number;
        royalLevel: number; //皇室等级,
        royVisibility: boolean;
        royalInvisible: boolean; //是否不可见,false:所有人可见,true:仅自己可见
        royalLevelNameAnimation: boolean; //昵称是否扫光,true:开启扫光,false:关闭扫光
        giftId: number; //别人赠送的互动礼物id
        banTalkData: string; //禁言数据,没有禁言时为空
        voiceType: VoiceType; //语音房类别
        openGameGift: boolean; //是否开启互动表情
        propNum?: number;
        talkSkinId?: number; //气泡皮肤id
        pokerSkinId: number; //扑克皮肤id
    }
}