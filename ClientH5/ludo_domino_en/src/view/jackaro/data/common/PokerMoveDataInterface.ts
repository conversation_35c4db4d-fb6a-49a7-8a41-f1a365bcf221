//可以操的扑克牌移动棋子相关的数据
// message PokerMoveData {
//     oneof data {
//         PlayCommonResultData commonResult = 1; //当出的牌为A,K,Q,2,3,4,5,6,7,8,9,10时返回的值
//         PlayJackResultData jackResult = 2; //当出牌为J时的返回值
//     }
// }
module yalla.data.jackaro {
    export interface PokerMoveDataInterface {
        commonResult: yalla.data.jackaro.PlayCommonResultDataInterface;
        jackResult: yalla.data.jackaro.PlayJackResultDataInterface;
        complexKing: ComplexPlayKingResultInterface;
        showPreResult: boolean;
        pokerTenDiscard: boolean; //出牌为10时,是否可以让其他玩家弃牌
        waitDrawnPokerCount: number;
    }
}
