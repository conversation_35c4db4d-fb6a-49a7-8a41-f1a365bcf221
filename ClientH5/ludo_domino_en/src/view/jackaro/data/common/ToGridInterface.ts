//移动棋子数据 ，主要记录棋子移动信息
//"toGrid":{"grid":{"index":-1,"color":"YELLOW","type":"END_POINT"},"chess":{"idx":87568687,"order":4,"color":"YELLOW"}}

interface ToGridInterface {
    grid: {
        index: number;
        color: yalla.data.jackaro.JackarooColor;
        type?: yalla.data.jackaro.GridType;
    };
    chess: {
        idx: number;
        order: number;
        color: yalla.data.jackaro.JackarooColor;
    };
}
