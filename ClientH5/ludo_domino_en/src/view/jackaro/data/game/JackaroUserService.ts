module yalla.data.jackaro {

    import EventDispatcher = laya.events.EventDispatcher;
    import EnumCustomizeCmd = yalla.data.jackaro.EnumCustomizeCmd;
    import EnumCmd = yalla.data.jackaro.EnumCmd;

    /**
 * 操作消息类型
 */
    enum JackaroOperateMsgType {
        PLAY_POKER = 1,
        EXCHANGE_POKER = 2,
        FREE_MOVE_POKER = 3,
        DISCARD_POKER = 4,
        ASSIGN_NEXT_PLAYER_DISCARD = 5,
        DRAW_NEXT_PLAYER_POKER = 6,

    }
    export class JackaroUserService extends EventDispatcher {
        public static _instance: JackaroUserService;
        private _user = new yalla.data.jackaro.User();
        private _room: yalla.data.jackaro.Room = new yalla.data.jackaro.Room();
        /**消息队列 */
        public queue: Ludo.Queue = new Ludo.Queue();
        /**未收到cmd=20 快速进入房间消息 存放的消息列表 */
        private _readyMsgList: Array<any> = [];
        private _isReady: boolean = false;

        public LevelUp: boolean = false;
        private _client;
        public IsUpdatePlayerCoin: boolean = false;       //是否更新金币钻石
        private _curPlayCard: yalla.data.jackaro.Poker;
        private logTag: string = "JackaroUserService : ";

        public addOperateTime: number = 10000;//花费钻石后每次延长的时间，单位秒;
        public defaultOperateTime: number = 15000//15000;//20000;//操作时间 20秒 头像、弃牌默认时长

        public delayMoveChessTime: number = 250;//出牌后走棋子延迟时间 ms
        public moveChessKillEmojiTime: number = 1000;//走棋子后显示击杀特效延迟时间 ms

        public recordTestExchangePoker: { sourcePoker: number, targetPoker: number } = null;
        private _curSocketClose: boolean = false;//当前是否网络关闭
        private deltaTime: number = 0;//时间差
        private sendTime: number = 0;//发送时间
        // private receiveTime: number = 0;//接收时间

        // public isEndMsg: boolean = false;
        // public msgList: Array<any> = [];
        /**
         * 缓存前端操作消息数据，根据操作类型记录数据参数
         */
        private _operateDataMap: Record<any, any> = {
            [JackaroOperateMsgType.PLAY_POKER]: null,
            [JackaroOperateMsgType.EXCHANGE_POKER]: null,
            [JackaroOperateMsgType.FREE_MOVE_POKER]: null,
            [JackaroOperateMsgType.DISCARD_POKER]: null,
            [JackaroOperateMsgType.ASSIGN_NEXT_PLAYER_DISCARD]: null,
            [JackaroOperateMsgType.DRAW_NEXT_PLAYER_POKER]: null
        };

        private _maxLoginRequestTimes: number = 6;//最大登录请求次数
        private _loginRequestTimes: number = 0;//登录请求次数
        private _loginRequestTimeout: number = 5000;//登录请求超时时间
        //是否触发主动发送向服务器发送出牌请求，出牌7比较特殊，第一颗棋子移动时就触发，因为第二颗棋子移动时才发送请求
        private _isTriggerPlayCard: boolean = false;
        private _isTriggerPlayCardOutRound: number = 0;

        constructor() {
            super();
            if (yalla.util.IsBrowser()) {
                this._client = yalla.ClientBrower.instance;
            } else {
                this._client = yalla.Client.instance;
            }
        }
        public get client() {
            return this._client;
        }

        static get instance(): JackaroUserService {
            return JackaroUserService._instance || (JackaroUserService._instance = new JackaroUserService());
        }


        public getCurUserId(): number {
            return this._user.idx;
        }
        public register(): void {
            if (!this._user) this._user = new yalla.data.jackaro.User();
            if (!this._room) this._room = new yalla.data.jackaro.Room();

            // this._client.on('Game_4_1', this, this.GameResponse);
            this._client.on('Game_4', this, this.GameResponse);
            this._client.on('Game_SocketClose', this, this.handleSocketClose);
            this._client.on('Game_GateWay_Error', this, this.handleGatewayError);
            yalla.Debug.log("JackaroUserService register  3.12-1602");
        }

        public clear(): void {
            Laya.timer.clear(this, this.onLoginRequestTimeout);
            this.IsUpdatePlayerCoin = false;
            // this._client.off('Game_4_1', this, this.GameResponse);
            this._client.off('Game_4', this, this.GameResponse);
            this._client.off('Game_SocketClose', this, this.handleSocketClose);
            this._client.off('Game_GateWay_Error', this, this.handleGatewayError)

            this._room && this._room.clear();
            this.clearQueue();

            this._room = null;
            this._user = null;
            this._readyMsgList = null;
            this._isReady = false;
            yalla.Debug.log("JackaroUserService clear");
            Laya.timer.clearAll(this);
        }

        public get user() { return this._user; }
        public get room() { return this._room; }

        private sendMsg(cmd, cmdName, bodyData?, businessOrder = 1): void {
            var msg = bodyData
            if (!msg) msg = {};
            msg.roomId = Global.Account.roomid;
            msg.command = cmd;

            this._client.sendMsg(cmdName, cmd, msg, { fixedRouteKey: String(Global.Account.fixedRouteKey) }, businessOrder);
        }
        public ticketLogin(isReconnection: boolean = false): void {
            if (!this._user) return;
            this._room.roomId = Global.Account.roomid;
            var msgindex = 1;
            if (yalla.util.IsBrowser()) msgindex = this.msgProgress - 1 >= 0 ? this.msgProgress - 1 : 0;
            var obj = {
                idx: this._user.idx,
                token: yalla.Global.Account.token,
                version: yalla.Global.Account.version,
                // roomid: yalla.Global.Account.roomid,
                reconnection: isReconnection,
                pokerConfigId: Global.Account.pokerConfigId || 0,
                chessPanelConfigId: Global.Account.chessPanelConfigId || 0,
                gameType: Global.Account.devGameType || 0,
                voiceVersion: yalla.Global.Account.version
            }
            if (ludo.Proto.instance.root_jackaro) {
                var loginMessage = ludo.Proto.instance.root_jackaro.lookupType('TicketLoginReq');
                var message = loginMessage.create(obj);
                var byteArray = loginMessage.encode(message).finish();
            }
            this.sendMsg(yalla.data.jackaro.Command.TICKET_LOGIN, 'JackarooGameStreamReq', {
                data: byteArray
            }, BusinessOrderCode.jackaro_ticketLogin);
            if (!this._curSocketClose) {
                Laya.timer.clear(this, this.onLoginRequestTimeout);
                Laya.timer.loop(this._loginRequestTimeout, this, this.onLoginRequestTimeout);
            }
        }
        private onLoginRequestTimeout(): void {
            this._loginRequestTimes++;
            if (this._loginRequestTimes < this._maxLoginRequestTimes) {
                this.resendTicketLogin();
                if (this._loginRequestTimes == 1) {
                    yalla.common.connect.ReconnectControl.instance.connect(true, true, true);
                }
            } else {
                Laya.timer.clear(this, this.onLoginRequestTimeout);
                this._loginRequestTimes = 0;
                yalla.common.Confirm.instance.showConfirm("Lose Network Links",
                    Laya.Handler.create(this, () => {
                        yalla.common.Confirm.instance.hideConfirm();
                        yalla.common.connect.ReconnectControl.instance.connect(true, true, true);
                        this.resendTicketLogin();
                    }),
                    Laya.Handler.create(this, () => {
                        this.backHall(true);
                    }), ['Connect', 'Exit'], false, false);
            }
        }

        private resendTicketLogin() {
            if (yalla.util.IsBrowser()) {
                //浏览器走这个逻辑，1.3.1使用原生 则不用主动发起
                yalla.ClientBrower.instance.init(Laya.Handler.create(this, () => {
                    this.ticketLogin(true);
                }));
                this._client = yalla.ClientBrower.instance;
            } else {
                this.ticketLogin(true);
            }
        }
        public sendSelectChess(chessData: { idx: number, order: number, color: number }) {
            if (!this._user) return;
            this.sendMsg(yalla.data.jackaro.Command.SELECT_POKER, 'SelectChessReq', {
                idx: this._user.idx,
                roomId: this._room.roomId,
                chess: chessData
            }, BusinessOrderCode.jackaro_select_chess);
        }
        public sendSelectItem(poker: number, itemType: number) {
            if (!this._user) return;
            yalla.Debug.filterLogMore(this.logTag + '请求选项 SelectItemReq:', poker, itemType);
            this.sendMsg(yalla.data.jackaro.Command.SELECT_POKER, 'SelectItemReq', {
                idx: this._user.idx,
                roomId: this._room.roomId,
                poker: poker,
                itemType: itemType
            }, BusinessOrderCode.jackaro_select_item);
        }
        public sendSelectPoker(poker: number, flyToChessPanel: boolean = false) {
            if (!this._user) return;
            if (!this._room) return;
            if (!JackaroGamePlayerManager.instance.isMyActive()) {
                return;
            }
            this.sendMsg(yalla.data.jackaro.Command.SELECT_POKER, 'SelectPokerReq', {
                idx: this._user.idx,
                roomId: this._room.roomId,
                poker: poker,
                flyToChessPanel: flyToChessPanel
            }, BusinessOrderCode.jackaro_selectPoker);
        }
        public sendPlayPoker(poker: number, commonMoveData: yalla.data.jackaro.MoveChessDataInterface) {
            if (!this._user) return;
            if (!this._room) return;
            let curOperatePlayerIdx = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx();
            if (curOperatePlayerIdx != this._user.idx) {
                return;
            }
            let gamePlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(this._user.idx);
            if (gamePlayer && gamePlayer.playerStatus != yalla.data.jackaro.PlayerStatus.WAIT_OUT && gamePlayer.playerStatus != yalla.data.jackaro.PlayerStatus.WAIT_DISCARD) {
                return;
            }
            this._curPlayCard = poker;
            // yalla.Debug.logMore("sendPlayPoker poker: " + poker + "commonMoveData: " + JSON.stringify(commonMoveData));
            this.sendMsg(yalla.data.jackaro.Command.PLAY_POKER, 'PlayPokerReq', {
                idx: this._user.idx,
                roomId: this._room.roomId,
                poker: poker,
                commonMove: commonMoveData
            }, BusinessOrderCode.jackaro_playPoker);
            this._operateDataMap[JackaroOperateMsgType.PLAY_POKER] = {
                poker: poker,
                commonMoveData: commonMoveData
            };
            //1.4.5 出牌后无法加时
            this.setIsTriggerPlayCard(true);
        }
        public sendExchangeChess(poker: number, data: any) {
            if (!this._user) return;
            // //出牌7如果等待时收到服务器托管推送，再次出牌时，需要取消托管
            this.sendMsg(yalla.data.jackaro.Command.PLAY_POKER, 'PlayPokerReq', {
                idx: this._user.idx,
                roomId: this._room.roomId,
                poker: poker,
                exchange: {
                    selfData: data[0],
                    otherData: data[1]
                }
            }, BusinessOrderCode.jackaro_playPoker);
            this._operateDataMap[JackaroOperateMsgType.EXCHANGE_POKER] = {
                poker: poker,
                data: data
            };
            //1.4.5 交换牌后无法加时
            this.setIsTriggerPlayCard(true);
        }
        public sendFreeMoveChess(poker: number, data: any) {
            if (!this._user) return;
            if (!this._room) return;
            //1.4.5.0 存在无效数据发送到服务器的情况
            if (data && data.firstData && data.secondData && data.firstData.order == 0 && data.secondData.order == 0) {
                yalla.Debug.logMore("sendFreeMoveChess", "no data");
                return;
            }
            //出牌7如果等待时收到服务器托管推送，再次出牌时，需要取消托管
            this.sendMsg(yalla.data.jackaro.Command.PLAY_POKER, 'PlayPokerReq', {
                idx: this._user.idx,
                roomId: this._room.roomId,
                poker: poker,
                pokerServen: data
            }, BusinessOrderCode.jackaro_playPoker);
            this._operateDataMap[JackaroOperateMsgType.FREE_MOVE_POKER] = {
                poker: poker,
                data: data
            };
            //1.4.5 出牌7后无法加时，如果两颗的话，其实第一颗移动时已经不能触发加时
            this.setIsTriggerPlayCard(true);
        }
        /**
         * 触发主动发送出牌请求
         * @param isTrigger 
         */
        public setIsTriggerPlayCard(isTrigger: boolean) {
            this._isTriggerPlayCard = isTrigger;
            if (isTrigger) {
                this._isTriggerPlayCardOutRound = JackaroGamePlayerManager.instance.getOutRound();
            }
        }
        public checkIsTriggerPlayCard() {
            let curOutRound = JackaroGamePlayerManager.instance.getOutRound();
            if (curOutRound != this._isTriggerPlayCardOutRound) {
                this.setIsTriggerPlayCard(false);
            }
        }

        public getIsTriggerPlayCard(): boolean {
            return this._isTriggerPlayCard;
        }

        public sendDiscardPoker(data: Array<yalla.data.jackaro.Poker>, isDiscardAll: boolean = false) {
            if (!this._user) return;
            // JackaroCardManger.instance.setRecordDiscardPoker(data);
            if (isDiscardAll) {
                data = [];
            }
            this.sendMsg(yalla.data.jackaro.Command.DISCARD_POKER, 'DiscardPokerReq', {
                idx: this._user.idx,
                roomId: this._room.roomId,
                discardPoker: data,
                discardAll: isDiscardAll
            }, BusinessOrderCode.jackaro_discardPoker);
            this._operateDataMap[JackaroOperateMsgType.DISCARD_POKER] = {
                data: data,
                isDiscardAll: isDiscardAll
            };
            //1.4.5 弃牌后无法加时
            this.setIsTriggerPlayCard(true);
        }
        public sendAssignNextPlayerDiscard(poker: yalla.data.jackaro.Poker) {
            this.sendMsg(yalla.data.jackaro.Command.ASSIGN_NEXT_PLAYER_DISCARD, 'AssignNextPlayerDiscardReq', { poker: poker, idx: this._user.idx, roomId: this._room.roomId }, BusinessOrderCode.jackaro_assignNextPlayerDiscard);

            this._operateDataMap[JackaroOperateMsgType.ASSIGN_NEXT_PLAYER_DISCARD] = {
                poker: poker
            };
            //1.4.5 出牌10强制弃牌时也不能加时
            this.setIsTriggerPlayCard(true);
        }
        public sendDrawNextPlayerPoker(poker: yalla.data.jackaro.Poker, drawnPokerIndex: number) {
            this.sendMsg(yalla.data.jackaro.Command.DRAW_NEXT_PLAYER_POKER, 'DrawNextPlayerPokerReq', { poker: poker, drawnPokerIndex: drawnPokerIndex, idx: this._user.idx, roomId: this._room.roomId }, BusinessOrderCode.jackaro_drawNextPlayerDiscard);
            this._operateDataMap[JackaroOperateMsgType.DRAW_NEXT_PLAYER_POKER] = {
                poker: poker,
                drawnPokerIndex: drawnPokerIndex
            };
        }
        public syncTimeReq() {
            this.sendTime = Date.now();
            this.sendMsg(yalla.data.jackaro.Command.SYNC_TIME, 'SyncTimeReq', { idx: this._user.idx, roomId: this._room.roomId }, BusinessOrderCode.jackaro_syncTime);
        }
        public sendSyncGameDataReq() {
            yalla.Debug.logMore("sendSyncGameDataReq");
            this.sendMsg(yalla.data.jackaro.Command.SYNC_GAME_DATA, 'SyncGameDataReq', { idx: this._user.idx, roomId: this._room.roomId }, BusinessOrderCode.jackaro_syncGameData);
        }
        public sendCancelTrust() {
            this.sendMsg(yalla.data.jackaro.Command.CANCEL_TRUST, 'CancelTrustReq', { idx: this._user.idx, roomId: this._room.roomId }, BusinessOrderCode.jackaro_cancelTrust);
        }
        public sendExtendTime() {
            this.sendMsg(yalla.data.jackaro.Command.EXTEND_TIME, 'ExtendTimeReq', { idx: this._user.idx, roomId: this._room.roomId }, BusinessOrderCode.jackaro_extendTime);
        }
        /**
         * 退出房间
         * 先通知网关断流（通知加入其它游戏、主动退出）
         */
        public quitRoom(): void {
            this.sendMsg(yalla.data.jackaro.Command.QUIT_ROOM, 'QuitGameReq', { idx: this._user.idx, roomId: this._room.roomId }, BusinessOrderCode.jackaro_quitRoom);
            Laya.timer.once(2000, this, this.doQuitGame);
            // this.clearStream();
        }

        /**
         * 快捷短语聊天
         */
        public sendChat(msgResult: any = {}, idx: number = 0, messageType: yalla.data.jackaro.GameChatMessageType, channelType: yalla.data.jackaro.GameChatChannelType): void {
            msgResult.idx = idx ? idx : this._user.idx;
            // msgResult.roomid = this._room.roomId;
            msgResult.messageType = messageType;
            msgResult.channelType = channelType;
            this.sendMsg(yalla.data.jackaro.Command.GAME_CHAT, 'GameChatReq', msgResult, BusinessOrderCode.jackaro_playerChatToAll);
        }

        /**
         * 获取金币接口  0金币 1钻石
         */
        public flushCurrencyRequest(type: number = 0): void {
            // if (!this.IsUpdatePlayerCoin) return;
            this.sendMsg(yalla.data.jackaro.Command.FLUSH_CURRENCY, 'FlushCurrencyReq', {
                idx: this._user.idx,
                roomId: this._room.roomId
            }, BusinessOrderCode.jackaro_flushCurrencyReq);
        }
        /**选择棋子移动 */
        sendChooseMsg() {
            if (!this._user) return;
            this.sendMsg(yalla.data.jackaro.Command.GAME_OPERATE, 'GameOperateRequest', {
                // gameOperateRequest: {
                idx: this._user.idx,
                msgindex: this.msgProgress,
                op: yalla.data.GameOperate.CHESS_MOVE,
                // }
            }, BusinessOrderCode.jackaro_gameOperate);
        }

        /**
         * 设置聊天屏蔽  
         * @param chatOffType false取消屏蔽   true设置屏蔽
         */
        public sendChatOff(isOpenChatOff: number): void {
            if (!this._user) return;
            this.sendMsg(yalla.data.jackaro.Command.SET_CHAT, 'SetChatReq', {
                // gameOperateRequest: {
                idx: this._user.idx,
                roomId: this._room.roomId,
                open: isOpenChatOff
                // }
            }, BusinessOrderCode.jackaro_set_chat);
            this._user.updateChatOff(isOpenChatOff);
            ludo.muteSpectator.isMute = isOpenChatOff;
            //TODO::自己操作的话不等服务端返回，避免因消息不返回，设置的状态和玩家头像上的状态不一致
            yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_ChatOFF_My, { idx: this.user.idx, open: isOpenChatOff });
        }

        public getGiftCnfList(): void {
            if (!this._user) return;
            this.sendMsg(yalla.data.jackaro.Command.GAME_GIFT_CONFIG, 'GameGiftConfigReq', {
                roomId: this._room.roomId
            }, BusinessOrderCode.jackaro_gameGiftCnfList);
        }

        public sendGift(giftId: number, rcvIdxs: Array<any>): void {
            if (!this._user) return;
            this.sendMsg(yalla.data.jackaro.Command.GAME_GIFT_SEND, 'GameGiftSendReq', {
                // gameGiftSendRequest: {
                idx: this._user.idx,
                roomId: this._room.roomId,
                giftId: giftId,
                rcvIdx: rcvIdxs
                // }
            }, BusinessOrderCode.jackaro_gameGiftSend);
        }
        public sendTestExchange(sourcePoker: number, targetPoker: number) {
            if (!this._user) return;
            this.sendMsg(yalla.data.jackaro.Command.TEST_EXCHANGE_POKER, 'TestExchangePokerReq', {
                idx: this._user.idx,
                roomId: this._room.roomId,
                sourcePoker: sourcePoker,
                targetPoker: targetPoker
            }, BusinessOrderCode.jackaro_testExchangePoker);
            this.recordTestExchangePoker = {
                sourcePoker: sourcePoker,
                targetPoker: targetPoker
            };
        }

        /**
         * 获取语音房间token
         */
        public getToken() {
            if (!this._user) return;
            this.sendMsg(yalla.data.jackaro.Command.VOICE_TOKEN, 'VoiceTokenReq', {
                roomId: this._room.roomId,
                version: yalla.Global.Account.version,
                type: yalla.Global.Account.voiceType
            }, BusinessOrderCode.jackaro_voiceToken);
        }
        //-----------------------------------------handle=------------------------------------
        //GameGiftCnfListRespone、 agoraTokenResponse、 zegoTokenResponse、flushCurrencyResponse 走双向流，其他仍然是jackaroLadderMessage保持不变
        private processMsg(businessOrder, cmd, data) {
            var msg;
            switch (businessOrder) {
                case BusinessOrderCode.jackaro_ticketLogin:
                    this.doubleStreamResponse(cmd, data);
                    break;
                case BusinessOrderCode.jackaro_message:
                    this.doubleStreamResponse(cmd, data);
                    break;
                case BusinessOrderCode.jackaro_getAgoraToken:
                    msg = this.decodeMsg(data, "GetAgoraTokenResponse");
                    if (!msg) return;
                    this.event(yalla.data.jackaro.EnumCmd.Game_GetAgoraToken, [90, msg.token, msg.cName]);
                    break;
                case BusinessOrderCode.jackaro_getZegoToken:
                    msg = this.decodeMsg(data, "GetZegoTokenResponse");
                    if (!msg) return;
                    this.event(yalla.data.jackaro.EnumCmd.Game_GetZegoToken, [92, msg.token, msg.cName]);
                    break;
                case BusinessOrderCode.jackaro_voiceToken:
                    msg = this.decodeMsg(data, "VoiceTokenRsp");
                    if (!msg) return;
                    msg.command = Command.VOICE_TOKEN;
                    this.voiceTokenResponse(msg);
                    break;
                case BusinessOrderCode.jackaro_gameGiftCnfList:
                    msg = this.decodeMsg(data, "GameGiftConfigRsp");
                    if (!msg) return;
                    msg.command = Command.GAME_GIFT_CONFIG;
                    this.gameGiftConfigResponse(msg);
                    break;
            }
        }
        /**
         *检测房间用户是否已满
         * Command.TICKET_LOGIN 
         * @param cmd 
         * @param data 
         */
        private doubleStreamResponse(cmd, data): void {
            var d;
            var msg;
            switch (cmd) {
                case Command.TICKET_LOGIN:
                    this.clearQueue();
                    d = this._client.decodeMsg(data, "JackarooGameStreamRsp");
                    if (!d || !d.data) return;
                    msg = this._client.decodeMsg(d.data, "TicketLoginRsp");
                    this.loginResponse(msg);
                    this._isReady = true;

                    this._readyMsgList.forEach(msgs => {
                        this.processMsg(msgs.businessOrder, msgs.cmd, msgs.msg);
                    })
                    this._readyMsgList = [];
                    break;
                case Command.GAME_PLAYER_LOGIN_PUB:
                    msg = this.decodeMsg(data, "GamePlayerLoginPub", true);
                    if (!msg) return;
                    this.gamePlayerLoginPubResponse(msg);
                    break;
                case Command.GAME_START:
                    // msg = this.decodeMsg(data, "RestoreGameRsp", true);
                    msg = this.decodeMsg(data, "GameStartPub", true);
                    if (!msg) return;
                    this.gameStartResponse(msg);
                    break;
                case Command.CHANGE_PLAYER:
                    msg = this.decodeMsg(data, "ChangePlayerPub", true);
                    if (!msg) return;
                    this.changePlayerResponse(msg);
                    break;
                case Command.SELECT_POKER:
                    msg = this.decodeMsg(data, "SelectPokerReq", true);
                    if (!msg) return;
                    // this.selectPokerResponse(msg);
                    break;
                case Command.PLAY_POKER:
                    msg = this.decodeMsg(data, "PlayPokerRsp", true);
                    if (!msg) return;
                    //TODO::4.9 自己的出牌消息加入队列（避免切后台、断线重连表现异常）
                    // this.pushMsg(msg);
                    this.playPokerResponse(msg);
                    break;
                case Command.MOVE_CHESS_PUB:
                    msg = this.decodeMsg(data, "MoveChessPub", true);
                    if (!msg) return;
                    this.moveChessPubResponse(msg);
                    break;
                case Command.EXCHANGE_CHESS:
                    msg = this.decodeMsg(data, "ExchangeChessRsp", true);
                    if (!msg) return;
                    this.exchangeChessResponse(msg);
                    break;
                case Command.EXCHANGE_CHESS_PUB:
                    msg = this.decodeMsg(data, "ExchangeChessPub", true);
                    if (!msg) return;
                    this.exchangeChessPubResponse(msg);
                    break;
                case Command.ASSIGN_NEXT_PLAYER_DISCARD:
                    msg = this.decodeMsg(data, "AssignNextPlayerDiscardRsp", true);
                    if (!msg) return;
                    this.assignNextPlayerDiscardResponse(msg);
                    break;

                case Command.DISCARD_POKER:
                    msg = this.decodeMsg(data, "DiscardPokerRsp", true);
                    if (!msg) return;
                    this.discardPokerResponse(msg);
                    break;
                case Command.DEAL_POKER:
                    msg = this.decodeMsg(data, "DealPokerPub", true);
                    if (!msg) return;
                    this.dealPokerResponse(msg);
                    break;
                case Command.POKER_SEVEN_MOVE_PUB:
                    msg = this.decodeMsg(data, "PokerSevenMovePub", true);
                    if (!msg) return;
                    this.pokerSevenMovePubResponse(msg);
                    break;
                case Command.IMPACT_MOVE_PUB:
                    msg = this.decodeMsg(data, "ImpactMovePub", true);
                    if (!msg) return;
                    this.impactMovePubResponse(msg);
                    break;
                case Command.DRAW_NEXT_PLAYER_POKER:
                    msg = this.decodeMsg(data, "DrawNextPlayerPokerRsp", true);
                    if (!msg) return;
                    this.drawNextPokerResponse(msg);
                case Command.DRAW_POKER_PUB:
                    msg = this.decodeMsg(data, "DrawPokerPub", true);
                    if (!msg) return;
                    this.drawPokerPubResponse(msg);
                    break;
                case Command.TRUST_PUB:
                    msg = this.decodeMsg(data, "TrustPub", true);
                    yalla.Debug.zzf(msg);
                    if (!msg) return;
                    this.trustPubResponse(msg);
                    break;
                case Command.SYNC_GAME_DATA:
                    msg = this.decodeMsg(data, "SyncGameDataRsp", true);
                    if (!msg) return;
                    this.syncGameDataResponse(msg);
                    break;
                case Command.FINISH_DISCARD_PUB:
                    msg = this.decodeMsg(data, "FinishDiscardPub", true);
                    if (!msg) return;
                    this.finishDiscardPubResponse(msg);
                    break;
                case Command.FORCED_DISCARD_PUB:
                    msg = this.decodeMsg(data, "ForcedDiscardPub", true);
                    if (!msg) return;
                    this.forcedDiscardPubResponse(msg);
                    break;
                case Command.CANCEL_TRUST:
                    msg = this.decodeMsg(data, "CancelTrustRsp", true);
                    if (!msg) return;
                    this.cancelTrustResponse(msg);
                    break;
                case Command.QUIT_ROOM:
                    msg = this.decodeMsg(data, "QuitGameRsp", true);
                    if (!msg) return;
                    this.quitGameResponse(msg);
                    break;
                case Command.EXTEND_TIME:
                    msg = this.decodeMsg(data, "ExtendTimeRsp", true);
                    if (!msg) return;
                    this.extendTimeResponse(msg);
                    break;
                case Command.EXTEND_TIME_PUB:
                    msg = this.decodeMsg(data, "ExtendTimePub", true);
                    if (!msg) return;
                    this.extendTimePubResponse(msg);
                    break;
                case Command.QUIT_GAME_PUB:
                    msg = this.decodeMsg(data, "QuitGamePub", true);
                    if (!msg) return;
                    this.quitGamePubResponse(msg);
                    break;
                case Command.SETTLEMENT:
                    msg = this.decodeMsg(data, "SettlementPub", true);
                    if (!msg) return;
                    this.settlementResponse(msg);
                    break;
                case Command.KICK_PLAYER:
                    msg = this.decodeMsg(data, "KickPlayerPub", true);
                    if (!msg) return;
                    this.kickPlayerResponse(msg);
                    break;
                case Command.GAME_CHAT_PUB:
                    msg = this.decodeMsg(data, "GameChatPub", true);
                    if (!msg) return;
                    this.event(EnumCmd.Game_Chat, msg);
                    break;
                case Command.GAME_GIFT_PUB:
                    msg = this.decodeMsg(data, "GameGiftSendPub", true);
                    if (!msg) return;
                    msg.command = Command.GAME_GIFT_PUB;
                    this.gameGiftSendResponse(msg);
                    break;
                case Command.GAME_GIFT_SEND:
                    msg = this.decodeMsg(data, "GameGiftSendPub", true);
                    if (!msg) return;
                    msg.command = Command.GAME_GIFT_SEND;
                    this.gameGiftSendResponse(msg);
                    break;
                case Command.OUT_GAME:
                    msg = this.decodeMsg(data, "OutGameResponse", true);
                    if (!msg) return;
                    this.outGameResponse(msg);
                    break;
                case Command.END_EXCEPTION_GAME_PUB:
                    msg = this.decodeMsg(data, "EndExceptionGamePub", true);
                    if (!msg) return;
                    this.endExceptionGamePubResponse(msg);
                    break;
                case Command.SET_CHAT_PUB:
                    msg = this.decodeMsg(data, "SetChatPub", true);
                    if (!msg) return;
                    yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_ChatOFF, msg);
                    break;
                case Command.FLUSH_CURRENCY:
                    msg = this.decodeMsg(data, "FlushCurrencyRsp", true);
                    if (!msg) return;
                    this.flushCurrencyResponse(msg);
                    break;
                case Command.TEST_EXCHANGE_POKER:
                    msg = this.decodeMsg(data, "TestExchangePokerRsp", true);
                    if (!msg) return;
                    this.testExchangePokerResponse(msg);
                    break;
                case Command.TEST_EXCHANGE_POKER_PUB:
                    msg = this.decodeMsg(data, "TestExchangePokerPub", true);
                    if (!msg) return;
                    this.testExchangePokerPubResponse(msg);
                    break;
                case Command.SYNC_TIME:
                    msg = this.decodeMsg(data, "SyncTimeRsp", true);
                    if (!msg) return;
                    this.syncTimeResponse(msg);
                    break;
            }
        }
        private decodeMsg(data: any, messageName: string, isJackaroStreamResponse: boolean = false): void {
            var msg;
            if (isJackaroStreamResponse) {
                var d = this._client.decodeMsg(data, "JackarooGameStreamRsp");
                if (!d || !d.data) return;
                msg = this._client.decodeMsg(d.data, messageName);
            } else {
                msg = this._client.decodeMsg(data, messageName);
            }
            return msg;
        }
        private GameResponse(code: any, msg: any, cmd, businessOrder?): void {
            // yalla.Debug.log("JackaroUserService GameResponse:code:" + code + "msg:" + msg + "cmd:" + cmd + "businessOrder:" + businessOrder + "yalla.Global.IsGameOver:" + yalla.Global.IsGameOver + "yalla.Global.gameType:" + yalla.Global.gameType + "this._isReady:" + this._isReady);
            if (yalla.Global.gameType != GameType.JACKAROO) return;
            if (yalla.Global.IsGameOver) return;   //TODO:;条件需要的，测试阶段先去掉
            if (cmd == 0 || !msg) return;
            if (!this._isReady) {
                if (!this._readyMsgList) this._readyMsgList = [];
                if (cmd > Command.TICKET_LOGIN) {//没有收到房间信息之前除登录和房间信息之外任何消息暂存不处理
                    this._readyMsgList.push({
                        cmd: cmd,
                        msg: msg,
                        businessOrder: businessOrder
                    })
                    return;
                }
            }
            this.processMsg(businessOrder, cmd, msg);
        }
        /**
         * 成功登录游戏
         * @param d 
         */
        private loginResponse(msg: any): void {
            // yalla.Debug.logMore("loginResponse:" + JSON.stringify(msg));
            msg.command = Command.TICKET_LOGIN;
            Laya.timer.clear(this, this.onLoginRequestTimeout);
            this._curSocketClose = false;
            this._loginRequestTimes = 0;
            if (!msg) {
                yalla.common.WaringDialog.showResultError(yalla.data.ErrorCode.NONE_ERROE, () => {
                    this.backHall(true);
                });
                return;
            }
            yalla.common.connect.ReconnectControl.instance.connectSucss();
            if (msg.result == yalla.data.jackaro.OperateResult.SUC && msg.code == yalla.data.jackaro.ErrorCode.NONE_ERROR) {
                // 注意 这里只是更新记录数据
                this._room.endpointNewCode = msg.endpointNewCode || 0;
                JackaroChessOrbitManager.instance.setAllowJumpOverConsecutiveChess(msg.allowAcross2OtherChess);
                //更新玩家自己的数据，包括金币、钻石、是否首充、棋盘皮肤 棋子皮肤id等
                this._user.update(msg.playerSelfData);
                // this._user.pokerSkinId = 5000999;
                if (this._user.pokerSkinId) {
                    JackaroCardManger.instance.setCardBackSkin(yalla.Global.Account.idx, this._user.pokerSkinId);
                }
                if (msg.selfGameData) {
                    //更新仅玩家自己可见的数据，包括延时次数
                    JackaroGamePlayerManager.instance.updatePlayerSelfGameData(msg.selfGameData);
                }
                var restoreGame = msg.restoreGame;
                //恢复数据
                if (restoreGame) {
                    //总的思考时间,单位秒,原先20s
                    if (restoreGame.hasOwnProperty('totalThinkingTime')) {
                        this.defaultOperateTime = Number(restoreGame.totalThinkingTime) * 1000;
                    }
                    //更新每次延时时长
                    if (restoreGame.hasOwnProperty('extendTime')) {//花费钻石后每次延长的时间，单位秒;
                        this.addOperateTime = Number(restoreGame.extendTime) * 1000;
                    }
                    if (restoreGame.playShowInfo) {
                        let playerShowInfo = restoreGame.playShowInfo as Record<string, any>;
                        //更新玩家展示信息 包括头像框 颜色 昵称等
                        for (let idx in playerShowInfo) {
                            JackaroGamePlayerManager.instance.updatePlayerShowInfo(playerShowInfo[idx].playerShowData as yalla.data.jackaro.PlayerShowInfoInterface);
                            JackaroGamePlayerManager.instance.updatePlayerShowGameDataList(parseInt(idx), playerShowInfo[idx].showGameData as yalla.data.jackaro.PlayerShowGameDataInterface);
                        }
                    }
                    if (restoreGame.gameData) {
                        let gameData = restoreGame.gameData;
                        //更新玩家游戏数据 包括玩家手牌数据 玩家状态 是否要求被强制弃牌等数据
                        JackaroGamePlayerManager.instance.updatePlayerDataMap(gameData.playerDataMap);
                        //更新房间信息 包括本局筹码数量 模式 游戏类型 棋盘信息
                        this._room.updateRoomInfo(gameData);
                        if (gameData.chessPanel) {
                            this._room.setGameStatus(gameData.chessPanel.gameStatus);
                        }
                    }
                }
                yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
            } else {
                yalla.Native.instance.removeMatchView();
                yalla.Global.IsGameOver = true;
                if (msg && msg.code == yalla.data.ErrorCode.TRUST_PUNISHMENT_KICKED_OUT) {
                    yalla.DialogManager.instance.showDialog('', yalla.data.TranslationD.Game_LoginOut_KickOut_Content, [yalla.data.TranslationD.Game_LoginOut_Btn], true);
                } else {
                    yalla.common.WaringDialog.showResultError(msg.code, () => {
                        //登录失败时，直接返回大厅
                        this.backHall(true, true);
                    });
                }
            }
            this.syncTimeReq();
        }
        private gamePlayerLoginPubResponse(msg: any): void {
            msg.command = Command.GAME_PLAYER_LOGIN_PUB;
            JackaroGamePlayerManager.instance.updatePlayerShowInfo(msg.playerShowData.playerShowData as yalla.data.jackaro.PlayerShowInfoInterface);
            if (msg.playerShowData.playerShowData.idx) {
                JackaroGamePlayerManager.instance.updatePlayerShowGameDataList(msg.playerShowData.playerShowData.idx, msg.playerShowData.showGameData as yalla.data.jackaro.PlayerShowGameDataInterface);
            }
            yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
        }
        private gameStartResponse(msg: any): void {
            msg.command = Command.GAME_START;
            if (msg.gameData) {
                this._room.updateRoomInfo(msg.gameData);
                if (msg.gameData && msg.gameData.playerDataMap) {
                    JackaroGamePlayerManager.instance.updatePlayerDataMap(msg.gameData.playerDataMap);
                }
                if (msg.gameData.chessPanel) {
                    this._room.setGameStatus(msg.gameData.chessPanel.gameStatus);
                    JackaroGamePlayerManager.instance.changePlayer(msg.gameData.chessPanel);
                }
            }
            this.pushMsg(msg);
        }
        /**
         * 改变玩家
         * @param msg 
         */
        private changePlayerResponse(msg: any): void {
            msg.command = Command.CHANGE_PLAYER;
            this.pushMsg(msg);
            //收到改变玩家时 重发消息置空
            this.clearOperateDataMap();

        }
        public handleChangePlayerData(msg: any) {
            // var lastOperatePlayerIdx = JackaroGamePlayerManager.instance.lastOperatePlayerIdx;
            // {{ YUM: [新增] - 新回合开始时重置抽牌奖励触发状态 }}1.4.5判断 2 个前后顺序问题
            if (yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode()) {
                JackaroCardReplenishManager.instance.onNewRoundStart(msg.chessPanel.outRound || 0);
            }
            JackaroCardManger.instance.selfCanTouchCard = true;
            JackaroChessManager.instance.handleChangePlayerChessMove();
            // {{ YY: [新增] - 在turnClear()清理数据之前，保存当前玩家的奖励牌触发状态 }}
            const currentPlayerIdx = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx();
            const hasRewardPoker = JackaroChessManager.instance.checkPreviewRewardPoker('HANDLE_CHANGE_PLAYER_CHECK', currentPlayerIdx);
            JackaroGamePlayerManager.instance.setTempRewardPokerTriggered(hasRewardPoker);

            JackaroChessManager.instance.turnClear();
            this._room.curRound++;
            JackaroGamePlayerManager.instance.changePlayer(msg.chessPanel);

            //打印击杀数据
            if (msg.chessPanel && msg.chessPanel.killedResult) {
                this._room.killedResult = msg.chessPanel.killedResult;
            }
            JackaroCardManger.instance.changePlayerClear();
            let curOperatePlayerIdx = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx();
            TipsViewManager.getInstance().turnClear(curOperatePlayerIdx);
            JackaroCardManger.instance.setSendPokerRound(msg.chessPanel.sendPokerRound);
            // {{ YUM: [新增] - 新回合开始时重置手牌视图 }} 1.4.5判断 2 个前后顺序问题
            if (yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode()) {
                JackaroCardManger.instance.resetHandUpdateCardPos(curOperatePlayerIdx);
            }
            // JackaroGamePlayerManager.instance.resetAddExpPlayerIdxList();
            if (JackaroGamePlayerManager.instance.isMyActive()) {
                JackaroGamePlayerManager.instance.setAllowMoveTeammateChess(msg.allowMoveTeammateChess);

            }
            // 设置抽取奖励牌 1.4.5版本新增
            if (msg.drawRewardPoker) {
                JackaroCardReplenishManager.instance.setDrawRewardPoker(msg.drawRewardPoker);
                JackaroCardManger.instance.setDrawRewardPoker(msg.drawRewardPoker);
            }

            // JackaroGamePlayerManager.instance.setSelfAddExp(msg.selfAddExp);
            JackaroChessManager.instance.selfAddExp = Boolean(msg.selfAddExp);
            // JackaroGamePlayerManager.instance.setTeammateAddExp(msg.teammateAddExp);
            // JackaroCardManger.instance.resetCheckDiscardPoker();
            let currentPlayer = msg.chessPanel.currentPlayer;
            let currentPlayerView = JackaroGamePlayerManager.instance.getGamePlayerByIdx(currentPlayer);
            if (currentPlayerView) {
                currentPlayerView.playerStatus = msg.currentPlayerStatus;
            }
            this._room.setGameStatus(msg.chessPanel.gameStatus);
            if (currentPlayer == yalla.Global.Account.idx) {
                if (msg.currentPlayerStatus == yalla.data.jackaro.PlayerStatus.WAIT_DISCARD && msg.drawnPoker) {
                    yalla.data.jackaro.JackaroUserService.instance.sendDiscardPoker([msg.drawnPoker]);
                } else {
                    JackaroCardManger.instance.handleAllowPokerData(msg.allowPokerData as Record<string, yalla.data.jackaro.PokerMoveDataInterface>);
                }
            } else {
                JackaroChessOrbitManager.instance.resetIgnoreChessId();
                JackaroCardManger.instance.refreshChessOrbit();
            }
            //1.4.5 增加手牌数量校验
            if (yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode()) {
                JackaroCardManger.instance.checkPlayerHandPokersNum(msg.handPokerAmount);
            }
            this.setIsTriggerPlayCard(false);
            this.nextMsg();
        }

        public playPokerResponse(msg: any): void {
            msg.command = Command.PLAY_POKER;
            this._operateDataMap[JackaroOperateMsgType.PLAY_POKER] = null;
            this._operateDataMap[JackaroOperateMsgType.FREE_MOVE_POKER] = null;

            if (msg.result == yalla.data.jackaro.OperateResult.SUC && msg.code == yalla.data.jackaro.ErrorCode.NONE_ERROR) {
                this._curPlayCard = msg.playingPoker;
                if (this._curPlayCard) {
                    JackaroCardManger.instance.handlePlayPoker(yalla.Global.Account.idx, this._curPlayCard);
                }
            } else {
                // yalla.Debug.logMore("playPokerResponse 出牌失败", msg);
                //todo 出牌失败时，请求刷新牌桌
                this.sendSyncGameDataReq();
            }
        }
        private moveChessPubResponse(msg: any): void {
            // yalla.Debug.log('==Userservice.moveChessPubResponse===');
            msg.command = Command.MOVE_CHESS_PUB;
            this._operateDataMap[JackaroOperateMsgType.PLAY_POKER] = null;
            this._operateDataMap[JackaroOperateMsgType.FREE_MOVE_POKER] = null;
            this.pushMsg(msg);
        }
        private exchangeChessResponse(msg: any): void {
            msg.command = Command.EXCHANGE_CHESS;
            this._operateDataMap[JackaroOperateMsgType.EXCHANGE_POKER] = null;
            JackaroChessManager.instance.handleExchangeChess(msg);
            yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
        }
        private exchangeChessPubResponse(msg: any): void {
            msg.command = Command.EXCHANGE_CHESS_PUB;
            this._operateDataMap[JackaroOperateMsgType.EXCHANGE_POKER] = null;
            this.pushMsg(msg);
        }
        private discardPokerResponse(msg: any): void {
            msg.command = Command.DISCARD_POKER;
            this._operateDataMap[JackaroOperateMsgType.DISCARD_POKER] = null;
            if (msg.result == yalla.data.jackaro.OperateResult.SUC && msg.code == yalla.data.jackaro.ErrorCode.NONE_ERROR) {
                TipsViewManager.getInstance().hideTip(TipViewType.DISCARD_ALL_CARD);
                TipsViewManager.getInstance().hideTip(TipViewType.DISCARD_CARD);
            } else {
                //todo 指定下一个玩家弃牌失败时，请求刷新牌桌
                this.sendSyncGameDataReq();
            }
        }
        private assignNextPlayerDiscardResponse(msg: any): void {
            msg.command = Command.ASSIGN_NEXT_PLAYER_DISCARD;
            this._operateDataMap[JackaroOperateMsgType.ASSIGN_NEXT_PLAYER_DISCARD] = null;
            if (!(msg.result == yalla.data.jackaro.OperateResult.SUC && msg.code == yalla.data.jackaro.ErrorCode.NONE_ERROR)) {
                //todo 指定下一个玩家弃牌失败时，请求刷新牌桌
                this.sendSyncGameDataReq();
            }
        }
        private dealPokerResponse(msg: any): void {
            msg.command = Command.DEAL_POKER;
            this._room.turnClear();

            this.pushMsg(msg);
        }
        private pokerSevenMovePubResponse(msg: any): void {
            msg.command = Command.POKER_SEVEN_MOVE_PUB;
            // yalla.Debug.logMore("pokerSevenMovePubResponse" + JSON.stringify(msg));
            this.pushMsg(msg);
        }
        private impactMovePubResponse(msg: any): void {
            msg.command = Command.IMPACT_MOVE_PUB;
            this.pushMsg(msg);
        }
        private drawNextPokerResponse(msg: any): void {
            msg.command = Command.DRAW_NEXT_PLAYER_POKER;
            this._operateDataMap[JackaroOperateMsgType.DRAW_NEXT_PLAYER_POKER] = null;
            if (!(msg.result == yalla.data.jackaro.OperateResult.SUC && msg.code == yalla.data.jackaro.ErrorCode.NONE_ERROR)) {
                //抽牌失败时，请求刷新牌桌
                this.sendSyncGameDataReq();
            }
        }
        /**抽取别人的牌 */
        private drawPokerPubResponse(msg: any): void {
            msg.command = Command.DRAW_POKER_PUB;
            this._operateDataMap[JackaroOperateMsgType.DRAW_NEXT_PLAYER_POKER] = null;
            this.pushMsg(msg);
        }
        private settlementResponse(msg: any): void {
            yalla.Debug.logMore("==Userservice.settlementResponse===收到服务器消息");
            msg.command = Command.SETTLEMENT;
            this.pushMsg(msg);
            // this.nextMsg();
            // yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
        }
        private voiceTokenResponse(msg: any) {
            msg.command = Command.VOICE_TOKEN;
            if (msg.result == yalla.data.jackaro.OperateResult.SUC && msg.code == yalla.data.jackaro.ErrorCode.NONE_ERROR) {
                // VoiceType type = 3;//语音类型
                // string token = 4;//token
                // string cName = 5;//频道名
                if (msg.type == VoiceType.Agora) {
                    // VoiceService.instance.handleGetAgoraToken(msg);
                    this.event(yalla.data.jackaro.EnumCmd.Game_GetAgoraToken, [90, msg.token, msg.cName]);
                } else if (msg.type == VoiceType.Zego) {
                    // VoiceService.instance.handleGetZegoToken(msg);
                    this.event(yalla.data.jackaro.EnumCmd.Game_GetZegoToken, [92, msg.token, msg.cName]);
                }
            }
        }
        private syncGameDataResponse(msg: any): void {
            msg.command = Command.SYNC_GAME_DATA;
            if (msg.result == yalla.data.jackaro.OperateResult.SUC && msg.code == yalla.data.jackaro.ErrorCode.NONE_ERROR) {
                this.queue.reset();
                yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
            }
        }
        /**主动弃牌推送 */
        private finishDiscardPubResponse(msg: any): void {
            msg.command = Command.FINISH_DISCARD_PUB;
            this._operateDataMap[JackaroOperateMsgType.DISCARD_POKER] = null;
            this.pushMsg(msg);
        }
        /**上家出10 下家被动弃牌 */
        private forcedDiscardPubResponse(msg: any): void {
            msg.command = Command.FORCED_DISCARD_PUB;
            this._operateDataMap[JackaroOperateMsgType.ASSIGN_NEXT_PLAYER_DISCARD] = null;
            this.pushMsg(msg);
        }
        private cancelTrustResponse(msg: any): void {
            msg.command = Command.CANCEL_TRUST;
            if (msg.result == yalla.data.jackaro.OperateResult.SUC && msg.code == yalla.data.jackaro.ErrorCode.NONE_ERROR) {
                var player = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx)
                player && player.setTrustSystem(false);
                yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
            }
        }
        private extendTimeResponse(msg: any): void {
            msg.command = Command.EXTEND_TIME;
            if (msg && msg.code == yalla.data.ErrorCode.ACCOUNT_BE_FROZEN) {
                yalla.common.Confirm.instance.showConfirm(yalla.data.TranslationD.Game_Error_ACCOUNT_BE_FROZEN, Laya.Handler.create(this, () => {
                    yalla.common.Confirm.instance.hideConfirm();
                }), null, [yalla.data.TranslationD.Game_Btn_Confirm]);
                return;
            }
            if (msg.result == yalla.data.jackaro.OperateResult.SUC && msg.code == yalla.data.jackaro.ErrorCode.NONE_ERROR) {
                this._room.handleExtendTime(msg);
                this._user.updateExtendState(true);
                yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
                this.flushCurrencyRequest();
            }
        }
        private extendTimePubResponse(msg: any): void {
            msg.command = Command.EXTEND_TIME_PUB;
            if (msg.idx == yalla.Global.Account.idx) {
                this._room.handleExtendTimePub(msg);
            }
            //1.4.5 为了解决1v1获取奖励牌时，刚再次切到自己时，此时对手还在奖励牌回合，导致加时加到获得奖励牌回合
            this.pushMsg(msg);
            // yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
        }
        private quitGameResponse(msg: any): void {
            msg.command = Command.QUIT_ROOM;
            Laya.timer.clear(this, this.doQuitGame);
            if (msg && msg.code == yalla.data.jackaro.ErrorCode.NONE_ERROR) {
                this.doQuitGame(msg);
            } else {
                yalla.common.WaringDialog.showResultError(msg.code, () => {
                    this.backHall(true);
                });
            }
        }
        private doQuitGame(msg: any): void {
            yalla.Global.IsGameOver = true;
            yalla.common.Confirm.instance.isExit = true;
            this.event(yalla.data.jackaro.EnumCmd.Game_QuitRoom, msg);
        }
        private quitGamePubResponse(msg: any): void {
            msg.command = Command.QUIT_GAME_PUB;
            yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
        }
        private trustPubResponse(msg: any) {
            msg.command = Command.TRUST_PUB;
            if (msg.idx == yalla.Global.Account.idx) {
                this._room.consecutiveTrustCount = msg.consecutiveTrustCount;
                var player = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx)
                player && player.setTrustSystem(true);
                let trustSystemView = TipsViewManager.getInstance().getTipView(TipViewType.TRUST_SYSTEM);
                if (trustSystemView) {
                    (trustSystemView as TrustSystemView).updateView();
                }
                TipsViewManager.getInstance().turnClear();
                //推送时 重发消息置空
                this.clearOperateDataMap();
            }
            yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
        }
        private kickPlayerResponse(msg: any): void {
            msg.command = Command.KICK_PLAYER;
            this.handleKickPlayer(msg.idx);
        }
        private handleKickPlayer(idx: number, exception: boolean = false) {
            if (idx && idx == this.user.idx) yalla.Global.IsGameOver = true;
            yalla.data.VoiceService.instance.levelGameRoom();//TODO::1.3.4 因被顶号后backhall有概率退出语音房释放，这里再次执行levelGameRoom
            yalla.Debug.log('被踢' + exception);
            if (exception) {
                yalla.DialogManager.instance.showDialog('', yalla.data.TranslationD.Game_LoginOut_KickOut_Content_Exception, [yalla.data.TranslationD.Game_LoginOut_Btn], true);
            } else {
                yalla.DialogManager.instance.showDialog('', yalla.data.TranslationD.Game_LoginOut_KickOut_Content, [yalla.data.TranslationD.Game_LoginOut_Btn], true);
            }
        }

        private outGameResponse(msg: any): void {
            msg.command = Command.OUT_GAME;
            if (!msg) return;
            var reason = msg.reason;
            if (msg.idx && msg.idx == this.user.idx) yalla.Global.IsGameOver = true;
            yalla.data.VoiceService.instance.levelGameRoom();//TODO::1.3.4 因被顶号后backhall有概率退出语音房释放，这里再次执行levelGameRoom
            this._client && this._client.clear();
            switch (reason) {
                case 1:
                    yalla.Debug.log('别处登陆' + reason);
                    // yalla.data.UserService.instance.recordPointQuit(yalla.data.ExitType.LOGGED_ANOTHER_QUIT);
                    // this.showDialog('loginOut', TranslationD.Game_LoginOut_Another_Content, [TranslationD.Game_LoginOut_Btn]);
                    yalla.Native.instance.alertAccountLoggedView(() => {
                        yalla.Native.instance.backHall(false);
                    });
                    break;
                case 3:
                    yalla.Debug.log('被踢--' + reason);
                    yalla.DialogManager.instance.showDialog('loginOut', yalla.data.TranslationD.Game_LoginOut_KickOut_Content, [yalla.data.TranslationD.Game_LoginOut_Btn], true);
                    // yalla.data.UserService.instance.recordPointQuit(yalla.data.ExitType.KICKED_QUIT);
                    break;
                case 2:
                    yalla.Debug.log('服务器维护' + reason);
                    yalla.DialogManager.instance.showDialog('loginOut', yalla.data.TranslationD.Game_LoginOut_Maintenance_Content, [yalla.data.TranslationD.Game_LoginOut_Btn], true);
                    // yalla.data.UserService.instance.recordPointQuit(yalla.data.ExitType.SERVER_MAINTENANCE_QUIT);
                    break;
            }
        }
        private syncTimeResponse(msg: any) {
            yalla.Debug.zzf("syncTimeResponse");
            yalla.Debug.zzf(msg);
            if (msg.serverTime) {
                // this.receiveTime = Date.now();
                this.deltaTime = msg.serverTime - Date.now();
                // console.log("this.deltaTime", this.deltaTime, this.receiveTime - this.sendTime);
            }
            yalla.Debug.zzf(`this.deltaTime ${this.deltaTime} this.sendTime ${this.sendTime} Date.now() ${Date.now()}`);

        }
        public getOperateTime(thinkingEndTime: number) {
            yalla.Debug.zzf(`thinkingEndTime ${thinkingEndTime} this.deltaTime ${this.deltaTime}`);
            return thinkingEndTime ? thinkingEndTime - this.deltaTime - Date.now() : this.defaultOperateTime;
        }

        private flushCurrencyResponse(msg: any): void {
            msg.command = Command.FLUSH_CURRENCY;
            this._user.update(msg);
            // this._user.updateCoin(msg);
            // if (this._room.leftDiamond != this.user.gold) {
            //     this._room.leftDiamond = this.user.gold;
            // }
            yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
        }
        private testExchangePokerResponse(msg: any): void {
            msg.command = Command.TEST_EXCHANGE_POKER;
            if (msg.result == yalla.data.jackaro.OperateResult.SUC && msg.code == yalla.data.jackaro.ErrorCode.NONE_ERROR) {
                TipsViewManager.getInstance().hideTip(TipViewType.GM_SELECT_CARD);
                JackaroCardManger.instance.handleTestExchangePoker(yalla.Global.Account.idx, this.recordTestExchangePoker.sourcePoker, this.recordTestExchangePoker.targetPoker);
            }
        }
        private testExchangePokerPubResponse(msg: any): void {
            msg.command = Command.TEST_EXCHANGE_POKER_PUB;
            yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
        }

        private gameGiftConfigResponse(msg: any): void {
            msg.command = Command.GAME_GIFT_CONFIG;
            if (msg.result == yalla.data.jackaro.OperateResult.SUC && msg.code == yalla.data.jackaro.ErrorCode.NONE_ERROR) {
                yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
            }
        }
        private gameGiftSendResponse(msg: any): void {
            if (msg.result == yalla.data.jackaro.OperateResult.SUC && msg.code == yalla.data.jackaro.ErrorCode.NONE_ERROR) {
                if (msg.senderIdx == yalla.Global.Account.idx) {
                    //TODO::proto3 如果剩余金币或者钻石是0，之前的hasOwnProperty 和 msg.senderMoney 条件不成立，无法更新。 1.4.5 增加根据礼物消耗类型判定更新money or diamond
                    var giftData = yalla.common.InteractiveGift.Instance.getGiftItemData(msg.giftId);
                    if (msg.senderMoney || (giftData && giftData.money)) this.user.updateCoin({ type: 0, value: msg.senderMoney });
                    if (msg.senderDiamond || (giftData && giftData.diamond)) this.user.updateCoin({ type: 1, value: msg.senderDiamond });
                    //TODO::jackaro  you钻石消耗需要通知服务端
                    this.flushCurrencyRequest();
                }
                yalla.common.InteractiveGift.Instance.pushMsg(msg);
                yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
            } else {
                if (msg.code > 0) {
                    yalla.common.InteractiveGift.Instance.checkError(msg.code);
                }
            }
        }

        private endExceptionGamePubResponse(msg: any) {
            msg.command = Command.END_EXCEPTION_GAME_PUB;
            this.handleKickPlayer(yalla.Global.Account.idx, true);
        }
        private handleGatewayError(code: number) {
            // if (yalla.Global.gameType != GameType.JACKAROO) return;
            if (code > 0) {
                yalla.common.WaringDialog.showGatewayError(code, () => {
                    this.backHall(true);
                });
                return;
            }
        }

        private handleSocketClose() {
            Laya.timer.clear(this, this.onLoginRequestTimeout);
            this._loginRequestTimes = 0;
            this._curSocketClose = true;
            this._readyMsgList = null;
            this._isReady = false;
            JackaroGamePlayerManager.instance.recordCurOfflinePlayerIdx();
            JackaroGamePlayerManager.instance.recordCurOffLineSelfOperateTime();
            JackaroCardManger.instance.recordCurPlayerHandCardData();
            JackaroChessManager.instance.recordCurCanChooseChess();
            yalla.Debug.filterLogMore(this.logTag + "%%%%%%   handleSocketClose    %%%%%%");
        }

        public leaveVoiceRoom() {
            if (yalla.Voice.instance.joinSuccess) yalla.Voice.instance.levelGameRoom();
        }
        public isQueue: boolean = false;
        public nextMsg() {
            this.isQueue = false;
            if (this.queue.isEmpty()) {
                yalla.Debug.logMore(this.logTag + "=0执行下一条消息 队列长度:" + this.queue.size() + "this.isQueue:" + this.isQueue);
                return;
            }
            let msg = this.queue.dequeue();
            this.isQueue = true;
            if (msg) {
                if (yalla.Global.isFouce && msg['msg'] && msg['msg'].command == Command.CHANGE_PLAYER) {
                    // Laya.timer.callLater(this, () => {
                    yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg['msg']);
                    // });
                } else {
                    yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg['msg']);
                }
            }
            yalla.Debug.logMore(this.logTag + "=1执行下一条消息 队列长度:" + this.queue.size() + "this.isQueue:" + this.isQueue);

        }

        /**
         * 队列执行所有消息
         */
        public playAllQueueMsg() {
            while (!this.queue.isEmpty()) {
                let msg = this.queue.dequeue();
                yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg['msg']);
            }
        }

        public pushMsg(msg: any) {
            // if (!this.isQueue || !yalla.Global.isFouce || this.queue.isEmpty()) {//不经过队列
            yalla.Debug.logMore("pushMsg 0  msg.command:" + msg.command + "this.isQueue:" + this.isQueue + "yalla.Global.isFouce:" + yalla.Global.isFouce);
            if (!this.isQueue || !yalla.Global.isFouce) {//不经过队列
                this.isQueue = true;
                yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Jackaro_PlayMsg, msg);
                // this.processMsg(cmd, msg);
            } else {//放进队列
                this.queue.enqueue(msg.command, msg);
            }
            let len = this.queue.size();
            // yalla.Debug.logMore(this.logTag + "==加入消息队列 队列长度:" + len + " msg.command:" + msg.command + "this.isQueue:" + this.isQueue + "yalla.Global.isFouce:" + yalla.Global.isFouce);
            // for (let i = 0; i < len; i++) {
            //     yalla.Debug.logMore(JSON.stringify(this.queue.items[i]));
            // }
            if (len > 5) {
                this.nextMsg();
            }
        }

        get msgProgress(): number {
            if (this.queue) {
                return this.queue.progress;
            } else {
                return 0;
            }
        }

        public clearQueue() {
            this.queue && this.queue.reset();
            this.isQueue = false;
            yalla.Debug.logMore("clearQueue");
        }

        /**
         * 是否立即重连
         */
        public get ImmediateConnect(): boolean {
            if (!this._client.msgHash || this._client.msgIndex <= 0) return true;
            if (this._client.msgHash) {
                var msg = this._client.msgHash[this._client.msgIndex];
                if (msg && this._user) {
                    if (msg.idx != this._user.idx) return true;
                }
            }
            return false;
        }
        /**
         * 根据记录的操作消息，进行消息重发到服务器
         */
        public resendOperateMsg() {
            for (let key in this._operateDataMap) {
                if (this._operateDataMap[key]) {
                    if (Number(key) === JackaroOperateMsgType.PLAY_POKER) {
                        this.sendPlayPoker(this._operateDataMap[key].poker, this._operateDataMap[key].commonMoveData);
                    } else if (Number(key) === JackaroOperateMsgType.EXCHANGE_POKER) {
                        this.sendExchangeChess(this._operateDataMap[key].poker, this._operateDataMap[key].data);
                    } else if (Number(key) === JackaroOperateMsgType.FREE_MOVE_POKER) {
                        this.sendFreeMoveChess(this._operateDataMap[key].poker, this._operateDataMap[key].data);
                    } else if (Number(key) === JackaroOperateMsgType.DISCARD_POKER) {
                        this.sendDiscardPoker(this._operateDataMap[key].data, this._operateDataMap[key].isDiscardAll);
                    } else if (Number(key) === JackaroOperateMsgType.ASSIGN_NEXT_PLAYER_DISCARD) {
                        this.sendAssignNextPlayerDiscard(this._operateDataMap[key].poker);
                    } else if (Number(key) === JackaroOperateMsgType.DRAW_NEXT_PLAYER_POKER) {
                        this.sendDrawNextPlayerPoker(this._operateDataMap[key].poker, this._operateDataMap[key].drawnPokerIndex);
                    }
                }
            }
        }

        /**
         * 所有player成功进入游戏，开始游戏相关逻辑，sendChatOff，获取语音token&进入语音房，更新mute状态
         * 正常匹配gamestart执行；断线重连&杀端重进通过TicketLogin的gameData判定
         */
        public initGameStart() {
            this.sendChatOff(ludo.muteSpectator.isMute);//TODO::避免设置取消屏蔽协议发送失败（但本地已改），导致房间内同个玩家状态不一致
            this.getGiftCnfList();
            //  TODO:: 获取语音token
            var isSpectator = this._room.watchIdx > 0;
            if (!isSpectator) {
                //TODO:: 获取语音token
                Laya.timer.once(1900, this, () => {
                    this.getToken();//获取语音token
                });
            }
        }
        //获取音频 Token
        public getTokenVice() {
            var isSpectator = this._room.watchIdx > 0;
            if (!isSpectator) {
                //TODO:: 获取语音token
                this.getToken();//获取语音token

            }
        }
        //根据登录信息 加入语音频道
        public voiceTokenInit(type, token, cName) {

            if (type == VoiceType.Agora) {
                // VoiceService.instance.handleGetAgoraToken(msg);
                this.event(yalla.data.jackaro.EnumCmd.Game_GetAgoraToken, [90, token, cName]);
            } else if (type == VoiceType.Zego) {
                // VoiceService.instance.handleGetZegoToken(msg);
                this.event(yalla.data.jackaro.EnumCmd.Game_GetZegoToken, [92, token, cName]);
            }
        }


        public clearOperateDataMap() {
            this._operateDataMap = {
                [JackaroOperateMsgType.PLAY_POKER]: null,
                [JackaroOperateMsgType.EXCHANGE_POKER]: null,
                [JackaroOperateMsgType.FREE_MOVE_POKER]: null,
                [JackaroOperateMsgType.DISCARD_POKER]: null,
                [JackaroOperateMsgType.ASSIGN_NEXT_PLAYER_DISCARD]: null,
                [JackaroOperateMsgType.DRAW_NEXT_PLAYER_POKER]: null
            };
        }

        /**
        * @param isRun 是否逃跑   true，返回模式选择页面，false根据情况定
        * @param isBackMainHall 是否返回大厅
        */
        public backHall(isRun: boolean = false, isBackMainHall: boolean = false, delayTime: number = 0): void {
            this._client && this._client.clear();
            yalla.Debug.log(isRun + "===jackaro.backHall===isPrivate=" + yalla.Global.Account.isPrivate);
            yalla.data.jackaro.VoiceService.instance.clear();
            // this._client && this._client.clear();
            if (this.client) this.client.clear();
            //换回参数定义 mode 0 经典 2 快速
            // return params {"to":"jackaroo","mode":0|2,"isComplex":true|false};
            let isComplex: boolean = false;
            let mode: number = 0;
            if (JackaroCardManger.instance.isComplexPlay(yalla.data.jackaro.Poker.SPADE_KING)) {
                isComplex = true;
            }

            let room = yalla.data.jackaro.JackaroUserService.instance.room;
            if (room) {
                mode = room.getBackHallMode();
            }

            let returnParams = { "to": "jackaroo", "mode": mode, "isComplex": isComplex };
            if (isBackMainHall) {
                returnParams = { "to": "", "mode": mode, "isComplex": isComplex };
            }
            yalla.Native.instance.backHall(isRun, returnParams, delayTime);
        }
        public testCase() {
            yalla.common.WaringDialog.showResultError(1000, this.backHall);
        }
    }
}