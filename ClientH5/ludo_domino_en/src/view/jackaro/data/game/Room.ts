module yalla.data.jackaro {
    export enum PlayAgainGameType {
        Basic = 100,
        Complex = 101,
        Quick = 102,
        QuickComplex = 103,
        OneVOne = 104
    }
    export class Room {

        public againstType: GameAgainstType; //对战模式
        private teamInfo: TeamInfo; //组队信息
        private bets: number; //本局筹码数量
        private gameGroup: GameGroup; //模式
        public gameType: GameType; //游戏类型
        private gameStatus: GameStatus; //游戏状态
        public roomId: number; //房间id
        public isPrivate: number = 0;                       //0公开  1私密  3锦标赛 4vip房间
        public curRound: number = 0; //当前轮次
        public extendTimeCount: number = 0; //已经延时的次数
        public extendTimeCost: number = 0; //下次延时需要的花费,钻石数
        public extendTime: number = 0; //延时时间
        public extendRemainCount: number = 5;//剩余的延时次数
        public watchIdx: number = 0;
        public expConfig: Record<any, number>;
        private _roomMaxNum: number = 4;
        public isTrust: boolean;
        public punishTrust: boolean; //是否开启托管惩罚
        public ignoreTrustExperience: boolean; //是否开启托管不加经验
        public consecutiveTrustCount: number = 0; //连续托管次数
        public allowConsecutiveTrustMax: number = 0; //连续托管次数最大值
        public endpointNewCode: number = 0;
        public killedResult: Record<number, {
            killChess: number,
            chessKilled: number
        }> = {};
        private logTag: string = "JackaroRoom : ";
        public get roomMaxNum(): number {
            return this._roomMaxNum;
        }
        public set roomMaxNum(value: number) {
            this._roomMaxNum = value;
        }
        public gameAgainstTypeTitle = {
            100: "Basic",
            101: "Complex",
            102: "Quick",
            103: "Quick",//complex quick
            104: "Complex"
        }
        public updateRoomInfo(data: any) {
            if (data) {
                this.againstType = data.againstType;
                if (this.againstType == GameAgainstType.MELEE) {
                    this.roomMaxNum = 4;
                } else if (this.againstType == GameAgainstType.ONE_VS_ONE) {
                    this.roomMaxNum = 2;
                }
                this.teamInfo = data.teamInfo;
                if (data.bets) {
                    this.bets = data.bets;
                }
                if (data.gameGroup) {
                    this.gameGroup = data.gameGroup;
                }
                if (data.gameType) {
                    this.gameType = data.gameType;
                }
            }
        }
        /**
         * 是否播放老式经验顶格表现
         */
        public isPlayOldExp(): boolean {
            return this.endpointNewCode == 0;
        }
        /**
         * 是否快速模式
         * @returns 
         */
        public isQuickMode(): boolean {
            return this.gameType == GameType.JACKAROO_BASIC_QUICK || this.gameType == GameType.JACKAROO_COMPLEX_QUICK;
        }

        /**
         * 是否快速基础模式
         * @returns 
         */
        public isQuickBasicMode(): boolean {
            return this.gameType == GameType.JACKAROO_BASIC_QUICK;
        }
        /**
         * 是否快速复杂模式
         * @returns 
         */
        public isQuickComplexMode(): boolean {
            return this.gameType == GameType.JACKAROO_COMPLEX_QUICK;
        }
        /**
         * 是否复杂玩法
         * @returns 
         */
        public isComplexMode(): boolean {
            return this.gameType == GameType.JACKAROO_COMPLEX || this.gameType == GameType.JACKAROO_COMPLEX_QUICK;
        }
        /**
         * 是否复杂基础玩法
         * @returns 
         */
        public isComplexBaseMode(): boolean {
            return this.gameType == GameType.JACKAROO_COMPLEX;
        }
        /**
         * 是否1v1玩法
         * @returns 
         */
        public is1V1Mode(): boolean {
            return this.gameType == GameType.JACKAROO_COMPLEX_ONE_VS_ONE;
        }
        public getBets() {
            return this.bets;
        }
        public getTeamInfo() {
            return this.teamInfo;
        }
        public setExpConfig(expConfig: Record<any, number>) {
            this.expConfig = expConfig;
        }
        public getExpByType(type: yalla.data.jackaro.ExpType) {
            if (this.expConfig) {
                return this.expConfig[type] || 0;
            }
            return 0;
        }
        // public notAddExpInTrust(idx: number): boolean {
        //     let player = JackaroGamePlayerManager.instance.getGamePlayerByIdx(idx);
        //     return player && player.getTrustSystem() && this.ignoreTrustExperience;
        // }
        public handleExtendTime(msg: any) {
            this.extendTimeCount = msg.extendTimeCount || 0;
            this.extendTimeCost = msg.extendTimeCost || 0;
            this.extendRemainCount = msg.extendRemainCount || 0;
            if (msg.currentRoundExtendTimeCount && msg.currentRoundExtendTimeCount > 0) {
                yalla.data.jackaro.JackaroUserService.instance.user && yalla.data.jackaro.JackaroUserService.instance.user.updateExtendState(true);
            }
        }
        public handleTrustConfig(msg: any) {
            if (!msg) return;
            this.punishTrust = msg.punishTrust || 0;
            this.ignoreTrustExperience = msg.ignoreTrustExperience || 0;
            this.allowConsecutiveTrustMax = msg.allowConsecutiveTrustMax || 0;
        }
        public isAllTrustConfig() {
            return this.punishTrust && this.ignoreTrustExperience;
        }
        public handleExtendTimePub(msg: any) {
            this.extendTime = msg.extendTime;
        }
        public getGameStatus(): GameStatus {
            return this.gameStatus;
        }
        public setGameStatus(status: GameStatus) {
            this.gameStatus = status;
        }
        public getGamePlayAgainGameType(): number {
            if (this.isComplexBaseMode()) {
                return PlayAgainGameType.Complex;
            } else if (this.isQuickComplexMode()) {
                return PlayAgainGameType.QuickComplex;
            } else if (this.is1V1Mode()) {
                return PlayAgainGameType.OneVOne;
            }
            else {
                let room = yalla.data.jackaro.JackaroUserService.instance.room;
                if (room && room.isQuickMode()) {
                    return PlayAgainGameType.Quick;
                }
            }
            return PlayAgainGameType.Basic;
        }
        public getGameAgainstTypeTitle(): string {
            return this.gameAgainstTypeTitle[this.gameType]
        }
        /**
         * 设置返回大厅的模式
         * @returns 
         */
        public getBackHallMode() {
            let defaultMode = 0;
            if (this.isQuickMode()) {
                defaultMode = 2;
            } else if (this.is1V1Mode()) {
                defaultMode = 3;
            }
            return defaultMode;
        }
        //  public isRoundTimeFree(cost: number): boolean {
        //     var usr = yalla.data.UserService.instance.user;
        //     if (this.addTimeCost && this.addTimeCost.indexOf(cost) == 0 && usr.playerShowInfo.fPlayerInfo.level <= 1) return true;
        //     return false;
        // }
        // /**
        //  * 当前钻石是否足够
        //  */
        // public isLessDiamond(): boolean {
        //     var isLess = (this.leftDiamond < this.nextDiamond && this.nextDiamond != -1);
        //     if (isLess && this.isRoundTimeFree(this.nextDiamond)) isLess = false;
        //     return isLess;
        // }
        public turnClear() {
            this.curRound = 0;
        }
        public clear() {
            this.roomId = 0;
        }
    }
}