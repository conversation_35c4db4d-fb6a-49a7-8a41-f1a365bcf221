module yalla.data.jackaro {

    export class User extends yalla.data.DataItem {
        public idx: number;                          //用户idx
        // public playerShowInfo: playerShowInfo;
        public diamond: number = 0;                       //钻石
        public gold: number = 0;                      //金币 游戏玩法消耗
        public isRecharged: boolean;
        public exp: number = 0;
        public chessPanelId: number = 0;
        public pokerSkinId: number = 0;//扑克牌皮肤id
        public isInChatOff: number = 0;  //1设置屏蔽 0取消屏蔽
        public isUseExtendTime = false;//本回合是否使用增加回合时间

        constructor() {
            super();
        }

        public update(d: Object): void {
            super.update(d);
            yalla.Global.Account.currentGoldNum = this.gold;
            yalla.Global.Account.currentDiamondNum = this.diamond;
        }

        /**
         * 更新coin
         * @param d 
         */
        public updateCoin(d: any): void {
            if (!d) return;
            switch (d.type) {
                case 0:
                    this.gold = d.value;
                    yalla.Global.Account.currentGoldNum = this.gold;
                    break;
                case 1:
                    this.diamond = d.value;
                    yalla.Global.Account.currentDiamondNum = this.diamond;
                    break;
            }
        }

        /**
         * 更新coin
         * @param d 
         */
        public addCoin(d: any): void {
            if (!d) return;
            if (d.diamond > 0) {
                this.diamond += parseInt(d.diamond);
                yalla.Global.Account.currentDiamondNum = this.diamond;
            }
            if (d.money > 0) {
                this.gold += parseInt(d.money);
                yalla.Global.Account.currentGoldNum = this.gold;
            }
        }

        /**
         * 更新是否屏蔽聊天
         */
        public updateChatOff(state: number): void {
            this.isInChatOff = state;//1 - this.playerShowInfo.isInChatOff;
        }

        /**回合切换后，本回合是否使用增加延时状态重置（服务端没有记录 so杀端重进状态可能不对，服务端会校验） */
        updateExtendState(isUse: boolean = false): void {
            this.isUseExtendTime = isUse;
        }
        /**
         * 更新发牌回合数
         */
        public checkUpdateExtendState(round: number): void {
            let outRound = JackaroGamePlayerManager.instance.getOutRound();
            if (outRound != round) {
                this.updateExtendState();
            }

        }

    }
}