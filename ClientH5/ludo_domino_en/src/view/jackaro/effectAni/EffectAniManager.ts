class EffectAniManager {

    private _chessChessPlaceHolderItem: Array<Laya.Image> = [];
    private _effectContainer: Laya.Box;
    static _instance: EffectAniManager = null;
    private _chessExchangeTw: Array<Laya.Tween> = [];
    static get instance() {
        return this._instance ? this._instance : this._instance = new EffectAniManager();
    }
    public initialize(toastContainer: Laya.Box): void {
        this._effectContainer = toastContainer;
    }
    /**
     * 播放交换棋子动画
     * @param fromCHess 
     * @param toStation 
     * @param duration 
     * @param callBack 
     * @returns 
     */
    public playExchangeEffAni(fromCHess: JackaroChess, toStation: Station, duration: number = ludo.Global.moveTime, callBack: Function) {
        if (!this._effectContainer) {
            return;
        }
        let chessPlaceHolderItem = this.getChessPlaceHolderItem();
        chessPlaceHolderItem.scale(ChessManger._moveExchangeScale, ChessManger._moveExchangeScale);
        chessPlaceHolderItem.skin = `jackaro/${fromCHess.resName}_50000_${fromCHess.color}.png`;
        this._effectContainer.addChild(chessPlaceHolderItem);
        let fromGPos = fromCHess.getGlobalPos();
        let {width, height} = fromCHess;
        chessPlaceHolderItem.pos(fromGPos.x + width / 2, fromGPos.y + height / 2);
        let grid: LudoGrid = JackaroBoardData.getGridByStation(toStation);
        let toGPos = (fromCHess.parent as Laya.Box).localToGlobal(new Laya.Point(grid.port.x, grid.port.y))

        let chessExchangeTw = Laya.Tween.to(chessPlaceHolderItem, {
            x: toGPos.x,
            y: toGPos.y,
            update: null
        }, duration, Laya.Ease.linearInOut, Laya.Handler.create(this, () => {
            if (this._chessExchangeTw.indexOf(chessExchangeTw) > -1) {
                this._chessExchangeTw.splice(this._chessExchangeTw.indexOf(chessExchangeTw), 1);
            }
            if (this._chessExchangeTw.length == 0) {
                this.removeChessOrbitItem();
            }
            chessExchangeTw && chessExchangeTw.clear();
            callBack && callBack();
        }));
        this._chessExchangeTw.push(chessExchangeTw);
    }
    /**
     * 获取棋子占位
     */
    public getChessPlaceHolderItem() {
        var chessPlaceHolderItem;
        chessPlaceHolderItem = Laya.Pool.getItemByClass('ChessPlaceHolderItem', Laya.Image);
        chessPlaceHolderItem.anchorX = chessPlaceHolderItem.anchorY = 0.5;
        this._chessChessPlaceHolderItem.push(chessPlaceHolderItem);
        return chessPlaceHolderItem;
    }
    public removeChessOrbitItem() {
        this._chessChessPlaceHolderItem.forEach(item => {
            item.removeSelf();
            Laya.Pool.recover('ChessPlaceHolderItem', item);
        })
        this._chessChessPlaceHolderItem = [];
    }
    public clear() {
        Laya.Pool.clearBySign("ChessPlaceHolderItem");
        EffectAniManager._instance = null;
    }
}