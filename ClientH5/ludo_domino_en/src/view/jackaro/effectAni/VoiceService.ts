module yalla.data.jackaro {

    import EventDispatcher = laya.events.EventDispatcher;

    export class VoiceService extends EventDispatcher {
        static _instance: VoiceService;

        public isJoinRoom: Boolean = false;
        private _voiceToken: string = '';		   //token(加入语音聊天房间需要)
        private _channelName: string;
        private _voiceOpen: boolean = false;       //语音开关  TODO::保存本地,下次进入以上次为准

        constructor() {
            super();
        }

        static get instance(): VoiceService {
            return VoiceService._instance || (VoiceService._instance = new VoiceService());
        }

        public get voiceToken(): string { return this._voiceToken; }
        public get channelName(): string { return this._channelName; }
        public get voiceOpen(): boolean { return this._voiceOpen; }
        /**
         * 加入语音房间
         */
        public joinGameRoomWithToken(idx, callBack: Laya.Handler = null, from: number = 1) {
            yalla.Debug.log(this._channelName + '======VoiceService.joinGameRoomWithToken=====voiceType=' + yalla.Global.Account.voiceType + "this.voiceToken:" + this.voiceToken + "this.isJoinRoom:" + this.isJoinRoom);
            yalla.Global.Account.banTalkData = yalla.Global.Account.banTalkData || '';
            this.updateVoiceState();
            if (this.isJoinRoom) {
                var voiceState = laya.net.LocalStorage.getJSON('voice_open').isOpen;
                if (voiceState && !yalla.Mute.isBanTalk) {
                    this.enableAudio(idx, () => {
                        if (!yalla.Sound.canPlayMusic) {
                            yalla.Sound.canPlayMusic = true;
                            yalla.Sound.playMusic(yalla.Sound.audios.jackaroo_bg, 0);
                        }
                        this.event(yalla.data.EnumCustomizeCmd.Dominose_JoinAgora);
                    }, from);
                } else {
                    this.disableAudio(idx, () => {
                        if (!yalla.Sound.canPlayMusic) {
                            yalla.Sound.canPlayMusic = true;
                            yalla.Sound.playMusic(yalla.Sound.audios.jackaroo_bg, 0);
                        }
                        this.event(yalla.data.EnumCustomizeCmd.Dominose_JoinAgora);
                    });
                }
            } else {

                yalla.Native.instance.joinGameRoomWithToken(this.voiceToken, this.channelName, idx, yalla.Global.Account.voiceType, (value) => {
                    this.isJoinRoom = value;
                    if (!value) {
                        callBack && callBack.run();
                        return;
                    }

                    if (yalla.Global.Account.isLimitVisitor || yalla.Mute.isBanTalk) {//被限制的游客，不能使用麦克风
                        this.disableAudio(idx, () => {
                            // if (!yalla.Sound.canPlayMusic) {
                            yalla.Sound.canPlayMusic = true;
                            yalla.Sound.playMusic(yalla.Sound.audios.jackaroo_bg, 0);
                            // }
                        });
                        return;
                    }

                    var voiceState = laya.net.LocalStorage.getJSON('voice_open').isOpen;
                    if (voiceState && !yalla.Mute.isBanTalk) {
                        this.enableAudio(idx, () => {
                            yalla.Sound.canPlayMusic = true;
                            yalla.Sound.playMusic(yalla.Sound.audios.jackaroo_bg, 0);
                            this.event(yalla.data.EnumCustomizeCmd.Dominose_JoinAgora);
                        }, from);
                    } else {
                        this.disableAudio(idx, () => {
                            yalla.Sound.canPlayMusic = true;
                            yalla.Sound.playMusic(yalla.Sound.audios.jackaroo_bg, 0);
                            this.event(yalla.data.EnumCustomizeCmd.Dominose_JoinAgora);
                        });
                    }
                });
            }
        }

        private updateVoiceState(): void {
            if (yalla.Mute.isBanTalk) {
                var voice_open = Laya.LocalStorage.getJSON("voice_open");
                if (!voice_open || !voice_open.isOpen)
                    Laya.LocalStorage.setJSON("voice_open", { isOpen: true });
            }
        }

        /**
         * 打开音频
         * from 1加入语音房间  2点击语音按钮打开关闭
         */
        public enableAudio(idx, cb, from: number = 1) {
            yalla.Native.instance.currentAudioStatus(obj => {//先检测时候有权限再开麦  
                Debug.log("=========888========obj.status=" + obj.status);
                if (obj.status) {
                    yalla.Native.instance.enableAudio((success) => {
                        if (success == 0) {
                            this._voiceOpen = true;
                            Laya.LocalStorage.setJSON("voice_open", { isOpen: true });
                            this.event(yalla.data.EnumCustomizeCmd.Dominose_EnableAudio, idx);
                            cb && cb(success);
                        }
                    }, from == 2 ? 1 : 0);
                } else {
                    this._voiceOpen = false;
                    if (from == 2) {
                        if (yalla.Native.instance.deviceType != DeviceType.Android)
                            yalla.common.Tip.instance.showTip(TranslationD.Game_Tip_Check_Microphone2);
                    } else {
                        yalla.common.TipManager.instance.showTip(yalla.data.TranslationD.Game_Tip_Check_Microphone, {}, 3000, 0);
                        Laya.timer.callLater(this, () => {//TODO::权限关闭，播放音乐,延迟播放了，ios关闭也正常
                            if (yalla.Native.instance.deviceType != DeviceType.Android) {
                                if (!yalla.Sound.canPlayMusic) {
                                    yalla.Sound.canPlayMusic = true;
                                    yalla.Sound.playMusic(yalla.Sound.audios.jackaroo_bg, 0);
                                }
                            }
                        });

                        //获取权限失败
                        this.event(yalla.data.EnumCustomizeCmd.Dominose_JoinAgora, false);
                    }
                    // yalla.common.TipManager.instance.showTip(TranslationD.Game_Tip_Check_Microphone, { alpha: 0 }, 0, 5000);
                    if (yalla.Native.instance.deviceType == DeviceType.Android) {
                        yalla.Native.instance.enableAudio((success) => {
                            if (success == 0) {
                                this._voiceOpen = true;
                                Laya.LocalStorage.setJSON("voice_open", { isOpen: true });
                                this.event(yalla.data.EnumCustomizeCmd.Dominose_EnableAudio, idx);
                                cb && cb(success);
                            } else if (success == -1) {//拒绝麦克风权限
                                if (!yalla.Sound.canPlayMusic) {
                                    yalla.Sound.canPlayMusic = true;
                                    yalla.Sound.playMusic(yalla.Sound.audios.jackaroo_bg, 0);
                                }
                            }
                        }, from == 2 ? 1 : 0);
                    }
                }
            })
        }

        /**
         * 关闭音频
         */
        public disableAudio(idx, cb: Function) {
            yalla.Native.instance.disableAudio((success) => {
                if (success == 0) {
                    this._voiceOpen = false;
                    Laya.LocalStorage.setJSON("voice_open", { isOpen: false });
                    this.event(yalla.data.EnumCustomizeCmd.Dominose_DisableAudio, idx);
                    cb && cb(success);
                }
            });
        }
        /**
         * 离开房间
         */
        public levelGameRoom() {
            this._voiceOpen = false;
            this._voiceToken = '';
            this.isJoinRoom = false;
            yalla.Native.instance.levelGameRoom();
        }

        //----------------------handle----------------------
        public handleGetAgoraToken(msg: any): void {
            this._voiceToken = msg.token;
            this._channelName = msg.cName;
            this.joinGameRoomWithToken(yalla.data.UserService.instance.user.idx);
        }

        public handleGetZegoToken(msg: any): void {
            this._voiceToken = msg.token;
            this._channelName = msg.cName;
            if (Global.IsUpdateZegoToken) {
                yalla.Native.instance.zegoTokenResponse(this._voiceToken, this._channelName);
                Global.IsUpdateZegoToken = false;
            } else {
                this.joinGameRoomWithToken(yalla.data.UserService.instance.user.idx);
            }
        }

        public clear(): void {
            this._voiceOpen = false;
            this._voiceToken = '';
            this.isJoinRoom = false;
            Laya.timer.clearAll(this);
        }
    }
}