/**
 * 延长思考时间界面
 */
module yalla.view.game.jackaro.gamepage {
    export class JackaroAddTimeView extends ui.jackaro.item.addTimeUI {
        private logTag: string = "JackaroAddTimeView : ";
        private extendOntag: string = 'jackaro_addRound_extendOn';
        constructor() {
            super();
            this.name = this.logTag;
            this.init();
        }
        init() {
            this.addTimeFeeBtn.on(Laya.Event.CLICK, this, this.onAddTimeFee);
            this.quick_buy_btn.on(Laya.Event.CLICK, this, this.buyCoin);
            //upate extend round
            var key = this.extendOntag;;
            if (!Laya.LocalStorage.getJSON(key)) Laya.LocalStorage.setJSON(key, { selected: false });
            this.extendRound.addTimeCheckBox.selected = Laya.LocalStorage.getJSON(key).selected;
            if (yalla.Font.lan == 'ar') {
                this.extendRound.addTimeCount.x += 28;
                this.extendRound.extendOnTxt.x -= 2;
                this.txt_money.x = 35;
                this.txt_money.align = "right";
                this.icon_diamond.x = 104;
                this.quick_buy_btn.skin = yalla.getPublic('Recharge_label_ar');
            }
            this.extendRoundVisible = false;
            this.extendRound.addTimeCheckBox.on(Laya.Event.CLICK, this, this.onClickAddSelected);
            this.txt_lack.visible = false;
            this.updateAddTimeView();
        }
        /**
         * 是否自动延长时间
         */
        private onClickAddSelected(): void {
            var room = yalla.data.RoomService.instance.room;
            // if (!this.extendRound.addTimeCheckBox.selected && room.remainRoundCounts > 0) {
            //     this.extendRound.clockImg.skin = yalla.getDomino('clock_on');
            // } else {
            //     this.extendRound.clockImg.skin = yalla.getDomino('clock_off');
            // }
            Laya.LocalStorage.setJSON(this.extendOntag, { selected: this.extendRound.addTimeCheckBox.selected });
            this.updateAddTimeView();
        }
        private updateAddTimeView(): void {
            if (!this.bg_addTime) {
                return;
            }
            let isSelected = this.extendRound.addTimeCheckBox.selected;
            this.bg_addTime.skin = isSelected ? "jackaro/bg_addTime_gray.png" : "jackaro/bg_addTime.png"
        }
        updateView() {
            let serviceInst = yalla.data.jackaro.JackaroUserService.instance;
            let room = serviceInst.room;
            this.txt_money.text = yalla.util.filterNum(room.extendTimeCost);
            let leftExtendTimeCount = room.extendRemainCount;
            this.extendRound.addTimeCount.text = String(leftExtendTimeCount);
            if (leftExtendTimeCount <= 0 || room.extendTimeCost > serviceInst.user.diamond) {
                this.txt_lack.visible = true;
                this.txt_lack.text = 'Lack';
                this.icon_diamond.visible = false;
                this.txt_money.visible = false;
                this.addTimeFeeBtn.gray = true;
            } else {
                if (room.extendTimeCost <= serviceInst.user.diamond) {
                    if (room.extendTimeCost == 0) {//TODO:: free
                        this.txt_lack.visible = true;
                        this.txt_lack.text = 'Free';
                        this.icon_diamond.visible = false;
                        this.txt_money.visible = false;
                        // this.addTimeFeeBtn.gray = true;
                    } else {
                        this.addTimeFeeBtn.gray = false;
                        this.txt_lack.visible = false;
                        this.icon_diamond.visible = true;
                        this.txt_money.visible = true;
                    }
                }
            }
            this.extendRound.addTimeContentTxt.text = yalla.util.getLangText("You can spend diamonds extending the round by") + " " + (serviceInst.addOperateTime / 1000) + " " + yalla.util.getLangText("seconds in your turn.");
        }

        onAddTimeFee(e: Laya.Event) {
            e.stopPropagation();
            //1.4.5 解决获得奖励牌时，点击加时，导致加时时间加在获得奖励牌回合，而不是下一个回合
            if (yalla.data.jackaro.JackaroUserService.instance.getIsTriggerPlayCard()) {
                this.extendRoundVisible = !this.extendRound.visible;
                return;
            }
            // if (JackaroGamePlayerManager.instance.getCurOperatePlayerIdx() == yalla.Global.Account.idx) {
            let isMyActive = JackaroGamePlayerManager.instance.getCurOperatePlayerIdx() == yalla.Global.Account.idx;
            let room = yalla.data.jackaro.JackaroUserService.instance.room;
            let user = yalla.data.jackaro.JackaroUserService.instance.user;
            var isSelected = this.extendRound.addTimeCheckBox.selected;
            let isHasEnoughDiamand = room.extendTimeCost <= yalla.data.jackaro.JackaroUserService.instance.user.diamond;
            let leftExtendTimeCount = room.extendRemainCount;
            if (!isSelected && isHasEnoughDiamand && leftExtendTimeCount >= 1 && !user.isUseExtendTime && isMyActive) {
                yalla.data.jackaro.JackaroUserService.instance.sendExtendTime();
                //增加延时时，如果自己是托管状态，需要取消托管
                let player = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx)
                if (player && player.getTrustSystem()) {
                    yalla.data.jackaro.JackaroUserService.instance.sendCancelTrust();
                }
            } else {
                if (!isHasEnoughDiamand && leftExtendTimeCount >= 1) {
                    this.quickBuyVisible = !this.quick_buy_btn.visible;
                } else {
                    this.extendRoundVisible = !this.extendRound.visible;
                }
            }
        }

        /**快速购买 */
        private buyCoin(callBack: Function) {
            yalla.Native.instance.buyGood((obj) => {
                if (obj.status) {
                    let user = yalla.data.jackaro.JackaroUserService.instance.user;
                    user.addCoin(obj);
                    // this.update();

                    if (!yalla.Global.IsGameOver) yalla.data.jackaro.JackaroUserService.instance.IsUpdatePlayerCoin = true;
                    yalla.data.jackaro.JackaroUserService.instance.flushCurrencyRequest()
                } else {
                    if (!yalla.Global.IsGameOver) yalla.data.jackaro.JackaroUserService.instance.flushCurrencyRequest(1);
                    // var tip = yalla.common.TipItem.instance;
                    // tip.pushTip((Laya.stage.width - tip.width) / 2, Laya.stage.height / 2, "Fail to purchase diamonds, please try again.", { alpha: 0 }, 0, 5000);
                }
            });
        }

        onClickGameUI() {
            this.extendRoundVisible = false;
            this.quickBuyVisible = false;
        }

        set extendRoundVisible(v) {
            this.extendRound.visible = v;
            if (v) {
                yalla.util.ViewExclusion.instance.add(yalla.util.ViewType.GameUndo, () => { this.extendRoundVisible = false; });
            } else {
                yalla.util.ViewExclusion.instance.remove(yalla.util.ViewType.GameUndo);
            }
        }
        set quickBuyVisible(v) {
            this.quick_buy_btn.visible = v;
            if (v) {
                yalla.util.ViewExclusion.instance.add(yalla.util.ViewType.QuickBuyView, () => { this.quickBuyVisible = false; });
            } else {
                yalla.util.ViewExclusion.instance.remove(yalla.util.ViewType.QuickBuyView);
            }
        }

    }
}