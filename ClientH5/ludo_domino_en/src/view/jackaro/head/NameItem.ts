module yalla.jackaro {
    export class NameItem {
        public ui: ui.publics.item.nameItemUI;
        public defaultNameTxt: Laya.Label;
        private _labelPropList: Array<string>;

        private _name: string = '';

        private _tempMaskMoveDis: number = 0;
        /** TODO::遮罩移动的最大距离，否则有些机型卡顿 */
        public maskMoveDis: number = 0;
        public maskBeginX: number = -60;
        public data: yalla.data.jackaro.PlayerShowInfoInterface = null;

        constructor(ui: any, defaultNameTxt: Laya.Label) {
            this.ui = ui;
            this.defaultNameTxt = defaultNameTxt;
            this._labelPropList = ['color', 'fontSize'];
        }

        /**通用主界面 TODO::ludo使用了hBox，比较特殊*/
        public gameName(data: yalla.data.jackaro.PlayerShowInfoInterface, nameLen: number): void {
            this.setData(data, nameLen);
            if (this.ui.nameTxt) {
                this.ui.width = this.ui.nameTxt.width + 30;
            }
        }
        /**通用游戏结算 */
        public resultName(data: yalla.data.jackaro.PlayerShowInfoInterface, name: string): void {
            this._name = name;
            this._labelPropList = ['color', 'fontSize', 'stroke', 'strokeColor'];
            this.setData(data, 13, 4);
        }

        /**domino 主界面名称 indexType=1,如果名字超出屏幕，在maskSp移动超出屏幕，mask属性无效*/
        public gameName_domino(data: yalla.data.jackaro.PlayerShowInfoInterface, nameLen: number, maskMoveDis: number = 0): void {
            this._tempMaskMoveDis = 0;
            if (maskMoveDis > 0) this._tempMaskMoveDis = maskMoveDis;
            this.setData(data, nameLen);
        }

        public setData(data: yalla.data.jackaro.PlayerShowInfoInterface, nameLen: number, stroke: number = 0): void {
            if (!this.ui) return;

            this.data = data;
            var name = this._name;
            if (!name) name = yalla.util.filterName(this.data.nickName, nameLen);
            var canShowRLNameAnimation = yalla.util.canShowJackaroRLNameAnimation(data);
            if (canShowRLNameAnimation) {
                this.ui.visible = true;
                if (this.defaultNameTxt) {
                    this.defaultNameTxt.visible = false;
                    this.ui.nameTxt.align = this.defaultNameTxt.align;
                    this.ui.nameTxt.fontSize = this.defaultNameTxt.fontSize;
                    this.ui.nameTxt.color = this.defaultNameTxt.color;

                    this.ui.nameDownMaskSp.y = this.ui.nameTxt.fontSize + this.ui.nameTxt.fontSize / 2 - 4;
                    this.ui.nameDownMaskSp.height = this.ui.nameTxt.fontSize;
                }
                if (data.realRoyLevel == 4) {
                    this.setColor_r4(stroke);
                } else {
                    this.setColor_r5(stroke);
                }
                yalla.Emoji.lateLable(name, this.ui.nameTxt, this._labelPropList);
                var nameTxtWid = this.ui.nameTxt.width;
                this.ui.nameDownMaskSp.width = nameTxtWid + 60;

                this.maskMoveDis = nameTxtWid;
                if (this._tempMaskMoveDis > 0) {
                    this.maskMoveDis = Math.min(nameTxtWid, this._tempMaskMoveDis);
                }

                if (this.ui.nameTxt_Down) {
                    this.ui.nameTxt_Down.visible = true;
                    this.ui.nameTxt_Down.fontSize = this.ui.nameTxt.fontSize;
                    this.ui.nameTxt_Down.x = this.ui.nameTxt.x;
                    this.ui.nameTxt_Down.align = this.ui.nameTxt.align;
                    yalla.Emoji.lateLable(name, this.ui.nameTxt_Down, this._labelPropList);
                    this.ui.nameTxt_Down.width = nameTxtWid + 60;
                }
                if (this.ui.nameTxt_Mask) {
                    this.ui.nameTxt_Mask.visible = true;
                    this.ui.nameTxt_Mask.fontSize = this.ui.nameTxt.fontSize;
                    this.ui.nameTxt_Mask.x = this.ui.nameTxt.x;
                    this.ui.nameTxt_Mask.align = this.ui.nameTxt.align;
                    yalla.Emoji.lateLable(name, this.ui.nameTxt_Mask, this._labelPropList);
                    this.ui.nameTxt_Mask.width = nameTxtWid + 30;
                }

                //mask位置重置
                this.ui.nameMaskSp.x = this.maskBeginX;
            } else {
                if (this.ui) this.ui.visible = false;
                if (this.defaultNameTxt) {
                    this.defaultNameTxt.visible = true;
                    yalla.Emoji.lateLable(name, this.defaultNameTxt, this._labelPropList);
                }
            }
        }

        /** rl>=4 名字扫光效果 */
        public frameLoopName(): void {
            var canShowRLNameAnimation = yalla.util.canShowJackaroRLNameAnimation(this.data);
            if (!this.data || !canShowRLNameAnimation) return;

            if (this.ui.nameTxt_Mask && this.ui.nameTxt_Mask.visible && this.maskMoveDis > 0) {
                if (this.ui.nameMaskSp) {
                    this.ui.nameMaskSp.x += 1;
                    if (this.ui.nameMaskSp.x > this.maskMoveDis) this.ui.nameMaskSp.x = this.maskBeginX;
                }
            }
        }

        public setColor_r4(stroke: number = 0): void {
            this.ui.nameTxt.color = '#DCF8FF';
            this.ui.nameTxt_Down.color = '#78B8FE';
            this.ui.nameTxt_Mask.color = '#FFFFFF';
            if (this._labelPropList && this._labelPropList.indexOf('stroke') > -1) {
                this.setStroke('#27374B', stroke);
            }
        }

        public setColor_r5(stroke: number = 0): void {
            this.ui.nameTxt.color = '#FEFE90';
            this.ui.nameTxt_Down.color = '#E9A94D';
            this.ui.nameTxt_Mask.color = '#FEFE90';
            if (this._labelPropList && this._labelPropList.indexOf('stroke') > -1) {
                this.setStroke('#B45E19', stroke);
            }
        }

        public setStroke(color: string = '', stroke: number = 0): void {
            if (color) {
                this.ui.nameTxt.stroke = stroke;
                this.ui.nameTxt_Down.stroke = stroke;
                this.ui.nameTxt_Mask.stroke = stroke;
                this.ui.nameTxt.strokeColor = color;
                this.ui.nameTxt_Down.strokeColor = color;
                this.ui.nameTxt_Mask.strokeColor = color;
            }
        }

        public clear(): void {
            this.maskMoveDis = 0;
            this._tempMaskMoveDis = 0;
        }
    }
}