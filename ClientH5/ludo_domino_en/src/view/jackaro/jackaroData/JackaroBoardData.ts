class JackaroBoardData {
    private static _color = 0;
    private static _totalGridLen = 19;
    private static _totalPlayLen = 4;
    static get color() {
        return this._color;
    }
    static setColor(val: number, type: yalla.data.jackaro.GameAgainstType) {
        this._color = val;
        var ports, playerNum;
        if (type == yalla.data.jackaro.GameAgainstType.ONE_VS_ONE) {
            ports = this._1v1ports;
            playerNum = 2;
            this._map = this._1v1map;
            this._totalGridLen = 28;
            this._totalPlayLen = 2;
        } else {
            ports = this._ports;
            playerNum = 4;
            this._map = this._2v2map;
            this._totalGridLen = 19;
            this._totalPlayLen = 4;
        }
        let transformInfo = this.getTransformInfo(type);
        let color = val || 0;
        for (let i = 0; i < playerNum; i++) {
            color = color % playerNum;
            var airPorts = ports["airport_" + i];
            var playerPort: Array<port> = ports["player_" + i];
            var gridNum = playerPort.length;
            var normalGridNum = gridNum - 5;
            for (let j = 0; j < gridNum; j++) {
                if (j < airPorts.length) {
                    var grid: LudoGrid = this.getGridByName(this.getGridName({ area: color, gridPosition: 101 + j }));
                    grid.port = airPorts[j];
                }
                var gridPosition = j > normalGridNum ? normalGridNum - j : j;
                let gridName = this.getGridName({ area: color, gridPosition: gridPosition });
                var grid: LudoGrid = this.getGridByName(gridName);
                if (transformInfo[gridName]) {
                    let transGridName = transformInfo[gridName];
                    if (transGridName) {
                        grid.transferName = transGridName;
                    }
                }
                grid.port = playerPort[j];
            }
            color++;
        }
    }
    public static getTransformInfo(type: yalla.data.jackaro.GameAgainstType): { [key: string]: string } {
        if (type == yalla.data.jackaro.GameAgainstType.ONE_VS_ONE) {
            return {
                "0_21": "1_7",
                "1_21": "0_7"
            };
        }
        return {};
    }
    // static setGeneralMap(map: JackaroMap) {
    //     this._curMap = map;
    // }

    public static convertGridPosition(gridPosition: number): number {
        if (gridPosition > 100) {
            return 100 + gridPosition % 100;
        }
        return gridPosition;
    }
    public static getGridName(station: Station): string {
        if (station && station.gridColor != undefined) {
            return `${station.gridColor}_${this.convertGridPosition(station.position)}`;
        } else {
            return station ? `${station.area}_${station.gridPosition}` : null;
        }
    }
    public static getGridByName(name: string) {
        return this._map[name];
    }
    public static getGridByStation(station: Station): LudoGrid {
        return this.getGridByName(this.getGridName(station))
    }
    //计算两个点直接的步数
    public static distanceStep(currentGrid: LudoGrid, goalGrid: LudoGrid): number {
        if (currentGrid && goalGrid) {
            if (currentGrid.inHome || goalGrid.inHome) return 1;
            var currentArea = currentGrid.station.area,
                currentPosition = Math.abs(currentGrid.station.gridPosition),
                goalArea = goalGrid.station.area,
                goalPosition = Math.abs(goalGrid.station.gridPosition);
            // if (currentArea == 3 && goalArea == 0) goalArea = 4;
            // return Math.abs((goalArea - currentArea) * 13 + goalPosition - currentPosition);
            if (currentArea < goalArea) currentArea += this._totalPlayLen;
            return (currentArea - goalArea) * this._totalGridLen + goalPosition - currentPosition;
        }
        return 0;
    }
    private static _bezierPool = {};
    public static createBezier(color: number, side: number) {
        var gridName = `${color}_21`;
        var grid1 = this.getGridByName(gridName);
        var grid3 = this.getGridByName(`${(color + 1) % 2}_7`);
        if (grid1 && grid3) {
            var p1 = grid1.port,
                p2 = side == 0 ? { x: 400, y: 300 } : { x: 400, y: 500 },
                p3 = grid3.port
            this._bezierPool[gridName] = new QuadraticBezier(p1, p2, p3);
        }
    }
    public static getBezier(gridName: string) {
        return this._bezierPool[gridName];
    }
    private static _2v2map = {
        "0_101": new LudoGrid({ area: 0, gridPosition: 101 }, "0_101", "0_2", null),
        "0_102": new LudoGrid({ area: 0, gridPosition: 102 }, "0_102", "0_2", null),
        "0_103": new LudoGrid({ area: 0, gridPosition: 103 }, "0_103", "0_2", null),
        "0_104": new LudoGrid({ area: 0, gridPosition: 104 }, "0_104", "0_2", null),
        "0_0": new LudoGrid({ area: 0, gridPosition: 0 }, "0_0", "0_1", "1_18"),
        "0_1": new LudoGrid({ area: 0, gridPosition: 1 }, "0_1", "0_2", "0_0"),
        "0_2": new LudoGrid({ area: 0, gridPosition: 2 }, "0_2", "0_3", "0_1"),
        "0_3": new LudoGrid({ area: 0, gridPosition: 3 }, "0_3", "0_4", "0_2"),
        "0_4": new LudoGrid({ area: 0, gridPosition: 4 }, "0_4", "0_5", "0_3"),
        "0_5": new LudoGrid({ area: 0, gridPosition: 5 }, "0_5", "0_6", "0_4"),
        "0_6": new LudoGrid({ area: 0, gridPosition: 6 }, "0_6", "0_7", "0_5"),
        "0_7": new LudoGrid({ area: 0, gridPosition: 7 }, "0_7", "0_8", "0_6"),
        "0_8": new LudoGrid({ area: 0, gridPosition: 8 }, "0_8", "0_9", "0_7"),
        "0_9": new LudoGrid({ area: 0, gridPosition: 9 }, "0_9", "0_10", "0_8"),
        "0_10": new LudoGrid({ area: 0, gridPosition: 10 }, "0_10", "0_11", "0_9"),
        "0_11": new LudoGrid({ area: 0, gridPosition: 11 }, "0_11", "0_12", "0_10"),
        "0_12": new LudoGrid({ area: 0, gridPosition: 12 }, "0_12", "0_13", "0_11"),
        "0_13": new LudoGrid({ area: 0, gridPosition: 13 }, "0_13", "0_14", "0_12"),
        "0_14": new LudoGrid({ area: 0, gridPosition: 14 }, "0_14", "0_15", "0_13"),
        "0_15": new LudoGrid({ area: 0, gridPosition: 15 }, "0_15", "0_16", "0_14"),
        "0_16": new LudoGrid({ area: 0, gridPosition: 16 }, "0_16", "0_17", "0_15"),
        "0_17": new LudoGrid({ area: 0, gridPosition: 17 }, "0_17", "0_18", "0_16"),
        "0_18": new LudoGrid({ area: 0, gridPosition: 18 }, "0_18", "3_0", "0_17"),
        "0_-1": new LudoGrid({ area: 0, gridPosition: -1 }, "0_-1", "0_-2", "0_0"),
        "0_-2": new LudoGrid({ area: 0, gridPosition: -2 }, "0_-2", "0_-3", "0_-1"),
        "0_-3": new LudoGrid({ area: 0, gridPosition: -3 }, "0_-3", "0_-4", "0_-2"),
        "0_-4": new LudoGrid({ area: 0, gridPosition: -4 }, "0_-4", "0_-5", "0_-3"),


        "1_101": new LudoGrid({ area: 1, gridPosition: 101 }, "1_101", "1_2", null),
        "1_102": new LudoGrid({ area: 1, gridPosition: 102 }, "1_102", "1_2", null),
        "1_103": new LudoGrid({ area: 1, gridPosition: 103 }, "1_103", "1_2", null),
        "1_104": new LudoGrid({ area: 1, gridPosition: 104 }, "1_104", "1_2", null),
        "1_0": new LudoGrid({ area: 1, gridPosition: 0 }, "1_0", "1_1", "2_18"),
        "1_1": new LudoGrid({ area: 1, gridPosition: 1 }, "1_1", "1_2", "1_0"),
        "1_2": new LudoGrid({ area: 1, gridPosition: 2 }, "1_2", "1_3", "1_1"),
        "1_3": new LudoGrid({ area: 1, gridPosition: 3 }, "1_3", "1_4", "1_2"),
        "1_4": new LudoGrid({ area: 1, gridPosition: 4 }, "1_4", "1_5", "1_3"),
        "1_5": new LudoGrid({ area: 1, gridPosition: 5 }, "1_5", "1_6", "1_4"),
        "1_6": new LudoGrid({ area: 1, gridPosition: 6 }, "1_6", "1_7", "1_5"),
        "1_7": new LudoGrid({ area: 1, gridPosition: 7 }, "1_7", "1_8", "1_6"),
        "1_8": new LudoGrid({ area: 1, gridPosition: 8 }, "1_8", "1_9", "1_7"),
        "1_9": new LudoGrid({ area: 1, gridPosition: 9 }, "1_9", "1_10", "1_8"),
        "1_10": new LudoGrid({ area: 1, gridPosition: 10 }, "1_10", "1_11", "1_9"),
        "1_11": new LudoGrid({ area: 1, gridPosition: 11 }, "1_11", "1_12", "1_10"),
        "1_12": new LudoGrid({ area: 1, gridPosition: 12 }, "1_12", "1_13", "1_11"),
        "1_13": new LudoGrid({ area: 1, gridPosition: 13 }, "1_13", "1_14", "1_12"),
        "1_14": new LudoGrid({ area: 1, gridPosition: 14 }, "1_14", "1_15", "1_13"),
        "1_15": new LudoGrid({ area: 1, gridPosition: 15 }, "1_15", "1_16", "1_14"),
        "1_16": new LudoGrid({ area: 1, gridPosition: 16 }, "1_16", "1_17", "1_15"),
        "1_17": new LudoGrid({ area: 1, gridPosition: 17 }, "1_17", "1_18", "1_16"),
        "1_18": new LudoGrid({ area: 1, gridPosition: 18 }, "1_18", "0_0", "1_17"),
        "1_-1": new LudoGrid({ area: 1, gridPosition: -1 }, "1_-1", "1_-2", "1_0"),
        "1_-2": new LudoGrid({ area: 1, gridPosition: -2 }, "1_-2", "1_-3", "1_-1"),
        "1_-3": new LudoGrid({ area: 1, gridPosition: -3 }, "1_-3", "1_-4", "1_-2"),
        "1_-4": new LudoGrid({ area: 1, gridPosition: -4 }, "1_-4", "1_-5", "1_-3"),

        "2_101": new LudoGrid({ area: 2, gridPosition: 101 }, "2_101", "2_2", null),
        "2_102": new LudoGrid({ area: 2, gridPosition: 102 }, "2_102", "2_2", null),
        "2_103": new LudoGrid({ area: 2, gridPosition: 103 }, "2_103", "2_2", null),
        "2_104": new LudoGrid({ area: 2, gridPosition: 104 }, "2_104", "2_2", null),
        "2_0": new LudoGrid({ area: 2, gridPosition: 0 }, "2_0", "2_1", "3_18"),
        "2_1": new LudoGrid({ area: 2, gridPosition: 1 }, "2_1", "2_2", "2_0"),
        "2_2": new LudoGrid({ area: 2, gridPosition: 2 }, "2_2", "2_3", "2_1"),
        "2_3": new LudoGrid({ area: 2, gridPosition: 3 }, "2_3", "2_4", "2_2"),
        "2_4": new LudoGrid({ area: 2, gridPosition: 4 }, "2_4", "2_5", "2_3"),
        "2_5": new LudoGrid({ area: 2, gridPosition: 5 }, "2_5", "2_6", "2_4"),
        "2_6": new LudoGrid({ area: 2, gridPosition: 6 }, "2_6", "2_7", "2_5"),
        "2_7": new LudoGrid({ area: 2, gridPosition: 7 }, "2_7", "2_8", "2_6"),
        "2_8": new LudoGrid({ area: 2, gridPosition: 8 }, "2_8", "2_9", "2_7"),
        "2_9": new LudoGrid({ area: 2, gridPosition: 9 }, "2_9", "2_10", "2_8"),
        "2_10": new LudoGrid({ area: 2, gridPosition: 10 }, "2_10", "2_11", "2_9"),
        "2_11": new LudoGrid({ area: 2, gridPosition: 11 }, "2_11", "2_12", "2_10"),
        "2_12": new LudoGrid({ area: 2, gridPosition: 12 }, "2_12", "2_13", "2_11"),
        "2_13": new LudoGrid({ area: 2, gridPosition: 13 }, "2_13", "2_14", "2_12"),
        "2_14": new LudoGrid({ area: 2, gridPosition: 14 }, "2_14", "2_15", "2_13"),
        "2_15": new LudoGrid({ area: 2, gridPosition: 15 }, "2_15", "2_16", "2_14"),
        "2_16": new LudoGrid({ area: 2, gridPosition: 16 }, "2_16", "2_17", "2_15"),
        "2_17": new LudoGrid({ area: 2, gridPosition: 17 }, "2_17", "2_18", "2_16"),
        "2_18": new LudoGrid({ area: 2, gridPosition: 18 }, "2_18", "1_0", "2_17"),
        "2_-1": new LudoGrid({ area: 2, gridPosition: -1 }, "2_-1", "2_-2", "2_0"),
        "2_-2": new LudoGrid({ area: 2, gridPosition: -2 }, "2_-2", "2_-3", "2_-1"),
        "2_-3": new LudoGrid({ area: 2, gridPosition: -3 }, "2_-3", "2_-4", "2_-2"),
        "2_-4": new LudoGrid({ area: 2, gridPosition: -4 }, "2_-4", "2_-5", "2_-3"),

        "3_101": new LudoGrid({ area: 3, gridPosition: 101 }, "3_101", "3_2", null),
        "3_102": new LudoGrid({ area: 3, gridPosition: 102 }, "3_102", "3_2", null),
        "3_103": new LudoGrid({ area: 3, gridPosition: 103 }, "3_103", "3_2", null),
        "3_104": new LudoGrid({ area: 3, gridPosition: 104 }, "3_104", "3_2", null),
        "3_0": new LudoGrid({ area: 3, gridPosition: 0 }, "3_0", "3_1", "0_18"),
        "3_1": new LudoGrid({ area: 3, gridPosition: 1 }, "3_1", "3_2", "3_0"),
        "3_2": new LudoGrid({ area: 3, gridPosition: 2 }, "3_2", "3_3", "3_1"),
        "3_3": new LudoGrid({ area: 3, gridPosition: 3 }, "3_3", "3_4", "3_2"),
        "3_4": new LudoGrid({ area: 3, gridPosition: 4 }, "3_4", "3_5", "3_3"),
        "3_5": new LudoGrid({ area: 3, gridPosition: 5 }, "3_5", "3_6", "3_4"),
        "3_6": new LudoGrid({ area: 3, gridPosition: 6 }, "3_6", "3_7", "3_5"),
        "3_7": new LudoGrid({ area: 3, gridPosition: 7 }, "3_7", "3_8", "3_6"),
        "3_8": new LudoGrid({ area: 3, gridPosition: 8 }, "3_8", "3_9", "3_7"),
        "3_9": new LudoGrid({ area: 3, gridPosition: 9 }, "3_9", "3_10", "3_8"),
        "3_10": new LudoGrid({ area: 3, gridPosition: 10 }, "3_10", "3_11", "3_9"),
        "3_11": new LudoGrid({ area: 3, gridPosition: 11 }, "3_11", "3_12", "3_10"),
        "3_12": new LudoGrid({ area: 3, gridPosition: 12 }, "3_12", "3_13", "3_11"),
        "3_13": new LudoGrid({ area: 3, gridPosition: 13 }, "3_13", "3_14", "3_12"),
        "3_14": new LudoGrid({ area: 3, gridPosition: 14 }, "3_14", "3_15", "3_13"),
        "3_15": new LudoGrid({ area: 3, gridPosition: 15 }, "3_15", "3_16", "3_14"),
        "3_16": new LudoGrid({ area: 3, gridPosition: 16 }, "3_16", "3_17", "3_15"),
        "3_17": new LudoGrid({ area: 3, gridPosition: 17 }, "3_17", "3_18", "3_16"),
        "3_18": new LudoGrid({ area: 3, gridPosition: 18 }, "3_18", "2_0", "3_17"),
        "3_-1": new LudoGrid({ area: 3, gridPosition: -1 }, "3_-1", "3_-2", "3_0"),
        "3_-2": new LudoGrid({ area: 3, gridPosition: -2 }, "3_-2", "3_-3", "3_-1"),
        "3_-3": new LudoGrid({ area: 3, gridPosition: -3 }, "3_-3", "3_-4", "3_-2"),
        "3_-4": new LudoGrid({ area: 3, gridPosition: -4 }, "3_-4", "3_-5", "3_-3"),
    }
    private static _ports: Maps = {
        "airport_0": [{ "x": 206, "y": 623 }, { "x": 235, "y": 594 }, { "x": 177, "y": 594 }, { "x": 206, "y": 565 }],
        "airport_1": [{ "x": 594, "y": 565 }, { "x": 623, "y": 594 }, { "x": 565, "y": 594 }, { "x": 594, "y": 623 }],
        "airport_2": [{ "x": 594, "y": 177 }, { "x": 623, "y": 206 }, { "x": 565, "y": 206 }, { "x": 594, "y": 235 }],
        "airport_3": [{ "x": 206, "y": 235 }, { "x": 235, "y": 206 }, { "x": 177, "y": 206 }, { "x": 206, "y": 177 }],
        "player_0": [{ "x": 400, "y": 732 }, { "x": 368, "y": 732 }, { "x": 337, "y": 732 }, { "x": 337, "y": 701 }, { "x": 337, "y": 670 }, { "x": 337, "y": 638 }, { "x": 337, "y": 606 }, { "x": 337, "y": 575 }, { "x": 315, "y": 553 }, { "x": 292, "y": 530 }, { "x": 269, "y": 508 }, { "x": 247, "y": 485 }, { "x": 224, "y": 462 }, { "x": 193, "y": 462 }, { "x": 162, "y": 462 }, { "x": 130, "y": 462 }, { "x": 99, "y": 462 }, { "x": 68, "y": 462 }, { "x": 68, "y": 432 }, { "x": 400, "y": 701 }, { "x": 400, "y": 670 }, { "x": 400, "y": 638 }, { "x": 400, "y": 607 }],
        "player_3": [{ "x": 68, "y": 400 }, { "x": 68, "y": 368 }, { "x": 68, "y": 337 }, { "x": 99, "y": 337 }, { "x": 130, "y": 337 }, { "x": 162, "y": 337 }, { "x": 194, "y": 337 }, { "x": 225, "y": 337 }, { "x": 247, "y": 315 }, { "x": 270, "y": 292 }, { "x": 292, "y": 269 }, { "x": 315, "y": 247 }, { "x": 338, "y": 224 }, { "x": 338, "y": 193 }, { "x": 338, "y": 162 }, { "x": 338, "y": 130 }, { "x": 338, "y": 99 }, { "x": 338, "y": 68 }, { "x": 368, "y": 68 }, { "x": 99, "y": 400 }, { "x": 130, "y": 400 }, { "x": 162, "y": 400 }, { "x": 193, "y": 400 }],
        "player_2": [{ "x": 400, "y": 68 }, { "x": 432, "y": 68 }, { "x": 463, "y": 68 }, { "x": 463, "y": 99 }, { "x": 463, "y": 130 }, { "x": 463, "y": 162 }, { "x": 463, "y": 194 }, { "x": 463, "y": 225 }, { "x": 485, "y": 247 }, { "x": 508, "y": 270 }, { "x": 531, "y": 292 }, { "x": 553, "y": 315 }, { "x": 576, "y": 338 }, { "x": 607, "y": 338 }, { "x": 638, "y": 338 }, { "x": 670, "y": 338 }, { "x": 701, "y": 338 }, { "x": 732, "y": 338 }, { "x": 732, "y": 368 }, { "x": 400, "y": 99 }, { "x": 400, "y": 130 }, { "x": 400, "y": 162 }, { "x": 400, "y": 193 }],
        "player_1": [{ "x": 732, "y": 400 }, { "x": 732, "y": 432 }, { "x": 732, "y": 463 }, { "x": 701, "y": 463 }, { "x": 670, "y": 463 }, { "x": 638, "y": 463 }, { "x": 606, "y": 463 }, { "x": 575, "y": 463 }, { "x": 553, "y": 485 }, { "x": 530, "y": 508 }, { "x": 508, "y": 531 }, { "x": 485, "y": 553 }, { "x": 462, "y": 576 }, { "x": 462, "y": 607 }, { "x": 462, "y": 638 }, { "x": 462, "y": 670 }, { "x": 462, "y": 701 }, { "x": 462, "y": 732 }, { "x": 432, "y": 732 }, { "x": 701, "y": 400 }, { "x": 670, "y": 400 }, { "x": 638, "y": 400 }, { "x": 607, "y": 400 }]
    }
    private static _1v1map = {
        "0_101": new LudoGrid({ area: 0, gridPosition: 101 }, "0_101", "0_2", null),
        "0_102": new LudoGrid({ area: 0, gridPosition: 102 }, "0_102", "0_2", null),
        "0_103": new LudoGrid({ area: 0, gridPosition: 103 }, "0_103", "0_2", null),
        "0_104": new LudoGrid({ area: 0, gridPosition: 104 }, "0_104", "0_2", null),
        "0_0": new LudoGrid({ area: 0, gridPosition: 0 }, "0_0", "0_1", "1_27"),
        "0_1": new LudoGrid({ area: 0, gridPosition: 1 }, "0_1", "0_2", "0_0"),
        "0_2": new LudoGrid({ area: 0, gridPosition: 2 }, "0_2", "0_3", "0_1"),
        "0_3": new LudoGrid({ area: 0, gridPosition: 3 }, "0_3", "0_4", "0_2"),
        "0_4": new LudoGrid({ area: 0, gridPosition: 4 }, "0_4", "0_5", "0_3"),
        "0_5": new LudoGrid({ area: 0, gridPosition: 5 }, "0_5", "0_6", "0_4"),
        "0_6": new LudoGrid({ area: 0, gridPosition: 6 }, "0_6", "0_7", "0_5"),
        "0_7": new LudoGrid({ area: 0, gridPosition: 7 }, "0_7", "0_8", "0_6"),
        "0_8": new LudoGrid({ area: 0, gridPosition: 8 }, "0_8", "0_9", "0_7"),
        "0_9": new LudoGrid({ area: 0, gridPosition: 9 }, "0_9", "0_10", "0_8"),
        "0_10": new LudoGrid({ area: 0, gridPosition: 10 }, "0_10", "0_11", "0_9"),
        "0_11": new LudoGrid({ area: 0, gridPosition: 11 }, "0_11", "0_12", "0_10"),
        "0_12": new LudoGrid({ area: 0, gridPosition: 12 }, "0_12", "0_13", "0_11"),
        "0_13": new LudoGrid({ area: 0, gridPosition: 13 }, "0_13", "0_14", "0_12"),
        "0_14": new LudoGrid({ area: 0, gridPosition: 14 }, "0_14", "0_15", "0_13"),
        "0_15": new LudoGrid({ area: 0, gridPosition: 15 }, "0_15", "0_16", "0_14"),
        "0_16": new LudoGrid({ area: 0, gridPosition: 16 }, "0_16", "0_17", "0_15"),
        "0_17": new LudoGrid({ area: 0, gridPosition: 17 }, "0_17", "0_18", "0_16"),
        "0_18": new LudoGrid({ area: 0, gridPosition: 18 }, "0_18", "0_19", "0_17"),
        "0_19": new LudoGrid({ area: 0, gridPosition: 19 }, "0_19", "0_20", "0_18"),
        "0_20": new LudoGrid({ area: 0, gridPosition: 20 }, "0_20", "0_21", "0_19"),
        "0_21": new LudoGrid({ area: 0, gridPosition: 21 }, "0_21", "0_22", "0_20"),
        "0_22": new LudoGrid({ area: 0, gridPosition: 22 }, "0_22", "0_23", "0_21"),
        "0_23": new LudoGrid({ area: 0, gridPosition: 23 }, "0_23", "0_24", "0_22"),
        "0_24": new LudoGrid({ area: 0, gridPosition: 24 }, "0_24", "0_25", "0_23"),
        "0_25": new LudoGrid({ area: 0, gridPosition: 25 }, "0_25", "0_26", "0_24"),
        "0_26": new LudoGrid({ area: 0, gridPosition: 26 }, "0_26", "0_27", "0_25"),
        "0_27": new LudoGrid({ area: 0, gridPosition: 27 }, "0_27", "1_0", "0_26"),

        "0_-1": new LudoGrid({ area: 0, gridPosition: -1 }, "0_-1", "0_-2", "0_0"),
        "0_-2": new LudoGrid({ area: 0, gridPosition: -2 }, "0_-2", "0_-3", "0_-1"),
        "0_-3": new LudoGrid({ area: 0, gridPosition: -3 }, "0_-3", "0_-4", "0_-2"),
        "0_-4": new LudoGrid({ area: 0, gridPosition: -4 }, "0_-4", "0_-5", "0_-3"),

        "1_101": new LudoGrid({ area: 1, gridPosition: 101 }, "1_101", "1_2", null),
        "1_102": new LudoGrid({ area: 1, gridPosition: 102 }, "1_102", "1_2", null),
        "1_103": new LudoGrid({ area: 1, gridPosition: 103 }, "1_103", "1_2", null),
        "1_104": new LudoGrid({ area: 1, gridPosition: 104 }, "1_104", "1_2", null),
        "1_0": new LudoGrid({ area: 1, gridPosition: 0 }, "1_0", "1_1", "0_27"),
        "1_1": new LudoGrid({ area: 1, gridPosition: 1 }, "1_1", "1_2", "1_0"),
        "1_2": new LudoGrid({ area: 1, gridPosition: 2 }, "1_2", "1_3", "1_1"),
        "1_3": new LudoGrid({ area: 1, gridPosition: 3 }, "1_3", "1_4", "1_2"),
        "1_4": new LudoGrid({ area: 1, gridPosition: 4 }, "1_4", "1_5", "1_3"),
        "1_5": new LudoGrid({ area: 1, gridPosition: 5 }, "1_5", "1_6", "1_4"),
        "1_6": new LudoGrid({ area: 1, gridPosition: 6 }, "1_6", "1_7", "1_5"),
        "1_7": new LudoGrid({ area: 1, gridPosition: 7 }, "1_7", "1_8", "1_6"),
        "1_8": new LudoGrid({ area: 1, gridPosition: 8 }, "1_8", "1_9", "1_7"),
        "1_9": new LudoGrid({ area: 1, gridPosition: 9 }, "1_9", "1_10", "1_8"),
        "1_10": new LudoGrid({ area: 1, gridPosition: 10 }, "1_10", "1_11", "1_9"),
        "1_11": new LudoGrid({ area: 1, gridPosition: 11 }, "1_11", "1_12", "1_10"),
        "1_12": new LudoGrid({ area: 1, gridPosition: 12 }, "1_12", "1_13", "1_11"),
        "1_13": new LudoGrid({ area: 1, gridPosition: 13 }, "1_13", "1_14", "1_12"),
        "1_14": new LudoGrid({ area: 1, gridPosition: 14 }, "1_14", "1_15", "1_13"),
        "1_15": new LudoGrid({ area: 1, gridPosition: 15 }, "1_15", "1_16", "1_14"),
        "1_16": new LudoGrid({ area: 1, gridPosition: 16 }, "1_16", "1_17", "1_15"),
        "1_17": new LudoGrid({ area: 1, gridPosition: 17 }, "1_17", "1_18", "1_16"),
        "1_18": new LudoGrid({ area: 1, gridPosition: 18 }, "1_18", "1_19", "1_17"),
        "1_19": new LudoGrid({ area: 1, gridPosition: 19 }, "1_19", "1_20", "1_18"),
        "1_20": new LudoGrid({ area: 1, gridPosition: 20 }, "1_20", "1_21", "1_19"),
        "1_21": new LudoGrid({ area: 1, gridPosition: 21 }, "1_21", "1_22", "1_20"),
        "1_22": new LudoGrid({ area: 1, gridPosition: 22 }, "1_22", "1_23", "1_21"),
        "1_23": new LudoGrid({ area: 1, gridPosition: 23 }, "1_23", "1_24", "1_22"),
        "1_24": new LudoGrid({ area: 1, gridPosition: 24 }, "1_24", "1_25", "1_23"),
        "1_25": new LudoGrid({ area: 1, gridPosition: 25 }, "1_25", "1_26", "1_24"),
        "1_26": new LudoGrid({ area: 1, gridPosition: 26 }, "1_26", "1_27", "1_25"),
        "1_27": new LudoGrid({ area: 1, gridPosition: 27 }, "1_27", "0_0", "1_26"),

        "1_-1": new LudoGrid({ area: 1, gridPosition: -1 }, "1_-1", "1_-2", "1_0"),
        "1_-2": new LudoGrid({ area: 1, gridPosition: -2 }, "1_-2", "1_-3", "1_-1"),
        "1_-3": new LudoGrid({ area: 1, gridPosition: -3 }, "1_-3", "1_-4", "1_-2"),
        "1_-4": new LudoGrid({ area: 1, gridPosition: -4 }, "1_-4", "1_-5", "1_-3"),
    }
    private static _1v1ports: Maps = {
        "airport_0": [{ "x": 206, "y": 623 }, { "x": 235, "y": 594 }, { "x": 177, "y": 594 }, { "x": 206, "y": 565 }],
        "airport_1": [{ "x": 594, "y": 177 }, { "x": 623, "y": 206 }, { "x": 565, "y": 206 }, { "x": 594, "y": 235 }],

        "player_0": [
            { "x": 400, "y": 732 }, { "x": 368, "y": 732 }, { "x": 337, "y": 732 },
            { "x": 337, "y": 701 }, { "x": 337, "y": 670 }, { "x": 337, "y": 638 },
            { "x": 337, "y": 606 }, { "x": 337, "y": 575 }, { "x": 315, "y": 553 },
            { "x": 292, "y": 530 }, { "x": 269, "y": 508 }, { "x": 247, "y": 485 },

            { "x": 224, "y": 462 }, { "x": 224, "y": 431 }, { "x": 224, "y": 400 }, { "x": 224, "y": 369 }, { "x": 224, "y": 338 },

            { "x": 247, "y": 315 }, { "x": 270, "y": 292 }, { "x": 292, "y": 269 },
            { "x": 315, "y": 247 }, { "x": 338, "y": 224 }, { "x": 338, "y": 193 },
            { "x": 338, "y": 162 }, { "x": 338, "y": 130 }, { "x": 338, "y": 99 },
            { "x": 338, "y": 68 }, { "x": 368, "y": 68 },

            { "x": 400, "y": 701 }, { "x": 400, "y": 670 }, { "x": 400, "y": 638 }, { "x": 400, "y": 607 }
        ],
        "player_1": [
            { "x": 400, "y": 68 }, { "x": 432, "y": 68 }, { "x": 463, "y": 68 },
            { "x": 463, "y": 99 }, { "x": 463, "y": 130 }, { "x": 463, "y": 162 },
            { "x": 463, "y": 194 }, { "x": 463, "y": 225 }, { "x": 485, "y": 247 },
            { "x": 508, "y": 270 }, { "x": 531, "y": 292 }, { "x": 553, "y": 315 },

            { "x": 576, "y": 338 }, { "x": 576, "y": 369 }, { "x": 576, "y": 400 }, { "x": 576, "y": 431 }, { "x": 576, "y": 463 },

            { "x": 553, "y": 485 }, { "x": 530, "y": 508 }, { "x": 508, "y": 531 },
            { "x": 485, "y": 553 }, { "x": 462, "y": 576 }, { "x": 462, "y": 607 },
            { "x": 462, "y": 638 }, { "x": 462, "y": 670 }, { "x": 462, "y": 701 },
            { "x": 462, "y": 732 }, { "x": 432, "y": 732 },

            { "x": 400, "y": 99 }, { "x": 400, "y": 130 }, { "x": 400, "y": 162 }, { "x": 400, "y": 193 }],



    }
    private static _map: any = this._2v2map;
}