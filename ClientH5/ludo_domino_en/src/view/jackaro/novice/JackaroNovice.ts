
class JackaroNovice extends ui.jackaro.novice.noviceUI {
    private bubble: JackaroNoviceBubble = null;
    public tip: ui.jackaro.novice.novice_tipUI = null;
    private finger: <PERSON><PERSON><PERSON>inger = null;
    private tpl: Laya.Templet = null;
    private jackSk: Laya.Skeleton = null;
    private aniName: string = "ani_en"
    constructor() {
        super()
        this.bubble = new JackaroNoviceBubble();
        this.tip = new ui.jackaro.novice.novice_tipUI();
        if (yalla.Font.isRight()) {
            this.tip.tip.skin = "jackaro/novice/tip_end_ar.png";
            this.aniName = "ani_ar";
            this.tip.btn_playnow.x = 271;
            this.tip.btn_watchagain.x = 478;
        }
        this.addChildAt(this.bubble, 1)
        this.addChildAt(this.tip, 1)
        this.finger = new JackarooFinger();
        this.tpl = new Laya.Templet();
        this.tpl.on(Laya.Event.COMPLETE, this, this.onSkLoad)
        this.tpl.loadAni("res/sk/novice/jiaoHuan01.sk");
        this.init();
    }

    public init() {
        this.nextTurn && this.nextTurn.pos(Laya.stage.width / 2, Laya.stage.height / 2 - 100);
    }
    public noviceEnd() {
        this.mask_bg.visible = true;
        this.mask_bg.mouseThrough = false;
        if (!this.tip) {
            this.tip = new ui.jackaro.novice.novice_tipUI();
            this.addChild(this.tip);
        }
        this.tip.visible = true;
        this.addNoviceEndLisenter();
    }
    private onSkLoad() {
        this.jackSk = this.tpl.buildArmature(1);
        this.addChild(this.jackSk);
    }
    public noviceAgain() {
        this.mask_bg.visible = false;
        this.mask_bg.mouseThrough = true;
        this.tip.visible = false;
    }
    private addNoviceEndLisenter() {
        this.tip.btn_playnow.on(Laya.Event.CLICK, this, this.playNow);
    }
    private removeNoviceLisenter() {
        this.tip.btn_playnow.off(Laya.Event.CLICK, this, this.playNow);
    }
    showBubble(tipIndex: string, card: string, side: string, centerY: number = -320) {
        this.bubble.show(tipIndex, card, side, centerY);
    }
    hideBubble() {
        this.bubble.hide();
    }
    clear() {
        this.removeNoviceLisenter();
        this.finger && this.finger.stop();
        this.nextTurn.offAll();
        this.hideJackTip();
        this.jackSk = null;
    }
    private playNow() {
        this.removeNoviceLisenter();
        yalla.util.clogDBAmsg("10394");
        yalla.Native.instance.quickGame({
            gamePay: 0,
            gameId: GameType.JACKARO,
            playerNum: 4,
            gameType: 100,
            gameGroup: 0,
            // isPrivate: 10
        });
        // this.clear();
    }
    showNextTurn(cb: Function = () => { }) {
        this.nextTurn.visible = true;
        this.nextTurn.play(0, false, this.aniName);
        this.nextTurn.offAll();
        this.nextTurn.once(Laya.Event.COMPLETE, this, cb);
    }
    private lastNode = null;
    private offSetY = 0;
    private isChild = false;

    showFinger(aniName: string, node: Laya.Sprite, offSetY: number, side: string, showRipple: boolean, isChild: boolean) {//aniName dclick;双击 silder 滑动
        if (node) {
            if (this.finger.displayedInStage) {
                if (node === this.lastNode)
                    return;
                this.finger.removeSelf();
            }
            this.lastNode = node;
            this.isChild = isChild;
            if (isChild) {
                this.finger.pos(node.width / 2, node.height / 2 + offSetY);
                this.timer.callLater(this, () => {
                    if (node && node.parent) {
                        node.parent.setChildIndex(node, node.parent.numChildren - 1);
                    }
                })
                node.addChild(this.finger);
            } else {
                let point = node.localToGlobal(new Laya.Point(), true);
                this.finger.pos(point.x + node.width / 2, point.y + node.height / 2 + offSetY);
                this.offSetY = offSetY;
                this.addChild(this.finger);
            }
            if (side == "left") {
                this.finger.scaleX = -1;
            } else {
                this.finger.scaleX = 1;
            }
            this.finger.play(aniName, showRipple);
        }
    }
    onResize() {
        !this.isChild && this.refreshPos();
    }
    refreshPos() {
        if (this.finger.displayedInStage && this.lastNode) {
            let point = this.lastNode.localToGlobal(new Laya.Point(), true);
            this.finger.pos(point.x + this.lastNode.width / 2, point.y + this.lastNode.height / 2 + this.offSetY);
        }
    }
    showJackTip(chess: JackaroChess) {
        if (chess && this.jackSk) {
            let point = chess.localToGlobal(new Laya.Point(), true);
            this.jackSk.pos(point.x + 68, point.y + chess.height / 2);
            this.jackSk.visible = true;
            this.jackSk.play(0, true)
        }
    }
    hideJackTip() {
        if (this.jackSk) {
            this.jackSk.stop();
            this.jackSk.visible = false;
        }
    }
    hideFinger() {
        if (this.finger && this.finger.displayedInStage) {
            this.finger.stop();
            this.finger.removeSelf();
        }
    }
}