class JackaroNoviceBubble extends ui.jackaro.novice.novice_bubbleUI {
    constructor() {
        super();
        if (yalla.Font.isRight()) this.lang = "ar";
    }
    private lang: string = "en";
    public show(tipIndex: string, card: string, side: string, centerY: number) {
        let cardSkin = "card_"
        if (side == "left") {
            this.bg.scaleX = 1;
            this.card.pos(135, 68);
            this.tip_img.x = 440;
            cardSkin += "l_";
        } else {
            this.bg.scaleX = -1;
            this.card.pos(526, 68);
            this.tip_img.x = 282;
            cardSkin += "r_";
        }
        this.centerY = centerY;
        cardSkin += card;
        this.tip_img.skin = `jackaro/novice/tip_${tipIndex}_${this.lang}.png`;
        this.card.skin = `jackaro/novice/${cardSkin}.png`;
        this.visible = true;
    }
    public hide() {
        this.visible = false;
    }



}