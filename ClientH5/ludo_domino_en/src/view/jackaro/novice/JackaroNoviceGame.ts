

class JackaroNoviceGame extends yalla.view.game.jackaro.Game {
    private novice: JackaroNovice = null;
    private noviceData: any = { "login": { "result": 1, "code": 1, "command": 1, "playerSelfData": { "gold": 0, "diamond": 0, "isFirstRecharge": true }, "selfGameData": {}, "restoreGame": { "gameData": { "playerDataMap": { "10001": { "idx": 10001, "banker": true, "color": 0, "chessPosition": { "1": { "order": 1, "position": 201, "gridColor": 0 }, "2": { "order": 2, "position": 202, "gridColor": 0 }, "3": { "order": 3, "position": 203, "gridColor": 0 }, "4": { "order": 4, "position": 204, "gridColor": 0 } }, "remainThinkingTime": 0 }, "10002": { "idx": 10002, "color": 1, "chessPosition": { "1": { "order": 1, "position": 401, "gridColor": 1 }, "2": { "order": 2, "position": 402, "gridColor": 1 }, "3": { "order": 3, "position": 403, "gridColor": 1 }, "4": { "order": 4, "position": 404, "gridColor": 1 } }, "remainThinkingTime": 0 }, "10003": { "idx": 10003, "color": 2, "chessPosition": { "1": { "order": 1, "position": 301, "gridColor": 2 }, "2": { "order": 2, "position": 302, "gridColor": 2 }, "3": { "order": 3, "position": 303, "gridColor": 2 }, "4": { "order": 4, "position": 304, "gridColor": 2 } }, "remainThinkingTime": 0 }, "10004": { "idx": 10004, "color": 3, "chessPosition": { "1": { "order": 1, "position": 101, "gridColor": 3 }, "2": { "order": 2, "position": 102, "gridColor": 3 }, "3": { "order": 3, "position": 103, "gridColor": 3 }, "4": { "order": 4, "position": 104, "gridColor": 3 } }, "playerStatus": 1 } }, "againstType": 1, "teamInfo": { "teamAPlayer1": 10001, "teamAPlayer2": 10003, "teamBPlayer1": 10002, "teamBPlayer2": 10004 }, "chessPanel": { "gameStatus": 1, "currentPlayer": 10001 }, "bets": 0 }, "playShowInfo": { "10001": { "playerShowData": { "idx": 10001, "faceUrl": "unkown", "nickName": "حسين", "prettyId": "10001", "royalLevelNameAnimation": true }, "showGameData": { "color": 0 } }, "10002": { "playerShowData": { "idx": 10002, "faceUrl": "unkown", "nickName": "يوسف", "prettyId": "10002" }, "showGameData": { "color": 1 } }, "10003": { "playerShowData": { "idx": 10003, "faceUrl": "unkown", "nickName": "مصطفى", "prettyId": "10003" }, "showGameData": { "color": 2 } }, "10004": { "playerShowData": { "idx": 10004, "faceUrl": "unkown", "nickName": "مصطفى", "prettyId": "10004" }, "showGameData": { "color": 3 } } } } }, "dealPokerPub": { "command": 101, "totalPokerCount": 52, "handPoker": [14, 28, 17, 5], "idx": 10002 }, "changePlayerResponse": { "command": 103, "chessPanel": { "gameStatus": 1, "currentPoker": 14, "currentPlayer": 10002 }, "currentPlayerStatus": 1, "allowPokerData": { "14": { "commonResult": { "playResultData": { "0": { "result": [{ "fromGrid": { "grid": { "index": 401, "color": 1 }, "chess": { "idx": 10002, "order": 1, "color": 1 } }, "toGrid": { "grid": { "index": 2, "color": 1, "type": 3 } } }] }, "11": { "result": [{ "fromGrid": { "grid": { "index": 401, "color": 1 }, "chess": { "idx": 10002, "order": 1, "color": 1 } }, "toGrid": { "grid": { "index": 2, "color": 1, "type": 3 } } }] }, "1": { "result": [{ "fromGrid": { "grid": { "index": 401, "color": 1 }, "chess": { "idx": 10002, "order": 1, "color": 1 } }, "toGrid": { "grid": { "index": 2, "color": 1, "type": 3 } } }] } } }, "showPreResult": true } } }, "forcedDiscardPubResponse": { "command": 114, "forcedDiscardIdx": 10002, "currentPoker": 10, "playPokerPlayerId": 10001 }, "changePlayerResponse2": { "command": 103, "chessPanel": { "gameStatus": 1, "currentPoker": 28, "currentPlayer": 10002 }, "currentPlayerStatus": 2, "allowPokerData": { "28": { "pokerTenDiscard": true, "commonResult": null, "showPreResult": true } } }, "changePlayerResponse3": { "command": 103, "chessPanel": { "gameStatus": 1, "currentPoker": 17, "currentPlayer": 10002 }, "currentPlayerStatus": 1, "allowPokerData": { "17": { "commonResult": { "playResultData": { "-4": { "result": [{ "fromGrid": { "grid": { "index": 2, "color": 1, "type": 0 }, "chess": { "idx": 10002, "order": 1, "color": 1 } }, "toGrid": { "grid": { "index": 17, "color": 2, "type": 0 }, "chess": { "idx": 10002, "order": 1, "color": 1 } } }] } } }, "showPreResult": true } } }, "changePlayerResponse4": { "command": 103, "chessPanel": { "gameStatus": 1, "currentPoker": 5, "currentPlayer": 10002 }, "currentPlayerStatus": 1, "allowPokerData": { "5": { "commonResult": { "playResultData": { "5": { "result": [{ "fromGrid": { "grid": { "index": 17, "color": 2, "type": 0 }, "chess": { "idx": 10002, "order": 1, "color": 1 } }, "toGrid": { "grid": { "index": -3, "color": 1, "type": 0 }, "chess": {} } }, { "fromGrid": { "grid": { "index": 5, "color": 2, "type": 0 }, "chess": { "idx": 10003, "order": 1, "color": 2 } }, "toGrid": { "grid": { "index": 10, "color": 2, "type": 0 }, "chess": {} } }] } } }, "showPreResult": true } } }, "dealPokerPub5": { "command": 101, "totalPokerCount": 36, "handPoker": [7, 34, 24, 26], "idx": 10002 }, "gameData5": { "playerDataMap": { "10001": { "idx": 10001, "banker": true, "color": 0, "chessPosition": { "1": { "order": 1, "position": 18, "gridColor": 2 }, "2": { "order": 2, "position": 202, "gridColor": 0 }, "3": { "order": 3, "position": 203, "gridColor": 0 }, "4": { "order": 4, "position": 204, "gridColor": 0 } }, "remainThinkingTime": 0 }, "10002": { "idx": 10002, "color": 1, "chessPosition": { "1": { "order": 1, "position": -4, "gridColor": 1 }, "2": { "order": 2, "position": -3, "gridColor": 1 }, "3": { "order": 3, "position": 8, "gridColor": 2 }, "4": { "order": 4, "position": 404, "gridColor": 1 } }, "remainThinkingTime": 0 }, "10003": { "idx": 10003, "color": 2, "chessPosition": { "1": { "order": 1, "position": 301, "gridColor": 2 }, "2": { "order": 2, "position": 302, "gridColor": 2 }, "3": { "order": 3, "position": 303, "gridColor": 2 }, "4": { "order": 4, "position": 304, "gridColor": 2 } }, "remainThinkingTime": 0 }, "10004": { "idx": 10004, "color": 3, "chessPosition": { "1": { "order": 1, "position": -1, "gridColor": 3 }, "2": { "order": 2, "position": -2, "gridColor": 3 }, "3": { "order": 3, "position": -3, "gridColor": 3 }, "4": { "order": 4, "position": -4, "gridColor": 3 } }, "playerStatus": 1 } }, "againstType": 1, "teamInfo": { "teamAPlayer1": 10001, "teamAPlayer2": 10003, "teamBPlayer1": 10002, "teamBPlayer2": 10004 }, "chessPanel": { "gameStatus": 1, "currentPlayer": 10001 }, "bets": 0 }, "changePlayerResponse5": { "command": 103, "chessPanel": { "gameStatus": 1, "currentPoker": 26, "currentPlayer": 10002 }, "currentPlayerStatus": 1, "allowPokerData": { "26": { "commonResult": { "playResultData": { "0": { "result": [{ "fromGrid": { "grid": { "index": 404, "color": 1 }, "chess": { "idx": 10002, "order": 4, "color": 1 } }, "toGrid": { "grid": { "index": 2, "color": 1, "type": 3 } } }] } } }, "showPreResult": true } } }, "changePlayerResponse6": { "command": 103, "chessPanel": { "gameStatus": 1, "currentPoker": 26, "currentPlayer": 10002 }, "currentPlayerStatus": 1, "allowPokerData": { "24": { "jackResult": { "playResultData": { "10001": { "result": [{ "fromGrid": { "grid": { "index": 2, "color": 1, "type": 2 }, "chess": { "idx": 10002, "order": 4 } }, "toGrid": { "grid": { "index": 18, "color": 2, "type": 3 }, "chess": { "idx": 10001, "order": 1, "color": 2 } } }] } } }, "showPreResult": true } } }, "exchangeChessPub": { "selfMoveResultData": { "fromGrid": { "grid": { "index": 18, "color": 2, "type": 3 }, "chess": { "idx": 10002, "order": 4 } }, "toGrid": { "grid": { "index": 2, "color": 1, "type": 1 }, "chess": { "idx": 10001, "order": 1, "color": 2 } } }, "currentPoker": 24, "remainPokerCount": 2, "moveChessPlayer": 10002, "moveChess": true }, "changePlayerResponse7": { "command": 103, "chessPanel": { "gameStatus": 1, "currentPoker": 7, "currentPlayer": 10002 }, "currentPlayerStatus": 1, "allowPokerData": { "7": { "commonResult": { "playResultData": { "3": { "result": [{ "fromGrid": { "grid": { "index": 18, "color": 2, "type": 3 }, "chess": { "idx": 10002, "order": 4, "color": 1 } }, "toGrid": { "grid": { "index": -2, "color": 1, "type": 3 } } }] }, "7": { "result": [{ "fromGrid": { "grid": { "index": 8, "color": 2, "type": 2 }, "chess": { "idx": 10002, "order": 3, "color": 1 } }, "toGrid": { "grid": { "index": 15, "color": 2, "type": 4 } } }] } } }, "showPreResult": true } } }, "changePlayerResponse8": { "command": 103, "chessPanel": { "gameStatus": 1, "currentPoker": 34, "currentPlayer": 10002 }, "currentPlayerStatus": 1, "allowPokerData": { "34": { "commonResult": { "playResultData": { "8": { "result": [{ "fromGrid": { "grid": { "index": 12, "color": 2 }, "chess": { "idx": 10002, "order": 3, "color": 1 } }, "toGrid": { "grid": { "index": -1, "color": 1, "type": 3 } } }] } } }, "showPreResult": true } } } };
    private myIdx = 10002;
    private step: number = 0;
    private aniTime: boolean = false;//动画中
    private currPoker: number = null;
    private currCard: yalla.view.jackaro.JackaroCardItem = null;
    constructor() {
        super();
        yalla.Global.isFouce = true;
        yalla.Sound.canPlayMusic = true;
        yalla.data.jackaro.JackaroUserService.instance['sendMsg'] = () => { };
        yalla.data.jackaro.JackaroUserService.instance['getCurUserId'] = () => { return this.myIdx };
        yalla.data.jackaro.JackaroUserService.instance['sendExchangeChess'] = this.handleExchangeChessPub.bind(this);
        JackaroChessManager.instance.isNewExpPlayLogic = () => { return true };
        let self = this;
        let tipMagr = TipsViewManager.getInstance();
        tipMagr.showTip = function (type: TipViewType, params?: any) {//改写showTip方法拦截弹窗
            if (type == TipViewType.RULE_CARD) return;//++改写+++隐藏默认的出牌提示
            let view = this.viewMap[type];
            let overwrite = !view;
            if (!view) {
                view = this.createView(type);
                if (!TipViewLayer[type]) { //没有定义层级，直接添加在游戏视图上
                    view.setLayer(this.curGameView);
                } else {
                    view.setLayer(this.curGameView.getChildByName(TipViewLayer[type]) as Laya.Box);
                }
                if (TipsViewZorder[type]) {
                    view.zOrder = TipsViewZorder[type];
                }
                this.viewMap[type] = view;
            }
            switch (type) {
                case TipViewType.SELECT_OPERATE://拦截选择A出牌的弹窗
                    overwrite && self.overwriteSelectStepOperateView(view);
                    break;
                case TipViewType.EXCHANGE_CHESS://拦截出牌J之后选择棋子的
                    overwrite && self.overwariteExChangeChessView(view);
                    let chess: JackaroChess = JackaroChessManager.instance.getChessById("10001_1");
                    chess && chess.clickArea.on("click", self, self.clearChessEvent, [chess]);
                    break;
                case TipViewType.CHOOSE_CHESS_OPERATE://出牌7 选择棋子之后选择点数的弹窗
                    overwrite && self.overwariteChooseChessOperateView(view);
                    if (self.step == 7) {//拦截选择棋子7的移动步数
                        self.showChessFinger("10002_4", "right");
                        self.removeChessClickEvent("10002_3");
                    } else if (self.step == 4) {//出牌5之后
                        self.removeChessClickEvent("10003_1");
                        let chess: JackaroChess = self.showChessFinger("10002_1", "right");
                        chess && chess.clickArea.on("click", self, self.clearChessEvent, [chess]);
                    }
                    break;
                case TipViewType.SELECT_STEP_NUM:
                    overwrite && self.overwariteSelectStepNumView(view);
                    self.timer.callLater(self, () => {
                        self.novice.showFinger("dclick", view.btn2, 40, "right", true, true);
                    })
                    break;
                case TipViewType.Discard_Self_ForceCard:
                    overwrite && self.overwariteDiscardSelfForceCard(view);
                    break;
            }
            // 显示新弹窗
            this.currentView = view;
            view.show(params);
        }
        if (this._topView && this._topView['_topUI']) {
            (this._topView['_topUI'] as any).visible = false;
        }
        this.topImg.visible = false;
        Object.defineProperties(this._bottomView, {
            isSpectator: { set: function () { } }
        })
        this._addTimeView && this._addTimeView.removeSelf();
        yalla.data.jackaro.JackaroUserService.instance.sendDiscardPoker = function (pokers) {
            JackaroCardManger.instance.handleDiscardPoker(self.myIdx, { poker: pokers[0] }, JackaroCardManger.novalidCardIndex, true);
            JackaroGamePlayerManager.instance.setForcedDiscard(false);
            self.novice3();
            self.clearTips();
        }
        yalla.data.jackaro.JackaroUserService.instance.sendSelectPoker = function (poker: number) {
            // if (!this._user) return;
        }
        let doPlayPoker = JackaroCardManger.instance.doPlayPoker.bind(JackaroCardManger.instance)
        JackaroCardManger.instance.doPlayPoker = function (poker: yalla.data.jackaro.Poker, isRestore: boolean = false) {//滑动出牌拦截
            if (poker == self.currPoker) {
                doPlayPoker(poker, isRestore);
                self.clearTips();
            }
        }
        JackaroCardManger.instance.clearSelectPoker = function (poker: yalla.data.jackaro.Poker = null) {
            if (poker === null) {
                // 如果传入的poker为null，清空所有选中牌
                this.selectCardList = [];
                if (this.curSelectPoker) {
                    //发送给服务器 取消选牌
                }
                this.curSelectPoker = null;
            } else {
                // 否则只移除指定的poker
                const index = this.selectCardList.indexOf(poker);
                if (index > -1) {
                    this.selectCardList.splice(index, 1);
                }
            }
            self.onClearSelectPoker();
        }
    }

    public onResize() {
        super.onResize();
        this.onForce();
        this.initnTopUI();
        this.novice && this.novice.onResize()
        this.resizeFurnitur();
    }

    private clearChessEvent(chess: JackaroChess) {
        chess && chess.clickArea.off("click", this, this.clearChessEvent);
        this.clearTips();
    }
    private removeChessClickEvent(chessid: string) {
        let chess: JackaroChess = JackaroChessManager.instance.getChessById(chessid);
        if (chess) {
            chess["clickChess"] = function () { }
            chess.removeClickEvent();
        }
    }
    private overwariteDiscardSelfForceCard(view: DiscardSelfForceCard) {
        view.show = function (params: any): void {
            if (this.layer && !this.layer.contains(this)) {
                this.layer.addChild(this);
            }

            this.bg_mask.visible = false;
            this.visible = true;

            this.playSelfForcedDiscardAni();
        }
    }
    private overwariteChooseChessOperateView(view: ChooseChessOperateView) {
        view.show = function (params: any): void {
            if (this.layer && !this.layer.contains(this)) {
                this.layer.addChild(this);
            }
            this.visible = true;
            this.checkShowChessMask();
        }
    }
    private overwariteSelectStepNumView(view: SelectStepNumView) {//重写出7后 点击棋子选择步数弹窗
        let self = this;
        view.show = function (params: any): void {
            // JackaroChessManager.instance.hideAllChessChoose();
            JkChooseChessManager.ins.removeAll()
            TipsViewManager.getInstance().showTip(TipViewType.No_Card_Touch);
            if (this.layer && !this.layer.contains(this)) {
                this.layer.addChild(this);
            }
            this.visible = true;
            this.maxStep = params.maxStep;
            this.minStep = params.minStep || 0;
            this.notAllowMoveNumber = params.notAllowMoveNumber || [];
            this._curMoveChessID = params.chessID;
            this.operateTime = params.operateTime ? params.operateTime : 0;
            if (this.maxStep) {
                var isRight = yalla.Font.isRight();
                for (let i = 0; i < this.maxNum; i++) {
                    let btn = this[`btn${i}`] as Laya.Button;
                    if (btn) {
                        var moveStep = i + 1;
                        let isEnable = (moveStep < this.minStep || i > this.maxStep - 1);
                        if (!isEnable && this.notAllowMoveNumber.indexOf(moveStep) > -1) {
                            isEnable = true;
                        }
                        btn.disabled = isEnable;
                        btn.off(Laya.Event.CLICK, this, this.onBtnClick);
                        !isEnable && btn.on(Laya.Event.CLICK, this, this.onBtnClick, [moveStep]);
                    }
                }
            }
        }
        view.onBtnClick = function (moveStep: number, e: Laya.Event) {
            if (e) { e.stopPropagation(); }
            if (moveStep == 3) {
                this.hide();
                let curMoveChessPlayerIdx = JackaroGamePlayerManager.instance.getMoveChessPlayerIdx();
                JackaroChessManager.instance.setPokerSevenFirstMoveData({ idx: curMoveChessPlayerIdx, order: this._curMoveChessID, number: moveStep });
                self.clearTips();
            }
        }
    }
    private overwariteExChangeChessView(view: ExChangeChessView) {
        let self = this;
        view.show = function (params: any): void {
            if (this.layer && !this.layer.contains(this)) {
                this.layer.addChild(this);
            }
            this.visible = true;
            this.checkShowChessMask();
            self.showJackTip(JackaroChessManager.instance.getChessById("10002_4"));//++改写++
        }
        view.hide = function () {
            if (this.progressBarCommonView)
                this.progressBarCommonView.clear();
            this.visible = false;
            if (this.canShowChessMask()) {
                JackaroChessMaskViewManager.instance.hide(this.getChessMaskParent());
            }
            self.novice.hideJackTip();//++改写++
        }
        view.checkShowChessMask = function () {
            if (this.canShowChessMask()) {
                JackaroChessManager.instance.updateChessChoose();
                JackaroChessMaskViewManager.instance.show(this.getChessMaskParent());
            }
            self.onJackChooseOtherChess();//++改写++
        }
    }
    private overwriteSelectStepOperateView(view: SelectStepOperateView) {
        let self = this;
        view.show = function (params: any) {
            let poker = params.poker;
            let curOperateDefine: yalla.view.jackaro.CardOperateItemDefine[] = yalla.view.jackaro.CardOperateDefine.getCardOperateDefine(poker);
            if (curOperateDefine) {
                if (this.layer && !this.layer.contains(this)) {
                    this.layer.addChild(this);
                }
                this.visible = true;
                this.clear();
                let operateCallBack = params.operateCallBack;
                var btns = []
                curOperateDefine.forEach((itemDefine, index: number) => {
                    let curOperatorKey = itemDefine.operatorKey;
                    let canChoose = curOperatorKey ? operateCallBack && operateCallBack[curOperatorKey] : true;
                    var btn: Laya.Button = this.createChooseBtn(itemDefine);
                    btn.offAll();
                    btns.push(btn);
                    let btnContent = btn.getChildByName("btnContent") as Laya.Label;
                    let btnTitle = btn.getChildByName("btnTitle") as Laya.Label;
                    let btnDescText = btn.getChildByName("btnDescText") as Laya.Label;

                    if (canChoose && String(itemDefine.operatorKey) == "0") {
                        if (curOperatorKey) itemDefine.callback = () => {
                            if (itemDefine.operatorKey && parseInt(curOperatorKey) > 0) {
                                yalla.data.jackaro.JackaroUserService.instance.sendSelectItem(poker, this.selectItemParams[parseInt(itemDefine.operatorKey)]);
                            }
                            operateCallBack[curOperatorKey]();
                        }

                        btnTitle.color = "#F6FFB6";
                        btnTitle.strokeColor = "#126640";
                        btnTitle.stroke = 2;

                        btnContent.color = "#ffffff";
                        btnContent.strokeColor = "#0C5F29";
                        btnContent.stroke = 4;
                        btnContent.bold = true;

                        btnDescText.color = "#FFFFFF";

                        btn.skin = itemDefine.btnSkin;
                        self.novice.showFinger("dclick", btn, 90, "right", true, true);//++改写++
                        btn.on("click", this, (e: Laya.Event) => {
                            e.stopPropagation();
                            yalla.Sound.playSound("click");
                            itemDefine.callback();
                            this.hide();
                            self.clearTips();//++改写++
                        })
                    } else {
                        btn.skin = "jackaro/btn_gray.png";

                        btnTitle.color = "#C9C9C9";
                        btnTitle.stroke = 0;
                        btnTitle.strokeColor = "#ffffff";

                        btnContent.color = "#EDEDED";
                        btnContent.stroke = 0;
                        btnContent.bold = false;

                        btnDescText.color = "#ECECEC";

                        btn.on("click", this, (e: Laya.Event) => {
                            e && e.stopPropagation();
                            //主要为了不响应点击播放声音
                        });
                    }
                })
                this.sortPos(btns);
            }
        }
    }
    public init(gameid, faceurl, nickName, royalLevel: string = "0", vipLevel: string = "0") {
        yalla.Global.Account.gameId = gameid;
        yalla.Global.Account.idx = this.myIdx;
        // this.noviceData = Laya.loader.getRes("res/json/jackarooNoviceData.json");
        this.novice = new JackaroNovice();
        this.initnTopUI();
        this.addChild(this.novice);
        this.addEventListener();
        let myInfo = this.noviceData.login.restoreGame.playShowInfo[this.myIdx].playerShowData;
        myInfo.faceUrl = faceurl;
        myInfo.nickName = nickName;
        myInfo.royalLevel = Math.min(Number(royalLevel), 5);
        myInfo.vipLevel = Number(vipLevel);
        this.initData(true);
    }
    private initnTopUI() {
        if (this.novice) this.novice.btn_skip.top = yalla.Screen.hairHeight + 40;
        if (this.novice) this.novice.init();
    }

    private initData(first: boolean = false) {
        yalla.data.jackaro.JackaroUserService.instance['loginResponse'](this.noviceData.login);
        this.hideVoiceBtn();
        this.handlePlayMsg(this.noviceData.login);
        if (first) {
            yalla.Native.instance.removeMatchView();
            yalla.Sound.canPlayMusic && this.timer.once(500, this, () => {//ios设备立即播放不生效，原生那里判断还未进入游戏
                yalla.Global.isFouce && yalla.Sound.playMusic(yalla.Sound.audios.jackaroo_bg, 0);
            })
        }
        this.timer.once(500, this, () => {
            this.novice1();
            // this.novice5();
            // this.testChessTagView();
        })
    }
    // private testChessTagView() {
    //     var chessM = JackaroChessManager.instance;
    //     var arr = []
    //     for (var i = 3; i >= 0; i--) {
    //         var idx = [10001, 10002, 10003, 10004][i]
    //         for (var j = 0; j < 19; j++) {
    //             var chessData = {
    //                 order: j,
    //                 position: j,
    //                 gridColor: i,
    //                 idx,
    //                 index: arr.length
    //             }
    //             arr.push(chessData);
    //             var ch = chessM.createChess(idx, i, chessData);
    //             ch.on(Laya.Event.CLICK, this, (data, ch) => {
    //                 Object.keys(chessM._pool).forEach(key => {
    //                     // console.log(key);
    //                     let chess = chessM.getChessById(key);
    //                     chess.setChessTag("");
    //                 })
    //                 var index = data.index;
    //                 // console.log(data);
    //                 var chArr = []
    //                 for (var k = -2; k <= 2; k++) {
    //                     var index2 = index + k;
    //                     // console.log(index);
    //                     index2 = index2 < 0 ? arr.length + index2 : index2;
    //                     index2 = index2 % (arr.length);
    //                     var d = arr[index2];
    //                     var key = `${d.idx}_${d.order}`;
    //                     // console.log(index2, k, key);
    //                     let chess = chessM.getChessById(key);
    //                     chArr.push(chess)
    //                 }
    //                 // this.noviceData.changePlayerResponse4.allowPokerData["5"].commonResult.playResultData["5"].result = result;
    //                 // this.changePlayerResponse(this.noviceData.changePlayerResponse4);
    //                 // console.log(chArr);
    //                 JackaroChessManager.instance.checkShowChooseChessTagView(ch, chArr);
    //             }, [chessData, ch])

    //         }
    //     }


    // }
    public onForce() {
        super.onForce()
        yalla.Global.isFouce = true;
        yalla.Sound.canPlayMusic && yalla.Sound.playMusic(yalla.Sound.audios.jackaroo_bg, 1);
    }
    private hideVoiceBtn() {
        JackaroGamePlayerManager.instance.forEach((p: JackaroGamePlayer) => {
            if (p) {
                if (p.UI) p.UI.voice_ani.visible = false;
                p.head.removeEvent();
                p['waitCD'] = null;
            }
        })
    }
    private playThrowJump(isPlay: boolean) {
        let p: JackaroGamePlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(this.myIdx);
        if (p) {
            if (!isPlay) p.UI.throw_start.removeSelf();
            else p.UI.addChild(p.UI.throw_start);
            p.playThrowJump(isPlay);
        }
    }

    private changePlayerResponse(msg) {
        yalla.data.jackaro.JackaroUserService.instance['changePlayerResponse'](msg);
    }

    private createMoveChessPubResponse(idx: number, order: number, color: number, currentPoker: number, fgrid: any, tgrid: any) {
        this.setCurOperatePlayerIdx(idx);
        JackaroChessManager.instance.handleMoveChessPub({
            moveResultData: {
                fromGrid: { grid: fgrid, chess: {} },
                toGrid: { grid: tgrid, chess: { idx, order, color } }
            },
            currentPoker, moveChess: true, remainPokerCount: 4, moveChessPlayer: idx
        })
    }
    private setCardoperatorState(card: number, state: number = -1) {//禁止出牌
        let cardItem: yalla.view.jackaro.JackaroCardItem = JackaroCardManger.instance.getPlayerHandPoker(this.myIdx, card);
        if (cardItem) cardItem['_operatorState'] = -1;
    }
    private playRoleAniByIdx(idx: number, cb: Function, time = 1000) {
        var p = JackaroGamePlayerManager.instance.getGamePlayerByIdx(idx);
        p && p.playRoleAni();
        cb && this.timer.once(time, this, cb);
    }
    private setCurOperatePlayerIdx(idx: number) {
        JackaroGamePlayerManager.instance.curOperatePlayerIdx = idx;
    }
    private novice1() {//第一步 出A
        if (this.step == 1) return;
        this.step = 1;
        this.aniTime = true;
        this.hideVoiceBtn();
        this.handlePlayMsg(this.noviceData.dealPokerPub);//发牌
        this.timer.once(3000, this, () => {
            this.changePlayerResponse(this.noviceData.changePlayerResponse);
            this.playThrowJump(true);
            this.showBubble("1", "a", "left", 400);
            this.aniTime = false;
            this.currPoker = 14;
            this.showCardFinger();
        })
    }

    private showChessFinger(chessId: string, side: string) {
        let chess: JackaroChess = JackaroChessManager.instance.getChessById(chessId);
        chess && this.novice.showFinger("dclick", chess, 20, side, false, false);
        return chess;
    }
    private startX = 0;
    private startY = 0;
    private showCardFinger(onTouchEnd: boolean = false, isChild: boolean = false) {
        this.currCard && this.currCard.off("downCardFinish", this, this.downCardFinish);
        this.currCard = JackaroCardManger.instance.getPlayerHandPoker(this.myIdx, this.currPoker);
        if (this.currCard) {
            this.startX = this.currCard.x;
            this.startY = this.currCard.y;
            this.novice.showFinger(this.step == 1 ? "silder" : "dclick", this.currCard, 0, "right", true, isChild);
            this.currCard.on("downCardFinish", this, this.downCardFinish);
            if (!onTouchEnd) {
                this.currCard.on(Laya.Event.MOUSE_MOVE, this, this.onMouseMove);
            }
        }
    }
    private downCardFinish(card) {
        if (card === this.currCard)
            this.novice.refreshPos();
    }
    private onClearSelectPoker() {
        if (this.currCard) {
            this.startX = this.currCard.x;
            this.startY = this.currCard.y;
            if (this.currCard.isPlayOut) {
                this.currCard.off(Laya.Event.MOUSE_MOVE, this, this.onMouseMove);
            } else {
                this.novice.showFinger(this.step == 1 ? "silder" : "dclick", this.currCard, 0, "right", true, false);
            }
        }
    }

    private onMouseMove() {
        if (this.currCard) {
            var { x, y } = this.currCard;
            // console.log("onMouseMove", Math.abs(this.startX - x), Math.abs(this.startY - y));
            if (Math.abs(this.startX - x) > 30 || Math.abs(this.startY - y) > 30)
                this.novice.hideFinger();
        }
    }
    private novice2() {//第二步 对手出10之后弃牌2
        if (this.step == 2) return;
        yalla.util.clogDBAmsg("10380");
        this.step = 2;
        this.aniTime = true;
        this.playThrowJump(false);
        this.timer.once(500, this, () => {
            this.playRoleAniByIdx(10003, () => {//10003出牌
                this.createMoveChessPubResponse(10003, 1, 2, 13, { index: 301, color: 2 }, { index: 2, color: 2, type: 2 });
                this.timer.once(1500, this, () => {
                    this.playRoleAniByIdx(10004, () => {//10004出牌
                        JackaroCardManger.instance.handleDiscardPoker(10004, { poker: 9 });
                        JackaroCardManger.instance.handleDiscardPoker(10004, { poker: 22 });
                        JackaroCardManger.instance.handleDiscardPoker(10004, { poker: 38 });
                        JackaroCardManger.instance.handleDiscardPoker(10004, { poker: 42 }, JackaroCardManger.novalidCardIndex, true);
                        this.timer.once(500, this, () => {
                            this.playRoleAniByIdx(10001, () => {//10001出牌
                                yalla.data.jackaro.JackaroUserService.instance["forcedDiscardPubResponse"](this.noviceData.forcedDiscardPubResponse);
                                this.timer.once(500, this, () => {
                                    this.changePlayerResponse(this.noviceData.changePlayerResponse2);
                                    this.setCardoperatorState(5);//禁止出5和4
                                    this.setCardoperatorState(17);
                                    this.showBubble("2", "10", "right", 200);
                                    this.currPoker = 28;
                                    this.playThrowJump(true);
                                    this.showCardFinger();
                                    this.aniTime = false;
                                })
                            });
                        })
                    });
                });
            });
        })
    }

    private novice3() {//出4后退4步
        if (this.step == 3) return;
        yalla.util.clogDBAmsg("10382");
        this.step = 3;
        this.aniTime = true;
        this.playThrowJump(false);
        this.showNextTurn(() => {
            this.flushPlayerPokerLen(1);
            this.changePlayerResponse(this.noviceData.changePlayerResponse3);
            this.showBubble("3", "4", "left");
            this.currPoker = 17;
            this.showCardFinger();
            this.aniTime = false;
        }, 1000)
    }
    private novice4() {//向前移动5步到终点区域
        if (this.step == 4) return;
        yalla.util.clogDBAmsg("10384");
        this.step = 4;
        this.aniTime = true;
        this.timer.once(1000, this, () => {
            this.playRoleAniByIdx(10003, () => {
                this.createMoveChessPubResponse(10003, 1, 3, 3, { index: 2, color: 2 }, { index: 5, color: 2, type: 1 });
                this.timer.once(2000, this, () => {
                    this.playRoleAniByIdx(10001, () => {
                        JackaroCardManger.instance.handleDiscardPoker(10001, { poker: 25 });
                        JackaroCardManger.instance.handleDiscardPoker(10001, { poker: 47 }, JackaroCardManger.novalidCardIndex, true);
                        this.timer.once(500, this, () => {
                            this.changePlayerResponse(this.noviceData.changePlayerResponse4);
                            this.showBubble("4", "5", "right");
                            this.currPoker = 5;
                            this.playThrowJump(true);
                            this.showCardFinger();
                            this.aniTime = false;
                        });
                    });
                });
            });
        });
    }
    private novice5() {//重新发牌后出K移动一颗新的棋子到起始点
        if (this.step == 5) return;
        yalla.util.clogDBAmsg("10386");
        this.step = 5;
        this.aniTime = true;
        this.playThrowJump(false);
        this.showNextTurn(() => {
            this.flushPlayerPokerLen(1);
            this.handleSyncGame(this.noviceData.gameData5);
            this.handlePlayMsg(this.noviceData.dealPokerPub5);
            this.timer.once(3500, this, () => {
                this.changePlayerResponse(this.noviceData.changePlayerResponse5);
                this.showBubble("5", "k", "left", 400);
                this.currPoker = 26;
                this.showCardFinger(false, true);
                this.aniTime = false;
            })
        }, 1000);
    }

    private novice6() {//引导出J交换棋子
        if (this.step == 6) return;
        yalla.util.clogDBAmsg("10388");
        this.playThrowJump(false);
        this.step = 6;
        this.aniTime = true;
        this.showNextTurn(() => {
            this.flushPlayerPokerLen(1);
            this.changePlayerResponse(this.noviceData.changePlayerResponse6);
            this.currPoker = 24;
            this.showCardFinger();
            this.showBubble("6", "j", "right");
            this.aniTime = false;
        }, 1000);
    }

    private novice7() {//出7后4和3选择进入终点区域
        if (this.step == 7) return;
        yalla.util.clogDBAmsg("10390");
        this.step = 7;
        this.aniTime = true;
        this.showNextTurn(() => {
            this.flushPlayerPokerLen(1);
            this.changePlayerResponse(this.noviceData.changePlayerResponse7);
            this.currPoker = 7;
            this.showCardFinger();
            this.showBubble("7", "7", "left");
            this.aniTime = false;
        }, 1000);
    }
    private novice8() {
        if (this.step == 8) return;
        yalla.util.clogDBAmsg("10392");
        this.step = 8;
        this.aniTime = true;
        this.showNextTurn(() => {
            this.flushPlayerPokerLen(1);
            this.changePlayerResponse(this.noviceData.changePlayerResponse8);
            this.currPoker = 34;
            this.showCardFinger();
            this.showBubble("8", "8", "left");
            this.aniTime = false;
        }, 1000);
    }
    private noviceEnd() {//新手引导结束
        this.step = 0;
        yalla.util.clogDBAmsg("10396");
        this.novice.hideFinger();
        this.timer.once(1000, this, () => {
            this.novice.noviceEnd();
            this.aniTime = false;
        })
    }

    private onSkip() {
        switch (this.step) {
            case 1://跳过 A
                yalla.util.clogDBAmsg("10381");
                break;
            case 2://跳过 2
                yalla.util.clogDBAmsg("10383");
                break;
            case 3://跳过 4
                yalla.util.clogDBAmsg("10385");
                break;
            case 4://跳过 5
                yalla.util.clogDBAmsg("10387");
                break;
            case 5://跳过 k
                yalla.util.clogDBAmsg("10389");
                break;
            case 6://跳过 j
                yalla.util.clogDBAmsg("10391");
                break;
            case 7://跳过 7
                yalla.util.clogDBAmsg("10393");
                break;
            case 8://跳过 8
                yalla.util.clogDBAmsg("10397");
                break;
            case 0:
                yalla.util.clogDBAmsg("10395");
                break;
        }
    }


    protected onMoveEnd(chess: JackaroChess) {
        super.onMoveEnd(chess);
        if (!this.aniTime) {
            switch (this.step) {
                case 1:
                    this.novice2();
                    break;
                case 3:
                    this.novice4();
                    break;
                case 4:
                    this.novice5();
                    break;
                case 5:
                    this.novice6();
                    break;
                case 6:
                    this.novice7();
                    break;
                case 7:
                    this.novice8();
                    break;
                case 8:
                    this.noviceEnd();
                    break;
            }
        }
    }
    private handleExchangeChessPub() {
        JackaroChessManager.instance.handleExchangeChessPub(this.noviceData.exchangeChessPub);
    }
    private flushPlayerPokerLen(num: number) {//刷新玩家手牌数量显示
        JackaroGamePlayerManager.instance.playerIdxList.forEach(idx => {
            idx != this.myIdx && JackaroCardManger.instance.dynamicDiscardPoker(idx, num);
        })
    }
    private clearTips() {
        this.timer.clear(this.novice, this.novice.showBubble);
        this.novice.hideFinger();
        this.novice.hideBubble();
    }
    private showNextTurn(callback: Function, delay: number = null) {
        if (delay) this.timer.once(delay, this.novice, this.novice.showNextTurn, [callback]);
        else this.novice.showNextTurn(callback);
    }
    private showJackTip(chess: JackaroChess) {
        if (chess) {
            this.novice.showJackTip(chess);
            this.novice.showFinger("dclick", chess, 20, "left", false, false);
        }
    }
    private onJackChooseOtherChess() {//出牌J之后 选择第二个交换的棋子
        let chess: JackaroChess = JackaroChessManager.instance.getChessById("10001_1");
        chess && this.novice.showFinger("dclick", chess, 20, "right", false, false);
    }
    private showBubble(tipIndex: string, card: string, side: string, delay: number = 0, centerY: number = -320) {
        if (delay) this.timer.once(delay, this.novice, this.novice.showBubble, [tipIndex, card, side, centerY]);
        else this.novice.showBubble(tipIndex, card, side, centerY);
    }
    private addEventListener() {
        if (this.novice) {
            if (this.novice.btn_skip) this.novice.btn_skip.on(Laya.Event.CLICK, this, this.skipNovice, [true]);
            if (this.novice.tip) this.novice.tip.btn_watchagain.on(Laya.Event.CLICK, this, this.watchAgain);
        }
    }
    private removeEventListener() {
        if (this.novice) {
            if (this.novice.btn_skip) this.novice.btn_skip.off(Laya.Event.CLICK, this, this.skipNovice);
            if (this.novice.tip) this.novice.tip.btn_watchagain.off(Laya.Event.CLICK, this, this.watchAgain);
        }
    }
    public onBackPressed() {
        yalla.common.Confirm.instance.isExit ? yalla.common.Confirm.instance.hideConfirm() : this.skipNovice();
    }
    private watchAgain() {
        this.currCard = null;
        this.novice.noviceAgain();
        yalla.Native.instance.noviceComplete(true, 100);
        this.initData();
    }
    private skipNovice() {//跳过引导
        yalla.common.Confirm.instance.isExit = true;
        yalla.common.Confirm.instance.showConfirm("Are you sure to skip tutorials?", Laya.Handler.create(this, () => {
            this.onSkip();
            this.clearNoviceTimer();
            yalla.data.jackaro.JackaroUserService.instance.backHall(true);
            this.backHallClear();
        }), Laya.Handler.create(this, () => {
            yalla.common.Confirm.instance.hideConfirm();
        }), ["Confirm", "Cancel"])
    }
    private clearNoviceTimer() {
        this.timer.clearAll(this);
        this.timer.clearAll(this.novice);
        this.novice && this.novice.clear();
    }
    public backHallClear() {
        this.currCard && this.currCard.off("downCardFinish", this, this.downCardFinish);
        this.removeEventListener();
        this.clearNoviceTimer()
        super.backHallClear();
        // Laya.loader.clearResByGroup("jackaroo_novice_res");
    }
}    
