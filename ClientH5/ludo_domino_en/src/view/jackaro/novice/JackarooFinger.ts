class JackarooFinger extends ui.jackaro.novice.fingerUI {
    private tpl: Laya.Templet = null;
    private sk: Laya.Skeleton = null;
    constructor() {
        super();
        this.init();
    }
    init() {
        this.tpl = new Laya.Templet();
        this.tpl.on(Laya.Event.COMPLETE, this, this.onSkLoad)
        this.tpl.loadAni("res/sk/novice/xinshou.sk")
    }
    play(aniName: string, showRipple: boolean) {
        this.finger.play(0, true, aniName);
        if (aniName == "dclick" && showRipple) {
            this.playSK();
        } else {
            this.stopSK();
        }
    }
    stop() {
        this.finger.stop();
        this.stopSK();
    }
    private playSK() {
        if (this.sk) {
            this.sk.visible = true;
            this.sk.play(0, true);
        }
    }
    private stopSK() {
        if (this.sk) {
            this.sk.stop();
            this.sk.visible = false;
        }
    }
    private onSkLoad() {
        this.sk = this.tpl.buildArmature(1);
        this.addChildAt(this.sk, 0);
        this.sk.pos(98, 78);
    }
}