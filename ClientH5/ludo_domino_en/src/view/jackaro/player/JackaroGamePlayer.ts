class JackaroGamePlayer extends Laya.Box {

    public head: yalla.jackaro.Head;
    private waitCD: Ludo.CdAni;
    public position: Object;
    public select: boolean = false;
    public isPass: boolean = false;
    public idx: number = null;
    private chatBubble: yalla.common.Bubble;
    private _nameItem: yalla.jackaro.NameItem;
    protected _side: number = 0;
    private _isLeft: boolean = false;
    public isWin: boolean = false;
    private isPlay: boolean = false;
    public isMute: boolean = false;
    private _isDiscard: boolean = false;
    private _tp: Laya.Templet = null;
    // private ripple: Laya.Skeleton = null;
    private timeLine: Laya.TimeLine;
    public offLine: boolean;
    public UI: any;
    private vipSk: Laya.Skeleton = null;
    private rlSk: Laya.Skeleton = null;
    private _vipKey: string = "";
    private _rlKey: string = "";
    private isMe: boolean = false;
    private timeValue: number = -1;
    private _playerStatus: yalla.data.jackaro.PlayerStatus = yalla.data.jackaro.PlayerStatus.NONE_PLAYER_STATUS;
    private handCardLayerPlaceHolder: Laya.Box;
    private _remainThinkingTime: number = 0;
    private _totalThinkingTime: number = 0;//这个时间只有断线重连回来会被设置，用于断线回来玩家总时长，其它时间默认重置为0
    private _trustSystem: boolean = false;
    private _discardTimeLine: Laya.TimeLine;
    private primaryColor: number;

    private waitCdColor = "#FDFF2F"; //倒计时颜色
    /**
     * 操作时间 20秒
     */
    // public static operateTime: number = 20000;
    // public static addOperateTime: number = 10000;//加时时间

    private logTag: string = "JackaroGamePlayer : ";
    /**
     * 操作倒计时 3秒
     */
    public static operateCountdownCDTime: number = 3000;

    private posList = [{ "left": 4, "bottom": -37 }, { "right": 4, "bottom": -37 }, { "right": 4, "top": -10 }, { "left": 4, "top": -10 }];
    constructor(primaryColor: number, color: number, offLine: boolean = false, playerInfo: yalla.data.jackaro.PlayerShowInfoInterface, againstType: yalla.data.jackaro.GameAgainstType) {
        super();
        this.idx = playerInfo.idx;
        this.isMe = this.idx == yalla.Global.Account.idx;
        this.timeValue = this.isMe ? 5000 : -1;
        this.offLine = offLine;
        this.updatePlayerColor(primaryColor, color, offLine, playerInfo, againstType);
    }
    public updatePlayerColor(primaryColor: number, color: number, offLine: boolean = false, playerInfo: yalla.data.jackaro.PlayerShowInfoInterface, againstType: yalla.data.jackaro.GameAgainstType) {
        if (this.primaryColor != primaryColor || this.color != color) {
            if (this.head) {
                this.head.removeSelf();
                this.head.destroy();
                this.head = null;
            }
            if (this.waitCD) {
                this.waitCD.clear();
                this.waitCD.removeSelf();
                this.waitCD = null;
            }
            if (this.UI) {
                this.UI.removeSelf();
                this.UI.destroy(true);
                this.UI = null;
            }
        }
        if (!this.head) {
            this.head = new yalla.jackaro.Head();
        }
        this.head.scale(1, 1);
        //对应 左下 右下 右上 ↖左上
        // var posList = [{ "left": 4, "bottom": 60 + 178 }, { "right": 4, "bottom": 60 + 178 }, { "right": 4, "top": -75 }, { "left": 4, "top": -75 }];
        if (againstType == yalla.data.jackaro.GameAgainstType.ONE_VS_ONE) {
            this.side = primaryColor == color ? 0 : 2;
            this.position = this.posList[this.side];
        } else {
            for (let i = 0; i < 4; i++) {
                let index: number = (primaryColor + i) % 4;
                if (color == index) {
                    this.side = i;
                    this.position = this.posList[i];
                    break;
                }
            }
        }
        this.color = color;

        this.UI.head_box.addChild(this.head);
        this.head.pos(this.UI.head_box.width / 2, this.UI.head_box.height / 2);
        var posxy = this.head.face.width / 2 + 4;

        this.waitCD = new Ludo.CdAni(posxy, yalla.data.jackaro.JackaroUserService.instance.defaultOperateTime, this.waitCdColor);
        this.head.addChildAt(this.waitCD, 1);
        this.waitCD.pivot(posxy, posxy);
        this.waitCD.pos(56, 56);
        this.primaryColor = primaryColor;

        this.UI.flag.removeSelf();
        for (var key in this.position) {
            this[key] = this.position[key];
        }

        if (!offLine) {
            this.initBubble(playerInfo.talkSkinId);
        }
        if (this.isMe && yalla.Global.Account.isLimitVisitor) {
            this.UI.voice_btn.visible = false;
        } else {
            this.initEvent();
        }
    }

    set side(side: number) {
        this._side = side;
        switch (side) {
            case 0:
                if (!this.UI) {
                    this.UI = new ui.jackaro.sub.jackaro_player_0UI();
                }
                if (yalla.Global.Account.idx == this.idx) {
                    (this.UI.voice_ani as Laya.Animation).source = "publics/ani/ani_voice_me.ani";
                    (this.UI.voice_btn as Laya.Button).mouseEnabled = false;
                    this.UI.voice_ani.play(0, false, "stop");
                } else {
                    this.UI.voice_ani.play(0, false, "pause");
                }
                this.head && this.head.btn_gift.pos(60, 0);
                break;
            case 1:
                if (!this.UI) {
                    this.UI = new ui.jackaro.sub.jackaro_player_3UI();
                }
                this.UI.voice_ani.play(0, false, "pause");
                this.head && this.head.btn_gift.pos(0, 0);
                this.head.team_icon.pos(4, 60);
                break;
            case 2:
                if (!this.UI) {
                    this.UI = new ui.jackaro.sub.jackaro_player_2UI();
                }
                this.UI.voice_ani.play(0, false, "pause");
                if (this.head) {
                    this.head.team_icon.pos(4, 60);
                    this.head.btn_gift.pos(0, 0);
                }
                break;
            case 3:
                if (!this.UI) {
                    this.UI = new ui.jackaro.sub.jackaro_player_1UI();
                }
                this.UI.voice_ani.play(0, false, "pause");
                if (this.head) {
                    this.head.btn_gift.pos(60, 0);
                }
                break;
            default:
                break;
        }
        this.head.side = side;
        this.addChild(this.UI);
    }
    public getSide(): number {
        return this._side;
    }
    set remainThinkingTime(time: number) {
        this._remainThinkingTime = time;
    }
    set totalThinkingTime(time: number) {
        this._totalThinkingTime = time;
    }

    get totalThinkingTime(): number {
        return this._totalThinkingTime;
    }

    /**获取剩余操作时间 */
    get remainThinkingTime(): number {
        if (this.waitCD && this.waitCD.operateTime > 1) return this.waitCD.operateTime;
        return this._remainThinkingTime;
    }
    get side(): number {
        return this._side;
    }

    set playerStatus(status: yalla.data.jackaro.PlayerStatus) {
        this._playerStatus = status;
    }
    get playerStatus(): yalla.data.jackaro.PlayerStatus {
        return this._playerStatus;
    }

    get handCard_box(): Laya.Box {
        return this.handCardLayerPlaceHolder ? this.handCardLayerPlaceHolder : this.UI.handCard_box;
    }
    setHandCardLayer(handCardLayerPlaceHolder: Laya.Box) {
        this.handCardLayerPlaceHolder = handCardLayerPlaceHolder;
    }
    public playVoiceAni(start: number, loop: boolean, name: string) {
        this.UI && (this.UI.voice_ani as Laya.Animation).play(start, loop, name);
    }
    public waitCdStart(time: number, timeValue: number = this.timeValue): JackaroGamePlayer {
        this.totalThinkingTime = 0;
        this.remainThinkingTime = 0;
        this.waitCD && this.waitCD.start(time, this.isMe ? timeValue : -1);
        return this;
    }
    public getWaitCdEndTime(): number {
        return this.waitCD && this.waitCD.getEndTime();
    }
    public addWaitCdTime(extendTime?: number): JackaroGamePlayer {
        let addTime: number = extendTime ? extendTime : yalla.data.jackaro.JackaroUserService.instance.addOperateTime;
        this.waitCD && this.waitCD.addTime(addTime);
        return this;
    }
    public resetWaitCdTime(remainTime: number, totalTime: number): JackaroGamePlayer {
        this.waitCD && this.waitCD.resetTime(remainTime, totalTime, this.isMe ? JackaroGamePlayer.operateCountdownCDTime : -1);
        return this;
    }
    public waitCdEnd(): JackaroGamePlayer {
        this.waitCD && this.waitCD.end();
        return this;
    }
    public playThrowJump(isShowThrowJump: boolean) {
        if (this.idx == yalla.Global.Account.idx) {
            if (this.UI && this.UI.throw_start) {
                if (isShowThrowJump) {
                    this.UI.throw_start.visible = true;
                    (this.UI.throw_start as Laya.Animation).play(0, true);
                } else {
                    this.UI.throw_start.visible = false;
                    this.UI.throw_start.gotoAndStop(0);
                }
            }
        }
    }

    public frameLoopName(): void {
        if (this._nameItem) {
            this._nameItem.frameLoopName();
        }
    }
    public initEvent() {
        this.UI.voice_btn.offAll();
        this.UI.voice_btn.on(Laya.Event.CLICK, this, (e) => {
            e.stopPropagation();
            if (this.idx == yalla.Global.Account.idx) return;//自己
            if (yalla.Mute.muted(this.idx)) return;//已举报的玩家
            if (yalla.Voice.instance.muted(this.idx)) {
                yalla.Voice.instance.muteRemoteAudioStream(this.idx, false, (val, uid) => {//取消屏蔽
                    if (val == 0) {
                        this.isMute = false;
                        this.pause();
                        this.mobClick_quietMic(1);
                    }
                })
            } else {
                yalla.Voice.instance.muteRemoteAudioStream(this.idx, true, (val, uid) => {//屏蔽
                    if (val == 0) {
                        this.timer.clear(this, this.pause);
                        this.isMute = true;
                        this.UI.voice_ani.play(0, false, "stop");
                        this.mobClick_quietMic(0);
                    }
                })
            }
        })
        this.on(Laya.Event.REMOVED, this, () => {
            this.UI.voice_btn.offAll();
        })
    }
    initBubble(talkSkinId) {
        if (this.UI) {
            let side = this.side == 1 ? 3 : this.side == 3 ? 1 : this.side;
            if (this.chatBubble) {
                this.chatBubble.removeSelf();
                this.chatBubble = null;
            }
            this.chatBubble = new yalla.common.Bubble(side);
            this.updateBublele(talkSkinId);
            var offSetY = this.side <= 1 ? -62 : 62;
            this.chatBubble.pos(this.UI.head_box.x, this.UI.head_box.y + offSetY);
            if (this.side == 0) {
                (this.UI as Laya.View).addChildAt(this.chatBubble, 0);
            } else {
                this.addChild(this.chatBubble);
            }
        }

    }
    updateBublele(talkSkinId: number) {
        if (this.chatBubble && talkSkinId) {
            this.chatBubble.skinId = talkSkinId;
        }
    }

    mobClick_quietMic(state) {

        if (state == 1) {
            if (!ludo.Global.inSit()) {
                yalla.Native.instance.mobClickEvent(buryPoint.GAME_SPECTATER_OPENEMIC);//埋点 观战点击取消静音
            } else {
                yalla.Native.instance.mobClickEvent(buryPoint.GAME_USER_OPENMIC);//埋点 玩家点击取消静音
            }
        } else {
            if (!ludo.Global.inSit()) {
                yalla.Native.instance.mobClickEvent(buryPoint.GAME_SPECTATER_QUIEMIC);//埋点 观战点击开启静音
            } else {
                yalla.Native.instance.mobClickEvent(buryPoint.GAME_USER_QUIETMIC);//埋点 玩家点击开启静音
            }
        }

    }
    clear() {
        if (this.timeLine) {
            this.timeLine.pause();
            this.timeLine.offAll();
            this.timeLine.destroy();
            this.timeLine = null;
        }
        if (this.head) {
            this.head.clear();
            this.head.destroy();
        }
        this.timer.clear(this, this.pause);
        if (this.waitCD)
            this.waitCD.destroy();
        if (this.vipSk) {
            var __s = yalla.SkeletonManager.instance;
            __s.off(this._vipKey, this, this.loadVipSk);
            __s.clearByName(this._vipKey);
            this._vipKey = '';
        }
        if (this.rlSk) {
            var __s = yalla.SkeletonManager.instance;
            __s.off(this._rlKey, this, this.loadRLSk);
            __s.clearByName(this._rlKey);
            this._rlKey = '';
        }
        if (this._nameItem && this._nameItem.ui) {
            this._nameItem.ui.removeSelf();
        }
        if (this.UI) {
            (this.UI.voice_ani as Laya.Animation).stop();
            this.UI.destroy(true);
        }
        this.destroy(true);
    }
    playRoleAni() {
        this.head && this.head.playRoleAni();
        // 震动
        // yalla.Native.instance.phoneVibrate();
        //需要播放弃牌10 音效时，自己的回合音效不播放
        let isSelfForceDiscard = JackaroCardManger.instance.isSelfNeedShowSelfForceCard();
        if (this._isDiscard || isSelfForceDiscard) return;
        yalla.Sound.playSound(this.isMe ? "my_turn" : "opp_turn");
    }
    resetHeadScale() {
        this.head && this.head.resetScale();
    }
    // playRipple() {
    //     if (yalla.Global.isFouce && this.ripple && this.ripple.templet)
    //         this.ripple.play(0, false);
    // }
    showName() {
        if (this.UI) {
            if (this.UI.buff_box.numChildren > 0) return;
            this.UI.name_box.visible = true;
        }
    }
    hideName() {
        if (this.UI && !this.isWin && !this.isLeft) {
            this.UI.name_box.visible = false;
        }
    }

    showEXP(num: number) {
        var expLable: ExpLable = LudoBuffManager.instance.getExpLable();
        if (expLable && this.UI) {
            expLable.text = "+" + num + " EXP";
            expLable.color = JackaroGamePlayerManager.getPcolor(this.color);
            expLable.fromTo({ x: 0, y: 50 }, this.UI.head_box);
        }
        yalla.Sound.playSound("exp_add");
    }
    setTrustSystem(val: boolean) {
        this._trustSystem = val;
        if (val) {
            TipsViewManager.getInstance().hideTip(TipViewType.SELECT_STEP_NUM);
            TipsViewManager.getInstance().hideTip(TipViewType.EXCHANGE_CHESS);
            TipsViewManager.getInstance().hideTip(TipViewType.PICKUP_CARD);
            TipsViewManager.getInstance().hideTip(TipViewType.DISCARD_ALL_CARD);
            TipsViewManager.getInstance().hideTip(TipViewType.DISCARD_CARD);

            let room = yalla.data.jackaro.JackaroUserService.instance.room;
            TipsViewManager.getInstance().showTip(TipViewType.TRUST_SYSTEM);
        } else {
            TipsViewManager.getInstance().hideTip(TipViewType.TRUST_SYSTEM);
        }
    }
    getTrustSystem() {
        return this._trustSystem;
    }
    pause() {
        if (this.idx == yalla.Global.Account.idx && !yalla.Voice.instance.voiceOpen) {
            this.UI && this.UI.voice_ani.play(0, false, "stop");
            return;
        }
        if (!this.isMute) {
            this.UI.voice_ani.play(0, false, "pause");
            this.isPlay = false;
        }
    }
    stop() {
        if (this.idx == yalla.Global.Account.idx && !yalla.Voice.instance.voiceOpen) return;
        this.UI && (this.UI.voice_ani as Laya.Animation).gotoAndStop(0);
    }
    play() {
        if (!this.isMute) {
            this.timer.clear(this, this.pause);
            if (!this.isPlay) {
                this.UI.voice_ani.play(0, true, "play");
            }
            this.isPlay = true;
            this.timer.once(500, this, this.pause);
        }
    }
    mute() {
        if (this.UI) {
            this.UI.voice_ani.play(0, false, "mute");
            this.UI.voice_btn.offAll();
        }
    }

    showChat(msg: any) {
        if (!this.chatBubble) {
            this.initBubble(this.data.talkSkinId || 11000);
        }
        this.chatBubble.onChat(msg);
        this.chatBubble.show();
    }
    private loadRLSk(sk: Laya.Skeleton) {
        if (!this.rlSk && !this.destroyed) {
            this.rlSk = sk;
            this.UI.RL.addChild(this.rlSk);
            this.rlSk.pos(20, 14);
            this.rlSk.scale(0.6, 0.6);
            //目前r+5等级标识和rl5一样
            if (this.dataSource) {
                let rlAnimationLv: number = this.dataSource.royalLevel || 0;
                rlAnimationLv = Math.min(5, rlAnimationLv);
                rlAnimationLv && this.rlSk.play(`R${rlAnimationLv}`, true);
            }
            yalla.SkeletonManager.instance.off(this._rlKey, this, this.loadRLSk);
        }
    }
    private loadVipSk(sk: Laya.Skeleton) {
        if (!this.vipSk && !this.destroyed) {
            this.vipSk = sk;
            this.UI.vip.addChild(this.vipSk);
            this.vipSk.pos(15, 14);
            this.vipSk.scale(0.6, 0.6);
            if (this.dataSource) this.vipSk.play("vip_" + this.dataSource.vipLevel, true);
            yalla.SkeletonManager.instance.off(this._vipKey, this, this.loadVipSk);
        }
    }

    set isLeft(boo: boolean) {
        if (yalla.Global.Account.isPrivate != 5) {
            this._isLeft = boo;
            this.head.isLeft = boo;
            if (boo) {
                this.showName();
                this.UI.dice_bg.alpha = 0;
            }
        }
    }
    get isLeft(): boolean {
        return this._isLeft;
    }
    set isDiscard(boo: boolean) {
        this._isDiscard = boo;
        this.UI.icon_discard.visible = boo;
        if (boo) {
            this.playDiscardEffect();
        }
    }
    private playDiscardEffect() {
        if (yalla.Global.isFouce) {
            if (!this._discardTimeLine) {
                this._discardTimeLine = new Laya.TimeLine();
            } else {
                this._discardTimeLine.reset();
            }
            this._discardTimeLine.addLabel("0", 0).to(this.UI.icon_discard, {
                alpha: 0, scaleX: 1.5, scaleY: 1.5
            }, 0);
            this._discardTimeLine.addLabel("1", 0).to(this.UI.icon_discard, { alpha: 1, scaleX: 1, scaleY: 1 }, 200, null);
            this._discardTimeLine.play("0", false);

            if (yalla.Font.isRight()) {
                yalla.Sound.playSound('Dicard_Card_10_ar');
            } else {
                yalla.Sound.playSound('Dicard_Card_10');
            }
        }
    }

    get isDiscard(): boolean {
        return this._isDiscard;
    }
    set isInChatOff(val: boolean) {
        if (!this._isLeft) this.head.isInChatOff = val;
    }

    setPlayerInfo(val: yalla.data.jackaro.PlayerShowInfoInterface, activated_id: number = null) {
        this.dataSource = val;
        this.head.showFaceBox(val);
        let jackaroPlayerShowGameData: yalla.data.jackaro.PlayerShowGameDataInterface = JackaroGamePlayerManager.instance.getPlayerShowGameDataByIdx(val.idx);
        // this.gift = val.giftId;
        if (val.royalLevel >= 4) {
            this.UI.nickName.text = '';
            if (!this._nameItem) {
                var nameItemUI: ui.publics.item.nameItemUI = new ui.publics.item.nameItemUI();
                this.UI.nickName.parent.addChild(nameItemUI);
                nameItemUI.height = 24;
                nameItemUI.y = 2;
                this._nameItem = new yalla.jackaro.NameItem(nameItemUI, this.UI.nickName);
            }
            this._nameItem.gameName(val, 13);
        } else {
            if (this._nameItem && this._nameItem.ui) this._nameItem.ui.removeSelf();
            yalla.Emoji.lateLable(yalla.util.filterName(val.nickName), this.UI.nickName);
        }

        this.name = val.idx.toString();
        this.isLeft = Boolean(jackaroPlayerShowGameData.left);
        this.idx = val.idx;
        if (val.vipLevel > 0) {
            if (!this.vipSk && (!this._vipKey || this._vipKey.length < 1)) {
                this._vipKey = "vip" + String(this.idx);
                this.UI.vip.visible = true;
                var __s = yalla.SkeletonManager.instance;
                __s.on(this._vipKey, this, this.loadVipSk);
                __s.initSk("vip/xunzhang", this._vipKey);
            }
        } else {
            this.UI.vip.removeSelf();
        }
        var royalLevel = yalla.util.getJackaroRoyalLevel(val);
        if (royalLevel > 0) {
            if (!this.rlSk && (!this._rlKey || this._rlKey.length < 1)) {
                this._rlKey = "rl" + String(this.idx);
                this.UI.RL.visible = true;
                var __s = yalla.SkeletonManager.instance;
                __s.on(this._rlKey, this, this.loadRLSk);
                __s.initSk("royallevel/huangguan", this._rlKey);
            }
        } else {
            this.UI.RL.removeSelf();
        }
        this.callLater(() => {
            var hBox = this.UI.name_box.getChildAt(0);
            if (hBox && hBox.width < 180) {
                if (this.side == 0 || this.side == 3) {
                    hBox.left = (180 - hBox.width) / 2;
                } else {
                    hBox.right = (180 - hBox.width) / 2;
                }
            }
        })
        if (val.idx == yalla.Global.Account.idx)
            // this.UI.dice_bg.alpha = 1;
            // this.updateMuteVoice();
            this.isInChatOff = Boolean(val.openChatOff);//0聊天  1屏蔽
        // this.win(jackaroPlayerShowGameData.winIndex);
        let room = yalla.data.jackaro.JackaroUserService.instance.room;
        //1.4.5 1v1 默认不显示组队显示
        if (room && !room.is1V1Mode()) {
            let team = JackaroGamePlayerManager.instance.getTeam(this.idx);
            team && this.head && this.head.showTeam(team, this.color);
        }
    }

    get data(): playerShowInfo {
        this.dataSource['fPlayerInfo'] = {
            country: this.dataSource.country, faceId: this.dataSource.faceId, faceUrl: this.dataSource.faceUrl,
            ftype: 0,
            idx: this.dataSource.idx,
            level: this.dataSource.level,
            nikeName: this.dataSource.nickName,
            placeId: this.dataSource.placeId,
            vipLevel: this.dataSource.vipLevel,
            winCount: this.dataSource.winCount,
            totalCount: this.dataSource.totalCount,
            talkSkinId: this.dataSource.talkSkinId || 11000,

        };
        return this.dataSource;
    }
    set color(color: number) {
        this.head.color = color;
    }
    get color(): number {
        return this.head.color;
    }
}