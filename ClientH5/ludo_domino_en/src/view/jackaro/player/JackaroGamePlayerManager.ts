class JackaroGamePlayerManager {
    public static _instance: JackaroGamePlayerManager;
    public static get instance(): JackaroGamePlayerManager {
        if (!this._instance) this._instance = new JackaroGamePlayerManager();
        return this._instance;
    }
    public playerPool: { [key: string]: JackaroGamePlayer } = {};
    private playerLayer: Laya.Sprite;
    private handCardLayerPlaceHolder: Laya.Box;
    private gameUI: yalla.view.game.jackaro.Game;
    public curOperatePlayerIdx: number;
    public lastOperatePlayerIdx: number;//上一个的操作玩家
    // {{ YUM: [新增] - 临时保存奖励牌触发状态，用于检查连续回合时的头像动效 }}
    private tempRewardPokerTriggered: boolean = false;

    // {{ YUM: [新增] - 设置临时奖励牌触发状态 }}
    public setTempRewardPokerTriggered(triggered: boolean) {
        this.tempRewardPokerTriggered = triggered;
    }
    private bankerInfo: Record<string, boolean> = {};
    private curPlayerDataMap: Record<string, yalla.data.jackaro.PlayerJackarooGameDataInterface>;
    //是否允许走队友棋子
    private allowMoveTeammateChess: boolean = false;
    //是否被强制弃牌
    private forcedDiscard: boolean = false;

    public static operateTime: number = 20 * 1000;//默认操作时间 20秒


    /**
     * 当前可以加经验的玩家idx 列表
     */
    // private addExpPlayerIdxList: number[] = [];

    public posMap: Record<string, laya.maths.Point> = {};

    /**
     * //仅自己可见的游戏数据
        message PlayerSelfGameData {
            int32 extendTimeCount = 1; //延时次数
            int32 extendTimeCost = 2; //延时需要消耗,钻石数
            bool trustSystem = 3; //是否托管,
        }
     */
    private playerSelfGameData: yalla.data.jackaro.PlayerSelfGameDataInterface;
    private curOfflinePlayerIdx: number = 0;
    private curOffLineSelfOperateTime: number = 0;
    private cProgress: CircularProgressBar = null;


    private outRound: number = 0; //出牌操作的轮次,每次切换玩家加1,一直累计

    private logTag: string = "JackaroChessManager :";
    constructor() {

    }
    public init(gameUI: yalla.view.game.jackaro.Game, playerLayer: Laya.Sprite, handCardLayerPlaceHolder: Laya.Box, cProgress: CircularProgressBar) {
        this.gameUI = gameUI;
        this.playerLayer = playerLayer;
        this.handCardLayerPlaceHolder = handCardLayerPlaceHolder;
        this.cProgress = cProgress;

    }
    public updatePlayerDataMap(dataMap: Record<string, yalla.data.jackaro.PlayerJackarooGameDataInterface>) {
        this.curPlayerDataMap = dataMap;
    }
    public getBankerColor(): number {
        for (let idx in this.curPlayerDataMap) {
            if (this.curPlayerDataMap[idx].banker) {
                return this.getColorByIdx(parseInt(idx));
            }
        }
        return yalla.data.jackaro.JackarooColor.RED;
    }
    public getBankerIdx(): number {
        for (let idx in this.curPlayerDataMap) {
            if (this.curPlayerDataMap[idx].banker) {
                return parseInt(idx);
            }
        }
        return -1;
    }
    public getSideByIdx(idx: number): number {
        let player = this.getGamePlayerByIdx(idx);
        if (player) {
            return player.getSide();
        }
        return 0;
    }
    /**
     * 获取队友 idx
     */
    public getTeamIdx(): number {
        let selfIdx = yalla.Global.Account.idx
        for (let idx in this.curPlayerDataMap) {
            if (parseInt(idx) != selfIdx && this.isSameTeam(parseInt(idx))) {
                return parseInt(idx);
            }
        }
        return -1;
    }
    public updateBankerInfo(idx: number, banker: boolean) {
        this.bankerInfo[idx] = banker;
    }
    private playerShowInfoMap: Record<string, yalla.data.jackaro.PlayerShowInfoInterface> = {} as Record<string, yalla.data.jackaro.PlayerShowInfoInterface>;
    private playerShowGameDataMap: Record<string, yalla.data.jackaro.PlayerShowGameDataInterface> = {} as Record<string, yalla.data.jackaro.PlayerShowGameDataInterface>;
    public updatePlayerShowInfo(data: yalla.data.jackaro.PlayerShowInfoInterface) {
        data.realRoyLevel = data.royalLevel;
        if (data.idx == yalla.Global.Account.idx) {
            yalla.Global.Account.voiceType = data.voiceType;
            yalla.Global.Account.banTalkData = data.banTalkData || "";
            yalla.Global.Account.realRoyLevel = data.royalLevel;
        }
        if (data && data.idx) {
            this.playerShowInfoMap[data.idx] = data;
        }
    }
    public updatePlayerShowGameDataList(idx: number, data: yalla.data.jackaro.PlayerShowGameDataInterface) {
        if (data && idx) {
            this.playerShowGameDataMap[idx] = data;
        }
    }
    public getCurOperatePlayerIdx(): number {
        return this.curOperatePlayerIdx;
    }
    public setAllowMoveTeammateChess(allow: boolean) {
        this.allowMoveTeammateChess = allow;
    }
    public getMoveChessPlayerIdx(): number {
        return this.allowMoveTeammateChess ? this.getTeamIdx() : yalla.data.jackaro.JackaroUserService.instance.getCurUserId();
    }
    getPlayerByIdx(idx: number): yalla.data.jackaro.PlayerShowInfoInterface {
        return this.playerShowInfoMap[idx];
    }
    isHonorRoyal(): boolean {
        if (!this.playerShowInfoMap) return;
        for (const key in this.playerShowInfoMap) {
            const element = this.playerShowInfoMap[key];
            if (element.royalLevel >= 4) return true;
        }
        return false;
    }
    public getColorByIdx(idx: number): number {
        return (this.playerShowGameDataMap[idx] && this.playerShowGameDataMap[idx].color) || yalla.data.jackaro.JackarooColor.RED;
    }
    public getPlayerShowGameDataByIdx(idx: number): yalla.data.jackaro.PlayerShowGameDataInterface {
        return this.playerShowGameDataMap[idx];
    }
    public getSelfColor(): number {
        return this.getColorByIdx(yalla.Global.Account.idx);
    }
    public updatePlayerSelfGameData(data: yalla.data.jackaro.PlayerSelfGameDataInterface) {
        this.playerSelfGameData = data;
    }
    public getPlayerSelfGameData(): yalla.data.jackaro.PlayerSelfGameDataInterface {
        return this.playerSelfGameData;
    }
    private isPlayerLayerValid(): boolean {
        return Boolean(this.playerLayer && this.playerLayer.parent);
    }
    public updatePlayer(playerShowInfo: yalla.data.jackaro.PlayerShowInfoInterface) {
        if (!this.isPlayerLayerValid()) return;
        let player = this.getGamePlayerByIdx(playerShowInfo.idx);
        let idx = playerShowInfo.idx;
        let primaryColor = this.getColorByIdx(yalla.Global.Account.idx);

        if (!player) {
            let room = yalla.data.jackaro.JackaroUserService.instance.room;
            player = new JackaroGamePlayer(primaryColor, this.getColorByIdx(idx), false, playerShowInfo, room.againstType);
            player.head.gameUI = this.gameUI;
            this.playerLayer.addChild(player);
            this.playerPool["player_" + idx] = player;
            this.posMap[String(idx)] = player.head.localToGlobal(new Laya.Point());
        } else {
            // player.updatePlayerColor(primaryColor, this.getColorByIdx(playerShowInfo.idx), false, playerShowInfo);
        }

        player.setPlayerInfo(playerShowInfo);
        let isInChatOff = playerShowInfo.openChatOff;
        if (idx == yalla.Global.Account.idx) {
            isInChatOff = Boolean(ludo.muteSpectator.isMute);
            player.setHandCardLayer(this.handCardLayerPlaceHolder);
        }
        player.isInChatOff = isInChatOff;
    }
    public updatePlayerList(playerShowInfoList: yalla.data.jackaro.PlayerShowInfoInterface[]) {
        for (let playerShowInfo of playerShowInfoList) {
            this.updatePlayer(playerShowInfo);
        }
    }
    public updatePlayerByIdx(idx: number) {
        if (this.playerShowInfoMap[idx]) {
            this.updatePlayer(this.playerShowInfoMap[idx]);
        }
    }
    public getGamePlayerByIdx(idx: number): JackaroGamePlayer {
        return this.playerPool["player_" + idx];
    }
    public getPlayerPool() {
        return this.playerPool;
    }
    public getPlayerCanDiscard(idx: number): boolean {
        if (idx == yalla.Global.Account.idx && this.forcedDiscard) {
            return true;
        }
        let player = this.getGamePlayerByIdx(idx);
        return player && player.playerStatus == yalla.data.jackaro.PlayerStatus.WAIT_DISCARD;
    }
    public setForcedDiscard(forcedDiscard: boolean) {
        this.forcedDiscard = forcedDiscard;
    }
    public getOperateTime(): number {
        let player = this.getGamePlayerByIdx(yalla.Global.Account.idx);
        if (!player) {
            return 0;
        }
        if (player.remainThinkingTime <= 0) {
            return JackaroGamePlayerManager.operateTime;
        }
        return player.remainThinkingTime;
    }

    /**
     * 	export enum JackarooColor {
        RED = 0,
        GREEN = 1,
        YELLOW = 2,
        BLUE = 3,
    }
     */
    public getPlayerUISide(idx: number): number {
        let primaryColor = this.getColorByIdx(yalla.Global.Account.idx);
        let otherColor = this.getColorByIdx(idx);
        let side = 0;
        for (let i = 0; i < 4; i++) {
            let index: number = (primaryColor + i) % 4;
            if (otherColor == index) {
                side = i;
                break;
            }
        }
        return side;
    }
    /**
     * 处理玩家头像切换，该方法不涉及重新播放玩家头像倒计时
     */
    public handlePlayerChange(chessPanel: ChessPanelInterface) {//TICKET_LOGIN 只有断线重连调用
        //清理延迟执行切换玩家的头像以及箭头效果方法
        Laya.timer.clearAll(this);
        var currentPlayer = chessPanel.currentPlayer;
        if (this.curOperatePlayerIdx != currentPlayer) {
            this.clearOperatePlayer(this.curOperatePlayerIdx);
            let gamePlayer = this.getGamePlayerByIdx(currentPlayer);
            gamePlayer && gamePlayer.playThrowJump(true);
        }
        this.curOperatePlayerIdx = currentPlayer;
        this.resetTurnAlreadyPlayExpIdxs();
        TipsViewManager.getInstance().checkCloseTipsAfterChangePlayer(this.curOperatePlayerIdx);
    }
    /**
     * 清除上一次操作的玩家
     */
    public clearOperatePlayer(idx: number) {
        let gamePlayer = this.getGamePlayerByIdx(idx);
        if (gamePlayer) {
            gamePlayer.waitCdEnd();
            gamePlayer.playThrowJump(false);
            gamePlayer.isDiscard = false;
        }
        this.cProgress && this.cProgress.hideProgress();
        this.clearOperateDiscard(idx);
    }
    private clearOperateDiscard(idx: number) {
        let curForcedDiscardIdx = JackaroCardManger.instance.getCurForcedDiscardIdx();
        if (curForcedDiscardIdx == idx) {
            JackaroCardManger.instance.resetCurForcedDiscardIdx();
        }
    }
    private checkPlayer(chessPanel: ChessPanelInterface) {
        this.clearOperatePlayer(this.lastOperatePlayerIdx);
        let curGamePlayer = this.getGamePlayerByIdx(this.curOperatePlayerIdx);//当前玩家
        if (curGamePlayer) {
            //1.4.5 如果上一个玩家是自己，而且触发奖励牌，再次轮到自己 则不播放头像缩放效果，其它情况都要播放
            // {{ YYY: [修复] - 检查奖励牌触发状态，避免连续触发时播放头像缩放效果 }}
            let shouldPlayRoleAni = true;

            // 检查是否是同一个玩家且触发了奖励牌
            if (this.lastOperatePlayerIdx == this.curOperatePlayerIdx) {
                // 当前玩家和上一个玩家是同一人，检查临时保存的奖励牌触发状态
                if (this.tempRewardPokerTriggered) {
                    //1.4.5 如果上回合触发了奖励牌而且加时了，则这个回合不能加延时，更新延时状态
                    if (yalla.data.jackaro.JackaroUserService.instance.user && yalla.data.jackaro.JackaroUserService.instance.user.isUseExtendTime) {
                        yalla.data.jackaro.JackaroUserService.instance.user.updateExtendState(true);
                    }
                    shouldPlayRoleAni = false;
                    yalla.Debug.yum(`[头像动效] 玩家${this.curOperatePlayerIdx}触发奖励牌连续回合，跳过头像缩放效果`);
                }
            } else {
                // 不同玩家之间的切换，正常播放动效
                shouldPlayRoleAni = true;
            }

            if (shouldPlayRoleAni) {
                //1.4.5 切换玩家时还原延时状态
                yalla.data.jackaro.JackaroUserService.instance.user && yalla.data.jackaro.JackaroUserService.instance.user.updateExtendState();
                curGamePlayer.playRoleAni();
            }

            // {{ YY: [新增] - 重置临时奖励牌状态，确保下次检查时状态干净 }}
            this.tempRewardPokerTriggered = false;

            var operateTime = yalla.data.jackaro.JackaroUserService.instance.getOperateTime(chessPanel.thinkingEndTime);
            var operateCountdownCDTime = JackaroGamePlayer.operateCountdownCDTime;
            operateTime = Math.max(0, operateTime - 100);

            curGamePlayer.waitCdStart(operateTime, operateCountdownCDTime);
            curGamePlayer.playThrowJump(true);
            yalla.Debug.zzf("checkPlayer");
            yalla.Debug.zzf(chessPanel);
            yalla.Debug.zzf({ operateTime });
            this.cProgress && this.cProgress.showProgress(operateTime, operateTime, curGamePlayer.color);
        }
    }

    public changePlayer(chessPanel: ChessPanelInterface) {
        Laya.timer.clearAll(this);
        this.lastOperatePlayerIdx = this.curOperatePlayerIdx;
        this.curOperatePlayerIdx = chessPanel.currentPlayer;

        this.outRound = chessPanel.outRound || 0;
        let curForcedDiscardIdx = JackaroCardManger.instance.getCurForcedDiscardIdx();
        if (this.curOperatePlayerIdx == curForcedDiscardIdx && this.curOperatePlayerIdx != yalla.Global.Account.idx) {
            let curForceDiscardPlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(curForcedDiscardIdx);
            if (curForceDiscardPlayer) {
                curForceDiscardPlayer.isDiscard = true;
            }
        }
        this.clearOperateDiscard(this.lastOperatePlayerIdx);
        // if (yalla.Global.isFouce) {
        //     let defaultDelayChangePlayerTime = 100;
        //     if (this.lastOperatePlayerIdx && this.lastOperatePlayerIdx != yalla.Global.Account.idx && JackaroChessManager.instance.getIsPokerSevenOnlyTwoChessMove()) {
        //         defaultDelayChangePlayerTime = 300;
        //     }
        //     Laya.timer.once(defaultDelayChangePlayerTime, this, this.checkPlayer);
        // } else {
        this.checkPlayer(chessPanel);
        // }
        this.forcedDiscard = false;
        this.resetTurnAlreadyPlayExpIdxs();
        TipsViewManager.getInstance().checkCloseTipsAfterChangePlayer(this.curOperatePlayerIdx);
    }
    private resetTurnAlreadyPlayExpIdxs() {
        if (this.curOperatePlayerIdx == yalla.Global.Account.idx || this.curOperatePlayerIdx == JackaroGamePlayerManager.instance.getTeamIdx()) {
            JackaroChessManager.instance.resetTurnAlreadyPlayExpIdxs();
        }
    }
    public isCurOutRound(curRound: number): boolean {
        return curRound == this.outRound;
    }
    public getOutRound(): number {
        return this.outRound;
    }
    public setOutRound(curOutRound: number) {
        this.outRound = curOutRound;
    }
    /** extendTime 单位ms
     * 用户的累积总时长也需要更新，因先加时再出现操作进度条，那进度条的总时长还是默认的15s
    */
    public addWaitCdTime(curIdx: number, extendTime?: number) {
        let gamePlayer = this.getGamePlayerByIdx(curIdx);
        if (gamePlayer) {
            yalla.Debug.logMore("1  触发加时 extendTime:" + extendTime + "curIdx:" + curIdx);
            gamePlayer.addWaitCdTime(extendTime);
            gamePlayer.totalThinkingTime = Math.round((extendTime + yalla.data.jackaro.JackaroUserService.instance.defaultOperateTime));
        }
        TipsViewManager.getInstance().addWaitCdTime(extendTime);
    }

    // handleChangePlayer(msg: ChangePlayerInterface) {
    //     // this.clearLastOperatePlayer();
    //     // this.setChessPanel(msg.chessPanel);
    // }
    /**
     * 获取下一个玩家
     */
    public getNextPlayerIdx(): number {
        let playerList = this.getSortedPlayerIdxList();
        let curPlayerIdx = playerList.indexOf(yalla.Global.Account.idx);
        if (playerList[curPlayerIdx + 1]) {
            return playerList[curPlayerIdx + 1];
        } else {
            return playerList[curPlayerIdx + 1 - playerList.length];
        }
    }

    /**
     * //颜色,约定红色为庄家所在位置,红色区域逆时针依次为:蓝,黄、绿、
        //约定红黄一队,绿蓝一队,根据玩家的颜色即可判断是否是队友
        enum JackarooColor {
            RED = 0;
            BLUE = 1;
            YELLOW = 2;
            GREEN = 3;
        }
        根据玩家颜色生成排序后玩家 idx 列表
     */
    public getSortedPlayerIdxList(): number[] {
        //从玩家自己开始发牌
        let bankerIdx = yalla.Global.Account.idx;
        let playerList: number[] = [];
        for (const key in this.playerPool) {
            playerList.push(this.playerPool[key].idx);
        }
        let sortedPlayerList = playerList.sort((a, b) => {
            return this.getColorByIdx(a) - this.getColorByIdx(b);
        });
        if (bankerIdx != -1) {
            let bankerOffset = sortedPlayerList.indexOf(bankerIdx);
            let tempList = sortedPlayerList.splice(bankerOffset, sortedPlayerList.length - bankerOffset);
            sortedPlayerList = tempList.concat(sortedPlayerList);
        }
        return sortedPlayerList;
    }

    /**获取房间里在座用户idx列表 */
    public get playerIdxList() {
        let playerList: number[] = [];
        for (const key in this.playerPool) {
            playerList.push(this.playerPool[key].idx);
        }
        return playerList;
    }

    public clear() {
        for (const key in this.playerPool) {
            this.playerPool[key] && this.playerPool[key].clear();
        }
        this.playerPool = {};
        this.playerShowInfoMap = {};
        this.playerShowGameDataMap = {};
        JackaroGamePlayerManager._instance = null;
    }
    /**
     * 清理玩家头像上的弃牌标识
     * @param idx 玩家idx
     */
    public resetPlayerForcedDiscard(idx: number) {
        if (this.curOperatePlayerIdx == idx) {
            if (this.playerPool && this.playerPool["player_" + idx]) {
                this.playerPool["player_" + idx].isDiscard = false;
            }
        }
    }
    /**
     * 判断和自己是否是队友
     */
    public isSameTeam(idx: number): boolean {
        // let primaryColor = this.getColorByIdx(yalla.Global.Account.idx);
        // let otherColor = this.getColorByIdx(idx);
        // return (primaryColor + 2) % 4 == otherColor;
        let selfIdx = yalla.Global.Account.idx;
        let room = yalla.data.jackaro.JackaroUserService.instance.room;
        let teamInfo = room.getTeamInfo();
        if (teamInfo) {
            if ((selfIdx == teamInfo.teamAPlayer1 || selfIdx == teamInfo.teamAPlayer2) && (idx == teamInfo.teamAPlayer1 || idx == teamInfo.teamAPlayer2)) {
                return true;
            } else if ((selfIdx == teamInfo.teamBPlayer1 || selfIdx == teamInfo.teamBPlayer2) && (idx == teamInfo.teamBPlayer1 || idx == teamInfo.teamBPlayer2)) {
                return true;
            }
        }
        return false;
    }
    /**
     * 判断两个玩家是否互为队友
     * @param idx1 玩家idx1
     * @param idx2 玩家idx2
     */
    public isInSameTeammate(idx1: number, idx2: number): boolean {
        let room = yalla.data.jackaro.JackaroUserService.instance.room;
        if (!room) return false;
        let teamInfo = room.getTeamInfo();
        if (teamInfo) {
            if ((idx1 == teamInfo.teamAPlayer1 && idx2 == teamInfo.teamAPlayer2) || (idx1 == teamInfo.teamAPlayer2 && idx2 == teamInfo.teamAPlayer1)) {
                return true;
            } else if ((idx1 == teamInfo.teamBPlayer1 && idx2 == teamInfo.teamBPlayer2) || (idx1 == teamInfo.teamBPlayer2 && idx2 == teamInfo.teamBPlayer1)) {
                return true;
            }
        }
        return false;
    }
    /**
     * 根据其他玩家idx获取队友idx
     * @param idx 
     * @returns 
     */
    public getTeamIdxByOther(idx: number): number {
        let room = yalla.data.jackaro.JackaroUserService.instance.room;
        if (!room) return 0;
        let teamInfo = room.getTeamInfo();
        if (teamInfo) {
            if (idx == teamInfo.teamAPlayer1) {
                return teamInfo.teamAPlayer2;
            } else if (idx == teamInfo.teamAPlayer2) {
                return teamInfo.teamAPlayer1;
            } else if (idx == teamInfo.teamBPlayer1) {
                return teamInfo.teamBPlayer2;
            } else if (idx == teamInfo.teamBPlayer2) {
                return teamInfo.teamBPlayer1;
            }
        }
        return 0;
    }
    public getTeam(idx: number): string {
        let room = yalla.data.jackaro.JackaroUserService.instance.room;
        let teamInfo = room.getTeamInfo();
        if (teamInfo && (idx == teamInfo.teamAPlayer1 || idx == teamInfo.teamAPlayer2)) {
            return "A";
        } else {
            return "B";
        }
    }
    public isMyActive(): boolean {
        return this.curOperatePlayerIdx == yalla.Global.Account.idx;
    }
    public resetOfflineData() {
        this.curOfflinePlayerIdx = 0;
        this.curOffLineSelfOperateTime = 0;
        this.tempRewardPokerTriggered = false;
    }
    /**
     * 重置记录断线时玩家idx 和 断线时间
     */
    public recordCurOfflinePlayerIdx() {
        //防止掉线时，多次调用，导致记录错误
        if (this.curOfflinePlayerIdx == 0) {
            this.curOfflinePlayerIdx = this.curOperatePlayerIdx;
        }
    }
    public recordCurOffLineSelfOperateTime() {
        //防止掉线时，多次调用，导致记录错误
        if (this.isMyActive() && this.curOffLineSelfOperateTime == 0) {
            let player = this.getGamePlayerByIdx(yalla.Global.Account.idx);
            if (player) {
                this.curOffLineSelfOperateTime = player.getWaitCdEndTime();
            }
        }
    }
    public getCurOffLineSelfOperateTime(): number {
        return this.curOffLineSelfOperateTime;
    }
    public getCurOfflinePlayerIdx(): number {
        return this.curOfflinePlayerIdx;
    }
    public forEach(callBack: Function) {
        for (const key in this.playerPool) {
            this.playerPool[key] && callBack && callBack(this.playerPool[key]);
        }
    }

    static getPcolor(index: number) {
        // 0: 红色, 1: 蓝色, 2: 黄色, 3: 绿色
        return ["#b2340a", "#0d7ecd", "#c49002", "#098f06"][index % 4];
    }

}