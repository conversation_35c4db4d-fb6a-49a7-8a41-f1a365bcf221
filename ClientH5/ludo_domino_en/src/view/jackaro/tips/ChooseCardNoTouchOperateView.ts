

/**
 * 手牌点击阻挡view
 */
class ChooseCardNoTouchOperateView extends ui.jackaro.tip.chooseCardNoTouchOperateUI implements BaseTipInterface {

    private layer: Laya.Box;
    private noTouchView: JackaroCardNoTouchView;

    constructor() {
        super();
        if (yalla.Screen.hasHair || yalla.Screen.screen_ratio > 1.9) {
            this.progress_box.centerY += 40;
        }
    }
    setLayer(layer: Laya.Box): void {
        this.layer = layer;
    }
    canShowChessMask(): boolean {
        return true;
    }
    getChessMaskParent(): Laya.Sprite {
        return this.chessMask;
    }
    checkShowChessMask() {
        if (this.canShowChessMask()) {
            if (!this.noTouchView) {
                this.noTouchView = new JackaroCardNoTouchView();
                this.addChild(this.noTouchView);
                this.noTouchView.show();
            }
        }
    }
    show(params: any): void {
        let selfPlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);
        if (selfPlayer && selfPlayer.getTrustSystem()) {
            return;
        }
        if (this.layer && !this.layer.contains(this)) {
            this.layer.addChild(this);
        }
        this.visible = true;
        this.checkShowChessMask();
    }
    public getProgressBarCommonView() {
        return null;
    }
    hide(): void {
        this.visible = false;
    }
    close(): void {
        this.removeSelf();
    }
}
