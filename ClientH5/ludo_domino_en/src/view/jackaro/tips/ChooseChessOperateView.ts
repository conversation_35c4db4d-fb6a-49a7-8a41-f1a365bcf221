

/**
 * 多个棋子可以走时选择棋子界面
 */
class ChooseChessOperateView extends ui.jackaro.tip.chooseChessOperateUI implements BaseTipInterface {

    private layer: Laya.Box;
    private operateTime: number = 0;
    private progressBarCommonView: ProgressBarCommonView;
    constructor() {
        super();
        if (yalla.Screen.hasHair || yalla.Screen.screen_ratio > 1.9) {
            this.progress_box.centerY += 40;
        }
    }
    setLayer(layer: Laya.Box): void {
        this.layer = layer;
    }
    canShowChessMask(): boolean {
        return true;
    }
    getChessMaskParent(): Laya.Sprite {
        return this.chessMask;
    }
    checkShowChessMask() {
        if (this.canShowChessMask()) {
            JackaroChessManager.instance.updateChessChoose();
            JackaroChessMaskViewManager.instance.show(this.getChessMaskParent());
        }
    }
    show(params: any): void {
        var p = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);
        if (p && p.getTrustSystem()) {
            return;
        }
        if (this.layer && !this.layer.contains(this)) {
            this.layer.addChild(this);
        }
        this.visible = true;
        this.checkShowChessMask();
        // this.tip.text = params.tips ? params.tips : "Choose a stone\nto move";
        this.operateTime = params.operateTime ? params.operateTime : 0;

        if (!this.progressBarCommonView) {
            this.progressBarCommonView = new ProgressBarCommonView();
            this.progress_box.addChild(this.progressBarCommonView);
        }
        this.progressBarCommonView.visible = true;
        this.progressBarCommonView.show(this.operateTime);
    }
    public getProgressBarCommonView() {
        return this.progressBarCommonView;
    }
    hide(): void {
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        this.visible = false;
        if (this.canShowChessMask()) {
            JackaroChessMaskViewManager.instance.hide(this.getChessMaskParent());
        }
    }
    close(): void {
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        if (this.canShowChessMask()) {
            JackaroChessMaskViewManager.instance.close();
        }
        this.removeSelf();
    }
}
