/**
 * 弃牌界面
 */
class DiscardCardView extends ui.jackaro.tip.DiscardCardUI implements BaseTipInterface {
    private layer: Laya.Box;
    private operateTime: number = 0;
    private isSendRequest: boolean = false;
    private progressBarCommonView: ProgressBarCommonView;
    private btnDiscardTimeLine: Laya.TimeLine;
    constructor() {
        super();
        if (yalla.Screen.hasHair || yalla.Screen.screen_ratio > 1.9) {
            this.progress_box.centerY += 40;
        }
    }
    canShowChessMask(): boolean {
        return false;
    }
    getChessMaskParent(): Laya.Sprite {
        return null;
    }
    show(params: any): void {
        let selfGamePlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);
        if (selfGamePlayer && selfGamePlayer.getTrustSystem()) {
            return;
        }
        if (this.layer && !this.layer.contains(this)) {
            this.layer.addChild(this);
        }
       
        if (params.tip) {
            this.tip.text = params.tip;
        }
        this.discard_txt.skin = yalla.Font.isRight() ? "jackaro/txt_discard_ar.png" : "jackaro/txt_discard.png";
        this.btnDiscardAll_L.visible = false;
        this.btnDiscardAll_R.visible = false;
        //1.4.4 全部弃牌按钮 根据对局场景来显示
        if (!params.isForceDiscard && JackaroCardManger.instance.getDiscardAllBtnVisible()) {
            this.btnDiscardAll_L.visible = yalla.Font.isRight();
            this.btnDiscardAll_R.visible = !yalla.Font.isRight();
            this.btnDiscardAll_L.off(Laya.Event.CLICK, this, this.onDiscardAllClick);
            this.btnDiscardAll_L.on(Laya.Event.CLICK, this, this.onDiscardAllClick);
            this.btnDiscardAll_R.off(Laya.Event.CLICK, this, this.onDiscardAllClick);
            this.btnDiscardAll_R.on(Laya.Event.CLICK, this, this.onDiscardAllClick);
            // this.playDiscardBtnEffect();
        }
        this.visible = true;

        this.operateTime = params.operateTime ? params.operateTime : 0;
        if (!this.progressBarCommonView) {
            this.progressBarCommonView = new ProgressBarCommonView();
            this.progress_box.addChild(this.progressBarCommonView);
        }
        this.progressBarCommonView.show(this.operateTime);
    }
    private playDiscardBtnEffect() {
        // if (yalla.Global.isFouce) {
        //     if (!this.btnDiscardTimeLine) {
        //         this.btnDiscardTimeLine = new Laya.TimeLine();
        //     } else {
        //         this.btnDiscardTimeLine.reset();
        //     }
        //     this.btnDiscardAll.alpha = 0;
        //     this.btnDiscardTimeLine.addLabel("0", 0).to(this.btnDiscardAll, { alpha: 0, scaleX: 0.8, scaleY: 0.8 }, 1366);
        //     this.btnDiscardTimeLine.addLabel("1", 0).to(this.btnDiscardAll, { alpha: 1, scaleX: 0.92, scaleY: 0.92 }, 166, null);
        //     this.btnDiscardTimeLine.addLabel("2", 0).to(this.btnDiscardAll, { alpha: 1, scaleX: 1, scaleY: 1 }, 100, null);
        //     this.btnDiscardTimeLine.play("0", false);
        // }
    }

    private onDiscardAllClick(): void {
        yalla.util.clogDBAmsg('10370');
        if (!this.isSendRequest) {
            JackaroCardManger.instance.discardAllCard();
            this.isSendRequest = true;
            if (this.btnDiscardAll_L.visible || this.btnDiscardAll_R.visible) {
                TipsViewManager.getInstance().hideTip(TipViewType.DISCARD_ALL_CARD);
            } else {
                TipsViewManager.getInstance().hideTip(TipViewType.DISCARD_CARD);
            }
        }
    }
    public getProgressBarCommonView() {
        return this.progressBarCommonView;
    }
    hide(): void {
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        this.visible = false;
        this.isSendRequest = false;
    }
    close(): void {
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        this.removeSelf();
    }
    setLayer(layer: Laya.Box): void {
        this.layer = layer;
    }
}
