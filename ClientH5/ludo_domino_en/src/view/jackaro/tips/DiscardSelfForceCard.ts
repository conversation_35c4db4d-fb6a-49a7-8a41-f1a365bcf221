/**
 * 别人使用10强制弃牌界面
 */
class DiscardSelfForceCard extends ui.jackaro.tip.DiscardSelfForceCardUI implements BaseTipInterface {
    private layer: Laya.Box;
    private operateTime: number = 0;
    private progressBarCommonView: ProgressBarCommonView;
    private selfForceDiscardTimeLine: Laya.TimeLine;
    constructor() {
        super();
        if (yalla.Screen.hasHair || yalla.Screen.screen_ratio > 1.9) {
            this.progress_box.centerY += 40;
        }
    }
    canShowChessMask(): boolean {
        return false;
    }
    getChessMaskParent(): Laya.Sprite {
        return null;
    }
    show(params: any): void {
        let selfGamePlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);
        if (selfGamePlayer && selfGamePlayer.getTrustSystem()) {
            return;
        }
        if (this.layer && !this.layer.contains(this)) {
            this.layer.addChild(this);
        }
        this.visible = true;
        //阿语翻转一下
        if (yalla.Font.isRight()) {
            this.selfForcedDiscard.x = 127;
            this.selfForcedIdent.x = 498;
        }
        this.selfForcedDiscard.skin = yalla.Font.isRight() ? "jackaro/txt_discard_ar.png" : "jackaro/txt_discard.png";
        this.playSelfForcedDiscardAni();

        this.operateTime = params.operateTime ? params.operateTime : 0;
        if (!this.progressBarCommonView) {
            this.progressBarCommonView = new ProgressBarCommonView();
            this.progress_box.addChild(this.progressBarCommonView);
        }
        this.progressBarCommonView.show(this.operateTime);
    }
    private playSelfForcedDiscardAni() {
        if (yalla.Global.isFouce) {
            if (!this.selfForceDiscardTimeLine) {
                this.selfForceDiscardTimeLine = new Laya.TimeLine();
            } else {
                this.selfForceDiscardTimeLine.reset();
            }
            this.selfForceDiscardTimeLine.addLabel("0", 0).to(this.selfForcedDiscard, { alpha: 0 }, 0);
            this.selfForceDiscardTimeLine.addLabel("2", 0).to(this.selfForcedDiscard, { alpha: 1 }, 580 * 0.6, null);
            this.selfForceDiscardTimeLine.play("0", false);
            this.onPlaySelfForcedDiscardAniEnd();
        }
    }
    private onPlaySelfForcedDiscardAniEnd() {
        if (yalla.Font.isRight()) {
            yalla.Sound.playSound('Dicard_Card_10_ar');
        } else {
            yalla.Sound.playSound('Dicard_Card_10');
        }
    }

    public getProgressBarCommonView() {
        return this.progressBarCommonView;
    }
    hide(): void {
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        this.visible = false;
    }
    close(): void {
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        this.removeSelf();
    }
    setLayer(layer: Laya.Box): void {
        this.layer = layer;
    }
}
