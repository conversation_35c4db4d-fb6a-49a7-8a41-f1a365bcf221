/**
 * 交换棋子界面
 * */
class ExChangeChessView extends ui.jackaro.tip.exchangeChessUI implements BaseTipInterface {
    private layer: Laya.Box;
    private operateTime: number = 0;
    private progressBarCommonView: ProgressBarCommonView;
    private curStep: number = 0;
    constructor() {
        super();
        if (yalla.Screen.hasHair || yalla.Screen.screen_ratio > 1.9) {
            this.progress_box.centerY += 40;
        }
    }
    setLayer(layer: Laya.Box): void {
        this.layer = layer;
    }
    canShowChessMask(): boolean {
        return true;
    }
    getChessMaskParent(): Laya.Sprite {
        return this.chessMask;
    }
    checkShowChessMask() {
        if (this.canShowChessMask()) {
            JackaroChessManager.instance.updateChessChoose();
            JackaroChessMaskViewManager.instance.show(this.getChessMaskParent());
        }
    }
    show(params: any): void {
        var p = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);
        if (p && p.getTrustSystem()) {
            return;
        }
        if (this.layer && !this.layer.contains(this)) {
            this.layer.addChild(this);
        }
        this.visible = true;
        this.checkShowChessMask();
        this.operateTime = params.operateTime ? params.operateTime : 0;
        if (!this.progressBarCommonView) {
            this.progressBarCommonView = new ProgressBarCommonView();
            this.progress_box.addChild(this.progressBarCommonView);
        }
        this.progressBarCommonView.show(this.operateTime);
    }
    setCurStep(step: number): void {
        if (step == 0) {
            this.tip.text = "Please choose the\nfirst stone";
        } else if (step == 1) {
            this.tip.text = "Please choose the\nsecond stone";
        }
    }
    public getProgressBarCommonView() {
        return this.progressBarCommonView;
    }
    hide(): void {
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        this.visible = false;
        if (this.canShowChessMask()) {
            JackaroChessMaskViewManager.instance.hide(this.getChessMaskParent());
        }
    }
    close(): void {
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        if (this.canShowChessMask()) {
            JackaroChessMaskViewManager.instance.close();
        }
        this.removeSelf();
    }
}
