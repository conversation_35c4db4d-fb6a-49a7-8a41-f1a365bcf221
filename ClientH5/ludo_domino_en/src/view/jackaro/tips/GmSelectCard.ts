/**
 * Gm界面
 */
class GmSelectCard extends ui.jackaro.tip.GmSelectCardUI implements BaseTipInterface {
    private layer: Laya.Box;
    private isSendRequest: boolean = false;
    private maxCardNum: number = 13;
    private maxColorNum: number = 4;
    private curClickCardIdx: number = 0;
    private curClickColorIdx: number = 0;
    private curShowPickUpPoker: Array<yalla.view.jackaro.JackaroPickUpCardItem> = [];
    private curSourcePoker: yalla.data.jackaro.Poker = yalla.data.jackaro.Poker.NONE_POKER;
    private targetPoker: yalla.data.jackaro.Poker = yalla.data.jackaro.Poker.NONE_POKER;
    constructor() {
        super();
        this.addEvent();
    }
    private addEvent(): void {
        for (let i = 0; i < this.maxCardNum; i++) {
            this["btnCard" + (i + 1)].on(Laya.Event.CLICK, this, this.onCardClick);
        }
        for (let i = 0; i < this.maxColorNum; i++) {
            this["btnColor" + i].on(Laya.Event.CLICK, this, this.onColorClick);
        }
        this.btnExchange.on(Laya.Event.CLICK, this, this.onExchangeClick);
        this.btnClose.on(Laya.Event.CLICK, this, () => {
            this.hide();
        });
    }
    private onCardClick(e: Laya.Event): void {
        let cardIdx = e.target.name.split("btnCard")[1];
        this.curClickCardIdx = parseInt(cardIdx);
    }
    private onColorClick(e: Laya.Event): void {
        let colorIdx = e.target.name.split("btnColor")[1];
        this.curClickColorIdx = parseInt(colorIdx);

        let cardIdx = this.curClickColorIdx * 13 + this.curClickCardIdx;
        this.targetPoker = cardIdx;
    }
    private onExchangeClick(e: Laya.Event): void {
        if (this.curSourcePoker == yalla.data.jackaro.Poker.NONE_POKER) {
            return;
        }
        if (this.targetPoker == yalla.data.jackaro.Poker.NONE_POKER) {
            return;
        }
        yalla.data.jackaro.JackaroUserService.instance.sendTestExchange(this.curSourcePoker, this.targetPoker);
    }


    getProgressBarCommonView(): ProgressBarCommonView {
        return null;
    }

    canShowChessMask(): boolean {
        return false;
    }
    getChessMaskParent(): Laya.Sprite {
        return null;
    }
    show(params: any): void {
        if (this.layer && !this.layer.contains(this)) {
            this.layer.addChild(this);
        }
        // if (params.tip) {
        //     this.tip.text = params.tip;
        // }
        this.showSelfHandCard();
        this.visible = true;
    }
    public showSelfHandCard() {
        //获取该玩家所有手牌
        let playerHandPokerItems: Array<yalla.view.jackaro.JackaroCardItem> = JackaroCardManger.instance.getPlayerAllPokerItem(yalla.Global.Account.idx);
        if (!playerHandPokerItems || playerHandPokerItems.length == 0) {
            return;
        }
        let cardLen = playerHandPokerItems.length;
        for (let index = 0; index < cardLen; index++) {
            const element = playerHandPokerItems[index];
            let pickUpCardItem = this.getCardItem();
            pickUpCardItem.pos(element.x, element.y);
            pickUpCardItem.scaleX = element.scaleX;
            pickUpCardItem.scaleY = element.scaleY;
            pickUpCardItem.setCardData(element.data);
            pickUpCardItem.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_PLAY);
            pickUpCardItem.setShowIdx(index);
            // pickUpCardItem.removeEvent();
            pickUpCardItem.setClickCallback((poker: yalla.data.jackaro.Poker, idx: number) => {
                this.curSourcePoker = poker;
            });
            this.addChild(pickUpCardItem);
        }
        let cardWid = this.curShowPickUpPoker[0].getCardWidth();
        let cardScale = yalla.view.jackaro.JackaroCardItem.normalScale;
        let gap = cardLen > 3 ? 10 : 30;
        let totalWidth = cardWid * cardLen + gap * (cardLen - 1);
        let startX = (Laya.stage.width - totalWidth) / 2 + cardWid / 2;
        let posY = Laya.stage.height / 2 + 100;
        //执行 tween 动画
        for (let index = 0; index < cardLen; index++) {
            const element = this.curShowPickUpPoker[index];
            let endPosx = startX + index * (cardWid + gap);

            element.pos(endPosx, posY);
            element.scaleX = cardScale;
            element.scaleY = cardScale;
        }
    }
    private getCardItem() {
        var cardItem;
        cardItem = Laya.Pool.getItemByClass('JackaroPickUpCardItem', yalla.view.jackaro.JackaroPickUpCardItem);
        this.curShowPickUpPoker.push(cardItem);
        cardItem.visible = true;
        return cardItem;
    }
    private clearCardItem() {
        for (let index = 0; index < this.curShowPickUpPoker.length; index++) {
            const item = this.curShowPickUpPoker[index];
            item.removeSelf();
            item.clear();
            Laya.Pool.recover('JackaroPickUpCardItem', item);
        }
        this.curShowPickUpPoker = [];
    }
    hide(): void {
        this.visible = false;
        this.isSendRequest = false;
        this.clearCardItem();
    }
    close(): void {
        this.removeSelf();
        this.clearCardItem();
    }
    setLayer(layer: Laya.Box): void {
        this.layer = layer;
    }
}
