/**
 * 棋子遮罩
 */

class JackaroChessMaskView extends ui.jackaro.tip.jackaroChessMaskUI {
    private interactionAreaPool: Laya.Sprite[] = [];
    private areaCirCleImgPool: Laya.Image[] = [];//区域光圈对象池
    private maskContainer: Laya.Sprite;
    private maskArea: Laya.Sprite;
    constructor() {
        super();
        this.init();
    }
    init(): void {
        if (!this.maskContainer) {
            this.maskContainer = new Laya.Sprite();
            // 设置容器为画布缓存
            this.maskContainer.cacheAs = "bitmap";
            this.addChild(this.maskContainer);

            //绘制遮罩区，含透明度，可见游戏背景
            this.maskArea = new Laya.Sprite();
            this.maskArea.alpha = 0.6;
            this.maskArea.graphics.drawRect(0, 0, Laya.stage.width, Laya.stage.height, "#000000");
            this.maskContainer.addChild(this.maskArea);

            this.hitArea = new Laya.HitArea();
            this.hitArea.hit.drawRect(0, 0, Laya.stage.width, Laya.stage.height, "#000000");

            this.maskContainer.hitArea = this.hitArea;
            this.maskContainer.mouseEnabled = true;
            this.maskContainer.mouseThrough = false;
            this.maskContainer.on("click", this, this.nextStep);
        }
    }
    show(): void {
        this.visible = true;
        // this.recoverChessMask();

        let chesss = JackaroChessManager.instance.getCanChooseChess();
        if (!chesss || chesss.length == 0) {
            return;
        }
        chesss.forEach(ch => {
            if (ch) {
                let { width, height } = ch;
                let chessGlobalPos = ch.getGlobalPos();
                let interactionArea: Laya.Sprite;
                let radius = 18;
                if (this.interactionAreaPool.length > 0) {
                    interactionArea = this.interactionAreaPool.pop();
                } else {
                    interactionArea = new Laya.Sprite();
                    interactionArea.size(radius * 2, radius * 2);
                }
                let posX = chessGlobalPos.x - width / 2 + radius - 0.5;
                let posY = chessGlobalPos.y - height / 2 + radius - 0.5;
                //设置叠加模式
                interactionArea.blendMode = "destination-out";
                interactionArea.pos(posX, posY);
                interactionArea.graphics.clear();
                interactionArea.graphics.drawCircle(radius, radius, radius, "#000000");
                interactionArea.name = "interactionAreaTag";
                this.maskContainer.addChild(interactionArea);


                let areaCirCleImg;
                if (this.areaCirCleImgPool.length > 0) {
                    areaCirCleImg = this.areaCirCleImgPool.pop();
                } else {
                    areaCirCleImg = new Laya.Image();
                }

                areaCirCleImg.skin = "jackaro/img_circle0.png";
                areaCirCleImg.pos(posX - 7, posY - 7);
                areaCirCleImg.name = "areaCirCleImg";
                this.maskContainer.parent.addChild(areaCirCleImg);
            }
        });
    }
    nextStep(e: Laya.Event): void {
        e && e.stopPropagation();
        if (e) {
            JackaroChessManager.instance.clickChessMask(e);;
        }
    }
    hide(): void {
        this.visible = false;
        this.recoverChessMask();
    }
    private recoverChessMask(): void {
        if (!this.maskContainer || !this.maskContainer._childs) {
            return;
        }
        for (let i = this.maskContainer._childs.length - 1; i >= 0; i--) {
            let child = this.maskContainer._childs[i];
            if (child.name == "interactionAreaTag") {
                child.graphics.clear();
                child.removeSelf();
                this.interactionAreaPool.push(child);
            }
        }

        let maskContainerParent = this.maskContainer.parent;
        if (!maskContainerParent || !maskContainerParent._childs) {
            return;
        }
        for (let i = maskContainerParent._childs.length - 1; i >= 0; i--) {
            let child = maskContainerParent._childs[i];
            if (child.name == "areaCirCleImg") {
                child.removeSelf();
                this.areaCirCleImgPool.push(child);
            }
        }
    }
    clear() {
        if (this.maskArea) {
            this.maskArea.graphics && this.maskArea.graphics.clear();
        }
        if (this.hitArea) {
            this.hitArea.hit && this.hitArea.hit.graphics && this.hitArea.hit.graphics.clear();
        }
        if (!this.maskContainer || !this.maskContainer._childs) {
            return;
        }
        for (let i = this.maskContainer._childs.length - 1; i >= 0; i--) {
            let child = this.maskContainer._childs[i];
            if (child.name == "interactionAreaTag") {
                child.graphics.clear();
                child.removeSelf();
            }
        }
    }
    close(): void {
        this.clear();
        this.interactionAreaPool = [];
        this.areaCirCleImgPool = [];
        if (this.maskContainer) {
            this.maskContainer.destroy(true);
        }

        if (this.maskContainer && this.maskContainer.parent) {
            this.maskContainer.parent.removeChildren();
        }

        this.maskContainer = null;
        this.removeSelf();
    }
}