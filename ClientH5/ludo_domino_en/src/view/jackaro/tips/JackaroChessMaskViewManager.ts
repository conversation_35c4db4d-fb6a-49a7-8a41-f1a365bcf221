/**
 * 棋子遮罩管理器
 */
class JackaroChessMaskViewManager {
    private static _instance: JackaroChessMaskViewManager;
    private _maskView: JackaroChessMaskView;
    constructor() {

    }
    public static get instance(): JackaroChessMaskViewManager {
        if (!this._instance) {
            this._instance = new JackaroChessMaskViewManager();
        }
        return this._instance;
    }
    public show(layer: Laya.Sprite): void {
        //防止一直重复使用一个，导致遮罩越来越黑
        if (this._maskView) {
            this._maskView.close();
        }
        this._maskView = new JackaroChessMaskView();
        layer.addChild(this._maskView);
        this._maskView.show();
    }
    public hide(layer: Laya.Sprite): void {
        if (this._maskView)
            this._maskView.hide();
    }
    close(): void {
        if (this._maskView)
            this._maskView.close();
    }
    static clear(): void {
        if (this._instance) {
            if (this._instance._maskView) {
                this._instance._maskView.close();
            }
            this._instance._maskView = null;
            this._instance = null;
        }
    }
}
