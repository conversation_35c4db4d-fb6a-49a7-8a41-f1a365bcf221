/**
 * 棋子点击阻挡层
 */

class JackaroChessNoTouchView extends ui.jackaro.tip.jackaroChessMaskUI {
    private maskContainer: Laya.Sprite;
    constructor() {
        super();
    }
    show(): void {
        this.visible = true;
        if (!this.maskContainer) {
            this.maskContainer = new Laya.Sprite();
            // 设置容器为画布缓存
            this.maskContainer.cacheAs = "bitmap";
            this.addChild(this.maskContainer);

            //绘制遮罩区，含透明度，可见游戏背景
            var maskArea: Laya.Sprite = new Laya.Sprite();
            maskArea.alpha = 0.1;
            maskArea.graphics.drawRect(0, 0, Laya.stage.width, Laya.stage.height, "#000000");
            this.maskContainer.addChild(maskArea);

            this.hitArea = new Laya.HitArea();
            this.hitArea.hit.drawRect(0, 0, Laya.stage.width, Laya.stage.height, "#000000");

            this.maskContainer.hitArea = this.hitArea;
            this.maskContainer.mouseEnabled = true;
            this.maskContainer.mouseThrough = false;
            this.maskContainer.on("click", this, this.nextStep);
        }

    }
    nextStep(e: Laya.Event): void {
        e && e.stopPropagation();
    }
    hide(): void {
        this.visible = false;
    }
    close(): void {
        this.removeSelf();
        this.maskContainer = null;
    }
}