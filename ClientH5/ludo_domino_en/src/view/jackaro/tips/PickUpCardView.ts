/**
 * 抽牌界面
 */
class PickUpCardView extends ui.jackaro.tip.PickUpCardUI implements BaseTipInterface {
    private layer: Laya.Box;
    private operateTime: number = 0;
    private isSendRequest: boolean = false;
    private progressBarCommonView: ProgressBarCommonView;
    private curShowPickUpPoker: Array<yalla.view.jackaro.JackaroPickUpCardItem> = [];
    private pickUpPlayerIdx: number;
    private curUser: yalla.data.jackaro.User;
    constructor() {
        super();
        if (yalla.Screen.hasHair || yalla.Screen.screen_ratio > 1.9) {
            this.progress_box.centerY += 40;
        }
        this.curUser = yalla.data.jackaro.JackaroUserService.instance.user;
    }
    canShowChessMask(): boolean {
        return false;
    }
    getChessMaskParent(): Laya.Sprite {
        return null;
    }
    show(params: any): void {
        let player = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx)
        if (player && player.getTrustSystem()) {
            return;
        }
        if (this.layer && !this.layer.contains(this)) {
            this.layer.addChild(this);
        }
        if (params.tip) {
            this.tip.text = params.tip;
        }
        this.visible = true;
        this.pickUpPlayerIdx = JackaroGamePlayerManager.instance.getNextPlayerIdx();
        //获取该玩家所有手牌
        let playerHandPokerItems: Array<yalla.view.jackaro.JackaroCardItem> = JackaroCardManger.instance.getPlayerAllPokerItem(this.pickUpPlayerIdx);
        if (!playerHandPokerItems || playerHandPokerItems.length == 0) {
            return;
        }
        let room = yalla.data.jackaro.JackaroUserService.instance.room;
        let roomId: number = 0;
        if (room) {
            roomId = room.roomId;
        }
        let cardLen = playerHandPokerItems.length;
        if (cardLen != params.waitDrawnPokerCount) {
            let selfHandPokerItems: Array<yalla.view.jackaro.JackaroCardItem> = JackaroCardManger.instance.getPlayerAllPokerItem(yalla.Global.Account.idx);
            let selfHandPokerStr = "";
            if (selfHandPokerItems && selfHandPokerItems.length > 0) {
                for (let index = 0; index < selfHandPokerItems.length; index++) {
                    const element = selfHandPokerItems[index];
                    if (element && element.data) {
                        selfHandPokerStr = ":" + element.data.poker;
                    }
                }
            }

            let logStr = {
                cardLen,
                waitDrawnPokerCount: params.waitDrawnPokerCount,
                pickUpPlayerIdx: this.pickUpPlayerIdx,
                selfHandPoker: selfHandPokerStr,
                roomId,
            };
            yalla.Native.instance.writeGameLog("jackaroLog", logStr);
            // cardLen = params.waitDrawnPokerCount;
        }
        cardLen = Math.min(cardLen, params.waitDrawnPokerCount);
        let curSkinId = JackaroCardManger.instance.getCardBackSkin(this.pickUpPlayerIdx);
        for (let index = 0; index < cardLen; index++) {
            const element = playerHandPokerItems[index];
            if (!element) continue;
            let pickUpCardItem = this.getCardItem();
            if (curSkinId) {
                pickUpCardItem.setSkinId(curSkinId);
            }
            pickUpCardItem.pos(element.x, element.y);
            pickUpCardItem.scaleX = element.scaleX;
            pickUpCardItem.scaleY = element.scaleY;
            pickUpCardItem.setShowState(yalla.view.jackaro.JackaroCardItemState.IN_HAND);
            pickUpCardItem.setShowIdx(index);
            pickUpCardItem.setOpPoker(params.poker);
            pickUpCardItem.removeEvent();
            this.addChild(pickUpCardItem);
            element.visible = false;
        }
        //使用实际的长度
        let realCardLen = this.curShowPickUpPoker.length;
        // 计算每张卡牌的终点位置
        let cardWid = this.curShowPickUpPoker[0].getCardWidth();
        let cardScale = yalla.view.jackaro.JackaroCardItem.normalScale;
        let gap = realCardLen > 3 ? 10 : 30;
        let totalWidth = cardWid * realCardLen + gap * (realCardLen - 1);
        let startX = (Laya.stage.width - totalWidth) / 2 + cardWid / 2;
        let posY = Laya.stage.height / 2;

        // 计算移动时间
        let maxDistance = 0;
        let endPositions = [];

        // 先计算所有终点位置和最大距离
        for (let index = 0; index < realCardLen; index++) {
            const element = this.curShowPickUpPoker[index];
            let endPosX = startX + (realCardLen - 1 - index) * (cardWid + gap);
            endPositions.push({ x: endPosX, y: posY });

            let distance = yalla.util.getDistance(
                new Laya.Point(element.x, element.y),
                new Laya.Point(endPosX, posY)
            )['Distance'];

            if (distance > maxDistance) {
                maxDistance = distance;
            }
        }

        // 根据最大距离计算动画时间
        let moveTime = maxDistance / yalla.data.GameConfig.PlayCardSpeed;
        if (moveTime < 300) moveTime = 300;
        else if (moveTime > 600) moveTime = 600;

        // 所有卡牌同时移动到各自的终点位置
        for (let index = 0; index < realCardLen; index++) {
            const element = this.curShowPickUpPoker[index];
            let endPos = endPositions[index];

            if (index === realCardLen - 1) {
                Laya.Tween.to(element, {
                    x: endPos.x,
                    y: endPos.y,
                    scaleX: cardScale,
                    scaleY: cardScale
                }, moveTime, null, Laya.Handler.create(this, () => {
                    // 所有卡牌到达终点后启用事件
                    for (let i = 0; i < realCardLen; i++) {
                        const card = this.curShowPickUpPoker[i];
                        card && card.initEvent();
                    }
                }));
            } else {
                Laya.Tween.to(element, {
                    x: endPos.x,
                    y: endPos.y,
                    scaleX: cardScale,
                    scaleY: cardScale
                }, moveTime, null);
            }
        }

        this.operateTime = params.operateTime ? params.operateTime : 0;
        if (!this.progressBarCommonView) {
            this.progressBarCommonView = new ProgressBarCommonView();
            this.progress_box.addChild(this.progressBarCommonView);
        }
        this.progressBarCommonView.show(this.operateTime);
    }
    public drawPokerComplete() {
        let cardScale = yalla.view.jackaro.JackaroCardItem.otherScale;
        //获取该玩家所有手牌
        let playerHandPokerItems: Array<yalla.view.jackaro.JackaroCardItem> = JackaroCardManger.instance.getPlayerAllPokerItem(this.pickUpPlayerIdx);
        let cardLen = playerHandPokerItems.length;
        // 计算最大移动距离，用于统一动画时间
        let maxDistance = 0;
        for (let index = 0; index < cardLen; index++) {
            const element = this.curShowPickUpPoker[index];
            if (!element) continue;
            const playerHandPokerItem = playerHandPokerItems[index];
            let distance = yalla.util.getDistance(
                new Laya.Point(element.x, element.y),
                new Laya.Point(playerHandPokerItem.x, playerHandPokerItem.y)
            )['Distance'];

            if (distance > maxDistance) {
                maxDistance = distance;
            }
        }

        // 根据最大距离计算统一的动画时间
        let moveTime = maxDistance / yalla.data.GameConfig.PlayCardSpeed;
        if (moveTime < 300) moveTime = 300;
        else if (moveTime > 600) moveTime = 600;

        // 移除所有卡牌的事件
        for (let index = 0; index < cardLen; index++) {
            this.curShowPickUpPoker[index] && this.curShowPickUpPoker[index].removeEvent();
        }

        // 所有卡牌同时移动到各自的终点位置
        for (let index = 0; index < cardLen; index++) {
            const element = this.curShowPickUpPoker[index];
            if (!element) continue;
            const playerHandPokerItem = playerHandPokerItems[index];
            let curIndex = index;

            Laya.Tween.to(element, {
                x: playerHandPokerItem.x,
                y: playerHandPokerItem.y,
                scaleX: cardScale,
                scaleY: cardScale
            }, moveTime, null, Laya.Handler.create(this, () => {
                element.visible = false;
                playerHandPokerItem.visible = true;

                // 最后一张卡牌动画完成后隐藏视图
                if (cardLen - 1 == curIndex) {
                    this.hide();
                }
            }));
        }
    }
    private clearCardItem() {
        for (let index = 0; index < this.curShowPickUpPoker.length; index++) {
            const item = this.curShowPickUpPoker[index];
            Laya.Tween.clearAll(item);
            item.removeSelf();
            item.clear();
            !item.destroyed && Laya.Pool.recover('JackaroPickUpCardItem', item);
        }
        this.curShowPickUpPoker = [];
    }
    private getCardItem() {
        var cardItem;
        cardItem = Laya.Pool.getItemByClass('JackaroPickUpCardItem', yalla.view.jackaro.JackaroPickUpCardItem);
        this.curShowPickUpPoker.push(cardItem);
        cardItem.visible = true;
        return cardItem;
    }
    public getProgressBarCommonView() {
        return this.progressBarCommonView;
    }
    hide(): void {
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        this.clearCardItem();
        this.visible = false;
        this.isSendRequest = false;
    }
    close(): void {
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        this.clearCardItem();
        this.removeSelf();
    }
    setLayer(layer: Laya.Box): void {
        this.layer = layer;
    }
}
