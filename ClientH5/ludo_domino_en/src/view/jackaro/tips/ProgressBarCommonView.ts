class ProgressBarCommonView extends ui.jackaro.tip.progressBarCommonUI {

    private operateTime: number = 0;
    private progressValue: Array<number> = [10000, 5000, 1000];
    private progressResources: Array<string> = ["jackaro/progress_bar_icloud.png", "jackaro/progress_bar_icloud2.png", "jackaro/progress_bar_icloud1.png"];
    private maxValue = 15000;//20000;
    private lastTime: number = 0;

    constructor() {
        super();
        this.maxValue = yalla.data.jackaro.JackaroUserService.instance.defaultOperateTime;
    }
    public show(operateTime: number) {
        //断线重连回来这个值，如果玩家使用过延时，这个值肯定要大于20秒，平时都是使用默认值。totalThinkingTime 只会在断线重连回来时 设置，其它时间都是默认值0
        let selfPlayer = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);
        if (selfPlayer && selfPlayer.totalThinkingTime > 0) {
            this.maxValue = selfPlayer.totalThinkingTime;
        } else {
            this.maxValue = yalla.data.jackaro.JackaroUserService.instance.defaultOperateTime;
        }
        this.lastTime = Date.now();
        this.operateTime = operateTime < 1 ? 0 : operateTime;
        this.runFrameLoop();
    }
    public addTime(ms: number) {
        //TODO::卡点加时，但是timer计时器已经结束,但是msecond从上一次开始计算
        this.operateTime += ms;
        this.maxValue += ms;
        this.runFrameLoop();
    }
    private runFrameLoop() {
        this.clear();
        this.updateProgress();
        if (this.operateTime > 0) this.timer.frameLoop(3, this, this.updateProgress);
    }
    private updateProgress() {
        let now = Date.now();
        let deltaTime = (now - this.lastTime);
        this.lastTime = now;
        this.operateTime -= deltaTime;
        if (this.operateTime >= 0) {
            this.progress_bar.value = this.operateTime / this.maxValue;
            this.updateProgressSkin();
        } else {
            this.clear();
        }
    }
    private updateProgressSkin() {
        let resourceIdx: number;
        for (let index = 0; index < this.progressValue.length; index++) {
            const element = this.progressValue[index];
            if (this.operateTime > element) {
                resourceIdx = index;
                break;
            }
        }
        if (this.progress_bar && this.progressResources[resourceIdx]) {
            this.progress_bar.skin = this.progressResources[resourceIdx];
        }
    }
    /**弃牌进度条消失后，重置临时加时变量 */
    clear() {
        this.timer.clear(this, this.updateProgress);
    }
}