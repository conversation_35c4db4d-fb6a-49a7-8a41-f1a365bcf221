class RuleCardView extends ui.jackaro.tip.ruleCardUI implements BaseTipInterface {
    private layer: Laya.Box;
    private curShowPoker: number = 0;
    private prefix: string = "ar_"

    constructor() {
        super();
        // if (yalla.Font.isRight()) {
        //     this.label_tip.width = 534;
        //     this.label_tip.fontSize = 26;
        //     // this.label_tip.wordWrap = false;
        //     // this.label_tip.align = "right";
        // }
        this.prefix = yalla.Font.isRight() ? "ar_" : "en_";
    }

    canShowChessMask(): boolean {
        return false;
    }
    getChessMaskParent(): Laya.Sprite {
        return null;
    }
    show(params: any): void {
        if (!yalla.JackaroTipsShow.isOpen()) {
            return;
        }
        if (this.layer && !this.layer.contains(this)) {
            this.layer.addChild(this);
        }
        this.visible = true;
        //TODO::规则页面和顶部banner的view、底部聊天view互斥，rulecard关闭，如果是非自己回合也把选中牌回落
        yalla.util.ViewExclusion.instance.add(yalla.util.ViewType.RuleCard, () => {
            this.hide();
            if (!JackaroGamePlayerManager.instance.isMyActive()) {
                JackaroCardManger.instance.resetCardUpState(yalla.Global.Account.idx)
                JackaroCardManger.instance.refreshChessOrbit();

            }
        });

        if (params.poker && params.poker == this.curShowPoker) {
            return;
        }
        this.curShowPoker = params.poker;
        let intro = yalla.view.jackaro.CardOperateDefine.getCardIntroRule(this.curShowPoker);

        let suffix = JackaroCardManger.instance.getPokerTypeSuffix(this.curShowPoker);
        if (suffix != "") {
            suffix = `${suffix}_`;
        }
        this.img_poker.skin = `jackaroCard/card_top_${suffix}52000_${params.poker}.png`;
        this.img_tip.skin = `jackaro/pokerRule/${this.prefix + intro}`
    }
    public getProgressBarCommonView() {
        return null;
    }
    hide(): void {
        this.visible = false;
        yalla.util.ViewExclusion.instance.remove(yalla.util.ViewType.RuleCard);
    }
    close(): void {
        this.removeSelf();
    }
    setLayer(layer: Laya.Box): void {
        this.layer = layer;
    }
}
