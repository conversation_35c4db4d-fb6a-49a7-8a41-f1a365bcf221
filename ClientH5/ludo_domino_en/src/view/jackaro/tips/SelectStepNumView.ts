/**
 * 出牌 7 步数界面
 * */
class SelectStepNumView extends ui.jackaro.tip.selectNumUI implements BaseTipInterface {
    private layer: Laya.Box;
    private maxNum: number = 7;
    private maxStep: number = 0;//最大可以走多少步
    private minStep: number = 0;//至少得走多少步
    private notAllowMoveNumber: Array<number>;//服务器返回不可选择的步数
    private _curMoveChessID: number = 0;
    private operateTime: number = 0;
    private progressBarCommonView: ProgressBarCommonView;

    private isAddMaskAfterHide: boolean = false;
    constructor() {
        super();
        if (yalla.Font.isRight())
            for (let i = 0; i < this.maxNum; i++) {
                let btn = this[`btn${i}`] as Laya.Button;
                if (btn) { btn.x = this.maxNum - i }
            }
    }
    onBtnClick(moveStep: number, e: Laya.Event) {
        if (e) {
            e.stopPropagation();
        }
        this.hide();
        let curMoveChessPlayerIdx = JackaroGamePlayerManager.instance.getMoveChessPlayerIdx();
        JackaroChessManager.instance.setPokerSevenFirstMoveData({ idx: curMoveChessPlayerIdx, order: this._curMoveChessID, number: moveStep });
    }
    checkShowChessMask(): boolean {
        if (this.canShowChessMask()) {
            JackaroChessMaskViewManager.instance.show(this.getChessMaskParent());
        }
        return false;
    }
    canShowChessMask(): boolean {
        return false;
    }
    getChessMaskParent(): Laya.Sprite {
        return null;
    }
    show(params: any): void {
        //到了这个界面，不再显示选择圈特效
        JkChooseChessManager.ins.removeAll();
        var p = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);
        if (p && p.getTrustSystem()) {
            return;
        }
        if (this.layer && !this.layer.contains(this)) {
            this.layer.addChild(this);
        }
        this.visible = true;
        this.maxStep = params.maxStep;
        this.minStep = params.minStep || 0;
        this.isAddMaskAfterHide = params.isAddMaskAfterHide || false;
        this.notAllowMoveNumber = params.notAllowMoveNumber || [];
        this._curMoveChessID = params.chessID;
        this.operateTime = params.operateTime ? params.operateTime : 0;
        if (this.maxStep) {
            for (let i = 0; i < this.maxNum; i++) {
                let btn = this[`btn${i}`] as Laya.Button;
                if (btn) {
                    var moveStep = i + 1;
                    let isEnable = (moveStep < this.minStep || i > this.maxStep - 1);
                    if (!isEnable && this.notAllowMoveNumber.indexOf(moveStep) > -1) {
                        isEnable = true;
                    }
                    btn.disabled = isEnable;
                    btn.off(Laya.Event.CLICK, this, this.onBtnClick);
                    !isEnable && btn.on(Laya.Event.CLICK, this, this.onBtnClick, [moveStep]);
                }
            }
        }
        if (!this.progressBarCommonView) {
            this.progressBarCommonView = new ProgressBarCommonView();
            this.progress_box.addChild(this.progressBarCommonView);
        }
        this.progressBarCommonView.show(this.operateTime);

        let chooseChessOperateView = TipsViewManager.getInstance().getTipView(TipViewType.CHOOSE_CHESS_OPERATE);
        if (chooseChessOperateView) {
            chooseChessOperateView.getProgressBarCommonView().visible = false;
        }
    }
    public getProgressBarCommonView() {
        return this.progressBarCommonView;
    }
    hide(): void {
        TipsViewManager.getInstance().hideTip(TipViewType.No_Card_Touch);
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        this.visible = false;
        if (this.canShowChessMask()) {
            JackaroChessMaskViewManager.instance.hide(this.getChessMaskParent());
        }
    }
    close(): void {
        TipsViewManager.getInstance().hideTip(TipViewType.No_Card_Touch);
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        if (this.canShowChessMask()) {
            JackaroChessMaskViewManager.instance.close();
        }
        this.removeSelf();
    }
    setLayer(layer: Laya.Box): void {
        this.layer = layer;
    }
}