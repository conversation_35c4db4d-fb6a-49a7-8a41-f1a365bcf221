/***
 * 出牌 A,10 ,K 时,选择下一步操作的界面
 */
class SelectStepOperateView extends ui.jackaro.tip.SelectStepOperateUI implements BaseTipInterface {
    private layer: Laya.Box;
    private operateTime: number = 0;
    private pool: Array<Laya.Button> = [];
    private btnWidth: number = 212;
    private progressBarCommonView: ProgressBarCommonView;

    constructor() {
        super();
        //TODO::加上这段代码，maskBg点击才不会穿透。目前无解，先这么处理
        this.on(Laya.Event.CLICK, this, () => { });
    }

    /**
     * 
     *     MOVE_ONE = 1; //出牌A,选择走1步
     *     MOVE_TEN = 2; //出牌10,选择走10步
     *     MOVE_ELEVEN = 3; //出牌A,选择走11步
     *     MOVE_THIRTEEN = 4; //复杂模式,出牌K,选择走13步
     */
    private selectItemParams = {
        1: yalla.data.jackaro.SelectItemType.MOVE_ONE,
        10: yalla.data.jackaro.SelectItemType.MOVE_TEN,
        11: yalla.data.jackaro.SelectItemType.MOVE_ELEVEN,
        13: yalla.data.jackaro.SelectItemType.MOVE_THIRTEEN,
    };
    canShowChessMask(): boolean {
        return false;
    }
    getChessMaskParent(): Laya.Sprite {
        return null;
    }
    show(params: any): void {
        var player = JackaroGamePlayerManager.instance.getGamePlayerByIdx(yalla.Global.Account.idx);
        if (player && player.getTrustSystem()) {//这个判断建议放到show的时候
            return;
        }
        let poker = params.poker;
        let curOperateDefine: yalla.view.jackaro.CardOperateItemDefine[] = yalla.view.jackaro.CardOperateDefine.getCardOperateDefine(poker);
        //TODO::ui中加了maskBg 禁止穿透
        // TipsViewManager.getInstance().showTip(TipViewType.No_Card_Touch);
        if (curOperateDefine) {
            if (this.layer && !this.layer.contains(this)) {
                this.layer.addChild(this);
            }
            this.visible = true;
            this.clear();
            let operateCallBack = params.operateCallBack;
            var btns = []
            curOperateDefine.forEach((itemDefine, index: number) => {
                let curOperatorKey = itemDefine.operatorKey;
                let canChoose = curOperatorKey ? operateCallBack && operateCallBack[curOperatorKey] : true;
                var btn: Laya.Button = this.createChooseBtn(itemDefine);
                btn.offAll();
                btns.push(btn);
                let btnContent = btn.getChildByName("btnContent") as Laya.Label;
                let btnTitle = btn.getChildByName("btnTitle") as Laya.Label;
                let btnDescText = btn.getChildByName("btnDescText") as Laya.Label;
                if (canChoose) {
                    if (curOperatorKey) itemDefine.callback = () => {
                        if (itemDefine.operatorKey && parseInt(curOperatorKey) > 0) {
                            yalla.data.jackaro.JackaroUserService.instance.sendSelectItem(poker, this.selectItemParams[parseInt(itemDefine.operatorKey)]);
                        }
                        operateCallBack[curOperatorKey]();
                    }

                    btn.skin = itemDefine.btnSkin;

                    btn.on("click", this, (e: Laya.Event) => {
                        e.stopPropagation();
                        yalla.Sound.playSound("click");
                        itemDefine.callback();
                        this.hide();
                    });
                    btnTitle.color = "#F6FFB6";
                    btnTitle.strokeColor = "#126640";
                    btnTitle.stroke = 2;

                    btnContent.color = "#ffffff";
                    btnContent.strokeColor = "#0C5F29";
                    btnContent.stroke = 4;
                    btnContent.bold = true;

                    btnDescText.color = "#FFFFFF";
                } else {
                    btnTitle.color = "#C9C9C9";
                    btnTitle.stroke = 0;
                    btnTitle.strokeColor = "#ffffff";

                    btnContent.color = "#EDEDED";
                    btnContent.stroke = 0;
                    btnContent.bold = false;

                    btnDescText.color = "#ECECEC";

                    btn.skin = "jackaro/btn_gray.png";
                    btn.on("click", this, (e: Laya.Event) => {
                        e && e.stopPropagation();
                        //主要为了不响应点击播放声音
                    });
                }
            })
            this.sortPos(btns);
            this.operateTime = params.operateTime ? params.operateTime : 0;
            if (!this.progressBarCommonView) {
                this.progressBarCommonView = new ProgressBarCommonView();
                this.progress_box.addChild(this.progressBarCommonView);
            }
            this.progressBarCommonView.show(this.operateTime);
        }
    }
    sortPos(btns: Array<Laya.Button>) {
        if (btns) {
            let childNum = btns.length;
            let totalChildWidth = childNum * this.btnWidth;
            let gap = (this.operateHbox.width - totalChildWidth) / (childNum + 1);
            if (yalla.Font.isRight()) btns.reverse();
            btns.forEach((btn: Laya.Button, index: number) => {
                btn.x = (index + 1) * gap + index * this.btnWidth;
                this.operateHbox.addChild(btn);
            })
        }
    }
    createChooseBtn(itemDefine: yalla.view.jackaro.CardOperateItemDefine) {
        var boxBtn: Laya.Button;
        if (this.pool.length > 0) {
            boxBtn = this.pool.shift();
            boxBtn.skin = itemDefine.btnSkin;
            let btnTitle = boxBtn.getChildByName("btnTitle") as Laya.Label;
            let btnDescText = boxBtn.getChildByName("btnDescText") as Laya.Label;
            let btnDescImg = boxBtn.getChildByName("btnDescImg") as Laya.Image;
            let btnContent = boxBtn.getChildByName("btnContent") as Laya.Label;

            btnTitle.fontSize = itemDefine.titleFontSize;
            btnTitle.text = itemDefine.title;

            btnContent.fontSize = itemDefine.contentFontSize;
            btnContent.text = itemDefine.desc;

            btnDescImg.visible = false;
            btnDescText.visible = false;
            if (itemDefine.operateDesc.indexOf(".png") != -1) {
                btnDescImg.visible = true;
                btnDescImg.skin = itemDefine.operateDesc;
            } else {
                btnDescText.visible = true;
                btnDescText.text = itemDefine.operateDesc;
            }

        } else {
            boxBtn = new Laya.Button();
            boxBtn.size(this.btnWidth, 184);
            boxBtn.stateNum = 1;
            boxBtn.skin = itemDefine.btnSkin;

            //顶部标题
            let btnTitle = new Laya.Label();
            btnTitle.size(148, 23);
            btnTitle.fontSize = itemDefine.titleFontSize;
            // btnTitle.font = "Montserrat-Bold";
            btnTitle.align = "center";
            btnTitle.valign = "middle";
            btnTitle.name = "btnTitle";
            btnTitle.centerX = 0;
            if (itemDefine.titlePosYOffset) {
                btnTitle.y = 20 + itemDefine.titlePosYOffset;
            } else {
                btnTitle.y = 20;
            }
            btnTitle.color = "#F6FFB6";
            btnTitle.strokeColor = "#126640";
            btnTitle.stroke = 2;
            btnTitle.text = itemDefine.title;
            btnTitle.bold = true;
            boxBtn.addChild(btnTitle);

            //中间描述 文字或者图片
            //描述文本
            let btnDescText = new Laya.Label();
            btnDescText.size(148, 23);
            btnDescText.fontSize = 64;
            // btnTitle.font = "Montserrat-Bold";
            btnDescText.align = "center";
            btnDescText.valign = "middle";
            btnDescText.name = "btnDescText";
            btnDescText.centerX = 0;
            btnDescText.y = 74;
            btnDescText.color = "#FFFFFF";
            btnDescText.text = itemDefine.operateDesc;
            btnDescText.bold = true;
            boxBtn.addChild(btnDescText);
            btnDescText.visible = false;

            //描述图片
            let btnDescImg = new Laya.Image();
            // btnDescImg.size(148, 148);
            btnDescImg.centerX = 0;
            btnDescImg.y = 102.5;
            btnDescImg.anchorY = 0.5;
            btnDescImg.name = "btnDescImg";
            boxBtn.addChild(btnDescImg);
            btnDescImg.visible = false;

            if (itemDefine.operateDesc.indexOf(".png") != -1) {
                btnDescImg.visible = true;
                btnDescImg.skin = itemDefine.operateDesc;
            } else {
                btnDescText.visible = true;
                btnDescText.text = itemDefine.operateDesc;
            }

            let btnContent = new Laya.Label();
            btnContent.size(184, 23);
            btnContent.fontSize = itemDefine.contentFontSize;
            btnContent.align = "center";
            // btnContent.font = "Montserrat-Bold";
            btnContent.valign = "middle";
            btnContent.name = "btnContent";
            btnContent.color = "#ffffff";
            btnContent.strokeColor = "#0C5F29";
            btnContent.stroke = 4;
            btnContent.bold = true;
            btnContent.centerX = 0;
            btnContent.y = 138;
            btnContent.text = itemDefine.desc;
            boxBtn.addChild(btnContent);
        }
        return boxBtn;
    }
    public getProgressBarCommonView() {
        return this.progressBarCommonView;
    }

    clear() {
        this.operateHbox._childs && this.operateHbox._childs.forEach(node => {
            this.pool.push(node);
        })
        this.operateHbox.removeChildren();
    }
    hide(): void {
        TipsViewManager.getInstance().hideTip(TipViewType.No_Card_Touch);
        this.visible = false;
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        this.clear();
    }
    close(): void {
        TipsViewManager.getInstance().hideTip(TipViewType.No_Card_Touch);
        if (this.progressBarCommonView)
            this.progressBarCommonView.clear();
        this.removeSelf();
        this.pool = [];
    }
    setLayer(layer: Laya.Box): void {
        this.layer = layer;
    }
}
