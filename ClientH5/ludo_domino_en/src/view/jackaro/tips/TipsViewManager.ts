// 定义弹窗类型枚举
enum TipViewType {
    SELECT_STEP_NUM = 'SELECT_STEP_NUM',    // 选择移动步数数字1-7
    EXCHANGE_CHESS = 'EXCHANGE_CHESS', // 棋子互换
    RULE_CARD = 'RULE_CARD',      // 规则牌介绍
    DISCARD_CARD = 'DISCARD_CARD', // 弃牌界面
    DISCARD_ALL_CARD = 'DISCARD_ALL_CARD', // 弃牌界面
    SELECT_OPERATE = 'SELECT_OPERATE', // 选择操作界面
    CHOOSE_CHESS_OPERATE = 'CHOOSE_CHESS_OPERATE', // 选择棋子操作界面
    TRUST_SYSTEM = 'TRUST_SYSTEM', // 托管界面
    PICKUP_CARD = 'PICKUP_CARD', //抽牌界面
    GM_SELECT_CARD = 'GM_SELECT_CARD', //GM选择牌界面
    Discard_Self_ForceCard = 'Discard_Self_ForceCard',//被别人使用出牌10弃牌界面
    No_Chess_Touch = 'No_Chess_Touch',//点击遮挡层
    No_Card_Touch = 'No_Card_Touch',//点击遮挡层
}

// 定义层级映射
const TipViewLayer = {
    [TipViewType.SELECT_STEP_NUM]: 'tipLayer',
    [TipViewType.EXCHANGE_CHESS]: 'tipLayer',
    [TipViewType.RULE_CARD]: 'tipLayer',
    [TipViewType.DISCARD_CARD]: 'operateLayer',
    [TipViewType.DISCARD_ALL_CARD]: 'operateLayer',
    [TipViewType.SELECT_OPERATE]: 'tipLayer',
    [TipViewType.CHOOSE_CHESS_OPERATE]: 'tipLayer',
    [TipViewType.No_Chess_Touch]: 'tipLayer',
    [TipViewType.TRUST_SYSTEM]: 'trustLayer',
    [TipViewType.PICKUP_CARD]: 'tipLayer',
    [TipViewType.GM_SELECT_CARD]: 'tipLayer',
    [TipViewType.Discard_Self_ForceCard]: 'operateLayer',
    [TipViewType.No_Card_Touch]: 'handCardLayer',
} as const;

const TipsViewZorder = {
    // [TipViewType.TRUST_SYSTEM]: 992,
} as const;

class TipsViewManager {
    public static _instance: TipsViewManager;
    private currentView: BaseTipInterface | null = null;
    private viewMap: Record<TipViewType, BaseTipInterface> = {} as Record<TipViewType, BaseTipInterface>;

    private curGameView: Laya.View;

    // 单例模式
    public static getInstance(): TipsViewManager {
        if (!this._instance) {
            this._instance = new TipsViewManager();
        }
        return this._instance;
    }
    setCurGameView(view: Laya.View) {
        this.curGameView = view;
    }

    // 显示指定类型的弹窗
    public showTip(type: TipViewType, params?: any) {
        // 如果有正在显示的弹窗，先关闭
        // if (this.currentView && type != TipViewType.RULE_CARD) {
        //     this.currentView.hide();
        // }
        if (!this.curGameView || !this.curGameView.displayedInStage) {
            return;
        }
        // 获取或创建弹窗实例
        let view = this.viewMap[type];
        if (!view) {
            view = this.createView(type);
            if (!TipViewLayer[type]) { //没有定义层级，直接添加在游戏视图上
                view.setLayer(this.curGameView as unknown as Laya.Box);
            } else {
                view.setLayer(this.curGameView.getChildByName(TipViewLayer[type]) as Laya.Box);
            }
            if (TipsViewZorder[type]) {
                (view as unknown as Laya.View).zOrder = TipsViewZorder[type];
            }
            this.viewMap[type] = view;
        }

        // 显示新弹窗
        this.currentView = view;
        view.show(params);

        /**TODO：：这些页面出现，需要关闭（包括个人详情，游戏规则页面、游戏类弹框） ;托管状态下，也会执行操作tip显示逻辑，但是托管的话，view的visible不显示 */
        if (this.isSpecialTip(type) && this.isShow(type)) {
            yalla.util.ViewExclusion.instance.removeAll();
            yalla.Native.instance.closeAllNativeView();
            if (!yalla.Global.IsGameOver) yalla.util.closeAllDialog();
        }
    }
    public hideTip(type: TipViewType) {
        if (this.viewMap && this.viewMap[type]) {
            this.viewMap[type].hide();
        }
    }
    public isShow(type: TipViewType) {
        var curView = this.viewMap[type] as unknown as Laya.View;
        return curView && curView.displayedInStage && curView.visible;
    }
    public getTipView(type: TipViewType) {
        return this.viewMap[type];
    }
    /** 棋子选择，交换棋子，抽牌界面，选择扑克牌弃掉页面  出现的时候，此时触发托管，这些页面需要关闭 */
    public isSpecialTip(type) {
        // let isSpecial = [
        //                         TipViewType.DISCARD_CARD,
        //                         TipViewType.DISCARD_ALL_CARD,
        //                         TipViewType.PICKUP_CARD,
        //                         TipViewType.SELECT_STEP_NUM,
        //                         TipViewType.EXCHANGE_CHESS
        //                     ].indexOf(type) >= 0;
        // return isSpecial;
        if (type != TipViewType.TRUST_SYSTEM) return true;
    }
    // public isCanTouchCard() {
    //     if (this.isShow(TipViewType.SELECT_STEP_NUM) || this.isShow(TipViewType.EXCHANGE_CHESS) || this.isShow(TipViewType.CHOOSE_CHESS_OPERATE) || this.isShow(TipViewType.PICKUP_CARD) || this.isShow(TipViewType.SELECT_OPERATE)) {
    //         return false;
    //     }
    //     return true;
    // }

    private createView(type: TipViewType): BaseTipInterface {
        switch (type) {
            case TipViewType.SELECT_STEP_NUM:
                return new SelectStepNumView();
            case TipViewType.EXCHANGE_CHESS:
                return new ExChangeChessView();
            case TipViewType.RULE_CARD:
                return new RuleCardView();
            case TipViewType.DISCARD_CARD:
            case TipViewType.DISCARD_ALL_CARD:
                return new DiscardCardView();
            case TipViewType.SELECT_OPERATE:
                return new SelectStepOperateView();
            case TipViewType.CHOOSE_CHESS_OPERATE:
                return new ChooseChessOperateView();
            case TipViewType.TRUST_SYSTEM:
                return new TrustSystemView();
            case TipViewType.PICKUP_CARD:
                return new PickUpCardView();
            case TipViewType.GM_SELECT_CARD:
                return new GmSelectCard();
            case TipViewType.Discard_Self_ForceCard:
                return new DiscardSelfForceCard();
            case TipViewType.No_Chess_Touch:
                return new ChooseChessNoTouchOperateView();
            case TipViewType.No_Card_Touch:
                return new ChooseCardNoTouchOperateView();
            default:
                throw new Error(`Unknown tip view type: ${type}`);
        }
    }
    /**
     * 回合玩家改变时，需要清理弹窗
     */
    public checkCloseTipsAfterChangePlayer(idx: number) {
        if (idx != yalla.Global.Account.idx) {
            this.hideTip(TipViewType.Discard_Self_ForceCard);
        }
    }
    /**
     * 每一个小回合清理（轮流一遍又轮到自己）
     * 参数，每轮回合，如果操作者 是自己 牌规则弹窗也要关掉，否则不用关掉
     */
    turnClear(curOperatePlayerIdx: number = 0) {
        let isSelfTurn = false;
        if (curOperatePlayerIdx == yalla.Global.Account.idx) {
            isSelfTurn = true;
        }
        for (let key in this.viewMap) {
            if (this.viewMap[key] instanceof TrustSystemView || this.viewMap[key] instanceof GmSelectCard || this.viewMap[key] instanceof DiscardSelfForceCard) {
                continue;
            }
            if (this.viewMap[key] instanceof RuleCardView && !isSelfTurn) {
                continue;
            }
            this.viewMap[key].hide();
        }
    }
    /**
     * 此方法用于game 类里面 ，handleSyncGame 同步时，清理所有弹窗，如果只是断线，不清理托管UI，重启服务器之类的需要清理托管UI
     */
    hideAll(isClearTrustUI: boolean = false) {
        for (let key in this.viewMap) {
            if (isClearTrustUI && this.viewMap[key] instanceof TrustSystemView) {
                continue;
            }
            this.viewMap[key].hide();
        }
    }
    public addWaitCdTime(extendTime?: number) {
        let addTime: number = extendTime ? extendTime : yalla.data.jackaro.JackaroUserService.instance.addOperateTime;
        for (let key in this.viewMap) {
            if (this.viewMap[key].getProgressBarCommonView()) {
                this.viewMap[key].getProgressBarCommonView().addTime(addTime);
            }
        }
    }
    clear() {
        this.currentView = null;
        for (let key in this.viewMap) {
            this.viewMap[key] && this.viewMap[key].hide();
            this.viewMap[key] && this.viewMap[key].close();
        }
        this.viewMap = {} as Record<TipViewType, BaseTipInterface>;
        TipsViewManager._instance = null;
    }
}

