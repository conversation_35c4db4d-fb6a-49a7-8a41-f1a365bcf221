/**
 * 托管界面
 */
class TrustSystemView extends ui.jackaro.tip.TrustSystemUI implements BaseTipInterface {
    private layer: Laya.Box;
    private isSendRequest: boolean = false;
    constructor() {
        super();
        if (yalla.Font.isRight()) {
            //交换一下位置
            this.auto.x = 1;
            this.autoRatio.x = 0;
            //阿语文案说是此文案不居中，特意手动调整宽度，居中此文案
            if (yalla.Native.instance.deviceType == DeviceType.IOS) {
                this.auto.width = 320;
                // this.auto.align = "left";
                this.autoRatio.width = 70;
            } else {
                this.auto.width = 280;
                // this.auto.align = "left";
                this.autoRatio.width = 70;
            }

            this.autoRatio.align = "right";
            this.iconAuto.skin = "jackaro/AUTO_ar.png"
            this.label_content.width = 1000;
            this.label_content.stroke = 0;
            this.label_content.strokeColor = "";
        }
    }
    canShowChessMask(): boolean {
        return false;
    }
    getChessMaskParent(): Laya.Sprite {
        return null;
    }
    show(): void {
        if (this.layer && !this.layer.contains(this)) {
            this.layer.addChild(this);
        }
        this.updateView();
        this.btnTrust.off(Laya.Event.CLICK, this, this.onTrustClick);
        this.btnTrust.on(Laya.Event.CLICK, this, this.onTrustClick);
        this.visible = true;
    }
    public updateView() {
        let room = yalla.data.jackaro.JackaroUserService.instance.room;
        if (room.isAllTrustConfig()) {//同时开启惩罚和不加经验
            this.label_content.text = "No EXP in AUTO mode. You will be kicked out due to prolonged use of AUTO mode.";
            this.autoRatio.text = `${room.consecutiveTrustCount}/${room.allowConsecutiveTrustMax}`;
            this.autoBox.visible = true;
        } else if (room.ignoreTrustExperience) {//只开启不加经验
            this.label_content.text = "No EXP in AUTO mode.";
            this.autoBox.visible = false;
        } else if (room.punishTrust) {//只开启惩罚
            this.label_content.text = "You will be kicked out due to prolonged use of AUTO mode.";
            this.autoRatio.text = `${room.consecutiveTrustCount}/${room.allowConsecutiveTrustMax}`;
            this.autoBox.visible = true;
        } else {
            this.label_content.text = "The game has entered AUTO mode.";
            this.autoBox.visible = false;
        }
    }

    private onTrustClick(): void {
        if (!this.isSendRequest) {
            this.isSendRequest = true;
            // this.hide();
            yalla.data.jackaro.JackaroUserService.instance.sendCancelTrust();
        }
    }
    public getProgressBarCommonView() {
        return null;
    }
    hide(): void {
        this.visible = false;
        this.isSendRequest = false;
    }
    close(): void {
        this.removeSelf();
    }
    setLayer(layer: Laya.Box): void {
        this.layer = layer;
    }
}
