class ToastManager {
    private static _currentToast: ToastView[] = [];
    private static _toastContainer: Laya.Box;
    private static _isShow: boolean;

    static initialize(toastContainer: Laya.Box): void {
        this._toastContainer = toastContainer;
    }

    static showToast(content: string): void {
        if (!ToastManager._toastContainer) return;
        if (this._isShow) return;
        // 获取一个提示UI实例
        const toast = ToastPool.get(content);

        // 计算提示UI的起始位置，避免重叠
        // let offsetY: number = 0;
        // for (const currentTip of ToastManager._currentToast) {
        //     offsetY = Math.max(offsetY, currentTip.y - toast.height - 10);
        // }
        // toast.y = offsetY;

        // 添加到容器中
        ToastManager._toastContainer.addChild(toast);
        ToastManager._currentToast.push(toast);

        // 开始移动动画
        // Laya.Tween.to(toast, { y: toast.y - 350 }, 2500, Laya.Ease.quadOut, Laya.Handler.create(this, () => {
        //     ToastManager.onTipComplete(toast);
        // }));
        Laya.timer.once(1500, this, () => {
            ToastManager.onTipComplete(toast);
        });
        this._isShow = true;
    }

    private static onTipComplete(toast: ToastView): void {
        // 从容器中移除，并回收到对象池
        ToastManager._toastContainer.removeChild(toast);
        const index = ToastManager._currentToast.indexOf(toast);
        if (index !== -1) {
            ToastManager._currentToast.splice(index, 1);
        }
        ToastPool.recover(toast);
        this._isShow = false;
    }
    public static clear() {
        for (let index = 0; index < ToastManager._currentToast.length; index++) {
            const element = ToastManager._currentToast[index];
            // Laya.Tween.clearAll(element);
            Laya.timer.clearAll(element);
            ToastManager._toastContainer.removeChild(element);
        }
        ToastManager._currentToast = [];
        ToastPool.clear();
        ToastManager._isShow = false;
    }
}
class ToastPool {
    private static _pool: ToastView[] = [];

    public static get(content: string): ToastView {
        if (this._pool.length > 0) {
            const toast = this._pool.pop()!;
            toast.show(content);
            return toast;
        } else {
            let toastView = new ToastView();
            toastView.show(content);
            return toastView;
        }
    }

    public static recover(toast: ToastView): void {
        this._pool.push(toast);
    }
    public static clear() {
        this._pool = [];
    }
}