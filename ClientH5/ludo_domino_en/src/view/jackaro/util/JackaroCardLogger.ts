/**
 * JackaroCard游戏日志管理器
 * 提供统一的日志记录功能，并支持各模块的日志单独开关控制
 */
class JackaroCardLogger {
    /**
     * 单例实例
     */
    private static _instance: JackaroCardLogger;

    /**
     * 获取单例实例
     */
    public static get instance(): JackaroCardLogger {
        return this._instance || (this._instance = new JackaroCardLogger());
    }

    /**
     * 各模块日志开关配置
     */
    private _logConfig: {
        // 卡牌全局日志开关
        global: boolean,
        // 卡牌管理器日志开关
        cardManager: boolean,
        // 手牌视图日志开关
        handCardView: boolean,
        // 卡牌项日志开关
        cardItem: {
            // 基本日志
            basic: boolean,
            // 详细日志
            verbose: boolean
        },
        // 补牌管理器日志开关
        cardReplenish: boolean
    } = {
            global: false,
            cardManager: true,
            handCardView: true,
            cardItem: {
                basic: false,
                verbose: true
            },
            cardReplenish: true
        };

    /**
     * 构造函数
     */
    constructor() {
        // 初始化日志配置
        this.initConfig();
    }

    /**
     * 初始化日志配置
     * 可以从本地存储或其他配置源加载设置
     */
    private initConfig(): void {
        // 在这里可以从本地存储读取配置
        // 暂时使用默认配置
    }

    /**
     * 设置日志配置
     * @param module 模块名称
     * @param enabled 是否启用
     * @param verboseEnabled 是否启用详细日志（仅对cardItem有效）
     */
    public setLogEnabled(module: string, enabled: boolean, verboseEnabled?: boolean): void {
        if (module === 'global') {
            this._logConfig.global = enabled;
        } else if (module === 'cardManager') {
            this._logConfig.cardManager = enabled;
        } else if (module === 'handCardView') {
            this._logConfig.handCardView = enabled;
        } else if (module === 'cardItem') {
            this._logConfig.cardItem.basic = enabled;
            if (verboseEnabled !== undefined) {
                this._logConfig.cardItem.verbose = verboseEnabled;
            }
        } else if (module === 'cardReplenish') {
            this._logConfig.cardReplenish = enabled;
        } else if (module === 'all') {
            this._logConfig.cardManager = enabled;
            this._logConfig.handCardView = enabled;
            this._logConfig.cardItem.basic = enabled;
            this._logConfig.cardReplenish = enabled;
            if (verboseEnabled !== undefined) {
                this._logConfig.cardItem.verbose = verboseEnabled;
            }
        }
    }

    /**
     * 检查指定模块的日志是否启用
     * @param module 模块名称
     * @param isVerbose 是否是详细日志（仅对cardItem有效）
     * @returns 是否启用
     */
    public isLogEnabled(module: string, isVerbose: boolean = false): boolean {
        if (module === 'global') {
            return this._logConfig.global;
        } else if (module === 'cardManager') {
            return this._logConfig.cardManager;
        } else if (module === 'handCardView') {
            return this._logConfig.handCardView;
        } else if (module === 'cardItem') {
            return isVerbose ?
                (this._logConfig.cardItem.basic && this._logConfig.cardItem.verbose) :
                this._logConfig.cardItem.basic;
        } else if (module === 'cardReplenish') {
            return this._logConfig.cardReplenish;
        }
        return false;
    }

    /**
     * 记录普通日志
     * @param module 模块名称
     * @param tag 日志标签
     * @param message 日志消息
     */
    public log(module: string, tag: string, message: string): void {
        if (!this.isLogEnabled('global')) return;
        if (!this.isLogEnabled(module)) return;
        yalla.Debug.yum(`[${module}] ${tag}: ${message}`);
    }

    /**
     * 记录详细日志
     * @param module 模块名称
     * @param tag 日志标签
     * @param message 日志消息
     * @param data 额外数据
     */
    public logMore(module: string, tag: string, message: string, data?: any): void {
        if (!this.isLogEnabled('global')) return;
        if (!this.isLogEnabled(module)) return;

        let logMessage = `[${module}] ${tag}: ${message}`;

        // 如果有附加数据，尝试将其添加到日志中
        if (data !== undefined) {
            try {
                const dataStr = typeof data === 'object' ? JSON.stringify(data) : String(data);
                logMessage += ` - ${dataStr}`;
            } catch (e) {
                logMessage += ' - [无法序列化的数据]';
            }
        }

        yalla.Debug.yum(logMessage);
    }

    /**
     * 记录详细日志（仅当启用详细日志时）
     * @param module 模块名称
     * @param tag 日志标签
     * @param message 日志消息
     * @param data 额外数据
     */
    public logVerbose(module: string, tag: string, message: string, data?: any): void {
        if (!this.isLogEnabled('global')) return;
        if (!this.isLogEnabled(module, true)) return;

        let logMessage = `[${module}] ${tag}: ${message}`;

        // 如果有附加数据，尝试将其添加到日志中
        if (data !== undefined) {
            try {
                const dataStr = typeof data === 'object' ? JSON.stringify(data) : String(data);
                logMessage += ` - ${dataStr}`;
            } catch (e) {
                logMessage += ' - [无法序列化的数据]';
            }
        }

        yalla.Debug.yum(logMessage);
    }

    /**
     * 记录错误日志
     * @param module 模块名称
     * @param tag 日志标签
     * @param message 日志消息
     * @param error 错误对象
     */
    public logError(module: string, tag: string, message: string, error?: any): void {
        if (!this.isLogEnabled('global')) return;
        // 错误日志总是输出，不受开关控制
        let logMessage = `[${module}] ${tag} ERROR: ${message}`;

        if (error) {
            try {
                if (error instanceof Error) {
                    logMessage += ` - ${error.message}\n${error.stack}`;
                } else {
                    logMessage += ` - ${JSON.stringify(error)}`;
                }
            } catch (e) {
                logMessage += ' - [无法序列化的错误对象]';
            }
        }

        yalla.Debug.log(logMessage);
        console.error(logMessage);
    }

    /**
     * 记录过滤日志
     * @param module 模块名称
     * @param tag 日志标签
     * @param message 日志消息
     */
    public filterLogMore(module: string, tag: string, message: string): void {
        if (!this.isLogEnabled('global')) return;
        if (!this.isLogEnabled(module)) return;
        yalla.Debug.filterLogMore(`[${module}] ${tag}: ${message}`);
    }

    // ==================== 补牌管理器专用日志方法 ====================

    /**
     * 补牌管理器带时间戳的日志
     * @param message 日志消息
     * @param args 额外参数
     */
    public logWithTime(message: string, ...args: any[]): void {
        if (!this.isLogEnabled('global')) return;
        if (!this.isLogEnabled('cardReplenish')) return;
        yalla.Debug.yum(`[抽牌奖励] [${yalla.getTimeHMS()}] ${message}`, ...args);
    }

    /**
     * 补牌管理器带时间戳的错误日志
     * @param message 错误消息
     * @param args 额外参数
     */
    public errorWithTime(message: string, ...args: any[]): void {
        if (!this.isLogEnabled('global')) return;
        // 错误日志总是输出，不受模块开关控制
        yalla.Debug.log(`[抽牌奖励] [${yalla.getTimeHMS()}] ${message}`, ...args);
    }
}