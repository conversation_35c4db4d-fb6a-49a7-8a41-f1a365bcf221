/**
 * 手牌监控系统
 * 用于收集玩家手牌数量不一致问题的详细日志
 */

/**
 * 手牌监控数据接口
 */
interface HandCardMonitorData {
    /** 记录生成时间戳 */
    timestamp: number;
    /** 记录生成时间字符串 */
    timeString: string;
    /** 当前操作玩家索引 */
    currentPlayerIdx: number;
    /** 当前操作玩家昵称 */
    currentPlayerName: string;
    /** 当前出的牌 */
    playedPoker: yalla.data.jackaro.Poker;
    /** 当前出的牌中文名称 */
    playedPokerName: string;
    /** 出牌是否成功 */
    playSuccess: boolean;
    /** 当前玩家的手牌数量 */
    currentPlayerHandCardCount: number;
    /** 所有玩家的手牌数量映射 */
    allPlayersHandCardCount: Record<number, number>;
    /** 当前玩家的具体手牌内容（仅自己可见） */
    selfHandCards: Array<yalla.data.jackaro.Poker>;
    /** 当前玩家的具体手牌中文名称（仅自己可见） */
    selfHandCardsNames: Array<string>;
    /** 游戏状态信息 */
    gameState: {
        /** 当前回合 */
        currentRound: number;
        /** 是否移动棋子 */
        isMoveChess: boolean;
        /** 是否验证 */
        isValidate: boolean;
    };
    /**房间id */
    roomId: number;
}

interface changePlayerHandCardMonitorData {
    /** 记录生成时间字符串 */
    timeString: string;
    /** 当前操作玩家索引 */
    currentPlayerIdx: number;
    /** 当前操作玩家昵称 */
    currentPlayerName: string;
    /** 允许出的牌 */
    allowPokerData: Array<yalla.data.jackaro.Poker>;
    /** 允许出的牌中文名称 */
    allowPokerDataChinese: Array<string>;
    /** 房间id */
    roomId: number;
}

/**
 * 手牌监控系统类
 */
class JackaroHandCardMonitor {
    /**
     * 单例实例
     */
    private static _instance: JackaroHandCardMonitor;

    /**
     * 获取单例实例
     */
    public static get instance(): JackaroHandCardMonitor {
        return this._instance || (this._instance = new JackaroHandCardMonitor());
    }

    /**
     * 监控开关
     */
    private _monitorEnabled: boolean = true;

    /**
     * 日志标签
     */
    private _logTag: string = "JackaroHandCardMonitor";

    /**
     * 房间id
     */
    private _roomId: number = 0;

    /**
     * 构造函数
     */
    constructor() {
        // 初始化监控系统
        this.init();
    }
    private init(): void {
        let room = yalla.data.jackaro.JackaroUserService.instance.room;
        let roomId: number = 0;
        if (room) {
            roomId = room.roomId;
        }
        this._roomId = roomId;
    }

    /**
     * 设置监控开关
     * @param enabled 是否启用监控
     */
    public setMonitorEnabled(enabled: boolean): void {
        this._monitorEnabled = enabled;
        JackaroCardLogger.instance.log('handCardMonitor', this._logTag, `监控系统${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 检查监控是否启用
     * @returns 是否启用
     */
    public isMonitorEnabled(): boolean {
        return this._monitorEnabled;
    }

    /**
     * 记录手牌监控数据
     * @param playerIdx 当前操作玩家索引
     * @param poker 出的牌
     * @param isMoveChess 是否移动棋子
     * @param isValidate 是否验证
     * @param playSuccess 出牌是否成功
     * @param cardManager 卡牌管理器实例
     */
    public recordHandCardData(
        playerIdx: number,
        poker: yalla.data.jackaro.Poker,
        isMoveChess: boolean,
        isValidate: boolean,
        playSuccess: boolean,
        cardManager: any
    ): void {
        if (!this._monitorEnabled) {
            return;
        }

        try {
            // 获取当前时间
            const now = Date.now();
            const timeString = new Date(now).toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZone: 'Asia/Shanghai'
            });

            // 获取玩家信息
            const playerInfo = JackaroGamePlayerManager.instance.getPlayerByIdx(playerIdx);
            const playerName = playerInfo ? playerInfo.nickName : `未知玩家_${playerIdx}`;

            // 获取出牌信息
            const playedPokerName = yalla.data.jackaro.PokerChinesDefine[poker] || `未知牌_${poker}`;

            // 获取所有玩家的手牌数量
            const allPlayersHandCardCount: Record<number, number> = {};

            let playerPool = JackaroGamePlayerManager.instance.getPlayerPool();
            if (playerPool) {
                // 遍历所有玩家获取手牌数量
                for (const key in playerPool) {
                    if (!playerPool.hasOwnProperty(key)) {
                        continue;
                    }
                    const idx = playerPool[key].idx;
                    const handCardCount = cardManager.getHandCardItemNum(idx);
                    if (handCardCount !== undefined) {
                        allPlayersHandCardCount[idx] = handCardCount;
                    }
                }
            }

            // 获取自己的具体手牌（仅当是自己时）
            let selfHandCards: Array<yalla.data.jackaro.Poker> = [];
            let selfHandCardsNames: Array<string> = [];

            if (playerIdx === yalla.Global.Account.idx && cardManager.playerHardCard[playerIdx]) {
                selfHandCards = cardManager.playerHardCard[playerIdx].map((cardItem: any) => cardItem.data.poker);
                selfHandCardsNames = selfHandCards.map(poker => yalla.data.jackaro.PokerChinesDefine[poker] || `未知牌_${poker}`);
            }

            // 构建监控数据
            const monitorData: HandCardMonitorData = {
                timestamp: now,
                timeString: timeString,
                currentPlayerIdx: playerIdx,
                currentPlayerName: playerName,
                playedPoker: poker,
                playedPokerName: playedPokerName,
                playSuccess: playSuccess,
                currentPlayerHandCardCount: allPlayersHandCardCount[playerIdx] || 0,
                allPlayersHandCardCount: allPlayersHandCardCount,
                selfHandCards: selfHandCards,
                selfHandCardsNames: selfHandCardsNames,
                gameState: {
                    currentRound: cardManager.sendPokerRound || 0,
                    isMoveChess: isMoveChess,
                    isValidate: isValidate
                },
                roomId: this._roomId
            };

            // 记录监控日志
            this.logMonitorData(monitorData);

        } catch (error) {
            JackaroCardLogger.instance.logError('handCardMonitor', this._logTag, '记录手牌监控数据时发生错误', error);
        }
    }

    /**
     * 记录监控数据到日志
     * @param data 监控数据
     */
    private logMonitorData(data: HandCardMonitorData): void {
        // 构建详细的日志信息
        const logMessage = `\n========== 手牌监控记录 ==========\n` +
            `时间: ${data.timeString}\n` +
            `操作玩家: ${data.currentPlayerName} (idx: ${data.currentPlayerIdx})\n` +
            `游戏房间: ${data.roomId}\n` +
            `出牌: ${data.playedPokerName} (${data.playedPoker})\n` +
            `出牌成功: ${data.playSuccess ? '是' : '否'}\n` +
            `当前玩家手牌数量: ${data.currentPlayerHandCardCount}\n` +
            `所有玩家手牌数量: ${JSON.stringify(data.allPlayersHandCardCount)}\n` +
            `游戏状态: 回合${data.gameState.currentRound}, 移动棋子:${data.gameState.isMoveChess}, 验证:${data.gameState.isValidate}\n` +
            (data.selfHandCards.length > 0 ?
                `自己手牌详情: [${data.selfHandCardsNames.join(', ')}]\n` :
                `自己手牌详情: 非本人操作\n`) +
            `================================`;

        // 使用专门的监控日志记录
        JackaroCardLogger.instance.logMore('handCardMonitor', this._logTag, logMessage);
        yalla.Native.instance.writeGameLog("jackarologMonitorData", { logMessage });

        // 同时记录结构化数据（便于后续分析）
        JackaroCardLogger.instance.logMore('handCardMonitor', this._logTag + '_STRUCTURED', JSON.stringify(data));
    }

    /**
     * 记录手牌数量不一致的特殊情况
     * @param description 描述信息
     * @param expectedCount 期望的手牌数量
     * @param actualCount 实际的手牌数量
     * @param playerIdx 玩家索引
     */
    public recordHandCardMismatch(
        description: string,
        expectedCount: number,
        actualCount: number,
        playerIdx: number
    ): void {
        if (!this._monitorEnabled) {
            return;
        }

        const playerInfo = JackaroGamePlayerManager.instance.getPlayerByIdx(playerIdx);
        const playerName = playerInfo ? playerInfo.nickName : `未知玩家_${playerIdx}`;
        const timeString = new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZone: 'Asia/Shanghai'
        });

        const mismatchLog = `\n!!!!!! 手牌数量不一致警告 !!!!!!\n` +
            `时间: ${timeString}\n` +
            `玩家: ${playerName} (idx: ${playerIdx})\n` +
            `房间: ${this._roomId}\n` +
            `描述: ${description}\n` +
            `期望数量: ${expectedCount}\n` +
            `实际数量: ${actualCount}\n` +
            `差异: ${actualCount - expectedCount}\n` +
            `!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!`;

        JackaroCardLogger.instance.logError('handCardMonitor', this._logTag + '_MISMATCH', mismatchLog);
        yalla.Native.instance.writeGameLog("jackaroRecordHandCardMismatch", { mismatchLog });
    }
    /**
     * 记录切换玩家时服务器返回当前玩家可以出的手牌
     * @param playerIdx 当前操作玩家索引
     */
    public recordChangePlayerHandCardInfo(playerIdx: number, allowPokerData: Array<yalla.data.jackaro.Poker>): void {
        if (!this._monitorEnabled) {
            return;
        }
        if (!playerIdx) {
            return;
        }
        if (!allowPokerData) {
            return;
        }
        //收到changePlayer 消息的时间
        const now = new Date();
        // 构建监控数据
        const playerInfo = JackaroGamePlayerManager.instance.getPlayerByIdx(playerIdx);
        const playerName = playerInfo ? playerInfo.nickName : `未知玩家_${playerIdx}`;
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            timeZone: 'Asia/Shanghai'
        });
        let allowPokerDataChinese = [];
        for (let i = 0; i < allowPokerData.length; i++) {
            const poker = allowPokerData[i];
            const pokerName = yalla.data.jackaro.PokerChinesDefine[poker] || `未知牌_${poker}`;
            allowPokerDataChinese.push(pokerName);
        }
        const monitorData: changePlayerHandCardMonitorData = {
            timeString: timeString,
            currentPlayerIdx: playerIdx,
            currentPlayerName: playerName,
            allowPokerData: allowPokerData,
            allowPokerDataChinese: allowPokerDataChinese,
            roomId: this._roomId
        };

        // 使用专门的监控日志记录
        const mismatchLog = `\n!!!!!! 切换玩家时服务器返回当前玩家可以出的手牌!!!!!!\n` +
            `时间: ${timeString}\n` +
            `玩家: ${playerName} (idx: ${playerIdx})\n` +
            `房间: ${this._roomId}\n` +
            `允许出的牌: ${allowPokerDataChinese.join(', ')}\n` +
            `!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!`;

        JackaroCardLogger.instance.logError('changePlayerHandCard', this._logTag + '_MISMATCH', mismatchLog);
        yalla.Native.instance.writeGameLog("jackaroRecordHandCardMismatch", { mismatchLog });
    }

    /**
     * 获取监控统计信息
     * @returns 统计信息
     */
    public getMonitorStats(): string {
        return `手牌监控系统状态: ${this._monitorEnabled ? '启用' : '禁用'}`;
    }
}