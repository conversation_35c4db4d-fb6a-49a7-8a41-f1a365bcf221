module yalla {

    //提示开关
    // 0:关闭，1：开启
    export class JackaroTipsShow {
        static _tipsShowSetting_cache: boolean = true
        static set tipsShowSetting_cache(value: boolean) {
            var isOpen = Boolean(value);
            Laya.LocalStorage.setJSON("tipsShow_setting_cache", { isOpen });
            this._tipsShowSetting_cache = isOpen;
        }
        static isOpen(): boolean {
            return this._tipsShowSetting_cache;
        }
        static init() {
            var cache = Laya.LocalStorage.getJSON("tipsShow_setting_cache");
            if (cache) {
                this._tipsShowSetting_cache = cache.isOpen;
            }
        }
    }
}