
class LoopTime extends Laya.Timer {
    private startTime: number;
    private onUpdated: <PERSON><PERSON><PERSON>;
    private onComplete: <PERSON><PERSON><PERSON>;
    private duration: number;
    constructor() { super(); }
    public start(duration: number, delay: number, onUpdated: <PERSON><PERSON><PERSON>, onComplete: <PERSON><PERSON>.<PERSON>): void {
        this.duration = duration;
        this.onUpdated = onUpdated;
        this.onComplete = onComplete;
        if (delay) this.once(delay, this, this.startLoop);
        else this.startLoop();
    }
    private startLoop() {
        yalla.Sound.playSound("stone_transform");
        this.startTime = Date.now();
        this.frameLoop(1, this, this.update);
    }
    private update(): void {
        var t = this.getT();
        if (t >= 1) {
            if (this.onComplete) {
                this.onComplete.run();
            }
            this.stop();
        }
        if (this.onUpdated) {
            var pro = 1 - Math.pow(1 - t, 2.6);
            this.onUpdated.runWith(pro);
        }
    }
    public stop(): void {
        this.duration = 0;
        this.onUpdated = null;
        this.onComplete = null;
        this.startTime = 0;
        this.clearAll(this)
    }
    private getDelta(): number {
        return Date.now() - this.startTime;
    }
    private getT(): number {
        return this.getDelta() / this.duration;
    }
}