/** 二次贝塞尔曲线 */
class QuadraticBezier {
    constructor(
        private p0: port, // 起点
        private p1: port, // 控制点
        private p2: port  // 终点
    ) { }

    /**
     * 计算曲线上指定参数t的点坐标
     * @param t 参数值，范围[0,1]
     */
    getPointAt(t: number): port {
        if (t < 0 || t > 1) t = 1;
        const mt = 1 - t;
        const mt2 = mt * mt;
        const t2 = t * t;
        return {
            x: mt2 * this.p0.x + 2 * mt * t * this.p1.x + t2 * this.p2.x,
            y: mt2 * this.p0.y + 2 * mt * t * this.p1.y + t2 * this.p2.y
        };
    }

    /**
     * 计算曲线上指定参数t的切线向量
     * @param t 参数值，范围[0,1]
     */
    getTangentVectorAt(t: number): port {
        if (t < 0 || t > 1) t = 1;
        const mt = 1 - t;
        return {
            x: 2 * mt * (this.p1.x - this.p0.x) + 2 * t * (this.p2.x - this.p1.x),
            y: 2 * mt * (this.p1.y - this.p0.y) + 2 * t * (this.p2.y - this.p1.y)
        };
    }

    /**
     * 计算曲线上指定参数t的旋转角度（弧度）
     * @param t 参数值，范围[0,1]
     */
    getRotationAt(t: number): number {
        const tangent = this.getTangentVectorAt(t);
        return Math.atan2(tangent.y, tangent.x);
    }

    /**
     * 均匀采样曲线上的点，包含坐标和旋转角度
     * @param count 采样点数量（包括起点和终点）
     */
    samplePathPoints(count: number): port[] {
        if (count < 2) throw new Error("采样点数量至少为2");
        const points: port[] = [];
        for (let i = 0; i < count; i++) {
            const t = i / (count - 1);
            // points.push({
            //     position: this.getPointAt(t),
            //     rotation: this.getRotationAt(t) * 180 / Math.PI
            // });
            points.push(this.getPointAt(t));
        }
        return points;
    }
}