class LudoMain extends BaseMain {
    private outTime: number = 15000;
    private lastTime: number = null;
    private isReady: boolean = false;
    private msgStack: Array<any> = [];
    private sockteStatus: number = 0;
    constructor(type: number = 0) {
        super();
        Laya.stage.bgColor = "#312445";
        this.reset();
        this.initGameView(type);
        // this.addNetStateEvent();
    }
    initGameView(type: number = 0) {
        yalla.Global.gameType = 10019;
        switch (type) {
            case 0:
                this._gameView = new Game();
                break;
            case 1:
                this._gameView = new OffGame();
                // this._gameView = new NoviceGame();
                // this._gameView.init(10019, "https://img2.baidu.com/it/u=2328204195,3953168763&fm=26&fmt=auto&gp=0.jpg", "yalla");
                break;
            case 2:
                this._gameView = new NoviceGame();
                break;
            case 3:
                this._gameView = new JungleNovice();
                break;
            default:
                break;
        }
        this._gameView.name = 'gameView';
        Laya.stage.addChild(this._gameView);
    }
    public updateMoney() {
        if (this.gameView instanceof Game) {
            this.gameView.update();
        }
    }
    private reset() {
        Audience._instance = null;
    }
    get gameView(): Game {
        return this._gameView;
    }
    get offGameView(): OffGame {
        return this._gameView;
    }
    get noviceGameView(): NoviceGame {
        return this._gameView;
    }
    public onLogin() {
        yalla.Debug.log("LudoMain onLogin isconnect:" + yalla.Global.isconnect);
        if (!this._gameView) return;
        Laya.timer.clear(this, this.heartBeat);
        // if (yalla.util.IsBrowser()) {
        // this.lastTime = new Date().getTime();
        // if(yalla.util.IsBrowser()) Laya.timer.loop(yalla.Global.heart_interval, this, this.heartBeat);
        // }
        this.lastTime = new Date().getTime();
        Laya.timer.loop(yalla.Global.heart_interval, this, this.heartBeat);

        // Laya.timer.once(4000, this, () => {
        //     if (ludoRoom.instance.firstLogin) {
        //         yalla.Debug.log("===登陆 closesocket===");
        //         this.closeSocket();
        //     }
        // })

        ludo.GameSocket.clear();
        // ludoRoom._instance = null;//before 1.2.7
        ludoRoom.instance.clear();//1.2.7change
        ludoRoom.instance.ID = yalla.Global.Account.roomid;
        ludoRoom.instance.firstLogin = true;
        this.addListener();
        // this.connectByUrl(yalla.Global.Account.host + ":" + yalla.Global.Account.port);
        if (yalla.util.IsBrowser()) {
            if (!yalla.Global.Account.ip || yalla.Global.Account.ip.length < 1) {
                if (yalla.Native.instance.deviceType != DeviceType.Android) {
                    this.connectByUrl(yalla.Global.Account.host + ":" + yalla.Global.Account.port);
                } else {
                    yalla.Native.instance.getHostIp(yalla.Global.Account.host, (ip) => {
                        if (!ip || ip.length < 1) {
                            yalla.common.connect.ReconnectControl.instance.connect();
                            // this.connectByUrl(yalla.Global.Account.host + ":" + yalla.Global.Account.port);
                        } else {
                            yalla.Global.Account.ip = 'ws://' + ip;
                            this.connectByUrl(`${yalla.Global.Account.ip}:${yalla.Global.Account.port}`);
                        }
                    });
                }
            } else {
                if (yalla.Global.Account.port)
                    this.connectByUrl(`${yalla.Global.Account.ip}:${yalla.Global.Account.port}`);
                else
                    this.connectByUrl(`${yalla.Global.Account.ip}`);
            }
            // url = "ws://wsstest.yalla.games:9037";//测试服浏览器调试
            // url = "ws://*************:9013";//德建本地浏览器调试
            // url = "ws://************:9024";//肖山本地浏览器调试 
        }
    }
    private connectByUrl(url: string) {
        if (yalla.util.IsBrowser()) ludo.GameSocket.instance.connectByUrl(url);
    }
    public onOffGame(msg) {
        super.onOffGame(msg);
        yalla.Global.isFouce = true;
        this.offGameView.serviceInit(msg);
        Laya.timer.frameOnce(10, this, () => {
            yalla.Native.instance.removeMatchView();
        })
    }
    public onNoviceGame(gameid, faceurl, name, realRoyLevel: string, viplevel: string) {
        this.noviceGameView.init(gameid, faceurl, name, realRoyLevel, viplevel);
    }

    public onForce() {
        super.onForce();
        if (yalla.Global.game_state != yalla.data.GameState.State_Wake && yalla.Global.game_state.length > 0)
            yalla.Global.game_state = yalla.data.GameState.State_Wake;
        if (this.gameView) {
            this.gameView.onForce();
            if (!this.gameView.isOffGame) {
                this.lastTime = new Date().getTime();
                Laya.timer.loop(yalla.Global.heart_interval, this, this.heartBeat);
            }
        }
    }
    public onBlur() {
        super.onBlur();
        yalla.Global.game_state = yalla.data.GameState.State_Sleep;
        // Laya.timer.clear(this, this.heartBeat);
        this.gameView && this.gameView.onBlur();
        yalla.common.InteractiveGift.Instance.onBlur();
    }
    public onQuitGame() {
        super.onQuitGame(null);
        Laya.timer.clearAll(this);
        if (this.gameView && this.gameView.onQuitGame) {
            this.gameView.onQuitGame();
        } else {
            // yalla.Native.instance.backHall(true);
            yalla.util.backHall(true)
        }
    }
    /**
     * 重连倒计时结束 手动结束socket连接
     */
    public onReTimeOver() {
        // ludo.GameSocket.instance.close();
        ludo.GameSocket.instance.closeNet();//TODO::1.3.1
        yalla.Global.IsGameOver = false;
    }
    /**
     * 开始断线重连
     */
    public onReconnect(): void {
        super.onReconnect();
        // if (yalla.util.IsBrowser()) {
        if (!yalla.Global.IsGameOver) {
            yalla.Debug.log("发起重连");
            ludo.GameSocket.clear();
            this.onLogin();
            if (!yalla.util.IsBrowser()) yalla.NativeWebSocket.instance.sendNetReConnect();
        } else {
            yalla.Debug.log("发起重连，游戏结束0");
            this.gameView && this.gameView.gameFinish("The game is finished");
        }
        // } else {
        //       yalla.NativeWebSocket.instance.sendNetReConnect();          
        // }
    }
    // private addNetStateEvent() {
    //     this.removeNetStateEvent();
    //     var native = yalla.Native.instance;
    //     native.on(native.Event.NETSTATECHANGE, this, this.onNetStateChange);
    // }
    // private removeNetStateEvent() {
    //     var native = yalla.Native.instance;
    //     native.off(native.Event.NETSTATECHANGE, this, this.onNetStateChange);
    // }
    /**
     * 网络发生改变
     */
    // public onNetStateChange(json) {
    //     yalla.Debug.log("--onNetStateChange ludo--");
    //     // if (yalla.Global.onlineMode && json.status < 1 && yalla.Global.isconnect && !yalla.Global.IsGameOver) {
    //     //     this.closeSocket();
    //     // }
    // }
    /**
     * onKickedOut
     */
    public onKickedOut() {
        super.onKickedOut();
        yalla.Global.IsGameOver = true;
        this.backHallClear();
    }
    private closeSocket(/*e: Laya.Event*/) {
        yalla.Debug.log("--closeSocket:" + yalla.Global.isconnect);
        if (yalla.Global.isconnect) {
            yalla.Global.isconnect = false;
            this.msgStack = [];
            yalla.util.sendExitType(ExitType.SOCKETFAILED_QUIT);
            this.isReady = false;
            this.print = 0;
            Laya.timer.clear(this, this.heartBeat);
            var isOther = false;
            if (this.gameView) {
                if (this.gameView.isOffGame) return;
                isOther = !!this.gameView.isOther();
                this.gameView.loseNetWork();
            }
            yalla.common.connect.ReconnectControl.instance.connect(!isOther);
            // if (yalla.Voice.instance.voiceOpen) {
            //     yalla.Voice.instance.levelGameRoom();
            // }
        }
        this.gameView && this.gameView.onSocketClose();
    }
    private onClose() {
        yalla.Debug.log("**onClose**");
        if (this.sockteStatus > 0) {
            yalla.Native.instance.mobClickEvent(buryPoint.GAME_BREAK_CONNECTFAILURE);
        } else {
            yalla.Native.instance.mobClickEvent(buryPoint.GAME_BREAK);
            this.sockteStatus++;
        }
        this.closeSocket();
    }
    private onError(e: Laya.Event) {
        yalla.Debug.log("**onError**");
        this.closeSocket();
    }
    private addListener() {
        yalla.NativeWebSocket.instance.off(Laya.Event.CLOSE, this, this.onClose);
        yalla.NativeWebSocket.instance.off(Laya.Event.ERROR, this, this.onError);

        ludo.GameSocket.instance;
        if (yalla.util.IsBrowser()) {
            ludo.GameSocket.instance.once(Laya.Event.OPEN, this, this.onOpen);
            ludo.GameSocket.instance.on(Laya.Event.CLOSE, this, this.onClose);
            ludo.GameSocket.instance.on(Laya.Event.ERROR, this, this.onError);
        } else {
            yalla.NativeWebSocket.instance.on(Laya.Event.CLOSE, this, this.onClose);
            yalla.NativeWebSocket.instance.on(Laya.Event.ERROR, this, this.onError);
        }
        ludo.GameSocket.instance.on(Laya.Event.CHANGE, this, this.onMsg);
    }

    public onWebSocketOpen() {
        super.onWebSocketOpen();
        if (this.gameView && this.gameView.isOffGame) return;
        if (!yalla.Global.IsGameOver) {
            this.onOpen(null);
        }
    }

    private onOpen(e: Laya.Event) {
        yalla.Debug.log("ludoMain onOpen");
        yalla.Global.isconnect = true;
        if (this.sockteStatus > 0) {
            yalla.Native.instance.mobClickEvent(buryPoint.GAME_BREAK_RECONNECTSUCCESS);
        }
        if (ludo.Proto.instance.root) {
            ludo.GameSocket.instance.ticketLogin(
                yalla.Global.Account.idx,
                yalla.Global.Account.token,
                yalla.Global.Account.roomid,
                yalla.Global.Account.version,
            );
            yalla.Debug.log("==ticketLogin==version:" + yalla.Global.Account.version);
            yalla.common.connect.ReconnectControl.instance.connectSucss();
            this.lastTime = new Date().getTime();
        }
    }
    private print: number = 0;
    private onMsg(cmd, msg) {
        if (yalla.Global.IsGameOver) return;
        if (!this.isReady) {
            if (cmd > 20) {//没有收到房间信息之前除登录和房间信息之外任何消息暂存不处理
                this.msgStack.push({
                    cmd: cmd,
                    msg: msg
                })
                return;
            }
        }
        switch (cmd) {
            case 10:
                yalla.Debug.log('==========Ludo.login===========');
                msg && yalla.Debug.log(msg);
                if (!msg || !msg.code) {
                    this.gameView && this.gameView.gameFinish("Fail to enter the room");
                    return;
                }
                if (msg.code == 1) {
                    ludo.Global.myInfo = msg.palyerInfo;
                    ludoRoom.instance.watchIdx = msg.watchIdx || 0;
                    var account = yalla.Global.Account
                    account.banTalkData = msg.banTalkData || "";
                    account.voiceType = msg.voiceType || VoiceType.Agora;
                    account.roylevel = ludo.Global.myInfo.playerShowInfo.roylevel;
                    account.realRoyLevel = ludo.Global.myInfo.playerShowInfo.realRoyLevel;
                    // account.voiceType = VoiceType.Agora;
                    yalla.Debug.log("watchIdx:" + ludoRoom.instance.watchIdx);
                    yalla.Debug.log("voiceType:" + account.voiceType);

                    if (msg.banTalkData) {
                        Laya.LocalStorage.setJSON("voice_open", { isOpen: true });
                    }
                    if (this.gameView) {
                        this.gameView.update();
                        this.gameView.onSocketConnect();
                        // this.gameView.muteSpectator();
                    }
                } else {
                    var mg;
                    if (msg.code == 203 || msg.code == 204 || msg.code == 205 || msg.code == 701) {
                        yalla.Debug.log("发起重连，游戏结束2");
                        mg = 'The game is finished';
                        if (msg.code == 701 && this.sockteStatus > 0) {
                            yalla.Native.instance.mobClickEvent(buryPoint.GAME_BREAK_GAMEOVER);
                        }
                    } else {
                        mg = yalla.util.errMsg(msg.code);
                    }
                    yalla.Native.instance.removeMatchView();
                    Laya.timer.frameOnce(5, this, () => {
                        yalla.Global.IsGameOver = true;
                        this.gameView && this.gameView.gameFinish(mg);
                    })
                    return;
                }
                this.sockteStatus = 0;
                // ludo.GameSocket.instance.getFriends();
                break;
            case 20:
                if (msg.hasOwnProperty('autoAddExp')) yalla.Global.Account.autoAddExp = msg.autoAddExp;
                this.gameView.onIndexMsg(cmd, msg);
                this.isReady = true;
                this.msgStack.forEach(msgs => {
                    this.onMsg(msgs.cmd, msgs.msg);
                })
                this.msgStack = [];
                break;
            case 41:
                var room = ludoRoom.instance;
                room.gameBeginTime = msg.gameBeginTime;
                room.quitPunishTime = msg.quitPunishTime;
                room.duelGameEndTime = msg.duelGameEndTime;
                room.currentTime = msg.currentTime;
                yalla.Debug.log(msg);
                this.gameView.gameDataInit(msg);
                if (room.duelGameEndTime && room.isDuel) {
                    DuelManager.instance.timePieceStart();
                }
                break;
            case 45:
                //可以移动的棋子列表 选择棋子(1.4.0版优化断线重连时，修复重连后因服务器发送20协议后又推送了类似协议50，协议46等进入消息队列导致选择棋子点击被移除，所以协议45也进队列)
                //协议带有msgindex才进队列
                if (msg && msg.msgindex) {
                    this.gameView.onIndexMsg(cmd, msg);
                } else {
                    this.gameView.chooseChessMsg(msg);
                }
                break;
            case 49://游戏结束
                this.gameView.gameOver(msg, true);
                yalla.Native.instance.mobClickEvent(buryPoint.LUDOSETTLE);
                break;
            case 60://后进入的玩家信息
                var enterIdx = (msg.player as playerShowInfo).fPlayerInfo.idx;
                if (enterIdx != yalla.Global.Account.idx) {
                    ludoRoom.instance.pushPlayer(msg.player);
                }
                // this.gameView.updateAudiencePLayer();
                // } else {
                this.gameView.update();
                // }
                break;
            case 70://聊天
                this.gameView.onChat(msg, true);
                break;
            case 80://更新金币
                if (msg.type == 0) {
                    // ludo.Global.myInfo.money = msg.value;
                    ludo.Global.myMoney = msg.value;
                } else if (msg.type == 1) {
                    // ludo.Global.myInfo.gold = msg.value;
                    ludo.Global.myGold = msg.value;
                }
                this.gameView.update();

                break;
            case 81://经验升级
                break;
            case 82://金币等级回复
                yalla.Global.Account.currentDiamondNum = msg.gold;
                yalla.Global.Account.currentGoldNum = msg.money;
                this.gameView && this.gameView.update();
                break;
            case 90://Agora token
            case 92://Zego token
                yalla.Debug.log(msg.token + "--" + msg.cName + "===获取语音token:" + cmd + '-IsUpdateZegoToken:' + yalla.Global.IsUpdateZegoToken);
                if (cmd == 92 && yalla.Global.IsUpdateZegoToken) {
                    yalla.Native.instance.zegoTokenResponse(msg.token, msg.cName);
                    yalla.Global.IsUpdateZegoToken = false;
                } else {
                    this.gameView.voiceTokenInit(msg.token, msg.cName);
                }
                // var voice: yalla.Voice = yalla.Voice.instance;
                // if (!voice.joinSuccess) {
                //     [voice.token, voice.channelId] = [msg.token, msg.cName];
                //     voice.joinTimes = 0;
                //     voice.joinGameRoomWithToken(yalla.Global.Account.idx);
                // } else {
                //     this.gameView.joinVoiceCallback();
                // }
                break;
            case 91: break;
            case 101://返回好友列表
                if (!!msg.playerList) {
                    yalla.Friends.friends_cache = msg.playerList;
                }
                break;
            case 102://邀请好友
                break;
            case 111:
                // yalla.Debug.log('==ludo handleUpdateGiftCnf=='+JSON.stringify(msg));    
                this.gameView && this.gameView.handleUpdateGiftCnf(msg);
                break;
            case 112:
                // yalla.Debug.log('==ludo handleRevGift==' + JSON.stringify(msg));     
                if (msg.code > 1) {
                    yalla.common.InteractiveGift.Instance.checkError(msg.code);
                    return;
                }
                if (msg.senderIdx == yalla.Global.Account.idx) {
                    if (msg.hasOwnProperty('senderMoney')) ludo.Global.myMoney = msg.senderMoney;
                    if (msg.hasOwnProperty('senderDiamond')) ludo.Global.myGold = msg.senderDiamond;
                }
                this.gameView && this.gameView.update();
                yalla.common.InteractiveGift.Instance.pushMsg(msg);
                if (yalla.common.InteractiveGift.Instance.msgList.length <= 1) yalla.common.InteractiveGift.Instance.handleRevGift(msg);
                yalla.Debug.log(yalla.common.InteractiveGift.Instance.msgList.length + '~len~~~giftId~' + msg.giftId);
                break;
            case 120://获取商品列表
                break;
            case 126://重复登录
                yalla.Global.IsGameOver = true;
                // ludo.GameSocket.instance.close();
                yalla.Voice.instance.levelGameRoom();//TODO::1.3.4 因被顶号后backhall有概率退出语音房释放，这里再次执行levelGameRoom
                ludo.GameSocket.instance.closeNet();//TODO::1.3.1
                if (!!msg.reason) {
                    var str = 'Repeat login!';
                    switch (msg.reason) {
                        case 1:
                            // str = 'Account has logged in another device.';
                            str = '';
                            yalla.util.sendExitType(ExitType.LOGGED_ANOTHER_QUIT);
                            yalla.Native.instance.alertAccountLoggedView(() => {
                                yalla.Native.instance.backHall(false);
                            });
                            break;
                        case 2:
                            str = 'Server maintenance';
                            yalla.util.sendExitType(ExitType.SERVER_MAINTENANCE_QUIT);
                            break;
                        case 3:
                            str = 'You have been kicked out of the game by GM';
                            yalla.util.sendExitType(ExitType.KICKED_QUIT);
                            break;
                    }
                }
                if (str.length > 0) yalla.common.Confirm.instance.showConfirm(str, Laya.Handler.create(this, this.loginOut), null, ['I got it']);
                break;
            case 127://心跳
                this.lastTime = new Date().getTime();
                break;
            default:
                //1.2.1 修改,之前只有 this.gameView.onIndexMsg(cmd, msg);会造成报错
                if (!!msg) {
                    if (msg.msgindex) {
                        this.gameView.onIndexMsg(cmd, msg);
                    } else {
                        this.gameView.processMsg(cmd, msg);
                    }
                }
                break;
        }
    }
    private loginOut() {
        yalla.Native.instance.backHall(false);
        yalla.Native.instance.loginOut();
    }

    private heartBeat() {
        if (!yalla.Global.onlineMode || yalla.Global.IsGameOver) {
            return;
        }
        var now = new Date().getTime();
        if (now - this.lastTime > yalla.Global.heart_interval + 3000) {
            if (ludoRoom.instance.firstLogin) yalla.Global.isconnect = true;
            yalla.Debug.log("====心跳closesocket===");
            this.closeSocket();
            return;
        }
        if (yalla.util.IsBrowser()) {
            if (ludo.GameSocket.instance.connected) ludo.GameSocket.instance.heartBeat();
        } else {
            // if(yalla.Global.isconnect) ludo.GameSocket.instance.heartBeat();
            ludo.GameSocket.instance.heartBeat();
        }
        // if (ludo.GameSocket.instance.connected) //TODO::1.3.1有问题
        //     ludo.GameSocket.instance.heartBeat();

        // else {
        //     this.closeSocket();
        //     yalla.Debug.log("connected:"+ludo.GameSocket.instance.connected)
        // }
    }
    public onResize() {
        this.gameView && this.gameView.onResize();
    }

    public backHallClear(remove: boolean = false): void {
        super.backHallClear();
        yalla.Voice.instance.offAll();
        if (!!this._gameView) {
            if (!remove) {
                // (this._gameView as Laya.Box).cacheAsBitmap = true;
                this._gameView.backHallClear();
            } else {
                this._gameView.clear();
                this._gameView = null;
            }
            Laya.timer.clearAll(this._gameView);
        }
        yalla.Global.IsGameOver = true;
        Laya.timer.clearAll(this);
        ludo.GameSocket.instance.offAll();
        ludo.GameSocket.clear();
        Laya.timer.clear(this, this.heartBeat);
        ludoRoom.instance.clear();
        ludoRoom._instance = null;
        this.lastTime = null;
        this.isReady = false;
        this.msgStack = [];
        yalla.File.loadStack = [];
        // this.removeNetStateEvent();
        if (yalla.common.TipItem._instance) {
            yalla.common.TipItem._instance.clear();
            yalla.common.TipItem._instance = null;
        }
        LudoBuffManager._instance && LudoBuffManager.instance.clear();
    }
    public clear() {
        this.backHallClear(true);
        super.clear();
    }
}