class BarrageItem extends ui.ludo.chat.barrageItemUI {
    private _tw: Laya.Tween = null;
    public _reductionY: number = 0
    private _royalLevel: number = 0;
    private _vip: number = 0;
    private rlSk: Laya.Skeleton = null;
    private vipSk: Laya.Skeleton = null;
    private _rlKey = "watchBarrage_rl";
    private _vipKey = "watchBarrage_vip";
    private _beginX = 0;
    private _xx: number = 0;

    constructor() {
        super();
        this._nickName.style.fontSize = 20;
        this._nickName.style.color = "#777777";
        this._chat.style.fontSize = 22;
        this._chat.style.color = "#555555";
        this._chat.style.wordWrap = true;
        this._chat.innerHTML = "";
        this.anchorY = 1;
    }
    set isMy(b: boolean) {
        if (b) this._chat.style.color = "#0A748F";
        else this._chat.style.color = "#555555";
    }
    set reductionY(v: number) {
        this._reductionY -= v;
    }
    get reductionY(): number {
        return this._reductionY;
    }
    public tweenMove(prop: any, durtain: number) {
        this._tw = Laya.Tween.to(this, prop, durtain, null, null, 0, true, true);
    }
    public setData(chat: any, fPlayerInfo: any): void {
        // this.chat = chat.chat;
        this.onChat(chat.chat);
        this.headUrl = fPlayerInfo.faceUrl;
        if (chat.watch) {
            this.setNickName(fPlayerInfo.nikeName, 10);
            this.royalLevel = fPlayerInfo.royal;
            this.vip = fPlayerInfo.vip;
            this.updatePos();
        } else {
            this.setNickName(fPlayerInfo.nikeName);
        }
    }

    public onChat(msg: any): void {
        if (!msg) return;
        yalla.Debug.log(JSON.stringify(msg) + "====观战聊天barrageItem.onchat=====");
        var messageType = msg.messageType;
        var extension = msg.extension;
        if (extension || messageType) {
            this.width = 300;
            this.height = 90;
            if (typeof (extension) == "string" && extension.indexOf('{') > -1) extension = JSON.parse(msg.extension);
            var emojiId = 0;
            if (extension) emojiId = extension.id;
            if (messageType == yalla.data.jackaro.GameChatMessageType.CHAT_MESSAGE_EMOJI) emojiId = Number(msg.msg);
            if (emojiId) {
                var path = `${yalla.File.cachePath}/emoji_${emojiId}`;
                var imgUrl = '';
                if (yalla.File.existed(`${path}.png`)) {
                    imgUrl = yalla.File.filePath + `emoji_${emojiId}.png`;
                } else {
                    var emojiConf = yalla.data.ChatDataPool.Instance.emojiById(emojiId);
                    if (emojiConf) imgUrl = emojiConf.url;
                    yalla.File.getFileByNative(`emoji_${emojiId}.png`);
                }
                if (imgUrl) this._chat.innerHTML = `<img style="width:48;height:48;" src="${imgUrl}"></img>`;

            } else if (msg.msg) {
                this.chat = msg.msg;
            }
        } else {
            if (msg.msg) {
                this.chat = msg.msg;
            }
        }

    }

    public set chat(str: string) {
        if (str.indexOf("icon_emoji_") > -1) {
            this._chat.innerHTML = `<img style="width:46;height:42;" src="public/${str}"></img>`;
            this.width = 300;
            this.height = 90;
        } else {
            this._chat.innerHTML = yalla.Emoji.createHtmlString(this.getChatStr(str), 24);
            var _H = 56 + this._chat.contextHeight; if (_H < 90) _H = 90;
            this.height = _H;
            this.width = Math.max(this._chat.contextWidth + 122, 322);
        }
    }

    private getChatStr(msg: string): string {
        msg = msg.replace(/[\\<>]/g, function (a) {
            if (a == '<')
                return '&lt;';
            else if (a == '>')
                return '&gt;';
            else
                return "";
        });
        var len = yalla.util.getBLen(msg);
        var maxbyte = 56;
        if (msg.hasArabic()) maxbyte = 120;
        if (len > maxbyte) return msg.subYlEmoji(maxbyte - 3) + "...";
        return msg;
    }
    public set headUrl(url: string) {
        this._headUrl.skin = "game/default_head.png";
        yalla.Debug.log("$$$$$$$$$--头像下载开始" + url)
        yalla.File.downloadImgByUrl(url, (localPath: string) => {
            yalla.Debug.log("$$$$$$$$$--头像 downloadImgByUrl" + url)
            yalla.Debug.log(localPath)
            yalla.File.groupLoad(localPath, Laya.Handler.create(this, (e) => {
                if (!!e) {
                    yalla.Debug.log("$$$$$$$$$--头像成功 downloadImgByUrl" + url)
                    this._headUrl.skin = localPath;
                    yalla.util.centerSetup(this._headUrl, 54, 0);
                } else {
                    yalla.Debug.log("$$$$$$$$$--头像失败 downloadImgByUrl" + url)
                    yalla.Debug.log(localPath)
                    this._headUrl.skin = localPath;
                    yalla.util.centerSetup(this._headUrl, 54, 0);
                }

            }));
        })

    }
    public set royalLevel(rl: number) {
        this._royalLevel = rl;
        if (rl > 0) {
            this.RL.visible = true;
            if (!this.rlSk) {
                var __s = yalla.SkeletonManager.instance;
                __s.on(this._rlKey, this, this.loadRLSk);
                __s.initSk("royallevel/huangguan", this._rlKey);
            } else {
                this.rlSk.visible = true;
                //目前r+5等级标识和rl5一样
                let rlAnimationLv: number = this._royalLevel > 5 ? 5 : this._royalLevel;
                this.rlSk.play(`R${rlAnimationLv}`, true);
            }
        } else {
            this.RL.visible = false;
            if (this.rlSk) {
                this.rlSk.stop();
                this.rlSk.visible = false;
            }
        }
    }
    public set vip(vip: number) {
        this._vip = vip;
        if (vip > 0) {
            this.VIP.visible = true;
            if (!this.vipSk) {
                var __s = yalla.SkeletonManager.instance;
                __s.on(this._vipKey, this, this.loadVipSk);
                __s.initSk("vip/xunzhang", this._vipKey);
            } else {
                this.vipSk.visible = true;
                this.vipSk.play(`vip_${this._vip}`, true);
            }
        } else {
            this.VIP.visible = false;
            if (this.vipSk) {
                this.vipSk.stop();
                this.vipSk.visible = false;
            }
        }
    }
    private updatePos() {
        this._xx = this._beginX;
        if (this._royalLevel > 0) {
            this.RL.x = this._xx;
            this._xx += this.RL.width;
        }
        if (this._vip > 0) {
            this.VIP.x = this._xx;
            this._xx += this.VIP.width;
        }
        this._nickName.x = this._xx;
    }
    private loadRLSk(sk: Laya.Skeleton) {
        if (!this.rlSk) {
            this.rlSk = sk;
            this.RL.addChild(this.rlSk);
            this.rlSk.pos(8, 14);
            this.rlSk.scale(0.5, 0.5);
            //目前r+5等级标识和rl5一样
            let rlAnimationLv: number = this._royalLevel > 5 ? 5 : this._royalLevel;
            this.rlSk.play(`R${rlAnimationLv}`, true);
            yalla.SkeletonManager.instance.off(this._rlKey, this, this.loadRLSk);
        }
    }
    private loadVipSk(sk: Laya.Skeleton) {
        if (!this.vipSk) {
            this.vipSk = sk;
            this.VIP.addChild(this.vipSk);
            this.vipSk.pos(8, 14);
            this.vipSk.scale(0.5, 0.5);
            this.vipSk.play("vip_" + this._vip, true);
            yalla.SkeletonManager.instance.off(this._vipKey, this, this.loadVipSk);
        }
    }
    public setNickName(str: string, nameLen: number = 13) {
        // this._nickName.innerHTML = yalla.Emoji.createHtmlString(this.getChatStr(str), 22);
        this._nickName.innerHTML = yalla.Emoji.createHtmlString(yalla.util.filterName(str, nameLen), 22);
    }

    clear() {
        this._tw && this._tw.clear();
        this._tw = null;
        yalla.SkeletonManager.instance.off(this._rlKey, this, this.loadRLSk);
        yalla.SkeletonManager.instance.off(this._vipKey, this, this.loadVipSk);
    }
}