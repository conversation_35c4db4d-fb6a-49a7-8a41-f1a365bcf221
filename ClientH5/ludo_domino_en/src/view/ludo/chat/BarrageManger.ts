class BarrageManger extends ui.ludo.chat.barrageMangerUI {
    private _itemPool: Array<BarrageItem> = [];
    private _chatShow: boolean = false;
    //队列最大数量100条 超出队列后 不再往队列内添加消息,
    //自己的消息立即再下一条出现    
    private _chatQueue = [];
    private _myQueue = [];
    private run = false;
    private minSpeed: number = 350;//最小间隔
    private maxSpeed: number = 600;//最大间隔
    constructor() {
        super();
        for (var i = 0; i < 5; i++) {
            var item = new BarrageItem();
            this._itemPool.push(item);
        }
    }
    public clearMsg(destroy: boolean = false) {
        this._chatQueue = [];
        this._myQueue = [];
        this._itemPool.forEach(val => {
            if (val) {
                val.clear();
                val.removeSelf();
                destroy && val.destroy(true);
            }
        })
        this.timer.clear(this, this.hideTopItem);
    }
    public clear() {
        this.clearMsg(true);
        this._itemPool = [];
    }
    public onChatShow() {
        // this.clearMsg();
        this._chatShow = true;
        this.visible = false;
    }
    public onChatHide() {
        this._chatShow = false;
        this.visible = true;
    }
    public addChat(chat: any, isMy: boolean = false) {
        if (!yalla.Global.isFouce) return;
        if (this.run) {
            if (isMy) {
                this._myQueue.push(chat);
            } else {
                this._chatQueue.length < 100 && this._chatQueue.push(chat);
            }
            return;
        }
        this.run = true;
        this.addChat2(chat, isMy);
        this.waitNext();
    }

    private hideTopItem() {
        var topitem = this._itemPool[1];
        topitem && topitem.tweenMove({ alpha: 0 }, 300);
    }
    private waitNext() {
        var speed = this._chatQueue.length > 10 ? this.minSpeed + 20 : this.maxSpeed;
        this.timer.once(speed, this, this.nextChat);
        this.timer.clear(this, this.hideTopItem);
        this.timer.once(5000, this, this.hideTopItem);//5秒后消失最上面的一条
    }

    private nextChat() {
        if (this._myQueue.length > 0) {
            this.addChat2(this._myQueue.shift(), true);
            this.waitNext();
        } else if (this._chatQueue.length > 0) {
            this.addChat2(this._chatQueue.shift(), false);
            this.waitNext();
        } else {
            this.run = false;
        }
    }

    public addChat2(chat: any, isMy: boolean = false) {
        if (yalla.Global.isFouce) {
            var item = this._itemPool.shift();
            if (item) {
                var fPlayerInfo: FPlayerShowInfo = chat.playerShowInfo.fPlayerInfo;
                item.removeSelf();
                item.isMy = isMy;
                item.alpha = 1;
                item.height = 90;
                this._itemPool.push(item);
                item.setData(chat, fPlayerInfo);
                item._reductionY = this.height;
                // item.nickName = fPlayerInfo.nikeName;
                // item.chat = chat.chat;
                // item.headUrl = fPlayerInfo.faceUrl;
                // // item.royalLevel = 1;
                // // item.vip = 1;
                // item.royalLevel = fPlayerInfo['royal'];
                // item.vip = fPlayerInfo['vip'];
                this.addChild(item);
                var item_H = item.height;
                item.y = this.height;
                item.scale(0, 0);
                var maxIndex = this._itemPool.length - 1;
                if (!this._chatShow) {
                    item.tweenMove({ scaleX: 1, scaleY: 1 }, this.minSpeed);
                    this._itemPool.forEach((val, index) => {
                        switch (index) {
                            case 0://最上面的条目
                                val.reductionY = item_H + 10;
                                val.tweenMove({ alpha: 0, y: val.reductionY, scaleX: 0, scaleY: 0 }, this.minSpeed);
                                break;
                            case 1:
                                val.reductionY = item_H + 10;
                                val.tweenMove({ alpha: 0.5, y: val.reductionY }, this.minSpeed);
                                break;
                            case 4: break;
                            default:
                                val.reductionY = item_H + 10;
                                val.tweenMove({ y: val.reductionY }, this.minSpeed);
                                break;
                        }
                    })
                } else {
                    item.scale(1, 1);
                    this._itemPool.forEach((val, index) => {
                        switch (index) {
                            case 0://最上面的条目
                                val.reductionY = item_H + 10;
                                val.scale(0, 0);
                                val.alpha = 0;
                                val.y = val.reductionY;
                                break;
                            case 1:
                                val.reductionY = item_H + 10;
                                val.alpha = 0.5;
                                val.y = val.reductionY;
                                break;
                            case 4: break;
                            default:
                                val.reductionY = item_H + 10;
                                val.y = val.reductionY;
                                break;
                        }
                    })
                }
            }
        }
    }
}