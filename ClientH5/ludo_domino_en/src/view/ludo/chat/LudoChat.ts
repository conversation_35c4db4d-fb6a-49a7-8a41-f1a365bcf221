class LudoChat extends ui.ludo.chat.ludoChatUI implements ChatClass {
    private chat: yalla.common.Chat = null;
    private fastChat: yalla.common.FastChat = null;

    private _friendUnReadMsg: yalla.common.UnReadMsg;
    private _chatUnReadMsg: yalla.common.UnReadMsg;
    private _isOpenFriend: boolean = false;

    // private _unread: number = 0;
    constructor(top: number) {
        super();
        if (yalla.Global.Account.isLimitVisitor && yalla.Global.Account.isPrivate == 1) {//私人房间&阿联酋游客账号不能语音和输入文字聊天 1.2.4add
            //    if (yalla.Global.Account.isLimitVisitor){//TODO::1.3.6 改成不管任何模式
            this.voice_btn.visible = this.chatSp.visible = false;
        }
        if ((yalla.Global.gameType == GameType.LUDO && ludoRoom.instance.isTeamGame()) ||
            (yalla.Global.gameType == GameType.JACKARO && (yalla.data.jackaro.JackaroUserService.instance.room && !yalla.data.jackaro.JackaroUserService.instance.room.is1V1Mode()))) {
            this.chat = new yalla.common.ChatTeam(yalla.Global.gameType)
        } else {
            this.chat = new yalla.common.Chat(yalla.Global.gameType);
        }
        this._friendUnReadMsg = new yalla.common.UnReadMsg(this.friend_unreadTx);
        this._chatUnReadMsg = new yalla.common.UnReadMsg(this.unreadTx);

        this.readMsgNum();
        // this.readMsgNum(yalla.Global.unReadMsg);
        // yalla.Global.unReadMsg = null;

        this.chat.arrow.x = 340;
        this.chat.box.top = top;
        this.chat.bottom = 120;
        this.chat.top = 0;
        this.addChild(this.chat);
        this.chat.setVisible(false);
        this.fastChat = new yalla.common.FastChat(yalla.Global.gameType);
        this.fastChat.mouseThrough = false;
        this.addChild(this.fastChat);
        this.fastChat.x = 10;
        this.fastChat.bottom = 100;
        this.fastChat.arrow.x = 190;
        this.hideFastChat(false);
        this.addEventListener();
    }
    public showLive: boolean = false;
    private addEventListener() {
        this.chat.on("close", this, this.hideChat);
        this.chatSp.on("click", this, this.chatHander);
        this.fastChat.on("close", this, this.hideFastChat);
        this.fastChat_btn.on("click", this, this.fastChatHander);
        this.friendChat_btn.on("click", this, this.friendChatHander);
        yalla.Native.instance.on(yalla.Native.instance.Event.READMSGNUM, this, this.readMsgNum);
        yalla.event.YallaEvent.instance.on('click_gameTopview', this, this.onClickGameTopCiew);

        if (yalla.Global.Account.isLimitVisitor && yalla.Global.Account.isPrivate == 1) {
            this.voice_btn.visible = false;
        } else {
            this.voice_btn.on("click", this, this.voiceHander);
        }
        this.on("click", this, () => {
            this.hideAllChat();
        })
    }


    private removeEventListener() {
        this.offAll();
        this.chat.off("close", this, this.hideChat);
        this.chatSp.off("click", this, this.chatHander);
        this.fastChat.off("close", this, this.hideFastChat);
        this.fastChat_btn.off("click", this, this.fastChatHander);
        this.friendChat_btn.off("click", this, this.friendChatHander);
        this.voice_btn.off("click", this, this.voiceHander);
        yalla.Native.instance.off(yalla.Native.instance.Event.READMSGNUM, this, this.readMsgNum);
        yalla.event.YallaEvent.instance.off('click_gameTopview', this, this.onClickGameTopCiew);
    }
    public close() {
        this.chat && this.chat.close();
        //1.4.5 增加ios机型线上偶现bug日志上报，针对jackaro游戏服  聊天日志
        yalla.util.logReport("LudoChat close:");
    }
    public clear() {
        this.removeEventListener();
        if (this.chat) {
            this.chat.clear();
            this.chat.destroy(true);
            this.destroy(true)
            this.chat = null;
        }
    }
    private set unread(val: number) {
        if (!this._chatUnReadMsg) return;
        // if (val == this._unread) return;
        // this._unread = val;
        // this.unreadTx.label = val <= 99 ? String(val) : "99+";
        // this.unreadTx.visible = val > 0;
        if (val == this._chatUnReadMsg.readNum) return;
        this._chatUnReadMsg.updateStatus(val);
    }
    private get unread(): number {
        return this._chatUnReadMsg ? this._chatUnReadMsg.readNum : 0;
    }
    private voiceHander(e) {
        e.stopPropagation();
        if (yalla.Mute.isBanTalk) {
            yalla.Mute.showBanTalkDialog();
            return;
        }
        // var url = yalla.Global.rootUrl + "/YallaGame/SundryInfo/VoiceStatistic";
        // if (this._isSpectator) return;
        if (yalla.Voice.instance.voiceOpen) {
            yalla.Voice.instance.disableAudio(() => {//close
                yalla.Debug.log("disableAudio");
                Laya.LocalStorage.setJSON("voice_open", { isOpen: false })
                this.trunVoice();
                yalla.Voice.instance.voiceStatistic(this.getSendParams(0))
                // yalla.util.ajax({
                //     url: url,
                //     data: this.getSendParams(0),
                //     success: e => { },
                //     error: e => { }
                // })
            })
        } else {
            yalla.Voice.instance.enableAudio(() => {//open
                yalla.Debug.log("enableAudio");
                Laya.LocalStorage.setJSON("voice_open", { isOpen: true })
                this.trunVoice();
                yalla.Voice.instance.voiceStatistic(this.getSendParams(1))
                // yalla.util.ajax({
                //     url: url,
                //     data: this.getSendParams(1),
                //     success: e => { },
                //     error: e => { }
                // })
            }, () => {
                if (yalla.Native.instance.deviceType != DeviceType.Android)
                    yalla.common.Tip.instance.showTip(yalla.data.TranslationD.Game_Tip_Check_Microphone2);
            }, 1)
        }
    }

    private getSendParams(type: number = 0): any {
        var data = {};
        if (yalla.Global.gameType == GameType.LUDO) {
            data = {
                accountId: yalla.Global.Account.idx,
                gameId: GameType.LUDO,
                roomId: ludoRoom.instance.ID,
                roomUserNum: ludoRoom.instance.getSitPlayers().length,
                roomCost: ludoRoom.instance.cost,
                type: type
            }
        } else if (yalla.Global.gameType == GameType.SNAKEANDLADER) {
            var room = yalla.data.snake.UserService.instance.room;
            data = {
                accountId: yalla.Global.Account.idx,
                gameId: GameType.SNAKEANDLADER,
                roomId: room ? room.roomid : 0,
                roomUserNum: room ? room.playerNums : 0,
                roomCost: room ? room.cost : 0,
                type: type
            }
        } else if (yalla.Global.gameType == GameType.JACKARO) {
            let room = yalla.data.jackaro.JackaroUserService.instance.room;
            data = {
                accountId: yalla.Global.Account.idx,
                gameId: GameType.JACKARO,
                roomId: room ? room.roomId : 0,
                roomUserNum: room ? room.roomMaxNum : 0,
                roomCost: room ? room.getBets() : 0,
                type: type
            }
        }
        return data;
    }

    public trunVoice() {
        if (yalla.Voice.instance.voiceOpen) {
            this.voice_btn.skin = "game/btn_speak.png";
            this.voice_btn.stateNum = 2;

        } else {
            this.voice_btn.skin = "game/btn_nospeak.png";
            this.voice_btn.stateNum = 1;
        }
    }
    private fastChatHander(e) {
        e.stopPropagation();
        yalla.Sound.playSound("click");
        this.exclusiveOperate(1);
    }

    private hideFastChat(isVisib: boolean) {
        this.fastChat.setVisible(isVisib);
        if (isVisib) {
            yalla.Native.instance.mobClickEvent(buryPoint.GAME_EXPRESSION);
            this.setChildIndex(this.fastChat, this.numChildren - 1);
            this.fastChat_btn.skin = "game/btn_closeChat.png";
            yalla.util.ViewExclusion.instance.add(yalla.util.ViewType.FastChatView, () => {
                this.hideFastChat(false);
            });//TODO::这里回掉不做处理,不影响其他游戏
        } else {
            this.fastChat_btn.skin = "game/btn_Expression.png";
            yalla.util.ViewExclusion.instance.remove(yalla.util.ViewType.FastChatView);
        }
    }
    private chatHander(e) {
        e.stopPropagation();
        yalla.Sound.playSound("click");
        this.exclusiveOperate(2);
        this.unread = 0;
        //1.4.5 增加ios机型线上偶现bug日志上报，针对jackaro游戏服  聊天日志
        yalla.util.logReport("LudoChat chatHander:");
    }
    private hideChat(isVisib: boolean) {
        if (!this.chat) return;
        this.chat.close(isVisib);
        if (isVisib) {
            this.chat_btn.skin = "game/btn_closeChat.png";
            this.chat.show();
            yalla.Native.instance.mobClickEvent(buryPoint.GAME_MESSAGE);
            yalla.event.YallaEvent.instance.event("stopAni");

            yalla.util.ViewExclusion.instance.add(yalla.util.ViewType.ChatView, () => {
                this.hideChat(false);
                yalla.Native.instance.closeReportMsg();
            });
        } else {
            this.chat_btn.skin = "game/btn_chat.png";
            this.chat.hide();
            yalla.event.YallaEvent.instance.event("startAni");
            yalla.util.ViewExclusion.instance.remove(yalla.util.ViewType.ChatView);
        }
        //1.4.5 增加ios机型线上偶现bug日志上报，针对jackaro游戏服  聊天日志
        yalla.util.logReport("LudoChat hideChat isVisib:" + isVisib);
    }

    private friendChatHander(e) {
        e.stopPropagation();
        yalla.Sound.playSound("click");
        this._isOpenFriend = !this._isOpenFriend;
        this.exclusiveOperate(3);

        yalla.util.clogDBAmsg('10179');
    }

    private onClickGameTopCiew(): void {
        if (this._isOpenFriend) {
            this._isOpenFriend = !this._isOpenFriend;
            this.exclusiveOperate(3);
        }
    }

    private updateFriendBtn(isVisible: boolean): void {
        var pos;
        this._isOpenFriend = isVisible;
        if (isVisible) {
            this.friendChat_btn.skin = "game/btn_closeChat.png";
            var arrowX = 485;
            var bottom = 105 + this.bottom;
            pos = { bottom: bottom, arrowX: arrowX, screenScale: yalla.Screen.screen_scale };
        } else {
            this.friendChat_btn.skin = "game/btn_friendChat.png";

            if (!this._friendUnReadMsg || this._friendUnReadMsg.readNum < 1) this.friendChat_btn.visible = false;
        }
        yalla.Native.instance.friendChat(yalla.Global.gameType, Number(isVisible), pos);

        if (isVisible) {
            yalla.util.ViewExclusion.instance.add(yalla.util.ViewType.FriendChat, () => {
                this.updateFriendBtn(false);
            });//TODO::这里回掉不做处理,不影响其他游戏
        } else {
            yalla.util.ViewExclusion.instance.remove(yalla.util.ViewType.FriendChat);
        }
    }

    /**
     * 红点消息
     * @param msg {num:1}
     */
    private readMsgNum(msg: any = null): void {
        var num = 0;
        if (msg && msg.num) num = msg.num;

        if (this._isOpenFriend) {
            this.friendChat_btn.visible = true;
        } else {
            this.friendChat_btn.visible = num > 0;
        }
        // this.friendChat_btn.visible = num > 0;
        this._friendUnReadMsg && this._friendUnReadMsg.updateStatus(num);
    }

    /**
     * 1 fastchat 2 chat  3 friendchat 互斥点击
     */
    private exclusiveOperate(type: number = 0) {
        if (type == 0) {
            this.hideChat(false);
            this.hideFastChat(false);
            this.updateFriendBtn(false);

        } else if (type == 1) {
            this.hideFastChat(!this.fastChat.visible);

            if (this.chat && this.chat.visible && !this.chat.inputChat.focus) {
                this.hideChat(false);
            }
            this.updateFriendBtn(false);

        } else if (type == 2) {
            if (this.contains(this.chat)) {
                this.hideChat(!this.chat.visible);
            }
            this.fastChat.setVisible(false);
            this.fastChat_btn.skin = "game/btn_Expression.png";
            this.updateFriendBtn(false);

        } else if (type == 3) {
            this.updateFriendBtn(this._isOpenFriend);

            if (this.chat && this.chat.visible && !this.chat.inputChat.focus) {
                this.hideChat(false);
            }
            this.fastChat.setVisible(false);
            this.fastChat_btn.skin = "game/btn_Expression.png";
        }
        yalla.common.InteractiveGift.Instance.hideGiftView();
    }

    public hideAllChat() {
        this.hideChat(false);
        this.hideFastChat(false);
        this.updateFriendBtn(false);
        yalla.common.InteractiveGift.Instance.hideGiftView();
    }
    public onChat(chat: any, isMy = false) {
        this.chat && this.chat.onChat(chat);
        if (!this.chat.visible && !isMy) {
            this.unread++;
        }
    }
}