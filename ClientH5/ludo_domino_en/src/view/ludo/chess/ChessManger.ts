class ChessManger {
    static _instance: <PERSON><PERSON>anger = null;
    static _runwayScale: number = 0.8;//跑道上的大小
    static _normalScale: number = 1;//正常大小
    static _pileScale: number = 0.6;//叠子时候的大小
    static _chooseScale: number = 1;//叠子时候的大小
    static _moveExchangeScale: number = 1.2;//棋子交换时缩放大小

    static get instance() {
        return this._instance ? this._instance : this._instance = new ChessManger();
    }
    public _pool = {};
    public _poolColor = {};
    public init(isDuel: boolean) {
        if (isDuel) {
            ChessManger._runwayScale = 1;
            ChessManger._normalScale = 1.2;
            ChessManger._pileScale = 0.8;
            ChessManger._chooseScale = 1.2;
        } else {
            ChessManger._runwayScale = 0.8;
            ChessManger._normalScale = 1;
            ChessManger._pileScale = 0.6;
            ChessManger._chooseScale = 1;
        }
    }
    public addNewChess(chess: LudoChess) {
        if (chess) this._pool[chess.id] = chess;
    }
    get chessLen(): number {
        return Object.keys(this._pool).length;
    }
    public getChessById(chessID: number): LudoChess {
        return this._pool[chessID];
    }
    public getChessesByColor(color: number): Array<LudoChess> {
        if (!this._poolColor[color]) {
            var arr = [];
            this.each(chess => {
                if (chess.color == color) arr.push(chess);
            })
            if (arr.length == 4) {
                this._poolColor[color] = arr;
            } else {
                return [];
            }
        }
        return this._poolColor[color];
    }
    public getWinsLenByColor(color: number): number {
        return this.getChessesByColor(color).filter(ch => {
            return ch.goalStation.gridPosition < -5;
        }).length
    }

    public allChessWin(color: number) {
        return this.getWinsLenByColor(color) == 4;
    }
    public getSameGridChesses(grid: LudoGrid, excludeChessId: number = null): LudoChess[] {
        let result = []
        // var allChess = this.mapView.chess_box._childs || [];
        // return allChess.filter((chess: LudoChess) => {
        //     return chess.displayedInStage && grid.atGrid(chess.goalGrid.gridName) && chess.id != excludeChessId;
        // })
        this.each((chess: LudoChess) => {
            if (chess && chess.displayedInStage && grid.atGrid(chess.goalGrid.gridName) && chess.id != excludeChessId) {
                result.push(chess);
            }
        })
        return result;
    }
    public each(cb: Function) {
        for (var key in this._pool) {
            cb(this._pool[key])
        }
    }
    public clear() {
        this.each(ch => {
            ch && ch.clear();
            ch = null;
        })
        this._pool = {};
        this._poolColor = {};
    }
}