class LudoChess extends ui.ludo.sub.chessUI {
    public _color: number;
    private _homeGrid: LudoGrid = null;//出生点
    private _wayGrid: LudoGrid = null;//当前所在的格子
    private _goalGrid: LudoGrid = null;//移动要去的目标格子,当移动结束的时候 _goalGrid和_wayGrid是同一个格子
    public id: number = null;
    public stepNum: number = 0;
    public childChessId: number = -1;
    public moving: boolean = false;
    public isEndChess: boolean = false;
    public isMaster: boolean = false;
    private circleAureole: CircleAureoleProp = null;//波纹动画
    private tigerBg: TigerBg = null;//老虎背景
    private tigerFace: TigerFace = null;//虎头
    private lockDown: LockDown = null;//麻痹状态显示
    private timeLine: Laya.TimeLine;
    private arrowLine: Laya.TimeLine;
    private _choose: boolean = false
    private skin_id: number = null;
    private _skinIsLoaded: boolean = false;
    private _soundTimeOut = null;
    private _tw: Laya.Tween = null;
    private _chessData: any = null;
    private _rocket: boolean = false;
    private _lineGirds: LudoGrid[] = [];
    public moveType: ChessMoveType = null;
    public tigerHitMsg: any = null;
    public winnerExp: number = null;
    constructor(skinId: number, color: number) {
        super();
        this.color = color;
        this.skinId = (!!skinId && skinId > 11000) ? skinId : 11000;
        this.chessImg.visible = true;
        this.timeLineInit();
        this.circleAureole = LudoBuffManager.instance.getCircleAureoleProp();
        this.circleAureole.pos(40, 40)
        this.box.addChildAt(this.circleAureole, 0);
    }
    set skinId(val: number) {
        this.skin_id = val;
        this.setSkin("chess");
    }
    get skinId(): number {
        return this.skin_id;
    }
    set choose(boo: boolean) {
        this._choose = boo
        if (yalla.Global.isFouce) {
            boo ? this.circleAureole.play(1, true) : this.circleAureole.stop();
        }
        this.circleAureole.visible = boo;
    }
    get choose(): boolean {
        return this._choose;
    }
    public set status(status: ChessState) {
        switch (status) {
            case ChessState.JUNGLE_PARALYSIS:
                this.showLockDown();
                break;
            default:
                this.hideLockDown();
                break;
        }
    }
    public setStatus(chessState: ChessState[]) {
        if (chessState && chessState.length) {
            chessState.forEach(status => {
                this.status = status;
            })
        } else {
            this.status = ChessState.JUNGLE_NONE_STATE;
        }
    }
    private showLockDown() {
        if (!this.lockDown) {
            this.lockDown = LudoBuffManager.instance.getLockDown();
            this.lockDown.pos(40, 40);
            this.box.addChild(this.lockDown);
        }
        this.lockDown.play(0, true);
    }
    private hideLockDown() {
        if (this.lockDown) {
            this.lockDown.recover();
        }
        this.lockDown = null;
    }
    private timeLineInit() {
        this.timeLine = new Laya.TimeLine();
        this.timeLine.on(Laya.Event.COMPLETE, this, this.moveComplete);
        this.timeLine.on(Laya.Event.LABEL, this, this.onLabel);
    }
    private onLabel(index: number) {
        index = Number(index)
        if (index) {
            let grid = this._lineGirds[index - 1];
            grid && this.event(Laya.Event.LABEL, [grid, this]);
        }
    }
    private moveComplete() {
        if (this.destroyed) return;
        if (this.timeLine) {
            this.timeLine.reset();
        }
        this.moveEnd();
        // if (!this.choose) {
        //     this.setChScale();
        // }
    }
    public cut(station) {
        var x = this.x;
        this._tw = Laya.Tween.to(this, {
            x: x + 10,
            update: null
        }, 120, Laya.Ease.linearInOut, Laya.Handler.create(this, () => {
            this._tw && this._tw.clear();
            this.x = x;
            this.timer.once(120, this, this.move, [station, 1])
        }), 300, true)
    }
    public move(station: Station, type: ChessMoveType = ChessMoveType.MOVE_NORMAL) {
        this.moveType = type;
        var grid: LudoGrid;
        // yalla.Debug.zzf(`${this.id},type:${type},---start:${JSON.stringify(this._goalGrid.station)}--end:${JSON.stringify(station)}`);
        ludoRoom.instance.event("reDraw", [this.id, this._wayGrid.station, this.color, false]);
        if (!yalla.Global.isFouce) {
            if (station && station.gridPosition <= -6) station.gridPosition = 95 - this.homeStation.gridPosition;
            grid = LudoBoardData.getGridByStation(station);
            if (grid) this._wayGrid = this._goalGrid = grid;
            this.moveEnd(); return;
        }
        grid = LudoBoardData.getGridByStation(station);
        if (!grid) return;
        this._wayGrid = this._goalGrid;
        this._goalGrid = grid;
        if (this.clickArea.width == 80)
            this.clickArea.size(60, 60);
        switch (type) {
            case ChessMoveType.MOVE_BE_HIT:
            case ChessMoveType.MOVE_TIGER_HIT:
            case ChessMoveType.MOVE_HOME:
                this.moveHome(grid);
                break;
            default:
                this.lineMove(type != ChessMoveType.MOVE_BACKWARD_WIND);
                break;
        }
    }
    private moveHome(grid: LudoGrid) {
        grid.port && this.jumpMove(grid.port);
        // this.setChScale();
        this.status = null;
        this.removeAllBuff();
        this.clickArea.size(80, 80);
    }
    public get goalStation(): Station { return this._goalGrid.station; }
    public set homeGrid(grid: LudoGrid) { this._homeGrid = grid; }
    public get homeGrid() { return this._homeGrid; }
    public get homeStation() { return this._homeGrid.station; }
    public get goalGrid() { return this._goalGrid; }
    public set goalGrid(grid: LudoGrid) { this._goalGrid = grid };
    public get wayGrid() { return this._wayGrid; }
    public get wayStation(): Station { return this._wayGrid.station; }
    set color(color: number) { this._color = color; }
    get color(): number { return this._color; }
    set data(val: any) {
        if (val.gridPosition <= -6) val.gridPosition = 95 - this.homeStation.gridPosition;
        this._chessData = val;
        this.id = val.chessId;
        this.name = String(this.id);
        this.isMaster = Boolean(val['isMaster']);
        var grid: LudoGrid = LudoBoardData.getGridByStation(val);
        if (grid) {
            if (this._goalGrid && this._goalGrid.inEnd) {//1.4.3 已经到达终点的棋子
                if (!(grid.inHome || grid.inStart || grid.inEnd)) {//排除起飞点和出生和终点 其它的不刷新
                    return;
                }
            }
            this._wayGrid = this._goalGrid = grid;
            this.setChScale();
            this.status = null;
            this.removeAllBuff();
            // this.clearTimeLine();
            !this.moving && this.moveEnd();
            this.tigerHitMsg = null;
            this.winnerExp = null;
        }
    }
    get data(): any { return this._chessData; }
    set rocket(bo: boolean) {
        this._rocket = bo;
        if (bo) {
            this.chessImg.skin = "game/rocket_fly.png";
            // this.rocketSk && this.rocketSk.stop();
        } else {
            this.setSkin(this.childChessId > 0 ? "pile" : "chess");
            this.rotation = 0;
        }
    }
    get rocket(): boolean { return this._rocket }

    private setSkin(name: string) {
        if (this.skin_id == 11000) {
            this.chessImg.skin = `game/${name}_11000_${this.color}.png`;
        } else if (this._skinIsLoaded) {
            if (this.chessImg) this.chessImg.skin = `chess_${this.skin_id}/${name}_${this.skin_id}_${this.color}.png`;
        } else {
            if (this.chessImg) this.chessImg.skin = `game/${name}_11000_${this.color}.png`;
            yalla.event.YallaEvent.instance.once(`chess_${this.skin_id}.atlas`, this, () => {
                if (this.chessImg) this.chessImg.skin = `chess_${this.skin_id}/${name}_${this.skin_id}_${this.color}.png`;
                this._skinIsLoaded = true;
            })
            yalla.event.YallaEvent.instance.once(`chess_${this.skin_id}.png`, this, () => {
                yalla.File.getFileByNative(`chess_${this.skin_id}.atlas`);
            })
            yalla.File.getFileByNative(`chess_${this.skin_id}.png`);
        }
    }
    public breakLink(grid: LudoGrid) {
        if (grid.inEnd) {
            grid = LudoBoardData.getGridByName(`${this.color}_${95 - this.homeStation.gridPosition}`);
        }
        this._wayGrid = this._goalGrid = grid;
        this.childChessId = -1;
        let port: port = grid.port;
        this.pos(port.x, port.y);
        this.isMaster = false;
    }
    public reset(updatePos: boolean = true) {
        this._goalGrid = this._wayGrid = this._homeGrid;
        this.choose = false;
        this.isMaster = false;
        this.childChessId = -1;
        this.setChScale();
        this.pivotX = 40;
        this.hidePlieNum();
        if (updatePos) {
            let port: port = this._wayGrid.port;
            this.pos(port.x, port.y);
        }
        this.clickArea.offAll();
        this.setStatus(null)
    }
    public onBlur() {
        if (this.timeLine) {
            this.timeLine.pause();
            this.setChScale();
            this.moveEnd();
        }
    }
    public onForce() {
        this.choose && this.circleAureole.play(1, true);
    }
    public tornadoMove(station: Station, type: ChessMoveType) {//龙卷风移动 可以向前或者向后移动
        this.move(station, type);
    }
    public tigerMove(station: Station) {//老虎移动
        this.moving = true;
        if (yalla.Global.isFouce && !this.tigerBg) {
            this.tigerBg = LudoBuffManager.instance.getTigerBg();
            this.tigerFace = LudoBuffManager.instance.getTigerFace();
            this.tigerBg.pos(40, 40);
            this.tigerFace.pos(40, 40);
            this.box.addChildAt(this.tigerBg, 0);
            this.box.addChild(this.tigerFace);
            this.tigerBg.play(0, true);
            this.tigerFace.play(0, false);
        }
        this.move(station, ChessMoveType.MOVE_TIGER);
    }
    private jumpMove(port: port, duration: number = ludo.Global.moveTime) {//直接移动到目标点
        this.moving = true;
        if (yalla.Global.isFouce) {
            // yalla.Sound.playSound("token_move");
            this._tw = Laya.Tween.to(this, {
                x: port.x,
                y: port.y,
                update: null
            }, duration, Laya.Ease.linearInOut, Laya.Handler.create(this, () => {
                this._tw && this._tw.clear();
                this.moveEnd();
            }))
        } else {
            this.pos(port.x, port.y);
            this.moveEnd();
        }
    }
    public rocketMove(val: Station, time: number = 500) {//火箭移动
        var grid: LudoGrid;
        if (val.gridPosition <= -6) {
            grid = LudoBoardData.getGridByName(`${this.color}_${95 - this.homeStation.gridPosition}`);
        } else {
            grid = LudoBoardData.getGridByStation(val);
        }
        if (!grid) return;
        this._wayGrid = this._goalGrid = grid;
        let port: port = grid.port;
        var bubble = this.getChildByName("bubble");
        bubble && bubble.removeSelf();
        if (yalla.Global.isFouce) {
            yalla.Sound.playSound("rocket");
            this._tw = Laya.Tween.to(this, {
                x: port.x,
                y: port.y,
                update: null
            }, time, Laya.Ease.linearIn, Laya.Handler.create(this, () => {
                this._tw && this._tw.clear();
                LudoBuffManager.instance.getRocketProp().addView(this).play(0, false);
                this.moveEnd();
            }))
        } else {
            this.rocket = false;
            this.pos(port.x, port.y);
            this.stepNum = 0;
            this.moveEnd();
        }
    }
    public arrowMove(station: Station) {//箭头移动
        var grid: LudoGrid = LudoBoardData.getGridByStation(station);
        if (!grid) return;
        yalla.Debug.log(`${this.id},type:arrow,-start:${JSON.stringify(this._goalGrid.station)}--end:${JSON.stringify(station)}`);
        this._wayGrid = this._goalGrid = grid;
        let port: port = grid.port;
        var bubble = this.getChildByName("bubble");
        bubble && bubble.removeSelf();
        if (yalla.Global.isFouce) {
            this.scale(ChessManger._normalScale, ChessManger._normalScale);
            if (!this.arrowLine) {
                this.arrowLine = new Laya.TimeLine();
                this.arrowLine.on(Laya.Event.COMPLETE, this, this.moveComplete);
            } else {
                this.arrowLine.reset();
            }
            this.arrowLine.addLabel("0", 0).to(this, { scaleX: 1.2, scaleY: 1.2 }, 30);
            this.arrowLine.addLabel("1", 0).to(this, { scaleX: 0, scaleY: 0 }, 100);
            this.arrowLine.addLabel("2", 0).to(this, port, 0);
            this.arrowLine.addLabel("3", 0).to(this, { scaleX: 1.2, scaleY: 1.2 }, 100, null, 300);
            this.arrowLine.addLabel("4", 0).to(this, { scaleX: 1, scaleY: 1 }, 30);
            this.arrowLine.play("0", false);
        } else {
            this.pos(port.x, port.y);
            this.stepNum = 0;
            this.moveEnd();
        }
    }
    private lineMove(forward: boolean) {//直线平滑移动
        this.moving = true;
        var len = forward ? LudoBoardData.distanceStep(this._wayGrid, this.goalGrid) : LudoBoardData.distanceStep(this.goalGrid, this._wayGrid);
        if (len > 25 || len <= 0) {
            this.moveEnd();
            return;
        }
        this.getPathsPos(len, forward);
        if (this._lineGirds.length > 0 && this.timeLine) {
            ludoRoom.instance.event("moveLight", [this.id, this._lineGirds, this.color]);
            this.scale(ChessManger._normalScale, ChessManger._normalScale);
            this._lineGirds.forEach((grid: LudoGrid, index) => {
                this.timeLine.addLabel(index.toString(), 0).to(this, grid.port, ludo.Global.moveTime);
            })
            this._soundTimeOut && clearTimeout(this._soundTimeOut);
            this.playsound(this._lineGirds.length);
            this.timeLine.play("0", false);
        } else {
            this.moveEnd();
        }
    }
    public showPlieNum() {
        this.setSkin("pile");
    }
    public hidePlieNum() {
        this.setSkin("chess");
    }
    public moveEnd(stepNum: number = this.stepNum) {//移动结束事件
        this.moving = false;
        this.event(Laya.Event.END, [stepNum, this]);
        this.stepNum = 0;
        if (this._goalGrid) {
            this._wayGrid = this._goalGrid;
            let port: port = this._goalGrid.port;
            this.pos(port.x, port.y);
            ludoRoom.instance.event("reDraw", [this.id, this.goalStation, this.color, true]);
            ludoRoom.instance.event("update", [this.id, this.goalStation, port]);
        }
        if (this.rocket) this.rocket = false;
        this.removeAllBuff();
    }
    private removeAllBuff() {
        if (this.tigerBg) {
            this.tigerBg.removeSelf()
            this.tigerBg.stop();
            LudoBuffManager.instance.recoverTigerBg(this.tigerBg);
            this.tigerBg = null;
        }
        if (this.tigerFace) {
            this.tigerFace.removeSelf()
            this.tigerFace.stop();
            LudoBuffManager.instance.recoverTigerFace(this.tigerFace);
            this.tigerFace = null;
        }
    }
    public clearTimeLine() {
        if (this.timeLine) {
            this.timeLine.pause();
            this.timeLine.reset();
        }
        if (this.arrowLine) {
            this.arrowLine.pause();
            this.arrowLine.reset();
        }
        clearTimeout(this._soundTimeOut);
        this.moveEnd();
    }
    public playsound(n: number) {
        if (!n || !yalla.Global.isFouce) { return; }
        n--;
        yalla.Sound.playSound("token_move");
        this._soundTimeOut = setTimeout(() => {
            clearTimeout(this._soundTimeOut);
            this.playsound(n);
        }, ludo.Global.moveTime);
    }
    public setChScale() {//通过当前的位置设置缩放比例
        if (this._wayGrid.inEnd) {
            this.scale(0.54, 0.54);
        } else if (this.choose || this.moving || this._wayGrid.inHome) {
            this.scale(ChessManger._normalScale, ChessManger._normalScale);
        } else {
            this.scale(ChessManger._runwayScale, ChessManger._runwayScale);
        }
        return this;
    }
    public pileScale() {
        !this.choose && this.scale(ChessManger._pileScale, ChessManger._pileScale);
        return this;
    }
    private getPathsPos(len: number, forward: boolean = true) {//获取路径坐标
        var endArea: boolean = this._goalGrid.inEndArea;
        this._lineGirds = []
        for (var i = 0; i < len; i++) {
            var grid: LudoGrid;
            if (forward) {
                if (this._wayGrid.beforeEnd) {//在终点前-5的格子
                    grid = LudoBoardData.getGridByName(`${this.color}_${95 - this.homeStation.gridPosition}`);
                    this._goalGrid = grid;
                } else if (endArea && this._wayGrid.inCorner && this.wayGrid.station.area == this.color) {//在0的拐角位置
                    grid = LudoBoardData.getGridByName(`${this.color}_-1`);
                } else {//其它情况
                    grid = LudoBoardData.getGridByName(this._wayGrid.nextName);
                }
            } else {
                grid = LudoBoardData.getGridByName(this._wayGrid.lastName);
            }
            if (grid) {
                this._lineGirds.push(grid)
                this._wayGrid = grid;
                if (this._wayGrid.atGrid(this._goalGrid.gridName)) {
                    return;
                }//和目标点一致
            }
        }
    }
    /**
     * 停止棋子移动，主要用于玩家操作棋子移动和冲突时，针对冲突棋子移动状态停止移动和恢复移动之前的数据状态
     */
    public stopMove(grid?: LudoGrid) {
        if (this.timeLine) {
            this.timeLine.pause();
        }
        if (this.arrowLine) {
            this.arrowLine.pause();
        }
        if (this._tw) {
            this._tw.clear();
        }
        this._soundTimeOut && clearTimeout(this._soundTimeOut);
        if (this.rocket) this.rocket = false;

        if (grid) {
            this._wayGrid = this._goalGrid = grid;
            if (grid.port) {
                this.pos(grid.port.x, grid.port.y);
                ludoRoom.instance.event("reDraw", [this.id, this.goalStation, this.color, true]);
                ludoRoom.instance.event("update", [this.id, this.goalStation, grid.port]);
            }
        }
    }
    public clear() {
        this.offAll();
        if (this.timeLine) {
            this.timeLine.offAll()
            this.timeLine.pause();
            this.timeLine.destroy();
            this.timeLine = null;
        }
        if (this.arrowLine) {
            this.arrowLine.offAll()
            this.arrowLine.pause();
            this.arrowLine.destroy();
            this.arrowLine = null;
        }
        this.circleAureole.clear();
        if (this._tw) {
            this._tw.complete();
            this._tw.clear();
            this._tw = null;
        }
        this._soundTimeOut && clearTimeout(this._soundTimeOut);
        if (this.displayedInStage) {
            this.removeSelf();
            this.destroy(true);
        }
        yalla.Debug.log('棋子移动 -------->>>>>> clear');

    }
}