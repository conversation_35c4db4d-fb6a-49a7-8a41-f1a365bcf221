class Audience extends ui.ludo.dialog.audienceUI {
    static _instance: Audience = null;
    static get instance(): Audience {
        if (!this._instance)
            this._instance = new Audience();
        return this._instance;
    }
    constructor() {
        super();
        if (yalla.Font.isRight()) this.close_btn.pos(22, 22);
        this.on(Laya.Event.DISPLAY, this, () => {
            this.list.array = ludoRoom.instance.getAudiencePlayers();
        })
        this.list.renderHandler = new Laya.Handler(this, (cell: Laya.Image, index: number) => {
            var head: yalla.common.Head = cell.getChildByName("head") as yalla.common.Head;
            if (!head) {
                head = new yalla.common.Head();
                head.name = "head";
                head.pos(106, 78);
                cell.addChild(head);
            }
            head.data = cell.dataSource;
            head.showFaceBox(cell.dataSource);
            // head.scale(0.8, 0.8);
            yalla.Emoji.lateLable(yalla.util.filterName((cell.dataSource as playerShowInfo).fPlayerInfo.nikeName), (cell.getChildByName("nickName") as Laya.Label), ['color', 'bold', 'fontSize'], false);
        })
    }
}