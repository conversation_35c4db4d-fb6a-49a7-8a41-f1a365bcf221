class SaveGame extends ui.ludo.dialog.saveGameUI {
    private gameData = null;
    constructor(data) {
        super();
        this.gameData = data;
        let date: string = offLine.util.getDateString();
        let promptTx: string = !!this.gameData.title ? this.gameData.title : date;
        this.input.text = promptTx;
        this.input.on(Laya.Event.INPUT, this, this.onInput);
        this.save.on("click", this, () => {
            if (!this.isSave.selected) {
                if (this.input.text.length <= 0) {
                    this.input.text = promptTx;
                }
                this.gameData.title = this.input.text;
                data.date = date;
                yalla.Native.instance.updateLocalGameData(JSON.stringify(this.gameData));
            }
            this.close();
            yalla.Native.instance.backHall();
        })
    }
    private onInput(e) {
        var len = this.input.text.length;
        if (len > 20) {
            this.input.text = this.input.text.slice(0, 20);
            return;
        }
    }
}
