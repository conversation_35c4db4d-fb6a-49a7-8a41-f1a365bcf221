class DuelKnife extends Laya.Box {
    private knifeTp: Laya.Templet = null;
    private knifeSk: Laya.Skeleton = null;
    private timeLine: Laya.TimeLine = new Laya.TimeLine();
    //该动画持续时间 1300ms
    private backRate: number = 2;
    constructor() {
        super();
        this.init();
    }
    private init() {
        this.size(126, 89);
        this.anchorX = this.anchorX = 0.5;
        this.knifeTp = new Laya.Templet();
        this.knifeTp.once(Laya.Event.COMPLETE, this, this.onComplete);
        this.knifeTp.loadAni(yalla.getSkeleton("duel_fight/duel_fight"));
    }
    private onComplete() {
        if (!this.destroyed && this.knifeTp) {
            this.knifeSk = this.knifeTp.buildArmature(0);
            this.knifeSk.pos(92, 36);
            this.knifeSk.playbackRate(this.backRate);
            this.addChild(this.knifeSk);
        }
    }
    public play(from: port, to: port) {
        this.pos(from.x - 92, from.y - 36);
        this.scale(1, 1)
        if (this.knifeSk) {
            this.knifeSk.play(0, false);
        }
        if (this.timeLine) {
            this.timeLine.reset();
            this.timeLine
                .addLabel("0", 0).to(this, to, 100, null, DuelManager.cutTime)
                .addLabel("1", 0).to(this, { scaleX: 0, scaleY: 0 }, 100, null, 0);
            this.timeLine.play("0");
        }
    }
    public clear() {
        if (this.knifeTp) {
            this.knifeTp.destroy();
            this.knifeTp = null;
        }
        if (this.knifeSk) {
            this.knifeSk.stop();
            this.knifeSk.destroy();
            this.knifeSk = null;
        }
    }
}