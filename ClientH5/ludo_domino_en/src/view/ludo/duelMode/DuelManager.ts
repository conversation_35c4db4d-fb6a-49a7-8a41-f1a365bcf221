class DuelManager {
    private mapView: DuelMap = null;
    private duelsObj = {};
    private duelsArr = [];
    private timePiece: TimePiece = null;
    private knife: DuelKnife = new DuelKnife();
    static cutTime: number = 500;//播放棋子挨一刀的时间 之后棋子移动
    constructor() { }
    public init(mapView: DuelMap, mapColor) {
        this.mapView = mapView;
        this.addTimePiece();
        this.createDuelRank(mapColor);
        this.mapView.ani_box.addChild(this.knife);
    }
    public static _instance: DuelManager = null;
    public static get instance(): DuelManager {
        return this._instance ? this._instance : this._instance = new DuelManager()
    }
    public onPlayerLeft(color: number, reflush: boolean = true) {
        var duelRank: DuelRank = this.getduelRankByColor(color);
        duelRank && (duelRank.isLeft = true);
        reflush && this.reflushRank();
    }
    public timePieceStart() {
        this.timePiece && this.timePiece.start(ludoRoom.instance.fightLeftTime);
    }
    public timePieceStop() {
        this.timePiece && this.timePiece.stop()
    }
    public showKnife(from: port, color: number) {
        var duelRank = this.getduelRankByColor(color);
        if (duelRank) {
            this.knife.play(from, {
                x: duelRank.x + 100,
                y: duelRank.y + 170
            })
        }
    }
    private addTimePiece() {
        this.timePiece = new TimePiece();
        this.timePiece.centerX = 0;
        this.timePiece.top = -90;
        this.mapView.sign_box.addChild(this.timePiece);
    }
    public refush() {
        this.resetDuelRank();
        this.reflushRank();
    }
    public resetDuelRank() {
        ludoRoom.instance.getSitPlayers().forEach(pinfo => {
            var duelRank = this.getduelRankByColor(pinfo.color);
            if (duelRank) {
                duelRank.fightNum = pinfo.fightNum;
                duelRank.fightLastTime = pinfo.fightLastTime;
            }
        })
    }
    private createDuelRank(mapColor: number) {
        this.createRealDuelRank(ludoRoom.instance.getSitPlayers());
        var margin = 35;
        for (var i = 0; i < 4; i++) {
            var color = (mapColor + i) % 4;
            var duelRank = this.getduelRankByColor(color);
            if (!duelRank) {
                duelRank = new DuelRank(null);
                this.duelsObj[color] = duelRank;
            }
            this.duelsArr.push(duelRank);
            switch (i) {
                case 0:
                    duelRank.bottom = margin;
                    duelRank.left = margin;
                    break;
                case 1:
                    duelRank.top = margin;
                    duelRank.left = margin;
                    break;
                case 2:
                    duelRank.top = margin;
                    duelRank.right = margin;
                    break;
                case 3:
                    duelRank.bottom = margin;
                    duelRank.right = margin;
                    break;
            }
            this.mapView.sign_box.addChild(duelRank);
        }
        this.reflushRank();
    }
    private createRealDuelRank(players: playerShowInfo[] = []) {
        players.forEach((player: playerShowInfo, index: number) => {
            var color = player.color;
            var duelRank = new DuelRank(color);
            duelRank.fightLastTime = player.fightLastTime;
            duelRank.fightNum = player.fightNum;
            this.duelsObj[color] = duelRank;
        })
    }
    public getduelRankByColor(color: number): DuelRank {
        return this.duelsObj[color] || null;
    }
    public onHit(chessColor: number, fightNum: number, isFouce: boolean) {
        isFouce ? Laya.timer.once(DuelManager.cutTime, this, this.addFightNum, [chessColor, fightNum]) :
            this.addFightNum(chessColor, fightNum);
    }
    private addFightNum(chessColor: number, fightNum: number) {
        var duelRank = this.getduelRankByColor(chessColor);
        if (duelRank) {
            if (fightNum)
                duelRank.fightNum = fightNum;
            duelRank.fightLastTime = ludoRoom.instance.currentTime;
            this.reflushRank();
        }
    }

    public reflushRank() {
        this.duelsArr.sort((a: DuelRank, b: DuelRank) => {
            var offsetNum = b.fightNum - a.fightNum;
            return a.isLeft ? 1 : b.isLeft ? -1 : offsetNum == 0 ? a.fightLastTime - b.fightLastTime : offsetNum
        })
        // yalla.Debug.log(this.duelsArr);
        this.duelsArr.forEach((duelRank: DuelRank, index: number) => {
            duelRank && (duelRank.rank = index + 1);
        })
    }
    public clear() {
        this.duelsArr.forEach(duelRank => {
            if (duelRank) {
                duelRank.removeSelf();
                duelRank.clear()
                duelRank.destroy();
            }
        })
        this.duelsObj = {};
        this.duelsArr = [];
        this.timePiece && this.timePiece.clear();
        this.timePiece = null;
        DuelManager._instance = null;
    }
}