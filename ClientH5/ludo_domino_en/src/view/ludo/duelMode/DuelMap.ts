class DuelMap extends ui.ludo.duelMode.duelMapUI {
    static _instance: Map = null;
    public _color: number = 0;
    private skin_id: number = 13000;
    constructor() {
        super();
        ChessManger._runwayScale = 1;
        ChessManger._normalScale = 1.2;
        ChessManger._pileScale = 0.8;
        this.mouseThrough = false;
        this.visible = false;
        DuelManager._instance && DuelManager.instance.clear();
    }
    set skinId(val: number) {
        if (val != this.skin_id && val > 13000) {
            this.skin_id = val;
        }
    }
    public stopAni() { }
    public startAni() { }
    showKillTip() { }
    hideVisibility() { }
    removeMask() { }
    showGolbleEye() { }
    private setSkin(skinid: number) {
        var [basePath, checkPath] = [yalla.File.cachePath + `/Base_${skinid}.png`, yalla.File.cachePath + `/Checkerboard_${skinid}.png`];
        if (yalla.File.existed(basePath) && yalla.File.existed(checkPath)) {
            this.check.skin = yalla.File.filePath + `Checkerboard_${skinid}.png`;
            this.bg.skin = yalla.File.filePath + `Base_${skinid}.png`;
        }
    }
    get skinId(): number {
        return this.skin_id;
    }
    private loadSk() { }
    public clear() {
        this.destroy(true);
    }
    set color(color: number) {
        this._color = color;
        var rotation = -color * 90
        this.check.rotation = rotation;
        LudoBoardData.color = this._color;
        this.visible = true;
        DuelManager.instance.init(this, color);
    }
    // getPos(val: Station, departurePos: number = 101): port {
    //     return LudoBoardData.getGridByStation(val).port;
    // }
}