class DuelRank extends ui.ludo.duelMode.duelRankUI {
    constructor(color: number = 0) {
        super();
        this.color = color;
    }
    private _color: number = 0;
    private _rank: number = 0;
    public fightLastTime: number = 0;
    private _fightNum: number = 0;
    private _isLeft: boolean = false;
    public set color(color: number) {
        if (color == null) {
            this.info.visible = false;
            this.bg.skin = "duel/no_player.png";
            return;
        }
        this._color = color;
        this.bg.skin = `duel/duel_${this._color}.png`;
        this.score_bg.skin = `duel/score_${this._color}.png`;
        this.rank_img.skin = `duel/kill_${this._color}_${this._rank}.png`;
        this.no.skin = `duel/no_${this._color}.png`;
        this.kill_score.strokeColor = ["#881816", "#146132", "#82691D", "#16517E"][this._color];
    }
    public set rank(rank: number) {
        if (this._fightNum > 0 || rank == 0) {
            this._rank = this._isLeft ? 0 : rank;
            this.rank_img.skin = `duel/kill_${this._color}_${this._rank}.png`;
        }
    }
    public set isLeft(val: boolean) {
        this._isLeft = val;
        val && (this.rank = 0);
    }
    public get isLeft(): boolean {
        return this._isLeft
    }

    private onAdd() {
        this.ani_knife.play(0, false)
        this.ani_rank.play(0, false)
    }
    public get fightNum(): number {
        return this._fightNum;
    }
    public set fightNum(fightNum: number) {
        if (fightNum > this._fightNum && yalla.Global.isFouce) this.onAdd();
        if (fightNum == 0) this.rank = 0;
        this._fightNum = fightNum;
        this.kill_score.text = String(fightNum || "-");
    }
    public clear() { }
}