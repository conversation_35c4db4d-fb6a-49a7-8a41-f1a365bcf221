class TimePiece extends ui.ludo.duelMode.timepieceUI {
    private clockTp: Laya.Templet = null;
    private clockSk: Laya.Skeleton = null;
    constructor() {
        super();
        this.addClockSk();
    }
    private _lefttime: number = 300;
    private addClockSk() {
        this.clockTp = new Laya.Templet();
        this.clockTp.once(Laya.Event.COMPLETE, this, this.onClockComplete);
        this.clockTp.loadAni(yalla.getSkeleton("duel_clock/duel_clock"));
    }
    private onClockComplete() {
        if (!this.destroyed && this.clockTp) {
            this.clockSk = this.clockTp.buildArmature(0);
            this.clockSk.pos(32, 42);
            this.addChild(this.clockSk);
            // this.clockSk.play(0, false);
            this.clockSk.play(0, false, true, 1, 2);
        }
    }
    public start(_lefttime: number = 300) {
        this._lefttime = _lefttime;
        this.stop();
        this.timer.loop(1000, this, this.countTime);
    }
    public stop() {
        this.timeTx.dataSource = {
            text: `--:--`,
            color: "#ffffff"
        }
        this.timer.clear(this, this.countTime);
    }
    private countTime() {
        if (this._lefttime > 0) {
            this._lefttime--;
            var m = String(Math.floor(this._lefttime / 60)),
                s = String(Math.round(this._lefttime % 60));
            m = m.length == 1 ? `0${m}` : m;
            s = s.length == 1 ? `0${s}` : s;
            this.timeTx.text = `${m}:${s}`;
            if (this._lefttime <= 30) this.timeTx.color = this._lefttime % 2 == 0 ? "#ffffff" : "#f82607";
            if (this._lefttime <= 60 && this._lefttime % 5 == 0 && this.clockSk) this.clockSk.play(0, false);
        } else {
            this.stop();
        }
    }
    public clear() {
        this.stop();
        this.removeSelf();
        this.destroy();
        if (this.clockTp) {
            this.clockTp.destroy();
            this.clockTp = null;
        }
        if (this.clockSk) {
            this.clockSk.stop();
            this.clockSk.destroy();
            this.clockSk = null
        }
    }
}