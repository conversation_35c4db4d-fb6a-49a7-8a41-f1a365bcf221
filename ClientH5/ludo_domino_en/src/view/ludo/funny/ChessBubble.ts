class ChessBubble extends ui.ludo.funny.chessBubbleUI {
    private sk: Laya.Skeleton;
    private tw: <PERSON><PERSON><PERSON>;
    constructor(tp: <PERSON><PERSON><PERSON>) {
        super();
        this.visible = false;
        this.sk = tp.buildArmature(1);
        this.sk.pos(40, 40);
        this.sk.scale(0.74, 0.74);
        this.bg.addChild(this.sk);
        this.name = "bubble";
    }
    show(n: string) {
        this.visible = true;
        this.scale(0, 0)
        this.sk && this.sk.play(n, true);
        this.timer.clear(this, this.hide);
        this.timer.once(1500, this, this.hide);
        this.tw = Laya.Tween.to(this, {
            scaleX: 1,
            scaleY: 1
        }, 200, Laya.Ease.backOut, Laya.Handler.create(this, () => {
            this.tw && this.tw.clear();
        }))
    }
    hide() {
        this.visible = false;
        this.sk && this.sk.stop();
        this.event("playend");
    }
}
