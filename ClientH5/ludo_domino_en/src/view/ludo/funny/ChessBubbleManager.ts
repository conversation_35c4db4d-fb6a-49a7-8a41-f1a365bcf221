class ChessBubbleManager {
    private pool: Array<ChessBubble> = [];
    private template: Laya.Templet = null;
    constructor() {
        this.template = new Laya.Templet();
        this.template.loadAni("res/sk/fastemoji/zong.sk");
    }
    play(parentBox: Laya.Sprite, n: string, pos?: any, cb: <PERSON><PERSON>.Handler = null) {
        if (!yalla.Global.isFouce) return;
        var bubble = this.getBubble();
        if (!bubble) return;
        bubble.once("playend", this, () => {
            bubble.removeSelf();
            this.pool.push(bubble);
            cb && cb.run();
        })
        if (bubble.displayedInStage) {
            bubble.removeSelf();
        }
        parentBox.addChild(bubble);
        if (pos) bubble.pos(pos.x, pos.y - 30);
        else bubble.pos(40, 6);
        bubble.show(n);
         return bubble;
    }
    clear() {
        this.pool.forEach(val => {
            val.destroy();
        })
        this.pool = [];
        this.template.destroy();
    }
    // stopAll() {
    //     this.pool.forEach(val => {
    //         val.visible && val.hide();
    //     })
    // }
    private getBubble(): ChessBubble {
        if (this.pool.length > 0) {
            return this.pool.pop();
        } else if (!!this.template && this.template.isParserComplete) {
            return new ChessBubble(this.template);
        } else {
            return null;
        }
    }
}