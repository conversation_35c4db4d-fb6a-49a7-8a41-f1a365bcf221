class Combo extends ui.ludo.funny.ComboUI {
    constructor() {
        super();
    }
    private _killNum: number = null;
    set killNum(val: number) {
        if (val != this._killNum && val > 0) {
            this._killNum = val;
            if (val > 5) {
                this.num.visible = true;
                this.num.skin = `game/x${val}.png`;
                this.bg.skin = `game/combo6.png`;
            } else {
                this.num.visible = false;
                this.bg.skin = `game/combo${val}.png`;
            }
        }
    }
    get killNum(): number { return this._killNum; }
}