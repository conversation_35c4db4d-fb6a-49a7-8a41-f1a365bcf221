class ComboManager extends Laya.Box {
    private _combo: number = 0;
    private pool: Object = {};
    private tw: <PERSON>a.Tween;
    private _comboNode: Combo = null;
    static get instance(): ComboManager {
        return this._instance ? this._instance : this._instance = new ComboManager();
    }
    static _instance: ComboManager = null;
    constructor() {
        super();
    }
    public clear() {
        for (var key in this.pool) {
            if (this.pool.hasOwnProperty(key)) {
                var element = this.pool[key] as Laya.Image;
                element.destroy();
            }
        }
        this.pool = {};
    }
    public onHit(num: number = 1/*, voice_name*/) {
        this._combo += num;
        if (this._combo > 0 && this._combo <= 12) {
            // var sound: string = this._combo <= 6 ? `${this._combo}kills` : `6kills`;
            yalla.Sound.playSound("1kills"/*, Laya.Handler.create(this, () => {
                 yalla.Sound.playSound(voice_name);
            })*/);
            this.show();
        }
    }
    public reset() {
        this._combo = 0;
    }
    private getCombo(name: string): Combo {
        var combo = this.pool[name];
        if (!combo) {
            combo = new Combo();
            this.pool[name] = combo;
            combo.killNum = this._combo;
            combo.pos(this.width / 2, this.height / 2);
        }
        return combo;
    }
    private show() {
        if (this._combo > 1 && yalla.Global.isFouce) {
            this.hide();
            this._comboNode = this.getCombo(`combo${this._combo}`);
            this._comboNode.scale(0, 0);
            this._comboNode.alpha = 0;
            this.addChild(this._comboNode);
            this.tw = Laya.Tween.to(this._comboNode, {
                scaleY: 1.2,
                scaleX: 1.2,
                alpha: 1
            }, 500, Laya.Ease.backInOut, Laya.Handler.create(this, () => {
                this.timer.once(1000, this, this.hide);
            }))
        }
    }
    private hide() {
        this.tw && this.tw.clear();
        this.timer.clear(this, this.hide);
        this._comboNode && this._comboNode.removeSelf();
        this._comboNode = null;
    }
}