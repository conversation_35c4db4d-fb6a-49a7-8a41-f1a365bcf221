class BaseGame extends Laya.Box {
    public gameUI: ui.ludo.page.gameenUI = null;
    public isOffGame: boolean = false;
    // public diceTime: number = 400;//骰子转动时间
    public diceTime: number = 200;//骰子转动时间 1.3.8改为200
    public waitTime: number = 10000;//等待时间
    public resetTime: number = 2000;//重置时间
    public mapView: any = null;
    public chooseStep: ChooseStep = null;
    public endPointAni: TerminusAni = null;
    public chessBubbleManager: ChessBubbleManager;
    private sence_id: number = 13000;
    public _isSpectator: boolean = null;
    public _myColor: number = null;
    private templet: Laya.Templet = null;
    private bgSk: Laya.Skeleton = null;
    // public rank: ludo.RoomInfoUI = null;
    public banner: LudoBanner = null;
    public _junglePropManager: JunglePropManager = null;
    public props: GridProp[] = null;
    public eventProps: EventProp[] = null;//道具掉落
    constructor() {
        super();
        ComboManager._instance = null;
        this.pageInit();
        this.addListenUIEvent();
        this.on(Laya.Event.REMOVED, this, () => {
            this.timer.clearAll(this);
        })
    }
    set audienceNum(val: number) {
        // this.gameUI.audience_num.text = String(val);
        this.banner && (this.banner.audienceNum = val);
    }
    set jungleGame(val: boolean) {
        if (val) {
            var scale = Laya.stage.height / 1334;
            this.gameUI.furniture.scale(scale, scale);
            this.gameUI.furniture.skin = "game/furniture_jungle.png";



        }
        this.mapView.jungleGame = val;
    }
    set sceneId(val: number) {
        if (val != this.sence_id && val > 13000) {
            var fevent = yalla.event.YallaEvent.instance;
            fevent.once(`BG_${val}.png`, this, () => {
                if (this.destroyed) return;
                if (this.gameUI && this.gameUI.bg) this.gameUI.bg.skin = yalla.File.filePath + `BG_${val}.png`;
            })
            yalla.File.getFileByNative(`BG_${val}.png`)
            this.sence_id = val;
            this.mapView.skinId = this.sceneId;
            if (yalla.Skin.instance.hasFurniture(val)) {
                fevent.once(`furniture_${val}.png`, this, () => {
                    if (this.destroyed) return;
                    if (this.gameUI && this.gameUI.furniture) {
                        var scale = Laya.stage.height / 1334;
                        this.gameUI.furniture.scale(scale, scale);
                        this.gameUI.furniture.skin = yalla.File.filePath + `furniture_${val}.png`;
                    }
                })
                yalla.File.getFileByNative(`furniture_${val}.png`)
            }
            if (yalla.Skin.instance.isDynamic(val)) {
                fevent.once(`BGANI_${val}.sk`, this, () => {
                    if (this.destroyed) return;
                    this.templet = new Laya.Templet();
                    this.templet.on(Laya.Event.COMPLETE, this, this.loadSk);
                    this.templet.on(Laya.Event.ERROR, this, (e) => {
                        yalla.Debug.log('====BaseGame isDynamic 3=====');
                    })
                    this.templet.loadAni(yalla.File.filePath + `BGANI_${val}.sk`);
                })
                fevent.once(`BGANI_${val}.png`, this, () => {
                    yalla.File.getFileByNative(`BGANI_${val}.sk`);
                })
                yalla.File.getFileByNative(`BGANI_${val}.png`);
            }
        }
    }
    get sceneId(): number {
        return this.sence_id;
    }
    public onBackPressed() {
        yalla.Debug.log("onBackPressed-----");
        this.event(yalla.Native.instance.Event.ONBACKPRESSED);
    }
    protected loadSk() {
        if (this.templet) {
            this.bgSk = this.templet.buildArmature(0);
            this.gameUI.bg.addChildAt(this.bgSk, 0);
            this.bgSk.size(Laya.stage.width, Laya.stage.height);
            var sX = Laya.stage.width / 746;
            var sY = Laya.stage.height / 1330;
            if (sX > sY) {
                this.bgSk.scale(sX, sX);
            } else {
                this.bgSk.scale(sY, sY);
            }
            this.bgSk.pos(Laya.stage.width / 2, Laya.stage.height / 2);
            this.bgSk.play(0, true);
            yalla.event.YallaEvent.instance.on("stopAni", this, this.stopAni);
            yalla.event.YallaEvent.instance.on("startAni", this, this.startAni);
        }
    }
    public stopAni() {
        this.bgSk && this.bgSk.paused();
        this.mapView && this.mapView.stopAni();
    }
    public startAni() {
        this.bgSk && this.bgSk.play(0, true);
        this.mapView && this.mapView.startAni();
    }
    //页面UI初始化
    protected pageInit() {
        this.size(Laya.stage.width, Laya.stage.height);
        if (yalla.Font.isRight()) {
            this.gameUI = new ui.ludo.page.gamearUI();
        } else {
            this.gameUI = new ui.ludo.page.gameenUI();
        }
        this.banner = new LudoBanner();
        this.gameUI.addChild(this.banner.view);
        this.onResize();
        this.addChild(this.gameUI);
    }
    onResize() {
        if (!this.destroyed) {
            this.size(Laya.stage.width, Laya.stage.height);
            if (this.banner) {
                var screen = yalla.Screen;
                if (screen.hasHair)
                    this.banner.hairHeight = screen.hairHeight ? screen.hairHeight / screen.screen_scale : 50;
                else this.banner.hairHeight = 0;
            }
        }
    }
    public addMapView() {
        var isDuel = ludoRoom.instance.isDuel;
        if (isDuel) {
            this.mapView = new DuelMap();
        } else if (ludoRoom.instance.nightGame()) {
            this.mapView = new NightMap();
        } else {
            this.mapView = new Map();
        }
        // if (yalla.Global.ProfileInfo.isDebug) {
        //     if (this.mapView.close_socket) {
        //         (this.mapView.close_socket as Laya.Button).visible = true
        //         this.mapView.close_socket.on("click", this, () => {
        //             ludo.GameSocket.instance.closeNet();
        //         });//测试网络掉线
        //         (this.mapView.flush_gamedata as Laya.Button).visible = true
        //         this.mapView.flush_gamedata.on("click", this, () => {
        //             ludo.GameSocket.instance.flushGameData()
        //         });//测试网络掉线
                
        //     }
        // }
        ChessManger.instance.init(isDuel);
        this.gameUI.map_layer.addChild(this.mapView);
        this.chessBubbleManager = new ChessBubbleManager();
        this.chooseStep = new ChooseStep();
        this.mapView.addChild(this.chooseStep);
        this.mapView.ani_box.addChild(ComboManager.instance);
        ComboManager.instance.size(this.mapView.ani_box.width, this.mapView.ani_box.height);
        this.chooseStep.hide();
        this.endPointAni = new TerminusAni();
        this.mapView.ani_box.addChild(this.endPointAni);
    }
    get junglePropManager(): JunglePropManager {
        return this._junglePropManager ? this._junglePropManager : this._junglePropManager = new JunglePropManager(this.mapView);
    }
    protected loseNetWork() { }
    public onSocketClose() { }
    protected addListenUIEvent() {
        this.gameUI.reset_info.on("click", this, this.stopEvent);
    }
    protected removeEventListener() {
        this.gameUI.reset_info.off("click", this, this.stopEvent);
    }
    public stopEvent(e: Laya.Event) {
        e.stopPropagation();
    }
    protected clear() {
        if (this.mapView) {
            this.mapView.clear();
            this.mapView.destroy(true);
            this.mapView = null;
        }
        if (this.endPointAni) {
            this.endPointAni.destroy(true);
            this.endPointAni = null;
        }
        if (this.chooseStep) {
            this.chooseStep.destroy(true);
            this.chooseStep = null;
        }
        if (this.endPointAni) {
            this.endPointAni.clear();
            this.endPointAni.destroy(true);
            this.endPointAni = null;
        }
        if (this.templet) {
            this.templet.destroy();
            this.templet.offAll();
            this.templet = null;
        }
        if (this.bgSk) {
            this.bgSk.destroy(true);
            this.bgSk = null;
        }
        if (this._junglePropManager) {
            this._junglePropManager.clear();
            this._junglePropManager = null;
        }
        if (this.banner) {
            this.banner.clear()
            this.banner = null;
        }
    }
    public pileChesses(chesses: LudoChess[]): LudoChess[] {
        if (chesses && chesses.length > 0) {
            if (chesses.length == 1) {
                var ch: LudoChess = chesses[0];
                if (ch && this.mapView.chess_box.contains(ch)) {
                    // ch.pileScale().pivotX = ch.width / 2;
                    this.mapView.chess_box.setChildIndex(ch, 0);
                    ch.scale(ChessManger._runwayScale, ChessManger._runwayScale).pivotX = ch.width / 2;
                }
            } else {
                var pivot: number = 30 / (chesses.length - 1);
                var w: number = chesses[0].width / 2 - 15;
                this.sortChesses(chesses).forEach((chess: LudoChess, index: number) => {
                    if (this.mapView.chess_box.contains(chess)) {
                        chess.pileScale().pivot(w + pivot * index, 40);
                        this.mapView.chess_box.setChildIndex(chess, index);
                    }
                })
            }
        }
        return chesses || [];
    }
    private sortChesses(chesses: LudoChess[]): LudoChess[] {//按照颜色调整棋子的层级顺序
        return chesses.sort((a: LudoChess, b: LudoChess) => {
            return a.childChessId > 0 ? -1 : b.childChessId > 0 ? 1 : a.color == this._myColor ? 1 : -1;
        })
    }
    public playChessEmoji(parentBox: Laya.Sprite, n: string, pos?: any, cb: Laya.Handler = null) {
        if (this.chessBubbleManager && !ludoRoom.instance.nightGame()) {
            this.chessBubbleManager.play(parentBox, n, pos, cb);
        }
    }
    private pileDuelChess(chesses: LudoChess[], cutChessColor: number): LudoChess[] {
        chesses.sort((a, b) => {
            return a.color == cutChessColor ? 1 : -1;
        }).forEach((chess: LudoChess, index: number) => {
            if (this.mapView.chess_box.contains(chess)) {
                chess.pileScale();
                if (index == 0) {
                    chess.pivot(55, 55)
                } else {
                    chess.pivot(25, 25)
                }
                this.mapView.chess_box.setChildIndex(chess, index);
            }
        })
        return chesses
    }
    private sameColor(chesses: LudoChess[]): boolean {
        return chesses[0].color == chesses[1].color;
    }
    public pileWithGrid(grid: LudoGrid, excludeChessId: number = null, cutChessColor: number = null, isMoveEnd: boolean = false): LudoChess[] {
        // var chesses = this.getSameGridChesses(grid, excludeChessId);
        var chesses = ChessManger.instance.getSameGridChesses(grid, excludeChessId);
        if (isMoveEnd && ludoRoom.instance.isDuel && chesses.length == 2 && !grid.inSafetyArea && !this.sameColor(chesses)) {
            return this.pileDuelChess(chesses, cutChessColor);
        }
        return this.pileChesses(chesses);
    }
    public pileUp(chess: LudoChess, contain: boolean, isMoveEnd: boolean = false) {
        if (!this.mapView || !chess) return;
        if (chess.goalGrid.inEnd && contain) {
            if (this.mapView.chess_box.contains(chess)) {
                chess.scale(0.6, 0.6).pivotX = chess.width / 2;
                this.mapView.chess_box.setChildIndex(chess, 0)
            }
            return;
        }
        var chesses = this.pileWithGrid(chess.goalGrid, contain ? null : chess.id, chess.color, isMoveEnd);
        this.ninghtModeLevel(chess, chesses);
    }
    private ninghtModeLevel(chess: LudoChess, chesses: LudoChess[]) {
        var _curtainMask = this.mapView._curtainMask as Curtain;
        if (_curtainMask) {
            if (chesses.length == 1) {
                var ch: LudoChess = chesses[0];
                ch && _curtainMask.isOurChess(ch.color) && _curtainMask.setoverlapLevel(ch.id, 1);
            } else if (_curtainMask.isOurChess(chess.color)) {
                var mychesses: Array<LudoChess> = chesses.filter(ch => {
                    return ch.color == chess.color;
                })
                var myChLen = mychesses.length;
                mychesses.forEach(ch => {
                    _curtainMask.setoverlapLevel(ch.id, myChLen);
                })
            }
        }
    }
    protected onIndexMsg(cmd: number, msg: any) { }
    protected gameFinish(msg) { }
    public onBlur(): void { }
    public onForce(): void { }
    public isOther(): boolean { return false }
}