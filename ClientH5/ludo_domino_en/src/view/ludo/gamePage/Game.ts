class Game extends BaseGame {
    private resultView: any;
    private fastChat: yalla.common.FastChat;
    private chat: any;
    protected activated_id: number = null;
    protected moveChesses: Array<any> = [];
    private throwBtn: Laya.Image;
    private isTrust: boolean = false;//是否托管
    private readNum: number = 0;
    private isFocusChanges: boolean = false;
    // private luckFlyDiceFactory: LuckFlyDiceFactory = null;
    private leftTime: number = null;
    private timeLine: Laya.TimeLine;
    private isOver = false;
    private quickMove: boolean = false;
    private _group: number = null;
    public resetCd: Ludo.CdAni;//重置Cd
    private _movingChess: LudoChess = null;
    public playerPool: Object = {};
    private queue: Ludo.Queue = null;    //队列
    public playerStatus: number = 0;
    private chooseMsg = null;
    private _buyCoin: boolean = false;
    private winTip: ui.ludo.sub.win_tipUI = null;
    private waitThrowTime: number = 0;
    private waitChooseChessTime: number = 0;
    private isQueue: boolean = false;
    constructor() {
        super();
        yalla.Voice.instance.reset();
        yalla.Global.IsGameOver = false;
        this.banner.on("exit", this, this.exitHander);
        this.on(yalla.Native.instance.Event.ONBACKPRESSED, this, this.exitHander);
        this.queue = new Ludo.Queue();
    }
    set isSpectator(val: boolean) {
        if (this._isSpectator == val) return;
        this._isSpectator = val;
        if (!val) {
            this.resetCd = new Ludo.CdAni(this.gameUI.reset.width / 2 - 4, this.waitTime);
            this.gameUI.myHander.addChild(this.resetCd);
            this.resetCd.pos(this.gameUI.reset.x + 4, this.gameUI.reset.y + 5);
            this.chat = new LudoChat(this.banner.view.bg.height);
        } else {
            // if (!ludoRoom.instance.isPrivate && !ludoRoom.instance.isDuel)
            //     this.banner.status = 1;
            // this.gameUI.audience_btn.visible = true;
            this.chat = new LiveChat(this.banner.view.bg.height);
        }

        var screen = yalla.Screen;
        if (screen.hasHair) {
            var hairHeight = screen.hairHeight ? screen.hairHeight / screen.screen_scale : 50;
            this.chat.bottom = hairHeight;
        }
        this.addChild(this.chat);
        this.chat.zOrder = 991;
        this.gameUI.reset.visible = !val
        // this.gameUI.muteSpectator_btn.visible = !val;
        // this.gameUI.store_btn.visible = !val;
        this.banner.isSpectator = val
    }
    // get msgProgress(): number {
    //     if (this.queue) {
    //         return this.queue.progress;
    //     } else {
    //         return 0;
    //     }
    // }
    get isSpectator(): boolean {
        return this._isSpectator;
    }
    set group(n: number) {
        if (this._group == n) return;
        if (this.banner)
            this.banner.setGroup(n);
        this._group = n;
    }
    get group(): number {
        return this._group;
    }
    set myColor(color: number) {//主玩家颜色
        this._myColor = color;
        this.mapView.color = this.myColor;
    }
    get myColor(): number {
        return this._myColor;
    }
    public onSocketClose() {
        this.activated_id = null;
        this._junglePropManager && this._junglePropManager.clear();
        this.timer.once(5000, this, this.removeChessEvent);
    }
    public onSocketConnect() {
        this.timer.clear(this, this.removeChessEvent);
    }
    public clearQueue() {
        this.queue && this.queue.reset();
    }
    public isOther() {
        return this.activated_id !== yalla.Global.Account.idx;
    }
    public onQuitGame() {
        if (ludo.GameSocket._instance && ludo.GameSocket.instance.connected) {
            ludo.GameSocket.instance.quitRoom();
        }
        yalla.Global.Account.token = "";
        this.timer.once(2000, this, () => {
            yalla.util.backHall(yalla.Native.instance.isBack)
        });
    }
    public onForce(enforce: boolean = false) {//获得焦点
        if (!enforce && yalla.Global.isFouce && yalla.Global.IsGameOver) return;
        yalla.Sound.stopAllSound();
        this.timer.clear(this, this.showBuffDelay);
        Laya.timer.callLater(this, () => {
            var nightMask = this.mapView ? this.mapView._curtainMask : null;
            ChessManger.instance.each((chess: LudoChess) => {
                if (chess && !chess.destroyed) {
                    chess.clearTimeLine();
                    if (chess.goalGrid && !chess.goalGrid.inHome && chess.childChessId < 0 && chess.wayStation.gridPosition == chess.goalStation.gridPosition && !chess.choose)
                        this.pileUp(chess, true);
                    if (nightMask && chess.color == this.myColor) {
                        nightMask.createBrillancyArea(chess.id).pos(chess.x, chess.y);
                        nightMask.reDraw(chess.id, chess.goalStation, chess.color, true);
                        nightMask.update(chess.id, chess.goalStation);
                    }
                }
            })
            for (var key in this.playerPool) {
                if (this.playerPool.hasOwnProperty(key)) {
                    var player: GamePlayer = this.playerPool[key];
                    if (player && !player.destroyed) {
                        // if (player.exp) player.exp.alpha = 0;
                        player.resetHeadScale();
                        if (this.activated_id != player.idx) {
                            if (player.idx == yalla.Global.Account.idx)
                                player.clearDice(this.isTrust);
                            else
                                player.clearDice(false);
                        }
                    }
                }
            }
        })
        if (this.gameUI) {
            this.changeTrustAni(this.gameUI.trust_jump.displayedInStage);
            if (this.gameUI.throw_jump.displayedInStage) {
                this.gameUI.throw_jump.play(0, true);
            } else {
                (this.gameUI.throw_jump as Laya.Animation).gotoAndStop(0);
            }
        }
        this.moveChesses && this.moveChesses.forEach((arr: Array<any>) => {
            var ch: LudoChess = arr instanceof Array ? arr[0] : null;
            if (ch && ch instanceof LudoChess) ch.onForce();
        })
        if (this._movingChess) {//用于清理老虎爪印
            if (this._movingChess.moveType !== ChessMoveType.MOVE_TIGER) {
                this._junglePropManager && this.junglePropManager.hideTigerClaw();
            }
        } else {
            this._junglePropManager && this.junglePropManager.hideTigerClaw();
        }
        super.startAni();
        yalla.Debug.log('game onForce:' + Date.now() + "yalla.Global.isFouce:" + yalla.Global.isFouce);
        if (!yalla.Global.isFouce) this.isFocusChanges = true;
    }
    public onBlur() {//失去焦点
        yalla.Sound.stopAllSound();
        super.stopAni();
        this.timer.runTimer(this, this.showBuffDelay);
        for (var key in this.playerPool) {
            if (this.playerPool.hasOwnProperty(key)) {
                var player: GamePlayer = this.playerPool[key];
                if (player) { player.stop() }
            }
        }
        if (this.gameUI) {
            this.gameUI.trust_jump.displayedInStage && this.changeTrustAni(false);
            this.gameUI.throw_jump.displayedInStage && (this.gameUI.throw_jump as Laya.Animation).gotoAndStop(0);
        }
        if (this._movingChess) {
            this._movingChess.onBlur();
        }
        this.queue.items.forEach((msg) => {
            this.processMsg(msg["cmd"], msg["msg"]);
        })
        this.isQueue = false;
        yalla.Debug.log('game onBlur:' + Date.now() + "yalla.Global.isFouce:" + yalla.Global.isFouce);
    }

    public update() {
        this.updateUndoUI();
        this.checkReset();
        if (this.banner) {
            this.banner.updateAudiencePLayer();
            this.banner.updateMoney();
        }
    }
    protected addEvents() {
        this.gameUI.reset.on("click", this, this.showResetInfo);
        this.gameUI.quick_buy_btn.on("click", this, this.buyCoin);
        this.gameUI.on("click", this, this.pageHander);
        yalla.Mute.event.on(yalla.Mute.MUTE, this, this.onMute)//监听举报
        yalla.Mute.event.on(yalla.Mute.UNMUTE, this, this.unMute);//监听取消举报
        this.mapView.chess_box.on("click", this, this.clickChessBox);
        yalla.event.YallaEvent.instance.on("ludo_chat_my", this, this.onChat);//自己发送的消息
        yalla.common.InteractiveGiftAnimation.Instance.on(yalla.common.InteractiveGiftAnimation.Instance.Event.UpdateGiftIcon, this, this.updateGiftIcon);
        yalla.Native.instance.on(yalla.Native.instance.Event.ONASSETSCHANGE, this, this.onAssetsChange);
    }
    private onAssetsChange(data) {
        if (data) {
            if (data.diamond != undefined) {
                ludo.Global.myGold = parseInt(data.diamond || 0);
            }
            if (data.money != undefined) {
                ludo.Global.myMoney = parseInt(data.money || 0);
            }
            this.update();
        }
    }
    private clickChessBox(e: Laya.Event) {
        var [mouseX, mouseY] = [e.target.mouseX, e.target.mouseY];
        for (var index = 0; index < this.moveChesses.length; index++) {
            var chArr = this.moveChesses[index];
            var ch = chArr[0];
            var move = chArr[1];
            var [x, y] = [ch.x, ch.y];
            if (mouseX < x + 60 && mouseX > x - 60 && mouseY < y + 60 && mouseY > y - 60) {
                ch.clickArea.event("click");
                return;
            }
        }
    }
    private onMute(idx: number) {
        var player: GamePlayer = this.filtratePlayer(idx);
        player && yalla.Voice.instance.muteRemoteAudioStream(idx, true, (val, uid) => {
            player = this.filtratePlayer(uid);
            if (val == 0 && player) {
                this.timer.clear(this, player.pause);
                if (player) {
                    player.isMute = true;
                    player.playVoiceAni(0, false, "stop");
                }
            }
        })
    }
    private unMute(idx: number) {
        var player: GamePlayer = this.filtratePlayer(idx);
        if (player) {
            yalla.Voice.instance.muteRemoteAudioStream(idx, false, (val, uid) => {
                player = this.filtratePlayer(uid);
                if (val == 0 && player) {
                    player.isMute = false;
                    player.pause();
                }
            })
        }
    }
    public pageHander(e: Laya.Event) {
        if (this.gameUI.reset_info.visible) {
            this.gameUI.reset_info.visible = false;
        }
        if (this.gameUI.quick_buy_btn.visible) {
            this.gameUI.quick_buy_btn.visible = false;
        }
        if (this.chooseStep && this.chooseStep.visible) {
            this.chooseStep.hide();
        }
        this.chat && this.chat.hideAllChat();
        this.banner && this.banner.pageHander();
        yalla.common.InteractiveGift.Instance.hideGiftView();
    }

    public exitHander() {
        var view;
        var myInfo = ludo.Global.myInfo;
        if (myInfo && myInfo.playerShowInfo && myInfo.playerShowInfo.winIndex > 0) {//已经获得名次的玩家
            var msg = "You could choose to leave with rewards when you win the 1st or 2nd place.";
            if (ludoRoom.instance.isPrivate) {
                msg = 'Are you sure you want to quit the\ngame?';
            }
            yalla.common.Confirm.instance.isExit = true;
            yalla.common.Confirm.instance.showConfirm(msg,
                Laya.Handler.create(this, () => {
                    this.buryPointExit("Confirm");
                    var resultMsg = ludoRoom.instance.resultMsg();
                    if (resultMsg && resultMsg.player) {
                        var len = resultMsg.player.length;
                        for (let index = 0; index < len; index++) {
                            var element = resultMsg.player[index];
                            if (element.fPlayerInfo && element.fPlayerInfo.idx == yalla.Global.Account.idx) {
                                element.propNum = this.banner ? this.banner.curCount : 0;
                            }
                        }
                    }
                    this.gameOver(resultMsg, false, yalla.Global.Account.isPrivate === 1);
                }),
                Laya.Handler.create(this, () => {
                    this.buryPointExit("Cancel");
                    yalla.common.Confirm.instance.close();
                }), ["Confirm", "Cancel"]);
        } else {//未获得名次的玩家
            var msg: string = "Are you sure you want to quit the\ngame? You will lose bets!";
            if (ludoRoom.instance.isPrivate) {//私人房间
                msg = 'Are you sure you want to quit the\ngame?';
            } else if (!ludo.Global.inSit()) {//观战
                msg = "Are you sure you want to quit the game? You are watching the game.";
            } else if (ludoRoom.instance.isActivityProp) {//道具掉落
                msg = "Are you sure you want to quit the game? You will lose bets and cannot get activity props!";
            } else if (ludoRoom.instance.isTeamGame()) {//组队
                msg = "Are you sure to quit the game? You will lose bets and you cannot team up with others for a period of time.";
            } else {//刚开局
                var room = ludoRoom.instance;
                var now = new Date().getTime() / 1000;
                if (room.gameBeginTime + room.quitPunishTime > now) {
                    msg = "Are you sure you want to quit the game? You will lose bets and may not be able to match the players normally for a while!"
                }
            }
            yalla.common.Confirm.instance.isExit = true;
            yalla.common.Confirm.instance.showConfirm(msg,
                Laya.Handler.create(this, () => {
                    // yalla.common.Confirm.instance.connect.offAll()
                    try {
                        ludo.GameSocket.instance.quitRoom();
                    } catch (error) { }
                    yalla.util.sendExitType(ExitType.INITIATIVE_QUIT);
                    this.timer.clear(this, this.exitNow);
                    // 此处延时返回大厅 为了处理服务端没有返回退出或锦标赛结算的消息的情况下无法返回大厅问题
                    var time = yalla.Global.Account.isPrivate == 3 ? 4000 : 2000;
                    this.timer.once(time, this, this.exitNow);
                    this.buryPointExit("Confirm");
                }),
                Laya.Handler.create(this, () => {
                    yalla.common.Confirm.instance.close();
                    this.buryPointExit("Cancel");
                }), ["Confirm", "Cancel"]);
        }
    }
    private buryPointExit(category: string) {
        if (!this._isSpectator)
            switch (category) {
                case "Confirm":
                    yalla.Native.instance.mobClickEvent(buryPoint.GAME_EXIT_CONFIRM);
                    break;
                case "Cancel":
                    yalla.Native.instance.mobClickEvent(buryPoint.GAME_EXIT_CANCEL);
                    break;
            }
    }
    private exitNow(isFinish: boolean = false) {
        var native = yalla.Native.instance;
        switch (yalla.Global.Account.isPrivate) {
            case 3:
                if (this.filtratePlayer(yalla.Global.Account.idx) && !isFinish) return;
                native.event(native.Event.BACKCHAMPION, [{ winnerId: -1 }]);
                break;
            case 4:
                if (this.isSpectator && !yalla.Global.Account.needBackHall) {
                    native.backHall(native.isBack, { to: "vip", gameId: yalla.Global.Account.gameId }, 500);
                } else {
                    native.backHall(native.isBack, null, 500);
                }
                break;
            default:
                native.backHall(native.isBack, null, 500);
                break;
        }
        this.timer.clearAll(this);
        this.removeEventListener();
    }
    /**
     * 语音功能及按钮初始化
     */
    private voiceInit() {
        yalla.Voice.instance.on("join", this, this.joinVoiceCallback);
        yalla.Voice.instance.on("change", this, this.voiceChange);
        if (!yalla.Voice.instance.token) {
            ludo.GameSocket.instance.getToken();
        }
    }
    public voiceTokenInit(token: string, cName: string) {
        var voice: yalla.Voice = yalla.Voice.instance;
        if (!voice.joinSuccess) {
            [voice.token, voice.channelId] = [token, cName];
            voice.joinTimes = 0;
            voice.joinGameRoomWithToken(yalla.Global.Account.idx);
        } else {
            this.joinVoiceCallback();
        }
    }
    private voiceChange(isOpen: boolean) {
        if (yalla.Global.Account.idx) {
            var player = this.filtratePlayer(yalla.Global.Account.idx);
            player && player.playVoiceAni(0, false, isOpen ? "pause" : "stop");
        }

    }

    public joinVoiceCallback() {
        if (yalla.Global.Account.isLimitVisitor && ludoRoom.instance.isPrivate) return;//沙特游客账号不可开麦
        // if (yalla.Global.Account.isLimitVisitor) return;//阿联酋游客账号不可开麦 1.3.6把模式限制去掉
        if (yalla.Mute.isBanTalk) return;//后台管理员禁言用户
        if (Laya.LocalStorage.getJSON("voice_open").isOpen && !this._isSpectator) {
            yalla.Voice.instance.enableAudio(() => {
                this.chat && this.chat.trunVoice();
                if (yalla.Global.Account.novice) {
                    yalla.Global.Account.novice = 0;
                    Laya.LocalStorage.setJSON("voice_first", { isFirst: false });
                    var noviceTip = new ui.ludo.novice.novice_tipUI();
                    if (yalla.Font.isRight()) {
                        noviceTip.tip.left = 140;
                        noviceTip.tip.align = "right";
                    }
                    noviceTip.mask_bg.visible = false;
                    noviceTip.voice_btn.visible = false;
                    this.addChild(noviceTip);
                    var onClick = function () {
                        yalla.Native.instance.noviceComplete(true);
                        noviceTip.destroy(true);
                        noviceTip.tip_ui.off("click", null, onClick);
                        noviceTip.next_btn.off("click", null, onClick);
                    }
                    noviceTip.tip_ui.once("click", null, onClick);
                    noviceTip.next_btn.once("click", null, onClick);
                } else if (!Laya.LocalStorage.getJSON("voice_first")) {
                    if (this.chat && this.chat.voice_btn) {
                        Laya.LocalStorage.setJSON("voice_first", { isFirst: false });
                        var point: Laya.Point = this.chat.voice_btn.localToGlobal(new Laya.Point(), true);
                        point.x += 28;
                        var tip = new VoiceTip(point);
                        this.addChild(tip);
                        tip.on("click", this, () => {
                            tip.removeSelf();
                        })
                    }
                }
            });
        }
        // if (this.timer) {
        //     this.timer.once(1000, this, this.updateMutePlayer);
        // }
        this.updateMutePlayer();
    }

    private updateMutePlayer() {
        ludoRoom.instance.muteAudiencePlayers();
        ludoRoom.instance.sitPlayers.forEach(pInfo => {
            var idx = pInfo.fPlayerInfo.idx;
            var player = this.filtratePlayer(idx);
            if (player) {
                if (yalla.Mute.muted(idx)) {
                    yalla.Voice.instance.muteRemoteAudioStream(idx, true, (val, uid) => {
                        yalla.Debug.log('ludo updateMuteVoice():' + val + "uid:" + uid);
                        if (val == 0) {
                            var cbPlayer = this.filtratePlayer(uid);
                            if (cbPlayer) {
                                cbPlayer.playVoiceAni(0, false, "stop");
                                cbPlayer.isMute = true;
                            }
                        } else {
                            yalla.Voice.instance.muteRemoteAudioStream(uid, false, (val, uid) => {
                                if (val == 0) {
                                    yalla.Mute.remove(uid);
                                }
                                yalla.Debug.log(' ludo 屏蔽别人失败后，取消屏蔽 updateMuteVoice():' + val);
                            });
                        }
                    })
                } else {
                    yalla.Voice.instance.muteRemoteAudioStream(idx, false, null);
                }
            }
            // player && player.updateMuteVoice();
        })
    }
    private updateUndoUI() {
        var undo = ludo.Undo.instance;
        this.gameUI.round_count_text.text = String(undo.residueTurnReThrowNum);//当前回合剩余重置次数
        yalla.Debug.log("undo 当前回合剩余重置次数" + String(undo.residueTurnReThrowNum))
        this.gameUI.count_text.text = String(undo.residueReThrowNum);//总剩余重置次数
        if (undo.costDiamond == 0) {
            this.gameUI.useCoin.fontSize = 16;
            this.gameUI.resetCoin.pos(22, 35);
            this.gameUI.useCoin.pos(17, 8);
            this.gameUI.useCoin.text = "Free";
            return;
        }
        var diamondShort = ludo.Global.myGold < undo.costDiamond;
        if (undo.residueTurnReThrowNum <= 0 || diamondShort) {//钻石不足 当前回合次数用尽 重置按钮置灰
            this.gameUI.reset.skin = "game/btn_refresh_off.png";
            if (undo.residueReThrowNum <= 0 || diamondShort) {//总次数用尽
                this.gameUI.useCoin.fontSize = 16;
                this.gameUI.resetCoin.pos(22, 35);
                this.gameUI.useCoin.pos(17, 8);
                this.gameUI.useCoin.text = "Lack";
                return;
            } else {//购买之后&&当前次用尽
                this.gameUI.useCoin.fontSize = 20;
                this.gameUI.resetCoin.pos(38, 24);
                this.gameUI.useCoin.pos(5, 18);
            }
        } else {
            if (!this.gameUI.isreset.selected) this.gameUI.reset.skin = "game/btn_refresh_on.png";
            this.gameUI.useCoin.fontSize = 20;
            this.gameUI.resetCoin.pos(38, 24);
            this.gameUI.useCoin.pos(5, 18);
        }
        this.gameUI.useCoin.text = String(undo.costDiamond);//下次使用钻石
    }

    private getColor(idx: number, pcolor): number {
        for (let i = 0; i < pcolor.length; i++) {
            if (pcolor[i].idx == idx) {
                return pcolor[i].color;
            }
        }
    }
    public loseNetWork() {
        super.loseNetWork();
        if (this.activated_id) {
            var player: GamePlayer = this.filtratePlayer(this.activated_id);
            if (player) {
                player.stopAndShowDice(7);
                player.waitCdEnd();
            }
        }
        this.hideThrowJump();
    }
    public gameDataInit = yalla.util.throttle((msg: any) => {
        // public gameDataInit(msg: any) {
        var room = ludoRoom.instance;
        if (this._buyCoin) {
            this._buyCoin = false;
            ludo.GameSocket.instance.flush();
        }
        let sitPlayers: Array<playerShowInfo> = room.sitPlayers;
        var pcolor = msg.pcolor;
        sitPlayers.forEach((info, n) => {
            info.color = this.getColor(info.fPlayerInfo.idx, pcolor);
        })
        ludoRoom.instance.isDuel && DuelManager.instance.refush();
        if (ChessManger.instance.chessLen > 0) {
            ludo.GameSocket.instance.msgindex = 0;
            //1.3.8 fix解决服务端重启时，先进入的玩家未收到flushGameData的返回，导致不刷新棋子位置；弊端：会多次调用刷新棋子updateChess，重复执行最多3次，断线重连2次
            if (pcolor && pcolor.length) {
                for (let i = 0; i < pcolor.length; i++) {
                    for (let j = 0; j < pcolor[i].chess.length; j++) {
                        var chessInfo = pcolor[i].chess[j];
                        chessInfo && this.updateChess(chessInfo);
                    }
                }
                this.orderChess();
                for (let i = 0; i < sitPlayers.length; i++) {
                    var playerInfo: playerShowInfo = sitPlayers[i];
                    var player = this.filtratePlayer(playerInfo.fPlayerInfo.idx)
                    player && this.updatePlayer(playerInfo, player);
                }
            }
            if (this._junglePropManager) {
                this.junglePropManager.hideTigerClaw();
            }
            this.timer.clear(this, this.tigerMoveDelay);
            return;
        }
        //1.3.1 请求礼物列表数据
        ludo.GameSocket.instance.GameGiftCnfListRequest();

        var watchIdx = room.watchIdx;
        var _isSpectator = watchIdx > 0;
        this.isSpectator = _isSpectator;
        var _myColor = 0;
        this.clearQueue();
        this.loseNetWork();
        this._myColor = null;
        this.addEvents();
        this.banner.setRank();
        this.activated_id = null;
        ChessManger.instance.clear();
        this.removeChessEvent();
        this.update();
        this.group = room.group;
        yalla.Voice.instance.off("join", this, this.joinVoiceCallback);
        yalla.Voice.instance.off("change", this, this.voiceChange);
        if (_isSpectator) {//观战
            for (let i = 0; i < pcolor.length; i++) {
                if (pcolor[i].idx == watchIdx) {
                    _myColor = pcolor[i].color;
                    break;
                }
            }
            if (yalla.Global.Account.isPrivate != 4)//VIP房间观战不能听语音 1.2.3add所有观战都不能听语音 1.2.4恢复观战可听语音
                this.voiceInit();
        } else {
            for (let i = 0; i < pcolor.length; i++) {
                if (pcolor[i].idx == yalla.Global.Account.idx) {
                    _myColor = pcolor[i].color;
                    break;
                }
            }
            this.voiceInit();
            this.updateUndoUI();
            //---> 1.3.6 undo默认改为关闭状态
            // this.gameUI.isreset.selected = true;
            // this.checkReset();//<---
        }
        this.myColor = _myColor;
        this.banner.resetSysLayout();
        this.clearPlayer();

        var isDuel = ludoRoom.instance.isDuel;
        for (let i = 0; i < sitPlayers.length; i++) {
            var playerInfo: playerShowInfo = sitPlayers[i];
            var player = new GamePlayer(this.myColor, playerInfo.color, false, playerInfo);
            player.head.gameUI = this;
            this.playerPool["player_" + playerInfo.fPlayerInfo.idx] = player;
            this.updatePlayer(playerInfo, player);
            this.gameUI.players_layer.addChild(player);
            var chInfos = pcolor[i].chess;

            if (chInfos) {
                // chInfos.sort(() => Math.random() - 0.5);//测试棋子打乱后的影响
                for (let j = 0; j < chInfos.length; j++) {
                    var chessInfo = chInfos[j];
                    var chess = ChessManger.instance.getChessById(chessInfo.chessId);
                    var chColor: number = chessInfo.color;
                    if (!chess) {
                        var skinID: number = ludoRoom.instance.getChessSkinByPColor(chColor, sitPlayers);
                        chess = new LudoChess(skinID, chColor);
                        this.mapView.chess_box.addChild(chess);
                    }
                    var [area, gridPosition] = [chColor, isDuel ? 2 : 101 + j];
                    chess.homeGrid = LudoBoardData.getGridByStation({ area, gridPosition });
                    this.updateChess(chessInfo, chess);
                    ChessManger.instance.addNewChess(chess);
                }
            }
        }
        var _curtainMask = this.mapView._curtainMask as Curtain;
        if (_curtainMask) {
            ChessManger.instance.getChessesByColor(this.myColor).forEach(ch => {
                _curtainMask.createBrillancyArea(ch.id, ch.color).pos(ch.x, ch.y);
                _curtainMask.reDraw(ch.id, ch.wayStation, ch.color, true);
            })
            if (ludoRoom.instance.isTeamGame()) {
                _curtainMask.friColor = (LudoBoardData.color + 2) % 4;
                ChessManger.instance.getChessesByColor(_curtainMask.friColor).forEach(ch => {
                    _curtainMask.createBrillancyArea(ch.id, ch.color).pos(ch.x, ch.y);
                    _curtainMask.reDraw(ch.id, ch.wayStation, ch.color, true);
                })
            }
        }
        yalla.Native.instance.on(yalla.Native.instance.Event.SPEAKER, this, this.handleSpeakResponse);
        // this.timer.once(500, this, this.onForce, [true]);
        this.timer.callLater(this, this.onForce, [true])
        // }
    }, 3000)
    private handleSpeakResponse(data: Array<number>) {
        if (yalla.Global.isFouce)
            for (let i = 0; i < data.length; i++) {
                let player = this.filtratePlayer(data[i]);
                if (player) {
                    player.play();
                }
            }
    }
    /**
     * 刷新游戏数据
     */
    public flushGameData(msg) {
        // public flushGameData = yalla.util.throttle((msg) => {
        this.hideThrowJump();
        this.mapView && this.mapView._curtainMask && this.mapView._curtainMask.reset();
        var [chesses, players]: Array<any> = [msg.gamedata.chess, msg.player.player];
        if (chesses) {
            chesses.forEach(chessInfo => {
                this.updateChess(chessInfo);
            })
        }
        if (players) {
            this.timer.runTimer(this, this.stopAndShowDice);
            players.forEach((playerInfo: playerShowInfo) => {
                this.updatePlayer(playerInfo, null);
            })
            // ludoRoom.instance.isDuel && DuelManager.instance.refush();
        }
        this._junglePropManager && this.junglePropManager.hideTigerClaw();
        this.timer.once(500, this, this.onForce, [true]);
        // }, 3000)
    }
    public updatePlayer(playerInfo: playerShowInfo, pr: GamePlayer) {
        var player: GamePlayer = pr ? pr : this.filtratePlayer(playerInfo.fPlayerInfo.idx);
        var is_Trust: boolean = false;
        if (player) {
            // player.data = playerInfo;
            player.setPlayerInfo(playerInfo, this.activated_id);
            player.resetHeadScale();
            // if (player.exp) player.exp.alpha = 0;
            if (playerInfo.fPlayerInfo.idx == yalla.Global.Account.idx) {
                if (ludo.Global.myInfo)
                    ludo.Global.myInfo.playerShowInfo = playerInfo;
                this.isTrust = Boolean(playerInfo.isInSysTrust);
                if (this.isTrust) yalla.common.TipManager.instance.showTrustTip(1, this, null, ludoRoom.instance.isDuel);
                else {
                    if (yalla.common.TipManager.instance.hasTrustTip) yalla.common.TipManager.instance.showTrustTip(0, this, null, ludoRoom.instance.isDuel);
                }
                this.banner.turnmuteSpectator();
                player.isInChatOff = Boolean(ludo.muteSpectator.isMute);
                player.UI && (this.throwBtn = player.UI.dice_bg);
                if (playerInfo.winIndex < 0) {
                    this.gameUI.myHander.visible = true;
                    if (this.isTrust) {
                        player.hideName();
                        this.showTrust();
                        this.throwBtn && this.throwBtn.on("click", this, this.onTrust);
                        player.head.on("click", this, this.onTrust);
                    } else {
                        this.hideTrust(true);
                    }
                    this.hideWinTip();
                } else {
                    this.gameUI.myHander.visible = false;
                    if (this.isTrust) {
                        this.isTrust = false
                        this.hideTrust(true);
                    }
                    this.showWinTip();
                }
                player.head.trustee = this.isTrust;
                this.chat && this.chat.trunVoice();
                is_Trust = this.isTrust;
            } else {
                player.UI && (player.UI.dice_bg.mouseThrough = true);
            }
            var nums: Array<number> = playerInfo.diceNum;
            if (nums && nums.length > 0) {
                player.flushDice(nums, is_Trust);
            } else {
                player.clearDice(is_Trust);
            }
            var color: number = player.color;
            if (ludoRoom.instance.type == RoomType.MASTER || ludoRoom.instance.type == RoomType.QUICK) {//大师模式中 添加地图禁止通过标记
                let sign = (this.mapView.sign_box.getChildByName("sign" + color) as RoadBlock);
                if (playerInfo.isFighted == 0) {
                    if (!sign) {
                        var port = LudoBoardData.getPosByName(`${color}_-1`);
                        if (port) {
                            sign = new RoadBlock("sign" + color)
                            sign.pos(port.x, port.y);
                            this.mapView.sign_box.addChild(sign);
                        }
                    }
                } else {
                    sign && sign.hide();
                }
            }
            if (ludoRoom.instance.isDuel && player.isLeft) {
                DuelManager.instance.onPlayerLeft(player.color, false);
            }
        }
    }
    public updateChess(chessInfo: any, ch?: LudoChess) {
        var chess: LudoChess = ch ? ch : this.filtrateChess(chessInfo.chessId);
        if (chess) {
            switch (ludoRoom.instance.type) {
                case 1://大师模式
                    if (chessInfo['chessLinkId'] != -1) {
                        let child: LudoChess = this.filtrateChess(chessInfo['chessLinkId']);
                        if (chessInfo['isMaster']) {
                            chess.childChessId = chessInfo['chessLinkId'];
                            if (child) {
                                child.removeSelf();
                            }
                            chess.showPlieNum();
                            chess.scale(0.8, 0.8).pivotX = chess.width / 2;
                        } else {
                            chess.removeSelf();
                        }
                    } else {//重置之前叠子的状态
                        chess.hidePlieNum();
                        chess.childChessId = -1;
                        if (!this.mapView.chess_box.contains(chess)) {
                            this.mapView.chess_box.addChild(chess);
                        }
                    }
                    break;
                case 2://快速模式
                    if (chessInfo.isWin == 1) {
                        if (chess.homeStation && chess.homeStation.gridPosition != 101) {
                            chessInfo.gridPosition = chess.homeStation.gridPosition;
                            chessInfo.area = chess.color;
                        }
                    }
                    break;
            }
            chess.data = chessInfo;
            var _curtain = this.mapView ? this.mapView._curtainMask as Curtain : null;
            if (_curtain && chess.color == this.myColor) {
                _curtain.createBrillancyArea(chess.id, chess.color).pos(chess.x, chess.y);
                _curtain.reDraw(chess.id, chess.goalStation, chess.color, true);
                _curtain.update(chess.id, chess.goalStation);

            }
            if (chessInfo.gridPosition < 100 && this.mapView.chess_box.contains(chess)) {
                this.pileUp(chess, true);
            }
        }
    }

    public filtratePlayer(idx: number): GamePlayer {
        return this.playerPool["player_" + idx];
    }
    public filtrateChess(chessId: number): LudoChess {
        return ChessManger.instance.getChessById(chessId);
    }
    public clearPlayer() {
        for (var key in this.playerPool) {
            if (this.playerPool.hasOwnProperty(key)) {
                var player: GamePlayer = this.playerPool[key];
                if (player) {
                    player.clear();
                    player = null;
                }
            }
        }
        this.playerPool = {};
    }
    public gameFinish(msg) {
        this.clearQueue();
        this.onGameOver();
        yalla.common.connect.ReconnectControl.instance.connectSucss();
        yalla.common.Confirm.instance.showConfirm(msg, null, Laya.Handler.create(this, this.exitNow, [true]), ["", "Exit"]);
    }
    private nextMsg() {
        this.timer.callLater(this, () => {
            this.isQueue = false;
            if (this.queue.isEmpty()) return;
            var m = this.queue.dequeue();
            this.processMsg(m["cmd"], m["msg"]);
        })
    }
    public onIndexMsg(cmd: number, msg: any) {
        if (!this.isQueue || !yalla.Global.isFouce) {//不经过队列
            this.processMsg(cmd, msg);
        } else {//放进队列
            this.queue.enqueue(cmd, msg);
        }
        if (this.queue.size() > 10) {
            this.nextMsg();
        }
    }
    public onGameOver() {
        ludo.GameSocket.clear();
        yalla.Global.IsGameOver = true;
        if (ludoRoom.instance.isDuel) DuelManager.instance.timePieceStop()
        return this;
    }

    /** r4 r5名字特殊处理 */
    private openFrameLoop(): void {
        Laya.timer.clear(this, this.frameLoopName);
        var isHonorRoyal = true;//ludoRoom.instance.isHonorRoyal;
        // yalla.Debug.log("===ludo 是否打开frameLoop==isHonorRoyal=" + isHonorRoyal);
        if (isHonorRoyal) {
            Laya.timer.frameLoop(1, this, this.frameLoopName);
        }
    }
    /** 主界面名称  游戏结算名称 */
    private frameLoopName(): void {
        for (var k in this.playerPool) {
            var data = this.playerPool[k].data;
            var canShowRLNameAnimation = yalla.util.canShowRLNameAnimation(data);
            if (canShowRLNameAnimation) this.playerPool[k].frameLoopName();
        }

        if (this.resultView && this.resultView.displayedInStage) {
            this.resultView.frameLoopName();
        }
    }

    public processMsg(cmd: number, msg: any) {
        // if (cmd === 20) {
        //     this.progress = msg.msgindex;
        // } else {
        //     this.progress++;
        // }
        this.isQueue = true;
        switch (cmd) {
            case 20:
                if (msg.status == 4 || msg.status == 5) { this.gameFinish('The game is finished'); }
                var room: ludoRoom = ludoRoom.instance;//断线重连 ludoRoom.instance会被重置为初始状态,其它时候不会
                [room.ID, room.players, room.type, room.group, room.cost, room.showID, room.teamInfo, room.activityPropUrl, room.activityPropInfo]
                    = [msg.roomid, msg.player.player, msg.roomType, msg.group, msg.cost, msg.showRoomid, msg.teamInfo, msg.activityPropUrl, msg.activityPropInfo];
                var undo = ludo.Undo.instance.reset();
                [undo.maxReThrowNum, undo.maxTurnReThrowNum, undo.reThrowCost, undo.currentReThrowNum, undo.currentTurnReThrowNum]
                    = [msg.maxReThrowNum, msg.maxTurnReThrowNum, msg.reThrowCost, msg.currentReThrowNum, msg.currentTurnReThrowNum];
                yalla.Global.Account.isPrivate = msg.isPublic;
                if (!this.mapView) {
                    this.addMapView();
                    this.jungleGame = Boolean(msg.roomType === RoomType.JUNGLE);
                    if (!ludoRoom.instance.isDuel && !ludoRoom.instance.isPrivate)
                        this.banner.status = msg.activityPropUrl ? 2 : 1;
                    else this.banner.status = 0;
                } else if (room.isActivityProp) {
                    this.banner && this.banner.refreshActivityPropInfo();
                    this.refreshEventProps({});
                }
                yalla.Debug.log("快速进入房间消息");
                yalla.Debug.log(msg);
                // yalla.Debug.log(msg.maxReThrowNum + "--" + msg.maxTurnReThrowNum + "--" + msg.reThrowCost + "--" + msg.currentReThrowNum + "--" + msg.currentTurnReThrowNum);
                // yalla.Debug.log("quickstart chesslength" + ChessManger.instance.chessLen);
                if (ChessManger.instance.chessLen > 0) {//非首次登陆
                    //在断线重连或者登陆的时候这条消息中没有 msg.gamedata的数据
                    //在调用ludo.GameSocket.instance.flushGameData();后也会收到这条消息 这个时候msg.gamedata 有数据
                    if (msg.gamedata) {//ludo.GameSocket.instance.flushGameData走这里
                        // yalla.Debug.log("accept flushGameData");
                        this.flushGameData(msg);
                    } else {//断线重连走这里
                        yalla.Debug.log("send flushGameData");
                        ludo.GameSocket.instance.flushGameData();
                    }
                } else {//首次登陆`;
                    switch (msg.isPublic) {
                        case 0:
                            yalla.Native.instance.mobClickEvent(buryPoint.LUDO_PUBLIC_PLAYING);
                            break;
                        case 1:
                            yalla.Native.instance.mobClickEvent(buryPoint.LUDO_PRIVATE_PLAYING);
                            break;
                    }
                }
                room.firstLogin = false;
                this.sceneId = msg.sceneId;
                var playerInfo = room.getPlayerByIdx(yalla.Global.Account.idx);
                if (playerInfo) {
                    if (playerInfo.isLeft == 1) {
                        this.gameFinish('Fail to connect the server because you have left the game for a long time');
                        // ludo.GameSocket.instance.close();
                        ludo.GameSocket.instance.closeNet();//TODO::1.3.1
                        return;
                    }
                    if (playerInfo.fPlayerInfo.level <= 1) {
                        undo.reThrowCost[0] = 0;
                    }
                }
                this.removeChessEvent();
                this.nextMsg();
                // this.timer.frameOnce(5, this, () => {//1.3.4移除延迟 因为IOs语音权限弹窗会在匹配页面弹起
                yalla.Native.instance.removeMatchView();
                // })
                this.banner && this.banner.muteSpectator();
                this.openFrameLoop();
                break;
            case 21://玩家退出游戏
                let player: playerShowInfo = ludoRoom.instance.playerOut(msg.idx);
                player && this.onQuitMsg(player);
                if (msg.idx == yalla.Global.Account.idx) {
                    ludoRoom.instance.clear();
                }
                this.nextMsg();
                break;
            // case 22://加入房间
            //     break;
            case 30://玩家操作
                this.onPlayerOperate(msg);
                this.nextMsg();
                break;
            case 40:
                this.nextMsg();
                break;
            // case 41:
            //     this.gameDataInit(msg);
            //     this.nextMsg();
            // break;
            case 47://丛林模式道具
                this.refreshJungleProp(msg);
                this.nextMsg();
                break;
            case 48://丛林模式棋子状态变化
                this.chessStateChange(msg.item);
                this.nextMsg();
                break;
            case 42://道具显示
                this.refreshProps(msg);
                this.nextMsg();
                break;
            case 43://格子中棋子事件
                this.onChessEvent(msg);
                this.nextMsg();
                break;
            case 44://使用buff
                this.useBuff(msg);
                this.nextMsg();
                break;
            case 45://用户可选择移动棋子列表
                this.chooseChessMsg(msg);
                this.nextMsg();
                break;
            case 46://棋子移动
                this.moveChess(msg);
                break;
            case 50://游戏状态
                this.onPlayerStatus(msg);
                this.nextMsg();
                break;
            case 51://有玩家胜利
                this.showRank(msg);
                this.nextMsg();
                break;
            case 52:
                this.refreshEventProps(msg);
                this.nextMsg();
                break;
            default://1.2.3添加 之前没有default处理 如果有未case的 会造成没有调用nextMsg()队列卡住
                this.nextMsg();
                break;
        }
    }
    public chooseChessMsg(msg) {
        if (this.playerStatus == 4) {
            // if (this.playerStatus == 4 || this.playerStatus == 7) {
            this.chooseChessStart(msg);
        } else {
            this.chooseMsg = msg;
        }
    }
    public chooseChessStart(msg: any) {
        //todo:断线重连后补发的此条消息，添加棋子事件之后 会被补发的GamePlayerStatusResponse消息调用removeChessEvent移除掉
        //所以应该在处理GamePlayerStatusResponse中msgindexx，低于已经处理的就不处理1.3.9.2下个版本修改
        //断线重连回来，服务器会发送断线前的10条消息，此时会包含别人的棋子点亮信息，因此判断当前操作玩家是不是自己
        let isSelfOperate: boolean = false;
        if (msg && msg.idx) {
            isSelfOperate = (msg.idx == yalla.Global.Account.idx && this.activated_id == yalla.Global.Account.idx);
        } else {
            isSelfOperate = (this.activated_id == yalla.Global.Account.idx);
        }
        if (!isSelfOperate) {
            return;
        }
        if (!this.isTrust) {
            let item = msg.item;
            if (this.gameUI.isreset.selected && item.length == 1) {//只有一个棋子可以移动时 跳过选择直接移动
                let chess: LudoChess = this.filtrateChess(item[0].chessId);
                let move = {
                    station: {
                        area: item[0].area,
                        gridPosition: item[0].gridPosition
                    },
                    num: item[0].num
                }
                this.chooseMove(chess, [move]);
            } else {
                var ids = [];
                this.moveChesses = [];
                for (let i = 0; i < item.length; i++) {
                    var chessid = item[i].chessId;
                    if (ids.indexOf(chessid) >= 0) { continue; }
                    ids.push(chessid);
                    var chess: LudoChess = this.filtrateChess(chessid);
                    if (chess) {
                        chess.choose = true;
                        var pivotX: number = chess.pivotX;
                        chess.scale(ChessManger._chooseScale, ChessManger._chooseScale).pivotX = pivotX;
                        let moves = [];
                        for (let j = 0; j < item.length; j++) {
                            if (chessid == item[j].chessId) {
                                let move = {
                                    station: {
                                        area: item[j].area,
                                        gridPosition: item[j].gridPosition
                                    },
                                    num: item[j].num
                                }
                                moves.push(move);
                            }
                        }
                        this.moveChesses.push([chess, moves]);
                        chess.clickArea.on("click", this, this.clickChess, [chess, moves]);
                    }
                }
                this.moveChesses.sort((a, b) => {
                    var [aIndex, bIndex] = [this.mapView.chess_box.getChildIndex(a[0]), this.mapView.chess_box.getChildIndex(b[0])];
                    return aIndex > bIndex ? 1 : -1;
                })
                var numChildren = this.mapView.chess_box.numChildren;
                // var len = ChessManger.instance.chessLen - 1;
                var len = ChessManger.instance.chessLen - this.moveChesses.length;
                // console.log(ChessManger.instance.chessLen, this.moveChesses);
                this.moveChesses.forEach((ch, i) => {
                    var ch0 = ch[0], chidIndex = len + i;
                    if (this.mapView.chess_box.contains(ch0) && chidIndex < numChildren)
                        this.mapView.chess_box.setChildIndex(ch0, chidIndex);
                })
                this.quickMove = true;
                Laya.timer.once(6000, this, this.quickMoveSign);
            }
            this.waitChooseChessTime = Date.now();
        }
        this.chooseMsg = null;
        this.nextMsg();
    }
    private quickMoveSign() {//客户端快读移动标记 超时必须等待服务端返回移动
        this.quickMove = false;
    }
    /**
     * 移除选择棋子的事件
     */
    public removeChessEvent() {
        this.chooseStep && this.chooseStep.hide();
        for (let i = 0; i < this.moveChesses.length; i++) {
            let chess: LudoChess = this.moveChesses[i][0];
            chess.choose = false;
            if (chess.goalStation.gridPosition < 100)
                this.pileUp(chess, true);
            chess.clickArea.off("click", this, this.clickChess);
        }
        this.moveChesses = [];
        if (!this.isTrust && this.waitChooseChessTime) {//选择棋子用时埋点
            var now = Date.now(),
                time = now - this.waitChooseChessTime;
            if (typeof time === 'number' && !isNaN(time) && time < 11000) {
                yalla.util.clogDBAmsg('10139', JSON.stringify({ time }));
            }
        }
        this.waitChooseChessTime = 0;
    }
    public sendChooseMsg(msg: any, notLinkChess: boolean) {
        ludo.GameSocket.instance.chooseChess(msg);
        msg['type'] = 0;
        Laya.timer.clear(this, this.quickMoveSign);
        if (this.quickMove && notLinkChess) {//叠子的棋子不自行移动(会有linkchess和chessid与服务端不同步的问题),6秒后不自行移动
            this.quickMoveSign()
            // this.moveChess(msg);
            msg.command = 46;
            this.processMsg(46, msg)
        }
        // yalla.CLogDBA.instance.record_request(yalla.CLogDBA_EventID.Ludo_ChessMove_Resp);
    }
    public chooseMove(chess, moves: Array<any>) {
        this.chooseStep && this.chooseStep.hide();
        // this.resetCd && this.resetCd.end();
        this.resetEnd();
        if (chess.wayStation.gridPosition > 100 && ludoRoom.instance.type != RoomType.FIRE) {//从出生点将要出发的棋子
            let msg = {
                operate: 6,
                roomid: ludoRoom.instance.ID,
                idx: yalla.Global.Account.idx,
                chessId: chess.id,
                chooseNum: 6,
                area: chess.color,
                gridPosition: 2
            }
            this.sendChooseMsg(msg, chess.childChessId <= 0);
        } else if (moves.length == 1) {//只有一个步数 跳过选择直接移动
            let msg = {
                operate: 6,
                roomid: ludoRoom.instance.ID,
                idx: yalla.Global.Account.idx,
                chessId: chess.id,
                chooseNum: moves[0].num,
                area: moves[0].station.area,
                gridPosition: moves[0].station.gridPosition
            }
            this.sendChooseMsg(msg, chess.childChessId <= 0);
        } else if (this.chooseStep) {//多个步数可以选择
            let nums = [];
            for (let i = 0; i < moves.length; i++) {
                nums.push(moves[i].num);
            }
            this.chooseStep.pos(chess.x + 7, chess.y);
            // this.chooseStep.hide();
            this.chooseStep.offAll();
            this.chooseStep.once("choose", this, (num: number) => {
                this.chooseStep && this.chooseStep.hide();
                let index = nums.indexOf(num);
                if (index > -1) {
                    let msg = {
                        operate: 6,
                        roomid: ludoRoom.instance.ID,
                        idx: yalla.Global.Account.idx,
                        chessId: chess.id,
                        chooseNum: num,
                        area: moves[index].station.area,
                        gridPosition: moves[index].station.gridPosition
                    }
                    this.sendChooseMsg(msg, chess.childChessId <= 0);
                }
            })
            this.chooseStep.show(nums, chess.color);
        }
    }
    public clickChess(chess, moves: Array<any>) {
        if (arguments[2]) arguments[2].stopPropagation();
        this.chooseMove(chess, moves);
    }
    public showGoldDice(port: port, color: number, idx: number) {
        var player: GamePlayer = this.filtratePlayer(idx);
        if (player && player.head) {
            port.x += 25;
            port.y += 25;
            var endpoint: port = player.head.localToGlobal(new Laya.Point(), true);
            endpoint.x += 62;
            endpoint.y += 62;
            var angel = yalla.util.angle(port, endpoint);
            var flyDice: LuckDice = LudoBuffManager.instance.getLuckDice();
            flyDice.rotation = angel - 90;
            flyDice.fromTo(port, endpoint, this.gameUI, Laya.Handler.create(player, player.playRipple));
        }
    }
    private onMoveEnd(num: number, chess: LudoChess) {
        this._movingChess = null;
        if (num > 0) {
            var player: GamePlayer = this.filtratePlayer(this.activated_id);
            if (player)
                player.popDice(num);
        }

        if (chess.goalGrid.inEnd && this.endPointAni) {
            yalla.Sound.playSound("firework");
            this.endPointAni.visible = true;
            this.endPointAni.pos(chess.x, chess.y);
            this.endPointAni.play();
            // if (!this.mapView.chess_box.contains(chess))
            //     (this.mapView.chess_box as Laya.Box).setChildIndex(chess, 0);
        } else {
            chess.color == this.myColor
                && ludoRoom.instance.isActivityProp
                && this.hitEventProp(chess.goalGrid.station)
        }
        if (chess.moveType == ChessMoveType.MOVE_BE_HIT) {
            !ludoRoom.instance.isDuel && this.playChessEmoji(this.mapView.ani_box, "cry", chess);
            this.orderChess();
        } else if (chess.moveType == ChessMoveType.MOVE_TIGER) {
            this.junglePropManager.hideTigerClaw();
            this.onChessStep(chess.goalGrid, chess);
        }
        if (!chess.wayGrid.inHome && !chess.choose)
            this.pileUp(chess, true, true);
        chess.off(Laya.Event.LABEL, this, this.onChessStep);
        this.nextMsg();
    }
    private listenChessMoveEnd(chess: LudoChess) {
        chess.off(Laya.Event.END, this, this.onMoveEnd);
        chess.once(Laya.Event.END, this, this.onMoveEnd);
    }
    public moveChess(msg: any) {
        // yalla.CLogDBA.instance.record_response(yalla.CLogDBA_EventID.Ludo_ChessMove_Resp);
        // console.log(ludo.GameSocket.instance.msgindex, msg);
        this.removeChessEvent();
        if (msg.msgindex) {//老虎移动端线重连 会收到多个消息补发处理消息序号最大的
            let socket = ludo.GameSocket.instance;
            if (msg.msgindex < socket.msgindex) {
                this.nextMsg(); return;
            } else {
                socket.msgindex = msg.msgindex;
            }
        }
        var chess: LudoChess = this.filtrateChess(msg.chessId);
        if (!chess) { this.nextMsg(); return; }
        if (chess.childChessId > 0 && !chess.isMaster) {//服务端棋子 副子和叠子ID有搞反的情况
            if (!this.mapView.contains(chess)) {
                var chessid = chess.childChessId;
                chess = this.filtrateChess(chessid);
                msg.chessId = chessid;
            }
        }
        if (chess.goalGrid.inEnd) {//已经到终点不再重复执行
            this.nextMsg();
            return;
        }
        if (chess.goalGrid.atGrid(LudoBoardData.getGridName(msg))) {
            this.nextMsg();
            return;
        }
        if (chess.moving) chess.clearTimeLine();
        // chess.moving = true;
        var len = this.mapView.chess_box.numChildren - 1;
        // var len = ChessManger.instance.chessLen - 1;
        if (this.mapView.chess_box.contains(chess) && len > 0 && msg.type != ChessMoveType.MOVE_BE_HIT && msg.type != ChessMoveType.MOVE_TIGER_HIT)
            this.mapView.chess_box.setChildIndex(chess, len);

        switch (msg.type) {
            case ChessMoveType.MOVE_NORMAL:
                this._movingChess = chess;
                this.listenChessMoveEnd(chess);
                this.normalMove(msg, chess);
                break;
            case ChessMoveType.MOVE_BE_HIT:
                this._movingChess = chess;
                this.listenChessMoveEnd(chess);
                !ludoRoom.instance.isDuel ? this.hitMove(msg, chess) : this.duelHitMove(msg, chess);
                break;
            case ChessMoveType.MOVE_ROCKET://火箭移动
                this._movingChess = chess;
                this.listenChessMoveEnd(chess);
                this.rocketMove(msg, chess);
                break;
            case ChessMoveType.MOVE_ARROW:
                this._movingChess = chess;
                this.listenChessMoveEnd(chess);
                this.arrowMove(msg, chess);
                break;
            case ChessMoveType.MOVE_FORWARD_WIND:
                yalla.Sound.playSound("Wind_Go_Ahead");
                this._movingChess = chess;
                this.listenChessMoveEnd(chess);
                this.tornadoMove(chess, msg);
                break;
            case ChessMoveType.MOVE_BACKWARD_WIND:
                this._movingChess = chess;
                this.listenChessMoveEnd(chess);
                yalla.Sound.playSound("Wind_Go_Back");
                this.tornadoMove(chess, msg);
                break;
            case ChessMoveType.MOVE_TIGER:
                this._movingChess = chess;
                this.tigerMove(msg, chess);
                break;
            case ChessMoveType.MOVE_TIGER_HIT:
                if (yalla.Global.isFouce) {
                    chess.tigerHitMsg = msg;
                } else {
                    chess.tigerHitMsg = null;
                    this.listenChessMoveEnd(chess);
                    this.pileUp(chess, false);
                    chess.move(msg, msg.type);
                    this.orderChess();
                    chess.reset();
                }
                this.nextMsg();
                break;
        }
    }
    private tigerHitMove(chess: LudoChess, fightChess: LudoChess, index: number) {
        if (chess.tigerHitMsg) {
            this.listenChessMoveEnd(chess);
            if (yalla.Global.isFouce && fightChess.color == this.myColor && fightChess.winnerExp) {
                this.showStar(chess.localToGlobal(new Laya.Point(), true), this.myColor, fightChess.winnerExp, index);
            }
            ComboManager.instance.onHit(1);
            // this.pileUp(chess, false);
            chess.move(chess.tigerHitMsg, ChessMoveType.MOVE_TIGER_HIT);
            chess.tigerHitMsg = null;
            // this.orderChess();
            chess.reset();
        }
    }
    private tornadoMove(chess: LudoChess, msg: any) {
        if (yalla.Global.isFouce) {
            this.junglePropManager.showTornado(chess.goalGrid);
        }
        chess.tornadoMove(msg, msg.type);
    }
    private tigerMove(msg: any, chess: LudoChess) {
        // yalla.Debug.log("老虎移动" + JSON.stringify(chess.goalGrid.station) + JSON.stringify(msg));
        this.junglePropManager.catchSameProp(chess.goalGrid.gridName).forEach(prop => {
            if (prop && prop.type == JungleEvent.JUNGLE_TIGER) {
                (prop as JungleProp).recover()
            }
        })
        if (yalla.Global.isFouce) {
            this.junglePropManager.showTiger();
            //中间切后台有问题 可能导致lable事件没有监听护盾没有破碎，棋子没有被吃
            this.timer.once(800, this, this.tigerMoveDelay, [msg, chess], false);
        } else {
            this.listenChessMoveEnd(chess);
            chess.tigerMove(msg);
        }
        this.nextMsg();
    }
    private tigerMoveDelay(msg: any, chess: LudoChess) {
        if (chess && !chess.goalGrid.atGrid(LudoBoardData.getGridName(msg))) {
            this.listenChessMoveEnd(chess);
            chess.on(Laya.Event.LABEL, this, this.onChessStep);
            var grid: LudoGrid = LudoBoardData.getGridByStation(msg);
            if (grid) {
                this.junglePropManager.showTigerClaw(grid.port);
            }
            chess.tigerMove(msg);
        }
    }
    private onChessStep(grid: LudoGrid, chess: LudoChess) {
        this.junglePropManager.catchSameProp(grid.gridName).forEach(prop => {
            if (prop && prop.type == 5) {
                if (prop.mode == "icon") {
                    this.junglePropManager.recoverIconByName(prop.name);
                } else {
                    this.junglePropManager.recoverAniByName(prop.name);
                    // (prop as ShieldProp).status = 1
                    yalla.Sound.playSound("Shield_broken");
                }
            }
        })
        let chesses: LudoChess[] = ChessManger.instance.getSameGridChesses(grid, chess.id);
        chesses.forEach((sameGridchess: LudoChess, index: number) => {
            this.tigerHitMove(sameGridchess, chess, index);
        })
    }
    private normalMove(msg: any, chess: LudoChess) {
        var ter: number;
        if (chess.goalGrid.inHome) {
            ter = 6;
        } else {
            var cells = ludoRoom.instance.isDuel ? 9 : 13;
            ter = (Math.abs(msg.gridPosition) + cells) % cells - Math.abs(chess.goalStation.gridPosition);
            if (ter < 0) ter += cells;
        }
        if (chess.childChessId > 0) ter = ter * 2;
        chess.stepNum = ter;
        this.pileUp(chess, false);
        chess.move(msg)
    }
    private duelHitMove(msg: any, chess: LudoChess) {
        var color = this.filtratePlayer(this.activated_id).color;
        if (yalla.Global.isFouce) {
            chess.cut(msg);
            DuelManager.instance.showKnife(chess.goalGrid.port, color);
            var grid = chess.goalGrid;
            this.timer.once(DuelManager.cutTime, this, (chess: LudoChess, grid: LudoGrid, color: number) => {
                if (chess && grid && DuelManager._instance) {
                    // DuelManager.instance.onHit(color);
                    this.timer.once(200, this, this.pileWithGrid, [grid]);
                    this.timer.once(300, this, () => {
                        this.orderChess();
                        this.playChessEmoji(chess, "cry");
                    });
                    ComboManager.instance.onHit(1);
                }
            }, [chess, grid, color])
        } else {
            this.pileUp(chess, false);
            chess.move(msg, ChessMoveType.MOVE_BE_HIT);
            // DuelManager.instance.onHit(color);
        }
    }

    private hitMove(msg: any, chess: LudoChess) {
        if (chess.childChessId > 0) {
            ComboManager.instance.onHit(2);
            let chessLink: LudoChess = this.filtrateChess(chess.childChessId);
            chessLink.reset();
            if (!chessLink.displayedInStage)
                this.mapView.chess_box.addChild(chessLink);
            if (!chess.displayedInStage)
                this.mapView.chess_box.addChild(chess);
            this.playChessEmoji(chessLink, "cry");
        } else {
            ComboManager.instance.onHit(1);
        }

        if (this.mapView && this.mapView._curtainMask) {
            let winnerPlayer = this.filtratePlayer(this.activated_id);
            if (winnerPlayer && winnerPlayer.color != this.myColor) {
                let player: GamePlayer = this.filtratePlayerByColor(chess.color);
                this.mapView.fighterWinnerPlayer = winnerPlayer;
                this.mapView.fightFailerPlayer = player;
                this.mapView.showKillTip(chess);
            }
        }
        this.pileUp(chess, false);
        chess.move(msg, ChessMoveType.MOVE_BE_HIT);
        this.orderChess();
        chess.reset();
    }
    private rocketMove(msg: any, chess: LudoChess) {
        var start = LudoBoardData.getPosByStation(chess.wayStation);
        var end = LudoBoardData.getPosByStation(msg);
        if (start && end && yalla.Global.isFouce) {
            chess.rocket = true;
            chess.rotation = yalla.util.angle(start, end);
        }
        this.pileUp(chess, false);
        chess.rocketMove(msg, msg.time - 10);
    }
    private arrowMove(msg: any, chess: LudoChess) {//1.3.5箭头移动
        this.pileUp(chess, false);
        chess.arrowMove(msg);
        if (chess.color == this.myColor && yalla.Global.isFouce) {
            this.timer.once(200, this, this.getArrowDice, [chess]);
        }
    }
    public getArrowDice(chess: LudoChess) {
        var player: GamePlayer = this.filtratePlayerByColor(chess.color);
        if (player && chess) {
            var endpoint: port = player.UI.dice_bg.localToGlobal(new Laya.Point(), true);
            var point: port = chess.localToGlobal(new Laya.Point(), true);
            if (endpoint && point) {
                LudoBuffManager.instance.getArrowDice().fromTo(point, {
                    x: endpoint.x + 70,
                    y: endpoint.y + 56
                }, this.gameUI, null)
                yalla.Sound.playSound("firework");
                this.endPointAni.visible = true;
                this.endPointAni.pos(chess.x, chess.y);
                this.timer.once(230, this.endPointAni, this.endPointAni.play)
            }
        }
    }
    private orderChess() {
        var chessAraay: Array<LudoChess> = [];
        ChessManger.instance.each(ch => {
            if (ch && ch.displayedInStage)
                chessAraay.push(ch);
        })
        chessAraay.sort((a, b) => {
            var left = a.y + (a.x + a.pivotX) / 1000,
                right = b.y + (b.x + b.pivotX) / 1000;
            if (left > right)
                return 1;
            else
                return -1;
        })
        chessAraay.forEach((c, i) => {
            var left = c.y + (c.x + c.pivotX) / 1000;
            this.mapView.chess_box.setChildIndex(c, i);
        })
    }


    /**
     * 玩家操作
     * @param msg 
     */
    public onPlayerOperate(msg: any = { operate: 200 }) {
        if (msg && !msg.msgindex && msg.code == ErrorCode.NO_TOAST) {
            yalla.Debug.log('走子和托管走子冲突' + JSON.stringify(msg));
            let chessId = msg.chessId;
            if (chessId) {
                let chess: LudoChess = this.filtrateChess(msg.chessId);
                if (!chess) return;
                let grid: LudoGrid = LudoBoardData.getGridByStation(msg);
                chess.stopMove(grid);
            }
        }
        switch (msg.operate) {
            case 4://掷骰子
                this.throwDice(msg);
                break;
            case 5://重置投掷
                if (msg.code == 403) {
                    yalla.common.Confirm.instance.showConfirm("Your account has been frozen", null, Laya.Handler.create(this, () => {
                        yalla.common.Confirm.instance.hideConfirm();
                    }), ["", "Confirm"]);
                } else if (msg.result == 1) {
                    if (msg.idx == yalla.Global.Account.idx) {
                        ludo.Undo.instance.happenUndo();
                        this.updateUndoUI();
                    }
                    var player = this.filtratePlayer(this.activated_id);
                    player && player.popDice();
                }
                yalla.CLogDBA.instance.record_response(yalla.CLogDBA_EventID.Ludo_Undo_DiceNums_Resp);
                // this.nextMsg();
                break;
            case 8://系统托管
                var idx = msg.idx
                if (idx == yalla.Global.Account.idx && !this.isTrust) {
                    this.isTrust = true;
                    var player: GamePlayer = this.filtratePlayer(yalla.Global.Account.idx);
                    var hasdice = false;
                    if (player) {
                        player.head.trustee = true;
                        this.throwBtn && this.throwBtn.on("click", this, this.onTrust);
                        player.head.on("click", this, this.onTrust);
                        hasdice = player.UI ? player.UI.dice_box.numChildren > 0 : false;
                    }
                    this.showTrust(hasdice);
                    yalla.common.TipManager.instance.showTrustTip(1, this, null, ludoRoom.instance.isDuel);
                }
                break;
            case 9://取消托管
                var idx = msg.idx
                if (idx == yalla.Global.Account.idx && this.isTrust == true) {
                    this.hideTrust(true);
                    this.isTrust = false;
                    var player = this.filtratePlayer(yalla.Global.Account.idx);
                    if (player) {
                        this.throwBtn && this.throwBtn.off("click", this, this.onTrust);
                        player.head.trustee = false;
                        player.head.off("click", this, this.onTrust);
                        this.activated_id != yalla.Global.Account.idx ? player.showName() : player.hideName();
                    }
                    yalla.common.TipManager.instance.showTrustTip(0, this, null, ludoRoom.instance.isDuel);
                }
                break;
            case 10://免费重置投掷
                break;
            case 11://屏蔽聊天
                if (msg.result) {
                    var player: GamePlayer = this.filtratePlayer(msg.idx);
                    player && (player.isInChatOff = true);
                }
                break;
            case 12://取消屏蔽聊天
                if (msg.result) {
                    var player: GamePlayer = this.filtratePlayer(msg.idx);
                    player && (player.isInChatOff = false);
                }
                break;
        }
    }
    /**
     * 骰子的点数显示
     */
    throwDice(msg: any) {
        if (!this.activated_id) return;
        // yalla.CLogDBA.instance.record_response(yalla.CLogDBA_EventID.Ludo_DiceNums_Resp);
        // yalla.Debug.log('骰子的点数显示 :' + this.activated_id);
        if (msg.result == 1) {
            let player: GamePlayer = this.filtratePlayer(this.activated_id);
            this.removeChessEvent();
            let num: number = msg.throwNum;
            if (this.activated_id == yalla.Global.Account.idx) {
                if (player && player.dice) {
                    this.throwBtn && this.throwBtn.off("click", this, this.clickDice);
                }
            }
            if (yalla.Global.isFouce) {
                if (player) {
                    player.waitCdEnd().waitCdStart(this.waitTime);
                    player.dice && player.dice.play();
                    this.timer.once(this.diceTime, this, this.stopAndShowDice, [player, num]);
                }
            } else {
                this.stopAndShowDice(player, num);
            }
        }
    }
    stopAndShowDice(player: GamePlayer, num: number) {
        player && player.pushDice(num);
        if (this.isTrust && this.activated_id == yalla.Global.Account.idx)
            this.hideTrust(false);
        player && player.stopAndShowDice(num);
    }
    /**
     * 显示托管状态
     */
    private showTrust(hasdice: boolean = false) {
        this.gameUI.trusteeship.visible = !hasdice;
        yalla.Global.isFouce && this.changeTrustAni(!hasdice);
        this.gameUI.trust.offAll();
        this.gameUI.trust_wait.visible = false;
        this.gameUI.trust.selected = false;
        this.addTrustEvent();
    }
    private changeTrustAni(show: boolean) {
        if (show) {
            this.gameUI.trust_jump.play(0, true);
            this.gameUI.trust_wait.play(0, true);
        } else {
            this.gameUI.trust_jump.gotoAndStop(0);
            this.gameUI.trust_wait.gotoAndStop(0);
        }
    }
    private hideTrust(removeEvent: boolean) {//取消托管UI
        this.timer.clear(this, this.trustDely);
        this.gameUI.trusteeship.visible = false;
        this.changeTrustAni(false);
        this.gameUI.trust.selected = false;
        this.gameUI.trust_wait.visible = false;
        removeEvent && this.removeTrustEvent();
    }
    public onTrust() {//取消托管事件监听
        if (yalla.Global.IsGameOver) return;
        this.gameUI.trust_wait.visible = true;
        ludo.GameSocket.instance.cancleTrust();
        this.timer.once(5000, this, this.trustDely);
        this.removeTrustEvent();
    }
    private trustDely() {
        this.addTrustEvent();
        this.gameUI.trust_wait.visible = false;
        this.gameUI.trust.selected = false;
    }
    private addTrustEvent() {
        this.gameUI.trusteeship.on("click", this, this.onTrust);
        this.gameUI.bg.on("click", this, this.onTrust);
        this.mapView && this.mapView.on("click", this, this.onTrust);

    }
    private removeTrustEvent() {
        this.gameUI.trust.off("click", this, this.onTrust);
        this.gameUI.bg.off("click", this, this.onTrust);
        this.mapView && this.mapView.off("click", this, this.onTrust);
    }
    /**
     * 玩家游戏状态 50
     */
    public onPlayerStatus(msg: any) {
        this.playerStatus = msg.status;
        if (this.activated_id == null) {//第一次玩家
            if (this.playerStatus === 2) {
                this.resetStart(msg);
                return;
            }
            if (msg.idx != yalla.Global.Account.idx) {
                let player: GamePlayer = this.filtratePlayer(msg.idx);
                if (player) {
                    player.waitCdStart(this.waitTime);
                    player.showDice();
                }
                ludo.Undo.instance.newRound();
                this.updateUndoUI();
            }
            this.activated_id = msg.idx;
        }
        if (this.activated_id != msg.idx && this.playerStatus == 0) {//切换玩家
            if (this.isFocusChanges && yalla.Global.isFouce && !yalla.Global.IsGameOver) {
                ludo.GameSocket.instance.flushGameData();
                this.isFocusChanges = false;
            }
            let lastPlayer: GamePlayer = this.filtratePlayer(this.activated_id);
            let nextpPlayer: GamePlayer = this.filtratePlayer(msg.idx);//下一个玩家
            nextpPlayer && nextpPlayer.newRound().playRoleAni();
            ComboManager.instance.reset();
            if (msg.idx == yalla.Global.Account.idx) {
                yalla.Sound.playSound("my_turn");
                ludo.Undo.instance.newRound();
                this.updateUndoUI();
                if (this.isTrust) yalla.common.TipManager.instance.showTrustTip(1, this, null, ludoRoom.instance.isDuel);
            } else {
                ludoRoom.instance.isActivityProp && this.refreshEventProps({});
                yalla.Sound.playSound("opp_turn");
                nextpPlayer && nextpPlayer.showDice();
            }
            lastPlayer && lastPlayer.stopAndShowDice(7).waitCdEnd();
            if (this.activated_id == yalla.Global.Account.idx) {
                if (this.isTrust) {
                    this.showTrust();
                }
                ludo.Undo.instance.newRound();
                this.updateUndoUI();
                lastPlayer && lastPlayer.clearDice(this.isTrust);
            } else {
                lastPlayer && lastPlayer.clearDice(false).hideDice();
            }
            nextpPlayer && nextpPlayer.waitCdStart(this.waitTime);
            this.activated_id = msg.idx;
        }
        let activatedPlayer: GamePlayer;
        switch (this.playerStatus) {
            case 0://("玩家" + res.idx + "开始投掷");//添加点击事件 倒计时动画
                this.throwStart();
                // if (this.isFocusChanges && yalla.Global.isFouce && !yalla.Global.IsGameOver) {
                //     ludo.GameSocket.instance.flushGameData();
                //     this.isFocusChanges = false;
                // }
                if (msg.idx == yalla.Global.Account.idx) this.waitThrowTime = Date.now();
                break;
            case 1: //("玩家" + res.idx + "投掷结束");//移除点击事件
                this.throwEnd();
                if (msg.idx == yalla.Global.Account.idx && !this.isSpectator) {//排除观战和其他人
                    if (!this.isTrust && this.waitThrowTime) {//投掷骰子时长埋点
                        var now = Date.now(),
                            time = now - this.waitThrowTime;
                        if (time < 11000) {
                            yalla.util.clogDBAmsg('10138', JSON.stringify({ time }));
                        }
                    }
                    this.waitThrowTime = 0;
                }
                break;
            case 2:
                this.resetStart(msg);
                break;
            case 3: //("是否重置结束");
                this.resetEnd();
                break;
            case 4:// ("选择数字和棋子开始", msg);
                activatedPlayer = this.filtratePlayer(this.activated_id);
                // yalla.Debug.log('选择数字和棋子开始 :' + this.activated_id);
                activatedPlayer && activatedPlayer.waitCdEnd().waitCdStart(this.waitTime);
                if (this.activated_id == yalla.Global.Account.idx && this.chooseMsg) {
                    this.chooseChessStart(this.chooseMsg);
                }
                break;
            case 5:   // ("选择数字和棋子结束");
                activatedPlayer = this.filtratePlayer(this.activated_id);
                activatedPlayer && activatedPlayer.waitCdEnd();
                this.removeChessEvent();
                break;
            case 6://("移动棋子开始");
                activatedPlayer = this.filtratePlayer(this.activated_id);
                activatedPlayer && activatedPlayer.waitCdEnd().waitCdStart(this.waitTime);
                break;
            case 7://("移动棋子结束");
                if (this.activated_id) {
                    activatedPlayer = this.filtratePlayer(this.activated_id);
                    activatedPlayer && activatedPlayer.waitCdEnd();
                }
                break;
        }
    }
    private sendResetMsg() {
        this.resetCd.end();
        this.gameUI.reset.on("click", this, this.showResetInfo);
        yalla.Debug.log('sendResetMsg this.gameUI.isreset:' + this.gameUI.isreset + "this.gameUI.isreset.selected:" + this.gameUI.isreset.selected);
        if (this.gameUI.isreset && this.gameUI.isreset.selected) {
            //修复在收到resetstart消息后，会添加sendResetMsg按钮监听，此时如果取消了undo选中，点击reset按钮不再发送数据到服务器
            //添加点击事件的逻辑在 resetStart()方法中  this.gameUI.reset.once("click", this, this.sendResetMsg)；
            return;
        }
        ludo.GameSocket.instance.clickReset();
        yalla.Native.instance.mobClickEvent(buryPoint.LUDO_GAME_UNDO_LACK);//埋点点击重置
        var val = `${yalla.Global.gameType}_${ludoRoom.instance.type}_${ludoRoom.instance.group}_${yalla.Global.Account.isPrivate}_${ludoRoom.instance.sitPlayers.length}_${ludoRoom.instance.cost}`
        yalla.util.clogDBAmsg('10321', val);
        yalla.CLogDBA.instance.record_request(yalla.CLogDBA_EventID.Ludo_Undo_DiceNums_Resp);
    }
    //快速购买
    private buyCoin() {
        yalla.Native.instance.buyGood((obj) => {
            if (obj.status) {
                if (obj.diamond > 0) {
                    ludo.Global.myGold += parseInt(obj.diamond);
                }
                if (obj.money > 0) {
                    ludo.Global.myMoney += parseInt(obj.money);
                }
                this.update();
                // ludo.GameSocket.instance.flush();//刷新会造成断线
                yalla.Native.instance.mobClickEvent(buryPoint.LUDO_GAME_UNDO_LACK_BUY);//埋点 点击购买
                // } else {
                //     var tip = yalla.common.TipItem.instance;
                //     tip.pushTip((Laya.stage.width - tip.width) / 2, Laya.stage.height / 2, "Fail to purchase diamonds, please try again.", { alpha: 0 }, 0, 5000);
            }
            this._buyCoin = true;
        });
    }
    private showResetInfo(e: Laya.Event) {
        e.stopPropagation();
        yalla.Sound.playSound("click");
        var undo = ludo.Undo.instance;
        if (undo.residueReThrowNum > 0 && ludo.Global.myGold < undo.costDiamond) {
            this.gameUI.quick_buy_btn.visible = !this.gameUI.quick_buy_btn.visible;
            return;
        }
        this.gameUI.reset_info.visible = !this.gameUI.reset_info.visible;
        if (this.gameUI.reset_info.visible) {
            this.gameUI.isreset.on("click", this, this.checkReset);
        } else {
            this.gameUI.isreset.off("click", this, this.checkReset);
        }
    }
    private checkReset() {
        if (!this.gameUI.isreset.selected) {
            this.gameUI.isreset_tx.text = "Undo ON";
            this.gameUI.reset_title.text = "Undo Dice Roll";
            this.gameUI.reset_body.text = "You can undo dice within 2 secs.";
            var undo = ludo.Undo.instance;
            // if (undo.residueTurnReThrowNum > 0) this.gameUI.reset.skin = "game/btn_refresh_on.png";
            if (undo.residueTurnReThrowNum <= 0 || ludo.Global.myGold < undo.costDiamond) {
                this.gameUI.reset.skin = "game/btn_refresh_off.png";
            } else {
                this.gameUI.reset.skin = "game/btn_refresh_on.png";
            }
        } else {
            this.gameUI.isreset_tx.text = "Undo OFF";
            this.gameUI.reset_title.text = "Undo is off";
            this.gameUI.reset_body.text = "Undo is off, tap to enable it.";
            this.gameUI.reset.skin = "game/btn_refresh_off.png";
            this.resetEnd();
        }
    }
    /**
     * 重置开始
     *1.断线重连的时候 服务端主动推送 msg中有costDiamond字段 表示修正本地文字显示,不需要转动undo的雷达
     *2.重置开始的时候 msg中有msgindex字段而无costDiamond 字段 需要转动undo的雷达
     */
    private resetStart(msg: any) {
        // yalla.Debug.log('重置开始 msg.idx:' + msg.idx);
        var undo = ludo.Undo.instance;
        if (msg.costDiamond) {
            if (undo.costDiamond > 0 && ludo.Global.myGold > undo.costDiamond) {
                undo.costDiamond = msg.costDiamond;
                this.gameUI.useCoin.text = String(undo.costDiamond);
            }
        }
        if (msg.msgindex) {
            if (msg.idx == yalla.Global.Account.idx) {//添加重置按钮的点击事件 发送重置消息
                if (ludo.Undo.instance.residueTurnReThrowNum <= 0 || this.activated_id != msg.idx) return;
                if (!this.isTrust /*&& undo.costDiamond != 0*/ && !this.gameUI.isreset.selected) {//没有托管且
                    if (undo.costDiamond <= ludo.Global.myGold) {
                        this.gameUI.reset.off("click", this, this.showResetInfo);
                        this.gameUI.reset.once("click", this, this.sendResetMsg);
                    }
                    this.resetCd && this.resetCd.start(this.resetTime, -1);
                }
            } else {//不是自己
                var player: GamePlayer = this.filtratePlayer(msg.idx);
                if (player) player.waitCdEnd().waitCdStart(this.waitTime);
            }
        }
    }
    /*
    * 开始掷骰子
    */
    throwStart() {
        if (!this.activated_id) return;
        var player: GamePlayer = this.filtratePlayer(this.activated_id);
        // yalla.Debug.log('开始掷骰子 :' + this.activated_id);
        if (player) player.waitCdStart(this.waitTime);
        if (this.activated_id == yalla.Global.Account.idx && !this.isTrust) {//添加骰子的点击事件 非托管
            this.showThrowJump();
        }
    }
    /**
     * 结束掷骰子
     */
    protected throwEnd() {
        if (!this.activated_id) return;
        let player = this.filtratePlayer(this.activated_id);
        if (player) player.waitCdEnd();
        if (this.activated_id == yalla.Global.Account.idx) {//移除骰子的点击事件
            this.hideThrowJump();
        }
    }
    private showThrowJump() {
        this.throwBtn && this.throwBtn.once("click", this, this.clickDice);
        this.gameUI.throw_jump.visible = true;
        yalla.Global.isFouce && this.gameUI.throw_jump.play(0, true);
    }
    private hideThrowJump() {
        this.throwBtn && this.throwBtn.off("click", this, this.clickDice);
        this.gameUI.throw_jump.visible = false;
        (this.gameUI.throw_jump as Laya.Animation).gotoAndStop(0);
    }
    protected clickDice() {
        ludo.GameSocket.instance.clickDice();
        this.resetEnd();
        if (this.activated_id == yalla.Global.Account.idx) {//优化点击立即播放筛子动画
            var player: GamePlayer = this.filtratePlayer(this.activated_id);
            player && player.dice && player.dice.play();
        }
    }
    /**
     * 重置结束
     */
    private resetEnd() {
        if (!this.activated_id)
            return;
        if (this.activated_id == yalla.Global.Account.idx && !this.gameUI.isreset.selected) {//移除重置按钮的点击事
            this.gameUI.reset.off("click", this, this.sendResetMsg);
            this.gameUI.reset.on("click", this, this.showResetInfo);
            this.resetCd && this.resetCd.end();
        }
    }
    public gameOver(msg: any, showfail: boolean, share: boolean = false): Result {
        // yalla.Debug.log(msg);
        if (yalla.Global.IsGameOver) return;
        yalla.common.TipManager.instance.hideTrustTip();
        this.timer.clear(this, this.exitNow);
        this.isFocusChanges = false;
        this.clearQueue();
        this.onGameOver();
        if (!this.activated_id) {
            var player = this.filtratePlayer(this.activated_id);
            player && player.waitCdEnd();
        }
        this.loseNetWork();
        yalla.util.sendExitType(ExitType.GAMEOVER_QUIT);
        if (ludoRoom.instance.isTeamGame()) {
            this.resultView = new TeamResult(msg, showfail);
        } else if (ludoRoom.instance.isDuel) {
            this.resultView = new DuelResult(msg, share, showfail);
        } else {
            this.resultView = new Result(msg, share, showfail);
        }
        this.addChild(this.resultView);
        this.resultView.zOrder = 995//500;
        var soundName = this.resultView.isWin ? "cheer" : "ludo_fail";
        if (ludoRoom.instance.isActivityProp && !this.isSpectator && this.resultView.propNum) {
            this.banner.onCurCountChange(this.resultView.propNum, true);
        }
        // yalla.Sound.playSound(soundName, Laya.Handler.create(this, () => {
        //     yalla.Voice.instance.levelGameRoom();
        // }));
        yalla.Voice.instance.gameOverSound(soundName);

        ludoRoom.instance.nightGame() && this.mapView && this.mapView.removeMask(false);
        yalla.Native.instance.closeAllNativeView();
        this.chat && this.chat.close();
        Dialog.manager.closeAll();
        return this.resultView;
    }
    public onChat(msg: any, server = false) {
        if (!yalla.Global.IsGameOver && msg && (msg.msg || msg.extension)) {
            if (!msg.idx) msg.idx = yalla.Global.Account.idx;
            let isMe = msg.idx == yalla.Global.Account.idx;
            if (server && isMe) { return; }//服务器返回自己的消息不显示 自己的消息会直接显示
            if (ludo.muteSpectator.isMute && !isMe && !this.isSpectator) return;//屏蔽状态
            if (yalla.Mute.muted(msg.idx)) return;//被屏蔽的人 
            var isLimitVisitor = yalla.Global.Account.isLimitVisitor;
            if (isLimitVisitor && ludoRoom.instance.isPrivate && !yalla.Mute.isShowMsgByLimit(msg)) return;
            let color: number = null;
            let playerInfo: playerShowInfo = ludoRoom.instance.getPlayerByIdx(msg.idx);
            if (!playerInfo || !this.chat) return;
            var insit = playerInfo.sitNum > -1;
            if (insit && !this.chat.showLive) {//弹幕被打开后不显示气泡
                let player: GamePlayer = this.filtratePlayer(msg.idx);
                if (player) {
                    player.showChat(msg);
                    color = player.color;
                }
            }
            // if (isLimitVisitor) return;
            let side = isMe;
            if (yalla.Font.lan == "ar") side = !side;
            let chat = {
                color: color,
                idx: msg.idx,
                playerShowInfo: playerInfo,
                chat: msg,
                side: side,
                isMe: isMe
            }
            this.chat.onChat(chat, isMe, insit);
        }
    }
    private filtratePlayerByColor(color: number): GamePlayer {
        var players_layer = this.gameUI.players_layer;
        for (var i = 0; i < players_layer.numChildren; i++) {
            var player = (players_layer.getChildAt(i) as GamePlayer);
            if (player.color == color) {
                return player;
            }
        }
        return null;
    }
    protected showStar(port: port, color: number, exp: number, index: number = 0) {
        if (this.isSpectator || ludoRoom.instance.isPrivate) return;
        var player: GamePlayer = this.filtratePlayerByColor(color);
        if (player && port) {
            var star: StarAni = LudoBuffManager.instance.getStarAni();
            var endpoint: port = player.head.localToGlobal(new Laya.Point(), true);
            endpoint && star && star.fromTo(port, endpoint, index, this.gameUI, Laya.Handler.create(this, (exp) => {
                !player.destroyed && player.showEXP(exp);
            }, [exp]));
        }
    }
    /**
     *  43 GridEventResponse
     */
    public onChessEvent(msg: any) {
        switch (msg.event) {
            case 0: //正常"
                if (msg.addActivityProp) {
                    let chess: LudoChess = this.filtrateChess(msg.chessId);
                    if (chess && chess.color == this.myColor) {
                        this.banner.addPropNum(msg.addActivityProp);
                    }
                }
                break;
            case 1:// "产生叠子"
                if (msg.chessLinkId != -1) {
                    let linkChess: LudoChess = this.filtrateChess(msg.chessLinkId);
                    if (linkChess) {
                        linkChess.childChessId = msg.chessId;
                        linkChess.removeSelf();
                    }
                    let chess: LudoChess = this.filtrateChess(msg.chessId);
                    if (chess) {
                        chess.childChessId = msg.chessLinkId;//TODO 线上有错误日志 TypeError: Cannot set property 'childChessId' of undefined
                        chess.isMaster = true;
                        this.pileUp(chess, true);
                        chess.showPlieNum();
                    }
                }
                break;
            case 2:  // "发生战斗"
                let fightChess: LudoChess = this.filtrateChess(msg.chessId);
                if (fightChess) {
                    var color = fightChess.color;
                    if (yalla.Global.isFouce) {
                        if (color == this.myColor && msg.winnerExp) {
                            this.showStar(fightChess.localToGlobal(new Laya.Point(), true), color, msg.winnerExp);//叠子加4点经验
                        }
                        if (this.mapView) {
                            var _curtainMask = this.mapView._curtainMask as Curtain;
                            if (_curtainMask && ludoRoom.instance.isSameTeam(this.isSpectator, this.activated_id) && _curtainMask.isOurChess(color)) {
                                this.mapView.showGolbleEye();
                            }
                        }
                        if (ludoRoom.instance.isDuel) {
                            let fightNum = msg.fightNum | 0;
                            this.timer.once(DuelManager.cutTime, this, () => {
                                msg.winnerExp && DuelManager.instance.onHit(color, fightNum, true);
                                fightChess && this.playChessEmoji(fightChess, "cool");
                            }, null, false);
                        } else {
                            this.playChessEmoji(this.mapView.ani_box, "cool", fightChess);
                        }
                    } else if (ludoRoom.instance.isDuel && msg.winnerExp) {
                        let fightNum = msg.fightNum | 0;
                        DuelManager.instance.onHit(color, fightNum, false);
                    }
                    if (ludoRoom.instance.type != 0) {
                        var playerColor: number = color;
                        let player = this.filtratePlayerByColor(playerColor);
                        if (player && !player.isPass) {
                            player.isPass = true;
                            let sign = (this.mapView.sign_box.getChildByName("sign" + player.color) as RoadBlock);
                            sign && sign.hide();
                        }
                    }
                }
                break;
            case 3://到达终点
                // yalla.Debug.log(msg);
                let endChesss: LudoChess = this.filtrateChess(msg.chessId);
                if (endChesss) {
                    endChesss.isEndChess = true;
                    //叠子乘坐火箭到终点 先取消叠子
                    endChesss.childChessId > 0 && this.breakChess(endChesss.displayedInStage ? msg.chessId : endChesss.childChessId);
                    if (endChesss.color == this.myColor) {
                        msg.addActivityProp && this.banner.addPropNum(msg.addActivityProp);
                        if (yalla.Global.isFouce) {
                            msg.winnerExp && this.showStar(endChesss.localToGlobal(new Laya.Point(), true), endChesss.color, msg.winnerExp);
                            if (ludoRoom.instance.nightGame() && ChessManger.instance.allChessWin(this.myColor)) {
                                new Sparkler(this);
                                if (this.mapView) {
                                    this.mapView.hideVisibility();
                                    !ludoRoom.instance.isTeamGame() && this.mapView.removeMask(true);
                                }
                            }
                        }
                    }
                    if (ludoRoom.instance.type == 2) {//快速模式 4颗棋子都回到出生点
                        ChessManger.instance.each((ch: LudoChess) => {
                            if (ch && ch.color == endChesss.color && ch.id != endChesss.id) {
                                if (!ch.isEndChess) {
                                    this.pileUp(ch, false);
                                }
                                ch.move(ch.homeStation, ChessMoveType.MOVE_HOME);
                                ch.reset(false);
                            }
                        })
                        this.removeChessEvent();
                    }
                }
                break;
            case 4://位移
                break;
            case 5://获得buff
                this.getBuff(msg);
                break;
            case 6://使用buff   ??????没有消息？？？？？
                this.useBuff(msg);
                break;
            case 7://取消叠子
                this.breakChess(msg.chessId)
                break;
            case 8:
                let ch: LudoChess = ChessManger.instance.getChessById(msg.chessId);
                if (ch && ch.color == this.myColor) {
                    ch.winnerExp = msg.winnerExp;
                }
                break;
        }
    }
    /**
     * 取消叠子
     * @param chessid
     */
    private breakChess(chessid: number) {
        var chess: LudoChess = this.filtrateChess(chessid);
        if (!chess) return;
        var chessLink: LudoChess = this.filtrateChess(chess.childChessId);
        chess.childChessId = -1;
        chess.isMaster = false;
        if (chessLink) {
            chessLink.breakLink(chess.goalGrid);
            if (!chessLink.displayedInStage)
                this.mapView.chess_box.addChild(chessLink);
            if (!chess.displayedInStage)
                this.mapView.chess_box.addChild(chess);
        }
        chess.hidePlieNum();
        this.pileUp(chess, true);
    }
    /**
    * 退出游戏
    * @param msg 消息
    */
    public onQuitMsg(player: playerShowInfo) {
        if (player.fPlayerInfo.idx == yalla.Global.Account.idx) {
            if (yalla.Global.Account.isPrivate == 3) {
                if (player.sitNum < 0) {
                    this.onGameOver().clearQueue();
                    yalla.Native.instance.backHall(yalla.Native.instance.isBack, null, 500);
                    this.timer.clearAll(this);
                    this.removeEventListener();
                } else {
                    this.exitNow(yalla.Global.IsGameOver);
                }
            } else {
                this.onGameOver().clearQueue();
                this.exitNow(yalla.Global.IsGameOver);
            }
        } else if (player.sitNum >= 0) {
            let quitPlayer: GamePlayer = this.filtratePlayer(player.fPlayerInfo.idx);
            if (quitPlayer) {
                quitPlayer.isLeft = true;
                !quitPlayer.isWin && this.chessRST(quitPlayer.color);
                yalla.Sound.playSound("huythoat");
                ludoRoom.instance.isDuel && DuelManager.instance.onPlayerLeft(player.color);
            }
            yalla.Native.instance.muteRemoteAudioStream(player.fPlayerInfo.idx, true, null)

        }
        this.update();
    }
    protected chessRST(color: number) {
        ChessManger.instance.each((chess: LudoChess) => {
            if (chess.color == color) {
                if (!chess.isEndChess) {
                    this.pileUp(chess, false);
                }
                chess.move(chess.homeStation, ChessMoveType.MOVE_HOME);
                chess.reset(false);
                if (ludoRoom.instance.isMaster) {//大师模式
                    if (!chess.displayedInStage) {
                        this.mapView.chess_box.addChild(chess);
                    }
                    chess.hidePlieNum();
                }
            }
        })
        if (ludoRoom.instance.isDuel) {
            this.pileWithGrid(LudoBoardData.getGridByStation({
                area: color,
                gridPosition: 2
            }))
        }
    }
    showRank(msg: any) {//组队模式没有此消息
        let player: GamePlayer = this.filtratePlayer(msg.idx);
        if (msg.idx == yalla.Global.Account.idx) {
            this.hideTrust(true);
            if (player) {
                player.head.trustee = false;
                player.head.off("click", this, this.onTrust);
                this.throwBtn && this.throwBtn.off("click", this, this.onTrust);

                if (ludoRoom.instance.isActivityProp && !this.isSpectator && !player.isWin) {
                    this.banner.onPlayerWin(msg.index);
                }
            }
            this.gameUI.myHander.visible = false;
            if (ludo.Global.myInfo.playerShowInfo) ludo.Global.myInfo.playerShowInfo.winIndex = msg.index;
            this.showWinTip();
            // if (ludoRoom.instance.nightGame()) {
            //     new Sparkler(this);
            //     this.mapView && this.mapView.hideVisibility();
            // }
        }
        if (player) {
            player.hideDice();
            player.win(msg.index);
        }
    }
    private chessStateChange(items: ChessStateItem[]) {
        items.forEach((item: ChessStateItem) => {
            var chess: LudoChess = ChessManger.instance.getChessById(item.chessId)
            if (chess) chess.setStatus(item.chessState);
        })
    }
    private refreshJungleProp(msg) {
        this.junglePropManager.refresh(msg, this.mapView);
    }
    private refreshProps(msg: any) {
        this.props && this.props.forEach(prop => {
            if (prop)
                prop.removeSelf(),
                    GridPropManager.instance.recover(prop);
        });
        this.props = [];
        msg.propGrid && msg.propGrid.forEach((propGrid: PropGrid) => {
            var prop: GridProp = GridPropManager.instance.getGridProp();
            prop.propGrid = propGrid;
            this.mapView.props_box.addChild(prop);
            this.props.push(prop);
        })
    }
    private hitEventProp(station: Station): boolean {
        if (this.eventProps) {
            for (var i = 0; i < this.eventProps.length; i++) {
                var eventProp: EventProp = this.eventProps[i];
                if (eventProp && LudoBoardData.getGridName(eventProp.station) == LudoBoardData.getGridName(station)) {
                    eventProp.removeSelf();
                    this.mapView.ani_box.addChild(eventProp);
                    eventProp.hide(true);
                    //     EventPropManager.instance.recover(eventProp);
                    this.eventProps.splice(i, 1);
                    return true;
                }
            }
        }
        return false;
    }
    private refreshEventProps(msg: any) {
        // yalla.Debug.log("refreshEventProps");
        // yalla.Debug.log(msg);
        this.recoverEventProps();
        this.eventProps = [];
        if (msg && msg.idx == yalla.Global.Account.idx && msg.grid) {
            msg.grid.forEach((propGrid: PropGrid) => {
                var eventProp: EventProp = EventPropManager.instance.getEventProp();
                eventProp.propGrid = propGrid;
                eventProp._num.text = "+" + String(ludoRoom.instance.gameMapCount);
                this.mapView.props_box.addChild(eventProp);
                eventProp.show();
                this.eventProps.push(eventProp);
            })
        }
    }
    private recoverEventProps() {
        this.eventProps && this.eventProps.forEach(eventProp => {
            if (eventProp) eventProp.hide(false);
            // eventProp.removeSelf(),
            //     EventPropManager.instance.recover(eventProp);
        });
    }
    private hideWinTip() {
        if (this.winTip) this.winTip.removeSelf();
        this.winTip = null;
    }
    private showWinTip() {
        if (ludoRoom.instance.sitPlayers.length > 2 && yalla.Global.Account.isPrivate != 5 && yalla.Global.Account.isPrivate != 1 && yalla.Global.onlineMode && this.mapView && !this.winTip) {
            this.winTip = new ui.ludo.sub.win_tipUI();
            if (yalla.Font.isRight()) {
                this.winTip.tx.fontSize = 24;
            } else {
                this.winTip.tx.fontSize = 20;
            }
            this.winTip.pos(-6, 436);
            this.mapView.addChild(this.winTip);
        }
    }
    useBuff(msg: any) {
        switch (msg.buff) {
            case Buff.LUCK_DICE:
            case Buff.SNAIL_DICE:
                var player = this.filtratePlayer(msg.idx);
                if (player) {
                    // player.isGold = true;
                    player.useBuff(msg.buff);
                }
                break;
        }
    }

    getBuff(msg) {
        // if (msg.msgindex < ludo.GameSocket.instance.msgindex) return;
        switch (msg.buff) {//先收到buffer 消息后移动结束 动画没显示出来
            case Buff.LUCK_DICE:
                if (msg.idx == this.activated_id) {
                    var chess: LudoChess = this.filtrateChess(msg.chessId);
                    if (chess) {
                        var point: port = chess.localToGlobal(new Laya.Point(), true);
                        this.timer.frameOnce(1, this, this.showGoldDice, [point, chess.color, msg.idx]);
                    }
                    yalla.Sound.playSound("magic_dice");
                    let player: GamePlayer = this.filtratePlayer(msg.idx);
                    if (player) {
                        if (yalla.Global.isFouce) {
                            this.timer.runTimer(this, this.showBuffDelay);
                            this.timer.once(800, this, this.showBuffDelay, [player, msg.buff]);
                        } else {
                            player.getBuff(msg.buff);
                        }
                    }
                }
                break;
            case Buff.FLARE://黑夜模式 小照明弹
                yalla.Sound.playSound("tanzhaodeng");
                if (yalla.Global.isFouce && ludoRoom.instance.isSameTeam(this.isSpectator, msg.idx)) {
                    this.mapView.showLight && this.mapView.showLight(msg);
                }
                break;
            case Buff.EXTRA_DICE://额外骰子
                if (yalla.Global.isFouce && msg.idx == this.activated_id) {
                    yalla.Sound.playSound("firework");;
                    var chess: LudoChess = this.filtrateChess(msg.chessId);
                    var player: GamePlayer = this.filtratePlayer(msg.idx);
                    if (chess && player && player.UI) {
                        var point: port = chess.localToGlobal(new Laya.Point(), true);
                        var endpoint: port = player.UI.rank.localToGlobal(new Laya.Point());
                        if (endpoint && point) {
                            LudoBuffManager.instance.getJungleDice().fromTo(point, endpoint, this.gameUI, null);
                        }
                    }
                }
                break;
            case Buff.SNAIL_DICE://蜗牛骰子
                if (msg.idx == this.activated_id) {
                    yalla.Sound.playSound("Snail_get");
                    var chess: LudoChess = this.filtrateChess(msg.chessId);
                    var player: GamePlayer = this.filtratePlayer(msg.idx);
                    if (chess && player && player.UI) {
                        if (yalla.Global.isFouce) {
                            var point: port = chess.localToGlobal(new Laya.Point(), true);
                            var endpoint: port = player.UI.rank.localToGlobal(new Laya.Point());
                            if (endpoint && point) {
                                LudoBuffManager.instance.getJungleSnail().fromTo(point, endpoint, this.gameUI, Laya.Handler.create(player, player.getBuff, [msg.buff]));
                            }
                        } else {
                            player.getBuff(msg.buff);
                        }
                    }
                }
                break;
        }
    }
    showBuffDelay(player: GamePlayer, buff: number) {
        player && player.getBuff(buff);
    }

    handleUpdateGiftCnf(msg: any) {
        yalla.common.InteractiveGift.Instance.resData = msg;
        var posHash = {};
        for (var key in this.playerPool) {
            if (this.playerPool.hasOwnProperty(key)) {
                var player: GamePlayer = this.playerPool[key];
                if (player) {
                    var facePos = player.head.localToGlobal(new Laya.Point());
                    posHash[key] = { x: facePos.x, y: facePos.y };
                    if (player.head) player.head.isSendGift = !player.offLine && !this.isSpectator;
                    if (player.data.giftId > 0) {
                        var giftData = yalla.common.InteractiveGift.Instance.getGiftItemData(player.data.giftId);
                        if (giftData) player.head.showGift(giftData.id, giftData.icon);
                    } else {
                        player.head.btn_gift.visible = player.head.isSendGift;
                    }
                }
            }
        }
        yalla.common.InteractiveGift.Instance.handleUpdateGiftCnf(msg, posHash, this);
    }
    private updateGiftIcon(sendData: any): void {
        for (var key in this.playerPool) {
            if (this.playerPool.hasOwnProperty(key)) {
                if (key == 'player_' + sendData.recIdx) {
                    var player: GamePlayer = this.playerPool[key];
                    if (player) {
                        if (sendData.giftId > 0) {
                            player.head.showGift(sendData.giftId, sendData.icon);
                        }
                        // var giftId = sendData.giftId;
                        // var imgGift:Laya.Image = player.head['img_gift'];
                        // imgGift.skin = sendData.icon;//`test/${giftId}.png`;
                        // imgGift.pivot(imgGift.width / 2, imgGift.height / 2).pos(24, 22);
                        // imgGift.scale(0.75, 0.75);
                    }
                }
            }
        }
    }
    backHallClear() {
        this.timer.clearAll(this);
        this.clearQueue();
        NetMessageCache.getInstance().clearCache();
        this.loseNetWork();
        this.removeEventListener();
        Laya.timer.clearAll(this);
        if (this.fastChat) {
            this.fastChat.clear();
            this.fastChat = null;
        }
        if (this.resultView) {
            this.resultView.clear();
            Laya.timer.clearAll(this.resultView);
        }
        if (ludoRoom.instance.isDuel && DuelManager._instance) {
            DuelManager.instance.clear();
        }
        if (this.gameUI) {
            var [throw_jump, trust_jump, trust_wait] = [this.gameUI.throw_jump, this.gameUI.trust_jump, this.gameUI.trust_wait];
            if (throw_jump) { throw_jump.gotoAndStop(0); throw_jump.destroy(); }
            if (trust_jump) { trust_jump.gotoAndStop(0); trust_jump.destroy(); }
            if (trust_wait) { trust_wait.gotoAndStop(0); trust_wait.destroy(); }
        }
        ChessManger.instance.clear();
        EventPropManager._instanse && EventPropManager.instance.clearPool();
        LudoBuffManager._instance && LudoBuffManager.instance.clear();
        GridPropManager._instace && GridPropManager.instance.clear();
        this.clearPlayer();
    }
    clear() {
        super.clear();
        this.backHallClear();
        if (this.timeLine) {
            this.timeLine.pause();
            this.timeLine.offAll();
            this.timeLine.destroy();
            this.timeLine = null;
        }
        if (this.chat) {
            this.chat.clear();
            this.chat = null;
        }
        if (this.resultView) {
            this.resultView.destroy(true);
            this.resultView = null;
        }
        this.removeSelf();
        this.destroy(true);
    }
    removeEventListener() {
        super.removeEventListener();
        if (this.banner) {
            this.banner.removeEvent()
            this.banner.off("exit", this, this.exitHander);
        }
        Laya.timer.clear(this, this.frameLoopName);
        yalla.Voice.instance.off("change", this, this.voiceChange);
        yalla.Voice.instance.off("join", this, this.joinVoiceCallback);
        this.gameUI.reset.off("click", this, this.showResetInfo);
        this.gameUI.quick_buy_btn.off("click", this, this.buyCoin);
        this.removeTrustEvent();
        this.gameUI.off("click", this, this.pageHander);
        yalla.Mute.event.off(yalla.Mute.MUTE, this, this.onMute)//监听举报
        yalla.Mute.event.off(yalla.Mute.UNMUTE, this, this.unMute);//监听取消举报
        this.mapView && this.mapView.chess_box.off("click", this, this.clickChessBox);
        this.mouseEnabled = false;
        this.off(yalla.Native.instance.Event.ONBACKPRESSED, this, this.exitHander);
        yalla.Native.instance.off(yalla.Native.instance.Event.SPEAKER, this, this.handleSpeakResponse);
        yalla.Native.instance.off(yalla.Native.instance.Event.ONASSETSCHANGE, this, this.onAssetsChange);
        yalla.event.YallaEvent.instance.off("ludo_chat_my", this, this.onChat);//自己发送的消息
        yalla.common.InteractiveGiftAnimation.Instance.off(yalla.common.InteractiveGiftAnimation.Instance.Event.UpdateGiftIcon, this, this.updateGiftIcon);
    }
}