class JungleNovice extends BaseGame {
    private data = {
        "dice": {
            "chesses": [
                {
                    "chessId": 101,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 2
                },
                {
                    "chessId": 102,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 102
                },
                {
                    "chessId": 103,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 103
                },
                {
                    "chessId": 104,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 104
                },
                {
                    "chessId": 201,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 101
                },
                {
                    "chessId": 202,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 102
                },
                {
                    "chessId": 203,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 103
                },
                {
                    "chessId": 204,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 104
                }
            ],
            "props": [
                {
                    "area": 0,
                    "gridPosition": 5,
                    "jungleEvent": [
                        {
                            "eventStatus": 0,
                            "jungleEvent": 4
                        }
                    ]
                }
            ],
            "throwNum": [
                3,
                1
            ]
        },
        "tiger": {
            "chesses": [
                {
                    "chessId": 101,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 6
                },
                {
                    "chessId": 102,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 102
                },
                {
                    "chessId": 103,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 103
                },
                {
                    "chessId": 104,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 104
                },
                {
                    "chessId": 201,
                    "color": 2,
                    "area": 1,
                    "gridPosition": 4
                },
                {
                    "chessId": 202,
                    "color": 2,
                    "area": 1,
                    "gridPosition": 6
                },
                {
                    "chessId": 203,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 103
                },
                {
                    "chessId": 204,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 104
                }
            ],
            "props": [
                {
                    "area": 0,
                    "gridPosition": 9,
                    "jungleEvent": [
                        {
                            "eventStatus": 0,
                            "jungleEvent": 3
                        }
                    ]
                },
                {
                    "area": 1,
                    "gridPosition": 6,
                    "jungleEvent": [
                        {
                            "eventStatus": 0,
                            "jungleEvent": 5
                        }
                    ]
                }
            ],
            "throwNum": [
                3
            ]
        },
        "snail": {
            "chesses": [
                {
                    "chessId": 101,
                    "color": 0,
                    "area": 1,
                    "gridPosition": 11
                },
                {
                    "chessId": 102,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 102
                },
                {
                    "chessId": 103,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 103
                },
                {
                    "chessId": 104,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 104
                },
                {
                    "chessId": 201,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 101
                },
                {
                    "chessId": 202,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 102
                },
                {
                    "chessId": 203,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 103
                },
                {
                    "chessId": 204,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 104
                }
            ],
            "props": [
                {
                    "area": 2,
                    "gridPosition": 1,
                    "jungleEvent": [
                        {
                            "eventStatus": 0,
                            "jungleEvent": 6
                        }
                    ]
                }
            ],
            "throwNum": [
                3,
                1
            ]
        },
        "wind": {
            "chesses": [
                {
                    "chessId": 101,
                    "color": 0,
                    "area": 2,
                    "gridPosition": 2
                },
                {
                    "chessId": 102,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 102
                },
                {
                    "chessId": 103,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 103
                },
                {
                    "chessId": 104,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 104
                },
                {
                    "chessId": 201,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 101
                },
                {
                    "chessId": 202,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 102
                },
                {
                    "chessId": 203,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 103
                },
                {
                    "chessId": 204,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 104
                }
            ],
            "props": [
                {
                    "area": 2,
                    "gridPosition": 3,
                    "jungleEvent": [
                        {
                            "eventStatus": 0,
                            "jungleEvent": 7,
                            "windDirection": 2
                        }
                    ]
                },
                {
                    "area": 2,
                    "gridPosition": 5,
                    "jungleEvent": [
                        {
                            "eventStatus": 0,
                            "jungleEvent": 7,
                            "windDirection": 1
                        }
                    ]
                }
            ],
            "throwNum": [
                1,
                5
            ]
        },
        "cloud": {
            "chesses": [
                {
                    "chessId": 101,
                    "color": 0,
                    "area": 2,
                    "gridPosition": 9
                },
                {
                    "chessId": 102,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 102
                },
                {
                    "chessId": 103,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 103
                },
                {
                    "chessId": 104,
                    "color": 0,
                    "area": 0,
                    "gridPosition": 104
                },
                {
                    "chessId": 201,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 12
                },
                {
                    "chessId": 202,
                    "color": 2,
                    "area": 3,
                    "gridPosition": 1
                },
                {
                    "chessId": 203,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 103
                },
                {
                    "chessId": 204,
                    "color": 2,
                    "area": 2,
                    "gridPosition": 104
                }
            ],
            "props": [
                {
                    "area": 2,
                    "gridPosition": 12,
                    "jungleEvent": [
                        {
                            "eventStatus": 0,
                            "jungleEvent": 5
                        }
                    ]
                },
                {
                    "area": 2,
                    "gridPosition": 9,
                    "jungleEvent": [
                        {
                            "eventStatus": 0,
                            "jungleEvent": 2
                        }
                    ]
                },
                {
                    "area": 2,
                    "gridPosition": 12,
                    "jungleEvent": [
                        {
                            "eventStatus": 0,
                            "jungleEvent": 2
                        }
                    ]
                },
                {
                    "area": 3,
                    "gridPosition": 1,
                    "jungleEvent": [
                        {
                            "eventStatus": 0,
                            "jungleEvent": 2
                        }
                    ]
                }
            ],
            "throwNum": [
                3
            ]
        }
    };
    private mySelf: GamePlayer = null;
    private tip: ui.ludo.novice.jungle_novice_tipUI = null;
    private nextturnTip: ui.ludo.jungleMode.nexttrun_tipUI = null;
    private step: number = null;
    private chidStep: number = 0;
    private freeNovice: boolean = false;
    private stepOver: boolean = true;
    private noviceBubble: NoviceBubble = null;
    private throwNum: number = 0;
    private connectView: yalla.common.connect.ReconnectView = null;
    private tabData = [];
    private actionCode: number = 0;
    private tapType: string = "open";
    private tab_box: Laya.HBox = null;
    private maskSp: Laya.Sprite;
    constructor() {
        super();
        LudoBoardData.color = 0;
        ChessManger._instance && ChessManger.instance.clear();
        this.chessBubbleManager = new ChessBubbleManager();
        this.tab_box = new Laya.HBox();
        this.tab_box.dataSource = {
            centerX: 0, centerY: -440, space: 8, height: 118
        }
        this.gameUI.map_layer.addChild(this.tab_box);
        this.addMapView();
        Laya.loader.load('game/jungle_novice_bg.png', Laya.Handler.create(this, this.addMaskCheckBg));
        this.mapView.addChild(this.nextturnTip = new ui.ludo.jungleMode.nexttrun_tipUI());
        this.mapView.jungleGame = this.jungleGame = true;
        this.isOffGame = true;
        this.mapView.visible = true;
        this.banner.view.visible = false;
        yalla.Global.IsGameOver = false;
        (this.gameUI.reset as Laya.Box).visible = false;
        this.addChild(this.tip = new ui.ludo.novice.jungle_novice_tipUI());
        if (yalla.Font.lan == 'ar') {
            this.tip.end_tip.dataSource =
                this.tip.error_tip.dataSource = {
                    width: 900, align: "right",
                    stroke: 0, fontSize: 32
                }
        }
        this.mapView.addChild(this.noviceBubble = new NoviceBubble());
        this.addTipEvent();
        yalla.common.Confirm.instance.isExit = false;
        this.junglePropManager.isDebut = false;
        this.connectView = new yalla.common.connect.ReconnectView();
        this.connectView.count.text = "";
        this.maskSp = new Laya.Sprite();
        this.maskSp.rotation = -90;
        this.maskSp.pivot(this.mapView.width / 2, this.mapView.height / 2);
        this.maskSp.pos(this.mapView.width / 2, this.mapView.height / 2);
    }
    public backPressed() {
        if (this.tip.net_error.visible) {
            this.exit(true, "jungle", true);
        } else {
            this.exit();
        }
    }
    private addMaskCheckBg() {
        if (this.gameUI) {
            var img = new Laya.Image()
            img.dataSource = {
                skin: 'game/jungle_novice_bg.png',
                centerX: 0, centerY: 0
            }
            this.gameUI.map_layer.addChildAt(img, 0);
        }
    }
    private getJungleEventData() {//获取丛林模式新手引导数据
        this.tip.mask_bg.visible = false;
        this.tip.mask_bg.mouseThrough = true;
        this.connectView.show();
        yalla.Native.instance.getJungleEventData((res) => {
            try {
                if (typeof res === "string") {
                    res = JSON.parse(res);
                    this.tabDataInit(res);
                } else {
                    this.tabDataInit(res);
                }
            } catch (error) {
                this.connectView && this.connectView.hide();
            }
        })
    }
    public init(gameid, faceurl, name, realRoyLevel: string, viplevel: string) {
        // Laya.loader.load("res/json/jungleNovice.json", Laya.Handler.create(this, (e) => {
        //     this.data = e;
        this.playerInit(faceurl, name, realRoyLevel, viplevel);
        this.getJungleEventData();
        yalla.Native.instance.removeMatchView();
        // }))
    }
    private tabDataInit(res) {
        if (res && res.data) {
            let pool = {};
            this.tabData = [];
            res.data.forEach(item => {
                if (item) {
                    switch (Number(item.event)) {
                        case 1:
                            pool["cloud"] = item.open;
                            break;
                        case 2:
                            pool["tiger"] = item.open;
                            break;
                        case 3:
                            pool["dice"] = item.open;
                            break;
                        case 5:
                            pool["snail"] = item.open;
                            break;
                        case 6:
                            pool["wind"] = item.open;
                            break;
                    }
                }
            });
            ["dice", "tiger", "snail", "wind", "cloud"].forEach(sn => {
                if (pool[sn]) this.tabData.push(sn)
            })
            // this.tabData = ["dice", "tiger", "snail", "wind", "cloud"];
            this.connectView && this.connectView.hide();
            this.checkTab(0);
        } else {
            this.connectView && this.connectView.hide();
            this.showNetError();
        }
    }
    private showNetError() {
        this.tip.novice_end.visible = false;
        this.tip.net_error.visible = true;
        this.tip.mask_bg.visible = true;
        this.tip.mask_bg.mouseThrough = false;
        this.addNetErrorListener();
    }
    private addNetErrorListener() {
        this.removeNetErrorListener();
        this.tip.btn_tryAgain.on(Laya.Event.CLICK, this, this.getJungleEventData);
        this.tip.btn_back.on(Laya.Event.CLICK, this, this.exit, [true, "jungle", true]);
    }
    private removeNetErrorListener() {
        this.tip.btn_tryAgain.on(Laya.Event.CLICK, this, this.getJungleEventData);
        this.tip.btn_back.on(Laya.Event.CLICK, this, this.exit);
    }
    private playerInit(faceurl, name, realRoyLevel: string = "0", viplevel: string = "0") {
        this.mySelf = new GamePlayer(0, 0, false, { fPlayerInfo: { idx: 10001 }, chessSkinId: 11000 } as playerShowInfo);
        this.mySelf.showDice();
        yalla.Emoji.lateLable(yalla.util.filterName(name), this.mySelf.UI.nickName);
        this.mySelf.head.src = faceurl;


        var rLevel = Number(realRoyLevel)
        var info: any = {
            fPlayerInfo: {
                country: 0,
                faceId: 0,
                faceUrl: faceurl,
                ftype: 0,
                idx: 10001,
                level: 0,
                nikeName: name,
                placeId: 0,
                viplevel: Number(viplevel),
                winCount: 0,
                totalCount: 0
            },
            isFighted: 0,
            isInSysTrust: 0,
            score: 0,
            isSignUp: 0,
            sitNum: 0,
            isLeft: 0,
            winIndex: 0,
            roylevel: rLevel,
            realRoyLevel: rLevel
        }
        this.mySelf.setPlayerInfo(info);
        this.mySelf.head.removeEvent();
        // var player2: GamePlayer = new GamePlayer(0, 2, false, { fPlayerInfo: { idx: 10002 }, chessSkinId: 11000 } as playerShowInfo);
        (this.gameUI.players_layer as Laya.Box).addChild(this.mySelf);
        // (this.gameUI.players_layer as Laya.Box).addChild(player2);
        this.mySelf.UI && this.mySelf.UI.voice_btn.offAll();
        // player2.UI && player2.UI.voice_btn.offAll();
    }
    private resetDice() {
        this.hideFinger();
        this.mySelf.UI.throw_jump.visible = false;
        this.mySelf.UI.throw_jump.gotoAndStop(0);
        this.mySelf.dice.off("click", this, this.throwStart);
        this.mySelf.dice.stopAndShowDice(7);
    }
    private addDiceEvent() {
        this.mySelf.UI.throw_jump.visible = true;
        this.mySelf.UI.throw_jump.play(0, true);
        this.mySelf.dice.stopAndShowDice(7);
        this.mySelf.dice.off("click", this, this.throwStart);
        this.mySelf.dice.once("click", this, this.throwStart);
        Laya.timer.callLater(this, this.showFinger, [this.mySelf.dice]);
    }
    private throwStart() {
        this.stepOver = false;
        this.resetDice();
        this.mySelf.dice.play();
        Laya.timer.once(this.diceTime, this, this.throwEnd, [this.throwNum]);
    }
    private throwEnd(num) {
        this.mySelf.pushDice(num);
        this.mySelf.dice.stopAndShowDice(num);
        let chess: LudoChess = ChessManger.instance.getChessById(101);
        let station = LudoBoardData.distanceStation(chess.goalGrid.station, num, 0);
        let grid = LudoBoardData.getGridByStation(station);
        let hasProp = this.addPropAureole(grid.gridName);
        hasProp ? this.timer.once(1000, this, this.chooseChess, [num, chess]) : this.chooseChess(num, chess);
    }
    private chooseChess(num: number, chess: LudoChess) {
        chess.choose = true;
        chess.setChScale();
        chess.once(Laya.Event.CLICK, this, this.moveChess, [num]);
    }
    private moveChess(num: number, moveType: ChessMoveType = ChessMoveType.MOVE_NORMAL, delayTime: number = 0) {
        let chess: LudoChess = ChessManger.instance.getChessById(101);
        chess.choose = false
        this.mapView.chess_box.setChildIndex(chess, 7);
        let station = LudoBoardData.distanceStation(chess.goalGrid.station, num, 0);
        chess.offAll()
        chess.once(Laya.Event.END, this, this.moveEnd, [chess, delayTime]);
        switch (moveType) {
            case ChessMoveType.MOVE_TIGER:
                chess.on(Laya.Event.LABEL, this, this.onChessStep);
                chess.tigerMove(station);
                this.junglePropManager.showTigerClaw(chess.goalGrid.port);
                break;
            case ChessMoveType.MOVE_FORWARD_WIND:
                chess.tornadoMove(station, moveType);
                yalla.Sound.playSound("Wind_Go_Ahead");
                break;
            case ChessMoveType.MOVE_BACKWARD_WIND:
                chess.tornadoMove(station, moveType);
                yalla.Sound.playSound("Wind_Go_Back");
                break;
            default:
                chess.move(station);
                break;
        }
    }
    private showBubbleTip(tip: string, width: number, hideTime: number, bottom: number = 0, inLeft: boolean = true, goNext: boolean = true) {
        if (!this.destroyed && this.noviceBubble && !this.noviceBubble.destroyed) {
            this.noviceBubble.bottom = bottom;
            this.noviceBubble.showTip(tip, width, inLeft);
            // this.timer.once(hideTime, this, this.hideBubbleTip, [goNext]);
            this.off(Laya.Event.CLICK, this, this.clearTip);
            this.once(Laya.Event.CLICK, this, this.clearTip, [goNext]);
        }
    }
    private hideBubbleTip(goNext: boolean) {
        this.off(Laya.Event.CLICK, this, this.clearTip);
        this.noviceBubble.hideTip();
        goNext && this.nextStep();
    }
    private clearTip(goNext: boolean) {
        // this.timer.clear(this, this.hideBubbleTip);
        this.hideBubbleTip(goNext);
    }
    private moveEnd(chess: LudoChess, delayTime: number) {
        this.mySelf.clearDice(false);
        this.junglePropManager.hideTigerClaw();
        if (this.getStepKey() != "cloud") this.removeProp(chess.goalGrid.gridName);
        this.pileUp(chess, true)
        if (delayTime) {
            this.timer.once(delayTime, this, this.nextStep, [chess]);
        } else {
            this.nextStep(chess);
        }
    }
    private nextStep(chess: LudoChess = ChessManger.instance.getChessById(101)) {
        // console.log(this.step, this.chidStep);
        switch (this.getStepKey()) {
            case "dice":
                switch (this.chidStep) {
                    case 0://出现点击骰子引导 投掷3点 点击棋子 移动到骰子道具 后进行下一步
                        this.chidStep++;
                        this.throwNum = this.getStepData().throwNum[0];
                        this.addDiceEvent();
                        break;
                    case 1://出现提示气泡 5s后消失 提示玩家点击骰子
                        this.chidStep++;
                        var endpoint: port = this.mySelf.UI.dice_bg.localToGlobal(new Laya.Point());
                        var point: port = chess.localToGlobal(new Laya.Point());
                        if (endpoint && point) {
                            yalla.Sound.playSound("firework");;
                            LudoBuffManager.instance.getJungleDice()
                                .fromTo(point, endpoint, this.gameUI, Laya.Handler.create(this, this.showBubbleTip, [
                                    "You can roll the dice again once you get the dice.",
                                    525, 5000, 300, true
                                ]));
                        }
                        break;
                    case 2://提示玩家点击棋子并投掷1点 之后移动结束
                        this.chidStep++;
                        this.throwNum = this.getStepData().throwNum[1];
                        this.timer.callLater(this, this.addDiceEvent);
                        break;
                    default:
                        this.step++;
                        this.stepOver = true;
                        yalla.util.clogDBAmsg("10291");
                        this.timer.once(500, this, this.checkTab);
                        break;
                }
                break;
            case "tiger"://老虎有两个子步骤
                switch (this.chidStep) {
                    case 0:
                        this.chidStep++;
                        this.throwNum = this.getStepData().throwNum[0];
                        this.addDiceEvent();
                        break;
                    case 1:
                        this.chidStep++;
                        this.timer.callLater(this, this.showBubbleTip, [
                            `Tiger: You can move forward 15 squares and kill other players' tokens on the path.`,
                            525, 5000, 10, false
                        ])
                        break;
                    case 2:
                        this.chidStep++;
                        this.junglePropManager.showTiger();
                        this.timer.once(800, this, this.moveChess, [15, ChessMoveType.MOVE_TIGER]);
                        break;
                    case 3:
                        this.chidStep++;
                        this.timer.callLater(this, this.showBubbleTip, [
                            `Shield can protect tokens from Tiger.`,
                            525, 5000, 10, false
                        ])
                        break;
                    default:
                        this.step++;
                        this.stepOver = true;
                        yalla.util.clogDBAmsg("10292");
                        this.timer.once(500, this, this.checkTab, [this.step, true]);
                        break;
                }
                break;
            case "snail":
                switch (this.chidStep) {
                    case 0:
                        this.chidStep++;
                        this.throwNum = this.getStepData().throwNum[0];
                        this.addDiceEvent();
                        break;
                    case 1:
                        this.chidStep++;
                        this.timer.callLater(this, this.showBubbleTip, [
                            "Snail: Once you step on it, your next roll will definitely be 1.",
                            550, 5000, 10, false
                        ])
                        break;
                    case 2:
                        this.chidStep++;
                        var endpoint: port = this.mySelf.UI.dice_bg.localToGlobal(new Laya.Point());
                        var point: port = chess.localToGlobal(new Laya.Point());
                        if (endpoint && point) {
                            yalla.Sound.playSound("Snail_get");
                            LudoBuffManager.instance.getJungleSnail()
                                .fromTo(point, endpoint, this.gameUI, Laya.Handler.create(this, () => {
                                    this.showNextTip()
                                    this.throwNum = this.getStepData().throwNum[1];
                                    this.mySelf.getBuff(Buff.SNAIL_DICE);
                                    this.timer.once(1000, this, this.addDiceEvent);
                                }));
                        }
                        break;
                    default:
                        this.step++;
                        this.stepOver = true;
                        yalla.util.clogDBAmsg("10293");
                        this.timer.once(500, this, this.checkTab);
                        break;
                }
                break;
            case "wind":
                switch (this.chidStep) {
                    case 0:
                        this.chidStep++;
                        this.throwNum = this.getStepData().throwNum[0];
                        this.addDiceEvent();
                        this.timer.callLater(this, this.showBubbleTip, [
                            "Wind: If you step on it, you will move in the corresponding direction for 3-7 squares.",
                            650, 8000, 10, false
                        ])
                        break;
                    case 1:
                        this.chidStep++;
                        break;
                    case 2:
                        this.chidStep++;
                        this.timer.callLater(this, this.showBubbleTip, [
                            "This is a backward wind direction, so you will go backward.",
                            550, 6000, 10, false
                        ])
                        break;
                    case 3:
                        this.chidStep++;
                        var grid = ChessManger.instance.getChessById(101).goalGrid
                        this.junglePropManager.showTornado(grid);
                        this.timer.callLater(this, this.moveChess, [-3, ChessMoveType.MOVE_BACKWARD_WIND]);
                        break;
                    case 4:
                        this.chidStep++;
                        this.throwNum = this.getStepData().throwNum[1];
                        this.timer.callLater(this, this.addDiceEvent);
                        break;
                    case 5:
                        this.chidStep++;
                        this.timer.callLater(this, this.showBubbleTip, [
                            "This is a forward wind direction, go ahead!",
                            525, 6000, 10, false
                        ])
                        break;
                    case 6:
                        this.chidStep++;
                        this.junglePropManager.showTornado(ChessManger.instance.getChessById(101).goalGrid);
                        this.timer.callLater(this, this.moveChess, [4, ChessMoveType.MOVE_FORWARD_WIND, 1500]);
                        break;
                    default:
                        this.step++;
                        this.stepOver = true;
                        yalla.util.clogDBAmsg("10294");
                        this.timer.once(500, this, this.checkTab);
                        break;
                }
                break;
            case "cloud":
                switch (this.chidStep) {
                    case 0:
                        this.chidStep++;
                        this.throwNum = this.getStepData().throwNum[0];
                        this.addDiceEvent();
                        this.timer.callLater(this, this.showBubbleTip, [
                            `Lightning: Stay away from the square with electricity or stay in the square with Shield.`,
                            725, 8000, 10, false
                        ])
                        break;
                    case 1:
                        this.chidStep++;
                        break;
                    case 2:
                        this.chidStep++;
                        this.showNextTip();
                        this.timer.once(1500, this, () => {
                            var props = this.getStepData("cloud").props;
                            props.forEach(propdata => {
                                (propdata.jungleEvent as JungleStateEvent[]).forEach(item => {
                                    item.eventStatus = 1
                                });
                            })
                            this.flushProps(props);
                            let chess: LudoChess = ChessManger.instance.getChessById(202);
                            if (chess) chess.setStatus([ChessState.JUNGLE_PARALYSIS]);
                            this.timer.once(1500, this, this.nextStep);
                        })
                        break;
                    case 3:
                        this.chidStep++;
                        this.timer.callLater(this, this.showBubbleTip, [
                            `Your token will be struck by lightning in the next turn, and unable to move for a turn.`,
                            525, 5000, 10, false
                        ])
                        break;
                    default:
                        this.stepOver = true;
                        this.step++;
                        yalla.util.clogDBAmsg("10295");
                        let chess: LudoChess = ChessManger.instance.getChessById(202)
                        if (chess) chess.setStatus([ChessState.JUNGLE_NONE_STATE]);
                        this.timer.once(500, this, this.checkTab);
                        break;
                }
                break;
        }
    }
    private onChessStep(grid: LudoGrid, chessId: number) {
        console.log("onChessStep");
        let isShiled = false
        this.junglePropManager.catchSameProp(grid.gridName).forEach(prop => {
            if (prop.type == 5) {
                isShiled = true;
                (prop as ShieldProp).jungleState = {
                    jungleEvent: 5, eventStatus: 1
                }
                yalla.Sound.playSound("Shield_broken");
            }
        })
        let chesses: LudoChess[] = ChessManger.instance.getSameGridChesses(grid, chessId);
        chesses.forEach(chess => {
            if (!isShiled && chess.color != 0) this.timer.once(165, this, this.hitMove, [chess]);
        })

    }
    private hitMove(chess: LudoChess) {
        chess.offAll();
        yalla.Sound.playSound("1kills");
        chess.once(Laya.Event.END, this, this.hitMoveEnd, [chess]);
        chess.move(chess.homeStation, ChessMoveType.MOVE_BE_HIT);
    }
    private hitMoveEnd(chess: LudoChess) {
        chess.scale(ChessManger._normalScale, ChessManger._normalScale);
    }
    private getStepKey() {
        return this.tabData[this.step];
    }
    private getStepData(key: string = this.getStepKey()) {
        return JSON.parse(JSON.stringify(this.data[key]));
    }
    private flushChesses() {
        let list = this.getStepData().chesses;
        for (let j = 0; j < list.length; j++) {
            let chessInfo = list[j];
            var chess = ChessManger.instance.getChessById(chessInfo.chessId);
            if (!chess) {
                var chColor: number = chessInfo.color;
                chess = new LudoChess(null, chColor);
                var [area, gridPosition] = [chessInfo.color, 101 + j % 4];
                chess.homeGrid = LudoBoardData.getGridByStation({ area, gridPosition })
                this.mapView.chess_box.addChild(chess);
                chess.data = chessInfo;
                ChessManger.instance.addNewChess(chess);
            } else {
                chess.choose = false;
                chess.data = chessInfo;
            }
            chess.moving = false;
            chess.choose = false;
            chess.clearTimeLine();
            chess.offAll();
            chess.setStatus(null)
            chess.setChScale();
        }
    }
    private flushTab() {
        let width = (this.tabData.length - 1) * 8;
        this.tab_box.removeChildren();
        this.tabData.forEach((iconName, index) => {
            var tab: ui.ludo.novice.jungle_tabUI = this.tab_box.getChildAt(index) as ui.ludo.novice.jungle_tabUI;
            if (!tab) {
                tab = Laya.Pool.getItemByCreateFun("jungle_novice_tab", this.createTab, this);
                this.tab_box.addChild(tab);
            } else {
                tab.offAll();
            }
            width += tab.width;
            let abled = this.step === index;
            tab.icon.skin = `jungleNovice/ico_${iconName}.png`;
            tab.bg.skin = abled ? "jungleNovice/bg_abled.png" : "jungleNovice/bg_disabled.png";
            tab.abled.visible = abled;
            this.freeNovice && tab.on("click", this, this.clickTab, [index]);
        })
        this.tab_box.width = width;
    }
    private noviceEnd() {
        this.tip.mask_bg.visible = true;
        this.tip.mask_bg.mouseThrough = false;
        this.tip.novice_end.visible = true;
        this.tip.net_error.visible = false;
        this.hideFinger();
        this.addNoviceEndLisenter();
        if (!this.freeNovice) {
            yalla.util.clogDBAmsg("200022", null, 3);
        }
    }
    private addNoviceEndLisenter() {
        this.removeNoviceLisenter();
        this.tip.btn_playNow.on(Laya.Event.CLICK, this, this.playNow);
        this.tip.btn_watchAgain.on(Laya.Event.CLICK, this, this.watchAgain);
    }
    private playNow() {
        this.removeNoviceLisenter();
        yalla.util.clogDBAmsg("10303");
        yalla.Native.instance.quickGame({
            gamePay: 0,
            gameId: 10019,
            playerNum: 4,
            gameType: 0,
            gameGroup: 0,
            isPrivate: 10
        });
        this.clear();
    }
    private watchAgain() {
        yalla.util.clogDBAmsg("10296");
        this.freeNovice = true;
        this.removeNoviceLisenter();
        this.chidStep = 0;
        this.checkTab(0);
        this.tip.mask_bg.visible = false;
        this.tip.mask_bg.mouseThrough = true;
        this.showBubbleTip("Tap the icon to check rules as you like!", 525, 5000, 300, null, false);
        // this.tip.next_btn.visible = true;
    }
    private removeNoviceLisenter() {
        this.tip.btn_playNow.off(Laya.Event.CLICK, this, this.playNow);
        this.tip.btn_watchAgain.off(Laya.Event.CLICK, this, this.watchAgain);
    }
    private clickTab(index: number) {
        if (!this.stepOver) return;
        this.checkTab(index);
        switch (this.getStepKey()) {
            case "dice":
                yalla.util.clogDBAmsg("10297");
                break;
            case "tiger":
                yalla.util.clogDBAmsg("10298");
                break;
            case "snail":
                yalla.util.clogDBAmsg("10299");
                break;
            case "wind":
                yalla.util.clogDBAmsg("10300");
                break;
            case "cloud":
                yalla.util.clogDBAmsg("10301");
                break;
        }
    }
    private checkTab(index: number = this.step) {
        if (index >= this.tabData.length) {
            this.noviceEnd();
            return;
        }
        var first = this.step == null;
        this.timer.clearAll(this);
        this.step = index;
        this.chidStep = 0;
        this.junglePropManager.hideDarkCloud();
        this.noviceBubble.hideTip();
        this.off(Laya.Event.CLICK, this, this.clearTip);
        // this.timer.clear(this, this.hideBubbleTip);
        this.flushTab();
        this.mySelf.clearDice(false);
        this.mySelf.clearBuff();
        this.resetDice();
        if (first) {
            this.flushChesses();
            this.junglePropManager.recoverAllIcon();
            this.junglePropManager.recoverAllAni();
            this.flushProps();
            this.nextStep();
        } else {
            var angel = 360;
            this.maskSp.graphics.drawPie(this.mapView.width / 2, this.mapView.height / 2, 550, 0, angel, "#ffffff");
            this.mapView.mask = this.maskSp;
            this.drawPie2(angel, (n) => {
                if (n == 1) {
                    this.flushChesses();
                    this.junglePropManager.recoverAllIcon();
                    this.junglePropManager.recoverAllAni();
                } else if (n == 2) {
                    this.flushProps();
                    this.nextStep();
                    this.mapView.mask = null;
                }
            });
        }
    }
    private createTab(): ui.ludo.novice.jungle_tabUI {
        return new ui.ludo.novice.jungle_tabUI();
    }
    private flushProps(props?) {
        var JungleGrids = props || this.getStepData().props;
        this.junglePropManager.eachAll(prop => {
            if (prop['aureole'])
                prop['aureole'].stop();
        })
        this.junglePropManager.refresh({ propGrid: JungleGrids }, this.mapView);
    }
    private addPropAureole(gridName: string): boolean {
        let hasProp = false
        this.junglePropManager.catchSameProp(gridName).forEach(prop => {
            if (prop) {
                hasProp = true;
                if (prop['aureole']) {
                    prop['aureole'].play(0, false);
                } else if (prop.mode == "icon") {
                    this.showAureole(prop);
                }
            }
        })
        return hasProp;
    }
    private removeProp(gridName: string) {
        this.junglePropManager.catchSameProp(gridName).forEach(prop => {
            if (prop) {
                if (prop['aureole']) {
                    prop['aureole'].stop();
                    prop['aureole'].recover();
                    prop['aureole'] = null;
                }
                this.junglePropManager.recoverIconByName(prop.name);
                if (prop.type != 5) this.junglePropManager.recoverAniByName(prop.name);
            }
        })
    }

    private addTipEvent() {
        this.tip.next_btn.on("click", this, this.exit);
        this.on(yalla.Native.instance.Event.ONBACKPRESSED, this, this.backPressed);
    }
    private removeTipEvent() {
        this.tip && this.tip.next_btn.off("click", this, this.exit);
        this.off(yalla.Native.instance.Event.ONBACKPRESSED, this, this.backPressed);
    }
    private drawPie2(angel: number, cb: Function) {
        angel -= 20;
        this.maskSp.graphics.clear();
        if (angel > 0)
            this.maskSp.graphics.drawPie(this.mapView.width / 2, this.mapView.height / 2, 550, 0, angel, "#ffffff");
        else
            this.maskSp.graphics.drawPie(this.mapView.width / 2, this.mapView.height / 2, 550, angel, 0, "#ffffff");
        if (angel == 0)
            cb(1);
        if (angel <= -360) {
            cb(2);
            return;
        }
        Laya.timer.frameOnce(1, this, this.drawPie2, [angel, cb]);
    }

    private exit(playsound: boolean = true, novice: string = "", netErrTip: boolean = false) {
        yalla.util.clogDBAmsg("10302");
        yalla.common.Confirm.instance.isExit = true;
        playsound && yalla.Sound.playSound("click");
        yalla.common.Confirm.instance.showConfirm(netErrTip ? "Are you sure to quit?" : "Are you sure to skip tutorials?", Laya.Handler.create(this, () => {
            // yalla.Native.instance.noviceComplete();
            yalla.util.clogDBAmsg("10248");
            yalla.Native.instance.backHall(true, { to: novice ? "" : "jungle", novice });
            // yalla.Native.instance.mobClickEvent(buryPoint.TUTORIAL_SKIP_TUTORIAL);//埋点返回大厅
            // this.accountActionRecordAdd(this.actionCode, this.tapType, this.actionCode == 11 ? 1 : 0, 1)
        }), Laya.Handler.create(this, () => {
            yalla.common.Confirm.instance.hideConfirm();
        }), ["Confirm", "Cancel"])
    }
    private showNextTip() {
        if (this.nextturnTip) {
            this.nextturnTip.visible = true;
            this.nextturnTip.showtip.play(0, false);
        }
    }
    public onResize() {
        super.onResize();
        if (this.tip && this.tip.finger && this.tip.finger.visible) {
            this.showFinger(this.fighterNode);
        }
    }
    private fighterNode: Laya.Sprite = null
    private showFinger(node?: Laya.Sprite) {
        if (node) {
            this.fighterNode = node;
            let point = node.localToGlobal(new Laya.Point(), true);
            this.tip.finger.pos(point.x + node.width / 2, point.y + node.height / 2);
            this.tip.finger.visible = true;
        }
    }
    private hideFinger() {
        this.tip.finger.visible = false;
    }
    public showAureole(prop: any) {
        if (!prop.aureole) {
            prop.aureole = LudoBuffManager.instance.getAureoleProp();
            prop.addChildAt(prop.aureole, 0);
        }
        prop.aureole.play(0, false);
    }
    public clear() {
        super.clear();
        this.backHallClear();
        this.destroy();
        this._junglePropManager = null;
        LudoBuffManager.instance.clear();
        LudoBuffManager._instance = null;
        ChessManger.instance.clear()
        ChessManger._instance = null;
    }
    public backHallClear() {
        this.removeTipEvent();
        this.timer.clearAll(this);
        this.removeNetErrorListener();
        this.removeNoviceLisenter();
        if (this.connectView) {
            this.connectView.clear();
            this.connectView = null;
        }
        if (this.nextturnTip) {
            this.nextturnTip.showtip.stop();
            this.nextturnTip.destroy(true);
            this.nextturnTip = null;
        }
    }
}