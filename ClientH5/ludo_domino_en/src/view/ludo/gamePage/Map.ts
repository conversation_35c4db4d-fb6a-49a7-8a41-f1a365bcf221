class Map extends ui.ludo.page.mapUI {
    // public _map: Maps = null;
    static _instance: Map = null;
    public _color: number = 0;
    private skin_id: number = 13000;
    private templet: Laya.Templet;
    private _sk: Laya.Skeleton = null;
    constructor() {
        super();
        this.mouseThrough = false;
        this.visible = false;
    }
    set jungleGame(val: boolean) {
        if (val) {
            this.bg.skin = "game/Base_jungle.png";
            this.check.skin = "game/Checkerboard_jungle.png"
        }
    }
    set skinId(val: number) {
        if (val != this.skin_id && val > 13000) {
            this.skin_id = val;
            var fevent = yalla.event.YallaEvent.instance;
            fevent.once(`Checkerboard_${val}.png`, this, () => {
                !this.destroyed && this.setSkin(this.skin_id);
            })
            fevent.once(`Base_${val}.png`, this, () => {
                !this.destroyed && this.setSkin(this.skin_id);
            })
            yalla.File.getFileByNative(`Checkerboard_${val}.png`);
            yalla.File.getFileByNative(`Base_${val}.png`);
            if (yalla.Skin.instance.isDynamic(val)) {
                fevent.once(`BGANI_${val}.sk`, this, () => {
                    if (this.destroyed) return;
                    this.templet = new Laya.Templet();
                    this.templet.on(Laya.Event.COMPLETE, this, this.loadSk);
                    this.templet.loadAni(yalla.File.filePath + `BGANI_${val}.sk`);
                })
                fevent.once(`BGANI_${val}.png`, this, () => {
                    yalla.File.getFileByNative(`BGANI_${val}.sk`);
                })
                yalla.File.getFileByNative(`BGANI_${val}.png`);
            }
        }
    }

    public stopAni() {
        this._sk && this._sk.paused();
    }
    public startAni() {
        this._sk && this._sk.play(1, true);
    }
    showKillTip() { }
    hideVisibility() { }
    removeMask() { }
    showGolbleEye() { }
    private setSkin(skinid: number) {
        var [basePath, checkPath] = [yalla.File.cachePath + `/Base_${skinid}.png`, yalla.File.cachePath + `/Checkerboard_${skinid}.png`];
        if (yalla.File.existed(basePath) && yalla.File.existed(checkPath)) {
            this.check.skin = yalla.File.filePath + `Checkerboard_${skinid}.png`;
            this.bg.skin = yalla.File.filePath + `Base_${skinid}.png`;
        }
    }
    get skinId(): number {
        return this.skin_id;
    }
    private loadSk() {
        if (this.templet) {
            this._sk = this.templet.buildArmature(0);
            this.addChildAt(this._sk, 2);
            this._sk.pos(this.width / 2, this.height / 2);
            this._sk.play(1, true);
        }
    }
    clear() {
        if (this.templet) {
            this.templet.destroy();
            this.templet.offAll();
            this.templet = null;
        }
        if (this._sk) {
            this._sk.destroy();
            this._sk = null;
        }
        this.destroy(true);
    }
    set color(color: number) {
        this._color = color;
        var rotation = -color * 90
        this.check.rotation = rotation;
        // this._map = MapData.getMap(this._color);
        LudoBoardData.color = this._color;
        this.visible = true;
        this.arrow_bg.visible = Boolean(ludoRoom.instance.type == RoomType.ARROW);
    }
    // getPos(val: Station, departurePos: number = 101): port {
    //     return LudoBoardData.getGridByStation(val).port;
    // }
}