/**
 * 新手引导页
 */
class NoviceGame extends BaseGame {
    private step1 = [{
        "chessId": 201,
        "color": 2,
        "area": 2,
        "gridPosition": 101,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 202,
        "color": 2,
        "area": 2,
        "gridPosition": 102,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 203,
        "color": 2,
        "area": 2,
        "gridPosition": 103,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 204,
        "color": 2,
        "area": 2,
        "gridPosition": 104,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 101,
        "color": 0,
        "area": 0,
        "gridPosition": 101,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 102,
        "color": 0,
        "area": 0,
        "gridPosition": 102,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 103,
        "color": 0,
        "area": 0,
        "gridPosition": 103,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 104,
        "color": 0,
        "area": 0,
        "gridPosition": 104,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }];
    private step5 = [{
        "chessId": 201,
        "color": 2,
        "area": 2,
        "gridPosition": -6,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 202,
        "color": 2,
        "area": 2,
        "gridPosition": -6,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 203,
        "color": 2,
        "area": 3,
        "gridPosition": 12,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 204,
        "color": 2,
        "area": 2,
        "gridPosition": -6,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 101,
        "color": 0,
        "area": 0,
        "gridPosition": -6,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 102,
        "color": 0,
        "area": 0,
        "gridPosition": -6,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 103,
        "color": 0,
        "area": 3,
        "gridPosition": 10,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }, {
        "chessId": 104,
        "color": 0,
        "area": 0,
        "gridPosition": -6,
        "isWin": 0,
        "chessSkin": 1,
        "chessLinkId": -1,
        "isMaster": 0
    }];
    private mySelf: GamePlayer = null;
    private chessPool: Object;
    private noviceTip: ui.ludo.novice.novice_tipUI;
    private stepNum: number = 1;
    private activChess: LudoChess = null;
    private template: Laya.Templet;
    private sk: Laya.Skeleton;
    private tw: Laya.Tween;
    private canNext: boolean = true;
    private maskSp: Laya.Sprite;
    private actionCode: number = 0;
    private tapType: string = "open";
    constructor() {
        super();
        LudoBoardData.color = 0;
        this.addMapView();
        this.isOffGame = true;
        this.mapView.visible = true;
        this.banner.view.visible = false;
        // (this.gameUI.head as Laya.Box).visible = false;
        (this.gameUI.reset as Laya.Box).visible = false;
        this.noviceTip = new ui.ludo.novice.novice_tipUI();
        this.addChild(this.noviceTip);
        this.template = new Laya.Templet();
        this.template.on(Laya.Event.COMPLETE, this, this.loadedTp);
        this.template.loadAni(yalla.getSkeleton("voice_ludo/skeleton"));
        this.noviceTip.voice_btn.y = Laya.stage.height - 70;
        this.noviceTip.btn_ok.on("click", this, this.noiveEnd);
        this.maskSp = new Laya.Sprite();
        this.maskSp.rotation = -90;
        this.maskSp.pivot(this.mapView.width / 2, this.mapView.height / 2);
        this.maskSp.pos(this.mapView.width / 2, this.mapView.height / 2);
        if (yalla.Font.isRight()) {
            this.noviceTip.tip.left = 140;
            this.noviceTip.tip.align = "right";
        }
        this.accountActionRecordAdd(1, "open");
        this.accountActionRecordAdd(1, "click");
        this.accountActionRecordAdd(2, "open");
    }

    private noiveEnd() {
        this.accountActionRecordAdd(11, "click", 1);
        yalla.Native.instance.noviceComplete();
        //TODO::1.3.9 从返回大厅 变更为 快速匹配游戏 500场 经典 2人模式 
        yalla.util.clogDBAmsg('10231');
        yalla.Native.instance.quickGame({
            gamePay: 500,
            gameId: 10019,
            playerNum: 2,
            gameType: 0,
            gameGroup: 0
        });
        // yalla.Native.instance.backHall(true);
    }
    public clear() {
        this.backHallClear();
        this.destroy();
    }
    public backHallClear() {
        if (this.template) {
            !this.template.released && this.template.releaseResource();
            this.template.destroy();
            this.template = null;
        }
        this.sk && this.sk.destroy();
        this.noviceTip.btn_ok.off("click", this, this.noiveEnd);
        this.noviceTip.next_btn.off("click", this, this.exit);
        this.off(yalla.Native.instance.Event.ONBACKPRESSED, this, this.exit);

    }
    private loadedTp() {
        if (!this.template) return;
        this.sk = this.template.buildArmature(0);
        this.sk.scale(0.5, 0.5);
        this.sk.pos(59, 32);
        this.noviceTip.voice_btn.addChild(this.sk);
    }
    private showVoiceAni() {
        this.noviceTip.mask_bg.visible = true;
        this.noviceTip.mask_bg.mouseThrough = false;
        this.noviceTip.next_btn.visible = false;
        this.hideFinger();
        this.sk && this.sk.play(0, true);
        this.tw = Laya.Tween.to(this.noviceTip.voice_btn, {
            x: Laya.stage.width / 2,
            y: Laya.stage.height / 2 - 130,
            scaleX: 1.5,
            scaleY: 1.5
        }, 1000, null, Laya.Handler.create(this, () => {
            this.showTip("On Yalla Ludo, you can voice chat with other players during the game.");
            this.noviceTip.btn_ok.label = 'Play Now';
            this.noviceTip.btn_ok.visible = true;
        }))
    }
    public init(gameid, faceurl, name, realRoyLevel: string, viplevel: string) {
        this.noviceBInit(faceurl, name);
        yalla.Native.instance.removeMatchView();
    }
    private noviceAInit() { }
    private nextStep() {
        if (!this.canNext) return;
        this.stepNum++;
        this.step(this.stepNum);
    }
    public onResize() {
        super.onResize();
        if (this.noviceTip && this.noviceTip.finger && this.noviceTip.finger.visible) {
            this.showFinger(this.fighterNode);
        }
    }
    private fighterNode: Laya.Sprite = null
    private showFinger(node?: Laya.Sprite) {
        if (node) {
            this.fighterNode = node;
            let point = node.localToGlobal(new Laya.Point(), true);
            this.noviceTip.finger.pos(point.x + node.width / 2, point.y + node.height / 2);
            this.noviceTip.finger.visible = true;
        }
    }
    private hideFinger() {
        this.noviceTip.finger.visible = false;
    }
    private noviceBInit(faceurl, name) {
        this.playerInit(faceurl, name);
        this.chessInit(this.step1);
        this.noviceTip.next_btn.on("click", this, this.exit);
        this.timer.frameOnce(2, this, this.step, [this.stepNum]);
        this.on(yalla.Native.instance.Event.ONBACKPRESSED, this, this.exit, [false]);
    }
    private exit(playsound: boolean = true) {
        yalla.common.Confirm.instance.isExit = true;
        playsound && yalla.Sound.playSound("click");
        yalla.common.Confirm.instance.showConfirm("Are you sure to skip tutorials?", Laya.Handler.create(this, () => {
            yalla.Native.instance.noviceComplete();
            yalla.Native.instance.backHall(true);
            yalla.Native.instance.mobClickEvent(buryPoint.TUTORIAL_SKIP_TUTORIAL);//埋点返回大厅
            this.accountActionRecordAdd(this.actionCode, this.tapType, this.actionCode == 11 ? 1 : 0, 1)
        }), Laya.Handler.create(this, () => {
            yalla.common.Confirm.instance.hideConfirm();
        }), ["Confirm", "Cancel"])
    }
    private drawPie2(angel: number, cb: Function) {
        angel -= 20;
        this.maskSp.graphics.clear();
        if (angel > 0)
            this.maskSp.graphics.drawPie(this.mapView.width / 2, this.mapView.height / 2, 550, 0, angel, "#ffffff");
        else
            this.maskSp.graphics.drawPie(this.mapView.width / 2, this.mapView.height / 2, 550, angel, 0, "#ffffff");
        if (angel == 0)
            cb(1);
        if (angel <= -360) {
            cb(2);
            return;
        }
        Laya.timer.frameOnce(1, this, this.drawPie2, [angel, cb]);
    }
    private playerInit(faceurl, name) {
        this.mySelf = new GamePlayer(0, 0, false, { fPlayerInfo: { idx: 10001 }, chessSkinId: 11000 } as playerShowInfo);
        this.mySelf.showDice();
        yalla.Emoji.lateLable(yalla.util.filterName(name), this.mySelf.UI.nickName);
        this.mySelf.head.src = faceurl;
        var player2: GamePlayer = new GamePlayer(0, 2, false, { fPlayerInfo: { idx: 10002 }, chessSkinId: 11000 } as playerShowInfo);
        (this.gameUI.players_layer as Laya.Box).addChild(this.mySelf);
        (this.gameUI.players_layer as Laya.Box).addChild(player2);

        this.mySelf.UI && this.mySelf.UI.voice_btn.offAll();
        player2.UI && player2.UI.voice_btn.offAll();
    }
    private showTip(msg: string) {
        if (!this.noviceTip.tip_ui.visible) this.noviceTip.tip_ui.visible = true;
        this.noviceTip.tip.text = msg;
    }
    private hideTip() {
        this.noviceTip.tip_ui.visible = false;
    }
    private accountActionRecordAdd(actionCode: number = this.actionCode, tapType: string = this.tapType, isAllDone: number = 0, isSkip: number = 0) {
        this.actionCode = actionCode;
        this.tapType = tapType;
        yalla.Native.instance.accountActionRecordAdd(actionCode, isSkip, isAllDone, tapType == "open" ? 1 : 2);
    }
    private step(num) {
        switch (num) {
            case 1://投掷骰子
                this.showTip("Tap the dice to roll it.");
                Laya.timer.frameOnce(10, this, () => {
                    this.showFinger(this.mySelf.dice);
                })
                this.mySelf.UI.throw_jump.visible = true;
                this.mySelf.UI.throw_jump.play(0, true);
                this.mySelf.dice.once("click", this, () => {
                    this.accountActionRecordAdd(2, "click");
                    this.throwNum(6, () => {
                        this.accountActionRecordAdd(3, "open");
                        this.nextStep();
                    })
                }/*, [6, () => {
                }]*/);
                break;
            case 2://第一次摇到6
                this.mySelf.pushDice(6);
                this.mySelf.dice.stopAndShowDice(6);
                this.mySelf.dice.off("click", this, this.throwNum);
                this.showTip("Great! You've got a 6. Let's roll again.");
                Laya.timer.frameOnce(1, this, () => {
                    this.showFinger(this.mySelf.dice);
                })
                this.mySelf.dice.once("click", this, () => {
                    this.accountActionRecordAdd(3, "click");
                    this.throwNum(5, () => {
                        this.nextStep();
                        this.accountActionRecordAdd(4, "open");
                    })
                });
                break;
            case 3://摇到5
                this.mySelf.pushDice(5);
                this.mySelf.dice.stopAndShowDice(5);
                this.mySelf.dice.off("click", this, this.throwNum);
                this.mySelf.UI.throw_jump.visible = false;
                this.mySelf.UI.throw_jump.gotoAndStop(0);
                this.showTip("Tap tokens to move them out of the nest.");
                this.showFinger(this.chessPool["chess_101"]);
                for (var key in this.chessPool) {
                    if (this.chessPool.hasOwnProperty(key)) {
                        var ch = this.chessPool[key];
                        if (ch.color == 0) {
                            ch.choose = true;
                            ch.on("click", this, (chess) => {
                                this.accountActionRecordAdd(4, "click");
                                this.hideFinger();
                                this.activChess = chess;
                                this.nextStep();
                            }, [ch])
                        }
                    }
                }
                break;
            case 4://从起飞点出来
                this.hideTip();
                this.removeChessEv();
                if (!this.activChess) {
                    this.activChess = this.chessPool["chess_101"];
                    var port = LudoBoardData.getPosByStation({ area: 0, gridPosition: 2 });
                    this.activChess.pos(port.x, port.y);
                    this.step4();
                } else {
                    this.moveChess(this.activChess, { area: 0, gridPosition: 2 }, () => {
                        this.step4();
                    })
                }
                break;
            case 5://击杀提示 转场动画

                this.accountActionRecordAdd(6, "open");
                this.showTip("Look, there is an opponent's token. Go and kill it to earn Exps! ");
                this.hideFinger();
                this.mySelf.clearDice(false);
                this.mySelf.stopAndShowDice(7);
                for (var key in this.chessPool) {
                    if (this.chessPool.hasOwnProperty(key)) {
                        var ch = this.chessPool[key];
                        if (ch.color == 0) {
                            ch.choose = false;
                            ch.offAll();
                        }
                    }
                }
                if (this.activChess.goalStation.gridPosition == 2) {
                    var port = LudoBoardData.getPosByStation({ area: 0, gridPosition: 7 });
                    this.activChess.pos(port.x, port.y);
                    this.activChess.scale(0.8, 0.8);
                }
                this.canNext = false;
                var angel = 360;
                this.maskSp.graphics.drawPie(this.mapView.width / 2, this.mapView.height / 2, 550, 0, angel, "#ffffff");
                this.mapView.mask = this.maskSp;
                this.drawPie2(angel, (n) => {
                    if (n == 1)
                        this.chessInit(this.step5);
                    if (n == 2) {
                        this.mapView.mask = null;
                        this.canNext = true;
                        this.showFinger(this.mySelf.dice);
                        (this.gameUI.map_layer as Laya.Box).addChildAt(this.mapView, 0);
                        this.mySelf.UI.throw_jump.visible = true;
                        this.mySelf.UI.throw_jump.play(0, true);
                        this.mySelf.dice.off("click", this, this.throwNum);
                        this.mySelf.dice.once("click", this, () => {
                            this.accountActionRecordAdd(6, "click");
                            this.throwNum(2, () => {
                                this.accountActionRecordAdd(7, "open");
                                this.hideFinger();
                                this.nextStep();
                            })
                        });
                    }
                });
                break;
            case 6://摇到2
                this.showTip("Tap your token.");
                this.mySelf.pushDice(2);
                this.mySelf.dice.stopAndShowDice(2);
                this.mySelf.dice.offAll();
                this.mySelf.UI.throw_jump.visible = false;
                this.mySelf.UI.throw_jump.gotoAndStop(0);
                this.activChess = this.chessPool["chess_103"];
                this.showFinger(this.activChess);
                this.activChess.choose = true;
                this.activChess.scale(1, 1);
                this.activChess.on("click", this, () => {
                    this.accountActionRecordAdd(7, "click");
                    this.activChess.choose = false;
                    this.mySelf.clearDice(false);
                    this.activChess.offAll();
                    this.moveChess(this.activChess, {
                        area: 3,
                        gridPosition: 12
                    }, () => {
                        this.accountActionRecordAdd(8, "open");
                        var reChess = this.chessPool["chess_203"] as LudoChess;
                        yalla.Sound.playSound("1kills"/*, Laya.Handler.create(this, () => {
                            yalla.Sound.playSound("voice_smile");
                        })*/);
                        this.moveChess(reChess, reChess.homeStation, () => {
                            this.nextStep();
                            this.mapView.chess_box.setChildIndex(this.activChess, 7);
                            this.playChessEmoji(reChess, "cry");
                            this.playChessEmoji(this.activChess, "cool");
                        }, ChessMoveType.MOVE_BE_HIT)
                    })
                })
                break;
            case 7://击杀对手 再次投掷机会
                this.mySelf.dice.offAll();
                this.activChess.offAll();
                var reChess = this.chessPool["chess_203"] as LudoChess;
                if (reChess.goalStation.gridPosition < 100) {
                    reChess.reset();
                    this.activChess.breakLink(LudoBoardData.getGridByStation({ area: 3, gridPosition: 12 }));
                    this.activChess.choose = false;
                    this.activChess.scale(0.8, 0.8);
                    this.mySelf.clearDice(false);
                }
                this.showTip("You can roll again after killing other players' token.");
                this.showFinger(this.mySelf.dice);
                this.mySelf.UI.throw_jump.visible = true;
                this.mySelf.UI.throw_jump.play(0, true);
                this.mySelf.dice.off("click", this, this.throwNum);
                this.mySelf.dice.once("click", this, () => {
                    this.accountActionRecordAdd(8, "click");
                    this.throwNum(6, () => {
                        this.nextStep();
                    });
                })
                break;
            case 8://摇到6
                this.accountActionRecordAdd(9, "open");
                this.hideTip();
                this.showTip("Let's move to the end and win the game!");
                this.mySelf.pushDice(6);
                this.mySelf.dice.stopAndShowDice(6);
                this.mySelf.dice.off("click", this, this.throwNum);
                this.mySelf.dice.once("click", this, () => {
                    this.accountActionRecordAdd(9, "click");
                    this.throwNum(1, () => {
                        this.nextStep();
                    })
                })
                break;
            case 9://摇到1 后选择棋子
                this.accountActionRecordAdd(10, "open");
                this.mySelf.dice.offAll();
                this.mySelf.UI.throw_jump.visible = false;
                this.mySelf.UI.throw_jump.gotoAndStop(0);
                this.hideFinger();
                this.mySelf.pushDice(1);
                this.mySelf.dice.stopAndShowDice(1);
                this.activChess.choose = true;
                this.showFinger(this.activChess);
                this.activChess.scale(1, 1);
                this.activChess.once("click", this, () => {
                    this.hideFinger();
                    this.chooseStep.pos(this.activChess.x + 7, this.activChess.y);
                    this.chooseStep.show([6, 1], 0);
                    this.chooseStep.on("choose", this, (num) => {
                        this.accountActionRecordAdd(10, "click");
                        this.chooseStep.offAll();
                        this.chooseStep.hide();
                        var s1 = {
                            area: 0,
                            gridPosition: 0
                        }, s2 = {
                            area: 0,
                            gridPosition: -5
                        };
                        if (num == 6) {
                            [s1, s2] = [s2, s1];
                        }
                        this.moveChess(this.activChess, s1, () => {
                            this.mySelf.popDice(num);
                            this.activChess.offAll();
                            Laya.timer.once(100, this, () => {
                                this.moveChess(this.activChess, {
                                    area: 0,
                                    gridPosition: -6
                                }, () => {
                                    this.accountActionRecordAdd(11, "open");
                                    this.mySelf.popDice(num == 6 ? 1 : 6);
                                    this.activChess.scale(0.54, 0.54);
                                    this.activChess.offAll();
                                    this.timer.once(1600, this, this.nextStep);
                                    // this.nextStep();
                                    if (this.endPointAni) {
                                        this.endPointAni.visible = true;
                                        this.endPointAni.pos(this.activChess.x, this.activChess.y);
                                        this.endPointAni.play();
                                        yalla.Sound.playSound("firework");
                                    }
                                })
                            })
                        })
                    })
                });
                break;
            case 10:
                this.showTip("Congratulations! You win this game!");
                if (this.activChess.goalStation.gridPosition != -6) {
                    var port = LudoBoardData.getPosByStation({ area: 0, gridPosition: -6 });
                    this.activChess.pos(port.x, port.y);
                    this.mySelf.clearDice(false);
                    this.activChess.scale(0.54, 0.54);
                    this.activChess.offAll();
                    this.activChess.choose = false;
                }
                this.noviceTip.next_btn.visible = false;
                this.showVoiceAni();
                break;
        }
    }
    private step4() {
        this.accountActionRecordAdd(5, "open");
        this.activChess.offAll();
        this.mySelf.popDice(6);
        this.activChess.choose = true;
        this.activChess.scale(1, 1);
        this.showFinger(this.activChess);
        this.activChess.once("click", this, () => {
            this.accountActionRecordAdd(5, "click");
            this.moveChess(this.activChess, { area: 0, gridPosition: 7 }, () => { this.nextStep() });
        })
    }
    private moveChess(chess: LudoChess, station: Station, cb: Function, moveType: ChessMoveType = ChessMoveType.MOVE_NORMAL) {
        this.hideFinger();
        chess.choose = false;
        chess.once(Laya.Event.END, this, () => {
            chess.scale(0.8, 0.8);
            cb();
        })
        chess.scale(1, 1);
        // chess.goalStation = station;
        chess.move(station, moveType)
    }
    private removeChessEv() {
        for (var key in this.chessPool) {
            if (this.chessPool.hasOwnProperty(key)) {
                var ch = this.chessPool[key];
                ch.offAll();
                ch.choose = false;
            }
        }
    }
    private throwNum(num: number, cb: Function) {
        this.hideFinger();
        this.mySelf.dice.play();
        Laya.timer.once(this.diceTime, this, () => {
            cb();
        })
    }
    private chessInit(list: Array<ChessData>) {
        for (var key in this.chessPool) {
            if (this.chessPool.hasOwnProperty(key)) {
                var ch = this.chessPool[key];
                (ch as LudoChess).destroy();
            }
        }
        this.chessPool = {};
        for (let j = 0; j < list.length; j++) {
            let chessInfo = list[j];
            let chess = new LudoChess(null, chessInfo.color);
            var [area, gridPosition] = [chessInfo.color, 101 + j % 4];
            chess.homeGrid = LudoBoardData.getGridByStation({ area, gridPosition })
            this.mapView.chess_box.addChild(chess);
            chess.data = chessInfo;
            this.chessPool["chess_" + chess.id] = chess;
        }
    }
}