class OffGame extends Game {
    private service: offLine.Service;
    private hasQuit: boolean = false;
    private _chessSkin: number = 1;
    private _diceId: number = 1;
    constructor() {
        super();
        this.addMapView();
        this.reset();
        // this.gameUI.audience_btn.visible = false;
        this.banner.view.store_btn.visible = false;
        this.gameUI.myHander.visible = false;
        this.banner.view.rank_btn.visible = false;
        this.group = 0;
        this.isOffGame = true;
        this.banner.addEvent();
    }
    serviceInit(msg) {
        this.sceneId = msg.sceneId || 1;
        this._chessSkin = msg.chessId || 1;
        this._diceId = msg.diceId || 1;
        this.service = new offLine.Service(msg);
        this.addEvents();
        yalla.Global.Account.idx = offLine.Player.instance.player[0].fPlayerInfo.idx;
        ludo.Global.myInfo = {
            playerShowInfo: offLine.Player.instance.player[0],
            gold: 0,
            money: 0,
            isRecharged: false,
            exp: 0
        }



        Laya.timer.callLater(this, () => {
            this.gameDataInit(offLine.Player.instance.player);


            if (!yalla.util.IsWinConch()) {
                TestSkin.instance.mapView = this.mapView;
                TestSkin.instance.initUI(this.gameUI);
                // var noviceGame: NoviceGame = new NoviceGame();
                // noviceGame.init(10019, "https://img2.baidu.com/it/u=**********,**********&fm=26&fmt=auto&gp=0.jpg", "yalla","1","1");
                // Laya.stage.addChild(noviceGame);

                // var jungleNovice: JungleNovice = new JungleNovice();
                // jungleNovice.init(10019, "https://img2.baidu.com/it/u=**********,**********&fm=26&fmt=auto&gp=0.jpg", "yalla", "1", "1");
                // Laya.stage.addChild(jungleNovice);

                // var jackaroNovice: JackaroNoviceGame = new JackaroNoviceGame();
                // jackaroNovice.init(GameType.JACKARO, "https://img2.baidu.com/it/u=**********,**********&fm=26&fmt=auto&gp=0.jpg", "yalla", "1", "1");
                // Laya.stage.addChild(jackaroNovice);

            }
        })
    }
    public onForce() {
        super.startAni();
        for (var key in this.playerPool) {
            if (this.playerPool.hasOwnProperty(key)) {
                var player: GamePlayer = this.playerPool[key];
                player && player.UI.throw_jump.visible && player.UI.throw_jump.play(0, true);
            }
        }
    }

    public onBlur() {
        super.stopAni();
        for (var key in this.playerPool) {
            if (this.playerPool.hasOwnProperty(key)) {
                var player: GamePlayer = this.playerPool[key];
                player && player.UI.throw_jump.visible && player.UI.throw_jump.gotoAndStop();
            }
        }
    }
    protected addEvents() {
        this.banner.on("exit", this, this.exitHander);
        this.on(yalla.Native.instance.Event.ONBACKPRESSED, this, this.exitHander);
        this.gameUI.on("click", this, this.pageHander);
        this.service.on(SERVICE_EVENT.PLAYER, this, this.onPlayerStatus);
        this.service.on(SERVICE_EVENT.CHESS, this, this.chessStatus);
        this.service.on(SERVICE_EVENT.MOVE, this, this.offMoveChess);
        // this.service.on(SERVICE_EVENT.FLUSH_PROP, this, this.refreshProps);
    }
    private offMoveChess(msg) {
        var player = ludoRoom.instance.getPlayerByIdx(msg.idx);
        if (player && player.isLeft <= 0)
            this.moveChess(msg);
    }
    public onQuitGame() {
        this.service && this.service.clear();
        yalla.Native.instance.backHall();
    }
    public pageHander() {
        if (this.chooseStep.visible) {
            this.chooseStep.hide();
        }
        // if (this.gameUI.system.visible) {
        //     this.gameUI.system.visible = false;
        // }
        this.banner.pageHander();
    }
    showStar() { return; }
    exitHander() {
        yalla.common.Confirm.instance.isExit = true;
        new SaveGame(this.service.gamedata).popup(true, true);
    }
    public gameDataInit = (msg: Array<any>) => {
        if (msg && msg[0]) {
            this.myColor = msg[0].color;
            this.clearPlayer();
            for (let i = 0; i < msg.length; i++) {
                let playerInfo: playerShowInfo = msg[i];
                playerInfo.diceSkinId = this._diceId;
                let player = new GamePlayer(0, msg[i].color, true, playerInfo);
                this.playerPool["player_" + playerInfo.fPlayerInfo.idx] = player;
                this.updatePlayer(playerInfo, player);
                this.gameUI.players_layer.addChild(player);
                let chesses = this.service.filterChesses(msg[i].color);
                for (let j = 0; j < chesses.length; j++) {
                    let chessInfo = chesses[j];
                    let chess = new LudoChess(this._chessSkin, chessInfo.color);
                    var [area, gridPosition] = [chessInfo.color, 101 + j]
                    chess.homeGrid = LudoBoardData.getGridByStation({ area, gridPosition })
                    this.mapView.chess_box.addChild(chess);
                    // this.chessPool["chess_" + chessInfo.chessId] = chess;
                    this.updateChess(chessInfo, chess);
                    ChessManger.instance.addNewChess(chess);
                    if (chessInfo.gridPosition < 100 && this.mapView.chess_box.contains(chess)) {
                        this.pileUp(chess, true);
                    }
                    // if (this.mapView._curtainMask && chess.color == this.myColor)
                    //     this.mapView.pushMyChess(chess);
                }
            }
            this.service.onGameClient({ status: 0 });
            if (ludoRoom.instance.group == 1) {
                this.banner.view.magic.visible = true;
                this.banner.view.system.width += 140;
                // this.banner.view.magic.on("click", this, this.showMagicRule);
            }
        }
    }
    removePLayer(idx: number) {
        this.chooseStep.hide();
        this.service.onGameClient({
            status: 2,
            idx: idx
        })
        this.hasQuit = true;
        let quitPlayer: GamePlayer = this.filtratePlayer(idx);
        quitPlayer.isLeft = true;
        quitPlayer.UI.dice_bg.visible = false;
        if (quitPlayer.isWin == false) {
            this.chessRST(quitPlayer.color);
        }
        yalla.Sound.playSound("huythoat");
    }
    updatePlayer(playerInfo: playerShowInfo, player: GamePlayer) {
        if (player) {
            player.setPlayerInfo(playerInfo, this.activated_id);
            // player.data = playerInfo;
            player.head.offAll();
            player.head.on(Laya.Event.CLICK, this, () => {
                yalla.Sound.playSound("click");
                if (player.isLeft) return;
                yalla.common.Confirm.instance.showConfirm("Are you sure to remove this player?",
                    Laya.Handler.create(this, () => {
                        yalla.common.Confirm.instance.close();
                        player.data && player.data.fPlayerInfo && this.removePLayer(player.data.fPlayerInfo.idx);
                    }),
                    Laya.Handler.create(this, () => {
                        yalla.common.Confirm.instance.close();
                    }), ["Confirm", "Cancel"])
            })
            player.showDice();
            player.UI.voice_btn.visible = false;
            let color: number = player.color;
            if (ludoRoom.instance.type != 0) {//大师模式中 添加地图禁止通过标记
                let sign = (this.mapView.sign_box.getChildByName("sign" + color) as RoadBlock);
                if (playerInfo.isFighted == 0) {
                    if (!sign) {
                        var port = LudoBoardData.getGridByName(`${color}_-1`).port;
                        sign = new RoadBlock("sign" + color)
                        sign.pos(port.x, port.y);
                        this.mapView.sign_box.addChild(sign);
                    }
                } else {
                    sign && sign.hide();
                }
            }
        }
    }
    chessStatus(msg: any) {
        this.onChessEvent(msg);
    }
    onPlayerStatus(msg: any) {
        this.playerStatus = msg.status;
        if (!this.activated_id) {//第一次玩家
            this.activated_id = msg.idx;
        }
        if (this.activated_id != msg.idx && this.playerStatus == 0) {//切换玩家
            yalla.Sound.playSound("opp_turn");
            ComboManager.instance.reset();
            let lastPlayer: GamePlayer = this.filtratePlayer(this.activated_id);
            if (lastPlayer) {
                lastPlayer.stopAndShowDice(7);
                lastPlayer.clearDice(false);
            }
        }
        // let activatedPlayer: GamePlayer;
        this.activated_id = msg.idx;
        switch (this.playerStatus) {
            case PlayerTurnStatus.THROW_START:
                this.throwStart();
                break;
            case PlayerTurnStatus.THROW_END:
                this.throwDice(msg);
                break;
            case PlayerTurnStatus.RETHROW_START:
                break;
            case PlayerTurnStatus.RETHROW_END:
                break;
            case PlayerTurnStatus.CHOOSECHESS_START:
                this.chooseChessStart(msg);
                break;
            case PlayerTurnStatus.CHOOSECHESS_END://结束投掷
                this.removeChessEvent();
                break;
            case PlayerTurnStatus.CHESSMOVE_START:
                break;
            case PlayerTurnStatus.CHESSMOVE_END:
                break;
            case PlayerTurnStatus.PLAYER_OUT:
                // this.onQuitMsg(msg.player);
                break;
            case PlayerTurnStatus.PLAYER_WIN:
                this.showRank(msg);
                break;
            case PlayerTurnStatus.GAME_OVER:
                this.gameOver(msg);
                let data = this.service.gamedata;
                data['isFinished'] = true;
                yalla.Native.instance.updateLocalGameData(JSON.stringify(data));
                break;
        }
    }
    public gameOver(msg: any): Result {
        yalla.Sound.playSound("cheer");
        Dialog.manager.closeAll();
        var result: Result = new Result(msg, true, true);
        result.result_tx.text = "Good game";
        this.addChild(result);
        result.zOrder = 995;//500;
        return result;
    }
    pileChooseChess(arr: Array<LudoChess>) {
        arr.sort((a, b) => {
            return 0
        })
    }
    public chooseChessStart(msg: any) {
        let item = msg.item;
        for (let i = 0; i < item.length; i++) {
            let chess: LudoChess = this.filtrateChess(item[i].chessId);
            if (chess) {
                let pivotX: number = chess.pivotX;
                chess.scale(1, 1).pivotX = pivotX;
                let moves = [];
                for (let j = 0; j < item.length; j++) {
                    if (item[i].chessId == item[j].chessId) {
                        let move = {
                            station: {
                                area: item[j].area,
                                gridPosition: item[j].gridPosition
                            },
                            num: item[j].num
                        }
                        moves.push(move);
                    }
                }
                if (chess.displayedInStage) {
                    chess.choose = true;
                    this.moveChesses.push([chess, moves]);
                    chess.clickArea.on("click", this, this.clickChess, [chess, moves]);
                }
            }
        }
        this.moveChesses.sort((a, b) => {
            var [aIndex, bIndex] = [this.mapView.chess_box.getChildIndex(a[0]), this.mapView.chess_box.getChildIndex(b[0])];
            if (aIndex > bIndex) {
                return 1;
            }
            return -1;
        })
        var len = this.mapView.chess_box.numChildren - 1;
        // var len = ChessManger.instance.chessLen - 1;
        this.moveChesses.forEach((ch, i) => {
            var ch0 = ch[0];
            if (this.mapView.chess_box.contains(ch0))
                this.mapView.chess_box.setChildIndex(ch[0], len);
        })
    }
    onTrust() { }
    stopAndShowDice(player: GamePlayer, num: number) {
        player.pushDice(num);
        player.stopAndShowDice(num);
    }
    /*
    * 开始掷骰子
    */
    throwStart() {
        if (!this.activated_id)
            return;
        let player: GamePlayer = this.filtratePlayer(this.activated_id);
        if (player) {
            player.UI.dice_bg.mouseEnabled = true;
            player.UI.dice_bg.once("click", this, this.clickDice);
            player.UI.throw_jump.visible = true;
            (player.UI.throw_jump as Laya.Animation).play(0, true);
        }
    }
    /**
     * 结束掷骰子
     */
    throwEnd() {
        if (!this.activated_id)
            return;
        let player = this.filtratePlayer(this.activated_id);
        if (player) {
            player.UI.dice_bg.mouseEnabled = false;
            player.UI.dice_bg.off("click", this, this.clickDice);
            player.UI.throw_jump.visible = false;
            (player.UI.throw_jump as Laya.Animation).stop();
        }
    }
    throwDice(msg: any) {
        if (!this.activated_id)
            return;
        let player: GamePlayer = this.filtratePlayer(this.activated_id);
        this.removeChessEvent();
        let num: number = msg.throwNum;
        if (this.activated_id == yalla.Global.Account.idx) {
            if (player.dice) {
                player.UI.dice_bg.mouseEnabled = false;
                player.UI.dice_bg.off("click", this, this.clickDice);
            }
        }
        if (player.dice) {
            player.dice.play();
        }
        if (player)
            this.gameUI.timer.once(this.diceTime, this, this.stopAndShowDice, [player, num]);
    }
    sendChooseMsg(msg: any) {
        msg.idx = offLine.Player.instance.activatedId;
        this.service.onGameClient(msg);
    }
    clickDice() {
        let player: GamePlayer = this.filtratePlayer(this.activated_id);
        if (player && player.dice) {
            player.dice.play();
            player.UI.throw_jump.visible = false;
            (player.UI.throw_jump as Laya.Animation).stop();
        }
        this.service.onGameClient({ status: 1 })
    }
    backHallClear() {
        super.backHallClear();
        this.reset();
    }
    reset() {
        this.service && this.service.clear();
        this.service = null;
        offLine.Chess._instance = null;
        offLine.MapData._instance = null;
        offLine.Player._instance = null;
        this.off(yalla.Native.instance.Event.ONBACKPRESSED, this, this.exitHander);
    }
    clear() {
        super.clear();
    }
}