class TestArb extends ui.ludo.page.testArbUI {
    private bubble = new yalla.common.Bubble(0);
    private data = [];
    constructor() {
        super();
        if (Laya.LocalStorage.getItem("rectifyWord")) {
            yalla.Str.rectifyWord = Laya.LocalStorage.getJSON("rectifyWord");
        } else {
            Laya.LocalStorage.setJSON("rectifyWord", yalla.Str.rectifyWord);
        }

        this.word_list.renderHandler = new Laya.Handler(this, (cell: Laya.Label, index) => {
            var [key, value] = [this.data[index][0], this.data[index][1]]
            cell.text = key + ":" + value;
            cell.offAll();
            cell.on("click", this, (key, value) => {
                this.word_key.text = key;
                this.word_value.text = value;
            }, [key, value])
        })

        this.updateList();
        this.add.on("click", this, () => {
            yalla.Str._rectifyReg = null;
            var [key, value] = [this.word_key.text, parseInt(this.word_value.text)];
            yalla.Str.rectifyWord[key] = value;
            this.cp.text = JSON.stringify(yalla.Str.rectifyWord);
            this.updateList();
        })

        this.test.on("click", this, () => {
            var tx = this.testinput.text;
            this.testText(tx);
            var arr = [];
            for(var i=0;i<tx.length;i++){
                arr.push(tx[i].charCodeAt(0))
                // console.log(tx[i].charCodeAt(1))
            }
            this.log.text = JSON.stringify(arr);
        })

    }
    updateList() {
        this.data = [];
        for (var key in yalla.Str.rectifyWord) {
            this.data.push([key, yalla.Str.rectifyWord[key]]);
        }
        this.word_list.array = this.data;
        Laya.LocalStorage.setJSON("rectifyWord", yalla.Str.rectifyWord);
        yalla.Str._rectifyReg = null;
    }

    testText(tx: string) {
        this.lb_box.removeChildren();
        var lb = new Laya.Label();
        lb.fontSize = 30;
        lb.text = tx;
        lb.borderColor = "#000000";
        this.lb_box.addChild(lb);
    }
}