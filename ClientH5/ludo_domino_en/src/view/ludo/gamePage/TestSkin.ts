class TestSkin {
    public static _instance: TestSkin;
    private _bubble: yalla.common.Bubble;
    public gameUI: any;
    public mapView: any;
    private _testImg: Laya.Image;
    private _bubbleBottomPos: Laya.Text;
    private _bubbleBottomPos_1: Laya.Text;
    private _bubbleTopPos: Laya.Text;
    private _bubbleTopPos_1: Laya.Text;

    constructor() {
    }

    static get instance(): TestSkin {
        if (!TestSkin._instance) TestSkin._instance = new TestSkin();
        return TestSkin._instance;
    }

    public initUI(gameUI: any): void {
        if (!yalla.util.IsWinConch()) {
            this.gameUI = gameUI;

            this._testImg = new Laya.Image('game/pic_input_bg.png');
            this._testImg.height = 100;
            this._testImg.sizeGrid = "4,4,4,4";
            this._testImg.left = 150;
            this._testImg.right = 0;

            var bubbletitleT: Laya.Text = new Laya.Text();
            bubbletitleT.text = "气泡Top Init位置：";
            bubbletitleT.pos(0, 15);
            bubbletitleT.fontSize = 22;
            this._bubbleTopPos = new Laya.Text();
            this._bubbleTopPos.fontSize = 24;
            this._bubbleTopPos.pos(210, 15);
            this._bubbleTopPos_1 = new Laya.Text();
            this._bubbleTopPos_1.fontSize = 24;
            this._bubbleTopPos_1.pos(350, 55);

            var bubbletitleB: Laya.Text = new Laya.Text();
            bubbletitleB.text = "气泡Bottom Init位置：";
            bubbletitleB.pos(0, 55);
            bubbletitleB.fontSize = 22;
            this._bubbleBottomPos = new Laya.Text();
            this._bubbleBottomPos.fontSize = 24;
            this._bubbleBottomPos.pos(210, 55);
            this._bubbleBottomPos_1 = new Laya.Text();
            this._bubbleBottomPos_1.fontSize = 24;
            this._bubbleBottomPos_1.pos(310, 55);

            // this.gameUI.addChild(this._testImg);
            this._testImg.addChild(bubbletitleT);
            this._testImg.addChild(this._bubbleTopPos);
            this._testImg.addChild(this._bubbleTopPos_1)

            this._testImg.addChild(bubbletitleB);
            this._testImg.addChild(this._bubbleBottomPos);
            this._testImg.addChild(this._bubbleBottomPos_1)

            // this.testBubble(12049);
            // this.testBgSkin(13059, true);
            // this.testChessSkin(11040, "pile");//红绿黄蓝 *_0 *_1 *_2 *_3
            // this.testChessPosition();
        }
    }

    private testBubble(id: number): void {
        if (yalla.util.IsWinConch()) return;
        this._bubble = new yalla.common.Bubble(0);
        this.gameUI.addChild(this._bubble);
        this._bubble.pos(100, Laya.stage.height / 2);
        // this._bubble.chat = "Hello";
        this._bubble.chat = "Hello New Bubble Hello New Bubble Hello New Bubble Hello New Bubble Hello New Bubble Hello New Bubble Hello New Bubble Hello New Bubble Hello New Bubble Hello New Bubble Hello New Bubble Hello New Bubble Hello New Bubble Hello New Bubble Hello New Bubble Hello New Bubble";
        Laya.loader.load([`res/test/bubble_${id}.png`, `res/test/bubble_${id}.atlas`], Laya.Handler.create(this, () => {
            (this._bubble.ui.box as Laya.Image).skin = `bubble_${id}/bubbles_${id}.png`;
            (this._bubble.ui.arrow as Laya.Image).skin = `bubble_${id}/bubbles_angle_${id}.png`;
            (this._bubble.ui.bottom_icon as Laya.Image).skin = `bubble_${id}/material_bubbles_${id}.png`;
            (this._bubble.ui.top_icon as Laya.Image).skin = `bubble_${id}/material_top_bubbles_${id}.png`;

            this._bubble.visible = this._bubble.ui.visible = true;
            this._bubble.ui.scale(1, 1);

            this._bubbleTopPos.text = this._bubble.ui.top_icon.x + ":" + this._bubble.ui.top_icon.y;
            this._bubbleBottomPos.text = this._bubble.ui.bottom_icon.x + ":" + this._bubble.ui.bottom_icon.y;
        }));

        this._bubble.ui.bottom_icon.on(Laya.Event.MOUSE_DOWN, this, this.onMouseDownBottom);
        this._bubble.ui.bottom_icon.on(Laya.Event.MOUSE_UP, this, this.onMouseUpBottom);
        this._bubble.ui.top_icon.on(Laya.Event.MOUSE_DOWN, this, this.onMouseDownTop);
        this._bubble.ui.top_icon.on(Laya.Event.MOUSE_UP, this, this.onMouseUpTop);

    }

    private testBgSkin(skinId: number, furniture: boolean = false): void {
        if (yalla.util.IsWinConch()) return;
        var arr = [`res/test/Checkerboard_${skinId}.png`, `res/test/Base_${skinId}.png`, `res/test/BG_${skinId}.png`];
        if (furniture) {
            arr.push(`res/test/furniture_${skinId}.png`);
        }
        Laya.loader.load(arr, Laya.Handler.create(this, () => {
            this.mapView.check.skin = `res/test/Checkerboard_${skinId}.png`;
            this.mapView.bg.skin = `res/test/Base_${skinId}.png`;
            this.gameUI.bg.skin = `res/test/BG_${skinId}.png`;
            if (furniture) {
                var scale = Laya.stage.height / 1334;
                this.gameUI.furniture.scale(scale, scale);
                this.gameUI.furniture.skin = `res/test/furniture_${skinId}.png`;
            }
        }));
    }

    private testChessSkin(skinId: number, name: string = "chess"): void {
        if (yalla.util.IsWinConch()) return;
        Laya.loader.load([`res/test/chess_${skinId}.png`, `res/test/chess_${skinId}.atlas`], Laya.Handler.create(this, () => {
            Laya.timer.once(300, this, () => {
                ChessManger.instance.each(chess => {
                    chess.chessImg.skin = `chess_${skinId}/${name}_${skinId}_${chess.color}.png`;
                })

            })
        }), null, null, 1, true, "chess");
    }

    private testChessPosition(): void {
        if (yalla.util.IsWinConch()) return;
        var btn = new Laya.Button("game/btn_y.png", "ChangeSkin");
        btn.stateNum = 1;
        btn.labelSize = 22;
        btn.sizeGrid = "29,26,30,23";
        btn.size(150, 80).pos(20, 15);
        this._testImg.addChild(btn);
        btn.on("click", this, () => {
            var color = this.mapView._color;
            color++;
            color = color % 4;
            this.mapView.color = color;
        })
    }

    private onMouseDownBottom(e: Laya.Event): void {
        this._bubble.ui.bottom_icon.startDrag();
        // this._bubble.ui.bottom_icon.on(Laya.Event.MOUSE_MOVE, this, this.onMouseMoveBottom);
    }
    private onMouseUpBottom(e: Laya.Event): void {
        this._bubbleBottomPos_1.text = this._bubble.ui.bottom_icon.x + ":" + this._bubble.ui.bottom_icon.y;
        // this._bubble.ui.bottom_icon.off(Laya.Event.MOUSE_OUT, this, this.onMouseMoveBottom);
    }

    private onMouseDownTop(e: Laya.Event): void {
        this._bubble.ui.top_icon.startDrag();
    }
    private onMouseUpTop(e: Laya.Event): void {
        this._bubbleTopPos_1.text = this._bubble.ui.top_icon.x + ":" + this._bubble.ui.top_icon.y;
    }
}