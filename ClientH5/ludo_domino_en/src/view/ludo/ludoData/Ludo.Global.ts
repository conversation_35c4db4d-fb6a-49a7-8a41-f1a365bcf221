module ludo {
    export class Global {
        static moveTime: number = 165; //棋子移动时间
        static _myInfo: palyerInfo = {
            playerShowInfo: null,
            gold: 0,
            money: 0,
            isRecharged: false,
            exp: null
        };
        static set myInfo(val: palyerInfo) {
            if (val) {
                this._myInfo = val;
                yalla.Global.Account.currentGoldNum = val.money;
                yalla.Global.Account.currentDiamondNum = val.gold;
            }
        }
        static get myInfo() {
            return this._myInfo;
        }
        static getludoPcolor(index: number) {
            return ["#b2340a", "#098f06", "#c49002", "#0d7ecd"][index % 4];
        }

        static sitNum(): number {
            return this.myInfo && this.myInfo.playerShowInfo ? this.myInfo.playerShowInfo.sitNum : -1;
        }
        static inSit(): boolean {
            if (this.myInfo && this.myInfo.playerShowInfo) {
                return this.myInfo.playerShowInfo.sitNum >= 0;
            }
            return false;
        }
        static get myMoney(): number {
            return yalla.Global.Account.currentGoldNum || 0;
        }
        static set myMoney(val: number) {
            yalla.Global.Account.currentGoldNum = val || 0;
        }
        static get myGold(): number {
            return yalla.Global.Account.currentDiamondNum || 0;
        }
        static set myGold(val: number) {
            yalla.Global.Account.currentDiamondNum = val || 0;
        }

    }

}