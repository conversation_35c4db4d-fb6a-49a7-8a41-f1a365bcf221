module ludo {
    export class Proto extends Laya.EventDispatcher {
        private loadCount: number = 0;
        private ProtoBuf: any = Laya.Browser.window.protobuf;
        private protoNum = 6;           //目前有5个proto文件
        public root: any;
        public root_domino: any;
        public root_domino_new: any;    //domino更换新协议
        public root_watch: any;         //观战（domino）
        public root_snake: any;
        public root_jackaro: any;
        static _instance: ludo.Proto;

        constructor() {
            super();
            this.ProtoBuf.load(yalla.getVersionProto("ChessGame"), this.onAssetsLoaded.bind(this));
            // if (yalla.Global.dominoNew) {
            this.ProtoBuf.load(yalla.getVersionProto('DominoNew'), this.onAssetsLoaded_dominoNew.bind(this));
            // } else {
            this.ProtoBuf.load(yalla.getVersionProto('VsGameProtocol'), this.onAssetsLoaded_domino.bind(this));
            // }
            this.ProtoBuf.load(yalla.getVersionProto('SnakeLadder'), this.onAssetsLoaded_snake.bind(this));
            this.ProtoBuf.load(yalla.getVersionProto('DominoWatch'), this.onAssetsLoaded_Watch.bind(this));
            this.ProtoBuf.load(yalla.getVersionProto('jackarooGame'), this.onAssetsLoaded_jackaro.bind(this));

        }
        static get instance(): ludo.Proto {
            if (!this._instance)
                this._instance = new ludo.Proto();
            return this._instance;
        }

        get protoRoot() {
            // console.log('=======获取proto======gameType：' + yalla.Global.gameType);
            if (yalla.Global.gameType == GameType.LUDO) return this.root;
            if (yalla.Global.gameType == GameType.DOMINO) return this.root_domino;
            if (yalla.Global.gameType == GameType.SNAKEANDLADER) return this.root_snake;
            if (yalla.Global.gameType == GameType.JACKARO) return this.root_jackaro;
        }
        get protoRootNew() {
            // console.log('=======获取new——proto==gameType：' + yalla.Global.gameType);
            if (yalla.Global.gameType == GameType.LUDO) return this.root;
            if (yalla.Global.gameType == GameType.DOMINO) return this.root_domino_new;
            if (yalla.Global.gameType == GameType.SNAKEANDLADER) return this.root_snake;
            if (yalla.Global.gameType == GameType.JACKARO) return this.root_jackaro;
        }

        public encodeMsg(name: string, obj: Object): ArrayBuffer {
            if (yalla.Global.IsGameOver || !yalla.Global.isconnect) {
                return null;
            }
            // try {
            let loginMessage = this.root.lookupType(name);
            let message = loginMessage.create(obj);
            let byteArray = loginMessage.encode(message).finish();
            return byteArray;
            // } catch (error) {
            //     ludo.GameSocket.instance.close();
            // }
        }
        public decodeMsg(bytearr: any, name: string): any {
            try {
                let loginMessage = this.root.lookupType(name);
                let msg: any = loginMessage.decode(bytearr);
                return msg;
            } catch (error) {
                // ludo.GameSocket.instance.close();
                ludo.GameSocket.instance.closeNet();//TODO::1.3.1
            }
        }
        /**
         * ludo 协议配置文件加载
         * @param err 
         * @param root 
         */
        private onAssetsLoaded(err: any, root: any): void {
            if (err) {
                yalla.Native.instance.initEngineFailed();
                // throw err;
                return;
            }
            this.loadCount++;
            this.root = root;
            if (this.loadCount >= this.protoNum) {
                this.event(Laya.Event.COMPLETE);
            }
            return;
        }

        /**
         * domino 协议文件加载
         * @param err 
         * @param root 
         */
        private onAssetsLoaded_domino(err: any, root: any): void {
            if (err) {
                yalla.Native.instance.initEngineFailed();
                return;
            }
            this.loadCount++;
            this.root_domino = root;
            if (this.loadCount >= this.protoNum) {
                this.event(Laya.Event.COMPLETE);
            }
        }
        /**
         * domino 新协议文件加载
         * @param err 
         * @param root 
         */
        private onAssetsLoaded_dominoNew(err: any, root: any): void {
            if (err) {
                yalla.Native.instance.initEngineFailed();
                return;
            }
            this.loadCount++;
            this.root_domino_new = root;
            if (this.loadCount >= this.protoNum) {
                this.event(Laya.Event.COMPLETE);
            }
        }

        /**
         * snake 协议文件加载
         * @param err 
         * @param root 
         */
        private onAssetsLoaded_snake(err: any, root: any): void {
            if (err) {
                yalla.Native.instance.initEngineFailed();
                return;
            }
            this.loadCount++;
            this.root_snake = root;
            if (this.loadCount >= this.protoNum) {
                this.event(Laya.Event.COMPLETE);
            }
        }

        /**
         * domino 观战 协议文件加载
         * @param err 
         * @param root 
         */
        private onAssetsLoaded_Watch(err: any, root: any): void {
            if (err) {
                yalla.Native.instance.initEngineFailed();
                return;
            }
            this.loadCount++;
            this.root_watch = root;
            if (this.loadCount >= this.protoNum) {
                this.event(Laya.Event.COMPLETE);
            }
        }
        /**
         * jackaro 协议文件加载
         * @param err 
         * @param root 
         */
        private onAssetsLoaded_jackaro(err: any, root: any): void {
            if (err) {
                yalla.Native.instance.initEngineFailed();
                return;
            }
            this.loadCount++;
            this.root_jackaro = root;
            if (this.loadCount >= this.protoNum) {
                this.event(Laya.Event.COMPLETE);
            }
        }
    }
}
