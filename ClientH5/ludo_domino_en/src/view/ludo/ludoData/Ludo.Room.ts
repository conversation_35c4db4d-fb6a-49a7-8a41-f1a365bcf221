class ludoRoom extends Laya.EventDispatcher {
    constructor() { super(); }
    public static _instance: ludoRoom = null;
    static get instance(): ludoRoom {
        if (!this._instance) this._instance = new ludoRoom();
        return this._instance;
    }
    public sitPlayers: Array<playerShowInfo> = [];//入座玩家
    public sitSideHash: Object = {};    //{idx:side} 0 1 2 3
    private audiencePlayers: Array<playerShowInfo> = [];//观战玩家
    private leftPlayers: Array<number> = [];//入座切已经离开的玩家的idx集合
    public cost: number = null;//房间赔率
    public ID: number = null;
    public showID: number = null;
    public group: RoomGroup = null;//房间类型
    // public isPrivate: number = 0;//是否公开房间 0是 1否 
    public type: RoomType = 0;
    public watchIdx: number = 0;
    public firstLogin: boolean = true;//首次登陆
    private _teamInfo: TeamInfo = null;//队伍信息
    public quitPunishTime: number = 60;
    public gameBeginTime: number = 0;
    private _teamNames: Object = {};
    public duelGameEndTime: number = 0//战斗模式结束时间
    private _offsetTime: number = 0;//本地和服务器的时间差
    private _activityPropUrl: string = null;//道具掉落图片Url 不包含道具掉落的时候没有此内容
    private _activityPropInfo: ActivityPropInfo = null;
    public set currentTime(val: number) {
        this._offsetTime = val ? Date.now() - val : 0;
    }
    public roomModeName(type: number) {
        return ["CLASSIC", "MASTER", "QUICK", "RUSH", "ARROW", "FIGHT LUDO", "Jungle Ludo"][type];
    }
    public set activityPropInfo(info: ActivityPropInfo) {
        this._activityPropInfo = info;
    }
    public get activityPropInfo(): ActivityPropInfo {
        return this._activityPropInfo || {} as any;
    }
    public get gameMapCount(): number {
        return this._activityPropInfo ? this._activityPropInfo.gameMapCount : 0;
    }
    public set activityPropUrl(url: string) {
        this._activityPropUrl = url;
        this.isActivityProp && yalla.File.downloadImgByUrl(url, null);//预加载
    }
    public get activityPropUrl(): string {
        return this._activityPropUrl;
    }
    public get isActivityProp(): boolean {
        return Boolean(this._activityPropUrl);
    }
    public get currentTime() {
        return Date.now() - this._offsetTime;
    }
    public get fightLeftTime(): number {//决斗模式剩余时间 单位s
        return Math.floor((this.duelGameEndTime - this.currentTime) / 1000)
    }
    public get isVipGame(): boolean {
        return yalla.Global.Account.isPrivate == 4;
    }
    public set teamInfo(teamInfo: TeamInfo) {
        if (teamInfo) {
            this._teamInfo = teamInfo;
            this._teamNames = {};
            for (var key in teamInfo) {
                var idx = teamInfo[key];
                switch (key) {
                    case "teamAPlayer1":
                        this._teamNames[idx] = "A";
                        break;
                    case "teamAPlayer2":
                        this._teamNames[idx] = "A";
                        break;
                    case "teamBPlayer1":
                        this._teamNames[idx] = "B";
                        break;
                    case "teamBPlayer2":
                        this._teamNames[idx] = "B";
                        break;
                }
            }
        }
    }
    public get isDuel(): boolean {
        return yalla.Global.Account.isPrivate == 9;
    }
    public get isMaster(): boolean {
        return this.type == 1;
    }
    public get isPrivate(): boolean {
        return yalla.Global.Account.isPrivate == 1;
    }
    public get teamInfo(): TeamInfo {
        return this._teamInfo;
    }
    public beWatch(idx: number): boolean {
        return this.watchIdx == idx
    }
    public isSameTeam(isSpter: boolean, friIdx: number): boolean {//观战的判断 是不是同一个队伍 观战的使用watchidx
        if (this.teamNight()) {
            if (isSpter) {
                return this._teamNames[this.watchIdx] == this._teamNames[friIdx];
            } else {
                return this._teamNames[yalla.Global.Account.idx] == this._teamNames[friIdx];
            }
        } else {
            if (isSpter) {
                return this.watchIdx == friIdx;
            } else {
                return yalla.Global.Account.idx == friIdx;
            }
        }
    }
    // public isTeamMate(friIdx: number) {//是不是队友
    //     return yalla.Global.Account.idx == friIdx || (this.isTeamGame() && this._teamNames[yalla.Global.Account.idx] == this._teamNames[friIdx]);
    // }
    set players(val: Array<playerShowInfo>) {
        this.sitPlayers = [];
        this.audiencePlayers = [];
        val.forEach((pInfo, index) => {
            if (this.leftPlayers.indexOf(pInfo.fPlayerInfo.idx) >= 0) {
                pInfo.isLeft = 1;
            }
            if (pInfo.sitNum >= 0) {
                this.pushSitPlayers(pInfo);
            } else {
                this.pushAudiencePlayers(pInfo);
            }
        })
        this.sitPlayers.sort((a: playerShowInfo, b: playerShowInfo) => {
            if (a.sitNum > b.sitNum) {
                return 1;
            } else if (a.sitNum < b.sitNum) {
                return -1;
            } else {
                return 0;
            }
        })
    }
    get players(): Array<playerShowInfo> {
        return this.sitPlayers.concat(this.audiencePlayers);
    }

    public isTeamGame(): boolean {
        var isPrivate = yalla.Global.Account.isPrivate;
        return isPrivate == 5 || isPrivate == 7;
    }
    public teamNight() {
        return yalla.Global.Account.isPrivate == 7;
    }
    // public basicNight() {
    //     return yalla.Global.Account.isPrivate == 6;
    // }
    public nightGame(): boolean {
        var isPrivate = yalla.Global.Account.isPrivate;
        return isPrivate == 6 || isPrivate == 7;
    }

    public pushPlayer(pInfo: playerShowInfo) {
        let have: boolean = false;
        for (var i = 0; i < this.players.length; i++) {
            var info = this.players[i];
            if (info && info.fPlayerInfo.idx == pInfo.fPlayerInfo.idx) {
                have = true;
                break;
            }
        }
        if (!have) {
            if (pInfo.sitNum >= 0) {
                this.pushSitPlayers(pInfo);
            } else {
                this.pushAudiencePlayers(pInfo);
            }
        }
    }
    private pushAudiencePlayers(pInfo: playerShowInfo) {
        this.audiencePlayers.push(pInfo);
        this.mutePlayer(pInfo.fPlayerInfo.idx);
    }
    private pushSitPlayers(pInfo: playerShowInfo) {
        this.sitPlayers.push(pInfo);
    }
    /**
     * 屏蔽观战的声音
     */
    public muteAudiencePlayers() {
        this.audiencePlayers.forEach(pInfo => {
            this.mutePlayer(pInfo.fPlayerInfo.idx);
        })
    }
    private mutePlayer(idx: number) {
        var voice = yalla.Voice.instance;
        voice.joinSuccess && !voice.muted(idx) && voice.muteRemoteAudioStream(idx, true, null);
    }
    public getPlayerByIdx(idx: number): playerShowInfo {
        let result: playerShowInfo = null;
        this.players.forEach((pInfo: playerShowInfo, index: number, arr: Array<playerShowInfo>) => {
            if (pInfo.fPlayerInfo.idx == idx) {
                result = pInfo;
                return true;
            }
        })
        return result;
    }
    /**
     * 获取观战的玩家
     */
    public getAudiencePlayers(): Array<playerShowInfo> {
        return this.audiencePlayers;
    }
    /**
     * 返回入座的玩家
     */
    public getSitPlayers(): Array<playerShowInfo> {
        return this.sitPlayers;
    }
    /**
     * 根据颜色返回棋子的schessSkinId
     */
    public getChessSkinByPColor(color: number, sitPlayer: Array<playerShowInfo>): number {
        for (var i = 0; i < sitPlayer.length; i++) {
            var pInfo = sitPlayer[i];
            if (pInfo.color == color)
                return pInfo.chessSkinId;
        }
        return 11000;
    }
    public sortWinPlayers(): Array<playerShowInfo> {
        return this.getSitPlayers().sort((a: playerShowInfo, b: playerShowInfo) => {
            if (a.winIndex == -1) {
                return 1;
            }
            if (b.winIndex == -1) {
                return -1;
            }
            if (a.winIndex < b.winIndex) {
                return -1;
            } else {
                return 1;
            }
        })
    }

    public getAward(): Array<number> {
        var len = this.getSitPlayers().length;
        var royalty = yalla.Global.Account.royalty ? yalla.Global.Account.royalty : 0;
        var [rankNum1, rankNum2, allcost] = [75, 25, this.cost * len];
        var [winMoney1, winMoney2] = [0, 0];
        if (len === 4) {
            var rankPercentList = yalla.Global.Account.rankPercentList;
            if (rankPercentList && rankPercentList.length > 0) {
                for (var key in rankPercentList) {
                    if (rankPercentList.hasOwnProperty(key)) {
                        var element = rankPercentList[key];
                        if (element['rankNum'] === 1) {
                            rankNum1 = element['rankPercent'];
                        } else if (element['rankNum'] === 2) {
                            rankNum2 = element['rankPercent'];
                        }
                    }
                }
            }
            winMoney1 = allcost * ((rankNum1 - royalty) / 100);
            winMoney2 = allcost * (rankNum2 / 100);
        } else {
            [winMoney1, winMoney2] = [allcost * (1 - royalty / 100), 0];
        }
        return [winMoney1, winMoney2];
    }
    /**
     * 模拟结算信息 胜利玩家提前退出时显示
     */
    public resultMsg(): any {
        var [winMoney1, winMoney2] = this.getAward();
        var players = this.sortWinPlayers();
        if (players[1] && players[1].winIndex == -1) {
            winMoney2 = 0;
        }
        return {
            winMoney1: winMoney1,
            winMoney2: winMoney2,
            player: players
        }
    }
    /**
     * 玩家退出
     */
    public playerOut(idx: number): playerShowInfo {
        for (var i = 0; i < this.sitPlayers.length; i++) {
            var pInfo: playerShowInfo = this.sitPlayers[i];
            if (pInfo.fPlayerInfo.idx == idx) {
                pInfo.isLeft = 1;
                if (this.leftPlayers.indexOf(idx) < 0) {
                    this.leftPlayers.push(idx);
                }
                return pInfo;
            }
        }
        for (var i = 0; i < this.audiencePlayers.length; i++) {
            var pInfo: playerShowInfo = this.audiencePlayers[i];
            if (pInfo.fPlayerInfo.idx == idx) {
                this.audiencePlayers.splice(i, 1);
                return pInfo;
            }
        }
        return null;
    }

    /** 在座的 是否尊贵的royal level >= 4 */
    public get isHonorRoyal(): boolean {
        var players = this.sitPlayers;
        for (var key in players) {
            var element: playerShowInfo = players[key];
            if (element.realRoyLevel >= 4) return true;
        }
        return false;
    }
    /**
     * 组队获取所在队伍
     */
    public getTeamNameByIdx(idx: number): string {
        return this._teamNames[idx] || "";
    }
    clear() {
        this._activityPropUrl = null;
        this.ID = null;//房间id
        this.leftPlayers = [];
        this.sitPlayers = [];
        this.audiencePlayers = [];
        this.type = 0;
        this.group = 0;
        this.teamInfo = null;

        this.cost = null;
        this.watchIdx = 0;
        this.showID = null;
        this.group = null;//房间类型
        this.firstLogin = true;//首次登陆
        this._teamNames = {};
    }

}