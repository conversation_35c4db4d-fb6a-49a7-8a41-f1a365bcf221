module ludo {
    export class Undo {
        constructor() { }
        public maxReThrowNum: number = 0;//最大重置次数
        public maxTurnReThrowNum: number = 0;//每轮最大重置次数
        public reThrowCost: Array<number> = [1];//每次重置消耗数量
        public currentReThrowNum: number = 0;//当前重置次数
        public currentTurnReThrowNum: number = 0;//当前回合重置次数
        private _costDiamond: number = null;
        private static _instance: ludo.Undo = null;
        /**
         * 当前总剩余重置次数
         */
        get residueReThrowNum(): number {
            return this.maxReThrowNum - this.currentReThrowNum;
        }
        /**
         * 当前回合剩余重置次数
         */
        get residueTurnReThrowNum(): number {
            var residueReThrowNum = this.residueReThrowNum;//总剩余次数 0
            var residueTurnReThrowNum = this.maxTurnReThrowNum - this.currentTurnReThrowNum;//当前剩余次数 -6
            if (residueReThrowNum < residueTurnReThrowNum) {
                return residueReThrowNum;
            } else {
                return residueTurnReThrowNum;
                // return residueTurnReThrowNum>=0?residueTurnReThrowNum:0;
            }
        }
        /**
         * 发生重置
         */
        public happenUndo() {
            this.currentTurnReThrowNum++;
            this.currentReThrowNum++;
        }

        get costDiamond(): number {
            // if (this._costDiamond)
            //     return this._costDiamond;
            return this.reThrowCost[this.currentReThrowNum];
        }
        set costDiamond(val: number) {
            if (this._costDiamond != 0) this._costDiamond = val;
        }
        /**
         * 新回合
         */
        public newRound() {
            this.currentTurnReThrowNum = 0;
        }
        static get instance(): ludo.Undo {
            if (!this._instance)
                this._instance = new ludo.Undo()
            return this._instance;
        }
        public reset(): ludo.Undo {
            this.maxReThrowNum = 0;
            this.maxTurnReThrowNum = 0;
            this.reThrowCost = [1];
            this.currentReThrowNum = 0;
            this.currentTurnReThrowNum = 0;
            this._costDiamond = null;
            return this;
        }
    }
}