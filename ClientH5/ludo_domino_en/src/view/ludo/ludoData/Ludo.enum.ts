//游戏房间组类型
enum RoomGroup {
    SPORTS_MODE = 0,//竞技
    PROP_MODE = 1,//道具
}
//游戏房间类型
enum RoomType {
    NORMAL = 0,//正常
    MASTER = 1,//大师
    QUICK = 2,//快速
    FIRE = 3,//火拼
    ARROW = 4,//箭头
    DUEL = 5,//决斗
    JUNGLE = 6//丛林模式
}
enum ExitType {
    INITIATIVE_QUIT = 1,//主动退出
    GAMEOVER_QUIT = 2,  //游戏结束
    SOCKETFAILED_QUIT = 3,      //socket中断,失败
    LOGGED_ANOTHER_QUIT = 4,    //其他地方登陆
    KICKED_QUIT = 5,        //被踢
    SERVER_MAINTENANCE_QUIT = 6,  //服务器维护
    JOIN_OTHER_GAME_QUIT = 7,    //接受好友,加入其它游戏
    PLAY_AGAIN = 8 //再来一局
}
enum ErrorCode {
    NONE_ERROE = 1,//无错误
    LOGIN_FAILD_PW = 101,//密码错误
    LOGIN_FAILD_ID = 102,//登陆ID错误
    LOGIN_FAILD_OLDSIGN = 103,//重复的签名
    LOGIN_FAILD_SIGN = 104,//签名错误
    LOGIN_FAILD_TIME = 105,//客户端时间错误
    QUICKSTART_FAILD_NOROOM = 203,//房间已满
    ENTER_ROOM_FAILD_NOROOM = 204,//房间已满
    ENTER_ROOM_FAILD_ROOMID = 205,//错误的房间号
    GAMEOPERATE_FAILD_SIT_NOSTOOL = 301,// 已有人
    GAMEOPERATE_FAILD_SIT_WRONGSTOOLNUM = 302,//错误的凳子ID
    GAMEOPERATE_FAILD_SIT_WRONGTIME = 303,//错误的操作时间
    NO_MONEY = 401,//余额不足
    NO_DIAMOND = 402,//钻石不足
    MAX_LIMIT = 601,//超出限制
    JOIN_ROOM_FAILD_NOROOM = 701,//无此房间
    JOIN_ROOM_FAILD_MAX = 702,//房间人数已满
    ACCOUNT_BE_FROZEN = 403,//1.2.2 add 账号被冻结
    NO_TOAST = 703//不提示
}

enum VoiceType {
    Agora = 1,//声网
    Zego = 2,//即构
}

enum GameRoomType {
    PUBLIC = 0,
    PRIVATE = 1,
    CHAMPION = 3,
    VIP = 4,
    LEAGUE = 8
}

enum JungleEvent {
    JUNGLE_NONE = 1,//无事件
    JUNGLE_LIGHTNING = 2,//闪电 1.3.9 丛林玩法
    JUNGLE_TIGER = 3,//老虎 1.3.9 丛林玩法
    JUNGLE_EXTRA_DICE = 4,//额外骰子 1.3.9 丛林玩法
    JUNGLE_GRID_SHIELD = 5, //格子护盾(棋子移动后消失) 1.3.9 丛林玩法
    JUNGLE_SNAIL = 6,//蜗牛 1.3.9 丛林玩法
    JUNGLE_WIND_DIRECTION = 7,//风向  1.3.9 丛林玩法
}

enum ChessState {
    JUNGLE_NONE_STATE = 1,//正常状态
    JUNGLE_PARALYSIS = 2 // 麻痹
}
enum WindDirection {
    FORWARD_WIND = 1, //向前
    BACKWARD_WIND = 2, //向后
}