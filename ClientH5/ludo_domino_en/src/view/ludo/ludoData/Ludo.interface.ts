interface Maps {
    airport_1?: Array<any>,
    airport_2?: Array<any>,
    airport_3?: Array<any>,
    airport_0?: Array<any>,
    player_1?: Array<any>,
    player_2?: Array<any>,
    player_3?: Array<any>,
    player_0?: Array<any>
}
interface ChatClass extends Laya.Node {
    onChat(msg: any, server: boolean, insit: boolean),
    trunVoice(),
    hideAllChat(),
    clear(),
    voice_btn?: Laya.Button
}

interface port {
    x: number,
    y: number
}

interface Station {
    area: number,
    gridPosition: number,
    gridColor?: number,
    position?: number
}

interface ChessData {
    area: number,
    chessId: number,
    color: number,
    gridPosition: number,
    isWin: number,
    chessLinkId?: number,
    chessSkin?: number,
    isMaster?: number
}

interface TeamInfo {
    teamAPlayer1: number,
    teamAPlayer2: number
    teamBPlayer1: number,
    teamBPlayer2: number
}

interface PropGrid {
    area: number,
    gridPosition: number,
    prop: number
}

interface JungleGrid {
    area: number,
    jungleEvent: JungleStateEvent[],
    gridPosition: number
}
interface JungleStateEvent {
    jungleEvent: JungleEvent,
    eventStatus: number,
    windDirection?: WindDirection
}

interface ActivityPropInfo {
    curCount: number,//当前获得的数量
    winCount: number,//胜利可得道具的数量
    winSecondCount: number,//胜利可得道具的数量
    arriveCount: number,//到达终点可得道具数量
    gameMapCount: number,//棋盘可吃道具数量
}

interface ChessStateItem {
    chessId: number,//选的棋子
    chessState: ChessState[]// 棋子状态
}