module ludo {
    export class muteSpectator {
        static _isMute: number = 0;
        static init(idx:number) {
            var local = Laya.LocalStorage.getJSON("muteSpectator"+idx);
            if (!local) {
                this._isMute = 0;
                Laya.LocalStorage.setJSON("muteSpectator"+idx, { muteSpectator: 0 });
            } else {
                this._isMute = local.muteSpectator;
            }
        }
        static set isMute(val: number) {
            Laya.LocalStorage.setJSON("muteSpectator"+yalla.Global.Account.idx, { muteSpectator: val })
            this._isMute = val;
        }
        static get isMute(): number {
            return this._isMute;
        }

        static clear(){
            this._isMute = 0;
        }
    }
}