//ludo棋盘中的格子
/**
 *                                  12  0  1
 *                                  11 -1  2
 *                  1               10 -2  3             2
 *                                  9  -3  4
 *                                  8  -4  5
 *                                  7  -5  6
 *           1   2   3   4   5   6     -6      7   8   9   10   11  12
 *           0  -1  -2  -3  -4  -5  -6    -6  -5  -4  -3  -2   -1   0 
 *           12  11  10  9   8   7     -6      6   5   4   3    2   1
 *                                  6  -5  7
 *                                  5  -4  8
 *                                  4  -3  9
 *                   0              3  -2  10            3
 *                                  2  -1  11
 *                                  1   0  12
 */
class LudoBoardData {
    private static _color = 0;
    static get color() {
        return this._color;
    }
    static set color(val: number) {
        this._color = val;
        var color = val || 0;
        var isDuel: boolean = ludoRoom.instance.isDuel
        for (var i = 0; i < 4; i++) {
            color = color % 4;
            var ports = isDuel ? this._portsDuel : this._ports;
            var airPorts = ports["airport_" + i];
            var playerPort: Array<port> = ports["player_" + i];
            for (var j = 0; j < playerPort.length; j++) {
                if (j < airPorts.length) {
                    var grid: LudoGrid = this.getGridByName(this.getGridName({ area: color, gridPosition: 101 + j }));
                    grid.port = airPorts[j];
                }
                var gridPosition = j > 12 ? 12 - j : j;
                var grid: LudoGrid = this.getGridByName(this.getGridName({ area: color, gridPosition: gridPosition }));
                grid.port = playerPort[j];
            }
            color++;
        }
    }
    //计算两个点直接的步数
    public static distanceStep(currentGrid: LudoGrid, goalGrid: LudoGrid): number {
        if (currentGrid && goalGrid) {
            if (currentGrid.inHome || goalGrid.inHome) return 1;
            var currentArea = currentGrid.station.area,
                currentPosition = Math.abs(currentGrid.station.gridPosition),
                goalArea = goalGrid.station.area,
                goalPosition = Math.abs(goalGrid.station.gridPosition);
            // if (currentArea == 3 && goalArea == 0) goalArea = 4;
            // return Math.abs((goalArea - currentArea) * 13 + goalPosition - currentPosition);
            if (goalArea < currentArea) goalArea += 4;
            return (goalArea - currentArea) * 13 + goalPosition - currentPosition;
        }
        return 0;
    }
    //根据步数计算下一个目标点 Station
    public static distanceStation(station: Station, num: number, chessColor: number, fighted: boolean = true): Station {
        let result = null;
        if (station && num) {
            let area = station.area, gridPosition;
            if (num > 0) {
                gridPosition = station.gridPosition >= 0 ? station.gridPosition + num : station.gridPosition - num;
                if (gridPosition > 12) {//跨区域s
                    gridPosition = gridPosition % 13;
                    area = (area + 1) % 4;
                    if (area === chessColor) gridPosition = -gridPosition;
                }
                return { area, gridPosition };
            } else {
                gridPosition = station.gridPosition + num;
                if (gridPosition < 0) {//跨区域s
                    gridPosition = (gridPosition + 13) % 13;
                    area = (area + 3) % 4;
                    // if (area === chessColor) gridPosition = -gridPosition;
                }
                return { area, gridPosition };
            }
        }
        return result;
    }

    public static getGridName(station: Station): string {
        return station ? `${station.area}_${station.gridPosition}` : null;
    }
    public static getGridByName(name: string) {
        return !ludoRoom.instance.isDuel ? this._map[name] : this._mapDuel[name];
    }
    public static getGridByStation(station: Station): LudoGrid {
        return this.getGridByName(this.getGridName(station))
    }
    public static getPosByStation(station: Station): port {
        return this.getPosByName(this.getGridName(station));
    }
    public static getPosByName(name: string): port {
        var grid: LudoGrid = this.getGridByName(name);
        return grid ? grid.port : { x: null, y: null };
    }
    public static getEndGird(color: number): LudoGrid {
        var winLen: number = ChessManger.instance.getWinsLenByColor(color);
        return this.getGridByName(`${color}_-${winLen + 5}`);
    }
    //返回值 0,1,2,3分别表示 上 右 下 左
    public static getWindDirection(station: Station, back: boolean): number {
        let direction = 0
        if (station) {
            //假如自己的颜色是0，每个颜色的1-6的位置 颜色值 刚好对应上 右 下 左，非1-6是逆时针旋转90度即color-1 所以套用以下逻辑即可
            //1 和 12是例外情况 
            let gridDirection = 0,
                areaDirection = station.area - this._color + 4;
            if (station.gridPosition == 1) {
                gridDirection = back ? -1 : 0;
            } else if (station.gridPosition == 12) {
                gridDirection = back ? -1 : 0;
            } else {
                gridDirection = station.gridPosition > 6 || station.gridPosition < 1 ? -1 : 0
            }
            direction = (areaDirection + gridDirection + (back ? -2 : 0)) % 4;
        }
        return direction
    }
    private static _map = {
        "0_101": new LudoGrid({ area: 0, gridPosition: 101 }, "0_101", "0_2", null),
        "0_102": new LudoGrid({ area: 0, gridPosition: 102 }, "0_102", "0_2", null),
        "0_103": new LudoGrid({ area: 0, gridPosition: 103 }, "0_103", "0_2", null),
        "0_104": new LudoGrid({ area: 0, gridPosition: 104 }, "0_104", "0_2", null),
        "0_0": new LudoGrid({ area: 0, gridPosition: 0 }, "0_0", "0_1", "3_12"),
        "0_1": new LudoGrid({ area: 0, gridPosition: 1 }, "0_1", "0_2", "0_0"),
        "0_2": new LudoGrid({ area: 0, gridPosition: 2 }, "0_2", "0_3", "0_1"),
        "0_3": new LudoGrid({ area: 0, gridPosition: 3 }, "0_3", "0_4", "0_2"),
        "0_4": new LudoGrid({ area: 0, gridPosition: 4 }, "0_4", "0_5", "0_3"),
        "0_5": new LudoGrid({ area: 0, gridPosition: 5 }, "0_5", "0_6", "0_4"),
        "0_6": new LudoGrid({ area: 0, gridPosition: 6 }, "0_6", "0_7", "0_5"),
        "0_7": new LudoGrid({ area: 0, gridPosition: 7 }, "0_7", "0_8", "0_6"),
        "0_8": new LudoGrid({ area: 0, gridPosition: 8 }, "0_8", "0_9", "0_7"),
        "0_9": new LudoGrid({ area: 0, gridPosition: 9 }, "0_9", "0_10", "0_8"),
        "0_10": new LudoGrid({ area: 0, gridPosition: 10 }, "0_10", "0_11", "0_9"),
        "0_11": new LudoGrid({ area: 0, gridPosition: 11 }, "0_11", "0_12", "0_10"),
        "0_12": new LudoGrid({ area: 0, gridPosition: 12 }, "0_12", "1_0", "0_11"),
        "0_-1": new LudoGrid({ area: 0, gridPosition: -1 }, "0_-1", "0_-2", "0_0"),
        "0_-2": new LudoGrid({ area: 0, gridPosition: -2 }, "0_-2", "0_-3", "0_-1"),
        "0_-3": new LudoGrid({ area: 0, gridPosition: -3 }, "0_-3", "0_-4", "0_-2"),
        "0_-4": new LudoGrid({ area: 0, gridPosition: -4 }, "0_-4", "0_-5", "0_-3"),
        "0_-5": new LudoGrid({ area: 0, gridPosition: -5 }, "0_-5", "0_-6", "0_-4"),
        "0_-6": new LudoGrid({ area: 0, gridPosition: -6 }, "0_-6", null, "0_-5"),
        "0_-7": new LudoGrid({ area: 0, gridPosition: -7 }, "0_-7", null, "0_-5"),
        "0_-8": new LudoGrid({ area: 0, gridPosition: -8 }, "0_-8", null, "0_-5"),
        "0_-9": new LudoGrid({ area: 0, gridPosition: -9 }, "0_-9", null, "0_-5"),

        "1_101": new LudoGrid({ area: 1, gridPosition: 101 }, "1_101", "1_2", null),
        "1_102": new LudoGrid({ area: 1, gridPosition: 102 }, "1_102", "1_2", null),
        "1_103": new LudoGrid({ area: 1, gridPosition: 103 }, "1_103", "1_2", null),
        "1_104": new LudoGrid({ area: 1, gridPosition: 104 }, "1_104", "1_2", null),
        "1_0": new LudoGrid({ area: 1, gridPosition: 0 }, "1_0", "1_1", "0_12"),
        "1_1": new LudoGrid({ area: 1, gridPosition: 1 }, "1_1", "1_2", "1_0"),
        "1_2": new LudoGrid({ area: 1, gridPosition: 2 }, "1_2", "1_3", "1_1"),
        "1_3": new LudoGrid({ area: 1, gridPosition: 3 }, "1_3", "1_4", "1_2"),
        "1_4": new LudoGrid({ area: 1, gridPosition: 4 }, "1_4", "1_5", "1_3"),
        "1_5": new LudoGrid({ area: 1, gridPosition: 5 }, "1_5", "1_6", "1_4"),
        "1_6": new LudoGrid({ area: 1, gridPosition: 6 }, "1_6", "1_7", "1_5"),
        "1_7": new LudoGrid({ area: 1, gridPosition: 7 }, "1_7", "1_8", "1_6"),
        "1_8": new LudoGrid({ area: 1, gridPosition: 8 }, "1_8", "1_9", "1_7"),
        "1_9": new LudoGrid({ area: 1, gridPosition: 9 }, "1_9", "1_10", "1_8"),
        "1_10": new LudoGrid({ area: 1, gridPosition: 10 }, "1_10", "1_11", "1_9"),
        "1_11": new LudoGrid({ area: 1, gridPosition: 11 }, "1_11", "1_12", "1_10"),
        "1_12": new LudoGrid({ area: 1, gridPosition: 12 }, "1_12", "2_0", "1_11"),
        "1_-1": new LudoGrid({ area: 1, gridPosition: -1 }, "1_-1", "1_-2", "1_0"),
        "1_-2": new LudoGrid({ area: 1, gridPosition: -2 }, "1_-2", "1_-3", "1_-1"),
        "1_-3": new LudoGrid({ area: 1, gridPosition: -3 }, "1_-3", "1_-4", "1_-2"),
        "1_-4": new LudoGrid({ area: 1, gridPosition: -4 }, "1_-4", "1_-5", "1_-3"),
        "1_-5": new LudoGrid({ area: 1, gridPosition: -5 }, "1_-5", "1_-6", "1_-4"),
        "1_-6": new LudoGrid({ area: 1, gridPosition: -6 }, "1_-6", null, "1_-5"),
        "1_-7": new LudoGrid({ area: 1, gridPosition: -7 }, "1_-7", null, "1_-5"),
        "1_-8": new LudoGrid({ area: 1, gridPosition: -8 }, "1_-8", null, "1_-5"),
        "1_-9": new LudoGrid({ area: 1, gridPosition: -9 }, "1_-9", null, "1_-5"),

        "2_101": new LudoGrid({ area: 2, gridPosition: 101 }, "2_101", "2_2", null),
        "2_102": new LudoGrid({ area: 2, gridPosition: 102 }, "2_102", "2_2", null),
        "2_103": new LudoGrid({ area: 2, gridPosition: 103 }, "2_103", "2_2", null),
        "2_104": new LudoGrid({ area: 2, gridPosition: 104 }, "2_104", "2_2", null),
        "2_0": new LudoGrid({ area: 2, gridPosition: 0 }, "2_0", "2_1", "1_12"),
        "2_1": new LudoGrid({ area: 2, gridPosition: 1 }, "2_1", "2_2", "2_0"),
        "2_2": new LudoGrid({ area: 2, gridPosition: 2 }, "2_2", "2_3", "2_1"),
        "2_3": new LudoGrid({ area: 2, gridPosition: 3 }, "2_3", "2_4", "2_2"),
        "2_4": new LudoGrid({ area: 2, gridPosition: 4 }, "2_4", "2_5", "2_3"),
        "2_5": new LudoGrid({ area: 2, gridPosition: 5 }, "2_5", "2_6", "2_4"),
        "2_6": new LudoGrid({ area: 2, gridPosition: 6 }, "2_6", "2_7", "2_5"),
        "2_7": new LudoGrid({ area: 2, gridPosition: 7 }, "2_7", "2_8", "2_6"),
        "2_8": new LudoGrid({ area: 2, gridPosition: 8 }, "2_8", "2_9", "2_7"),
        "2_9": new LudoGrid({ area: 2, gridPosition: 9 }, "2_9", "2_10", "2_8"),
        "2_10": new LudoGrid({ area: 2, gridPosition: 10 }, "2_10", "2_11", "2_9"),
        "2_11": new LudoGrid({ area: 2, gridPosition: 11 }, "2_11", "2_12", "2_10"),
        "2_12": new LudoGrid({ area: 2, gridPosition: 12 }, "2_12", "3_0", "2_11"),
        "2_-1": new LudoGrid({ area: 2, gridPosition: -1 }, "2_-1", "2_-2", "2_0"),
        "2_-2": new LudoGrid({ area: 2, gridPosition: -2 }, "2_-2", "2_-3", "2_-1"),
        "2_-3": new LudoGrid({ area: 2, gridPosition: -3 }, "2_-3", "2_-4", "2_-2"),
        "2_-4": new LudoGrid({ area: 2, gridPosition: -4 }, "2_-4", "2_-5", "2_-3"),
        "2_-5": new LudoGrid({ area: 2, gridPosition: -5 }, "2_-5", "2_-6", "2_-4"),
        "2_-6": new LudoGrid({ area: 2, gridPosition: -6 }, "2_-6", null, "2_-5"),
        "2_-7": new LudoGrid({ area: 2, gridPosition: -7 }, "2_-7", null, "2_-5"),
        "2_-8": new LudoGrid({ area: 2, gridPosition: -8 }, "2_-8", null, "2_-5"),
        "2_-9": new LudoGrid({ area: 2, gridPosition: -9 }, "2_-9", null, "2_-5"),

        "3_101": new LudoGrid({ area: 3, gridPosition: 101 }, "3_101", "3_2", null),
        "3_102": new LudoGrid({ area: 3, gridPosition: 102 }, "3_102", "3_2", null),
        "3_103": new LudoGrid({ area: 3, gridPosition: 103 }, "3_103", "3_2", null),
        "3_104": new LudoGrid({ area: 3, gridPosition: 104 }, "3_104", "3_2", null),
        "3_0": new LudoGrid({ area: 3, gridPosition: 0 }, "3_0", "3_1", "2_12"),
        "3_1": new LudoGrid({ area: 3, gridPosition: 1 }, "3_1", "3_2", "3_0"),
        "3_2": new LudoGrid({ area: 3, gridPosition: 2 }, "3_2", "3_3", "3_1"),
        "3_3": new LudoGrid({ area: 3, gridPosition: 3 }, "3_3", "3_4", "3_2"),
        "3_4": new LudoGrid({ area: 3, gridPosition: 4 }, "3_4", "3_5", "3_3"),
        "3_5": new LudoGrid({ area: 3, gridPosition: 5 }, "3_5", "3_6", "3_4"),
        "3_6": new LudoGrid({ area: 3, gridPosition: 6 }, "3_6", "3_7", "3_5"),
        "3_7": new LudoGrid({ area: 3, gridPosition: 7 }, "3_7", "3_8", "3_6"),
        "3_8": new LudoGrid({ area: 3, gridPosition: 8 }, "3_8", "3_9", "3_7"),
        "3_9": new LudoGrid({ area: 3, gridPosition: 9 }, "3_9", "3_10", "3_8"),
        "3_10": new LudoGrid({ area: 3, gridPosition: 10 }, "3_10", "3_11", "3_9"),
        "3_11": new LudoGrid({ area: 3, gridPosition: 11 }, "3_11", "3_12", "3_10"),
        "3_12": new LudoGrid({ area: 3, gridPosition: 12 }, "3_12", "0_0", "3_11"),
        "3_-1": new LudoGrid({ area: 3, gridPosition: -1 }, "3_-1", "3_-2", "3_0"),
        "3_-2": new LudoGrid({ area: 3, gridPosition: -2 }, "3_-2", "3_-3", "3_-1"),
        "3_-3": new LudoGrid({ area: 3, gridPosition: -3 }, "3_-3", "3_-4", "3_-2"),
        "3_-4": new LudoGrid({ area: 3, gridPosition: -4 }, "3_-4", "3_-5", "3_-3"),
        "3_-5": new LudoGrid({ area: 3, gridPosition: -5 }, "3_-5", "3_-6", "3_-4"),
        "3_-6": new LudoGrid({ area: 3, gridPosition: -6 }, "3_-6", null, "3_-5"),
        "3_-7": new LudoGrid({ area: 3, gridPosition: -7 }, "3_-7", null, "3_-5"),
        "3_-8": new LudoGrid({ area: 3, gridPosition: -8 }, "3_-8", null, "3_-5"),
        "3_-9": new LudoGrid({ area: 3, gridPosition: -9 }, "3_-9", null, "3_-5"),
    }
    private static _ports: Maps = {
        "airport_0": [{ "x": 107, "y": 538 }, { "x": 197, "y": 538 }, { "x": 107, "y": 630 }, { "x": 197, "y": 630 }],
        "airport_1": [{ "x": 107, "y": 108 }, { "x": 197, "y": 108 }, { "x": 107, "y": 196 }, { "x": 197, "y": 196 }],
        "airport_2": [{ "x": 539, "y": 108 }, { "x": 629, "y": 108 }, { "x": 539, "y": 196 }, { "x": 629, "y": 196 }],
        "airport_3": [{ "x": 539, "y": 538 }, { "x": 629, "y": 538 }, { "x": 539, "y": 630 }, { "x": 629, "y": 630 }],
        "player_0": [
            { "x": 368, "y": 704 },

            { "x": 320, "y": 704 },
            { "x": 320, "y": 656 },
            { "x": 320, "y": 608 },
            { "x": 320, "y": 560 },
            { "x": 320, "y": 512 },
            { "x": 320, "y": 464 },

            { "x": 272, "y": 416 },
            { "x": 224, "y": 416 },
            { "x": 176, "y": 416 },
            { "x": 128, "y": 416 },
            { "x": 80, "y": 416 },
            { "x": 32, "y": 416 },

            { "x": 368, "y": 656 },
            { "x": 368, "y": 608 },
            { "x": 368, "y": 560 },
            { "x": 368, "y": 512 },
            { "x": 368, "y": 464 },

            { "x": 368, "y": 422 },
            { "x": 337, "y": 422 },
            { "x": 368, "y": 390 },
            { "x": 399, "y": 422 }
        ],
        "player_1": [
            { "x": 32, "y": 368 },
            { "x": 32, "y": 320 },
            { "x": 80, "y": 320 },
            { "x": 128, "y": 320 },
            { "x": 176, "y": 320 },
            { "x": 224, "y": 320 },
            { "x": 272, "y": 320 },
            { "x": 320, "y": 272 },
            { "x": 320, "y": 224 },
            { "x": 320, "y": 176 },
            { "x": 320, "y": 128 },
            { "x": 320, "y": 80 },
            { "x": 320, "y": 32 },
            { "x": 80, "y": 368 },
            { "x": 128, "y": 368 },
            { "x": 176, "y": 368 },
            { "x": 224, "y": 368 },
            { "x": 272, "y": 368 },
            { "x": 314, "y": 368 },
            { "x": 314, "y": 336 },
            { "x": 346, "y": 368 },
            { "x": 314, "y": 400 }
        ],
        "player_2": [
            { "x": 368, "y": 32 },
            { "x": 416, "y": 32 },
            { "x": 416, "y": 80 },
            { "x": 416, "y": 128 },
            { "x": 416, "y": 176 },
            { "x": 416, "y": 224 },
            { "x": 416, "y": 272 },
            { "x": 463, "y": 320 },
            { "x": 511, "y": 320 },
            { "x": 559, "y": 320 },
            { "x": 607, "y": 320 },
            { "x": 655, "y": 320 },
            { "x": 703, "y": 320 },

            { "x": 368, "y": 80 },
            { "x": 368, "y": 128 },
            { "x": 368, "y": 176 },
            { "x": 368, "y": 224 },
            { "x": 368, "y": 272 },

            { "x": 368, "y": 314 },
            { "x": 399, "y": 314 },
            { "x": 368, "y": 346 },
            { "x": 337, "y": 314 }
        ],
        "player_3": [
            { "x": 703, "y": 368 },

            { "x": 703, "y": 416 },
            { "x": 655, "y": 416 },
            { "x": 607, "y": 416 },
            { "x": 559, "y": 416 },
            { "x": 511, "y": 416 },
            { "x": 463, "y": 416 },

            { "x": 416, "y": 464 },//
            { "x": 416, "y": 512 },//
            { "x": 416, "y": 560 },//
            { "x": 416, "y": 608 },//
            { "x": 416, "y": 656 },//
            { "x": 416, "y": 704 },//

            { "x": 655, "y": 368 },
            { "x": 607, "y": 368 },
            { "x": 559, "y": 368 },
            { "x": 511, "y": 368 },
            { "x": 463, "y": 368 },

            { "x": 422, "y": 368 },
            { "x": 422, "y": 400 },
            { "x": 392, "y": 368 },
            { "x": 422, "y": 336 }
        ]
    };
    private static _mapDuel = {
        "0_0": new LudoGrid({ area: 0, gridPosition: 0 }, "0_0", "0_1", null),
        "0_1": new LudoGrid({ area: 0, gridPosition: 1 }, "0_1", "0_2", null),
        "0_2": new LudoGrid({ area: 0, gridPosition: 2 }, "0_2", "0_3", null),
        "0_3": new LudoGrid({ area: 0, gridPosition: 3 }, "0_3", "0_4", null),
        "0_4": new LudoGrid({ area: 0, gridPosition: 4 }, "0_4", "0_5", null),
        "0_5": new LudoGrid({ area: 0, gridPosition: 5 }, "0_5", "0_6", null),
        "0_6": new LudoGrid({ area: 0, gridPosition: 6 }, "0_6", "0_7", null),
        "0_7": new LudoGrid({ area: 0, gridPosition: 7 }, "0_7", "0_8", null),
        "0_8": new LudoGrid({ area: 0, gridPosition: 8 }, "0_8", "1_0", null),

        "1_0": new LudoGrid({ area: 1, gridPosition: 0 }, "1_0", "1_1", null),
        "1_1": new LudoGrid({ area: 1, gridPosition: 1 }, "1_1", "1_2", null),
        "1_2": new LudoGrid({ area: 1, gridPosition: 2 }, "1_2", "1_3", null),
        "1_3": new LudoGrid({ area: 1, gridPosition: 3 }, "1_3", "1_4", null),
        "1_4": new LudoGrid({ area: 1, gridPosition: 4 }, "1_4", "1_5", null),
        "1_5": new LudoGrid({ area: 1, gridPosition: 5 }, "1_5", "1_6", null),
        "1_6": new LudoGrid({ area: 1, gridPosition: 6 }, "1_6", "1_7", null),
        "1_7": new LudoGrid({ area: 1, gridPosition: 7 }, "1_7", "1_8", null),
        "1_8": new LudoGrid({ area: 1, gridPosition: 8 }, "1_8", "2_0", null),

        "2_0": new LudoGrid({ area: 2, gridPosition: 0 }, "2_0", "2_1", null),
        "2_1": new LudoGrid({ area: 2, gridPosition: 1 }, "2_1", "2_2", null),
        "2_2": new LudoGrid({ area: 2, gridPosition: 2 }, "2_2", "2_3", null),
        "2_3": new LudoGrid({ area: 2, gridPosition: 3 }, "2_3", "2_4", null),
        "2_4": new LudoGrid({ area: 2, gridPosition: 4 }, "2_4", "2_5", null),
        "2_5": new LudoGrid({ area: 2, gridPosition: 5 }, "2_5", "2_6", null),
        "2_6": new LudoGrid({ area: 2, gridPosition: 6 }, "2_6", "2_7", null),
        "2_7": new LudoGrid({ area: 2, gridPosition: 7 }, "2_7", "2_8", null),
        "2_8": new LudoGrid({ area: 2, gridPosition: 8 }, "2_8", "3_0", null),

        "3_0": new LudoGrid({ area: 3, gridPosition: 0 }, "3_0", "3_1", null),
        "3_1": new LudoGrid({ area: 3, gridPosition: 1 }, "3_1", "3_2", null),
        "3_2": new LudoGrid({ area: 3, gridPosition: 2 }, "3_2", "3_3", null),
        "3_3": new LudoGrid({ area: 3, gridPosition: 3 }, "3_3", "3_4", null),
        "3_4": new LudoGrid({ area: 3, gridPosition: 4 }, "3_4", "3_5", null),
        "3_5": new LudoGrid({ area: 3, gridPosition: 5 }, "3_5", "3_6", null),
        "3_6": new LudoGrid({ area: 3, gridPosition: 6 }, "3_6", "3_7", null),
        "3_7": new LudoGrid({ area: 3, gridPosition: 7 }, "3_7", "3_8", null),
        "3_8": new LudoGrid({ area: 3, gridPosition: 8 }, "3_8", "0_0", null),
    }
    private static _portsDuel: Maps = {
        "airport_0": [],
        "airport_1": [],
        "airport_2": [],
        "airport_3": [],
        "player_0": [{ "x": 368, "y": 698 }, { "x": 302, "y": 698 }, { "x": 302, "y": 632 }, { "x": 302, "y": 566 }, { "x": 302, "y": 500 }, { "x": 236, "y": 434 }, { "x": 170, "y": 434 }, { "x": 104, "y": 434 }, { "x": 38, "y": 434 }],
        "player_1": [{ "x": 38, "y": 368 }, { "x": 38, "y": 302 }, { "x": 104, "y": 302 }, { "x": 170, "y": 302 }, { "x": 236, "y": 302 }, { "x": 302, "y": 236 }, { "x": 302, "y": 170 }, { "x": 302, "y": 104 }, { "x": 302, "y": 38 }],
        "player_2": [{ "x": 368, "y": 38 }, { "x": 434, "y": 38 }, { "x": 434, "y": 104 }, { "x": 434, "y": 170 }, { "x": 434, "y": 236 }, { "x": 500, "y": 302 }, { "x": 566, "y": 302 }, { "x": 632, "y": 302 }, { "x": 698, "y": 302 }],
        "player_3": [{ "x": 698, "y": 368 }, { "x": 698, "y": 434 }, { "x": 632, "y": 434 }, { "x": 566, "y": 434 }, { "x": 500, "y": 434 }, { "x": 434, "y": 500 }, { "x": 434, "y": 566 }, { "x": 434, "y": 632 }, { "x": 434, "y": 698 }]
    }
}