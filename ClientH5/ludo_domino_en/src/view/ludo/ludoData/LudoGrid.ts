class LudoGrid {
    private _myName: string = null;//本格子的名字
    private _station: Station = null;//当前站点
    private _port: port = { x: null, y: null };
    private _nextName: string = null;
    private _lastName: string = null;
    private _transferName: string = null;
    public get gridName() { return this._myName }
    public set port(port: port) { this._port = port; }
    public get port(): port { return this._port; }//todo:终点站port使用arr.shift返回
    public get station(): Station { return this._station }
    public beforeEnd: boolean = false;
    public inCorner: boolean = false;
    public inStart: boolean = false;
    public inEndArea: boolean = false;
    public inEnd: boolean = false;
    public inHome: boolean = false;

    public inSafetyArea: boolean = false;
    constructor(station: Station, myName: string, nextName: string, lastName: string) {
        this._station = station;
        this._myName = myName;
        this._nextName = nextName;
        this._lastName = lastName;
        var gridPosition = station.gridPosition;
        this.beforeEnd = gridPosition == -5;
        this.inCorner = gridPosition == 0;
        this.inStart = gridPosition == 2;
        this.inEndArea = gridPosition < 0;
        this.inHome = gridPosition >= 100;
        this.inEnd = gridPosition < -5;
        this.inStart = gridPosition == 2;
        this.inSafetyArea = this.inStart || gridPosition == 10 || this.inEndArea;
    }
    public get color(): number {
        if (this._myName) {
            return parseInt(this._myName.split("_")[0]);
        }
        return -1;
    }
    public get gridPosition(): number {
        if (this._myName) {
            return parseInt(this._myName.split("_")[1]);
        }
        return -1;
    }

    public atGrid(name: string): boolean {//名字是否一样
        return name == this._myName;
    }
    public get nextName() { return this._nextName; }
    public get lastName() { return this._lastName; }
    public get myName() { return this._myName; }
    public get transferName() { return this._transferName; }
    public set transferName(val: string) { this._transferName = val; }

    // public set nextName(val: string) { this._nextName = val };
}