/**
* name 
*/
module ludo {
    export class GameSocket extends Laya.Socket {
        private byte: Laya.Byte;
        public msgindex: number = 0;
        constructor() {
            super();
            this.byte = new Laya.Byte();
            if (yalla.util.IsBrowser()) this.on(Laya.Event.MESSAGE, this, this.receiveHandler);
            else yalla.NativeWebSocket.instance.on(Laya.Event.MESSAGE, this, this.receiveHandler);
        }
        static _instance: ludo.GameSocket = null;
        static get instance(): ludo.GameSocket {
            return this._instance ? this._instance : this._instance = new GameSocket();
        }
        static clear() {
            if (ludo.GameSocket._instance) {
                try {
                    yalla.NativeWebSocket.instance.off(Laya.Event.MESSAGE, ludo.GameSocket._instance, ludo.GameSocket._instance.receiveHandler);
                    ludo.GameSocket._instance.offAll();
                    ludo.GameSocket._instance.closeNet();//TODO::1.3.1
                    ludo.GameSocket._instance.cleanSocket();
                    ludo.GameSocket._instance = null;
                    // yalla.NativeWebSocket.instance.sendNetClose();
                } catch (error) {
                }
            }
        }
        get protocolBuffer() {
            return ludo.Proto.instance;
        }
        get root(): any {
            return this.protocolBuffer.root;
        }
        private receiveHandler(arrayBuffer: ArrayBuffer = null) {
            if (!yalla.Global.isconnect) { return; }
            this.byte.clear();
            if (yalla.util.IsBrowser()) arrayBuffer && this.byte.writeArrayBuffer(arrayBuffer);
            else arrayBuffer && this.byte.writeArrayBuffer(new Uint8Array(arrayBuffer));
            this.byte.pos = 0;
            if (this.byte.length < 4 || this.byte.length != this.byte.getInt16() + 4) {
                this.closeNet(); return;
            }
            this.byte.pos = 3;
            var type = this.byte.readByte(),
                bytes = this.byte.getUint8Array(4, this.byte.length - 4),
                protoMsg: any;
            let cacheBytes;
            switch (type) {
                case this.root.Command.LOGIN://登录 10
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "LoginResponse");
                    break;
                case this.root.Command.TICKET_LOGIN://票据登录 11
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "TicketLoginRequest");
                    break;
                case this.root.Command.QUICKSTART://进入房间 20
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "QuickEnterRoomResponse");
                    yalla.CLogDBA.instance.clear();
                    break;
                case this.root.Command.QUIT_ROOM://离开房间 21
                    cacheBytes = bytes && bytes.slice(0);
                    NetMessageCache.getInstance().addMessage("QUIT_ROOM", (data: any) => {
                        if (!data || data.byteLength == 0) return;
                        let cmdType = type;
                        let protoMsgCache = this.protocolBuffer.decodeMsg(data, "QuitRoomResponse");
                        if (protoMsgCache) {
                            this.event(Laya.Event.CHANGE, [cmdType, protoMsgCache]);
                        }
                    }, cacheBytes);
                    // protoMsg = this.protocolBuffer.decodeMsg(bytes, "QuitRoomResponse");
                    break;
                case this.root.Command.JOIN_ROOM://加入房间 22
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "JoinRoomResponse");
                    break;
                case this.root.Command.GAMEOPERATE://游戏操作 入座 准备 站起等 30
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GameOperateResponse");
                    break;
                case this.root.Command.GAME_PRESTART://游戏开始 倒计时开始 39
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GamePreStartResponse");
                    break;
                case this.root.Command.GAMESTATUS://游戏状态 40
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GameStatusResponse");
                    break;
                case this.root.Command.GAME_PLAYER_COLOR_SELECT://更新玩家的颜色 41
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GamePlayerColorSelectResponse");
                    yalla.CLogDBA.instance.clear();
                    break;
                case this.root.Command.GAME_MAP_WHETHER_CHANGE://道具显示 42
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GameMapPropChangeResponse");
                    break;
                case this.root.Command.GAME_ACTIVITY_PROP_REFRESH://活动道具刷新
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "ActivityPropResponse");
                    break;
                case this.root.Command.GAME_MAP_GRID_EVENT://目标格子发生的事件 43
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GridEventResponse");
                    break;
                case this.root.Command.GAME_BUFF_USE://使用buff 44
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GameBuffUseResponse");
                    break;
                case this.root.Command.GAME_CHESS_CANMOVELIST://可以移动的棋子列表 选择棋子 45
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GameChessCanMoveListResponse");
                    break;
                case this.root.Command.GAME_MAP_CHESS_MOVE://棋子移动 46
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "ChessMoveResponse");
                    break;
                case this.root.Command.GAME_MAP_JUNGLE_EVENT://丛林模式道具格子事件 47
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GameMapJungleChangeResponse");
                    break;
                case this.root.Command.GAME_CHESS_STATE_EVENT://棋子状态事件 48
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GameChessStateChangeResponse");
                    break;
                case this.root.Command.GAMERESULT://游戏结束 49
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GameResultResponse");
                    break;
                case this.root.Command.GAMEPLAYERSTATUS://游戏状态 50
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GamePlayerStatusResponse");
                    break;
                case this.root.Command.GAME_PLAYER_COMPLETE://有玩家胜利 51
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GamePlayerCompleteResponse");
                    break;
                case this.root.Command.PLAYER_ENTER://后进入的玩家信息 60
                    cacheBytes = bytes && bytes.slice(0);
                    NetMessageCache.getInstance().addMessage("PLAYER_ENTER", (data: any) => {
                        if (!data || data.byteLength == 0) return;
                        let cmdType = type;
                        let protoMsgCache = this.protocolBuffer && this.protocolBuffer.decodeMsg(data, "GamePlayerEnterResponse");
                        if (protoMsgCache) {
                            this.event(Laya.Event.CHANGE, [cmdType, protoMsgCache]);
                        }
                    }, cacheBytes);
                    // protoMsg = this.protocolBuffer.decodeMsg(bytes, "GamePlayerEnterResponse");
                    break;
                case this.root.Command.PLAYER_CHAT_ALL://聊天 70
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GameRoomPlayerChatToAllRequestAndResponse");
                    break;
                case this.root.Command.UPDATA_PLAYER_COIN://更新金币 80
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "UpdataCoinResponse");
                    break;
                case this.root.Command.UPDATA_PLAYER_LEVEL://经验升级 81
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "MassagePlayerLevelUPResponse");
                    break;
                case this.root.Command.FLUSH_CURRENCY://金币等级回复 82
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "FlushCurrencyResponse");
                    break;
                // case this.root.Command.FLUSH_GAMEINFO://刷新游戏信息 83
                //     protoMsg = this.protocolBuffer.decodeMsg(bytes, "FlushCurrencyResponse");
                // break;
                case this.root.Command.GET_AGORA_TOKEN://获取语音Agora token 90
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GetAgoraTokenResponse");
                    break;
                case this.root.Command.GET_ZEGO_TOKEN://Zego token 92
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GetZegoTokenResponse");
                    break;
                case this.root.Command.AGORA_OPERATE://91
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "AgoraOperateRequestAndResponse");
                    break;
                case this.root.Command.FRIEND_LIST://返回好友列表 101
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "FriendListResponse");
                    break;
                case this.root.Command.FRIEND_INVITE://邀请好友102
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "FriendInviteRequest");
                    break;
                case this.root.Command.GAME_GIFT_CNF_LIST://获取游戏内互动礼物配置列表111
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GameGiftCnfListRespone");
                    break;
                case this.root.Command.GAME_GIFT_SEND://游戏内互动礼物赠送112
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GameGiftSendResponse");
                    break;
                case this.root.Command.GOODS_LIST://获取商品列表 120
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "GoodsListResponse");
                    break;
                case this.root.Command.OUT_GAME://重复登录 126
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "OutGameResponse");
                    break;

                case this.root.Command.HEART://心跳 127
                    protoMsg = this.protocolBuffer.decodeMsg(bytes, "HeartResponse");
                    break;
            }
            if (protoMsg) {//1.2.3add protoMsg判断
                this.event(Laya.Event.CHANGE, [type, protoMsg]);
            }
        }
        heartBeat(arg?: any) {
            this.sendMsg(ludo.Proto.instance.encodeMsg("HeartRequest", arg), 127);
        }
        ticketLogin(idx: number, token: string, roomid: number, version: string) {//票据登录
            let msg = {
                idx: idx,
                token: token,
                roomid: roomid,
                msgindex: this.msgindex,
                version: version
            }
            var byte = ludo.Proto.instance.encodeMsg("TicketLoginRequest", msg);
            this.sendMsg(byte, 11);
        }
        getToken(){
            switch (yalla.Global.Account.voiceType) {
                case VoiceType.Agora:
                    this.getAgoraToken();
                    break;
                case VoiceType.Zego://1.2.8接入zego 不需要tokrn
                    this.getZegoToken();
                    break;
            }
        }
        getAgoraToken() {
            let msg = {
                idx: yalla.Global.Account.idx,
                roomid: ludoRoom.instance.ID
            }
            var byte = ludo.Proto.instance.encodeMsg("GetAgoraTokenRequest", msg);
            this.sendMsg(byte, 90);
        }
        getZegoToken() {
            let msg = {
                idx: yalla.Global.Account.idx,
                roomid: ludoRoom.instance.ID,
                version: 4
            }
            var byte = ludo.Proto.instance.encodeMsg("GetZegoTokenRequest", msg);
            this.sendMsg(byte, 92);
        }

        chooseChess(msg: any) {
            var byte = ludo.Proto.instance.encodeMsg("GameOperateRequest", msg);
            this.sendMsg(byte, 30);
        }
        clickReset() {
            var byte = ludo.Proto.instance.encodeMsg("GameOperateRequest", {
                operate: 5,
                roomid: ludoRoom.instance.ID,
                idx: yalla.Global.Account.idx
            });
            this.sendMsg(byte, 30);
        }
        chatOff() {//屏蔽聊天
            var byte = ludo.Proto.instance.encodeMsg("GameOperateRequest", {
                operate: 11,
                idx: yalla.Global.Account.idx
            });
            this.sendMsg(byte, 30);
        }
        cancleChatOff() {//取消屏蔽聊天
            var byte = ludo.Proto.instance.encodeMsg("GameOperateRequest", {
                operate: 12,
                idx: yalla.Global.Account.idx
            });
            this.sendMsg(byte, 30);
        }
        sendChat(msgResult: any = {}) {
            msgResult.idx = yalla.Global.Account.idx;
            msgResult.roomid = ludoRoom.instance.ID;
            var byte = ludo.Proto.instance.encodeMsg("GameRoomPlayerChatToAllRequestAndResponse", msgResult);
            this.sendMsg(byte, 70);
        }
        quitRoom() {
            var byte = ludo.Proto.instance.encodeMsg("QuitRoomRequest", {
                roomid: ludoRoom.instance.ID,
            });
            // ludoRoom.instance.ID = -1;
            this.sendMsg(byte, 21);
        }
        clickDice() {
            var byte = ludo.Proto.instance.encodeMsg("GameOperateRequest", {
                operate: 4,
                roomid: ludoRoom.instance.ID,
                idx: yalla.Global.Account.idx
            });
            this.sendMsg(byte, 30);
            // yalla.CLogDBA.instance.record_request(yalla.CLogDBA_EventID.Ludo_DiceNums_Resp);
        }
        getFriends() {//获取好友
            var byte = ludo.Proto.instance.encodeMsg("FriendListRequest", {});
            this.sendMsg(byte, 101);
        }
        addFriend(idx: number) {//加好友
            var byte = ludo.Proto.instance.encodeMsg("AddFriendMsgRequest", {
                fromidx: yalla.Global.Account.idx,
                toidx: idx
            });
            this.sendMsg(byte, 100);
        }
        flush() {//刷新金币等级
            var byte = ludo.Proto.instance.encodeMsg("FlushCurrencyRequest", null);
            this.sendMsg(byte, 82);
        }
        cancleTrust() {//取消托管
            if (ludoRoom.instance.ID && yalla.Global.Account.idx) {
                let msg = {
                    operate: 9,
                    roomid: ludoRoom.instance.ID,
                    idx: yalla.Global.Account.idx,
                }
                var byte = ludo.Proto.instance.encodeMsg("GameOperateRequest", msg);
                this.sendMsg(byte, 30);
            }
        }
        flushGameData() {//刷新游戏数据
            if (yalla.Global.Account.idx && ludoRoom.instance.ID) {
                var obj = {
                    idx: yalla.Global.Account.idx,
                    roomid: ludoRoom.instance.ID
                }
                var byte = ludo.Proto.instance.encodeMsg("FlushGameInfoRequest", obj);
                this.sendMsg(byte, 83);
            }
        }
        GameGiftCnfListRequest() {
            var byte = ludo.Proto.instance.encodeMsg("GameGiftCnfListRequest", null);
            this.sendMsg(byte, 111);
        }
        GameGiftSendRequest(giftId: number, rcvIdxs: Array<any>) {
            var byte = ludo.Proto.instance.encodeMsg("GameGiftSendRequest", {
                giftId: giftId,
                rcvIdx: rcvIdxs
            });
            this.sendMsg(byte, 112);
        }
        sendMsg(byte: any, command: number) {
            if (byte) {
                this.byte.clear();
                this.byte.writeInt16(byte.byteLength);
                this.byte.writeByte(0);
                this.byte.writeByte(command);
                this.byte.writeArrayBuffer(byte);
                if (yalla.util.IsBrowser()) this.send(this.byte.buffer);
                else {
                    var arr: Array<number>;
                    if (yalla.Native.instance.deviceType == DeviceType.IOS) {
                        arr = Array.prototype.slice.call(new Uint8Array(this.byte.buffer));
                    } else {
                        arr = Array.prototype.slice.call(new Int8Array(this.byte.buffer));
                    }
                    yalla.NativeWebSocket.instance.sendNetMsg(arr);
                }
            }
        }

        closeNet() {
            if (yalla.util.IsBrowser()) {
                this.close();
            } else {
                yalla.NativeWebSocket.instance.sendNetClose();
            }
        }
    }
}