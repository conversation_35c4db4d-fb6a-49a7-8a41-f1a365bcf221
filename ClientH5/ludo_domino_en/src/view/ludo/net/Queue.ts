module Ludo {
    export class Queue extends Laya.EventDispatcher {
        private _items: Array<Object> = [];//队列数组
        private _progress: number = 0;//消息序号
        static NEXT: string = "next";
        get progress(): number {
            return this._progress;
        }
        get items(): Array<Object> {
            return this._items;
        }
        constructor() {
            super();
        }
        reset() {
            this._progress = 0;
            this._items = [];
        }
        /**
         * 添加队列
         */
        enqueue(cmd: number, msg: any) {
            this._progress = msg.msgindex || this._progress;
            this._items.push({
                cmd: cmd,
                msg: msg
            });
        }
        shift() {
            this._items.shift();
        }
        /**
         * 删除并返回队列
         */
        dequeue(): Object {
            return this._items.shift();
        }
        size() {
            return this._items.length;
        }
        isEmpty() {
            return this._items.length === 0;
        }
    }
}