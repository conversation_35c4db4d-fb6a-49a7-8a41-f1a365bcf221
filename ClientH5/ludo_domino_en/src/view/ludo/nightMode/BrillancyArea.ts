class BrillancyArea extends Laya.Image {
    private _level: number = 0;
    private _isSafe: boolean = false;
    private _overlapLevel: number = 1;//叠字数量 如果在安全点忽略不计
    public _color: number = null;
    private _endLevel: number = 0;//到终点的棋子的数量
    constructor() {
        super();
        this.level = 1;
        this.anchorX = this.anchorY = 0.5;
        this.on(Laya.Event.REMOVED, this, this.clear);
    }
    public set overlapLevel(val: number) {
        if (this._overlapLevel != val) {
            !this.isSafe && (this._overlapLevel = val);
            this.update();
        }
    }
    public set level(val: number) {
        if (val != this._level) {
            this.skin = `curtain/img_circle_${val}.png`;
            this._level = val;
            switch (val) {
                case 1:
                    this.size(174, 174);
                    break;
                case 2:
                    this.size(262, 262);
                    break;
                case 3:
                    this.size(356, 356);
                    break;
                case 4:
                    this.size(454, 454);
                    break;
                case 5:
                    this.size(542, 542);
                    break;

            }
        }
    }
    public get level(): number {
        return this._level;
    }
    public set isSafe(val: boolean) {
        this._isSafe = val;
        this.update();
    }
    public set endLevel(n: number) {
        this._endLevel = n;
        this.update();
    }
    update() {
        if (this._isSafe) {
            this.level = this._endLevel + 2;
        } else {
            this.level = this._overlapLevel + this._endLevel;
        }
    }
    clear() {
        this.destroy(true);
    }
}