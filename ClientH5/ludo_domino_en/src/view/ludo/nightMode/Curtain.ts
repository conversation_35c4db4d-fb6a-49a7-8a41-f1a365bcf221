//黑夜模式的遮罩
class Curtain extends ui.ludo.nightMode.curtainUI {
    constructor() {
        super();
        // this._endLevel = 0;
        if (ludoRoom.instance.isTeamGame()) {
            this.fixed_mate.visible = true;
        } else {
            this.fixed_mate.removeSelf().destroy();
        }
        this.removeEvents();
        this.addEvents();
    }
    private get myColor(): number { return LudoBoardData.color; }
    public friColor: number = null;
    private gridLight: Object = {};
    private timeLine: Laya.TimeLine = new Laya.TimeLine();
    public isOurChess(color) {//是不是自己或者队友的棋子
        return color == this.myColor || color == this.friColor;
    }

    public clear() {
        this.prop_light_0.timer.clearAll(this);
        this.prop_light_1.timer.clearAll(this);
        this.prop_light_2.timer.clearAll(this);
        this.prop_light_3.timer.clearAll(this);
        this.removeEvents();
        this.timeLine.pause();
        this.timeLine.reset();
        this.timeLine.offAll();
        this.timeLine.destroy();
        this.timer.clearAll(this);
        this.gridLight = {};
        this.friColor = null;
    }
    private addEvents() {
        ludoRoom.instance.on("reDraw", this, this.reDraw);
        ludoRoom.instance.on("update", this, this.update);
        ludoRoom.instance.on("moveLight", this, this.moveLight);
        this.once(Laya.Event.REMOVED, this, this.clear);
    }
    private removeEvents() {
        ludoRoom.instance.off("reDraw", this, this.reDraw);
        ludoRoom.instance.off("update", this, this.update);
        ludoRoom.instance.off("moveLight", this, this.moveLight);
    }
    public showPropLight(area: number) {
        var propLight: Laya.Image = this[`prop_light_${area}`];
        if (propLight && !propLight.visible) {
            propLight.alpha = 0;
            propLight.visible = true;
            propLight.timer.frameLoop(2, this, this.fadeIn, [propLight]);
        }
    }
    private fadeIn(node: Laya.Image) {
        if (node) {
            node.alpha += 0.1;
            if (node.alpha >= 1) {
                node.timer.clear(this, this.fadeIn);
            }
            this.event("reCache");
        }
    }
    public hidePropLight(area: number) {
        var propLight = this[`prop_light_${area}`];
        if (propLight) {
            propLight.alpha = 0;
            propLight.visible = false;
        }
    }

    public setoverlapLevel(chessId: number, n: number) {
        var img = this.gridLight[chessId] as BrillancyArea;
        img && (img.overlapLevel = n);
    }
    public reset() {
        this.hideGlobleEye();
        this.event("reCache");
    }
    public showGolbleEye() {
        this.globle_eye.alpha = 0;
        this.globle_eye.visible = true;
        this.timer.frameLoop(2, this, this.toAlpha0);
    }
    public onForce() { }
    private toAlpha0() {
        this.globle_eye.alpha += 0.05;
        this.event("reCache");
        if (this.globle_eye.alpha >= 1) {
            this.timer.clear(this, this.toAlpha0);
        }
    }
    private toAlpha1() {
        this.globle_eye.alpha -= 0.05;
        this.event("reCache");
        if (this.globle_eye.alpha <= 0) {
            this.timer.clear(this, this.toAlpha1);
        }
    }
    public hideGlobleEye() {
        this.globle_eye.visible = false;
        this.event("reCache");
        this.globle_eye.alpha = 1;
        this.timer.frameLoop(2, this, this.toAlpha1);
    }
    public showSafe(chessId: number, color: number) {
        var safeImg = this.createBrillancyArea(chessId, color);
        safeImg && (safeImg.isSafe = true);
        this.timer.frameOnce(10, this, () => {
            this.event("reCache");
        })
    }
    public hideSafe(chessId: number, color: number) {
        var safeImg = this.createBrillancyArea(chessId, color);
        safeImg && (safeImg.isSafe = false);
    }

    public moveLight(chessId: number, grids: Array<LudoGrid>, color: number) {
        if (this.isOurChess(color)) {
            var img = this.gridLight[chessId] as BrillancyArea;
            if (img) {
                img.overlapLevel = 1;
                img.visible = true;
                this.timeLine.reset();
                grids.forEach((grid: LudoGrid, index) => {
                    this.timeLine.addLabel(index.toString(), 0).to(img, grid.port, ludo.Global.moveTime);
                })
                this.timeLine.play(0, false);
            }
            this.event("reCache");
        }
    }

    public createBrillancyArea(chessId: number, color: number): BrillancyArea {
        var img = this.gridLight[chessId] as BrillancyArea;
        if (!img) {
            img = new BrillancyArea();
            img.name = chessId.toString();
            img._color = color;
            this.gridLight[chessId] = img;
            this.addChild(img);
        }
        return img;
    }
    public update(chessId: number, station: Station, port: port = LudoBoardData.getGridByStation(station).port) {
        if (station.gridPosition >= -5 && station.gridPosition < 100) {
            var img = this.gridLight[chessId];
            if (img) {
                img.visible = true;
                img.pos(port.x, port.y);
            }
        }
    }
    private levelChange() {
        var endChessFri: number = ChessManger.instance.getWinsLenByColor(this.friColor);
        var endChessMe: number = ChessManger.instance.getWinsLenByColor(this.myColor);
        this._terminal.visible = endChessFri + endChessMe > 0;
        // this.endLevel = endChessMe;
        for (var key in this.gridLight) {
            if (this.gridLight.hasOwnProperty(key)) {
                var img = this.gridLight[key] as BrillancyArea;
                if (img) {
                    if (img._color == this.myColor) {
                        img.endLevel = endChessMe;
                    } else {
                        img.endLevel = endChessFri;
                    }
                }
            }
        }
        this.event("levelChange", [endChessMe]);
    }
    public reDraw(chessId: number, station: Station, chessColor: number, isGoal: boolean) {
        if (station && this.isOurChess(chessColor)) {
            if (station.gridPosition == 10) {
                if (isGoal) {
                    this.showSafe(chessId, chessColor);
                } else {
                    this.hideSafe(chessId, chessColor);
                }
            } else if (station.gridPosition < -5) {
                var img = this.gridLight[chessId];
                img && img.removeSelf();
                delete this.gridLight[chessId];
                this.levelChange();
            } else if (station.gridPosition >= 100) {
                var port = LudoBoardData.getGridByStation(station).port;
                var img2 = this.createBrillancyArea(chessId, chessColor);
                img2.pos(port.x, port.y);
                img2.visible = false;
            }
        }
    }
}