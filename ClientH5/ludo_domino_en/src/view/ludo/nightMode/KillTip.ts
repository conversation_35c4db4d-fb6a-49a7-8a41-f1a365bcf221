class KillTip extends ui.ludo.nightMode.killtipUI {
    constructor() {
        super();
        this.pivotY = 96;
    }
    public set winnerFace(url: string) {
        this._winnerFace.skin = "curtain/default_head.png";
        yalla.File.downloadImgByUrl(url, (localPath: string) => {
            yalla.File.groupLoad(localPath, Laya.Handler.create(this, (e) => {
                if (!!e) {
                    this._winnerFace.skin = localPath;
                }
            }));
        })
    }
    public set failerFace(url: string) {
        this._failerFace.skin = "curtain/default_head.png";
        // this._failerFace.skin = url;
        yalla.File.downloadImgByUrl(url, (localPath: string) => {
            yalla.File.groupLoad(localPath, Laya.Handler.create(this, (e) => {
                if (!!e) {
                    this._failerFace.skin = localPath;
                }
            }));
        })
    }
    public set winnerColor(color: number) {
        this._winnerColor.skin = `curtain/head_${color}.png`;
    }
    public set failerColor(color: number) {
        this._failerColor.skin = `curtain/head_${color}.png`;
    }
    public setPos(x: number, y: number) {
        if (x < 120) {
            this.pivotX = 32;
            this.ko_grid.x = 32;
            this.bg.skin = "curtain/ko_l.png"
        } else if (x > 616) {
            this.pivotX = 200;
            this.ko_grid.x = 200;
            this.bg.skin = "curtain/ko_r.png"
        } else {
            this.pivotX = 117;
            this.ko_grid.x = 117;
            this.bg.skin = "curtain/ko_c.png"
        }
        this.pos(x, y);
    }
}