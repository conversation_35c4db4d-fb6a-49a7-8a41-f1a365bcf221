class NightMap extends ui.ludo.nightMode.nightMapUI {
    static _instance: Map = null;
    public _color: number = 0;
    private skin_id: number = 13000;
    private _fightWinnerPlayer: GamePlayer = null;
    private _fightFailerPlayer: GamePlayer = null;
    public _curtainMask: Curtain = null;
    private killTip: KillTip = null;
    private _globalEyeSk: Laya.Skeleton = null;
    private _globalEyeTp: Laya.Templet = null;
    private globle_eye_time: number = 5000;//全局视野时间
    private _lightTp: Laya.Templet = null;
    private _timers = {};
    private _lightAni0: Laya.Skeleton = null;
    private _lightAni1: Laya.Skeleton = null;
    private _lightAni2: Laya.Skeleton = null;
    private _lightAni3: Laya.Skeleton = null;
    constructor() {
        super();
        this.mouseThrough = false;
        this.visible = false;
        this.lightAniInit();
        this._curtainMask = new Curtain();
        this._curtainMask.on("levelChange", this, this.onLevelChange);
        this.killTip = new KillTip();
        this.hideKillTip();
        this.ani_box.addChild(this.killTip);
        this._globalEyeTp = new Laya.Templet();
        this._globalEyeTp.on(Laya.Event.COMPLETE, this, this.onGlobalSkloaded);
        this._globalEyeTp.loadAni("res/sk/night/heiye.sk")
        if (yalla.Font.isRight()) {
            this.visibility_img.skin = "game/visibility_ar.png"
            this.visibility_img.x = 92;
            this.visibility_num.x = 24;
        }
    }
    private lightAniInit() {
        this._lightTp = new Laya.Templet();
        this._lightTp.on(Laya.Event.COMPLETE, this, this.onLightAniCom);
        this._lightTp.loadAni("res/sk/light/deng.sk");
    }
    private onLightAniCom() {
        this._lightAni0 = this._lightTp.buildArmature(0);
        this.light0.addChildAt(this._lightAni0, 0);
        this._lightAni0.scaleX = -1;
        this._lightAni0.pos(74, 65);

        this._lightAni3 = this._lightTp.buildArmature(0);
        this.light3.addChildAt(this._lightAni3, 0);
        this._lightAni3.scaleX = -1;
        this._lightAni3.pos(74, 65);

        this._lightAni1 = this._lightTp.buildArmature(1);
        this.light1.addChild(this._lightAni1);
        this._lightAni1.pos(74, 93);

        this._lightAni2 = this._lightTp.buildArmature(1);
        this.light2.addChild(this._lightAni2);
        this._lightAni2.pos(74, 93);
    }
    private onLevelChange(n: number) {//等级发生变化
        this.visibilityNumMe = n;
    }
    showLight(msg: any) {
        var area = (msg.area - this._color + 4) % 4;
        if (area >= 0 && area <= 3) {
            var light = this[`light${area}`];
            var hideLight = this[`hideLight${area}`];
            this.timer.clear(this, hideLight);
            this.timer.once(this.globle_eye_time, this, hideLight);
            if (light && light.visible) return;
            this.timer.once(200, this, this.showPropLightDelay, [area, light]);
            switch (area) {
                case 0:
                    this._lightAni0 && this._lightAni0.play(1, false);
                    break;
                case 1:
                    this._lightAni1 && this._lightAni1.play(0, false);
                    break;
                case 2:
                    this._lightAni2 && this._lightAni2.play(0, false);
                    break;
                case 3:
                    this._lightAni3 && this._lightAni3.play(1, false);
                    break;
            }
            light.visible = true;
        }
    }

    private showPropLightDelay(area, light) {//200ms之后显示探照区域 切后台之后此堆栈位于隐藏之后 light已经隐藏 所以要判断light是否还在显示
        if (light && light.visible) {
            this._curtainMask && this._curtainMask.showPropLight(area);
            var ray = this[`ray${area}`];
            if (ray) ray.visible = true;
            this.maskRecache();
        }
    }

    hideLight0() {
        this._curtainMask && this._curtainMask.hidePropLight(0);
        this.light0.visible = false;
        this.ray0.visible = false;
        this.maskRecache();
    }
    hideLight1() {
        this._curtainMask && this._curtainMask.hidePropLight(1);
        this.light1.visible = false;
        this.ray1.visible = false;
        this.maskRecache();
    }
    hideLight2() {
        this._curtainMask && this._curtainMask.hidePropLight(2);
        this.light2.visible = false;
        this.ray2.visible = false;
        this.maskRecache();
    }
    hideLight3() {
        this._curtainMask && this._curtainMask.hidePropLight(3);
        this.light3.visible = false;
        this.ray3.visible = false;
        this.maskRecache();
    }
    hideVisibility() {
        this.visibility_bg.visible = false;
    }
    showGolbleEye() {
        this._curtainMask.showGolbleEye();
        this._globalEyeSk && this._globalEyeSk.play(0, false);
        this.night_mask.visible = false;
        this.timer.once(this.globle_eye_time, this, this.hideGlobleEye);
    }
    hideGlobleEye() {
        this._curtainMask.hideGlobleEye();
        this.night_mask.visible = true;
    }
    private onGlobalSkloaded() {
        this._globalEyeSk = this._globalEyeTp.buildArmature(0);
        this._globalEyeSk.playbackRate(1.7);
        this.addChild(this._globalEyeSk);
        this._globalEyeSk.blendMode = "lighter";
        this._globalEyeSk.pos(this.width / 2, this.height / 2)
        // this._globalEyeSk.play(0, false);
    }
    private set visibilityNumMe(n: number) {
        var l = (n + 1) * 2 + 1;
        this.visibility_num.text = `${l}x${l}`;
    }

    public set fighterWinnerPlayer(pl: GamePlayer) {
        this._fightWinnerPlayer = pl;
    }
    public set fightFailerPlayer(pl: GamePlayer) {
        this._fightFailerPlayer = pl;
    }
    showKillTip(port: port) {
        if (this._fightWinnerPlayer != null && this._fightFailerPlayer != null) {
            this.killTip.setPos(port.x, port.y);
            this.killTip.winnerColor = this._fightWinnerPlayer.color;
            this.killTip.failerColor = this._fightFailerPlayer.color;
            this.killTip.winnerFace = this._fightWinnerPlayer.data.fPlayerInfo.faceUrl;
            this.killTip.failerFace = this._fightFailerPlayer.data.fPlayerInfo.faceUrl;
            this.killTip.visible = true;
            this.timer.once(3000, this, this.hideKillTip);
            this.resetKillTip();
        }
    }
    resetKillTip() {
        this._fightFailerPlayer = this._fightWinnerPlayer = null;
    }
    hideKillTip() {
        this.killTip.visible = false;
    }
    set skinId(val: number) {
        if (val != this.skin_id && val > 13000) {
            this.skin_id = val;
            var fevent = yalla.event.YallaEvent.instance;
            fevent.once(`Checkerboard_${val}.png`, this, () => {
                !this.destroyed && this.setSkin(this.skin_id);
            })
            fevent.once(`Base_${val}.png`, this, () => {
                !this.destroyed && this.setSkin(this.skin_id);
            })
            yalla.File.getFileByNative(`Checkerboard_${val}.png`);
            yalla.File.getFileByNative(`Base_${val}.png`);
        }
    }
    public stopAni() {
    }
    public startAni() {
    }
    private setSkin(skinid: number) {
        var [basePath, checkPath] = [yalla.File.cachePath + `/Base_${skinid}.png`, yalla.File.cachePath + `/Checkerboard_${skinid}.png`];
        if (yalla.File.existed(basePath) && yalla.File.existed(checkPath)) {
            this.check.skin = yalla.File.filePath + `Checkerboard_${skinid}.png`;
            this.bg.skin = yalla.File.filePath + `Base_${skinid}.png`;
            this.night_check.skin = yalla.File.filePath + `Checkerboard_${skinid}.png`;
            this.night_bg.skin = yalla.File.filePath + `Base_${skinid}.png`;
        }
    }
    get skinId(): number {
        return this.skin_id;
    }
    clear() {
        this.removeMask();
        this.timer.clearAll(this);
        this.timer.clearAll(this.curtain);
        this.removeEvent();
        if (this._globalEyeSk) {
            this._globalEyeSk.stop();
            this._globalEyeSk.destroy(true);
            this._globalEyeSk = null;
        }
        if (this._globalEyeTp) {
            this._globalEyeTp.offAll();
            this._globalEyeTp.destroy();
            this._globalEyeTp = null;
        }
        if (this._curtainMask) {
            this._curtainMask.clear();
            this._curtainMask.destroy(true);
            this._curtainMask = null;
        }
        this.destroy(true);
    }
    set color(color: number) {
        this._color = color;
        LudoBoardData.color = color;
        var rotation = -color * 90
        this.check.rotation = rotation;
        // this._map = MapData.getMap(this._color);
        this.visible = true;

        this.night_check.rotation = rotation;
        this.curtain.mask = this._curtainMask;

        this._curtainMask.on("reCache", this, this.maskRecache);
    }
    private removeEvent() {
        if (this._curtainMask) {
            this._curtainMask.off("reCache", this, this.maskRecache);
            this._curtainMask.off("levelChange", this, this.onLevelChange);
        }
        this._globalEyeTp && this._globalEyeTp.off(Laya.Event.COMPLETE, this, this.onGlobalSkloaded);
    }
    private maskRecache() {
        // this.curtain.reCache();
        this.timer.callLater(this.curtain, this.curtain.reCache);
    }
    private removeMaskStepping(step: number = 0) {
        var alpha = step * 0.05;
        this.night_mask.alpha = 1 - alpha;
        this._curtainMask.globle_eye.alpha = alpha;
        this.maskRecache();
        if (step >= 20) {
            this.night_mask.visible = false;
            this.curtain.mask = null;
            this.timer.clear(this, this.removeMaskStepping);
            return;
        }
        step++;
        this.timer.frameOnce(2, this, this.removeMaskStepping, [step]);
    }
    removeMask(dynamic: boolean = false) {
        if (dynamic && yalla.Global.isFouce) {
            this._curtainMask.globle_eye.visible = true;
            this.removeMaskStepping(0);
        } else {
            this.timer.clear(this, this.removeMaskStepping);
            this.night_mask.visible = false;
            this.curtain.mask = null;
        }
    }
    // getPos(val: Station, departurePos: number = 101): port {
    //     return LudoBoardData.getGridByStation(val).port;
    // }
}