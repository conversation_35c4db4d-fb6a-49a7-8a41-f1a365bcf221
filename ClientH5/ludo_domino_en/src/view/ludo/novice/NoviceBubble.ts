class NoviceBubble extends ui.ludo.novice.novice_bubbleUI {
    constructor() {
        super();
        this.visible = false;

    }
    public showTip(tx: string, width: number = 525, inLeft: boolean = true) {
        this.visible = true;
        this.left_side.visible = inLeft;
        this.right_side.visible = !inLeft;
        // this.tip.width = width;
        if (yalla.Font.lan == 'ar') {
            this.tip.width = 1000;
            this.tip.align = "right";
        } else {
            this.tip.width = 525;
            this.tip.align = "left";
        }
        this.tip.right = inLeft ? 26 : 178;
        this.tip.text = tx;
    }
    public hideTip() {
        this.visible = false;
    }
}