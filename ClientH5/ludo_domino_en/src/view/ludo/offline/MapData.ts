module offLine {
    export class MapData {//离线地图数据管理
        static get instance(): offLine.MapData {
            return !!this._instance ? this._instance : this._instance = new offLine.MapData();
        }
        static _instance: offLine.MapData;
        constructor() { }
        _map: Array<Array<any>>;
        get map(): Array<Array<any>> {
            if (!this._map) {
                this._map = [];
                for (var i = 0; i < 4; i++) {
                    var gridPosition = [];
                    for (var j = 0; j < 13; j++) {
                        var safe = false;
                        if (j == 2 || j == 10 || j > 12)
                            safe = true;
                        gridPosition.push({
                            chesses: [],
                            color: -1,
                            isSafe: safe,
                            props: -1
                        })
                    }
                    this._map.push(gridPosition);
                }
            }
            return this._map;
        }
        clearChessByColor(color: number) {
            this.map.forEach((area: Array<any>) => {
                area.forEach((gri) => {
                    if (gri.color == color) {
                        gri.chesses = [];
                        gri.color = -1
                    }
                })
            })
        }
        /**
         * 空的地图
         */
        // get emptyMapItem(): Array<Station> {
        //     var empty = [];
        //     this.map.forEach((maps, area) => {
        //         maps.forEach((item, grid) => {
        //             if (item.color == -1 && !item.isSafe && item.props == -1) {
        //                 empty.push({
        //                     area: area,
        //                     gridPosition: grid
        //                 })
        //             }
        //         })
        //     })
        //     return empty;
        // }
        /**
         * 触碰火箭后随机出目标点
         * @param chess 
         */
        rocketStation(chess: ChessData): Station {
            var arr = [];
            var isFighted: boolean = !!offLine.Player.instance.activatedPlayer.isFighted;
            for (var i = 1; i <= 8; i++) {
                var station = this.countStation(chess, i, isFighted);
                if (station.gridPosition < 0) {
                    arr.push(station); continue;
                }
                var end = this.map[station.area][station.gridPosition];
                if (end.isSafe) {
                    arr.push(station); continue;
                }
                if (end.color == -1) {
                    arr.push(station);
                }
            }
            var index: number = Math.floor(Math.random() * arr.length);
            return arr[index];
        }
        /**
         * 根据步数计算 目标点
         * @param chessdata  
         * @param step 
         * @param isFighted 
         */
        countStation(chessdata: ChessData, step: number, isFighted: boolean): Station {
            var result: Station = {
                "area": chessdata.area,
                "gridPosition": chessdata.gridPosition
            }
            if (ludoRoom.instance.type == 0) isFighted = true;
            if (chessdata.gridPosition <= 0 && chessdata.area == chessdata.color && isFighted) {//终点区的棋子
                if (Math.abs(result.gridPosition) + step > 6)
                    return null;
                result.gridPosition -= step;
            } else if (chessdata.gridPosition >= 100) {//起点区的棋子
                if (step == 6) {
                    result.gridPosition = 2;
                    result.area = chessdata.color;
                    return result;
                } else {
                    return null;
                }
            } else {
                result.gridPosition += step;
                if (result.gridPosition > 12) {//进入下一个颜色区
                    result.area++;
                    result.area = result.area % 4;
                    if (result.area == chessdata.color && isFighted) {
                        result.gridPosition = -result.gridPosition % 13;
                    } else {
                        result.gridPosition = result.gridPosition % 13;
                    }
                }
            }
            return result;
        }

        /**
         * 后方目标点之内的格子是否有叠子
         * @param data 
         * @param step 
         * @param isFighted 
        */
        isLinkAfter(data: ChessData, step: number, isFighted: boolean = false): boolean {
            for (var i = 1; i <= step; i++) {
                var next = this.createMoveItem(data, i, isFighted);
                if (this.isLinkMapItem(next, i == step))
                    return true;
            }
            return false;
        }
        /**
         * 是否叠子的格子
         * @param data 
         * @param isEnd 
         */
        isLinkMapItem(data: ChessData, isEnd: boolean): boolean {
            if (!data) {
                return false;
            } else if (data.gridPosition >= 0 && data.gridPosition < 100) {
                var mapItem = this.map[data.area][data.gridPosition];
                if (mapItem.color == -1 || mapItem.chesses.length == 1 || mapItem.isSafe) {
                    return false;
                } else {
                    if (isEnd && data.color != mapItem.color && data.chessLinkId != -1) {
                        return false;
                    }
                    return true;
                }
            } else {
                return false;
            }
        }
        /**
         * 创建一个移动的消息
         * @param chess 
         * @param step 
         * @param isFighted 
         */
        createMoveItem(chess: ChessData, step: number, isFighted: boolean): ChessData {
            var station = this.countStation(chess, step, isFighted);
            if (!station) return null;
            var result = {
                "chessId": chess.chessId,
                "color": chess.color,
                "area": station.area,
                "gridPosition": station.gridPosition,
                "isWin": chess.isWin,
                "chessSkin": chess.chessSkin,
                "isMaster": chess.isMaster,
                "num": chess.chessLinkId == -1 ? step : step * 2,
                "chessLinkId": chess.chessLinkId
            }
            return result;
        }
    }
}
