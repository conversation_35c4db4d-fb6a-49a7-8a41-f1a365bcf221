module offLine {
    export class Service extends Laya.EventDispatcher {
        throwTime: number = 820;//每次投掷骰子的间隔
        mapEv: offLine.MapEv;
        random: offLine.Random;
        title: string = null;
        constructor(gamedata?: any) {
            super();
            this.mapEv = new offLine.MapEv();
            this.mapEv.on(SERVICE_EVENT.CHESS, this, this.chessEventOnMap);//转发map中检测到的棋子格子事件
            this.mapEv.on(SERVICE_EVENT.MOVE, this, (msg) => {//转发map中检测到的棋子移动事件
                msg.idx = offLine.Player.instance.activatedId;
                this.event(SERVICE_EVENT.MOVE, [msg]);
            })
            this.gamedata = yalla.util.parse(gamedata);;
            this.random = new offLine.Random();
        }
        set gamedata(val: any) {//整理数据
            var room = ludoRoom.instance;
            var playerData;
            var players = offLine.Player.instance;
            if (!val.gamedata) {
                playerData = this.amendPlayer(val.player.player);
                players.activatedId = 1000;
                offLine.Chess.instance.chesses = this.createChessData(playerData, val.roomType);
            } else {
                players.activatedId = val.activatedId;
                playerData = val.player.player;
                offLine.Chess.instance.chesses = val.gamedata.chess;
            }
            [room.type, room.group, room.ID, room.players] = [val.roomType, val.group, val.roomId, playerData];
            players.player = playerData;
            players.activatedPlayer = players.getPlayer(players.activatedId);
            players.on(SERVICE_EVENT.PLAYER, this, this.onPlayerEvent);
            if (!!val.title) {
                this.title = val.title;
            }
        }
        get gamedata(): any {
            return {
                "isFinished": false,
                "title": this.title,
                "date": null,
                "roomId": ludoRoom.instance.ID,
                "activatedId": offLine.Player.instance.activatedId,
                "roomType": ludoRoom.instance.type,
                "group": ludoRoom.instance.group,
                "player": {
                    "player": offLine.Player.instance.player
                },
                "status": 0,
                "showRoomid": null,
                "isPublic": 1,
                "cost": 0,
                "sceneId": 1,
                "gamedata": {
                    "chess": offLine.Chess.instance.chesses
                }
            }
        }
        amendPlayer(players: Array<playerShowInfo>) {
            var playerdata: Array<playerShowInfo> = [];
            players.sort((a, b) => {
                if (a.color > b.color) {
                    return 1
                }
            })
            players.forEach((info, index) => {
                info = {
                    "fPlayerInfo": {
                        "idx": index + 1000, //玩家idx 创建时建议低于10000
                        "faceId": info.fPlayerInfo.faceId || -1,
                        "nikeName": info.fPlayerInfo.nikeName || `Player ${index + 1}`, //玩家昵称
                        "faceUrl": info.fPlayerInfo.faceUrl || "public/default_head.png", //头像
                        "placeId": info.fPlayerInfo.placeId || 0,
                        "ftype": info.fPlayerInfo.ftype || 0,
                        "country": info.fPlayerInfo.country || 0,
                        "level": info.fPlayerInfo.level || 0,
                        "viplevel": info.fPlayerInfo.viplevel || 0,
                        "winCount": info.fPlayerInfo.winCount || 1,
                        "totalCount": info.fPlayerInfo.totalCount || 2
                    },
                    "prettyId": 1,
                    "color": info.color, //玩家的颜色 0-3 红 绿 黄 蓝
                    "sitNum": info.sitNum || 1,
                    "isSignUp": info.isSignUp || 1,
                    "winIndex": info.winIndex || -1,
                    "isLeft": info.isLeft || 0, //是否离开 0否 1是
                    "isFighted": info.isFighted || 0, //大师和快速模式中记录是否发生过撞子 
                    "isInSysTrust": info.isInSysTrust || 0,
                    "reThrowNum": info.reThrowNum || 0,
                    "reThrowCost": info.reThrowCost || 2,
                    "roylevel": info.roylevel || 0,
                    'score': 0
                }
                playerdata.push(info);
            })
            return playerdata;
        }
        onPlayerEvent(msg: any) {
            this.event(SERVICE_EVENT.PLAYER, [msg]);
            switch (msg.status) {
                case PlayerTurnStatus.GAME_OVER:
                    var data = this.gamedata;
                    data.isFinished = true;
                    break;
                case PlayerTurnStatus.PLAYER_OUT:
                    if (msg.player.fPlayerInfo.idx == offLine.Player.instance.activatedId) {
                        offLine.Player.instance.activatedPlayer.isLeft = 1;
                        this.nextPlayer();
                    }
                    break;
                default:
                    break;
            }
        }
        createChessData(players: Array<playerShowInfo>, type: number): Array<ChessData> {
            var arr = [];
            for (var i = 0; i < players.length; i++) {
                var player: playerShowInfo = players[i];
                player.isFighted = 0;
                player.winIndex = -1;
                player.fPlayerInfo.faceUrl = "public/default_head.png";
                for (var j = 0; j < 4; j++) {
                    var chessdata = {
                        "chessId": (i + 1) * 100 + j + 1,
                        "color": player.color,
                        "area": player.color,
                        "gridPosition": (type == 2 && j == 0) ? 2 : 101 + j,
                        "isWin": 0,
                        "chessSkin": 1,
                        "chessLinkId": -1,
                        "isMaster": 0
                    }
                    arr.push(chessdata);
                }
            }
            return arr;
        }
        chessEventOnMap(msg: any) {
            msg.idx = offLine.Player.instance.activatedId;
            Laya.timer.once(msg.delay, this, () => {
                this.event(SERVICE_EVENT.CHESS, [msg]);
            })
            var chess: ChessData, winColor: number;
            if (msg.chessId > 0) {
                chess = offLine.Chess.instance.getChessById(msg.chessId);
                if (chess) winColor = chess.color;
            }
            switch (msg.event) {
                case chessEvent.GetLink://叠子
                    break;
                case chessEvent.Fight://战斗
                    offLine.Player.instance.status = PlayerTurnStatus.THROW_START;
                    if (ludoRoom.instance.type != 0) {
                        var player: playerShowInfo = offLine.Player.instance.getPlayerByColor(winColor);
                        player.isFighted = 1;
                    }
                    break;
                case chessEvent.Win://胜利
                    if (ludoRoom.instance.type == 2) {
                        this.random.numArray = [];
                        offLine.MapData.instance.clearChessByColor(winColor);
                        var player: playerShowInfo = offLine.Player.instance.getPlayerByColor(winColor);
                        var winIndex = offLine.Player.instance.winPlayers.length + 1;
                        player.winIndex = winIndex;
                        this.event(SERVICE_EVENT.PLAYER, {
                            status: PlayerTurnStatus.PLAYER_WIN,
                            index: winIndex,
                            idx: player.fPlayerInfo.idx
                        })
                    } else {
                        if (this.allChessWin(winColor)) {
                            Laya.timer.once(msg.delay, this, () => {
                                var player: playerShowInfo = offLine.Player.instance.getPlayerByColor(winColor);
                                var winIndex = offLine.Player.instance.winPlayers.length + 1;
                                player.winIndex = winIndex;
                                this.event(SERVICE_EVENT.PLAYER, {
                                    status: PlayerTurnStatus.PLAYER_WIN,
                                    index: winIndex,
                                    idx: player.fPlayerInfo.idx
                                })
                            })
                        } else {
                            offLine.Player.instance.status = PlayerTurnStatus.THROW_START;
                        }
                    }
                    break;
                case chessEvent.TRANSLOCATION://位移
                    break;
                case chessEvent.GET_BUFF://获得buff
                    break;
                case chessEvent.USE_BUFF://使用buff
                    break;
                case chessEvent.ReleaseLink://取消叠子
                    break;
                default:
                    break;
            }
        }
        allChessWin(color: number): boolean {
            var chesses: Array<ChessData> = offLine.Chess.instance.getChessesByColor(color);
            var num: number = 0;
            chesses.forEach((data: ChessData) => {
                if (data.isWin == 1) {
                    num++;
                }
            })
            return num == 4;
        }
        filterChesses(color: number) {
            return offLine.Chess.instance.getChessesByColor(color);
        }
        nextPlayer(delay: number = this.throwTime) {//切换玩家
            this.random.clear();
            Laya.timer.once(delay, this, () => {
                var player = offLine.Player.instance;
                player.nextPlayer();
                this.sendPlayerEvent(PlayerTurnStatus.THROW_START);
            })
        }
        /**
         * 验证摇到的点数
         * @param num 
         */
        turingNumber(num: number) {
            if (num == 6) {//继续or切换玩家
                if (this.random.isThreeSix()) {
                    this.nextPlayer();
                } else {
                    Laya.timer.once(this.throwTime, this, () => {
                        this.sendPlayerEvent(PlayerTurnStatus.THROW_START);
                    })
                }
            } else {//移动or切换玩家
                var chooseItem = this.chooseItem();
                if (chooseItem['item'].length <= 0) {
                    this.nextPlayer();
                } else if (chooseItem['item'].length == 1) {
                    this.autoMoveChess(chooseItem['item'][0]);
                } else {
                    Laya.timer.once(this.throwTime, this, () => {
                        this.sendPlayerEvent(PlayerTurnStatus.CHOOSECHESS_START, chooseItem);
                    })
                }
            }
        }
        /**
         * 自动移动棋子
         * @param item 
         */
        autoMoveChess(item) {
            var msg = this.createMoveMsg(item);
            var delay: number = msg.chooseNum * ludo.Global.moveTime;
            Laya.timer.once(this.throwTime, this, () => {
                this.event(SERVICE_EVENT.MOVE, msg);
                this.mapEv.chessMove(msg);
                this.random.removeNum(msg.chooseNum);
                Laya.timer.once(delay, this, () => {
                    if (offLine.Player.instance.status == PlayerTurnStatus.THROW_START) {
                        this.sendPlayerEvent(PlayerTurnStatus.CHOOSECHESS_END);
                        this.sendPlayerEvent(PlayerTurnStatus.THROW_START);
                    } else {
                        var chooseItem = this.chooseItem();
                        if (chooseItem['item'].length > 0) {
                            if (chooseItem['item'].length == 1) {
                                this.autoMoveChess(chooseItem['item'][0]);
                            } else {
                                Laya.timer.once(this.throwTime, this, () => {
                                    this.sendPlayerEvent(PlayerTurnStatus.CHOOSECHESS_START, chooseItem);
                                })
                            }
                        } else {
                            this.nextPlayer(0);
                        }
                    }
                })
            })
        }
        onGameClient(msg) {//视图层操作
            switch (msg.status) {
                case 0://加载完成
                    var activatedPlayer = offLine.Player.instance.activatedPlayer;
                    if (activatedPlayer.isLeft != 0 && activatedPlayer.winIndex > -1) {
                        this.nextPlayer();
                        return;
                    }
                    // if (ludoRoom.instance.group == 1) {
                    //     var props = offLine.Prop.refreshProps();
                    //     this.event(SERVICE_EVENT.FLUSH_PROP, [{
                    //         propGrid: props
                    //     }])
                    // }
                    this.sendPlayerEvent(PlayerTurnStatus.THROW_START);
                    break;
                case 1://点击骰子
                    var num: number;
                    if (ludoRoom.instance.group == 1 && offLine.Player.instance.hasProp(Props.PROP_LUCK_DICE, offLine.Player.instance.activatedPlayer.color)) {
                        num = this.random.randomNum(true);
                        this.chessEventOnMap({
                            event: chessEvent.USE_BUFF,
                            chessLinkId: -1,
                            chessId: -1,
                            idx: offLine.Player.instance.activatedId,
                            delay: delay,
                            buff: Buff.LUCK_DICE
                        })
                    } else {
                        var color = offLine.Player.instance.activatedPlayer.color;
                        var allHome = offLine.Chess.instance.isAllhome(color);
                        if (!!allHome && this.random.numArray.length == 0) {
                            num = this.random.randomNum(Math.random() > 0.5);
                        } else {
                            num = this.random.randomNum(false);
                        }
                    }
                    this.sendPlayerEvent(PlayerTurnStatus.THROW_END, { throwNum: num });
                    this.turingNumber(num);
                    break;
                case 2://移除玩家
                    offLine.Player.instance.playerLeft(msg.idx);
                    var color: number = offLine.Player.instance.getPlayer(msg.idx).color;
                    var chesses: Array<ChessData> = offLine.Chess.instance.getChessesByColor(color);
                    offLine.MapData.instance.clearChessByColor(color);
                    chesses.forEach((chess => {
                        chess.gridPosition = chess.chessId % 100 + 100;
                        chess.isMaster = 0;
                        chess.area = chess.color;
                        chess.chessLinkId = -1;
                        chess.isWin = 0;
                    }))
                    break;
                default:
                    break;
            }
            switch (msg.operate) {
                case 6://选择了移动步数 开始移动
                    msg.type = 0;
                    var delay: number = msg.chooseNum * ludo.Global.moveTime;
                    var chess: ChessData = offLine.Chess.instance.getChessById(msg.chessId);
                    if (chess.gridPosition > 100) {
                        delay = ludo.Global.moveTime;
                    }
                    this.sendPlayerEvent(PlayerTurnStatus.CHOOSECHESS_END);
                    this.event(SERVICE_EVENT.MOVE, msg);
                    this.mapEv.chessMove(msg);
                    this.random.removeNum(msg.chooseNum);

                    Laya.timer.once(delay + 100, this, (idx) => {
                        if (offLine.Player.instance.status == PlayerTurnStatus.THROW_START) {
                            this.sendPlayerEvent(PlayerTurnStatus.CHOOSECHESS_END);
                            this.sendPlayerEvent(PlayerTurnStatus.THROW_START);
                            return;
                        }
                        var chooseItem = this.chooseItem()['item'];
                        if (chooseItem.length == 1) {
                            var item = chooseItem[0];
                            var msg = this.createMoveMsg(item);
                            Laya.timer.once(delay, this, () => {
                                this.onGameClient(msg);
                            })
                        } else if (chooseItem.length > 1) {
                            Laya.timer.once(100, this, () => {
                                this.sendPlayerEvent(PlayerTurnStatus.CHOOSECHESS_START, this.chooseItem());
                            })
                        } else {
                            this.sendPlayerEvent(PlayerTurnStatus.CHOOSECHESS_END);
                            var player = offLine.Player.instance.getPlayer(offLine.Player.instance.activatedId);
                            if (player.isLeft != 1) {
                                this.nextPlayer(0);
                            } else {
                                this.random.numArray = [];
                            }
                        }
                    })
                    break;
                default:
                    break;
            }
        }
        createMoveMsg(chessdata: any) {
            return {
                area: chessdata.area,
                chessId: chessdata.chessId,
                chooseNum: chessdata.num,
                gridPosition: chessdata.gridPosition,
                idx: offLine.Player.instance.activatedId,
                operate: 6,
                roomid: ludoRoom.instance.ID,
                type: 0,
            }
        }
        sendPlayerEvent(status: PlayerTurnStatus, val?: any) {
            offLine.Player.instance.status = status;
            var msg = {
                idx: offLine.Player.instance.activatedId,
                status: status
            }
            if (!!val) {
                for (var key in val) {
                    msg[key] = val[key];
                }
            }
            this.event(SERVICE_EVENT.PLAYER, msg);
        }
        chooseItem(): Object {
            var arr = [];
            var color = offLine.Player.instance.activatedPlayer.color;

            for (var i = 0; i < this.random.numArray.length; i++) {
                var num = this.random.numArray[i];
                arr = arr.concat(this.getChessesByStep(color, num));
            }
            return { item: arr, status: 4 };
        }
        getChessesByStep(color: number, step: number): Array<any> {
            var arr = [];
            offLine.Chess.instance.getChessesByColor(color).forEach(val => {
                var step2 = step;
                if (ludoRoom.instance.isMaster) {//大师模式
                    if (val.chessLinkId != -1) {//有叠子
                        if (val.isMaster == 0 || step2 % 2 != 0) return false;
                        step2 = step / 2;
                    }
                    if (offLine.MapData.instance.isLinkAfter(val, step2, !!offLine.Player.instance.activatedPlayer.isFighted)) {//后方目标点之内的格子是否有叠子
                        return false;
                    }
                }
                var mobil = offLine.MapData.instance.createMoveItem(val, step2, !!offLine.Player.instance.activatedPlayer.isFighted);
                if (this.mapEv.canMove(mobil, step, ludoRoom.instance.type)) {
                    arr.push(mobil);
                }
            })
            return arr;
        }
        clear() {
            Laya.timer.clearAll(this);
        }

    }
}
