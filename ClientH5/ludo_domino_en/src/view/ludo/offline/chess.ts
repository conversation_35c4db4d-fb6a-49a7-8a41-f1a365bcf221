module offLine {
    export class Chess {//离线棋子管理
        static _instance: offLine.Chess = null;
        static get instance(): offLine.Chess {
            return !!this._instance ? this._instance : this._instance = new offLine.Chess();
        }
        constructor() { };
        _chesses: Array<ChessData> = [];
        chessObj: Object = {};
        set chesses(val: Array<ChessData>) {
            this._chesses = val;
            this._chesses.forEach((chess, index) => {
                if (chess.gridPosition < 100 && chess.gridPosition >= 0) {
                    var map = offLine.MapData.instance.map[chess.area][chess.gridPosition];
                    if (!map.isSafe) {
                        map.chesses.push(chess.chessId);
                        map.color = chess.color;
                    }
                }
                this.chessObj[`chess${chess.chessId}`] = index;
            })
        }
        get chesses(): Array<ChessData> {
            return this._chesses;
        }
        getChessesByColor(color: number): Array<ChessData> {
            return this.chesses.filter(val => {
                return val.color == color;
            })
        }
        isAllhome(color: number) {
            var chesses: Array<ChessData> = this.getChessesByColor(color);
            return chesses.every((ch) => {
                return ch.gridPosition > 100;
            })
        }
        getChessById(chessId: number): ChessData {
            return this._chesses[this.chessObj[`chess${chessId}`]];
        }
    }
}