module offLine {
    export class MapEv extends Laya.EventDispatcher {
        constructor() { super(); }
        /**
         * 使用道具
         * @param prop 
         * @param chess 
         * @param delay 
         */
        // onProp(prop: number, chess: ChessData, delay: number = 0) {
        //     switch (prop) {
        //         case Props.PROP_ROCKET://火箭
        //             // this.chessEvent(chessEvent.USE_BUFF, chess.chessId, 100);
        //             var station = offLine.MapData.instance.rocketStation(chess);
        //             var msg = {
        //                 type: ChessMoveType.MOVE_ROCKET,
        //                 chessId: chess.chessId,
        //                 chessLinkId: chess.chessLinkId,
        //                 idx: null,
        //                 area: station.area,
        //                 gridPosition: station.gridPosition,
        //                 operate: 6,
        //                 roomid: null
        //             }
        //             Laya.timer.once(delay, this, () => {
        //                 this.event(SERVICE_EVENT.MOVE, [msg]);
        //                 this.chessMove(msg);
        //             })
        //             break;
        //         case Props.PROP_LUCK_DICE://金骰子
        //             this.chessEvent(chessEvent.GET_BUFF, chess.chessId, 100, -1, Buff.LUCK_DICE);
        //             offLine.Player.instance.buff.push({
        //                 color: chess.color,
        //                 buff: Props.PROP_LUCK_DICE
        //             })
        //             break;
        //         default:
        //             break;
        //     }
        // }

        /**
         * 发生移动后检测事件
         * @param msg 
         */
        chessMove(msg: any) {
            var chess: ChessData = offLine.Chess.instance.getChessById(msg.chessId);
            if (chess.gridPosition < 100 && chess.gridPosition >= 0) {
                var start = offLine.MapData.instance.map[chess.area][chess.gridPosition];
                if (!start.isSafe) {
                    if (start.chesses.length > 1) {
                        start.chesses.splice(start.chesses.indexOf(chess.chessId), 1);
                        if (chess.chessLinkId != -1) {
                            start.chesses.splice(start.chesses.indexOf(chess.chessLinkId), 1);
                            if (start.chesses.length == 0) start.color = -1;
                        }
                    } else {
                        start.chesses = [];
                        start.color = -1;
                    }
                }
            }
            [chess.area, chess.gridPosition] = [msg.area, msg.gridPosition];
            var linkChess;
            if (chess.chessLinkId != -1) {
                linkChess = offLine.Chess.instance.getChessById(chess.chessLinkId);
                [linkChess.area, linkChess.gridPosition] = [msg.area, msg.gridPosition];
            }
            if (msg.gridPosition < 100 && msg.gridPosition >= 0) {//检测棋子事件
                var end = offLine.MapData.instance.map[msg.area][msg.gridPosition];
                if (!end.isSafe) {
                    if (end.color != -1 && end.color != chess.color) {//撞子
                        var reChess: ChessData = offLine.Chess.instance.getChessById(end.chesses[0]);
                        reChess.area = reChess.color;
                        reChess.gridPosition = reChess.chessId % 100 + 100;
                        var sendMsg = {
                            type: ChessMoveType.MOVE_BE_HIT,
                            chessId: reChess.chessId,
                            chessLinkId: reChess.chessLinkId,
                            idx: null,
                            area: reChess.area,
                            gridPosition: reChess.gridPosition,
                            operate: 6,
                            roomid: null
                        }
                        var moveTime = ludo.Global.moveTime;
                        Laya.timer.once(msg.chooseNum * moveTime, this, () => {
                            this.event(SERVICE_EVENT.MOVE, [sendMsg]);
                            this.chessEvent(chessEvent.Fight, msg.chessId, msg.chooseNum * moveTime, reChess.chessLinkId);
                        })
                        if (reChess.chessLinkId != -1) {
                            var reLinkChess = offLine.Chess.instance.getChessById(reChess.chessLinkId);
                            reLinkChess.chessLinkId = -1;
                            reChess.chessLinkId = -1;
                            reLinkChess.isMaster = 0;
                            reChess.isMaster = 0;
                            reLinkChess.area = reLinkChess.color;
                            reChess.area = reChess.color;
                            reLinkChess.gridPosition = reLinkChess.chessId % 100 + 100;
                            reChess.gridPosition = reChess.chessId % 100 + 100;
                        }
                        end.chesses = [];
                    }
                    end.chesses.push(chess.chessId);
                    if (chess.chessLinkId != -1) {
                        end.chesses.push(chess.chessLinkId);
                    }
                    end.color = chess.color;
                    // if (end.props != -1) {
                    //     this.onProp(end.props, chess, msg.chooseNum * ludo.Global.moveTime());
                    //     end.props = -1;
                    // }
                    if (ludoRoom.instance.isMaster) {//大师模式
                        if (end.chesses.length == 2 && chess.chessLinkId == -1) {//叠子
                            linkChess = offLine.Chess.instance.getChessById(end.chesses[0]);
                            [chess.chessLinkId, linkChess.chessLinkId] = [linkChess.chessId, chess.chessId];
                            chess['isMaster'] = 1;
                            //产生叠子
                            this.chessEvent(chessEvent.GetLink, chess.chessId, msg.chooseNum * ludo.Global.moveTime, chess.chessLinkId);
                        }
                    }
                } else {
                    if (chess.chessLinkId != -1) {
                        //取消叠子
                        this.chessEvent(chessEvent.ReleaseLink, chess.chessId, msg.chooseNum * ludo.Global.moveTime, chess.chessLinkId);
                        linkChess.chessLinkId = -1;
                        linkChess.isMaster = 0;
                        chess.chessLinkId = -1;
                        chess.isMaster = 0;
                    }
                }
            } else if (msg.gridPosition == -6) {//到终点
                msg.delay = msg.chooseNum * ludo.Global.moveTime;
                chess.isWin = 1;
                this.chessEvent(chessEvent.Win, msg.chessId, msg.chooseNum * ludo.Global.moveTime);
            } else {
                if (chess.chessLinkId != -1) {
                    // "取消叠字"
                    this.chessEvent(chessEvent.ReleaseLink, chess.chessId, msg.chooseNum * ludo.Global.moveTime, chess.chessLinkId);
                    offLine.Chess.instance.getChessById(chess.chessLinkId).chessLinkId = -1;
                    chess.chessLinkId = -1;
                    chess['isMaster'] = 0;
                }
            }
        }
        /**
         * 是否可移动
         * @param data 
         * @param stepNum 
         * @param type 
         * @param isFighted 
         */
        canMove(data: ChessData, stepNum: number, type: number = 0, isFighted: boolean = false) {
            if (!!data) {
                var end = offLine.MapData.instance.map[data.area][data.gridPosition];
                if (!end)
                    return true;
                if (end.color == -1)
                    return true;
                if (end.chesses.length < 2)
                    return true;
                if (type == 1) {
                    if (end.color != data.color) {
                        return true;
                    }
                } else {
                    if (end.color == data.color) {
                        return true;
                    }
                }
            }
            return false;
        }
        //产生棋子事件
        chessEvent(event: chessEvent, chessId: number, delay: number = 0, chessLinkId: number = -1, buff: number = -1) {
            this.event(SERVICE_EVENT.CHESS, [{
                event: event,
                chessLinkId: chessLinkId,
                chessId: chessId,
                idx: null,
                delay: delay,
                buff: buff
            }]);
        }
    }
}