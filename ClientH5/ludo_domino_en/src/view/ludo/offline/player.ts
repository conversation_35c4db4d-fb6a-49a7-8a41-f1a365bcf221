module offLine {
    export class Player extends Laya.EventDispatcher {
        _player: Array<playerShowInfo> = [];
        activatedId: number;
        activatedPlayer: playerShowInfo;
        status: PlayerTurnStatus = PlayerTurnStatus.THROW_START;
        length: number;
        round: number = 0;
        isSwitch: boolean = false;
        buff: Array<Object> = [];
        isGameOver: boolean = false;
        static _instance: offLine.Player = null;
        static get instance(): offLine.Player {
            if (!this._instance) this._instance = new offLine.Player();
            return this._instance;
        }
        constructor() { super(); }
        hasProp(prop: Props, color: number): boolean {
            for (var i = 0; i < this.buff.length; i++) {
                var val = this.buff[i];
                if (val['color'] == color && val['buff'] == prop) {
                    this.buff.splice(i, 1);
                    return true;
                }
            }
            return false;
        }
        set player(player: Array<playerShowInfo>) {//排序
            this._player = player;
            this.length = player.length;
        }
        get player(): Array<playerShowInfo> {
            return this._player;
        }
        get playPlayers(): Array<playerShowInfo> {//正在玩的玩家
            return this.player.filter(showInfo => {
                return showInfo.isLeft == 0 && showInfo.winIndex <= 0;
            })
        }
        get winPlayers(): Array<playerShowInfo> {//胜利的玩家
            return this.player.filter(showInfo => {
                return showInfo.winIndex >= 0;
            })
        }
        get noWinPlayer(): Array<playerShowInfo> {//没有胜利的玩家
            return this.player.filter(showInfo => {
                return showInfo.winIndex < 0;
            })
        }
        get leftPLayers(): Array<playerShowInfo> {//踢出的玩家
            return this.player.filter(showInfo => {
                return showInfo.isLeft == 1;
            })
        }
        nextPlayer() {//下一个玩家
            if (this.isGameOver) return;
            var playPlayers: Array<playerShowInfo> = this.playPlayers;
            if (playPlayers.length <= 1) {
                this.isGameOver = true;
                var noWinPlayer = this.noWinPlayer.sort((a, b) => {
                    if (a.isLeft == 0) {
                        return -1;
                    }
                    if (a.winIndex < b.winIndex) {
                        return 1;
                    }
                    return -1;
                })
                var players = this.winPlayers.sort((a, b) => {
                    if (a.winIndex > b.winIndex) {
                        return 1;
                    }
                    return -1;
                }).concat(noWinPlayer);
                this.event(SERVICE_EVENT.PLAYER, {
                    status: PlayerTurnStatus.GAME_OVER,
                    winMoney1: 0,
                    winMoney2: 0,
                    player: players
                })
                return;
            }
            this.round++;
            if (this.round % 10 % playPlayers.length == 0) {
                this.round += (10 - playPlayers.length);
                if (this.round % 20 == 0) {
                    this.isSwitch = true;
                    this.round = 0;
                }
            } else {
                this.isSwitch = false;
            }
            this.findNext(this.findIndex());
        }
        findNext(index: number) {//寻找下一个玩家
            index++;
            var player = this.player[index % this.length];
            if (player.winIndex <= 0 && player.isLeft == 0) {
                this.activatedPlayer = player;
                this.activatedId = this.activatedPlayer.fPlayerInfo.idx;
            } else {
                if (index > this.length * 2) {
                    // alert('没找到可切换玩家');
                    return;
                }
                this.findNext(index);
            }
        }
        findIndex(): number {//下一个玩家的索引
            for (var i = 0; i < this.player.length - 1; i++) {
                if (this.player[i].fPlayerInfo.idx == this.activatedId) {
                    return i;
                }
            }
            return -1;
        }
        getPlayer(idx: number): playerShowInfo {//通过idx获取玩家
            return this.player.filter(val => {
                return val.fPlayerInfo.idx == idx;
            })[0];
        }
        getPlayerByColor(color: number) {//通过颜色获取玩家
            for (var i = 0; i < this.player.length; i++) {
                if (this.player[i].color == color)
                    return this.player[i];
            }
            return null;
        }
        playerLeft(idx: number) {//当玩家被踢出时
            for (var i = 0; i < this.player.length; i++) {
                var player = this.player[i]
                if (player.fPlayerInfo.idx == idx && player.isLeft == 0) {
                    player.isLeft = 1;
                    player.winIndex = -(this.playPlayers.length + 1);
                    this.event(SERVICE_EVENT.PLAYER, [{
                        status: PlayerTurnStatus.PLAYER_OUT,
                        player: player,
                        idx: this.activatedId
                    }])
                    break;
                }
            }
            if (this.playPlayers.length <= 1) {
                this.nextPlayer();
            }
        }
    }
}