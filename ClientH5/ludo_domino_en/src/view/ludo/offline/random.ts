module offLine {
    export class Random {
        numArray: Array<number> = [];
        randomNum(six: boolean = false): number {
            var num: number = six ? 6 : <PERSON><PERSON>ceil(Math.random() * 6.5);
            num = num >= 6 ? 6 : num;
            // if (this.numArray.length == 0) {
            // num = 5;
            // }
            this.numArray.push(num);
            return num;
        }
        /**
         * 是否三个连续的6
         */
        isThreeSix(): boolean {
            return yalla.util.isThreeSix(this.numArray);
        }
        clear() {
            this.numArray = [];
        }
        removeNum(num: number) {
            var index = this.numArray.indexOf(num);
            if (index >= 0) {
                this.numArray.splice(index, 1);
            }
        }
    }
}