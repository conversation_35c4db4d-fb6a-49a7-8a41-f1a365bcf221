//玩家获得buff&数量显示
class BuffIcon extends ui.ludo.sub.buff_iconUI {
    private _num: number = 1;
    constructor(buff: number) {
        super();
        this.buffImg.skin = `game/buff_${buff}.png`;
        this.num = this._num;
        this.name = `buff_${buff}`;
    }
    set num(n: number) {
        this.numTx.text = `x${n}`;
        this._num = n;
    }
    get num(): number {
        return this._num;
    }
}