
class LudoBuffManager {
    static get instance(): LudoBuffManager { return this._instance ? this._instance : this._instance = new LudoBuffManager() }
    static _instance: LudoBuffManager = null;
    public tpPool = {};
    private signs = {
        ArrowDice: "ludo_ArrowDice",
        JungleDice: "ludo_JungleDice",
        JungleSnail: "ludo_JungleSnail",
        LuckDice: "ludo_LuckDice",
        JungleGrass: "ludo_JungleGrass",
        AureoleProp: "ludo_AureoleProp",
        ShieldProp: "ludo_ShieldProp",
        RocketProp: "ludo_RocketProp",
        CircleAureoleProp: "ludo_CircleAureoleProp",
        DarkCloud: "ludo_DarkCloud",
        Tornado: "ludo_Tornado",
        LockDown: "ludo_LockDown",
        Lightning: "ludo_Lightning",
        Tiger: "ludo_Tiger",
        TigerFace: "tiger_face",
        TigerBg: "tiger_bg",
        ExpLable: "exp_lable",
        StarAni: "star_ani",
        Debut: "debut_ani"
    }
    // public getTemplet(path: string, cb: Laya.Handler) {
    //     var item = this.tpPool[path];
    //     if (item && cb) cb.runWith(item);
    //     else {
    //         var tp = new Laya.Templet();
    //         tp.on(Laya.Event.COMPLETE, this, (d) => {
    //             if (d && this.tpPool) {
    //                 this.tpPool[path] = d;
    //                 cb && cb.runWith(d);
    //             }
    //         });
    //         tp.loadAni(yalla.getSkeleton(path));
    //     }
    // }
    public getExpLable(): ExpLable {
        return Laya.Pool.getItemByClass(this.signs.ExpLable, ExpLable);
    }
    public recoverExpLable(item: ExpLable) {
        item instanceof ExpLable && Laya.Pool.recover(this.signs.ExpLable, item);
    }
    public getStarAni(): StarAni {
        return Laya.Pool.getItemByClass(this.signs.StarAni, StarAni);
    }
    public recoverStarAni(item: StarAni) {
        item instanceof StarAni && Laya.Pool.recover(this.signs.StarAni, item);
    }
    /**
     * 闪电禁锢
     */
    public getDebut(): Debut {
        return Laya.Pool.getItemByClass(this.signs.Debut, Debut);
    }
    public recoverDebut(item: Debut) {
        item instanceof Debut && Laya.Pool.recover(this.signs.Debut, item);
    }
    /**
    * 虎背景
    */
    public getTigerBg(): TigerBg {
        return Laya.Pool.getItemByClass(this.signs.TigerBg, TigerBg);
    }
    public recoverTigerBg(item: TigerBg) {
        item instanceof TigerBg && Laya.Pool.recover(this.signs.TigerBg, item);
    }
    /**
     * 虎脸
     */
    public getTigerFace(): TigerFace {
        return Laya.Pool.getItemByClass(this.signs.TigerFace, TigerFace);
    }
    public recoverTigerFace(item: TigerFace) {
        item instanceof TigerFace && Laya.Pool.recover(this.signs.TigerFace, item);
    }
    /**
     * 闪电
     */
    public getLightning(): Lightning {
        return Laya.Pool.getItemByClass(this.signs.Lightning, Lightning);
    }
    public recoverLightning(item: Lightning) {
        item instanceof Lightning && Laya.Pool.recover(this.signs.Lightning, item);
    }
    /**
     * 闪电禁锢
     */
    public getLockDown(): LockDown {
        return Laya.Pool.getItemByClass(this.signs.LockDown, LockDown);
    }
    public recoverLockDown(item: LockDown) {
        item instanceof LockDown && Laya.Pool.recover(this.signs.LockDown, item);
    }
    /**
     * 龙卷风
     */
    public getTornado(): Tornado {
        return Laya.Pool.getItemByClass(this.signs.Tornado, Tornado);
    }
    public recoverTornado(item: Tornado) {
        item instanceof Tornado && Laya.Pool.recover(this.signs.Tornado, item);
    }
    /**
     * 乌云天气
     */
    public getDarkCloud(): weather.DarkCloud {
        return Laya.Pool.getItemByClass(this.signs.DarkCloud, weather.DarkCloud);
    }
    public recoverDarkCloud(item: weather.DarkCloud) {
        item instanceof weather.DarkCloud && Laya.Pool.recover(this.signs.DarkCloud, item);
    }
    /**
     * 老虎动画
     */
    public getTiger(): weather.Tiger {
        return Laya.Pool.getItemByClass(this.signs.Tiger, weather.Tiger);
    }
    public recoverTiger(item: weather.Tiger) {
        item instanceof weather.Tiger && Laya.Pool.recover(this.signs.Tiger, item);
    }
    /**
     * 火箭
     */
    public getRocketProp(): RocketProp {
        return Laya.Pool.getItemByClass(this.signs.RocketProp, RocketProp);
    }
    public recoverRocketProp(item: RocketProp) {
        item instanceof RocketProp && Laya.Pool.recover(this.signs.RocketProp, item);
    }
    /**
     * 护盾
     */
    public getShieldProp(): ShieldProp {
        return Laya.Pool.getItemByClass(this.signs.ShieldProp, ShieldProp);
    }
    public recoverShieldProp(item: ShieldProp) {
        item instanceof ShieldProp && Laya.Pool.recover(this.signs.ShieldProp, item);
    }
    /**
     * 方形光环
     */
    public getAureoleProp(): AureoleProp {
        return Laya.Pool.getItemByClass(this.signs.AureoleProp, AureoleProp);
    }
    public recoverAureoleProp(item: AureoleProp) {
        item instanceof AureoleProp && Laya.Pool.recover(this.signs.AureoleProp, item);
    }
    /**
     * 圆形光环
     */
    public getCircleAureoleProp(): CircleAureoleProp {
        return Laya.Pool.getItemByClass(this.signs.CircleAureoleProp, CircleAureoleProp);
    }
    public recoverCircleAureoleProp(item: CircleAureoleProp) {
        item instanceof CircleAureoleProp && Laya.Pool.recover(this.signs.CircleAureoleProp, item);
    }
    /**
     * 丛林骰子后面的草的动画
     */
    // public getJungleGrass(): JungleGrass {
    //     return Laya.Pool.getItemByClass(this.signs.JungleGrass, JungleGrass);
    // }
    // public recoverJungleGrass(item: JungleGrass) {
    //     item instanceof JungleGrass && Laya.Pool.recover(this.signs.JungleGrass, item);
    // }
    /**
     * 丛林骰子动画
     */
    public getJungleDice(): JungleDice {
        return Laya.Pool.getItemByClass(this.signs.JungleDice, JungleDice);
    }
    public recoverJungleDice(item: JungleDice) {
        item instanceof JungleDice && Laya.Pool.recover(this.signs.JungleDice, item);
    }
    /**
     * 丛林蜗牛动画
     */
    public getJungleSnail(): JungleSnail {
        return Laya.Pool.getItemByClass(this.signs.JungleSnail, JungleSnail);
    }
    public recoverJungleSnail(item: JungleSnail) {
        item instanceof JungleSnail && Laya.Pool.recover(this.signs.JungleSnail, item);
    }
    /**
     * 箭头骰子
     */
    public getArrowDice(): ArrowDice {
        return Laya.Pool.getItemByClass(this.signs.ArrowDice, ArrowDice);
    }
    public recoverArrowDice(item: ArrowDice) {
        item instanceof ArrowDice && Laya.Pool.recover(this.signs.ArrowDice, item);
    }
    /**
     * 道具幸运骰子
     */
    public getLuckDice(): LuckDice {
        return Laya.Pool.getItemByClass(this.signs.LuckDice, LuckDice);
    }
    public recoverLuckDice(item: LuckDice) {
        item instanceof LuckDice && Laya.Pool.recover(this.signs.LuckDice, item);
    }
    public clear() {
        for (var cls in this.signs) {
            Laya.Pool.clearBySign(this.signs[cls]);
        }
        if (this.tpPool) {
            for (var path in this.tpPool) {
                var tp = this.tpPool[path];
                tp && tp.destroy();
            }
            this.tpPool = null;
        }
        LudoBuffManager._instance = null;
    }
}