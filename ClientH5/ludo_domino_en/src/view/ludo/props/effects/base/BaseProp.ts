class BaseProp extends Laya.Box {
    public sk: Laya.Skeleton = null;
    public _loop: boolean = false;
    public _nameOrIndex: any = null;
    public _tp: Laya.Templet = null;
    constructor() {
        super();
        this.dataSource = {
            width: 46,
            height: 46,
            anchorX: 0.5,
            anchorY: 0.5
        }
        this.mouseThrough = true;
    }
    public initSk(path: string) {
        this._tp = new Laya.Templet()
        this._tp.once(Laya.Event.COMPLETE, this, this.loaded);
        this._tp.loadAni(yalla.getSkeleton(path));
    }
    public loaded(tp: Laya.Templet) {
        if (tp) {
            this._tp = tp;
            this.sk = tp.buildArmature(1);
            this.sk.pos(23, 23);
            this.addChild(this.sk);
            if (this.visible && this._nameOrIndex != null)
                this.sk.play(this._nameOrIndex, this._loop);
            this.sk.on(Laya.Event.STOPPED, this, this.onStopped);
        }
    }
    public addView(view: Laya.Node, index: number = null): BaseProp {
        if (view) index == null ? view.addChild(this) : view.addChildAt(this, index);
        return this;
    }
    public play(nameOrIndex: any = 0, loop: boolean = false) {
        // if (this._tp) this.sk = this._tp.buildArmature(1)
        this.visible = true;
        this.sk && this.sk.play(nameOrIndex, loop);
        this._nameOrIndex = nameOrIndex;
        this._loop = loop;
        return this;
    }
    public stop() {
        this.sk && this.sk.stop();
        // this.visible = false;
    }
    public onStopped() {
        this.event(Laya.Event.STOPPED);
    }
    public clear() {
        this.removeSelf();
        this.offAll();
        this.stop();
        if (this.sk) {
            this.sk.offAll();
            this.sk.destroy(true);
        }
        this.sk = null;
    }
}