

class ArrowDice extends BaseBuff {
    private view: ui.ludo.sub.arrowFlyDiceUI = null;
    private timeLine: Laya.TimeLine = new Laya.TimeLine();
    constructor() {
        super();
        this.view = new ui.ludo.sub.arrowFlyDiceUI();
        this.addChild(this.view);
        this.timeLine.on(Laya.Event.COMPLETE, this, this.onPlayEnd);
    }
    private onPlayEnd() {
        if (!this.destroyed) {
            this.clearTimeLine();
            this.removeSelf();
            LudoBuffManager.instance.recoverArrowDice(this);
        }
    }
    public fromTo(from, to, view, complete = null) {
        if (!yalla.Global.isFouce) return;
        super.initView(view, from);
        this.clearTimeLine()
        this.view.circlePic.alpha = 1;
        this.view.dicePic.alpha = 0;
        this.view.circlePic.scale(0.5, 0.5);
        this.visible = true;
        this.timeLine.addLabel("0", 0)
            .to(this.view.circlePic, { scaleX: 1.4, scaleY: 1.4, }, 200, null)
            .to(this.view.circlePic, { alpha: 0 }, 0)
        this.timeLine.addLabel("1", 600)
            .to(this.view.dicePic, { alpha: 1 }, 0)
            .to(this, { x: from.x - 20, y: from.y - 60, alpha: 1 }, 90, Laya.Ease.circOut)
            .to(this, { x: to.x, y: to.y, alpha: 0.4 }, 500, Laya.Ease.cubicIn);
        this.timeLine.play(0, false);
    }
    public clearTimeLine() {
        if (this.timeLine) {
            this.timeLine.pause();
            this.timeLine.reset();
        }
    }
    public clear() {
        this.clearTimeLine()
        if (this.timeLine) {
            this.timeLine.offAll();
            this.timeLine.destroy();
            this.timeLine = null;
        }
        this.removeSelf();
        this.destroy(true);
    }
}