class ExpLable extends Laya.Label {//蜗牛buff
    private timeLine: Laya.TimeLine = null;
    private complete: Laya.Handler = null;
    constructor() {
        super();
        this.dataSource = {
            strokeColor: "#eeeeee",
            stroke: 2,
            bold: true,
            fontSize: 26,
            width: 90,
            height: 30,
            align: "center",
            // anchorX: 0.5,
            // anchorY: 0.5
        }
    }
    public fromTo(from: port, view: Laya.Node, complete: Laya.Handler = null) {
        if (!yalla.Global.isFouce) return;
        this.complete = complete;
        this.visible = true;
        this.alpha = 1;
        this.dataSource = from;
        view.addChild(this);
        if (this.timeLine) { this.timeLine.reset(); }
        else this.timeLine = new Laya.TimeLine();
        this.timeLine.addLabel("0", 0).to(this, { y: -80 }, 600).to(this, { alpha: 0.01 }, 200);
        this.timeLine.offAll();
        this.timeLine.on(Laya.Event.COMPLETE, this, this.onComplete);
        this.timeLine.play("0");
    }
    private onComplete() {
        this.removeSelf();
        LudoBuffManager.instance.recoverExpLable(this);
        this.complete && this.complete.run();
    }
}