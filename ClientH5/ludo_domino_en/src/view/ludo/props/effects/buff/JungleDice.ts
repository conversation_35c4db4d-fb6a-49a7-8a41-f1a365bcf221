class JungleDice extends BaseBuff {//骰子buff
    private sk: Laya.Skeleton = null;
    private sk2: Laya.Skeleton = null;
    private _loop: boolean = false;
    private _nameOrIndex: any = 0;
    private timeLine: Laya.TimeLine = null;
    private complete: Laya.Handler = null;
    private _tp: Laya.Templet = null;
    private _tp2: Laya.Templet = null;
    constructor() {
        super();
        this.init();
    }
    private init() {
        // LudoBuffManager.instance.getTemplet("props/dice/shaizi", Laya.Handler.create(this, this.loaded));
        // LudoBuffManager.instance.getTemplet("props/dice/shaizi2", Laya.Handler.create(this, this.loaded2));
        this._tp = new Laya.Templet()
        this._tp.on(Laya.Event.COMPLETE, this, this.loaded);
        this._tp.loadAni(yalla.getSkeleton("props/dice/shaizi"));

        this._tp2 = new Laya.Templet()
        this._tp2.once(Laya.Event.COMPLETE, this, this.loaded2);
        this._tp2.loadAni(yalla.getSkeleton("props/dice/shaizi2"));

    }
    private loaded(tp: Laya.Templet) {
        if (tp) {
            this.sk = tp.buildArmature(0);
            this.addChild(this.sk);
            if (this.visible) this.sk.play(this._nameOrIndex, this._loop);
        }
    }
    private loaded2(tp: Laya.Templet) {
        if (tp) {
            this.sk2 = tp.buildArmature(0);
            this.addChild(this.sk2);
            if (this.visible) this.sk2.play(this._nameOrIndex, this._loop);
        }
    }
    public play(nameOrIndex, loop) {
        this.sk && this.sk.play(nameOrIndex, loop);
        this.sk2 && this.sk2.play(nameOrIndex, loop);
        this._nameOrIndex = nameOrIndex;
        this._loop = loop;
    }
    public stop() {
        this.sk && this.sk.stop();
        this.visible = false;
    }
    public fromTo(from: port, to: port, view: Laya.Node, complete: Laya.Handler = null) {
        if (!yalla.Global.isFouce) return;
        this.complete = complete;
        this.visible = true;
        from.x += 32;
        to.x += 90;
        to.y += 50
        super.initView(view, from);
        this.play(0, false);
        if (this.timeLine) { this.timeLine.reset(); }
        else this.timeLine = new Laya.TimeLine()
        this.timeLine.addLabel("0", 0).to(this, { scaleX: 0.5, scaleY: 0.5 }, 500);
        this.timeLine.addLabel("1", 0).to(this, to, 500);
        this.timeLine.offAll();
        this.timeLine.on(Laya.Event.COMPLETE, this, this.onComplete);
        this.timeLine.play(0)
    }
    private onComplete() {
        this.stop();
        this.removeSelf();
        LudoBuffManager.instance.recoverJungleDice(this);
        this.complete && this.complete.run();
    }
}