class JungleSnail extends BaseBuff {//蜗牛buff
    constructor() {
        super();
        this.init();
    }
    private sk: Laya.Skeleton = null;
    private _nameOrIndex: any = 0;
    private _loop: boolean = false;
    private timeLine: Laya.TimeLine = null;
    private complete: Laya.Handler = null;
    private _tp: Laya.Templet = null;
    private init() {
        // LudoBuffManager.instance.getTemplet("props/snail/woniu", Laya.Handler.create(this, this.loaded));
        this._tp = new Laya.Templet()
        this._tp.on(Laya.Event.COMPLETE, this, this.loaded);
        this._tp.loadAni(yalla.getSkeleton("props/snail/woniu"));
    }
    private loaded(tp: Laya.Templet) {
        if (tp) {
            this.sk = tp.buildArmature(0);
            this.sk.playbackRate(1.7);
            this.addChild(this.sk);
            if (this.visible) this.sk.play(this._nameOrIndex, this._loop);
        }
    }
    public play(nameOrIndex, loop) {
        this.sk && this.sk.play(nameOrIndex, loop);
        this._nameOrIndex = nameOrIndex;
        this._loop = loop;
    }
    public stop() {
        this.sk && this.sk.stop();
        this.visible = false;
    }
    public fromTo(from: port, to: port, view: Laya.Node, complete: Laya.Handler = null) {
        if (!yalla.Global.isFouce) return;
        this.complete = complete;
        this.visible = true;
        from.x += 32;
        to.x += 90;
        to.y += 50
        super.initView(view, from);
        this.play(0, false);
        if (this.timeLine) { this.timeLine.reset(); }
        else this.timeLine = new Laya.TimeLine()
        this.timeLine.addLabel("0", 0).to(this, { scaleX: 0.5, scaleY: 0.5 }, 500);
        this.timeLine.addLabel("1", 0).to(this, to, 500);
        this.timeLine.offAll();
        this.timeLine.on(Laya.Event.COMPLETE, this, this.onComplete);
        this.timeLine.play(0)
    }
    private onComplete() {
        this.stop();
        this.removeSelf();
        LudoBuffManager.instance.recoverJungleSnail(this);
        this.complete && this.complete.run();
    }
}