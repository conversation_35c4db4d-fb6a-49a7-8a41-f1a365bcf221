class LuckDice extends BaseBuff {
    private sk: Laya.Skeleton = null;
    private _loop: boolean = false;
    private _nameOrIndex: any = 0;
    private _tp: Laya.Templet = null;
    constructor() {
        super();
        this.init();
    }
    private init() {
        // LudoBuffManager.instance.getTemplet("props/gold_dice/skeleton", Laya.Handler.create(this, this.loaded))
        this._tp = new Laya.Templet()
        this._tp.on(Laya.Event.COMPLETE, this, this.loaded);
        this._tp.loadAni(yalla.getSkeleton("props/gold_dice/skeleton"));
    }
    private loaded(tp: Laya.Templet) {
        if (tp) {
            this.sk = tp.buildArmature(0);
            this.addChild(this.sk);
            if (this.visible) this.sk.play(this._nameOrIndex, this._loop);
            else this.sk.stop();
        }
    }
    public play(nameOrIndex, loop) {
        this.sk && this.sk.play(nameOrIndex, loop);
        this._nameOrIndex = nameOrIndex;
        this._loop = loop;
    }
    public stop() {
        this.sk && this.sk.stop();
        this.visible = false;
    }
    public fromTo(from: port, to: port, view: Laya.Node, complete: Laya.Handler = null) {
        if (!yalla.Global.isFouce) return;
        this.visible = true;
        super.initView(view, from);
        this.play(1, true);
        Laya.Tween.to(this, to, 1000, Laya.Ease.linearIn, Laya.Handler.create(this, () => {
            this.stop();
            this.removeSelf();
            LudoBuffManager.instance.recoverLuckDice(this);
            complete && complete.run();
        }))
    }
}