class StarAni extends Laya.Image {//蜗牛buff
    private timeLine: Laya.TimeLine = null;
    private complete: Laya.Handler = null;
    constructor() {
        super();
        this.skin = "game/Stars.png";
        this.anchorX = this.anchorY = 0.5;
    }
    public fromTo(from: port, to: port, index: number, view: Laya.Node, complete: Laya.Handler = null, speedTimes: number = 1, flyUpTime = 120) {
        if (!yalla.Global.isFouce) return;
        this.complete = complete;
        this.visible = true;
        this.alpha = 0;
        from.x += 15;
        to.x += 50;
        to.y += 50
        this.dataSource = from;
        view.addChild(this);
        if (this.timeLine) {
            this.timeLine.offAll();
            this.timeLine.destroy();
            this.timeLine = null;
        }
        this.timeLine = new Laya.TimeLine();
        let offset = 80 + index * 160;
        this.timeLine.addLabel("start", 0)
            .to(this, { alpha: 1 }, 0, null, offset)
            .to(this, { y: from.y - 50 }, flyUpTime)
            .to(this, to, 1000 * speedTimes)
        // this.timeLine.offAll();
        this.timeLine.on(Laya.Event.COMPLETE, this, this.onComplete);
        this.timeLine.play("start", false);
    }
    private onComplete() {
        this.removeSelf();
        this.complete && this.complete.run();
        LudoBuffManager.instance.recoverStarAni(this);
    }
}