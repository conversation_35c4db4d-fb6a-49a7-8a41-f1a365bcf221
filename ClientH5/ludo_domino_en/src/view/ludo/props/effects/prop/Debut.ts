class Debut extends BaseProp {//首次道具出现的动画
    private _jungleGrid: JungleGrid = null;
    constructor() {
        super();
        this._nameOrIndex = 0;
        this.initSk("props/debut/sjcx");
        this.on(Laya.Event.STOPPED, this, this.recover);
    }
    public recover() {
        this.stop();
        this.removeSelf();
        LudoBuffManager.instance.recoverDebut(this);
    }
    public set jungleGrid(jungleGrid: JungleGrid) {
        this._jungleGrid = jungleGrid;
        var port = LudoBoardData.getGridByStation(jungleGrid).port;
        port && this.pos(port.x, port.y);
    }
    public get jungleGrid(): JungleGrid {
        return this._jungleGrid;
    }
}