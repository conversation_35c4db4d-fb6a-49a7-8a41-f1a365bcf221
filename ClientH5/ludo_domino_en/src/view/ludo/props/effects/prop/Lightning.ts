class Lightning extends BaseProp {//雷电
    private _jungleGrid: JungleGrid = null;
    private _type: number = -1;
    private _status: number = null;
    public mode: string = 'ani';
    private sk2: Laya.Skeleton = null;
    private _nameOrIndex2: any = null;
    private _loop2: boolean = false;
    private _tp2: Laya.Templet = null;

    constructor() {
        super();
        this.anchorX = this.anchorY = 0.5;
        this.initSk("props/lightning/shandianyujing");
        // LudoBuffManager.instance.getTemplet("props/lightning/leidian", Laya.Handler.create(this, this.loaded2));
        this._tp2 = new Laya.Templet()
        this._tp2.once(Laya.Event.COMPLETE, this, this.loaded2);
        this._tp2.loadAni(yalla.getSkeleton("props/lightning/leidian"));
    }
    public loaded(tp: Laya.Templet) {
        if (tp) {
            this.sk = tp.buildArmature(0);
            this.sk.pos(23, 23);
            this.addChild(this.sk);
            if (this.visible && this._status == 0) this.sk.play(this._nameOrIndex, this._loop);
        }
    }
    private loaded2(tp: Laya.Templet) {
        if (tp) {
            this.sk2 = tp.buildArmature(0);
            this.sk2.pos(23, 23);
            this.addChild(this.sk2);
            if (this.visible && this._status == 1) this.sk2.play(this._nameOrIndex2, this._loop2);
        }
    }
    public stop() {
        this.sk && this.sk.stop();
        this.sk2 && this.sk2.stop();
        this.visible = false;
    }
    public recover() {
        this.stop();
        this._status = null;
        this.removeSelf();
        LudoBuffManager.instance.recoverLightning(this);
    }
    public set jungleState(val: JungleStateEvent) {
        this.type = val.jungleEvent;
        this.status = val.eventStatus;
    }
    public set status(status: number) {
        if (this._status === status) return;
        this._status = status;
        if (status == 1) this.play2(0, false);
        else if (status == 0) this.play(0, true);
    }
    public get status(): number {
        return this._status;
    }
    public play(nameOrIndex: any = 0, loop: boolean = false) {
        this.visible = true;
        if (this.sk2) this.sk2.visible = false;
        if (this.sk) this.sk.visible = true;
        super.play(nameOrIndex, loop);
        return this;
    }
    public play2(nameOrIndex: any = 0, loop: boolean = false) {
        if (this.sk) this.sk.visible = false;
        if (this.sk2) {
            this.sk2.visible = true;
            this.sk2.play(nameOrIndex, loop);
        }
        this.visible = true;
        this._nameOrIndex2 = nameOrIndex;
        this._loop2 = loop;
        return this;
    }
    public get type() {
        return this._type;
    }
    public set type(val: number) {
        if (this._type != val) {
            this._type = val;
        }
    }
    public set jungleGrid(jungleGrid: JungleGrid) {
        this._jungleGrid = jungleGrid;
        var port = LudoBoardData.getGridByStation(jungleGrid).port;
        port && this.pos(port.x, port.y);
    }
    public get jungleGrid(): JungleGrid {
        return this._jungleGrid;
    }
}