class ShieldProp extends BaseProp {//护盾prop
    private _jungleGrid: JungleGrid = null;
    private _type: number = -1;
    private _status: number = null;
    public mode: string = 'ani';
    constructor() {
        super();
        this.initSk("props/shield/dunpai");
        this.scale(0.8, 0.8);
    }
    public recover(removeFirst: boolean = false) {
        if (this._status == 1 || removeFirst) {
            this._status = null;
            this.removeSelf();
            LudoBuffManager.instance.recoverShieldProp(this);
        } else {
            this.status = 1;
        }
    }
    public reset() {
        if (this.sk) {
            this.sk.off(Laya.Event.STOPPED, this, this.onStopped);
            this.sk.stop();
            this.sk.on(Laya.Event.STOPPED, this, this.onStopped);
            this._status = null;
            this._nameOrIndex = null;
        }
    }
    public set jungleState(val: JungleStateEvent) {
        // if (val.eventStatus == 0) this.reset();
        this.type = val.jungleEvent;
        this.status = val.eventStatus;
    }
    public set status(status: number) {
        // if (this._status == status) return;
        // this.stop()
        this.reset();
        if (status == 0 || status == 1) this.play(status, false);
        this._status = status;
    }
    public get status(): number {
        return this._status;
    }
    public onStopped() {
        if (this._status == 1) this.recover();
    }
    public get type() {
        return this._type;
    }
    public set type(val: number) {
        if (this._type != val) {
            this._type = val;
        }
    }
    public set jungleGrid(jungleGrid: JungleGrid) {
        this._jungleGrid = jungleGrid;
        var port = LudoBoardData.getGridByStation(jungleGrid).port;
        port && this.pos(port.x, port.y);
    }
    public get jungleGrid(): JungleGrid {
        return this._jungleGrid;
    }
}