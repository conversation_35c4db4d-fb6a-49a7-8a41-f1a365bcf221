// class TigerClaw extends Laya.Animation {
//     constructor() {
//         super();
//         // this.anchorX = this.anchorY = 0.5;
//         // this.skin = "game/jungleEvent_claw.png";
//         this.source = "ludo/jungleMode/tigerClaw.ani";
//     }
// }
class TigerClaw extends BaseProp {
    constructor() {
        super();
        this.scale(0.6, 0.6);
        this.initSk("props/tiger/zongdian");
        // this.on(Laya.Event.STOPPED, this, this.recover);
    }
    public recover() {
        this.stop();
        this.removeSelf();
        // LudoBuffManager.instance.recoverLockDown(this);
    }
}