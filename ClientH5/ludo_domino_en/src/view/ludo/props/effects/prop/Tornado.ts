class Tornado extends BaseProp {//龙卷风
    constructor() {
        super();
        this.initSk("props/tornado/xuanfeng");
        // this.on(Laya.Event.STOPPED, this, this.recover, [true]);
    }
    public loaded(tp: Laya.Templet) {
        super.loaded(tp);
        this.sk && this.sk.playbackRate(1.5);
    }
    public recover(onStop: boolean = false) {
        if (onStop) {
            this.removeSelf();
            LudoBuffManager.instance.recoverTornado(this);
        } else {
            this.stop();
        }
    }
}