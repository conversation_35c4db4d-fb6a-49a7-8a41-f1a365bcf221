module weather {
    //老虎出现动画
    export class Tiger extends BaseProp {
        public isPlaying: boolean = false;
        constructor() {
            super();
            this.initSk("props/tiger/laohu");
            this.pos(368, 368);
        }

        public show() {
            this.play(0, false);
            this.isPlaying = true;
        }
        public loaded(tp: Laya.<PERSON>t) {
            super.loaded(tp);
            this.sk && this.sk.playbackRate(2.8);
        }
        public hide() {
            this.stop();
        }
        public onStopped() {
            this.isPlaying = false;
            this.event(Laya.Event.STOPPED);
        }
    }
}