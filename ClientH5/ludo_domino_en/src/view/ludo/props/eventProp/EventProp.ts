class EventProp extends ui.ludo.eventProp.eventPropUI {
    constructor() {
        super();
        this.propSrc = ludoRoom.instance.activityPropUrl;
    }
    private _station: Station = null;
    // private _tw: Laya.Tween = null;
    private _timeLine: Laya.TimeLine = null;
    // public set num(val: number) {
    //     this._num.text = `+${val}`;
    // }
    public set propGrid(val: any) {
        this.station = val;
    }
    public set propSrc(url: string) {
        url && yalla.File.downloadImgByUrl(url, (localPath: string) => {
            //groupLoad会在返回大厅的时候清理纹理资源 对象不销毁的情况下 纹理显示出错，对象必须要清理掉
            Laya.loader.load(localPath, Laya.Handler.create(this, (e) => {
                if (e && this.prop_icon) {
                    this.prop_icon.skin = localPath;
                }
            }));
        })
    }
    public show() {
        // this._tw && this._tw.clear();
        this._timeLine && this._timeLine.pause();
        this.prop_icon.dataSource = { y: 0, alpha: 1 };
    }
    public hide(animation: boolean) {
        if (animation && yalla.Global.isFouce) {
            this.prop_icon.dataSource = { y: 0, alpha: 1 };
            // this._tw = Laya.Tween.to(this.prop_icon, { y: -90, alpha: 0 }, 500, null, Laya.Handler.create(this, this.recover));
            if (!this._timeLine) {
                this._timeLine = new Laya.TimeLine();
                this._timeLine.on(Laya.Event.COMPLETE, this, this.recover);
                this._timeLine.addLabel("1", 0)
                    .to(this.prop_icon, { y: -40, }, 300)
                    .to(this.prop_icon, { y: -60, alpha: 0.5 }, 300)
            }
            this._timeLine.play("1", false);
        } else {
            this.recover();
        }
    }
    public recover() {
        // this._tw && this._tw.clear();
        this.removeSelf();
        EventPropManager.instance.recover(this);
    }
    public set station(station: Station) {
        this._station = station;
        var port = LudoBoardData.getGridByStation(station).port;
        port && this.pos(port.x, port.y);
    }
    public get station(): Station {
        return this._station;
    }
}