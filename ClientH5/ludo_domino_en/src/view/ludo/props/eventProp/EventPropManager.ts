class EventPropManager {
    constructor() { }
    static _instanse: EventPropManager = null;
    static get instance() { return this._instanse || (this._instanse = new EventPropManager()) }
    public getEventProp(): EventProp {
        return Laya.Pool.getItemByClass("ludo_eventProp", EventProp);
    }
    public recover(eventProp: EventProp) {
        Laya.Pool.recover("ludo_eventProp", eventProp)
    }
    public clearPool() {
        Laya.Pool.clearBySign("ludo_eventProp");
    }
}