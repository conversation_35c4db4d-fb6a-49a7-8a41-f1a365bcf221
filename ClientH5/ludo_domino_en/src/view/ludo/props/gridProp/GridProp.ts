class GridProp extends Laya.Image {//地图上显示的道具图标
    private _station: Station = null;
    private _type: number = -1;
    private _status: number = 0;
    private aureole: AureoleProp = null;
    constructor() {
        super();
        this.anchorX = this.anchorY = 0.5;
    }
    public set propGrid(val: any) {
        this.type = val.prop;
        this.station = val;
        this._status = val.propStatus;
    }
    public get type() {
        return this._type;
    }
    public set type(val: number) {
        if (this._type != val) {
            this._type = val;
            this.skin = `game/prop_${this._type}.png`;
        }
    }
    public set station(station: Station) {
        this._station = station;
        var port = LudoBoardData.getGridByStation(station).port;
        port && this.pos(port.x, port.y);
    }
    public get station(): Station {
        return this._station;
    }
}