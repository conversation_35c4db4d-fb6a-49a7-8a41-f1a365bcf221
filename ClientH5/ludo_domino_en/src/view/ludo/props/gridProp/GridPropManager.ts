class GridPropManager {//道具icon管理器
    constructor() { }
    static _instace: GridPropManager = null;
    static get instance(): GridPropManager {
        return this._instace || (this._instace = new GridPropManager())
    }
    public getGridProp(): GridProp {
        return Laya.Pool.getItemByClass("ludo_prop", GridProp);
    }
    public recover(prop: GridProp) {
        prop instanceof GridProp && Laya.Pool.recover("ludo_prop", prop)
    }
    public clear() {
        Laya.Pool.clearBySign("ludo_prop");
    }
}