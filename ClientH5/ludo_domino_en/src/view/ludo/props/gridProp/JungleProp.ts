class JungleProp extends Laya.Image {//地图上显示的道具图标
    private _jungleGrid: JungleGrid = null;
    private _type: number = null;
    private _status: number = null;
    private _windDirection: WindDirection = null;
    public mode: string = 'icon';
    // private lb: Laya.Label = null;
    constructor() {
        super();
        this.anchorX = this.anchorY = 0.5;
        // this.lb = new Laya.Label();
        // this.lb.fontSize = 20;
        // this.addChild(this.lb)
    }
    public set jungleState(val: JungleStateEvent) {
        this.type = val.jungleEvent;
        this._status = val.eventStatus;
        this.setDirection(val.jungleEvent == JungleEvent.JUNGLE_WIND_DIRECTION ? val.windDirection : null);
    }
    public setDirection(val: WindDirection) {
        switch (val) {
            case WindDirection.BACKWARD_WIND:
                // this.lb.text = "后:"
                this.rotation = LudoBoardData.getWindDirection(this.jungleGrid, true) * 90;
                break;
            case WindDirection.FORWARD_WIND:
                this.rotation = LudoBoardData.getWindDirection(this.jungleGrid, false) * 90
                // this.lb.text = "前:";
                break;
            default:
                // this.lb.text = "";
                this.rotation = 0;
                break;
        }
    }
    public get type() {
        return this._type;
    }
    public set type(val: number) {
        // if (this._type != val) {
            this._type = val;
            this.skin = `game/jungleEvent_${this._type}.png`;
        // }
    }
    public set jungleGrid(jungleGrid: JungleGrid) {
        this._jungleGrid = jungleGrid;
        var port = LudoBoardData.getGridByStation(jungleGrid).port;
        port && this.pos(port.x, port.y);
    }
    public get jungleGrid(): JungleGrid {
        return this._jungleGrid;
    }
    public recover() {
        this.offAll();
        this.removeSelf();
        Laya.Pool.recover("ludo_jungle_event", this);
    }
}