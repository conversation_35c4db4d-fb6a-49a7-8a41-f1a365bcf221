class JunglePropManager {//道具icon管理器
    public oldPool: any = null;
    public newPool: any = null;
    public iconPool: any = null;
    public tornadoPool: any = {};
    private mapView: Map = null;
    private tiger: weather.Tiger = null;
    private darkCloud: weather.DarkCloud = null;
    private clouding: boolean = false;
    private tigerClaw: TigerClaw = null;
    private lightningSound: boolean = false;//闪电劈下音效开关
    public isDebut: boolean = true;//第一次进游戏动画
    private msgindex: number = -1;
    constructor(mapView: Map) {
        this.mapView = mapView;
    }
    public showTiger() {
        if (this.mapView) {
            if (!this.tiger) {
                this.tiger = LudoBuffManager.instance.getTiger();
                this.mapView.addChild(this.tiger);
            };
            // this.mapView.ani_box.setChildIndex(this.tiger, this.mapView.ani_box.numChildren - 1)
            if (!this.tiger.isPlaying) {
                this.tiger.show();
                yalla.Sound.playSound("Tiger_Roar");
            }
        }
    }
    public showTigerClaw(port: port) {
        if (yalla.Global.isFouce && this.mapView) {
            this.tigerClaw = this.tigerClaw || new TigerClaw();
            this.mapView.ani_box.addChildAt(this.tigerClaw, this.mapView.ani_box.numChildren - 1);
            this.tigerClaw.pos(port.x, port.y);
            this.tigerClaw.play(0, true);
        }
    }
    public hideTigerClaw() {
        if (this.tigerClaw) this.tigerClaw.removeSelf();
    }
    public hideTiger() {
        this.tiger && this.tiger.hide();
    }
    public showDarkCloud() {
        if (this.clouding) return;
        if (this.mapView) {
            this.clouding = true;
            if (!this.darkCloud) {
                this.darkCloud = LudoBuffManager.instance.getDarkCloud();
                this.mapView.ani_box.addChild(this.darkCloud);
            };
            this.darkCloud.visible = true;
            this.darkCloud.show();
        }
    }
    public hideDarkCloud() {
        if (!this.clouding) return;
        this.clouding = false;
        if (this.darkCloud) {
            this.darkCloud.stop();
            this.darkCloud.visible = false
        }
    }

    public showTornado(grid: LudoGrid) {
        var name = grid ? grid.gridName : "";
        if (!this.tornadoPool[name]) {
            var tornado = LudoBuffManager.instance.getTornado();
            this.tornadoPool[name] = tornado;
            tornado.off(Laya.Event.STOPPED, this, this.recorTornado);
            tornado.once(Laya.Event.STOPPED, this, this.recorTornado, [name]);
            var port = grid.port;
            tornado.pos(port.x, port.y);
            this.mapView.ani_box.addChild(tornado);
            tornado.play(0, false);
        }
        // console.log(this.tornadoPool);
    }
    private recorTornado(name: string) {
        if (name) {
            var tornado: Tornado = this.tornadoPool[name];
            tornado.recover(true);
            delete this.tornadoPool[name];
        }
    }
    public catchSameProp(gridName: string) {
        let result = [];
        if (this.oldPool) {
            for (let name in this.oldPool) {
                let prop = this.oldPool[name];
                if (prop) {
                    let jGridName = LudoBoardData.getGridName(prop.jungleGrid);
                    if (jGridName == gridName)
                        result.push(prop);
                }
            }
        }
        if (this.iconPool) {
            for (let name in this.iconPool) {
                let prop = this.iconPool[name];
                if (prop) {
                    let jGridName = LudoBoardData.getGridName(prop.jungleGrid);
                    if (jGridName == gridName) {
                        result.push(prop);
                    }
                }
            }
        }
        return result;
    }
    public eachAll(cb: Function) {
        if (this.oldPool && cb) {
            for (let name in this.oldPool) {
                let prop = this.oldPool[name];
                if (prop) cb(prop)
            }
        }
        if (this.iconPool) {
            for (let name in this.iconPool) {
                let prop = this.iconPool[name];
                if (prop) cb(prop)
            }
        }
    }
    private deletePool(name: string) {
        if (this.oldPool) this.oldPool[name] = null;
    }
    private addShieldProp(name: string, jungleGrid: JungleGrid, jungleState: JungleStateEvent) {//添加盾牌道具
        // console.log("护盾刷新", jungleGrid, jungleState);
        var prop: ShieldProp = this.getPropByName(name),
            status = jungleState.eventStatus;
        // console.log("addShieldProp", name, prop ? "oldpool" + prop.status : "cache", jungleState, this.oldPool);
        if (prop) {
            if (status == 1) {
                if (prop.status == 1) return;
                this.deletePool(name);
                this.updateState(prop, jungleGrid, jungleState); return;
            }
            this.deletePool(name);
            if (prop.status == 0) {
                prop.name = name;
                this.newPool[name] = prop;
                return;
            }
            prop.recover();
        }
        if (status == 1) return;
        prop = LudoBuffManager.instance.getShieldProp();
        this.updateState(prop, jungleGrid, jungleState);
        if (!this.mapView.chess_box.contains(prop)) {
            this.mapView.chess_box.addChild(prop);
        }
        prop.name = name;
        this.newPool[name] = prop;

        // var prop: ShieldProp = this.getPropByName(name);
        // console.log("addShieldProp", name, prop ? "oldpool" + prop.status : "cache", jungleState, this.oldPool);
        // if (prop && prop.status == jungleState.eventStatus) {//相同的不重复执行
        //     prop.name = name;
        //     this.newPool[name] = prop;
        // } else {
        //     if (prop && prop.status == null) {
        //         console.log("有问题的护盾", prop.name, jungleState.eventStatus, this.mapView.chess_box.contains(prop));
        //     }
        //     if (!prop) {
        //         prop = LudoBuffManager.instance.getShieldProp();
        //         console.log("cache" + prop.status);
        //     }
        //     // prop.reset();
        //     this.updateState(prop, jungleGrid, jungleState);
        //     if (!this.mapView.chess_box.contains(prop)) {
        //         this.mapView.chess_box.addChild(prop);
        //     }
        //     prop.name = name;
        //     this.newPool[name] = prop;
        // }
    }
    private addLightningProp(name: string, jungleGrid: JungleGrid, jungleState: JungleStateEvent) {
        var prop: Lightning = this.getPropByName(name);
        if (!prop) {
            prop = LudoBuffManager.instance.getLightning();
        } else if (this.oldPool) {
            this.oldPool[name] = null;
        }
        if (this.lightningSound && jungleState.eventStatus == 1) {
            yalla.Sound.playSound("Lighting_triger");
            this.lightningSound = false;
        }
        !this.mapView.chess_box.contains(prop) && this.mapView.chess_box.addChild(prop);
        this.updateState(prop, jungleGrid, jungleState);
        prop.name = name;
        this.newPool[name] = prop;
    }
    private addIconProp(name: string, jungleGrid: JungleGrid, jungleState: JungleStateEvent) {//添加静态道具
        var prop = Laya.Pool.getItemByClass("ludo_jungle_event", JungleProp);
        this.mapView.props_box.addChild(prop);
        prop.name = name;
        this.updateState(prop, jungleGrid, jungleState);
        this.iconPool[name] = prop;
        this.isDebut && this.showDebut(jungleGrid, jungleState);
    }
    private showDebut(jungleGrid: JungleGrid, jungleState: JungleStateEvent) {
        var debut: Debut = LudoBuffManager.instance.getDebut();
        this.updateState(debut, jungleGrid, jungleState);
        this.mapView.props_box.addChild(debut);
    }
    private getPropByName(name: string) {
        // if (this.oldPool && this.oldPool[name]) {
        //     var prop = this.oldPool[name];
        //     this.oldPool[name] = null;
        //     return prop;
        // }
        return this.oldPool ? this.oldPool[name] : null;

    }
    private updateState(prop: any, jungleGrid, jungleState) {
        if (prop) {
            prop.jungleGrid = jungleGrid;
            prop.jungleState = jungleState;
        }
    }
    public refresh(msg: any, mapView: Map) {
        var msgindex = msg.msgindex;
        if (msgindex) {
            if (msgindex <= this.msgindex) return;
            this.msgindex = msgindex;
        }
        var jungleGrids: JungleGrid[] = msg.propGrid;
        this.newPool = {};
        this.lightningSound = true;
        this.recoverAllIcon();
        this.iconPool = {};
        if (jungleGrids instanceof Array && mapView) {
            jungleGrids.forEach((jungleGrid: JungleGrid) => {
                jungleGrid.jungleEvent.forEach((jungleState: JungleStateEvent) => {
                    var name: string = `${jungleGrid.area}_${jungleGrid.gridPosition}_${jungleState.jungleEvent}`;
                    switch (jungleState.jungleEvent) {
                        case JungleEvent.JUNGLE_GRID_SHIELD:
                            this.addShieldProp(name, jungleGrid, jungleState);
                            jungleState.eventStatus == 0 && this.addIconProp(name, jungleGrid, jungleState);
                            break;
                        case JungleEvent.JUNGLE_LIGHTNING:
                            this.addLightningProp(name, jungleGrid, jungleState);
                            jungleState.eventStatus == 0 ? this.showDarkCloud() : this.hideDarkCloud();
                            break;
                        case JungleEvent.JUNGLE_TIGER:
                        case JungleEvent.JUNGLE_EXTRA_DICE:
                        case JungleEvent.JUNGLE_SNAIL:
                        case JungleEvent.JUNGLE_WIND_DIRECTION:
                            this.addIconProp(name, jungleGrid, jungleState)
                            break;
                    }
                });
            })
            this.recoverAllAni();
            this.oldPool = this.newPool;
        }
        this.isDebut && (this.isDebut = false);

    }
    // public recoverAllPropByName(name: string) {
    //     this.recoverAniByName(name);
    //     this.recoverIconByName(name);
    // }
    public recoverAniByName(name: string) {
        var prop = this.oldPool ? this.oldPool[name] : null;
        if (prop) {
            prop.recover();
            this.oldPool[name] = null;
        }
    }
    public recoverIconByName(name: string) {
        var prop = this.iconPool ? this.iconPool[name] : null;
        if (prop) {
            prop.recover();
            this.iconPool[name] = null;
        }
    }
    public recoverAllIcon() {
        if (this.iconPool) {
            for (var name in this.iconPool) {
                var prop = this.iconPool[name];
                prop && prop.recover();
            }
        }
        this.iconPool = null;
    }

    public recoverAllAni() {
        if (this.oldPool) {
            for (var name in this.oldPool) {
                var prop = this.oldPool[name];
                prop && prop.recover(true);
            }
        }
        this.oldPool = null;
    }
    public clear() {
        this.msgindex = -1
        this.recoverAllAni();
        this.recoverAllIcon();
        Laya.Pool.clearBySign("ludo_jungle_event");
    }
}