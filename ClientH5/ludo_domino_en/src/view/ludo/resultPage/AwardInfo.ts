

class AwardInfo {
    public view: ui.ludo.resultPage.championAwardUI = null;
    private _champion: boolean = false;
    constructor(champion: boolean, gold: number, prop: number, isQuit: boolean, dataSource: Object = { right: 0, centerY: 0 }) {
        this._champion = champion;
        this.view = champion ? new ui.ludo.resultPage.championAwardUI() : new ui.ludo.resultPage.normalAwardUI();
        this.view.dataSource = dataSource;
        ludoRoom.instance.isActivityProp && (this.propSrc = ludoRoom.instance.activityPropUrl);
        this.isQuit = isQuit;
        !isQuit && this.setAward(gold, prop);
    }
    private set propSrc(url: string) {
        url && yalla.File.downloadImgByUrl(url, (localPath: string) => {
            Laya.loader.load(localPath, Laya.Handler.create(this, (e) => {
                if (e && this.view && this.view.prop_icon) {
                    this.view.prop_icon.skin = localPath;
                }
            }));
        })
    }
    private setAward(gold: number, prop: number) {
        if (gold) {
            this.view.gold_num.text = '+' + yalla.util.filterNum(gold);
        }
        this.view.gold_bg.visible = Boolean(gold);
        if (prop) {
            this.view.prop_num.text = '+' + yalla.util.filterNum(prop);
        }
        this.view.prop_bg.visible = Boolean(prop);
        if (gold && !prop) {//仅显示金币
            this._champion ? this.view.gold_bg.pos(0, 31) : this.view.gold_bg.pos(0, 28);
        } else if (prop && !gold) {//仅显示道具
            this._champion ? this.view.prop_bg.pos(0, 31) : this.view.prop_bg.pos(0, 28);
        }
    }

    private set isQuit(val: boolean) {
        if (this.view) {
            this.view.isQuit.visible = val;
            this.view.prop_bg.visible = !val;
            this.view.gold_bg.visible = !val;
        }
    }

}