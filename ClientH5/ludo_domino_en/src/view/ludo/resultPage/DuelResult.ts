class DuelResult extends ui.ludo.resultPage.duelResultUI {
    private templet: Laya.Templet;
    private sk: Laya.Skeleton;
    public isWin: boolean = true;
    public isDraw: boolean = false;//是否是平局
    // private offGame: boolean = false;
    private inSit: boolean = false;
    static instance: Result = null;
    private _nameItemHash: Object = {};
    constructor(msg: any, offGame: boolean, showFali: boolean) {
        super();
        // this.offGame = offGame;
        yalla.util.swapPos(this.again_btn, this.back_btn, "centerX");
        this.showInfo(msg, showFali);
        this.templet = new Laya.Templet();
        this.templet.on(Laya.Event.COMPLETE, this, this.parseComplete);
        this.templet.on(Laya.Event.ERROR, this, this.onError);
        this.bg_mask.size(Laya.stage.width, Laya.stage.height);
        this.addEvent(0);
        this.timer.once(10000, this, this.backHall);
        this.showItem(msg.player ? msg.player.length : 4);
        yalla.Global.IsGameOver = true;
        yalla.util.closeAllDialog();
        this.name = "resultView";
        yalla.common.ChatManager.instance.clearChatMsgAni();
        if (yalla.Global.Account.roomid) yalla.util.clogDBAmsg('200019', { roomid: yalla.Global.Account.roomid }, 3);
        yalla.util.isLocked = false;
    }
    public addEvent(type: number = 0) {
        switch (type) {
            case 0://正常结算
                this.again_btn.once(Laya.Event.CLICK, this, this.playAgain);
                this.back_btn.on(Laya.Event.CLICK, this, this.backHander);
                this.isDraw ? this.drawGame() : this.isWin ? this.winGame() : this.loseGame();
                break;
            case 1://观战者||离线结算||私人房间提前退出
                this.winGame();
                if (!this.inSit)//排除掉 私人房间提前退出
                    this.result_tx.text = "Game Finished";
                this.result_tx.fontSize = 50;
                this.again_btn.label = "Share";
                this.again_btn.once(Laya.Event.CLICK, this, this.share);
                this.back_btn.on(Laya.Event.CLICK, this, this.backHander);
                break;
        }
    }
    private drawGame() {
        this.result_tx.text = "You Draw";
        this.templet.loadAni(yalla.getSkeleton("result/deuce"));
        this.result_tx.color = "#ffffff";
        this.result_tx.strokeColor = "#0d8713";
    }
    private loseGame() {
        this.result_tx.text = "You Lose";
        this.templet.loadAni(yalla.getSkeleton("result/lose"));
        this.result_tx.color = "#ffffff";
        this.result_tx.strokeColor = "#297bc5";
    }
    private winGame() {
        this.result_tx.text = "You Win";
        this.templet.loadAni(yalla.getSkeleton("result/win"));
    }
    private backHander = yalla.util.withLock(() => {
        // private backHander() {
        yalla.Native.instance.mobClickEvent(buryPoint.GAME_BACK);//埋点 结算返回大厅
        yalla.Global.Account.roomid &&
            yalla.util.clogDBAmsg('10269', { roomid: yalla.Global.Account.roomid });
        this.backHall();
        // }
    })
    public backHall() {
        this.leaveVoiceRoom();
        this.removeEvent();
        if (ludo.GameSocket._instance && ludo.GameSocket.instance.connected) {
            ludo.GameSocket.instance.quitRoom();
        }
        var native = yalla.Native.instance;
        var param = null;
        if (!yalla.Global.Account.needBackHall) {
            var backName = yalla.backHall_direction[yalla.Global.Account.isPrivate];
            param = { to: backName, gameId: yalla.Global.Account.gameId };
        }
        native.backHall(native.isBack, param, 500);
        this.gameOverBack();
    }
    removeEvent() {
        this.timer.clearAll(this);
        this.back_btn.offAll();
        this.again_btn.offAll();
        yalla.Native.instance.off(yalla.Native.instance.Event.SHARE, this, this.backHall);
    }
    private leaveVoiceRoom() {
        //TODO::1.4.4.1 android playagai 等先退出语音房，音playsound 没有回掉（声网）
        // if (yalla.Voice.instance.joinSuccess)
        //         yalla.Voice.instance.levelGameRoom();
        yalla.Voice.instance.levelGameRoom_android();
    }
    /**
     * 1.4.4.1 只有ios 结算的back playAgain 才先关闭界面，避免下一次进入游戏有上一局的界面
     */
    public gameOverBack() {
        if (yalla.Native.instance.deviceType == DeviceType.IOS) {
            this.clear();
        }
    }
    public clear() {
        if (this.templet) {
            this.templet.offAll();
            this.templet.destroy();
            this.templet = null;
        }
        if (this.sk) {
            this.sk.offAll();
            this.sk.destroy();
        }
        this.removeEvent();
        this.removeSelf();
    }
    public playAgain = yalla.util.withLock(() => {
        yalla.util.clogPlayAgain(ludoRoom.instance.type, ludoRoom.instance.group);
        yalla.Global.IsGameOver = true;
        this.leaveVoiceRoom();
        this.timer.clearAll(this);
        if (ludo.GameSocket.instance.connected) {
            ludo.GameSocket.instance.quitRoom();
            yalla.util.sendExitType(ExitType.PLAY_AGAIN);
        }
        ludo.GameSocket.instance.offAll();
        ludo.GameSocket.instance.close();
        yalla.Native.instance.playAgain({
            gamePay: ludoRoom.instance.cost,
            gameId: 10019,
            gameType: ludoRoom.instance.type,
            playerNum: ludoRoom.instance.getSitPlayers().length,
            gameGroup: ludoRoom.instance.group,
            roomId: ludoRoom.instance.showID,
            isPrivate: yalla.Global.Account.isPrivate
        });
        // yalla.util.clogDBAmsg('10165');
        this.gameOverBack();
    })

    public share = yalla.util.withLock(() => {
        // public share() {
        this.leaveVoiceRoom();
        this.timer.clearAll(this);
        yalla.Native.instance.shortScreenNative();
        yalla.util.clogShare(ludoRoom.instance.type, ludoRoom.instance.group);
        this.timer.clear(this, this.backHall);
        yalla.Native.instance.off(yalla.Native.instance.Event.SHARE, this, this.backHall);
        yalla.Native.instance.on(yalla.Native.instance.Event.SHARE, this, this.backHall);
    })
    private parseComplete() {
        if (this.templet) {
            this.sk = this.templet.buildArmature(0);
            this.skBox.addChild(this.sk);
            this.skBox.setChildIndex(this.sk, 0);
            this.sk.pos(this.skBox.width / 2, this.skBox.height / 2);
            this.sk.play(0, false);
            Laya.Tween.to(this.result_tx, { alpha: 1 }, 200, null, null, 300);
            this.sk.on(Laya.Event.STOPPED, this, () => {
                this.sk.play(1, true);
            });
        }
    }
    private showItem(len: number) {
        var _X = Laya.stage.width / 2;
        for (var i = 0; i < len; i++) {
            var target = this['info_' + i]
            Laya.Tween.to(target, {
                x: _X
            }, 500, Laya.Ease.backInOut, Laya.Handler.create(this, (tgt) => {
                if (tgt && !tgt.destroyed) tgt.x = _X;
            }, [target]), i * 80)
        }
        this.timer.once(500 + len * 80, this, () => {
            this.mode1.play(0, false);
        })
        this.timer.once(600 + len * 80, this, () => {
            this.mode2.play(0, false);
        })
    }
    private onError() { }
    public showInfo(msg: any, showFali) {
        var players: Array<playerShowInfo> = msg.player;
        var len = players.length;
        if (len > 0 && len < 4) {
            var btn_y = this["info_" + (len - 1)].y + 290;
            this.back_btn.y = btn_y;
            this.again_btn.y = btn_y;
        }
        this.isDraw = Boolean(players[0].winMoney == players[1].winMoney);
        // this.isDraw = true;
        for (let i = 0; i < len; i++) {
            var playerInfo: playerShowInfo = msg.player[i];
            // playerInfo.roylevel = 5;
            var idx = playerInfo.fPlayerInfo.idx;
            if (idx == yalla.Global.Account.idx) {
                this.inSit = true;
                this.isWin = ((i < 2 && len == 4) || (i < 1 && len < 4));
            }
            if (!showFali && playerInfo.winIndex == -1) {
                this["index_" + i].visible = false;
            }
            var info: Laya.Image = this["info_" + i];
            if (!info) break;
            if (this.isDraw) {
                if (i == 0) {
                    info.skin = `public/BG_234.png`;
                    this.index_0.visible = false;
                    this.box_0.y = 14;
                    this.fightNumBg_0.skin = `duel/result_bg_2.png`;
                    this.fightNum_0.dataSource = {
                        color: "#60E59B",
                        strokeColor: "#217E27"
                    }
                    this.gold_0.dataSource = {
                        color: "#ffffff",
                        strokeColor: "#000000"
                    }
                    this.nikeName_0.dataSource = {
                        color: "#8dfff8",
                        strokeColor: "#000000"
                    }
                    this.gold_0_box.dataSource = {
                        width: 162,
                        height: 44,
                        skin: "public/bg_coingreen.png",
                        x: 504
                    }
                    this.self_0.size(728, 160);
                } else {
                    info.y = info.y - 30;
                }
                var index = this["index_" + i];//平局不显示名次
                if (i < 2) {
                    if (index) index.visible = false;
                } else {
                    if (index) index.text = "-";
                }
            }
            info.visible = true;
            if (idx == yalla.Global.Account.idx)
                (info.getChildByName("self") as Laya.Image).visible = true;
            var nameItem: yalla.common.NameItem = this._nameItemHash[idx];
            if (!nameItem) {
                nameItem = new yalla.common.NameItem(this['nameItem_' + i], this['nikeName_' + i]);
                this._nameItemHash[idx] = nameItem;
            }
            var p: playerShowInfo = ludoRoom.instance.getPlayerByIdx(playerInfo.fPlayerInfo.idx) || playerInfo;
            nameItem.resultName(p, yalla.util.filterName(p.fPlayerInfo.nikeName, 13, "m"));
            // yalla.Emoji.lateLable(yalla.util.filterName(playerInfo.fPlayerInfo.nikeName, 13, "m"), this["nikeName_" + i]);
            var head: yalla.common.Head = new yalla.common.Head();
            head.mouseEnabled = false;
            head.left = 114;
            head.top = this.isDraw || i != 0 ? 6 : 26;
            info.addChild(head);
            head.showFaceBox(playerInfo);
            var goldInfoBox = this[`gold_${i}_box`];
            if (goldInfoBox) {
                if (playerInfo.winMoney) {
                    goldInfoBox.visible = true;
                    var goldTx = this[`gold_${i}`];
                    goldTx && (goldTx.text = "+" + yalla.util.filterNum(playerInfo.winMoney));
                } else {
                    goldInfoBox.visible = false;
                    if (playerInfo.isQuit && i > 0) {
                        var isQuit = this[`isQuit_${i}`];
                        isQuit && (isQuit.visible = true);
                    }
                }
            }
            if (playerInfo.fightNum > 0) {
                var fightNumLb: Laya.Label = this["fightNum_" + i];
                if (fightNumLb) fightNumLb.text = String(playerInfo.fightNum);
            }
        }
    }
    public frameLoopName(): void {
        for (var key in this._nameItemHash) {
            var nameItem = this._nameItemHash[key];
            nameItem.frameLoopName();
        }
    }
}