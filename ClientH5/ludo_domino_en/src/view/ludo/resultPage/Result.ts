class Result extends ui.ludo.resultPage.resultUI {
    private templet: Laya.Templet;
    private sk: Laya.Skeleton;
    public isWin: boolean = true;
    private offGame: boolean = false;
    private winnerId: number = 0;
    private inSit: boolean = false;
    static instance: Result = null;
    private _nameItemHash: Object = {};
    public propNum: number = 0;

    constructor(msg: any, offGame: boolean, showFali: boolean) {
        super();
        yalla.util.swapPos(this.again_btn, this.back_btn, "centerX");
        this.offGame = offGame;
        this.showInfo(msg, showFali);
        this.templet = new Laya.Templet();
        this.templet.on(Laya.Event.COMPLETE, this, this.parseComplete);
        // this.templet.on(Laya.Event.ERROR, this, this.onError);
        this.bg_mask.size(Laya.stage.width, Laya.stage.height);
        if (yalla.Global.Account.isPrivate === 4) {//VIP房间
            this.addEvent(3);
            this.timer.once(10000, this, this.backHall);
        } else if (yalla.Global.Account.isPrivate === 3 && this.inSit) {//锦标赛
            this.addEvent(2);
        } else if (!this.inSit || offGame) {//观战 或者 离线
            this.addEvent(1);
            this.timer.once(10000, this, this.backHall);
        } else {
            this.addEvent(0);
            this.timer.once(10000, this, this.backHall);
        }
        this.showItem(msg.player ? msg.player.length : 4);
        yalla.Global.IsGameOver = true;
        yalla.util.closeAllDialog();
        this.name = "resultView";
        yalla.common.ChatManager.instance.clearChatMsgAni();
        if (yalla.Global.Account.roomid) yalla.util.clogDBAmsg('200019', { roomid: yalla.Global.Account.roomid }, 3);
        yalla.util.isLocked = false;
    }
    public addEvent(type: number = 0) {
        switch (type) {
            case 0://正常结算
                this.again_btn.once(Laya.Event.CLICK, this, this.playAgain);
                this.back_btn.on(Laya.Event.CLICK, this, this.backHander);
                this.isWin ? this.winGame() : this.loseGame();
                break;
            case 1://观战者||离线结算||私人房间提前退出
                this.winGame();
                if (!this.inSit)//排除掉 私人房间提前退出
                    this.result_tx.text = "Game Finished";
                this.result_tx.fontSize = 50;
                this.again_btn.label = "Share";
                // this.again_btn.on(Laya.Event.CLICK, this, this.share);
                this.again_btn.once(Laya.Event.CLICK, this, this.share);
                this.back_btn.on(Laya.Event.CLICK, this, this.backHander);
                break;
            case 2://锦标赛
                this.isWin ? this.winGame() : this.loseGame();
                this.again_btn.visible = false;
                this.back_btn.label = "Back";
                this.back_btn.centerX = 0;
                this.timer.clear(this, this.backHall);
                this.back_btn.on(Laya.Event.CLICK, this, () => {
                    yalla.Native.instance.event(yalla.Native.instance.Event.BACKCHAMPION, [{ winnerId: this.winnerId }]);
                });
                break;
            case 3://VIP房间
                if (!this.inSit) {//观战
                    this.winGame();
                    this.result_tx.text = "Game Finished";
                } else {
                    this.isWin ? this.winGame() : this.loseGame();
                }
                this.again_btn.label = "Share";
                // this.again_btn.on(Laya.Event.CLICK, this, this.share);
                this.again_btn.once(Laya.Event.CLICK, this, this.share);
                this.back_btn.on(Laya.Event.CLICK, this, this.backHander);
                break;
        }
    }
    private loseGame() {
        this.result_tx.text = "You Lose";
        this.templet.loadAni(yalla.getSkeleton("result/lose"));
        this.result_tx.color = "#ffffff";
        this.result_tx.strokeColor = "#297bc5";
    }
    private winGame() {
        this.result_tx.text = "You Win";
        this.templet.loadAni(yalla.getSkeleton("result/win"));
    }

    public backHall() {
        this.leaveVoiceRoom();
        this.removeEvent();
        if (ludo.GameSocket._instance && ludo.GameSocket.instance.connected) {
            ludo.GameSocket.instance.quitRoom();
        }
        var native = yalla.Native.instance;
        var param = null;
        if (this.offGame) {
            param = { to: "local", gameId: 10019 };
        } else if (!yalla.Global.Account.needBackHall) {
            var backName = yalla.backHall_direction[yalla.Global.Account.isPrivate];
            param = { to: backName, gameId: yalla.Global.Account.gameId };
        }
        native.backHall(native.isBack, param, 500);
        this.gameOverBack();
    }

    removeEvent() {
        this.timer.clearAll(this);
        this.back_btn.offAll();
        this.again_btn.offAll();
        yalla.Native.instance.off(yalla.Native.instance.Event.SHARE, this, this.backHall);
    }
    leaveVoiceRoom() {
        //TODO::1.4.4.1 android playagai 等先退出语音房，音playsound 没有回掉（声网）
        // if (yalla.Voice.instance.joinSuccess)
        //         yalla.Voice.instance.levelGameRoom();
        yalla.Voice.instance.levelGameRoom_android();
    }

     /**
     * 1.4.4.1 只有ios 结算的back playAgain 才先关闭界面，避免下一次进入游戏有上一局的界面
     */
    public gameOverBack() {
        if (yalla.Native.instance.deviceType == DeviceType.IOS) {
            this.clear();
        }
    }

    public clear() {
        if (this.templet) {
            this.templet.offAll();
            this.templet.destroy();
            this.templet = null;
        }
        if (this.sk) {
            this.sk.offAll();
            this.sk.destroy();
        }
        this.removeEvent();
        this.removeSelf();
        this.destroy(true);
    }
    private backHander = yalla.util.withLock(() => {
        // private backHander() {
        yalla.Native.instance.mobClickEvent(buryPoint.GAME_BACK);//埋点 结算返回大厅
        yalla.Global.Account.roomid &&
            yalla.util.clogDBAmsg('10269', { roomid: yalla.Global.Account.roomid });
        this.backHall();
        // }
    })
    public playAgain = yalla.util.withLock(() => {
        // public playAgain() {
        yalla.Global.IsGameOver = true;
        this.leaveVoiceRoom();
        this.timer.clearAll(this);
        if (ludo.GameSocket.instance.connected) {
            ludo.GameSocket.instance.quitRoom();
            yalla.util.sendExitType(ExitType.PLAY_AGAIN);
        }
        ludo.GameSocket.instance.offAll();
        ludo.GameSocket.instance.close();
        yalla.util.clogPlayAgain(ludoRoom.instance.nightGame() ? 7 : ludoRoom.instance.type, ludoRoom.instance.group);
        yalla.Native.instance.playAgain({
            gamePay: ludoRoom.instance.cost,
            gameId: 10019,
            gameType: ludoRoom.instance.type,
            playerNum: ludoRoom.instance.getSitPlayers().length,
            gameGroup: ludoRoom.instance.group,
            roomId: ludoRoom.instance.showID,
            isPrivate: yalla.Global.Account.isPrivate
        });
        // }
        this.gameOverBack();
    })

    public share = yalla.util.withLock(() => {
        // public share() {
        // if (yalla.System.isRepeatClick()) return;
        this.leaveVoiceRoom();
        this.timer.clearAll(this);
        yalla.Native.instance.shortScreenNative();
        yalla.util.clogShare(ludoRoom.instance.nightGame() ? 7 : ludoRoom.instance.type, ludoRoom.instance.group);
        this.timer.clear(this, this.backHall);
        yalla.Native.instance.off(yalla.Native.instance.Event.SHARE, this, this.backHall);
        yalla.Native.instance.on(yalla.Native.instance.Event.SHARE, this, this.backHall);
        // }
    });

    private parseComplete() {
        if (this.templet) {
            this.sk = this.templet.buildArmature(0);
            // this.sk.playbackRate(0.66);
            this.skBox.addChild(this.sk);
            this.skBox.setChildIndex(this.sk, 0);
            this.sk.pos(this.skBox.width / 2, this.skBox.height / 2);
            this.sk.play(0, false);
            Laya.Tween.to(this.result_tx, { alpha: 1 }, 200, null, null, 300);
            this.sk.on(Laya.Event.STOPPED, this, () => {
                this.sk.play(1, true);
            });
        }
    }
    private showItem(len: number) {
        var _X = Laya.stage.width / 2;
        for (var i = 0; i < len; i++) {
            var target = this['info_' + i];
            Laya.Tween.to(target, {
                x: _X
            }, 500, Laya.Ease.backInOut, Laya.Handler.create(this, (tgt) => {
                if (tgt && !tgt.destroyed) tgt.x = _X;
            }, [target]), i * 80)
        }
        this.timer.once(500 + len * 80, this, () => {
            this.mode1.play(0, false);
        })
        this.timer.once(600 + len * 80, this, () => {
            this.mode2.play(0, false);
        })
    }
    private onError() { }
    private showAwardInfo(champion: boolean, gold: number, prop: number, isQuit: boolean) {
        return new AwardInfo(champion, gold, prop, isQuit, { right: 15, centerY: 0 });
    }
    public showInfo(msg: any, showFali) {
        var [winMoney1, winMoney2] = [msg.winMoney1 || 0, msg.winMoney2 || 0];
        if (yalla.Global.Account.isPrivate === 3) {//锦标赛
            winMoney1 = ludoRoom.instance.cost;
        }
        var players: Array<playerShowInfo> = msg.player;
        var len = players.length;
        if (len > 0 && len < 4) {
            var btn_y = this["info_" + (len - 1)].y + 290;
            this.back_btn.y = btn_y;
            this.again_btn.y = btn_y;
        }
        if (players && players[0])
            this.winnerId = (msg.player[0] as playerShowInfo).fPlayerInfo.idx;

        for (let i = 0; i < len; i++) {
            var playerInfo: playerShowInfo = msg.player[i];
            // playerInfo.roylevel = 5;
            var idx = playerInfo.fPlayerInfo.idx;
            var isMe = idx == yalla.Global.Account.idx;
            if (isMe) {
                this.inSit = true;
                this.isWin = ((i < 2 && len == 4) || (i < 1 && len < 4));
                this.propNum = playerInfo.propNum;
            }
            if (!showFali && playerInfo.winIndex == -1) {
                this["index_" + i].visible = false;
            }
            var info: Laya.Image = this["info_" + i];
            if (!info) break;
            // if (i < 2) {
            var winMoney = i == 0 ? winMoney1 : i == 1 ? winMoney2 : 0,
                propNum = isMe ? playerInfo.propNum : 0;
            if (propNum || winMoney) {
                var award = this.showAwardInfo(i == 0, winMoney, propNum, false);
                info.addChild(award.view);
            }
            info.visible = true;
            if (isMe && !this.offGame)
                (info.getChildByName("self") as Laya.Image).visible = true;
            var nameItem: yalla.common.NameItem = this._nameItemHash[idx];
            if (!nameItem) {
                nameItem = new yalla.common.NameItem(this['nameItem_' + i], this['nikeName_' + i]);
                this._nameItemHash[idx] = nameItem;
            }
            var p: playerShowInfo = ludoRoom.instance.getPlayerByIdx(playerInfo.fPlayerInfo.idx) || playerInfo;
            nameItem.resultName(p, yalla.util.filterName(p.fPlayerInfo.nikeName, 13, "m"));
            // yalla.Emoji.lateLable(yalla.util.filterName(playerInfo.fPlayerInfo.nikeName, 13, "m"), this["nikeName_" + i]);
            var head: yalla.common.Head = new yalla.common.Head();
            head.mouseEnabled = false;
            head.left = 110;
            head.top = i === 0 ? 26 : 6;
            info.addChild(head);
            head.showFaceBox(playerInfo);
        }
    }
    public frameLoopName(): void {
        for (var key in this._nameItemHash) {
            var nameItem = this._nameItemHash[key];
            nameItem.frameLoopName();
        }
    }
}