/**
 * 计时动画
 */
module Ludo {
    export class CdAni extends Laya.Sprite {
        private _r: number
        // private angle: number;
        private totalTime: number;
        // private running: Boolean;
        private color: string;
        // private step: number;
        public alarm: boolean = true;//倒计时音效
        public timeValue: number = 5000;//剩余时间开始播放音效
        // private logTag: string = "CdAni : ";
        //当前计时器结束时间
        private endTime: number;
        private startTime: number;

        private maskImg = new Laya.Image("game/head_mask.png");
        /**
         * @param r 半径
         * @param totalTime 时间 毫秒
         * @param color 颜色0-3
         */
        constructor(r: number, totalTime: number, color?: string) {
            super();
            this._r = r;
            this.totalTime = totalTime;
            // this.angle = -90;
            // this.step = 360 / (this.totalTime / 100);
            // this.running = true;
            this.color = color ? color : "#50f254";
            this.on(Laya.Event.REMOVED, this, () => {
                this.timer.clearAll(this);
            })
            var wh = r * 2 * 0.9;
            this.maskImg.size(wh, wh);
            this.maskImg.pos(r * 0.1, r * 0.1);
            this.mask = this.maskImg;
            this.alpha = 0.75;
        }
        public getEndTime(): number {
            return this.endTime;
        }
        private msecond: number;
        public start(totalTime: number, timeValue: number = 5000) {
            this.startTime = yalla.System.getNowTime();
            // if (totalTime) {
            //     this.step = 360 / (totalTime / 100);
            // }
            this.totalTime = totalTime;
            //自己回合结束时间，用于断线时判断是否自己回合结束
            this.endTime = yalla.System.getNowTime() + totalTime;
            this.timeValue = timeValue;
            this.msecond = 1;
            this.timer.loop(100, this, this.update);
            return this;
        }
        public addTime(totalTime: number) {
            this.totalTime += totalTime;
            //自己回合结束时间，用于断线时判断是否自己回合结束
            this.endTime = this.endTime + totalTime;
            // this.step = 360 / (this.totalTime / 100);
            // this.angle = this.msecond * this.step - 90;
            //TODO::卡点加时，但是timer计时器已经结束,但是msecond从上一次开始计算
            if (totalTime > 0 && this.msecond <= 1) {
                this.end();
                this.msecond = (this.totalTime - totalTime) / 100;
                // this.angle = this.msecond * this.step - 90;
                this.timer.loop(100, this, this.update);
            }
        }
        public resetTime(remainTime: number, totalTime: number, timeValue: number = 5000) {
            if (remainTime <= 0 || totalTime <= 0) return;
            this.msecond = (totalTime - remainTime) / 100;
            this.totalTime = totalTime; //总时间
            this.endTime = yalla.System.getNowTime() + remainTime;
            this.startTime = this.endTime - totalTime;
            // this.step = 360 / (this.ms / 100);
            // this.angle = this.msecond * this.step - 90;
            this.timeValue = timeValue;

            this.timer.clear(this, this.update);
            this.timer.loop(100, this, this.update);

        }
        private getAngle(): number {
            let remainTime = yalla.System.getNowTime() - this.startTime;//已用时
            if (remainTime <= 0) return 270;
            let angle = remainTime / this.totalTime * 360 - 90;
            return angle;
        }



        private update() {
            // this.angle += this.step;
            var angle = this.getAngle();
            this.graphics.clear();
            this.graphics.drawPie(this._r, this._r, this._r, angle, 270, this.color);
            if (this.alarm && this.msecond % 10 == 0) {
                // let sencond = this.msecond * 100 - this.timeValue;
                let sencond = this.totalTime - this.msecond * 100;
                if (sencond > 0 && sencond <= this.timeValue) {
                    yalla.Sound.playSound("last_sec");
                    //jackaro 倒计时3秒时，每秒震动一下
                    (yalla.Global.gameType == GameType.JACKARO) && yalla.Sound.playPhoneVibrate();
                }
            }
            if (angle >= 270) {
                this.end();
            }
            this.msecond++;
        }
        /**返回剩余操作时间 */
        public get operateTime(): number {
            // console.log("====operateTime===",this.ms,"====",this.msecond*100);
            // if (this.msecond > 1) return (this.totalTime - this.msecond * 100);
            // return this.totalTime;
            return Math.max(0, this.endTime - yalla.System.getNowTime());
        }
        public end() {
            this.endTime = 0;
            this.timeValue = 5000;
            this.msecond = 0;
            // this.angle = -90;
            this.graphics.clear();
            this.timer.clear(this, this.update);
            return this;
        }

        public clear(): void {
            this.graphics && this.graphics.clear();
            this.timer.clearAll(this);
            this.removeSelf();
            this.destroy();
        }
    }
}