class ChooseStep extends ui.ludo.sub.chooseStepUI {
    constructor() {
        super();
    }
    private pool: Array<Laya.Box> = [];
    show(nums: Array<number>, color) {
        nums = yalla.util.uniq(nums);
        if (nums.length <= 0)
            return;
        if (nums.length == 1) {
            this.event("choose", nums[0]);
            return;
        }
        this.poise(color, nums.length);
        nums.forEach((num, index: number) => {
            var btn: Laya.Box = this.createChooseBtn(color, num);
            btn.offAll();
            btn.x = index;
            this.nums_box.addChild(btn);
            btn.on("click", this, (e: Laya.Event) => {
                e.stopPropagation();
                yalla.Sound.playSound("click");
                this.event("choose", num);
            })
        })
        this.visible = true;
    }
    //姿势
    poise(color, len) {
        this.rotation = 0;
        this.nums_bg.width = len * 100;
        this.nums_bg.skin = `game/Bubble_${color}.png`;
        this.arrow_skin.skin = `game/Bubble_${color}_0.png`;
        var centerX = 0;
        if (this.x < this.nums_bg.width / 2) {
            centerX = len / 2 * 100 - this.x;
        } else if ((this.parent as Laya.Sprite).width - this.x < this.nums_bg.width / 2) {
            var offX = len / 2 * 100 - ((this.parent as Laya.Sprite).width - this.x);
            centerX = offX * -1;
        }
        if (!yalla.Global.onlineMode && (color == 1 || color == 2)) {//离线 上方两位玩家
            this.rotation = 180;
            this.nums_bg.centerX = -centerX;
            if (this.y > (this.parent as Laya.Sprite).height - 100) {
                this.anchorY = 0;
                this.arrow.rotation = 180;
            } else {
                this.anchorY = 1;
                this.arrow.rotation = 0;
            }
        } else {
            this.rotation = 0;
            this.nums_bg.centerX = centerX;
            if (this.y < 100) {
                this.anchorY = 0;
                this.arrow.rotation = 180;
            } else {
                this.anchorY = 1;
                this.arrow.rotation = 0;
            }
        }
    }
    hide() {
        if (!this.visible) return;
        this.visible = false;
        this.nums_box._childs && this.nums_box._childs.forEach(node => {
            this.pool.push(node);
        })
        this.nums_box.removeChildren();
    }
    createChooseBtn(color: number, num: number) {
        var box: Laya.Box;
        if (this.pool.length > 0) {
            box = this.pool.shift();
            (box.getChildByName("img") as Laya.Image).skin = `game/lable_${color}.png`;
            (box.getChildByName("tx") as Laya.Text).text = num.toString();
        } else {
            box = new Laya.Box();
            box.size(90, 90);
            let img: Laya.Image = new Laya.Image();
            img.name = "img";
            img.centerX = img.centerY = 0;
            img.skin = `game/lable_${color}.png`;
            let tx: Laya.Text = new Laya.Text();
            tx.text = num.toString();
            tx.name = "tx"
            tx.align = "center";
            tx.valign = "middle";
            tx.size(90, 90);
            tx.fontSize = 50;
            tx.color = "#ffffff";
            tx.pos(0, 0);
            box.addChild(img);
            box.addChild(tx);
        }
        return box;
    }
}