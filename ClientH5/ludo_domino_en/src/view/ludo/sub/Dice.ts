/**
 * 筛子&&动画
 */
class Dice extends Laya.Box {
    public sk: Laya.Skeleton;//sk动画
    public W: number;
    public H: number;
    public templet: Laya.Templet;
    private skin_id: number = null;
    private status: string = "n7";
    private soundLoaded: boolean = false;
    constructor(skin: number = 10000) {
        super();
        this.W = 90;
        this.H = 94;
        this.size(this.W, this.H);
        this.loadDice(`res/sk/dice/dice_10000.sk`);
        // this.loadDice(`res/10095/dice_10095.sk`);
        this.skinId = skin < 10000 ? 10000 : skin;
    }
    set skinId(val: number) {
        if (this.skin_id != val) {
            this.skin_id = val;
            if (val != 10000) {
                var path = `${yalla.File.cachePath}/dice_${this.skin_id}`;
                if (yalla.File.existed(`${path}.png`) && yalla.File.existed(`${path}.sk`)) {
                    this.loadDice("file://" + `${path}.sk`.replace(new RegExp('//', 'g'), "/"));
                } else {
                    yalla.event.YallaEvent.instance.once(`dice_${this.skin_id}.sk`, this, (data) => {
                        !this.destroyed && this.loadDice(`${yalla.File.filePath}dice_${this.skin_id}.sk`);
                    })
                    yalla.event.YallaEvent.instance.once(`dice_${this.skin_id}.png`, this, (data) => {
                        yalla.File.getFileByNative(`dice_${this.skin_id}.sk`)
                    })
                    yalla.File.getFileByNative(`dice_${this.skin_id}.png`);
                }
            }
        }
    }
    get skinId(): number {
        return this.skin_id;
    }
    private loadDice(url: string) {
        this.resetTemplate();
        this.templet.loadAni(url);
    }
    public clear() {
        this.timer.clearAll(this)
        this.clearTp();
        this.clearSk();
        this.destroy();
    }
    private clearTp() {
        if (this.templet) {
            this.templet.offAll();
            this.templet.destroy();
            this.templet = null;
        }
    }
    private clearSk() {
        if (this.sk) {
            this.sk.stop();
            this.sk.removeSelf();
            this.sk.destroy();
            this.sk = null;
        }
    }
    private resetTemplate() {
        this.clearTp();
        this.templet = new Laya.Templet();
        this.templet.on(Laya.Event.COMPLETE, this, this.parseComplete);
        this.templet.on(Laya.Event.ERROR, this, this.onError);
    }
    
    private _errorCount = 0;
    private parseComplete() {//动画加载成功
        if (this.destroyed || !this.templet) return;
        this.clearSk();
        this.sk = this.templet.buildArmature(0);
        if (this.sk && this.sk.templet && this.sk.templet.skinDataArray) {
            this.addChild(this.sk);
            this.sk.pos(this.W / 2, this.H / 2);
            this.sk.playbackRate(1.7);
            this.sk.play("n7", false);
        } else {
            this.onError();
        }
    }
    protected onError() {
        if (this._errorCount < 3) {
            this.resetTemplate();
            if (this.skinId != 10000) {
                this.loadDice(`res/sk/dice/dice_10000.sk`);
            }
        }
        this._errorCount += 1;
    }
    /**
     * play 播放动画
     */
    public play() {
        if (this.sk && this.status != "n0") {
            this.status = "n0";
            this.sk.stop();
            this.sk.play(this.status, true);
            if (yalla.Skin.instance.hasSound(this.skin_id) && yalla.File.existed(yalla.File.cachePath + `/start_roll_${this.skin_id}.mp3`)) {
                yalla.Sound.playSkinSound(`start_roll_${this.skin_id}`);
            } else {
                yalla.Sound.playSound("dice_roll");
            }
            this.timer.once(5000, this, this.stopAndShowDice, [7]);
        }
    }
    /**
     * @param num 展示数字
     */
    public stopAndShowDice(num: number) {
        this.timer.clear(this, this.stopAndShowDice);
        if (this.sk) {
            if (num === 6 && yalla.Skin.instance.hasSound(this.skin_id)) {
                yalla.Sound.playSkinSound(`stop_roll_${this.skin_id}`);
            }
            this.sk.stop();
            this.status = `n${num}`;
            this.sk.play(this.status, false);
        }
    }
}