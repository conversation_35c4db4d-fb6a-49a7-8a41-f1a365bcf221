class GamePlayer extends Laya.Box {
    public head: yalla.common.Head;
    private waitCD: Ludo.CdAni;
    public dice: Dice;
    public position: Object;
    public select: boolean = false;
    public isPass: boolean = false;
    public idx: number = null;
    private chatBubble: yalla.common.Bubble;
    private _nameItem: yalla.common.NameItem;
    private _side: number = 0;
    private _isLeft: boolean = false;
    public isWin: boolean = false;
    private isPlay: boolean = false;
    public isMute: boolean = false;
    // public exp: Laya.Text;
    public buff: Array<number> = [];
    private _luckDice: number = 0;
    private _snailDice: number = 0;
    private _flushluckDice: number = 0;//用于记录断线前是否有金骰子道具
    private _flushSnailDice: number = 0;//用于记录断线前是否有蜗牛骰子道具
    private _tp: Laya.Templet = null;
    private ripple: Laya.Skeleton = null;
    private timeLine: Laya.TimeLine;
    public offLine: boolean;
    public _diceArr: Array<number> = [];
    public UI: any;
    public gameType: number = 0;
    private vipSk: Laya.Skeleton = null;
    private rlSk: Laya.Skeleton = null;
    private _vipKey: string = "";
    private _rlKey: string = "";
    private isMe: boolean = false;
    private timeValue: number = -1;

    constructor(primaryColor: number, color: number, offLine: boolean = false, playerInfo: playerShowInfo, gameTyp: number = GameType.LUDO) {
        super();
        this.idx = playerInfo.fPlayerInfo.idx;
        this.isMe = this.idx == yalla.Global.Account.idx;
        this.timeValue = this.isMe || ludoRoom.instance.beWatch(this.idx) ? 5000 : -1;
        this.offLine = offLine;
        this.gameType = gameTyp;
        this.head = new yalla.common.Head();
        this.head.scale(1, 1);
        this.dice = new Dice(playerInfo.diceSkinId);
        this.dice.pivotX = this.dice.W / 2;
        this.dice.pivotY = this.dice.H / 2;
        this.dice.pos(62, 50);
        var posList = [{ "left": 4, "bottom": -30 }, { "left": 4, "top": 0 }, { "right": 4, "top": 0 }, { "right": 4, "bottom": -30 }];
        if (this.gameType == GameType.SNAKEANDLADER) {
            this.side = color;
            this.position = posList[color];
            ludoRoom.instance.sitSideHash[playerInfo.fPlayerInfo.idx] = color;
        } else {
            for (let i = 0; i < 4; i++) {
                let index: number = (primaryColor + i) % 4;
                if (color == index) {
                    this.side = i;
                    this.position = posList[i];
                    ludoRoom.instance.sitSideHash[playerInfo.fPlayerInfo.idx] = i;
                    break;
                }
            }
            this.color = color;
        }
        if (!this.UI) return //1.4.4add容错 可能color的问题导致UI没有创建
        this.UI.head_box.addChild(this.head);
        this.head.pos(this.UI.head_box.width / 2, this.UI.head_box.height / 2);
        var posxy = this.head.face.width / 2 + 4;

        if (yalla.Global.gameType != GameType.SNAKEANDLADER) {
            this.UI.flag.removeSelf();
        }

        this.waitCD = new Ludo.CdAni(posxy, 10000);
        this.head.addChildAt(this.waitCD, 1);
        this.waitCD.pivot(posxy, posxy);
        this.waitCD.pos(56, 56);
        // this.color = color;
        this.UI.dice_vessel.addChild(this.dice);
        for (var key in this.position) {
            this[key] = this.position[key];
        }
        if (!offLine) {
            this.initBubble(playerInfo.talkSkinId);
        }
        if (this.isMe && yalla.Global.Account.isLimitVisitor) {
            this.UI.voice_btn.visible = false;
        } else {
            this.initEvent();
        }
        // LudoBuffManager.instance.getTemplet("props/gold_dice/skeleton", Laya.Handler.create(this, this.rippleLoaded))
        this._tp = new Laya.Templet()
        this._tp.on(Laya.Event.COMPLETE, this, this.rippleLoaded);
        this._tp.loadAni(yalla.getSkeleton("props/gold_dice/skeleton"));
    }
    private rippleLoaded(tpl) {
        if (tpl) {
            this.ripple = tpl.buildArmature(1);
            this.addChild(this.ripple);
            this.ripple.pos(this.UI.head_box.x, this.UI.head_box.y);
        }
    }
    public playVoiceAni(start: number, loop: boolean, name: string) {
        this.UI && (this.UI.voice_ani as Laya.Animation).play(start, loop, name);
    }
    public waitCdStart(time: number, timeValue: number = this.timeValue): GamePlayer {
        this.waitCD && this.waitCD.start(time, timeValue);
        return this;
    }
    public waitCdEnd(): GamePlayer {
        this.waitCD && this.waitCD.end();
        return this;
    }

    public frameLoopName(): void {
        if (this._nameItem) {
            this._nameItem.frameLoopName();
        }
    }
    public initEvent() {
        this.UI.voice_btn.on(Laya.Event.CLICK, this, () => {
            if (this.idx == yalla.Global.Account.idx) return;//自己
            if (yalla.Mute.muted(this.idx)) return;//已举报的玩家
            if (yalla.Voice.instance.muted(this.idx)) {
                yalla.Voice.instance.muteRemoteAudioStream(this.idx, false, (val, uid) => {//取消屏蔽
                    if (val == 0) {
                        this.isMute = false;
                        this.pause();
                        this.mobClick_quietMic(1);
                    }
                })
            } else {
                yalla.Voice.instance.muteRemoteAudioStream(this.idx, true, (val, uid) => {//屏蔽
                    if (val == 0) {
                        this.timer.clear(this, this.pause);
                        this.isMute = true;
                        this.UI.voice_ani.play(0, false, "stop");
                        this.mobClick_quietMic(0);
                    }
                })
            }
        })
        this.on(Laya.Event.REMOVED, this, () => {
            this.UI.voice_btn.offAll();
        })
    }
    initBubble(talkSkinId) {
        this.chatBubble = new yalla.common.Bubble(this.side);
        this.chatBubble.skinId = talkSkinId;
        this.chatBubble.pos(this.UI.name_box.x, this.UI.name_box.y);
        this.addChild(this.chatBubble);
    }

    mobClick_quietMic(state) {
        if (this.gameType == GameType.LUDO) {
            if (state == 1) {
                if (!ludo.Global.inSit()) {
                    yalla.Native.instance.mobClickEvent(buryPoint.GAME_SPECTATER_OPENEMIC);//埋点 观战点击取消静音
                } else {
                    yalla.Native.instance.mobClickEvent(buryPoint.GAME_USER_OPENMIC);//埋点 玩家点击取消静音
                }
            } else {
                if (!ludo.Global.inSit()) {
                    yalla.Native.instance.mobClickEvent(buryPoint.GAME_SPECTATER_QUIEMIC);//埋点 观战点击开启静音
                } else {
                    yalla.Native.instance.mobClickEvent(buryPoint.GAME_USER_QUIETMIC);//埋点 玩家点击开启静音
                }
            }
        }
    }
    clear() {
        if (this.timeLine) {
            this.timeLine.pause();
            this.timeLine.offAll();
            this.timeLine.destroy();
            this.timeLine = null;
        }
        if (this.head) {
            this.head.clear();
            this.head.destroy();
        }
        this.timer.clear(this, this.pause);
        if (this.waitCD)
            this.waitCD.destroy();
        this.dice && this.dice.clear();
        this.dice = null;
        if (this.ripple) {
            // this.template && this.template.destroy();
            // this.template = null;
            this.ripple.stop();
            this.ripple.destroy(true);
            this.ripple = null;
        }
        if (this.vipSk) {
            var __s = yalla.SkeletonManager.instance;
            __s.off(this._vipKey, this, this.loadVipSk);
            __s.clearByName(this._vipKey);
            this._vipKey = '';
        }
        if (this.rlSk) {
            var __s = yalla.SkeletonManager.instance;
            __s.off(this._rlKey, this, this.loadRLSk);
            __s.clearByName(this._rlKey);
            this._rlKey = '';
        }
        if (this._nameItem && this._nameItem.ui) {
            this._nameItem.ui.removeSelf();
        }
        if (this.UI) {
            (this.UI.voice_ani as Laya.Animation).stop();
            (this.UI.throw_jump as Laya.Animation).stop();
            this.UI.destroy(true);
        }

        this.clearBuff();
        this.destroy(true);

    }
    set luckDice(val: number) {
        if (this._luckDice == val) return;
        this._luckDice = val;
        if (this._luckDice <= 0) {
            this.hideBuff();
            this._luckDice = 0;
        }
    }
    get luckDice(): number {
        return this._luckDice;
    }
    set snailDice(val: number) {
        if (this._snailDice == val) return;
        this._snailDice = val;
        if (this._snailDice <= 0) {
            this.hideBuff();
            this._snailDice = 0;
        }
    }
    get snailDice(): number {
        return this._snailDice;
    }
    getBuff(bn: Buff) {
        switch (bn) {
            case Buff.LUCK_DICE:
                this.luckDice++;
                var buff = this.UI.buff_box.getChildByName(`buff_${bn}`) as BuffIcon;
                this.UI.buff_box.visible = true;
                if (!buff) {
                    buff = new BuffIcon(bn);
                    this.UI.buff_box.addChild(buff);
                }
                buff.num = this._luckDice;
                this.hideName();
                this._flushluckDice = this._luckDice;
                break;
            case Buff.SNAIL_DICE:
                this.snailDice++;
                var buff = this.UI.buff_box.getChildByName(`buff_${bn}`) as BuffIcon;
                this.UI.buff_box.visible = true;
                if (!buff) {
                    buff = new BuffIcon(bn);
                    this.UI.buff_box.addChild(buff);
                }
                buff.num = this._snailDice;
                this.hideName();
                this._flushSnailDice = this._snailDice;
                break;
        }
    }
    hideBuff() {
        this.UI.buff_box.visible = false;
    }
    clearBuff() {
        if (this.UI.buff_box) {
            this.UI.buff_box.removeChildren();
            (this.UI.buff_box as Laya.Box).destroyChildren();
        }
        this._luckDice = 0;
        this._snailDice = 0;
    }
    useBuff(bn: Buff) {
        var buff = this.UI.buff_box.getChildByName(`buff_${bn}`) as BuffIcon;
        if (buff) {
            if (buff.num > 1) {
                buff.num--;
            } else {
                buff.removeSelf();
                buff.destroy();
            }
        }
    }

    playRoleAni() {
        this.head && this.head.playRoleAni();
    }
    resetHeadScale() {
        this.head && this.head.resetScale();
    }
    playRipple() {
        if (yalla.Global.isFouce && this.ripple && this.ripple.templet)
            this.ripple.play(0, false);
    }
    stopAndShowDice(num: number) {
        if (!this.dice) return;
        if (this.UI.dice_bg && this.UI.dice_bg.alpha === 0) {
            this.showDice();
        }
        this.dice.stopAndShowDice(num);
        return this;
    }
    showName() {
        if (this.UI) {
            if (this.UI.buff_box.numChildren > 0) return;
            this.UI.name_box.visible = true;
        }
    }
    hideName() {
        if (this.UI && !this.isWin && !this.isLeft) {
            this.UI.name_box.visible = false;
        }
    }
    showDice() {
        this.UI.dice_bg.alpha = 1;
    }
    hideDice() {
        if (yalla.Global.isFouce) {
            var tw = Laya.Tween.to(this.UI.dice_bg, {
                alpha: 0
            }, 200, null, Laya.Handler.create(null, () => {
                tw && tw.clear();
            }), 1000)
        } else {
            this.UI.dice_bg.alpha = 0;
        }
    }
    // private createExpText(): Laya.Label {
    //     var exp: Laya.Label = new Laya.Label();
    //     exp.dataSource = {
    //         strokeColor: "#eeeeee",
    //         stroke: 2,
    //         bold: true,
    //         fontSize: 26,
    //         color: ludo.Global.getludoPcolor(this.color),
    //         width: 90,
    //         height: 30,
    //         align: "center"
    //     }
    //     return exp;
    // }
    showEXP(num: number) {
        var expLable: ExpLable = LudoBuffManager.instance.getExpLable();
        if (expLable && this.UI) {
            expLable.text = num + " EXP";
            expLable.color = ludo.Global.getludoPcolor(this.color);
            expLable.fromTo({ x: 0, y: 50 }, this.UI.head_box);
        }
        // if (!this.exp) {
        //     this.exp = new Laya.Text();
        //     this.exp.bold = true;
        //     this.exp.strokeColor = "#eeeeee";
        //     this.exp.stroke = 2;
        //     this.exp.fontSize = 26;
        //     this.exp.color = ludo.Global.getludoPcolor(this.color);
        //     this.exp.size(90, 30);
        //     this.exp.align = "center";
        //     this.UI.head_box.addChild(this.exp);
        // }
        // this.exp.visible = true;
        // this.exp.pos(0, 50);
        // this.exp.text = num + " EXP";
        yalla.Sound.playSound("exp_add");
        // if (!this.timeLine) {
        //     this.timeLine = new Laya.TimeLine();
        //     this.timeLine.addLabel("0", 0).to(this.exp, { y: -80 }, 600)
        //         .to(this.exp, { alpha: 0.01 }, 200);
        //     this.timeLine.on(Laya.Event.COMPLETE, this, () => {
        //         // this.timeLine.reset();
        //         this.exp.visible = false;
        //         this.exp.alpha = 1;
        //     })
        // }
        // this.timeLine.play("0", false);
    }
    win(num: number) {
        this.isWin = Boolean(num > 0);
        this.UI.dice_bg.visible = !this.isWin;
        this.head.showCrown(num);
        let playerInfo: playerShowInfo;
        if (this.gameType == GameType.LUDO) {
            playerInfo = ludoRoom.instance.getPlayerByIdx(this.idx);
            if (playerInfo) playerInfo.winIndex = num;
        } else {
            let playerInfo: playerShowInfo = yalla.data.snake.UserService.instance.room.getPlayerByIdx(this.idx);
            if (playerInfo) playerInfo.winIndex = num;
        }
        switch (num) {
            case 1:
                this.UI.rank1.visible = true;
                this.UI.rank2.visible = false;
                this.showName();
                break;
            case 2:
                this.UI.rank1.visible = false;
                this.UI.rank2.visible = true;
                this.showName();
                break;
            default:
                this.UI.rank1.visible = false;
                this.UI.rank2.visible = false;
                break;
        }
        this.clearBuff();
    }
    pause() {
        if (this.idx == yalla.Global.Account.idx && !yalla.Voice.instance.voiceOpen) {
            this.UI && this.UI.voice_ani.play(0, false, "stop");
            return;
        }
        if (!this.isMute) {
            this.UI.voice_ani.play(0, false, "pause");
            this.isPlay = false;
        }
    }
    stop() {
        if (this.idx == yalla.Global.Account.idx && !yalla.Voice.instance.voiceOpen) return;
        this.UI && (this.UI.voice_ani as Laya.Animation).gotoAndStop(0);
    }
    play() {
        if (!this.isMute) {
            this.timer.clear(this, this.pause);
            if (!this.isPlay) {
                this.UI.voice_ani.play(0, true, "play");
            }
            this.isPlay = true;
            this.timer.once(500, this, this.pause);
        }
    }
    mute() {
        if (this.UI) {
            this.UI.voice_ani.play(0, false, "mute");
            this.UI.voice_btn.offAll();
        }
    }
    public newRound() {
        this._flushluckDice = 0;
        return this;
    }
    pushDice(num: number, flushpush: boolean = false) {
        if (flushpush) this._luckDice = this._flushluckDice;
        this.hideName();
        this._diceArr.push(num);
        if (yalla.util.isThreeSix(this._diceArr)) {
            yalla.Sound.playSound("threeSix");
        }
        var url = '', poolName = "diceImg_" + num;
        if (this.luckDice > 0 && num === 6) {
            url = "game/shaizi_gold_6.png";
            this.luckDice--;
            poolName = "diceImg_glod";
        } else if (this.snailDice > 0 && num == 1) {
            url = "game/shaizi_snail_1.png";
            this.snailDice--;
            poolName = "diceImg_snail";
        } else {
            url = "game/shaizi_" + num + ".png";
            this.luckDice = 0;
        }
        let img = Laya.Pool.getItem(poolName);
        if (!img) {
            img = new Laya.Image();
            img.size(40, 44);
            img.skin = url;
            img.name = num.toString();
        }
        if ((this.UI.dice_box as Laya.HBox)._childs)
            img.x = this.UI.dice_box.numChildren * 50;
        this.UI.dice_box.addChild(img);
        this.UI.dice_box.refresh();
        return this;
    }
    /**
     * 移除骰子
     */
    popDice(num?: number) {
        var dice, index;
        if (!num) {
            dice = this.UI.dice_box.getChildAt(this.UI.dice_box.numChildren - 1);
            if (!dice) return;
            index = this._diceArr.length - 1;
            num = dice.name;
        } else {
            dice = this.UI.dice_box.getChildByName(num.toString());
            index = this._diceArr.indexOf(num);
        }
        if (index >= 0) {
            this._diceArr.splice(index, 1);
        }
        if (dice) {
            var poolName = "diceImg_" + num;
            if (dice.skin.indexOf("gold") > -1) poolName = "diceImg_glod";
            if (dice.skin.indexOf("snail") > -1) poolName = "diceImg_snail";
            dice.removeSelf();
            Laya.Pool.recover(poolName, dice);
        }
        return this;
    }
    flushDice(nums: Array<number> = [], is_Trust: boolean = false) {//用于替换clearDice+pushDice 刷新骰子列表的时候 道具模式下会把黄金骰子刷成白色的骰子,显示不对应
        // console.log("骰子数量", nums);
        if (String(nums) != String(this._diceArr)) {
            this.clearDice(is_Trust);
            nums.forEach(num => {
                this.pushDice(num, true);
            })
        }
    }
    //清空骰子
    clearDice(isTrust: boolean) {
        isTrust ? this.hideName() : this.showName();
        this.UI && this.UI.dice_box.removeChildren();
        this._diceArr = [];
        return this;
    }
    showChat(msg: any) {
        if (!this.chatBubble) {
            this.initBubble(this.data.talkSkinId || 11000);
        }
        // this.chatBubble.chat = msg.msg;
        this.chatBubble.onChat(msg);
        this.chatBubble.show();
    }
    private loadRLSk(sk: Laya.Skeleton) {
        if (!this.rlSk && !this.destroyed) {
            this.rlSk = sk;
            this.UI.RL.addChild(this.rlSk);
            this.rlSk.pos(20, 14);
            this.rlSk.scale(0.6, 0.6);
            //目前r+5等级标识和rl5一样
            if (this.dataSource) {
                let rlAnimationLv: number = this.dataSource.roylevel || 0
                rlAnimationLv = Math.min(5, rlAnimationLv);
                rlAnimationLv && this.rlSk.play(`R${rlAnimationLv}`, true);
            }
            yalla.SkeletonManager.instance.off(this._rlKey, this, this.loadRLSk);
        }
    }
    private loadVipSk(sk: Laya.Skeleton) {
        if (!this.vipSk && !this.destroyed) {
            this.vipSk = sk;
            this.UI.vip.addChild(this.vipSk);
            this.vipSk.pos(15, 14);
            this.vipSk.scale(0.6, 0.6);
            if (this.dataSource && this.dataSource.fPlayerInfo) this.vipSk.play("vip_" + this.dataSource.fPlayerInfo.viplevel, true);
            yalla.SkeletonManager.instance.off(this._vipKey, this, this.loadVipSk);
        }
    }

    set isLeft(boo: boolean) {
        if (yalla.Global.Account.isPrivate != 5) {
            this._isLeft = boo;
            this.head.isLeft = boo;
            if (boo) {
                this.clearBuff();
                this.showName();
                this.hideDice();
                this.UI.dice_bg.alpha = 0;
            }
        }
    }
    get isLeft(): boolean {
        return this._isLeft;
    }
    set isInChatOff(val: boolean) {
        if (!this._isLeft) this.head.isInChatOff = val;
    }

    setPlayerInfo(val: playerShowInfo, activated_id: number = null) {
        // val.roylevel = 4;
        // val.fPlayerInfo.viplevel = 2;
        this.dataSource = val;
        this.head.showFaceBox(val);
        // this.gift = val.giftId;
        if (val.realRoyLevel >= 4) {
            this.UI.nickName.text = '';
            if (!this._nameItem) {
                var nameItemUI: ui.publics.item.nameItemUI = new ui.publics.item.nameItemUI();
                this.UI.nickName.parent.addChild(nameItemUI);
                nameItemUI.height = 24;
                nameItemUI.y = 2;
                this._nameItem = new yalla.common.NameItem(nameItemUI, this.UI.nickName);
            }
            this._nameItem.gameName(val, 13);
        } else {
            if (this._nameItem && this._nameItem.ui) this._nameItem.ui.removeSelf();
            yalla.Emoji.lateLable(yalla.util.filterName(val.fPlayerInfo.nikeName), this.UI.nickName);
        }

        this.name = val.fPlayerInfo.idx.toString();
        this.isPass = Boolean(val.isFighted);
        this.isLeft = Boolean(val.isLeft);
        this.idx = val.fPlayerInfo.idx;
        if (val.fPlayerInfo.viplevel > 0) {
            if (!this.vipSk && (!this._vipKey || this._vipKey.length < 1)) {
                this._vipKey = "vip" + String(this.idx);
                this.UI.vip.visible = true;
                var __s = yalla.SkeletonManager.instance;
                __s.on(this._vipKey, this, this.loadVipSk);
                __s.initSk("vip/xunzhang", this._vipKey);
            }
        } else {
            this.UI.vip.removeSelf();
        }
        var royalLevel = yalla.util.getRoyalLevel(val);
        if (royalLevel > 0) {
            if (!this.rlSk && (!this._rlKey || this._rlKey.length < 1)) {
                this._rlKey = "rl" + String(this.idx);
                this.UI.RL.visible = true;
                var __s = yalla.SkeletonManager.instance;
                __s.on(this._rlKey, this, this.loadRLSk);
                __s.initSk("royallevel/huangguan", this._rlKey);
            }
        } else {
            this.UI.RL.removeSelf();
        }
        this.callLater(() => {
            var hBox = this.UI.name_box.getChildAt(0);
            if (hBox && hBox.width < 180) {
                if (this.side <= 1) {
                    hBox.left = (180 - hBox.width) / 2;
                } else {
                    hBox.right = (180 - hBox.width) / 2;
                }
            }
        })
        if (val.fPlayerInfo.idx == yalla.Global.Account.idx)
            this.UI.dice_bg.alpha = 1;
        // this.updateMuteVoice();
        this.isInChatOff = Boolean(val.isInChatOff);//0聊天  1屏蔽
        this.win(val.winIndex);
        this.hideBuff();
        if (val.buff && val.buff.length) {
            this._luckDice = 0;
            this.newRound();
            //从后台切回来的时候,正在投掷骰子此时复盘buff这里的长度是0,导致显示了白色的6,
            //如果不处理会不会导致多回合之前的道具仍然显示?? 待验证
            for (var i = 0; i < val.buff.length; i++) {
                this.getBuff(val.buff[i])
            }
        }
        if (this.gameType == GameType.LUDO) {
            var ludoRoomData = ludoRoom.instance;
            if (ludoRoom.instance.isTeamGame()) {
                var team = ludoRoomData.getTeamNameByIdx(this.idx);
                team && this.head && this.head.showTeam(team);
            }
        }
    }
    get data(): playerShowInfo {
        return this.dataSource;
    }
    set color(color: number) {
        this.head.color = color;
    }
    get color(): number {
        return this.head.color;
    }
    set side(side: number) {
        this._side = side;
        switch (side) {
            case 0:
                this.UI = new ui.ludo.sub.ludo_player_0UI();
                if (yalla.Global.Account.idx == this.idx) {
                    (this.UI.voice_ani as Laya.Animation).source = "publics/ani/ani_voice_me.ani";
                    (this.UI.voice_btn as Laya.Button).mouseEnabled = false;
                    this.UI.voice_ani.play(0, false, "stop");
                } else {
                    this.UI.voice_ani.play(0, false, "pause");
                }
                this.head && this.head.btn_gift.pos(60, 0);
                break;
            case 1:
                this.UI = new ui.ludo.sub.ludo_player_1UI();
                this.UI.voice_ani.play(0, false, "pause");
                if (this.offLine) {
                    this.UI.name_box.rotation = this.UI.head_box.rotation = this.UI.rank.rotation = this.dice.rotation = 180;
                    this.dice.pos(64, 50);
                }
                this.head && this.head.btn_gift.pos(60, 0);
                break;
            case 2:
                this.UI = new ui.ludo.sub.ludo_player_2UI();
                this.UI.voice_ani.play(0, false, "pause");
                this.dice.scaleX = -1;
                if (this.offLine) {
                    this.UI.name_box.rotation = this.UI.head_box.rotation = this.UI.rank.rotation = this.dice.rotation = 180;
                    this.dice.pos(60, 50);
                }
                if (this.head) {
                    this.head.team_icon.pos(4, 60);
                    this.head.btn_gift.pos(0, 0);
                    this.head.crown_1.scaleX = -0.7;
                    this.head.crown_2.scaleX = -0.7;
                    this.head.crown_1.pos(81, 17);
                    this.head.crown_2.pos(79, 18);
                }
                break;
            case 3:
                this.UI = new ui.ludo.sub.ludo_player_3UI();
                this.UI.voice_ani.play(0, false, "pause");
                this.dice.scaleX = -1;
                if (this.head) {
                    this.head.team_icon.pos(4, 60);
                    this.head.btn_gift.pos(0, 0);
                    this.head.crown_1.scaleX = -0.7;
                    this.head.crown_2.scaleX = -0.7;
                    this.head.crown_1.pos(81, 17);
                    this.head.crown_2.pos(79, 18);
                }
                break;
            default:
                break;
        }
        // this.head && (this.head.btn_gift.visible = !this.offLine);
        this.head.side = side;
        this.addChild(this.UI);
    }
    get side(): number {
        return this._side;
    }
}