
class LudoBanner extends Laya.EventDispatcher {
    public view: ui.ludo.sub.banner_enUI = null;
    private _status: number = 0;
    private _isSpectator: boolean = false;
    private rank: ludo.RoomInfoUI = null;
    private _addTw: Laya.Tween = null;
    private _curCount: number = 0;
    private _winCount: number = 0;
    private _winSecondCount: number = 0;
    private _gameMapCount: number = 0;
    private _arriveCount: number = 0;
    private _leftShow: number = 3000;
    constructor() {
        super();
        this.view = yalla.Font.isRight() ? new ui.ludo.sub.banner_arUI() : new ui.ludo.sub.banner_enUI() as any;
        this.view.store_btn.visible = Boolean(yalla.Global.Account.sameSkinBuySwitch);
    }
    public set hairHeight(val: number) {
        this.view.head.top = val;
        if (val > 0) this.view.bg.height = 105 + val;
        else this.view.bg.height = 105;
    }
    public set curCount(num: number) {
        this._curCount = num || 0;
        this.view && (this.view.curCount.text = String(this._curCount));
    }
    public get curCount(): number { return this._curCount; }

    public set winCount(num: number) {
        this._winCount = num || 0;
        this.view && (this.view.winCount.text = `+${this._winCount}`);
    }
    public get winCount(): number { return this._winCount; }

    public set winSecondCount(num: number) {
        this._winSecondCount = num || 0;
        this.view && (this.view.winCount2.text = `+${this._winSecondCount}`);
    }
    public get winSecondCount(): number { return this._winSecondCount; }
    public set gameMapCount(num: number) {
        this._gameMapCount = num || 0;
        this.view && (this.view.gameMapCount.text = `+${this._gameMapCount}`);
    }
    public get gameMapCount(): number { return this._gameMapCount; }

    public set arriveCount(num: number) {
        this._arriveCount = num || 0;
        this.view && (this.view.arriveCount.text = `+${this._arriveCount}`);
    }
    public get arriveCount(): number { return this._arriveCount; }

    public set isSpectator(val: boolean) {
        this._isSpectator = val;
        this.view.muteSpectator_btn.visible = !val;
        if (Boolean(yalla.Global.Account.sameSkinBuySwitch)) this.view.store_btn.visible = !val;
    }
    public set vip(val: boolean) {
        this.view.bg.skin = val ? "public/top-bg.png" : "game/image_ludo_bg0.png";
    }
    public set status(val: number) {
        this._status = val;
        switch (val) {
            case 1://仅显示观战按钮
                this.view.audience_btn.visible = true;
                break;
            case 2://显示观战和道具掉落按钮
                this.view.audience_btn2.visible = true;
                this.view.eventProp_btn.visible = true;
                this.setPropSrc();
                this.refreshActivityPropInfo();
                break;
        }
        this.addEvent();
        this.vip = ludoRoom.instance.isVipGame;
    }
    public get status(): number { return this._status }
    public set audienceNum(val: number) {
        switch (this._status) {
            case 1://仅显示观战按钮
                this.view.audience_num.text = String(val);
                break;
            case 2://显示观战和道具掉落按钮
                this.view.audience_num2.text = String(val);
                break;
        }

    }
    public set audienceVisible(val: boolean) {
        switch (this._status) {
            case 1:
                this.view.audience_btn.visible = val;
                break;
            case 2:
                this.view.audience_btn2.visible = val;
                break;
        }
    }
    public get audienceVisible(): boolean {
        return this.view.audience_btn.visible || this.view.audience_btn2.visible;
    }
    public refreshActivityPropInfo() {
        var info: ActivityPropInfo = ludoRoom.instance.activityPropInfo;
        if (info && this.view) {
            this.gameMapCount = info.gameMapCount;
            this.arriveCount = info.arriveCount;
            this.winCount = info.winCount;
            this.curCount = info.curCount;
            if (info.winSecondCount && !ludoRoom.instance.isTeamGame() && ludoRoom.instance.getSitPlayers().length == 4) {
                this.winSecondCount = info.winSecondCount;
                this.view.propWin2.visible = true;
                this.view.eventProp_info.height = 274;
            } else {
                this.view.propWin2.visible = false;
                this.view.eventProp_info.height = 221;
            }
        }
    }
    public hideRank() {
        if (this.rank && this.view) {
            this.rank.visible = false;
            this.view.timer.clear(this.view, this.hideRank);
        }
    }
    public hidePropInfo() {
        if (this.view) {
            this.view.timer.clear(this.view, this.hidePropInfo);
            this.view.eventProp_info.visible = false;
        }
    }
    public setGroup(val) {
        if (val == 1) {
            this.view.magic.visible = true;
            this.view.magic.on("click", this, this.showMagicRule);
        } else {
            this.view.magic.removeSelf();
            this.view.magic.destroy(true);
        }
    }
    public onCurCountChange(curCount: number, delay: boolean) {
        if (curCount > this.curCount) {
            if (yalla.Global.isFouce && delay) Laya.timer.once(600, this, this.addPropNum, [curCount - this.curCount]);
            else this.addPropNum(curCount - this.curCount);
        }
    }
    public onPlayerWin(index: number) {
        if (index == 1 || index == 2) {
            let winCount = index == 1 ? this.winCount : this.winSecondCount;
            this.onCurCountChange(this.curCount + winCount, true);
        }
    }
    public addPropNum(num: number) {
        if (num > 0 && this.view) {
            this.curCount += num;
            if (yalla.Global.isFouce) {
                this.view.eventProp_add.dataSource = { visible: true, text: `+${String(num)}`, y: 7 };
                this._addTw = Laya.Tween.to(this.view.eventProp_add, { y: -23 }, 600, null, Laya.Handler.create(this, this.hidePropAdd), 0, true, true);
            }
        }
    }
    private hidePropAdd() {
        this.clearAddTw();
        this.view && (this.view.eventProp_add.visible = false);
    }
    private clearAddTw() {
        this._addTw && this._addTw.complete();
        this._addTw && this._addTw.clear();
        this._addTw = null;
    }
    private setPropSrc(url: string = ludoRoom.instance.activityPropUrl) {
        url && yalla.File.downloadImgByUrl(url, (localPath: string) => {
            Laya.loader.load(localPath, Laya.Handler.create(this, (e) => {
                if (e && this.view && this.view.prop_icon) {
                    this.view.prop_icon.skin = localPath;
                }
            }));
        })
    }
    public addEvent() {
        if (this.view) {
            switch (this._status) {
                case 1://仅显示观战按钮
                    this.view.audience_btn.on("click", this, this.audienceHandler)
                    break;
                case 2://显示观战和道具掉落按钮
                    this.view.audience_btn2.on("click", this, this.audienceHandler);
                    this.view.eventProp_btn.on("click", this, this.propInfoHandler);
                    break;
            }
            this.view.system_btn.on("click", this, this.systemHander);
            this.view.rule_btn.on("click", this, this.ruleHander);
            this.view.sound_btn.on("click", this, this.soundHander);
            this.view.rank_btn.on("click", this, this.rankHander);
            this.view.exit_btn.on("click", this, this.exitHander);
            this.view.store_btn.on("click", this, this.showSameStore);
            this.view.muteSpectator_btn.on("click", this, this.muteSpectator);
        }
    }
    public removeEvent() {
        if (this.view) {
            this.view.audience_btn.off("click", this, this.audienceHandler)
            this.view.eventProp_btn.off("click", this, this.propInfoHandler);
            this.view.audience_btn2.off("click", this, this.audienceHandler);
            this.view.system_btn.off("click", this, this.systemHander);
            this.view.rule_btn.off("click", this, this.ruleHander);
            this.view.sound_btn.off("click", this, this.soundHander);
            this.view.rank_btn.off("click", this, this.rankHander);
            this.view.exit_btn.off("click", this, this.exitHander);
            this.view.store_btn.off("click", this, this.showSameStore);
            this.view.magic.off("click", this, this.showMagicRule);
            this.view.muteSpectator_btn.off("click", this, this.muteSpectator);
        }
    }
    private showSameStore() {//显示快速商店
        var sitPlayers = ludoRoom.instance.getSitPlayers();
        if (sitPlayers) {
            var players = []
            var base64Encode = laya.utils.Browser.window.Base64.encode;
            sitPlayers.forEach(val => {
                var f = val.fPlayerInfo
                if (f.idx != yalla.Global.Account.idx)
                    players.push({
                        idx: f.idx,
                        faceUrl: base64Encode(f.faceUrl),
                        nickName: base64Encode(f.nikeName),
                        color: val.color
                    })
            })
            yalla.Native.instance.showSameStyleStore(players, ludo.Global.myGold, ludo.Global.myMoney);
        }

    }
    public turnmuteSpectator() {
        if (!ludoRoom.instance.isPrivate && !ludoRoom.instance.isDuel) //this.gameUI.audience_btn.visible = !ludo.muteSpectator.isMute;
            this.audienceVisible = !ludo.muteSpectator.isMute;
        if (!this.view.muteSpectator_btn || this.view.muteSpectator_btn.numChildren <= 0) return;
        var img = this.view.muteSpectator_btn.getChildAt(0) as Laya.Image;
        // if (!img) return;
        if (ludo.muteSpectator.isMute) {
            img.skin = "game/ChatOFF.png";
            this.view.muteSpectator_lb.text = "Chat OFF";
        } else {
            img.skin = "game/ChatON.png";
            this.view.muteSpectator_lb.text = "Chat ON";
        }
    }
    private exitHander() {
        this.event("exit");
    }
    public showMagicRule() {
        yalla.Native.instance.showGameRules(GameType.LUDO, ludoRoom.instance.type, 2);
        this.view.magic.mouseEnabled = false;
        Laya.timer.once(1000, this, () => {
            this.view.magic.mouseEnabled = true;
        })
    }
    private rankHander(e: Laya.Event) {//奖励信息按钮事件
        e.stopPropagation();
        yalla.Sound.playSound("click");
        if (this.rank) {
            var visb = this.rank.visible;
            this.pageHander()
            this.rank.visible = !visb;
            !visb && this.view.timer.once(this._leftShow, this, this.hideRank);
        }
        if (!this._isSpectator) yalla.Native.instance.mobClickEvent(buryPoint.GAME_REWARD);//埋点 奖励
        yalla.event.YallaEvent.instance.event('click_gameTopview');
    }
    private ruleHander() {
        switch (yalla.Global.Account.isPrivate) {
            case 5:
                yalla.Native.instance.showGameRules(GameType.LUDO, ludoRoom.instance.type, 3);
                break;
            case 6:
                yalla.Native.instance.showGameRules(GameType.LUDO, 0, 4);
                break;
            case 7:
                yalla.Native.instance.showGameRules(GameType.LUDO, 1, 4);
                break;
            case 9:
                yalla.Native.instance.showGameRules(GameType.LUDO, 1, 6);
                yalla.util.clogDBAmsg("10191");
                break;
            case 10:
                yalla.Native.instance.showGameRules(GameType.LUDO, 1, 7);
                break;
            default:
                yalla.Native.instance.showGameRules(GameType.LUDO, ludoRoom.instance.type);
                break;
        }
        if (!this._isSpectator) yalla.Native.instance.mobClickEvent(buryPoint.GAME_SETTING_RULES);//埋点 查看规则
        (this.view.rule_btn as Laya.Button).mouseEnabled = false;
        Laya.timer.once(1000, this, () => {
            (this.view.rule_btn as Laya.Button).mouseEnabled = true;
        })
    }
    private soundHander(e: Laya.Event) {
        yalla.Sound.sound = !yalla.Sound.sound;
        this.turnSound();
    }
    private systemHander(e: Laya.Event) {//设置按钮事件
        e.stopPropagation();
        yalla.Sound.playSound("click");
        if (this.view) {
            this.turnSound();
            var visb = this.view.system.visible;
            this.pageHander();
            this.view.system.visible = !visb;
        }
        if (!this._isSpectator) yalla.Native.instance.mobClickEvent(buryPoint.GAME_SETTING);//埋点 设置
        yalla.event.YallaEvent.instance.event('click_gameTopview');
    }
    public setRank() {//设置榜单的信息
        this.rank = new ludo.RoomInfoUI();
        this.view.timer.callLater(this, () => {
            this.view && !this.view.destroyed && this.view.head.addChild(this.rank);
        })
        if (this.rank) {
            this.view.timer.once(this._leftShow, this, this.hideRank);
        }
    }
    public resetSysLayout() {
        var style = yalla.Font.isRight() ? "right" : "left";
        var system = this.view.system;
        var index = 0;
        system._childs.forEach((el) => {
            if (el.visible && el instanceof Laya.Button) {
                el[style] = index * 140 + 10;
                index++;
            }
        });
        system.width = index * 140 + 12;
    }

    private propInfoHandler(e: Laya.Event) {
        e.stopPropagation();
        yalla.Sound.playSound("click");
        if (this.view) {
            var visb = this.view.eventProp_info.visible;
            this.pageHander();
            this.view.eventProp_info.visible = !visb;
            !visb && this.view.timer.once(this._leftShow, this, this.hidePropInfo);
            yalla.util.clogDBAmsg("10226");
        }
    }
    protected turnSound() {
        if (!yalla.Sound.sound) {
            (this.view.sound_btn.getChildAt(0) as Laya.Image).skin = "game/voice_on.png";
            this.view.sound_btn.label = "ON";
            if (!this._isSpectator) yalla.Native.instance.mobClickEvent(buryPoint.GAME_SETTING_OPENSOUND);//埋点 打开声音
        } else {
            (this.view.sound_btn.getChildAt(0) as Laya.Image).skin = "game/voice_off.png";
            this.view.sound_btn.label = "OFF";
            if (!this._isSpectator) yalla.Native.instance.mobClickEvent(buryPoint.GAME_SETTING_CLOSESOUND);//埋点 关闭声音
        }
    }
    private audienceHandler() {
        yalla.Native.instance.mobClickEvent(buryPoint.GAME_SPECTATER);
        // if (ludoRoom.instance.getAudiencePlayers().length = 0) { 
        //     //todo:1.3.9.1 toast提示 暂无玩家观战
        //     return;
        // }
        Audience.instance.popup(true, true);
    }
    public updateAudiencePLayer() {
        this.audienceNum = ludoRoom.instance.getAudiencePlayers().length;
        if (this.audienceVisible && Audience.instance.displayedInStage) {
            Audience.instance.list.array = ludoRoom.instance.getAudiencePlayers();
        }
    }
    public updateMoney() {
        // this.view.store_btn.label = yalla.util.filterNum(ludo.Global.myGold);
        yalla.common.InteractiveGift.Instance.updateMoney(ludo.Global.myMoney, ludo.Global.myGold);
    }
    public muteSpectator(e: Laya.Event = null) {
        if (e) ludo.muteSpectator.isMute = 1 - ludo.muteSpectator.isMute;
        if (ludo.muteSpectator.isMute)
            ludo.GameSocket.instance.chatOff();
        else
            ludo.GameSocket.instance.cancleChatOff();
        this.turnmuteSpectator();
    }
    public pageHander() {
        this.view.system.visible = false;
        this.view.eventProp_info.visible = false;
        this.rank && this.hideRank();
        this.view && this.hidePropInfo();
    }
    public clear() {
        this.pageHander();
        this.removeEvent();
        if (this.rank) {
            this.rank.destroy(true);
            this.rank = null;
        }
        if (this.view) {
            this.view.timer.clearAll(this)
            this.view.destroy();
        }
        Laya.timer.clear(this, this.addPropNum);
        this.clearAddTw();
    }
}