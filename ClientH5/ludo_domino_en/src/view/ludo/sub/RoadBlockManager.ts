

class RoadBlockManager extends Laya.EventDispatcher {
    public static _instance: RoadBlockManager = null;
    public static get instance(): RoadBlockManager {
        return this._instance ? this._instance : this._instance = new RoadBlockManager();
    }

    constructor() {
        super();
        this.init();
    }
    public tpl: Laya.Templet = null;
    public done: boolean = false;
    private init() {
        this.tpl = new Laya.Templet();
        this.tpl.once(Laya.Event.COMPLETE, this, this.loadedSk);
        this.tpl.loadAni("res/sk/roadblock/luzhang1.sk");
    }
    private loadedSk() {
        this.event(Laya.Event.COMPLETE, this.tpl);
        this.done = true;
    }
}

class RoadBlock extends Laya.Sprite {
    private sk: Laya.Skeleton = null;
    private isHide: boolean = false;
    constructor(__name) {
        super();
        this.init();
        this.name = __name;
    }
    private init() {
        RoadBlockManager.instance.done ? this.create() : RoadBlockManager.instance.on(Laya.Event.COMPLETE, this, this.create);
    }
    private create() {
        if (!this.destroyed && !this.isHide) {
            this.sk = RoadBlockManager.instance.tpl.buildArmature(0);
            this.addChild(this.sk);
            this.sk.y = 4;
            this.sk.play(0, false, true, 1, 2);
        }
    }
    public hide() {
        if (this.sk) {
            this.sk.play(0, false);
            this.sk.once(Laya.Event.STOPPED, this, this.onStop);
        }
        this.isHide = true;
    }
    private onStop() {
        this.removeSelf();
        this.sk && this.sk.destroy();
        this.sk = null;
        this.destroy();
    }
}