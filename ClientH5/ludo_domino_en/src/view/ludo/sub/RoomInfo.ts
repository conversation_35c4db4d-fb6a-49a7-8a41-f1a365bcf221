module ludo {
    /**
     * 房间排名奖励信息显示UI 依赖于ludoData Ludo.Room.ts中的数据
     */
    export class RoomInfoUI extends Laya.Box {
        private infoView: any = null;
        constructor() {
            super();
            this.size(460, 290);
            this.init();
        }
        private init() {
            if (ludoRoom.instance.isTeamGame()) {//组队模式
                if (yalla.Font.isRight()) {//阿语
                    this.infoView = new ui.ludo.sub.teamRoomInfoarUI();
                    this.arInit();
                } else {
                    this.infoView = new ui.ludo.sub.teamRoomInfoenUI();
                    this.enInit();
                }
                this.teamGameinit();
            } else {//正常模式
                if (yalla.Font.isRight()) {//阿语
                    this.infoView = new ui.ludo.sub.normalRoomInfoarUI();
                    this.arInit();
                } else {
                    this.infoView = new ui.ludo.sub.normalRoomInfoenUI();
                    this.enInit();
                }
                this.normalGameInit();
            }
            this.addChild(this.infoView);
        }
        private enInit() {
            this.left = 106;
            this.top = 95;
        }
        private arInit() {
            this.left = 216;
            this.top = 95;
        }
        private teamGameinit() {//组队模式UI和数据绑定
            var room = ludoRoom.instance;
            if (this.infoView) {
                this.infoView.rank_roomType.text = ludoRoom.instance.roomModeName(room.type) || "CLASSIC";
                var royalty = yalla.Global.Account.royalty ? yalla.Global.Account.royalty : 0;
                this.infoView.rank_1.text = yalla.util.filterNum(room.cost * 4 * (1 - royalty / 100) / 2 || 0);
                this.infoView.rank_roomId.text = room.ID;
                var idx: number = room.teamInfo ? room.teamInfo.teamBPlayer1 : null;
                var player: playerShowInfo = room.getPlayerByIdx(idx);
                if (player && (player.color == 0 || player.color == 2)) {//红黄是TeamB
                    (this.infoView.teamA_img as Laya.Image).skin = `game/Team2.png`;
                    (this.infoView.teamB_img as Laya.Image).skin = `game/Team1.png`;
                }
            }
        }

        private normalGameInit() {//非组队模式UI和数据绑定
            var room = ludoRoom.instance;
            this.infoView.rank_roomType.text = ludoRoom.instance.roomModeName(room.type) || "CLASSIC";
            this.infoView.rank_roomId.text = room.showID + "";
            switch (yalla.Global.Account.isPrivate) {
                case 1://私人房间
                    (this.infoView.rankP1 as Laya.Image).visible = false;
                    (this.infoView.rankP2 as Laya.Image).visible = false;
                    this.infoView.bg.height = 70;
                    break;
                case 3://锦标赛
                    this.infoView.rank_1.text = yalla.util.filterNum(room.cost || 0);
                    (this.infoView.rankP2 as Laya.Image).visible = false;
                    this.infoView.bg.height = 150;
                    break;
                default:
                    var [rankNum1, rankNum2] = room.getAward();
                    this.infoView.rank_1.text = yalla.util.filterNum(rankNum1);
                    if (rankNum2 > 0) {
                        this.infoView.rank_2.text = yalla.util.filterNum(rankNum2);
                    } else {
                        (this.infoView.rankP2 as Laya.Image).visible = false;
                        this.infoView.bg.height = 150;
                    }
                    break;
            }
        }
    }
}