class Sparkler {
    private tpl: Laya.Templet = new Laya.Templet();
    private streamerSK: Laya.Skeleton = null;
    private sparklerSK: Laya.Skeleton = null;
    private parent: Laya.Sprite = null;
    constructor(parent: Laya.Sprite) {
        this.parent = parent;
        Laya.loader.load([
            { url: yalla.getSkeleton("sparkler/heiyemoshi"), type: Laya.Loader.BUFFER },
            { url: yalla.getSkeletonPng('sparkler/heiyemoshi'), type: Laya.Loader.IMAGE },
            { url: yalla.getSkeletonPng('sparkler/heiyemoshi2'), type: Laya.Loader.IMAGE },
        ], Laya.Handler.create(this, this.resComplete), null, null, 0, true, "Sparkler");
    }
    private resComplete() {
        this.tpl.once(Laya.Event.COMPLETE, this, this.loaded);
        this.tpl.loadAni(yalla.getSkeleton("sparkler/heiyemoshi"));
    }
    private loaded() {
        if (this.parent) {
            this.sparklerSK = this.tpl.buildArmature(0);
            this.sparklerSK.once(Laya.Event.STOPPED, this, this.clear);
            this.streamerSK = this.tpl.buildArmature(1);
            this.parent.addChild(this.streamerSK);
            this.parent.addChild(this.sparklerSK);
            this.sparklerSK.playbackRate(0.9);
            this.sparklerSK.blendMode = "lighter";
            this.sparklerSK.pos(this.parent.width >> 1, this.parent.height >> 1);
            this.streamerSK.pos(this.parent.width >> 1, this.parent.height >> 1);
            this.sparklerSK.play(0, false);
            this.streamerSK.play(1, false);
        }
    }
    private clear() {
        if (this.tpl) {
            this.tpl.destroy();
            this.tpl = null;
        }
        if (this.streamerSK) {
            this.streamerSK.stop();
            this.streamerSK.destroy(true);
            this.streamerSK = null;
        }
        if (this.streamerSK) {
            this.streamerSK.stop();
            this.streamerSK.destroy(true);
            this.streamerSK = null;
        }
        this.parent = null;
        Laya.loader.clearResByGroup("Sparkler");
    }
}