class TerminusAni extends Laya.Box {
    private sk: Laya.Skeleton;
    private tm: Laya.Templet = new Laya.Templet();
    constructor() {
        super();
        this.tm.on(Laya.Event.COMPLETE, this, () => {
            if (!this.tm) return;
            this.sk = this.tm.buildArmature(0);
            this.addChild(this.sk);
            this.sk.on(Laya.Event.STOPPED, this, () => {
                this.visible = false;
            })
        });
        this.tm.loadAni("res/sk/endpoint/skeleton.sk");
    }
    clear() {
        if (this.tm) {
            this.tm.destroy();
            this.tm = null;
        }
        if (this.sk) {
            this.sk.destroy();
            this.sk = null;
        }
    }
    play() {
        if (!!this.sk) {
            this.sk.play(0, false);
        }
    }
}