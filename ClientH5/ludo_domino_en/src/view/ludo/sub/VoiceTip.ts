class VoiceTip extends ui.publics.dialog.voiceTipUI {
    constructor(point: Laya.Point) {
        super();
        // this.bg.graphics.drawRect(0, 0, Laya.stage.width, Laya.stage.height, "#000000");
        var tip = new Laya.Label();
        // tip.text = "Click to open the microphone";
        tip.text = "Your microphone is working";
        tip.fontSize = 22;
        this.tip_bg.size(tip.width + 20, tip.height + 40);
        this.tip_bg.addChild(tip);
        if (point.x > Laya.stage.width / 2) {
            this.tip_bg.scaleX = -1;
            tip.scaleX = -1;
            tip.pos(10 + tip.width, 20);
        } else {
            tip.pos(10, 20);
        }
        this.tip.pos(point.x + 30, point.y);
    }
}