class BaseMain {
    protected _gameView: any;
    constructor() { }

    public get gameView(): any {
        return this._gameView;
    }

    /**
     * 切入后台
     */
    public onBlur(): void {
        /**TODO::1.4.3 所有游戏在ios下，键盘打开情况下切后台把键盘收起来 */
        if (yalla.Native.instance.deviceType == DeviceType.IOS) {
            yalla.event.YallaEvent.instance.event(yalla.data.EnumCustomizeCmd.ResetInputTextFocus);
        }
    }
    /**
     * 切到前台
     */
    public onForce(): void {
    }
    /**
     * 重连倒计时结束
     */
    public onReTimeOver() {

    }
    /**
    * 新手页
    * @param msg 
    */
    public onNoviceGame(a, b, c, d, f) {
    }
    /**
     * onResize
     */
    public onResize() {

    }
    /**
     * 重新开始游戏
     */
    public restartGame(): void {
    }

    /**
     * 登陆游戏
     * @param msg 
     */
    public onLogin(msg: any): void {
        yalla.Global.Account = msg;
        // Laya.stage.renderingEnabled = true;
    }

    /**
     * 离线游戏
     */
    public onOffGame(msg: any): void {
        // Laya.stage.renderingEnabled = true;
    }

    /**
     * 断线重连
     * 如果loading中断线重连，则移除loading
     */
    public onReconnect(): void {
    }

    /**
     * 退出游戏
     * @param msg 
     */
    public onQuitGame(msg: any): void {
        yalla.Voice.instance.levelGameRoom();
    }
    /**
     * 网络状态发生改变
     */
    // public netStateChange(json) { }
    /**
     * 大厅socket链接情况
     */
    public connectStateChange(json) { }
    /**
     * onKickedOut其它设备登录被踢出游戏
     */
    public onKickedOut() {
        yalla.Voice.instance.levelGameRoom();
    }
    public onWebSocketOpen() {
    }
    public updateMoney() {

    }

    public backHallClear(): void {
        yalla.Native.instance.clear();
        // if (yalla.Voice.instance.voiceOpen) //TODO::无论是否开启语音，都离开房间；因被顶号会有问题
        // yalla.Voice.instance.levelGameRoom();
        yalla.Voice.instance.levelGameRoom_android();
        yalla.common.InteractiveGift._instance && yalla.common.InteractiveGift.Instance.clear();
        yalla.common.Tip._instance && yalla.common.Tip.instance.close();
        yalla.common.ChatManager._instance && yalla.common.ChatManager.instance.clear();
        yalla.SkeletonManager._instance && yalla.SkeletonManager.instance.clear();
        yalla.common.TipManager._instance && yalla.common.TipManager.instance.clear();
        yalla.common.TipItem._instance && yalla.common.TipItem.instance.clear();
        yalla.data.ChatDataPool._instance && yalla.data.ChatDataPool._instance.clear();
        yalla.CLogDBA._instance && yalla.CLogDBA.instance.clear();
        yalla.util.ViewExclusion._instance && yalla.util.ViewExclusion.instance.clear();
        ludo.muteSpectator.clear();
        yalla.Native.instance.closeUserProfile();
        yalla.Native.instance.closeReportMsg();
        yalla.Global.backStatusHash = null;
    }

    public changeToGame(): void { }
    public backChampion(d: any): void { }

    public clear(): void {
        ludo.Global.myInfo = {
            playerShowInfo: null,
            gold: 0,
            money: 0,
            isRecharged: false,
            exp: null
        };
        yalla.Voice.instance.reset();
        yalla.Global.ProtocolMsgIndex = 0;
        yalla.Global.HallNetState = 0;
        yalla.Global.onLoseNextTime = 0;
        yalla.Global.ChatLastMsg = '';
        yalla.Global.nativeIputMsg = '';
        yalla.Global.ChatIndex = 0;
        yalla.Global.ChatMsgList = [];
        // yalla.Global.loseNet_interval = 6000;
        // yalla.Global.heart_interval = 5000;
        // yalla.Global.DomainTypeInfo = null;
        Laya.stage.removeChildByName("resultView");
        yalla.Global.onAccountLoginMsg = null;
        yalla.common.connect.ReconnectControl.instance.hideConnect();
        yalla.common.Confirm._instance = null;
        yalla.common.Tip._instance = null;
        yalla.common.ChatManager._instance = null;
        yalla.SkeletonManager._instance = null;
        yalla.common.BubbleReport._instance = null;
        yalla.common.InteractiveGift._instance = null;
        yalla.common.InteractiveGiftAnimation._instance = null;
        yalla.common.TipManager._instance = null;
        yalla.data.ChatDataPool._instance = null;
        yalla.util.ViewExclusion._instance = null;
        yalla.CLogDBA._instance = null;
        // yalla.Friends.hasSendFriendHash = {};
        yalla.common.TipItem._instance = null;
        // yalla.Global.DomainTypeInfo = null;
        Laya.Dialog.closeAll();
    }
}

