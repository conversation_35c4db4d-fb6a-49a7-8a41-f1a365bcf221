module yalla.common {
    /**
     * 绘制计时动画
     */
    export class CdAni extends Laya.Sprite {
        private _r: number;
        private running: Boolean;
        private color: string;
        public ms: number;
        public angle: number;
        public step: number;
        public msecond: number;     //剩余时间
        public alarm: boolean = true;//倒计时音效
        public InCD: boolean = false;
        
        private startTime: number;
        private totalDuration: number;
        private updateInterval: number = 50; // 从100ms降到50
        
        constructor() {
            super();
        }

        public init(r: number, ms: number, color?: number): void {
            this._r = r;
            this.ms = ms;
            this.totalDuration = ms;
            this.angle = -90;
            this.running = true;
            this.color = "#50f254";

            this.on(Laya.Event.REMOVED, this, () => {
                this.timer.clearAll(this);
            })
            this.mouseThrough = true;
        }
        
        public start(ms?: number, overMs?: number) {
            this.InCD = true;
            if (!!ms) {
                this.ms = ms;
                this.totalDuration = ms;
            }
            // 记录开始时间
            this.startTime = Laya.Browser.now();
            if (!!overMs) {// 如果有已过去的时间，调整开始时间
                this.startTime -= overMs;
            }
            this.startTime -= 500;
            this.timer.clear(this, this.update);

            this.timer.loop(this.updateInterval, this, this.update);
            return this;
        }

        private update() {
            if (!this.graphics) return;
            
            // 根据实际经过时间计算当前角度
            const currentTime = Laya.Browser.now();
            const elapsedTime = currentTime - this.startTime;
            const progress = Math.min(elapsedTime / this.totalDuration, 1);
            if (progress > 1) {
                this.end();
                return;
            }

            this.angle = -90 + (progress * 360);
            
            // 更新剩余时间
            this.msecond = Math.floor(elapsedTime / 100);
            
            this.graphics.clear();
            this.graphics.drawPie(this._r, this._r, this._r, this.angle, 270, this.color);
            this.alpha = 0.75;
            
            // 倒计时5s的音效
            if (this.alarm && this.msecond % 100 == 0) {
                var sencond = this.ms - this.msecond * 100;
                if (sencond > 0 && sencond <= 5000) {
                    yalla.Sound.playSound('last_sec');
                }
            }
        }

        public resetParams(ms: number, overMs?: number): void {
            if (!!ms) {
                this.ms = ms;
                this.totalDuration = ms;
                if (!!overMs) {
                    this.startTime = Laya.Browser.now() - overMs;
                }
            }
        }

        public end() {
            this.InCD = false;
            this.angle = -90;
            this.msecond = 0;
            this.graphics && this.graphics.clear();
            this.timer.clear(this, this.update);
            return this;
        }

        public clear(): void {
            this.graphics && this.graphics.clear();
            this.timer.clearAll(this);
            this.removeSelf();
            this.destroy();
        }
    }
}