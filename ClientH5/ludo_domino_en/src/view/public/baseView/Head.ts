module yalla.common {
    export class Head extends ui.ludo.sub.headUI {
        private _color: number = null;
        private _trustee: boolean = false;
        private _src: string = "public/default_head.png";
        private _facetemplet: Laya.Templet;
        private _faceSk: Laya.Skeleton;
        private _faceId: number = -1;
        private _timeLine: Laya.TimeLine = null;
        private _skName: any = 0;
        private _side: number;
        private _isSendGift: boolean = true;
        private _giftPos: Laya.Point = null;
        public gameUI: any;// Game类


        constructor() {
            super();
            this.addEvent();
        }
        private addEvent() {
            this.on(Laya.Event.CLICK, this, this.onClick);
            this.btn_gift.on(Laya.Event.CLICK, this, this.onClickGift)
        }
        public removeEvent() {
            this.off(Laya.Event.CLICK, this, this.onClick);
            this.btn_gift.off(Laya.Event.CLICK, this, this.onClickGift)
        }
        private onClick(e: Laya.Event) {
            e.stopPropagation();
            if (this.data) {
                yalla.Sound.playSound("click");

                //TODO::1.3.2   2.点击个人信息头像框 会和互动表情窗口互斥
                if (yalla.common.InteractiveGift.Instance.visible) {
                    yalla.common.InteractiveGift.Instance.hideGiftView();
                    return;
                }
                yalla.Native.instance.showUserProfile(this.data.fPlayerInfo.idx);

                // var view;
                // if (this.data.fPlayerInfo.idx == yalla.Global.Account.idx) {
                //     view = yalla.common.playerinfo.MyInfo.instance;
                // } else {
                //     view = yalla.common.playerinfo.OtherInfo.instance;
                // }
                // view.setData(this.data);
                // yalla.DialogManager.instance.open(view, true, false, true, null);
                if (this.data.fPlayerInfo.faceUrl != this._src) {
                    this.src = this.data.fPlayerInfo.faceUrl;
                }
            }
        }

        /**
         * 互动礼物
         */
        private onClickGift(e: Event): void {
            e.stopPropagation();
            var p;
            if (!this._isSendGift) return;
            if (!yalla.common.InteractiveGift.Instance.displayedInStage) {
                // if (this.parent && this.parent.parent.parent.parent.parent.parent) {
                //     p = this.parent.parent.parent.parent.parent.parent;
                //     p.addChild(yalla.common.InteractiveGift.Instance);
                // }
                if (this.gameUI) this.gameUI.addChild(yalla.common.InteractiveGift.Instance);
                yalla.common.InteractiveGift.Instance.zOrder = 992;
            }
            if (!this._giftPos) this._giftPos = this.localToGlobal(new Laya.Point());
            var isShow = yalla.common.InteractiveGift.Instance.visible;
            if (isShow) yalla.common.InteractiveGift.Instance.hideGiftView();
            else yalla.common.InteractiveGift.Instance.showGiftView(this._side, this._giftPos, this.data);
        }

        /**
         * @param team 
         */
        showTeam(team: string) {
            this.team_icon.skin = `game/team_${team}.png`;
            this.team_icon.visible = true;
        }

        /**
         * fplayerInfo如果有giftId，需要在头像上方显示礼物
         */
        public showGift(giftId: number, icon: string): void {
            if (giftId > 0) {
                this.btn_gift.visible = true;

                var path = `${yalla.File.cachePath}/giftIcon_${giftId}`;
                if (yalla.File.existed(`${path}.png`)) {
                    this.img_gift.skin = yalla.File.filePath + `giftIcon_${giftId}.png`;
                } else {
                    this.img_gift.skin = icon;
                    // yalla.event.YallaEvent.instance.once(`giftIcon_${giftId}.png`, this, () => {
                    //     this.updateSkin(giftId);
                    // })
                    // yalla.File.getFileByNative(`giftIcon_${giftId}.png`);
                }
                // this.img_gift.skin = icon;//`test/${giftId}.png`;
                this.img_gift.pivot(95 / 2, 95 / 2).pos(22, 22);
                this.img_gift.scale(0.75, 0.75);
            }
        }
        // private updateSkin(giftId: number): void {
        //     var path = yalla.File.cachePath + `/giftIcon_${giftId}.png`
        //     if (yalla.File.existed(path)) {
        //         this.img_gift.skin = yalla.File.filePath + `giftIcon_${giftId}.png`;
        //         // this.img_gift.pivot(95 / 2, 95 / 2).pos(22, 22);
        //         // this.img_gift.scale(0.75, 0.75);
        //     }    
        // }

        clear() {
            this.timer.clearAll(this);
            if (this._timeLine) {
                this._timeLine.removeLabel("1");
                this._timeLine.removeLabel("2");
                this._timeLine.removeLabel("3");
                this._timeLine.pause();
                this._timeLine.offAll()
                this._timeLine.destroy();
                this._timeLine = null;
            }
            this.removeSk();
        }
        showCrown(num: number) {
            switch (num) {
                case 1:
                    this.crown_1.visible = true;
                    this.crown_2.visible = false;
                    break;
                case 2:
                    this.crown_2.visible = true;
                    this.crown_1.visible = false;
                    break;
                default:
                    this.crown_2.visible = false;
                    this.crown_1.visible = false;
                    break;
            }
        }
        /**
         * 显示头像框
         * 有头像动效则显示，否则只显示头像框
         */
        showFaceBox(val: playerShowInfo) {
            this.data = val;
            var _faceId = val.fPlayerInfo.faceId;
            if (_faceId == this._faceId) return;
            // var oldFace: boolean = yalla.faceBoxAni.indexOf(this._faceId) > -1;
            this._faceId = _faceId;
            if (_faceId > 0) {
                // if (yalla.faceBoxAni.indexOf(_faceId) > -1) {
                //     !oldFace && this.removeSk();
                //     this.initSK(yalla.getSkeleton('face/touxiangkuang'));
                //     this.bg.visible = false;
                //     this._skName = this._faceId.toString();
                // } else {
                if (yalla.Skin.instance.isDynamic(this._faceId)) {
                    yalla.event.YallaEvent.instance.once(`faceframe_${this._faceId}.sk`, this, () => {
                        if (this.destroyed) return;
                        this._skName = 0;
                        this.removeSk();
                        this.initSK(yalla.File.filePath + `faceframe_${this._faceId}.sk`);
                    })
                    yalla.event.YallaEvent.instance.once(`faceframe_${this._faceId}.png`, this, () => {
                        yalla.File.getFileByNative(`faceframe_${this._faceId}.sk`);
                    })
                    yalla.File.getFileByNative(`faceframe_${this._faceId}.png`);
                } else {
                    yalla.event.YallaEvent.instance.once(`face_${this._faceId}.png`, this, d => {
                        if (this.destroyed) return;
                        this.removeSk();
                        this.bg.visible = true;
                        this.bg.skin = `${yalla.File.filePath}face_${this._faceId}.png`;
                    })
                    yalla.File.getFileByNative(`face_${this._faceId}.png`)
                }
                // }
            } else {
                this.removeSk();
                this.bg.visible = true;
                if (this.color === null)
                    this.bg.skin = 'public/face_bg.png';
                else
                    this.bg.skin = "game/head_" + this.color + ".png";
            }
        }

        private initSK(url: string) {
            if (this._facetemplet && this._faceSk) {
                if (this._faceId > 0) this._faceSk.play(this._faceId.toString(), true);
            } else {
                this._facetemplet = new Laya.Templet();
                this._facetemplet.on(Laya.Event.COMPLETE, this, this.faceSkComplete);
                this._facetemplet.on(Laya.Event.ERROR, this, this.faceSkError);
                this._facetemplet.loadAni(url);
            }
        }
        private faceSkError() {
            yalla.Debug.log("head facesk error");
        }

        private faceSkComplete() {
            if (!this._facetemplet) return;
            this._faceSk = this._facetemplet.buildArmature(1);
            this._faceSk.pos(this.bg.x + this.bg.width / 2, this.bg.y + this.bg.height / 2);
            this._faceSk.scale(0.7, 0.7);
            this.addChildAt(this._faceSk, 2);
            if (this._faceId > 0) this._faceSk.play(this._skName, true);
            this.bg.visible = false;
        }

        public playRoleAni() {
            if (!yalla.Global.isFouce) return;
            if (!this._timeLine) {
                this._timeLine = new Laya.TimeLine();
                this._timeLine.addLabel("1", 0).to(this, { scaleX: 1.5, scaleY: 1.5 }, 350).to(this, { scaleX: 0.8, scaleY: 0.8 }, 200).to(this, { scaleX: 1, scaleY: 1 }, 150);
            }
            this._timeLine.play("1", false);
            this.timer.once(750, this, this.resetScale);
        }
        resetScale() {
            if (this._timeLine) {
                this._timeLine.pause();
            }
            this.scale(1, 1);
        }

        public removeSk(): void {
            if (this._facetemplet) {
                this._facetemplet.offAll();
                this._facetemplet.destroy();
                this._facetemplet = null;
            }
            if (this._faceSk) {
                this._faceSk.stop();
                this._faceSk.removeSelf();
                this._faceSk.destroy(true);
                this._faceSk = null;
            }
        }

        private setFaceSkin(url: string) {
            this.face.skin = url;
            yalla.util.centerSetup(this.face, 84, 14);
        }
        set data(val: playerShowInfo) {
            this.dataSource = val;
            this.src = val.fPlayerInfo.faceUrl;
        }

        get data(): playerShowInfo {
            return this.dataSource;
        }
        set trustee(val: boolean) {//是否显示托管
            if (this._trustee == val) return;
            this._trustee = val;
            if (this._trustee) {
                this.setFaceSkin("game/Robot.png");
                this.removeEvent();
            } else {
                this.setFaceSkin(this._src);
                this.addEvent();
            }
        }
        get trustee(): boolean {
            return this._trustee;
        }
        set color(val: number) {
            if (val >= 0 && val <= 3) {
                this._color = val;
                if (this._faceId === -1) {
                    this.bg.skin = "game/head_" + val + ".png";
                }
            }
        }
        get color(): number {
            return this._color;
        }
        set src(value: string) {
            yalla.File.downloadImgByUrl(value, (localPath: string) => {
                // Laya.loader.load(localPath, Laya.Handler.create(this, (e) => {
                yalla.File.groupLoad(localPath, Laya.Handler.create(this, (e) => {
                    if (!!e) {
                        this._src = localPath;
                    }
                    if (!this.trustee)
                        this.setFaceSkin(this._src);
                }));
            })
        }
        get src(): string {
            return this._src;
        }
        set isLeft(val: boolean) {
            this.left_img.visible = this.face.gray = val;
            this.status.text = "LEFT";
            this.status.fontSize = 20;
            this.status_icon.skin = "game/left_man.png";
        }
        set isInChatOff(val: boolean) {
            this.left_img.visible = val;
            this.status.text = "Chat OFF";
            this.status.fontSize = 16;
            this.status_icon.skin = "game/ChatOFF.png";
        }
        set side(s) {
            this._side = s;
        }
        set isSendGift(val: boolean) {
            this._isSendGift = val;
        }
        get isSendGift(): boolean {
            return this._isSendGift;
        }
    }
}