module yalla.common {
    export class Setting {
        private _ui;
        private _setWidth = 0;
        private _gameUI: yalla.view.game.jackaro.Game;
        constructor(ui) {
            this._ui = ui;
            this.initUI();
        }

        private initUI(): void {
            this._ui.ruleBtn.on(Laya.Event.CLICK, this, this.onClickRules);
            this._ui.exitBtn.on(Laya.Event.CLICK, this, this.onExit);
            this._ui.soundBtn.on(Laya.Event.CLICK, this, this.onClickSound);
            this._ui.chatSwitchBtn.on(Laya.Event.CLICK, this, this.onClickChatSwitch);
            this._ui.musicBtn.on(Laya.Event.CLICK, this, this.onClickMusic);
            if (this._ui.shakeBtn) {
                // if (yalla.Native.instance.deviceType == DeviceType.IOS) {
                //     if (this._ui.exitBtn) {
                //         this._ui.exitBtn.x = this._ui.shakeBtn.x;
                //     }
                //     this._ui.shakeBtn.removeSelf();
                //     if (this._ui.set) {
                //         this._ui.set.width = 608;
                //     }
                //     if (yalla.Font.lan != 'en') {
                //         let movePosArr = ["soundBtn", "musicBtn", "ruleBtn", "chatSwitchBtn", "exitBtn"];
                //         for (let i = 0; i < movePosArr.length; i++) {
                //             let item = this._ui[movePosArr[i]];
                //             if (item) {
                //                 item.x = item.x - 115;
                //             }
                //         }
                //     }
                // } else {
                this._ui.shakeBtn.on(Laya.Event.CLICK, this, this.onClickShake);
                // }
            }
            //tips 按钮 用户展示是否显示手牌规则和手牌击杀 终点图标
            if (this._ui.tipsBtn) {
                this._ui.tipsBtn.on(Laya.Event.CLICK, this, this.onClickTips);
            }
            if (yalla.Font.lan == 'ar') {
                this._ui.soundBtn.labelPadding = '-4,0,0,-8';
                this._ui.exitBtn.labelPadding = '-4,0,0,-35';
            } else if (yalla.Font.lan == 'urdu') {
                this._ui.soundBtn.labelPadding = '-4,0,0,-48';
                this._ui.exitBtn.labelPadding = '-4,0,0,-15';
            }
            // else if(yalla.Font.lan == 'indy'){
            //     this._ui.soundBtn.labelPadding = '-4,0,0,-40';
            //     this._ui.exitBtn.labelPadding = '-4,0,0,-30';
            // }
            this._setWidth = this._ui.set.width;
        }
        public setGameUI(gameUI: any) {
            this._gameUI = gameUI;
        }

        public update(): void {
            this.updateSound();
            this.updateMusic();
            this.updateChatSwitch();
            this.updatePhoneVibrate();
            this.updateTips();
        }

        public updateSound(): void {
            if (yalla.Sound.sound) {
                this._ui.soundIcon.skin = this.getBtnSkin('soundOFF');
                this._ui.soundTxt.text = 'OFF';
            } else {
                this._ui.soundIcon.skin = this.getBtnSkin('soundON');
                this._ui.soundTxt.text = 'ON';
            }
        }

        public updateMusic(): void {
            if (yalla.Sound.music) {
                this._ui.musicIcon.skin = this.getBtnSkin('musicOFF');
                this._ui.musicTxt.text = 'OFF';
            } else {
                this._ui.musicIcon.skin = this.getBtnSkin('musicON');
                this._ui.musicTxt.text = 'ON';
            }
        }
        //tips 按钮 用户展示是否显示手牌规则和手牌击杀 终点图标 v1.4.5
        public updateTips(): void {
            if (yalla.Global.gameType != GameType.JACKARO) return;
            if (yalla.JackaroTipsShow.isOpen()) {
                this._ui.tipsIcon.skin = this.getBtnSkin('tipsON');
            } else {
                this._ui.tipsIcon.skin = this.getBtnSkin('tipsOFF');
            }
        }
        private getBtnSkin(name: string) {
            return Global.gameType == GameType.DOMINO ? yalla.getDomino(name) : yalla.getJackaro(name);
        }

        public updateChatSwitch(): void {
            var isInChatOff = 0;
            if (Global.gameType == GameType.DOMINO) {
                var user = yalla.data.UserService.instance.user;
                if (user.playerShowInfo) isInChatOff = user.playerShowInfo.isInChatOff;
            } else if (Global.gameType == GameType.JACKARO) {
                isInChatOff = yalla.data.jackaro.JackaroUserService.instance.user.isInChatOff;
            }
            if (isInChatOff) {
                this._ui.switchIcon.skin = this.getBtnSkin('ChatOFF');
                this._ui.switchTxt.text = 'Chat OFF';
            } else {
                this._ui.switchIcon.skin = this.getBtnSkin('ChatON');
                this._ui.switchTxt.text = 'Chat ON';
            }
        }
        public onClickShake(): void {
            if (yalla.Global.gameType != GameType.JACKARO) return;
            yalla.PhoneVibrate.phoneVibrateSetting_cache = !yalla.PhoneVibrate.isOpen();
            if (yalla.PhoneVibrate.isOpen()) {
                if (this._ui.shakeIcon) {
                    this._ui.shakeIcon.skin = this.getBtnSkin('shakeOn');//phoneVibrateON
                }
            } else {
                if (this._ui.shakeIcon) {
                    this._ui.shakeIcon.skin = this.getBtnSkin('shakeOff');//phoneVibrateOFF
                }
            }
        }
        public updatePhoneVibrate(): void {
            if (yalla.Global.gameType != GameType.JACKARO) return;
            if (this._ui.shakeIcon && yalla.PhoneVibrate.isOpen()) {
                this._ui.shakeIcon.skin = this.getBtnSkin('shakeOn');//phoneVibrateON
            } else {
                if (this._ui.shakeIcon) {
                    this._ui.shakeIcon.skin = this.getBtnSkin('shakeOff');//phoneVibrateOFF
                }
            }
        }
        /**观战的chaton 不显示 */
        public setWatchSetting(isLeft: boolean = true): void {
            var itemWid = this._ui.chatSwitchBtn.width;
            var v = 11;
            this._ui.chatSwitchBtn.visible = false;
            if (this._setWidth > 0) this._ui.set.width = this._setWidth - (itemWid + v);
            if (isLeft) {
                this._ui.exitBtn.x = this._ui.chatSwitchBtn.x;
            } else {
                this._ui.ruleBtn.x = this._ui.chatSwitchBtn.x;
                this._ui.musicBtn.x = this._ui.ruleBtn.x + itemWid + v;
                this._ui.soundBtn.x = this._ui.musicBtn.x + itemWid + v;
            }
        }

        private onExit(): void {
            if (yalla.Global.gameType == GameType.JACKARO) {
                if (this._gameUI) {
                    this._gameUI.exitHander();
                }
            } else {
                yalla.event.YallaEvent.instance.event(yalla.data.EnumCustomizeCmd.Dominose_Back);
            }
        }

        private onClickRules(): void {
            if (Global.gameType == GameType.DOMINO) {
                yalla.Native.instance.mobClickEvent(buryPoint.DGAME_SETTING_RULES);
                yalla.Native.instance.showGameRules(yalla.Global.gameType, yalla.data.RoomService.instance.room.gameType);
            } else if (Global.gameType == GameType.JACKARO) {
                var room = yalla.data.jackaro.JackaroUserService.instance.room;
                var mode = 100;
                var ruleType = room.isComplexMode() ? 9 : 8;
                if (room.isQuickBasicMode()) {
                    mode = 102;
                } else if (room.isQuickComplexMode()) {
                    mode = 103;
                } else if (room.is1V1Mode()) {
                    ruleType = 10;
                    mode = 104;
                }
                yalla.Native.instance.showGameRules(yalla.Global.gameType, mode, ruleType);
            }
        }

        private onClickSound(e: Laya.Event): void {
            // e.stopPropagation();
            yalla.Sound.sound = !yalla.Sound.sound;
            if (yalla.Sound.sound) yalla.Sound.stopAllSound();
            this.updateSound();

            if (yalla.Sound.sound) yalla.Native.instance.mobClickEvent(buryPoint.DGAME_SETTING_CLOSESOUND);
            else yalla.Native.instance.mobClickEvent(buryPoint.DGAME_SETTING_OPENSOUND);
        }

        private onClickChatSwitch(e: Laya.Event): void {
            // e.stopPropagation();
            if (Global.gameType == GameType.DOMINO) {
                yalla.data.RoomService.instance.sendChatOff(1 - ludo.muteSpectator.isMute);
            } else if (Global.gameType == GameType.JACKARO) {
                yalla.data.jackaro.JackaroUserService.instance.sendChatOff(1 - ludo.muteSpectator.isMute);
            }
        }

        private onClickMusic(e: Laya.Event): void {
            yalla.Sound.music = !yalla.Sound.music;
            var musicName = Global.gameType == GameType.JACKARO ? yalla.Sound.audios.jackaroo_bg : yalla.Sound.audios.domino_bg;
            if (yalla.Sound.music) {
                yalla.Sound.stopMusic(musicName);
            } else {
                yalla.Sound.playMusic(musicName, 0);
            }
            this.updateMusic();
        }
        //tips 按钮 用户展示是否显示手牌规则和手牌击杀 终点图标
        private onClickTips(e: Laya.Event): void {
            var isOpen = !yalla.JackaroTipsShow.isOpen();
            yalla.JackaroTipsShow.tipsShowSetting_cache = isOpen;
            yalla.util.clogDBAmsg('10421', isOpen ? "1" : "0", 1);
            this.updateTips();
        }
        public clear(): void {
            Laya.timer.clearAll(this);
            this._ui.ruleBtn.off(Laya.Event.CLICK, this, this.onClickRules);
            this._ui.exitBtn.off(Laya.Event.CLICK, this, this.onExit);
            this._ui.soundBtn.off(Laya.Event.CLICK, this, this.onClickSound);
            this._ui.chatSwitchBtn.off(Laya.Event.CLICK, this, this.onClickChatSwitch);
            if (this._ui.shakeBtn) {
                this._ui.shakeBtn.off(Laya.Event.CLICK, this, this.onClickShake);
            }
            if (this._ui.tipsBtn) {
                this._ui.tipsBtn.off(Laya.Event.CLICK, this, this.onClickTips);
            }
            this._ui.musicBtn.off(Laya.Event.CLICK, this, this.onClickMusic);
        }
    }
}