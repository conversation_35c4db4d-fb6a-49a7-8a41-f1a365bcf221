module yalla.common {
    export class TipItem extends Laya.Image {
        private _tween: Laya.Tween;
        private _lb: Laya.Label = new Laya.Label();
        private _queue: Array<any> = null;
        constructor() {
            super();
            this.size(641, 80);
            this.loadImage('public/BG_words.png', 0, 0, 641, 80);
            this._lb.size(641, 80);
            this._lb.align = "center";
            this._lb.valign = 'middle';
            this._lb.color = "#ffffff";
            this._lb.fontSize = 28;
            this._lb.strokeColor = "#000000";
            this._lb.stroke = 1;
            this.addChild(this._lb);
        }
        static _instance: yalla.common.TipItem = null;
        static get instance(): yalla.common.TipItem {
            return this._instance ? this._instance : this._instance = new yalla.common.TipItem();
        }
        /**
         * @param parent 父级
         * @param posX 添加位置x
         * @param posY 添加位置y
         * @param content tip内容
         * @param params tween 动画参数
         * @param showTime 动画展示时间
         * @param delayTime 动画延迟播放时间
         */
        public showTip(posX: number, posY: number, content: string, params: any, showTime: number, delayTime: number = 0, parent: any = null): void {
            if (yalla.Global.tipContent == content) return;
            this._tween && Laya.Tween.clear(this._tween);
            yalla.Global.tipContent = content;
            this._lb.text = content;
            this.pos(posX, posY);
            if (!parent) parent = Laya.stage;
            parent.addChild(this);
            this._tween = Laya.Tween.to(this, params, showTime, Laya.Ease.linearIn, Laya.Handler.create(this, () => {
                this._tween && Laya.Tween.clear(this._tween);
                this._tween = null;
                this.removeSelf();
                yalla.Global.tipContent = '';
                this.nextTip();
            }), delayTime);
        }
        public recover(): yalla.common.TipItem {
            this.alpha = 1;
            return this;
        }
        public pushTip(posX: number, posY: number, content: string, params: any, showTime: number, delayTime: number = 0, parent: any = null) {
            if (this._tween) {
                if (!this._queue) this._queue = [];
                this._queue.push(arguments);
            } else {
                this.recover().showTip.apply(this, arguments);
            }
        }
        public nextTip() {
            var arg = this._queue ? this._queue.shift() : null;
            arg && this.recover().showTip.apply(this, arg);
        }

        public hideTip(): void {
            if (this._tween) this._tween.complete();
        }

        public clear(): void {
            this._queue = null;
            this._tween && this._tween.clear();
            this._tween = null;
            yalla.Global.tipContent = '';
            this.removeSelf();
            this.destroy(true);
        }
    }
}