module yalla.common {
    export class TipManager {
        public static _instance: TipManager;
        private _tipList: Array<any> = [];

        constructor() {
        }

        static get instance(): TipManager {
            return TipManager._instance || (TipManager._instance = new TipManager());
        }

        /**
         * 
         * @param parent 父级
         * @param posX 添加位置x
         * @param posY 添加位置y
         * @param content tip内容
         * @param params tween 动画参数
         * @param showTime 动画展示时间
         * @param delayTime 动画延迟播放时间
         */
        public showTip(content: string, params: any, showTime: number, delayTime: number = 0, parent: any = null): void {
            if (!parent) parent = Laya.stage;
            var tipItem = new TipItem();
            var [w, h] = [tipItem.width, tipItem.height];
            var [posX, posY] = [(parent.width - w) / 2, parent.height / 2];
            tipItem.showTip(posX, posY, content, params, showTime, delayTime, parent);

            !this._tipList && (this._tipList = []);
            this._tipList.push(tipItem);
        }

        /**
         * 只显示当前的TipItem
         * @param content 
         * @param params 
         * @param showTime 
         * @param delayTime 
         * @param parent 
         */
        public showOneTip(content: string, params: any, showTime: number, delayTime: number = 0, parent: any = null) {
            this.clear();
            this.showTip(content, params, showTime, delayTime, parent);
        }

        /**
         * autoAddExp// 托管自动加经验 托管加经验 0 可以加  1 不可以加
         * @param trustState 1托管 0取消托管
         * @param pos        
         * @param parent 
         */
        private _trustItem: TrustTip = null;
        public showTrustTip(trustState: number, parent?, pos?,isDuelMode?) {
            if (yalla.Global.Account.isPrivate == 1) return;
            if (!yalla.Global.Account.autoAddExp) return;
            if (!parent) parent = Laya.stage;
            if (!this._trustItem) this._trustItem = new TrustTip();
            if (!pos) {
                var xx = (Laya.stage.width - this._trustItem.width) / 2;
                var yy = 20;
                if (yalla.Screen.hasHair) {
                    yy = yalla.Global.ProfileInfo.hairHeight ? yalla.Global.ProfileInfo.hairHeight - 20 : 80;
                }
                pos = {x:xx, y: yy };
            }
            // if (!this.hasTrustTip && trustState == 0) this._trustItem.hideTip();
            // else this._trustItem.showTip(trustState, parent, pos);
            if (trustState == 1) this._trustItem.showTip(trustState, parent, pos,isDuelMode);
            else this._trustItem.hideTip();
        }

        public hideTrustTip() {
            if (this._trustItem) this._trustItem.hideTip();
        }

        public get hasTrustTip() {
            return this._trustItem && this._trustItem.parent && this._trustItem.visible;
        }


        public clear(): void {
            if (this._trustItem) this._trustItem.clear();
            Laya.stage.removeChildByName("TrustTip");
            this._trustItem = null;
            
            for (var k in this._tipList) {
                this._tipList[k].clear();
            }
            this._tipList = null;
        }
    }
}