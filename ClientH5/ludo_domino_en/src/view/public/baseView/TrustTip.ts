module yalla.common {
    export class TrustTip extends ui.publics.dialog.trustTipUI {
        constructor() {
            super();
            this.name = 'TrustTip';
            this.closeBtn.on(Laya.Event.CLICK, this, this.hideTip);
        }

        public showTip(trustState: number, parent?, pos?, isDuelMode?): void {
            // if (this.visible) {
            //     this.updateText(trustState);
            //     return;
            // }
            if (!this.displayedInStage) {
                // if (!parent) parent = Laya.stage;
                Laya.stage.addChild(this);//TODO::domino 互动礼物和托管tip层级问题，所以加入stage
                this.pos(pos.x, pos.y);
                this.zOrder = 998;
            }
            this.updateText(trustState, isDuelMode);
            this.visible = true;
            yalla.util.updateBackPressed(1, 1);
        }

        private updateText(trustState: number, isDuelMode): void {
            if (yalla.Font.lan == 'ar') {
                this.contentTxt.fontSize = 24;
                this.contentTxt.width = 880;
                this.contentTxt.align = 'right';
                this.contentTxt.left = NaN;
                this.contentTxt.right = 148;

                this.closeBtn.right = NaN;
                this.closeBtn.left = 14;

                this.icon.left = NaN;
                this.icon.right = 30;

            } else {
                this.contentTxt.fontSize = 20;
                this.contentTxt.width = 500;
                this.contentTxt.align = 'left';
                this.contentTxt.right = NaN;
                this.contentTxt.left = NaN;
                this.contentTxt.x = 138;

                this.closeBtn.right = 14;
                this.closeBtn.left = NaN;

                this.icon.left = 30;
                this.icon.right = NaN;
            }
            // if (trustState == 1) {
            this.contentTxt.text = !isDuelMode ? "AUTO is turned ON. You obtain no Exp while on AUTO." : "AUTO is turned ON. Your kills will not be counted, and you obtain no Exp while on AUTO.";
            // } else {
            //     this.contentTxt.text = "AUTO is turned OFF. If you miss a turn, AUTO will be turned ON  again.";
            // }

        }

        public hideTip(): void {
            yalla.util.updateBackPressed(1);
            this.visible = false;
        }

        clear() {
            this.offAll();
            this.removeSelf();
            this.destroy();
        }

    }
}