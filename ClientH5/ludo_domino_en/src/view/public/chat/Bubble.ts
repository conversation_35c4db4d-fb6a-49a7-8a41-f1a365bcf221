module yalla.common {
    export class Bubble extends Laya.Box {
        public ui: any;
        private tw: <PERSON><PERSON>.Tween;
        private face_templet: <PERSON><PERSON>.Templet;
        private face_sk: Laya.Skeleton;
        private skin_id: number = 0;
        private side: number;
        private _skList: Array<Laya.Skeleton> = [];
        private _templateList: Array<Laya.Templet> = [];
        constructor(side: number) {
            super();
            this.side = side;
            switch (side) {
                case 0:
                    this.ui = new ui.publics.chat.bubble_0UI();
                    break;
                case 1:
                    this.ui = new ui.publics.chat.bubble_1UI();
                    break;
                case 2:
                    this.ui = new ui.publics.chat.bubble_2UI();
                    break;
                case 3:
                    this.ui = new ui.publics.chat.bubble_3UI();
                    break;
                default:
                    break;
            }
            this.addChild(this.ui);
            this.ui && this.ui.scale(0, 0);
            this.visible = false;
            this.initSK();
        }
        set skinId(val: number) {
            if (this.skin_id != val) {
                this.skin_id = val;
                if (val > 12000)
                    this.setSkin();
                else
                    this.resetLayout();
            }
        }
        get skinId(): number {
            return this.skin_id;
        }
        private resetLayout() {
            if (this.destroyed) return;
            this.ui.box.sizeGrid = "61,50,37,101";
        }
        private setSkin() {
            if (this.ui) {
                yalla.event.YallaEvent.instance.once(`bubble_${this.skin_id}.atlas`, this, () => {
                    if (this.ui && this.ui.box) {
                        this.resetLayout();
                        (this.ui.box as Laya.Image).skin = `bubble_${this.skin_id}/bubbles_${this.skin_id}.png`;
                        (this.ui.arrow as Laya.Image).skin = `bubble_${this.skin_id}/bubbles_angle_${this.skin_id}.png`;
                        (this.ui.bottom_icon as Laya.Image).skin = `bubble_${this.skin_id}/material_bubbles_${this.skin_id}.png`;
                        (this.ui.top_icon as Laya.Image).skin = `bubble_${this.skin_id}/material_top_bubbles_${this.skin_id}.png`;
                    }
                })
                yalla.event.YallaEvent.instance.once(`bubble_${this.skin_id}.png`, this, () => {
                    yalla.File.getFileByNative(`bubble_${this.skin_id}.atlas`);
                })
                yalla.File.getFileByNative(`bubble_${this.skin_id}.png`);
            }
        }
        public testSkin(id) {
            this.skin_id = id;
            this.resetLayout();
            (this.ui.box as Laya.Image).skin = `bubble_${this.skin_id}/bubbles_${this.skin_id}.png`;
            (this.ui.arrow as Laya.Image).skin = `bubble_${this.skin_id}/bubbles_angle_${this.skin_id}.png`;
            (this.ui.bottom_icon as Laya.Image).skin = `bubble_${this.skin_id}/material_bubbles_${this.skin_id}.png`;
            (this.ui.top_icon as Laya.Image).skin = `bubble_${this.skin_id}/material_top_bubbles_${this.skin_id}.png`;
        }
        private initSK() {
            this.face_templet = new Laya.Templet();
            this.face_templet.on(Laya.Event.COMPLETE, this, this.faceSkComplete);
            this.face_templet.on(Laya.Event.ERROR, this, () => { });
            this.face_templet.loadAni("res/sk/fastemoji/zong.sk");
        }
        private faceSkComplete() {
            if (!this.face_templet) return;
            this.face_sk = this.face_templet.buildArmature(0);
            this.ui.sk_box.addChild(this.face_sk);
            this.face_sk.pos(59, 59);
        }

        public onChat(msg: any): void {
            if (!msg) return;
            var messageType = msg.messageType;
            var extension = msg.extension;
            if (extension || messageType) {
                if (typeof (extension) == "string" && extension.indexOf('{') > -1) extension = yalla.util.parseJson(msg.extension);
                var emojiId = 0;
                if (extension) emojiId = extension.id;
                if (messageType == yalla.data.jackaro.GameChatMessageType.CHAT_MESSAGE_EMOJI) emojiId = Number(msg.msg);
                if (emojiId) {
                    this.initEmojiAni(emojiId);
                } else if (msg.msg) {
                    this.chat = msg.msg;
                }
            } else if (msg.msg) {
                this.chat = msg.msg;
            }
            
            // if (msg.msg && !msg.extension) {
            //     this.chat = msg.msg;
            // } else {
            //     this.ui.sk_box.visible = true;
            //     this.ui.lb.visible = false;
            //     this.ui.box.size(158, 158);
            //     var extension = msg.extension;
            //     if (typeof (extension) == "string") extension = JSON.parse(msg.extension);
            //     this.initEmojiAni(extension);
            // }
        }
        set chat(val: string) {
            var _lb: Laya.Label = this.ui.lb;
            if (val.indexOf("icon_emoji_") > -1) {
                this.ui.emoji_box.removeChildren();
                this.ui.sk_box.visible = true;
                this.ui.emoji_box.visible = false;
                _lb.visible = false;
                if (this.face_sk) {
                    this.face_sk.visible = true;
                    this.face_sk.stop();
                    var animate = val.replace("icon_emoji_", "").replace(".png", "");
                    this.face_sk.play(animate, true);
                }
                this.ui.box.size(158, 158);
            } else {
                _lb.width = null;
                _lb.height = null;
                _lb.wordWrap = false;
                _lb.text = '';
                this.ui.sk_box.visible = this.ui.emoji_box.visible = false;
                _lb.visible = true;
                _lb.text = this.getChatStr(val);
                var w = _lb.width;
                var isArabic = val.isArabic(0);
                if (w > 260) {
                    _lb.wordWrap = true;
                    _lb.width = val.isArabic(0) ? 220 : 250;
                    var __h = _lb.height + 30;
                    _lb.reCache()
                    if (__h > 270) {
                        _lb.fontSize = 24;
                        _lb.height = 290;
                        __h = 320;
                    }
                    // this.ui.box.size(__w, __h);
                    //最大宽度不能超过290 否则会有遮挡棋盘的问题
                    this.ui.box.size(290, Math.max(98, __h));
                } else if (w < 126) {
                    _lb.fontSize = 30;
                    // if (val.isArabic(0)) this.ui.box.size(_lb.width + 150, 98);
                    // else this.ui.box.size(200, 98);
                    this.ui.box.size(190, 98);
                } else {
                    _lb.fontSize = 30;
                    // if (val.isArabic(0)) this.ui.box.size(_lb.width + 120, 98);
                    // else this.ui.box.size(_lb.width + 50, 98);
                    this.ui.box.size(_lb.width + 50, 98);
                }
                _lb.centerX = _lb.centerY = 0;
            }
        }

        getChatStr(msg: string): string {
            msg = msg.replace(/[\\]/gm, "");
            var len = yalla.util.getBLen(msg);
            // if (len > 110) return yalla.util.filterStr(msg, 107) + "...";
            if (len > 80) return yalla.util.filterStr(msg, 77) + "...";//1.3.5产品要求改为80字节
            return msg;
        }

        initEmojiAni(emojiId) {
            this.ui.emoji_box.visible = true;
            this.ui.sk_box.visible = false;
            this.ui.lb.visible = false;
            this.ui.box.size(158, 158);

            if (!emojiId) return;
            if (this.face_sk) {
                this.face_sk.stop();
                this.face_sk.visible = false;
            }
            this.ui.emoji_box.removeChildren();

            if (yalla.util.IsBrowser()) {
                this.playEmojiAni(emojiId);
            } else {
                yalla.event.YallaEvent.instance.once(`emojiSk_${emojiId}.sk`, this, (eId: any) => {
                    this.playEmojiAni(emojiId);
                }, [emojiId]);
                yalla.event.YallaEvent.instance.once(`emojiSk_${emojiId}.png`, this, () => {
                    yalla.File.getFileByNative(`emojiSk_${emojiId}.sk`);
                })
                yalla.File.getFileByNative(`emojiSk_${emojiId}.png`);
            }
        }

        playEmojiAni(emojiId) {
            var template = Laya.Pool.getItemByClass("ludo_emoji_animtion_templet", Laya.Templet);
            template.once(Laya.Event.COMPLETE, this, (template: Laya.Templet, eId) => {
                this.removeNewEmoji();
                this.ui.emoji_box.removeChildren();

                var sk = template.buildArmature(0);
                sk.play(0, true);
                this.ui.emoji_box.addChild(sk);

                sk.pos(59, 105);

                if (!this._skList) this._skList = [];
                this._skList.push(sk);
            }, [template, emojiId]);

            var skUrl = "res/test/emojiSk_" + emojiId + ".sk";
            if (!yalla.util.IsBrowser()) skUrl = yalla.File.filePath + `emojiSk_${emojiId}.sk`;
            template.loadAni(skUrl);
            if (!this._templateList) this._templateList = [];
            this._templateList.push(template);
        }

        removeNewEmoji(): void {
            if (!this._skList) return;
            for (var i = 0; i < this._skList.length; i++) {
                if (this._skList[i]) {
                    this._skList[i].stop();
                    this._skList[i].removeSelf();
                }
            }
            this._skList = [];
        }

        show() {
            if (this.visible) {
                this.visible = false;
                this.ui.scale(0, 0);
                this.tw && this.tw.complete();
            }
            this.visible = true;
            this.timer.clear(this, this.hide);
            this.timer.once(5000, this, this.hide);
            this.tw = Laya.Tween.to(this.ui, {
                scaleX: 1,
                scaleY: 1
            }, 400, Laya.Ease.backOut, Laya.Handler.create(this, () => {
                this.tw && this.tw.clear();
            }))
        }
        hide() {
            this.visible = false;
            this.ui.scale(0, 0);
            if (this.face_sk) {
                this.face_sk.stop();
                this.face_sk.visible = false;
            }
            this.removeNewEmoji();
        }

        clear() {
            if (this.face_templet) {
                this.face_templet.destroy();
                this.face_templet = null
            }
            if (this.face_sk) {
                this.face_sk.destroy();
                this.face_sk = null;
            }
            var i;
            if (this._templateList) {
                for (i = 0; i < this._templateList.length; i++) {
                    this._templateList[i].offAll();
                    this._templateList[i].destroy()
                }
            }
            if (this._skList) {
                for (i = 0; i < this._skList.length; i++) {
                    this._skList[i].stop();
                    this._skList[i].offAll();
                    this._skList[i].destroy(true);
                }
            }
            this._skList = null;
            this._templateList = null;

            if (this.timer) {
                this.timer.clearAll(this);
            }
            if (this.tw) {
                this.tw.complete();
            }
            Laya.Pool.clearBySign("ludo_emoji_animtion_templet");
        }
    }
}