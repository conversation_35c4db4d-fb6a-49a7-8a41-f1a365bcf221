module yalla.common {
    export class BubbleReport extends ui.publics.chat.bubble_muteUI {
        static _instance: BubbleReport;
        private _chatIndex: number = 0;
        private _playerShowInfo: playerShowInfo = null;
        private _reportMsg: string = '';
        public sendMsgList: Array<any> = [];

        static get instance(): BubbleReport {
            return BubbleReport._instance || (BubbleReport._instance = new BubbleReport());
        }
        constructor() {
            super();
            this.initEvent();
        }

        private initEvent(): void {
            this.repartBtn.on(Laya.Event.CLICK, this, this.onClickReport);
        }

        private onClickReport(e: Laya.Event): void {
            e.stopPropagation();
            this.hide();
            if (yalla.Global.ChatIndex - this._chatIndex > yalla.Global.ChatMaxLen) {
                yalla.common.TipManager.instance.showTip('Message does not exist. Report failed.', {}, 2000);
                return;
            }
            if (this.sendMsgList) {
                for (var i = 0; i < this.sendMsgList.length; i++) {
                    this.sendMsgList[i].IsReport = 0;
                }
            }
            //TODO: sourceId（gameId 10021） 、gameRoomType、beReportedUserId、onlineNum、onLookerNum、chatInfo、content（当前举报信息）
            var len = yalla.Global.ChatMsgList.length;
            var bIndex, eIndex = 0;
            if (yalla.Global.ChatIndex <= yalla.Global.ChatMaxLen) {
                bIndex = Math.max(0, this._chatIndex - 10);
                eIndex = Math.min(this._chatIndex + 6, len);
                if (yalla.Global.ChatMsgList[this._chatIndex]) yalla.Global.ChatMsgList[this._chatIndex].IsReport = 1;
            } else {
                var newIndex = yalla.Global.ChatMaxLen - (yalla.Global.ChatIndex - this._chatIndex);
                bIndex = Math.max(0, newIndex - 10);
                eIndex = Math.min(newIndex + 6, len);
                if (yalla.Global.ChatMsgList[newIndex]) yalla.Global.ChatMsgList[newIndex].IsReport = 1;
            }

            this.sendMsgList = yalla.Global.ChatMsgList.slice(bIndex, eIndex);
            if (!this.sendMsgList) this.sendMsgList = [];

            var userID = 0;
            var userName = '';
            var onlineNum = 0;
            var onLookerNum = 0;
            if (this._playerShowInfo) {
                userID = this._playerShowInfo.fPlayerInfo.idx;
                userName = this._playerShowInfo.fPlayerInfo.nikeName;
            }
            if (yalla.Global.gameType == GameType.DOMINO) {
                onlineNum = yalla.data.RoomService.instance.room.playerNums;
            } else if (yalla.Global.gameType == GameType.SNAKEANDLADER){
                
            } else {
                var ludoroom = ludoRoom.instance
                onlineNum = ludoroom.sitPlayers.length;
                onLookerNum = ludoroom.getAudiencePlayers().length;
            }
            var p = {
                sourceId: yalla.Global.gameType,
                gameRoomType: yalla.Global.Account.isPrivate,
                beReportedUserId: userID,
                onlineNum: onlineNum,
                onLookerNum: onLookerNum,
                content: this._reportMsg,
                userName: laya.utils.Browser.window.Base64.encode(userName),
                chatInfo: this.sendMsgList
            };
            yalla.Native.instance.reportMsg(p);
        }

        public show(chatObj: any, x: number, y: number, chatIndex: number, parent: any = null): void {
            if (!chatObj || !chatObj.chat) return;
            var chatMsg = chatObj.chat;
            var extension = chatMsg.extension;
            if (chatMsg.msg.indexOf("icon_emoji_") > -1) {
                return;
            }
            if (typeof (extension) == "string" && extension.indexOf('{') > -1 || (extension && extension.id)) return;
            this._playerShowInfo = chatObj.playerShowInfo;
            this._reportMsg = chatMsg.msg;
            this.visible = true;
            this._chatIndex = chatIndex;
            if (!parent) Laya.stage.addChild(this);
            this.pos(x, y);

            // this.zOrder = 999;//最上层
            // this.left = 0;
            // this.bottom = Laya.stage.height - this.height;
        }

        public hide(): void {
            this.visible = false;
        }

        public removeEvent(): void {
            this.repartBtn.off(Laya.Event.CLICK, this, this.onClickReport);
        }

        public clear(): void {
            this.removeEvent();
            this.removeSelf();
        }
    }
}