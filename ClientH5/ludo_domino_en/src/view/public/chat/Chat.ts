module yalla.common {
    export class Chat extends ui.publics.chat.chatUI {
        protected _gameType: number;
        protected _newMsgScrollValue = 0;
        protected hasChat: boolean = false;
        protected urlFunc: any;
        public maxLen: number = 10;
        /** 1：组队聊天 */
        public chatChannel: number = 0;

        constructor(gameType: number = yalla.Global.gameType) {
            super();
            this._gameType = gameType;
            this.urlFunc = yalla.getGame;
            if (gameType == GameType.DOMINO) {
                this.urlFunc = yalla.getDomino;
            } else if (this._gameType == GameType.SNAKEANDLADER) {
                this.urlFunc = yalla.getSnake;
            } else if (this._gameType == GameType.JACKARO) {
                this.urlFunc = yalla.getJackaro;
            }
            if (yalla.Font.isRight())
                this.inputChat.align = "right";
            this.bg.skin = this.urlFunc('chatBg');
            this.chatboxBg.skin = this.urlFunc('BG_chat80');
            this.chatPanel.vScrollBarSkin = this.urlFunc('scroll');
            this.arrow.skin = this.urlFunc('BG_chat_v');
            this.footer.skin = this.urlFunc('BG_topchat');
            this.banner.skin = this.urlFunc('BG_basechat');
            this.sendBtn.skin = this.urlFunc('btn_G');
            this.inputBgImg.skin = this.urlFunc('chat_input');
            // this.inputChat.skin = this.urlFunc('chat_input');
            this.msgHintBtn.skin = this.urlFunc('newmessage');

            this.chatPanel.vScrollBar.hide = true;
            this.chatPanel.vScrollBar.elasticDistance = 0;
            this.chatList.size(Laya.stage.width, 0);
            this.chatList['sortItem'] = function (items) {
                return;
            }

            if (this._gameType == GameType.JACKARO) {
                this.sendTxt.color = "#A96C1D";
                this.sendTxt.strokeColor = "#FFED45";

                this.sendNumTxt.color = "#A96C1D";
                this.sendNumTxt.strokeColor = "#FFED45";
            }

            yalla.Native.instance.on(yalla.Native.instance.Event.PRESSENTER, this, this.pressEnter);
            yalla.Native.instance.on(yalla.Native.instance.Event.SHOWKEYBOARD, this, this.showKeyboard);
            this.inputChat.on(Laya.Event.INPUT, this, this.onInput);
            this.inputChat.on(Laya.Event.FOCUS, this, this.catchFouce);//获取焦点
            this.inputChat.on(Laya.Event.BLUR, this, this.catchBlur);//获取焦点

            this.msgHintBtn.on(Laya.Event.CLICK, this, this.onClickToMsg);
            this.bg.on(Laya.Event.CLICK, this, this.pressEnter);
            this.sendBtn.on(Laya.Event.CLICK, this, this.pressEnter);
            this.on(Laya.Event.CLICK, this, this.onClick);
            this.on(Laya.Event.MOUSE_UP, this, this.onMonseUpChatPanel);
            yalla.event.YallaEvent.instance.on(yalla.data.EnumCustomizeCmd.ResetInputTextFocus, this, this.resetFocus);
            this.onInput();

            this.channelBannerBox.visible = false;
            this.chatTitleTxt.visible = true;
        }
        /**TODO::1.4.3 ios 弹起键盘(这是点击cpu 内存入口，导致键盘收起，但是舞台位置偏移) (ps:发送消息enter 也会触发keyboard，这里不能清空输入框) */
        private showKeyboard(n) {
            if (yalla.Native.instance.deviceType == DeviceType.IOS) {
                if (n == 0) {
                    this.event("close");
                    // this.resetFocus();
                }
            }
        }

        private resetFocus() {
            this.clearInputTxt();
            this.event("close");
        }

        private catchBlur() {
            Laya.timer.frameOnce(10, this, () => {
                this.chatbox.visible = true;
            })
        }
        private pressEnter(): void {
            //1.4.5 增加ios机型线上偶现bug日志上报，针对jackaro游戏服  聊天日志
            yalla.util.logReport({ log: "Chat pressEnter: ", time: yalla.getTimeHMS() });
            if (!this || !this.bg) return;
            yalla.common.BubbleReport.instance.hide();
            if (this.bg.visible && this.inputChat) {
                var chat = this.inputChat.text;
                if (chat.length == 0) {
                    if (!this.hasChat) {
                        this.event("close");
                        this.setVisible(false);
                        return;
                    }
                    this.chatbox.visible = true;
                    return;
                }
                this.sendMsg();
                this.hide();
            }
        }
        public close(isVisib: boolean = false) {
            this.setVisible(isVisib);
            this.inputChat.focus = false;
            if (!isVisib) yalla.common.BubbleReport.instance.hide();
            //1.4.5 增加ios机型线上偶现bug日志上报，针对jackaro游戏服  聊天日志
            yalla.util.logReport({ log: "Chat close: ", time: yalla.getTimeHMS() });
        }
        public onClick(e: Laya.Event) {
            yalla.common.BubbleReport.instance.hide();
            if (e.target.name == "send") this.sendMsg();
            e.stopPropagation();
        }

        /**
         * 定位到最新的消息
         */
        private onClickToMsg(): void {
            var listPanel = this.listPanel;
            listPanel.vScrollBar.value = this._newMsgScrollValue + 146;
            this.msgHintBtn.visible = false;
            this._newMsgScrollValue = listPanel.vScrollBar.max;
        }

        private onMonseUpChatPanel(): void {
            if (!this.visible) return;
            this.timer.once(310, this, () => {//默认回弹300ms
                var listPanel = this.listPanel;

                var isShow1 = this.msgHintBtn.visible && listPanel.vScrollBar.value >= this._newMsgScrollValue;
                var isShow2 = listPanel.vScrollBar.value >= listPanel.vScrollBar.max;
                if (isShow1 || isShow2) this.msgHintBtn.visible = false;
            });
        }
        /**
         * 显示新消息按钮场景
         *1. 来的新消息超出一屏
          2. 是否有举报按钮 vScrollBar.max>0当前已经有一屏
         */
        protected updateChatScrollValue(): void {
            this.timer.clear(this, this.checkScrollShow);
            var listPanel = this.listPanel;
            var disValue = listPanel.vScrollBar.max - listPanel.vScrollBar.value;
            if (disValue > this.chatbox.height - 200 || yalla.common.BubbleReport.instance.visible) {
                if (!this.msgHintBtn.visible) this._newMsgScrollValue = listPanel.vScrollBar.max;
                if (listPanel.vScrollBar.max > 0) this.msgHintBtn.visible = true;
            } else {
                this.timer.frameOnce(30, this, this.checkScrollShow);
            }
        }

        public show() {
            this.refreshScroll(2);
            this.msgHintBtn.visible = false;
            this._newMsgScrollValue = this.listPanel.vScrollBar.value;
        }
        private refreshScroll(num: number) {
            this.timer.frameOnce(3, this, () => {
                var listPanel = this.listPanel;

                listPanel.vScrollBar.value = listPanel.vScrollBar.max - num;
                num--;
                if (num > 0) this.refreshScroll(num);
            })
        }

        public hide() {
            this.timer.clearAll(this);
        }
        private catchFouce() {
            this.chatbox.visible = false;
        }
        private onInput() {
            var len = yalla.util.getBLen(this.inputChat.text);
            this.sendNumTxt.text = "(" + Math.min(len, yalla.Global.MAX_CHARS) + "/" + yalla.Global.MAX_CHARS + ")"; len
            if (len < 1) this.inputChat.text = "";
            if (len > yalla.Global.MAX_CHARS) {
                if (this.inputChat.text) this.inputChat.text = yalla.util.filterStr(this.inputChat.text, yalla.Global.MAX_CHARS);
                return;
            }
        }
        public sendMsg() {
            if (yalla.Mute.isBanTalk) {//被运营禁言
                yalla.Mute.showBanTalkDialog();
                return;
            }
            var chat = this.inputChat.text;
            this.inputChat.text = "";
            this.onInput();
            if (chat.trim().length > 0) {
                this.event("close", false);

                if (yalla.Global.ChatLastMsg == chat) {
                    yalla.common.TipManager.instance.showTip('Please do not send messages repeatedly', {}, 2000);
                    return;
                }
                var result = { msg: chat };
                if (this.chatChannel == ChatChannel.TEAM) {
                    // result['extension'] = JSON.stringify({ type: 1 });
                    result['extension'] = 'chatChannel:1'; //TODO::服务端解析转义字符比较麻烦
                    yalla.util.clogDBAmsg('10188');//组队频道消息发送埋点
                }

                if (this._gameType == GameType.LUDO) {
                    yalla.event.YallaEvent.instance.event("ludo_chat_my", result);
                    ludo.GameSocket.instance.sendChat(result);
                } else if (this._gameType == GameType.DOMINO) {
                    yalla.data.RoomService.instance.sendChat(result);
                    yalla.event.YallaEvent.instance.event(yalla.data.EnumCustomizeCmd.Dominose_Chat_My, result);
                } else if (this._gameType == GameType.SNAKEANDLADER) {
                    yalla.data.snake.UserService.instance.sendChat(result);
                    yalla.event.YallaEvent.instance.event(yalla.data.snake.EnumCustomizeCmd.Game_Chat_My, result);
                } else if (this._gameType == GameType.JACKARO) {//TODO::这里msgType默认文本类型
                    let msgType = yalla.data.jackaro.GameChatMessageType.CHAT_MESSAGE_TEXT;
                    let curChannel = yalla.data.jackaro.GameChatChannelType.CHAT_CHANNEL_ALL;
                    if (this.chatChannel == ChatChannel.TEAM) {
                        curChannel = yalla.data.jackaro.GameChatChannelType.CHAT_CHANNEL_TEAM
                    }
                    yalla.data.jackaro.JackaroUserService.instance.sendChat(result, 0, msgType, curChannel);
                    yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_Chat_My, result);
                }
                yalla.Global.ChatLastMsg = chat;
            }
        }
        // private skinID: number = 12001;
        public onChat(chat: any) {
            var item: Laya.View;
            var poolKey = '';
            var [key_L, key_R] = ['ludo-chatItem-L', 'ludo-chatItem-R'];
            if (this.chatList.numChildren >= yalla.Global.ChatMaxLen) {

                yalla.Global.ChatMsgList.shift();
                item = this.chatList.getChildAt(0) as Laya.View;
                poolKey = item['side'] == 0 ? key_L : key_R;
                item.removeSelf();
                Laya.Pool.recover(poolKey, item);
                this.chatList.refresh();
                if (this.visible && this.msgHintBtn.visible && this._newMsgScrollValue > item.height) this._newMsgScrollValue -= item.height;
            }
            var cls: any = chat.side == 0 ? ChatItemL : ChatItemR;
            poolKey = chat.side == 0 ? key_L : key_R;
            item = Laya.Pool.getItemByClass(poolKey, cls);

            item['update'](chat, yalla.Global.ChatIndex, chat.playerShowInfo.talkSkinId);
            // item['update'](chat, yalla.Global.ChatIndex, this.skinID);
            // this.skinID += 1;
            // if (this.skinID > 12023) this.skinID = 12000;

            this.chatList.addChild(item);
            yalla.Global.ChatIndex++;
            yalla.Global.ChatMsgList.push(this.createReportMsg(chat.chat, chat.idx));
            this.hasChat = true;
            if (this.visible) this.updateChatScrollValue();
        }

        //IsReport:1被举报的聊天 0:非举报聊天 Type 1:文字 2:URL
        protected createReportMsg(msgResult: any, idx: number, isReport: number = 0, type: number = 1): any {
            var msg = '';
            if (msgResult) {
                if (msgResult.msg) {
                    msg = msgResult.msg;
                } else {
                    var extension = msgResult.extension;
                    if (extension) {
                        if (typeof (extension) == "string" && extension.indexOf('{') > -1) {
                            extension = JSON.parse(msgResult.extension);
                        }
                        msg = extension.id;
                    }
                }
            }
            return { "AccountId": idx, "MsgContent": msg, "IsReport": isReport, "Type": type };
        }

        private checkScrollShow(): void {
            this.listPanel.vScrollBar.value = this.listPanel.vScrollBar.max;
        }

        public setVisible(v: boolean): void {
            yalla.util.updateBackPressed(3, v ? 1 : 0);
            this.visible = v;
            //1.4.5 增加ios机型线上偶现bug日志上报，针对jackaro游戏服  聊天日志
            yalla.util.logReport({ log: "Chat setVisible: " + v, time: yalla.getTimeHMS() });
        }

        /**
         * socket 连接，输入框有内容，收起键盘，关闭聊天界面，消息自动发送，so，先清理输入框
         */
        public clearInputTxt(): void {
            this.inputChat.text = '';
            this.inputChat.focus = false;
        }

        public get listPanel(): any {
            if (this.chatChannel == 1) return this.teamChatPanel;
            return this.chatPanel;
        }

        public clear(): void {
            yalla.util.updateBackPressed(3);
            yalla.Global.ChatIndex = 0;
            this.timer.clearAll(this);
            this.inputChat.offAll();
            BubbleReport.instance.clear();

            yalla.Native.instance.off(yalla.Native.instance.Event.PRESSENTER, this, this.pressEnter);
            yalla.Native.instance.off(yalla.Native.instance.Event.SHOWKEYBOARD, this, this.showKeyboard);
            this.bg.off(Laya.Event.CLICK, this, this.pressEnter);
            this.sendBtn.off(Laya.Event.CLICK, this, this.pressEnter);
            this.off(Laya.Event.CLICK, this, this.onClick);
            this.off(Laya.Event.MOUSE_UP, this, this.onMonseUpChatPanel);
            yalla.event.YallaEvent.instance.off(yalla.data.EnumCustomizeCmd.ResetInputTextFocus, this, this.resetFocus);
            Laya.Pool.clearBySign('ludo-chatItem-L');
            Laya.Pool.clearBySign('ludo-chatItem-R');
            this.removeSelf();
            this.destroy(true);
        }
    }
}