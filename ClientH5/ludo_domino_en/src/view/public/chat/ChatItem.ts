module yalla.common {
    export class ChatItem extends ui.publics.chat.chatItemUI {
        private defaultFaceUrl = '';
        private _faceid: number = 0;
        private playerShowInfo: playerShowInfo = null;
        constructor(obj?: any) {
            super();
            !!obj && this.update(obj);
        }

        initUI(): void {
            this.nickName.pos(89, 6);
            this.chat_tx.size(NaN, NaN);
            this.chat_tx.pos(90, 40);
            this.chat_tx.text = '';

            this.item.pos(0, 0);
            this.item.size(NaN, NaN);
            this.item.top = this.item.bottom = NaN;

            this.size(NaN, NaN);
            this.pos(NaN, NaN);

            this.head.on("click", this, (e) => {
                yalla.Debug.log(this.playerShowInfo);
                e.stopPropagation();
                if (!!this.playerShowInfo) {
                    yalla.Sound.playSound("click");
                    yalla.Native.instance.showUserProfile(this.playerShowInfo.fPlayerInfo.idx);
                    // var view;
                    // if (this.playerShowInfo.fPlayerInfo.idx == yalla.Global.Account.idx) {
                    //     view = yalla.common.playerinfo.MyInfo.instance;
                    // } else {
                    //     view = yalla.common.playerinfo.OtherInfo.instance;
                    // }
                    // view.setData(this.playerShowInfo);
                    // yalla.DialogManager.instance.open(view, true, false, true, null);
                }
            })
        }

        update(obj: any) {
            this.playerShowInfo = obj.playerShowInfo;
            var fPlayerInfo = this.playerShowInfo.fPlayerInfo;
            this.head.y = 0;
            this.head_frame.y = 0;
            this.nickName.width = NaN;
            yalla.Emoji.lateLable(yalla.util.filterName(fPlayerInfo.nikeName), this.nickName, ['color', 'fontSize']);
            if (yalla.Global.gameType == GameType.LUDO) {
                this.line.skin = yalla.getGame('chat_line');
                this.defaultFaceUrl = yalla.getPublic('default_head');
                var player = ludoRoom.instance.getPlayerByIdx(obj.idx);
                var img = this.bb.getChildByName("Audience") as Laya.Image;
                if (!!player && player.sitNum < 0) {
                    if (!img) {
                        img = new Laya.Image(yalla.getGame('Audience2'));
                        img.name = "Audience";
                    } else {
                        img.visible = true;
                    }
                    var offX = 5;
                    if (fPlayerInfo.nikeName.hasArabic()) {
                        offX = 10;
                    }
                    var posX = this.nickName.x + this.nickName.width + offX;
                    img.pos(posX, this.nickName.y);
                    this.bb.addChild(img);
                } else if (!!img) {
                    img.visible = false;
                }
            } else {
                this.line.skin = yalla.getDomino('chat_line');
                this.defaultFaceUrl = yalla.getPublic('default_head');
            }
            this.loadFace(fPlayerInfo);
            if (obj.chat.indexOf("icon_emoji_") > -1) {
                this.chat_tx.visible = false;
                this.chat_emoji.visible = true;
                this.chat_emoji.skin = "public/" + obj.chat;
            } else {
                var chat: string = obj.chat;
                this.chat_tx.text = this.getChatStr(chat);
                this.chat_tx.visible = true;
                this.chat_tx.width = Laya.stage.width - 96;
                this.chat_emoji.visible = false;
            }
            this.head.centerY = 0;
            this.head_frame.centerY = 0;
            this.item.refresh();
        }

        private loadFace(data: FPlayerShowInfo): void {
            var faceUrl = data && data.faceUrl ? data.faceUrl : "";
            if (!faceUrl) faceUrl = this.defaultFaceUrl;
            this.head.skin = this.defaultFaceUrl;
            yalla.File.downloadImgByUrl(faceUrl, path => {
                yalla.File.groupLoad(path, Laya.Handler.create(this, (e) => {
                    if (!!e) {
                        this.head.skin = path;
                        yalla.util.centerSetup(this.head, 60, 10);
                    }
                }));
            })

            if (data.faceId > 0) {
                // this.head_frame.skin = yalla.getPublic(data.faceId);
                var path = "file://" + (yalla.File.cachePath + `/face_${data.faceId}.png`).replace(new RegExp('//', 'g'), "/");
                Laya.loader.load(path, Laya.Handler.create(this, (e) => {
                    if (!!e) {
                        this.head_frame.skin = path;
                    } else {
                        this.head_frame.skin = "public/face_bg.png";
                    }
                }));
            } else {
                this.head_frame.skin = "public/face_bg.png";
            }
        }

        private getChatStr(msg: string): string {
            msg = msg.replace(/[\r\n]/g, "");
            var len = yalla.util.getBLen(msg);
            if (len > yalla.Global.MAX_CHARS) return yalla.util.filterStr(msg, yalla.Global.MAX_CHARS);
            return msg;
        }
    }
}
