module yalla.common {
    export class ChatItemL extends ui.publics.chat.chatItemLUI {
        private defaultFaceUrl = '';
        private playerShowInfo: playerShowInfo = null;
        private chatObj: any = null;
        private _msgItem: MsgItem;
        public side: number;
        public isMe: boolean;
        public chatIndex: number;
        private reportAppearX: number = 0;

        constructor(obj?: any) {
            super();
            this.initUI();
        }

        private initUI(): void {
            this.reportAppearX = 0;
            this.head.on("click", this, this.onClickHead);
            this.msgBox.on(Laya.Event.CLICK, this, this.onClickMuteBubble);
            this.lb.mouseEnabled = false;

            this.chat_emoji.on(Laya.Event.CLICK, this, this.onClickMuteBubble);
        }
        private onClickMuteBubble(e: Laya.Event): void {
            if (this.isMe || (this._msgItem && this._msgItem.isEmoji)) return;
            e.stopPropagation();
            var p: Laya.Point = this.localToGlobal(new Laya.Point(this.msgBox.x, this.msgBox.y));
            if (!this.reportAppearX) {
                var xx: number = Math.min(Math.max(Laya.stage.mouseX, p.x + 70), p.x + this.msgBox.width - 60);
                this.reportAppearX = xx;
            }
            yalla.common.BubbleReport.instance.show(this.chatObj, this.reportAppearX, p.y + 20, this.chatIndex);
        }

        private onClickHead(e: Laya.Event): void {
            if (this.isMe) return;
            e.stopPropagation();
            if (!!this.playerShowInfo) {
                yalla.Sound.playSound("click");
                yalla.Native.instance.showUserProfile(this.playerShowInfo.fPlayerInfo.idx);
            }
        }

        public update(obj: any, chatIndex: number, skinId: number = 12001) {
            this.side = obj.side;
            this.isMe = obj.isMe;
            this.chatObj = obj;
            this.chatIndex = chatIndex;

            this.playerShowInfo = obj.playerShowInfo;
            var fPlayerInfo = this.playerShowInfo.fPlayerInfo;
            this.nickName.width = NaN;
            yalla.Emoji.lateLable(yalla.util.filterName(fPlayerInfo.nikeName), this.nickName, ['color', 'fontSize']);
            if (yalla.Global.gameType == GameType.LUDO) {
                var player = ludoRoom.instance.getPlayerByIdx(obj.idx);
                var img = this.bb.getChildByName("Audience") as Laya.Image;
                if (!!player && player.sitNum < 0) {
                    if (!img) {
                        img = new Laya.Image(yalla.getGame('Audience2'));
                        img.name = "Audience";
                    }
                    var offX = fPlayerInfo.nikeName.hasArabic() ? 10 : 5;
                    var posX = this.nickName.x + this.nickName.width + offX;
                    img.visible = true;
                    img.pos(posX, this.nickName.y);
                    this.bb.addChildAt(img, 0);
                } else if (!!img) {
                    img.visible = false;
                }
            }
            this.defaultFaceUrl = yalla.getPublic('default_head');
            this.loadFace(fPlayerInfo);
            if (!this._msgItem) this._msgItem = new MsgItem(this, 102);
            else this._msgItem.clear();
            this._msgItem.update(skinId, yalla.getPublic(this.isMe ? 'chat_right' : 'chat_left'), obj);
            this._msgItem.watchUI(obj, 1);
            // var msgItem = new MsgItem(this);
            // msgItem.update(skinId, obj);
            this.item.refresh();
        }

        private loadFace(data: FPlayerShowInfo): void {
            var faceUrl = data && data.faceUrl ? data.faceUrl : "";
            if (!faceUrl) faceUrl = this.defaultFaceUrl;
            this.head.skin = this.defaultFaceUrl;

            // var headPath = ChatManager.instance.getHeadSkin(faceUrl);
            // if (headPath.length > 0) this.head.skin = headPath;
            // else {
            yalla.File.downloadImgByUrl(faceUrl, path => {
                yalla.File.groupLoad(path, Laya.Handler.create(this, (e) => {
                    if (!!e) {
                        this.head.skin = path;
                        yalla.util.centerSetup(this.head, 60, { x: 10, y: 24 });
                        // ChatManager.instance.addLoadHeadSkin(faceUrl, path);
                    }
                }));
            })
            // }

            this.head_frame.skin = "public/face_bg.png";
            var faceId = data.faceId;
            if (faceId > 0) {//TODO：：因包内可能没有这个需要
                var fileName = `face_${faceId}.png`;
                var path = yalla.File.cachePath + "/" + fileName;
                if (yalla.File.existed(path)) {
                    this.head_frame.skin = yalla.File.filePath + fileName;
                } else {
                    yalla.event.YallaEvent.instance.once(fileName, this, () => {
                        this.head_frame.skin = yalla.File.filePath + fileName;
                    })
                    yalla.File.getFileByNative(fileName);
                }

                // var path = "file://" + (yalla.File.cachePath + `/face_${data.faceId}.png`).replace(new RegExp('//', 'g'), "/");
                // yalla.Debug.log('====chat ==path:'+path);
                // Laya.loader.load(path, Laya.Handler.create(this, (e) => {
                //     yalla.Debug.log('====chat ==e:'+e);
                //     if (!!e) {
                //         this.head_frame.skin = path;
                //     } else {
                //         this.head_frame.skin = "public/face_bg.png";
                //     }
                // }));
            }
        }
    }
}
