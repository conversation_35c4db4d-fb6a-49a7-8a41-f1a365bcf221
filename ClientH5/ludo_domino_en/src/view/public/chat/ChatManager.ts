module yalla.common {
    export class ChatManager extends laya.events.EventDispatcher {
        // private _chatBtn:Laya.Button;
        // private _chatBtnTimeLine:Laya.TimeLine;
        // public InAni:boolean = false;

        public msgSkinDic:Object;           //存放已经加载过的聊天皮肤
        public headSkinDic:Object;          //存放已经加载过的头像
        public headFrameSkinDic:Object;     //存放已经加载过的头像框

        constructor() {
            super();
        }

        public static _instance: ChatManager;
        static get instance(): ChatManager {
            if (!this._instance) {
                this._instance = new ChatManager();
            }
            return this._instance;
        }

        public addLoadMsgSkin(skinId:number):void{
            if(!this.msgSkinDic) this.msgSkinDic = {};
            this.msgSkinDic[skinId] = true;
        }

        public isMsgSkinLoaded(skinId:number):boolean{
            if(!this.msgSkinDic || !this.msgSkinDic[skinId]) return false;
            return true;
        }


        public addLoadHeadSkin(faceUrl:string, path:String):void{
            if(!this.headSkinDic) this.headSkinDic = {};
            this.headSkinDic[faceUrl] = path;
        }

        public getHeadSkin(faceUrl:string):string{
            if(this.headSkinDic && this.headSkinDic[faceUrl]) return this.headSkinDic[faceUrl];
            return '';
        }

        public addLoadHeadFrameSkin(path:string):void{
            if(!this.headFrameSkinDic) this.headFrameSkinDic = {};
            this.headFrameSkinDic[path] = true;
        }

        public getHeadFrameSkin(path:string):string{
            if(this.headFrameSkinDic && this.headFrameSkinDic[path]) return this.headFrameSkinDic[path];
            return '';
        }

        public initChatMsg(chatBtn:Laya.Button){
            // this._chatBtn = chatBtn;
            // this.InAni = false;
            // Laya.timer.loop(700, this, this.playChatAni);
            // this._chatBtnTimeLine = new Laya.TimeLine();
            // this._chatBtnTimeLine.to(this._chatBtn, { alpha: 0.2 }, 600)
            //                      .to(this._chatBtn, { alpha: 1 }, 600);
            // this._chatBtnTimeLine.pause();
        }
        private _count = 0;
        private playChatAni():void{
            // if(!this.InAni) return;
            // if(this._count % 2 == 0) this._chatBtn.visible = false;
            // else this._chatBtn.visible = true;
            // this._count++;
        }

        /**
         * 收到新消息，按钮呈现闪烁状态
         */
        public showChatMsgAni(){
            // if(!this.InAni){
            //     // this._chatBtnTimeLine.play(0, true);
            //     this.InAni = true;
            // }
        }

        public clearChatMsgAni():void{
            // this.InAni = false;
            // this._chatBtn.visible = true;
            // this._count = 0;
            // this._chatBtn.alpha = 1;
            // this._chatBtnTimeLine && this._chatBtnTimeLine.pause();
        }

        public clear():void{
            this.msgSkinDic = null;
            this.headSkinDic = null;
            this.headFrameSkinDic = null;
            // this.InAni = false;
            // Laya.timer.clear(this, this.playChatAni);
            // // if(this._chatBtnTimeLine){
            // //     this._chatBtnTimeLine.offAll();
            // //     this._chatBtnTimeLine.destroy();
            // // }
        }

    }
}