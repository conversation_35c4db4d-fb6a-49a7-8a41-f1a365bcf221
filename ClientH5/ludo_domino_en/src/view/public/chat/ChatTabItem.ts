module yalla.common {
    export class ChatTabItem extends ui.publics.chat.chatTabItemUI{

        public channelType: number = 0;
        constructor(channelType: number) {
            super();
            this.channelType = channelType;
        }
        
        public initTabUI(btnSkin:string = 'game/btn_chatBanner_noSelect.png', iconSkin:string = 'game/ico_gameChat_noSelect', txtColor:string='',txtStrokeColor:string=''): void{
            this.skin = btnSkin;
            this.icon.skin = iconSkin;
            this.txt.color = txtColor;
            this.txt.strokeColor = txtStrokeColor;
        }
       

        public clear(): void{
            this.offAll();
            this.destroy(true);
        }
    }    
}