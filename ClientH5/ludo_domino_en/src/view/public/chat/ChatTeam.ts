module yalla.common {
    export class ChatTeam extends Chat {
        private _chatIndexHash = {};			//聊天消息累计索引
        private _chatMsgListHash = {};

        private chatItemHash = {};
        private _selcectItem: ChatTabItem = null;
        /** chatList  teamChatList */
        private _chatListUI: any;

        public channelTypeList = [0, 1];                                                //聊天频道 0：All  1:Team 
        public selectColor = '#FFFFFF';                                                 //tab 选中的文本颜色
        public noSlectColor = '#332E7E';                                                //tab 非选中的文本颜色
        public selectStrokeColor = '#000000';                                           //tab 选中的文本边框颜色
        public noSelectStrokeColor = '#7676CC';                                         //tab 非选中的文本边框颜色
        public selectIconSkin = ['ico_gameChat_select', 'ico_teamChat_select'];         //tab 选中的icon
        public noSelectIconSkin = ['ico_gameChat_noSelect', 'ico_teamChat_noSelect'];    //tab 非选中的icon
        private curGameType: number;
        constructor(gameType: number = yalla.Global.gameType) {
            super(gameType);
            this.curGameType = gameType;
            this.teamChatPanel.vScrollBarSkin = this.urlFunc('scroll');
            this.teamChatPanel.vScrollBar.hide = true;
            this.teamChatPanel.vScrollBar.elasticDistance = 0;
            this.teamChatList.size(Laya.stage.width, 0);
            this.teamChatList['sortItem'] = function (items) {
                return;
            }

            this._chatListUI = this.chatList;
            this.channelBannerBox.visible = true;
            this.chatTitleTxt.visible = false;
            if (yalla.Global.gameType == GameType.JACKARO) {
                this.selectColor = "#FFFFFF";
                this.selectStrokeColor = "#007C4B";
                this.noSlectColor = "#005E39";
                this.noSelectStrokeColor = "#1FD08A";
            }
            this.adapterUI();
        }

        private initChatHash(chatChannel: number = 0): void {
            if (!this._chatMsgListHash) this._chatMsgListHash = {};
            if (!this._chatIndexHash) this._chatIndexHash = {};
            if (!this._chatIndexHash[chatChannel]) this._chatIndexHash[chatChannel] = 0;
            if (!this._chatMsgListHash[chatChannel]) this._chatMsgListHash[chatChannel] = [];
        }

        public adapterUI(): void {
            var len = this.channelTypeList.length;
            var itemWid = Math.ceil(Laya.stage.width / len) + 6;
            var beginX = 0;
            for (var i = 0; i < len; i++) {
                var cType = this.channelTypeList[i];
                var chatTabItem: ChatTabItem = new ChatTabItem(cType);
                chatTabItem.width = itemWid;
                chatTabItem.x = beginX;
                beginX += itemWid;
                chatTabItem.on(Laya.Event.CLICK, this, this.onClickTab);
                this.initTabUI(chatTabItem, cType);
                this.channelBannerBox.addChild(chatTabItem);

                this.chatItemHash[cType] = chatTabItem;
            }
            this.selectChatTab();
        }

        private onClickTab(e: MouseEvent): void {
            var chatTabItem = e.currentTarget;
            if (this._selcectItem && this._selcectItem.channelType == chatTabItem['channelType']) return;
            var channelType = chatTabItem['channelType'];
            this.selectChatTab(channelType);

            /** 举报信息重新赋值 */
            this.initChatHash(this.chatChannel);
            yalla.Global.ChatIndex = this._chatIndexHash[this.chatChannel];
            yalla.Global.ChatMsgList = this._chatMsgListHash[this.chatChannel];

            if (channelType == ChatChannel.TEAM) {//组队频道入口点击
                if (this.curGameType == GameType.JACKARO) {
                    yalla.util.clogDBAmsg('10368');
                } else {
                    yalla.util.clogDBAmsg('10187');
                }
            } else {
                if (this.curGameType == GameType.JACKARO) {
                    yalla.util.clogDBAmsg('10369');
                }
            }


        }

        public initTabUI(chatTabItem: ChatTabItem, channelType: number = 0): void {
            switch (channelType) {
                case 1:
                    chatTabItem.skin = this.curGameType == GameType.JACKARO ? yalla.getJackaro('btn_chatBanner_noSelect') : yalla.getGame('btn_chatBanner_noSelect');
                    chatTabItem.txt.text = 'Team';
                    chatTabItem.txt.color = this.noSlectColor;
                    chatTabItem.txt.strokeColor = this.noSelectStrokeColor;
                    chatTabItem.icon.skin = this.curGameType == GameType.JACKARO ? yalla.getJackaro('ico_teamChat_noSelect') : yalla.getGame('ico_teamChat_noSelect');
                    break;
                default:
                    chatTabItem.skin = this.curGameType == GameType.JACKARO ? yalla.getJackaro('btn_chatBanner_noSelect') : yalla.getGame('btn_chatBanner_noSelect');
                    chatTabItem.txt.text = 'All';
                    chatTabItem.txt.color = this.noSlectColor;
                    chatTabItem.txt.strokeColor = this.noSelectStrokeColor;
                    chatTabItem.icon.skin = this.curGameType == GameType.JACKARO ? yalla.getJackaro('ico_gameChat_noSelect') : yalla.getGame('ico_gameChat_noSelect');
                    break;
            }
        }

        /**
         * 1 组队聊天
         * @param channelType 
         */
        public selectChatTab(channelType: number = 0): void {
            this.chatChannel = channelType;

            if (!this.chatItemHash) return;
            var chatTabItem: ChatTabItem = this.chatItemHash[channelType];
            if (!chatTabItem) return;
            if (channelType == 1) {
                this.teamChatPanel.visible = true;
                this.chatPanel.visible = false;
                this._chatListUI = this.teamChatList;
                this.teamChatPanel.vScrollBar.value = this.teamChatPanel.vScrollBar.max;
            } else {
                this.teamChatPanel.visible = false;
                this.chatPanel.visible = true;
                this._chatListUI = this.chatList;
                this.chatPanel.vScrollBar.value = this.chatPanel.vScrollBar.max;
            }

            let btnChatBannerSelect = this.curGameType == GameType.JACKARO ? yalla.getJackaro('btn_chatBanner_select') : yalla.getGame('btn_chatBanner_select');
            let selectIconSkin = this.curGameType == GameType.JACKARO ? yalla.getJackaro(this.selectIconSkin[channelType]) : yalla.getGame(this.selectIconSkin[channelType]);
            let noSelectIconSkin = this.curGameType == GameType.JACKARO ? yalla.getJackaro(this.noSelectIconSkin[lastChannelType]) : yalla.getGame(this.noSelectIconSkin[lastChannelType]);
            let btnChatBannerNoSelect = this.curGameType == GameType.JACKARO ? yalla.getJackaro('btn_chatBanner_noSelect') : yalla.getGame('btn_chatBanner_noSelect');


            chatTabItem.initTabUI(btnChatBannerSelect, selectIconSkin, this.selectColor, this.selectStrokeColor);
            if (this._selcectItem) {
                var lastChannelType = this._selcectItem.channelType;
                this._selcectItem.initTabUI(btnChatBannerNoSelect, noSelectIconSkin, this.noSlectColor, this.noSelectStrokeColor);
            }
            this._selcectItem = chatTabItem;
        }

        public onChat(chat: any) {
            var item: Laya.View;
            var poolKey = '';
            var [key_L, key_R] = ['ludo-chatItem-L', 'ludo-chatItem-R'];
            var chatChannel = 0;
            var msg = chat.chat;
            var extension = msg.extension;
            var channelType = msg.channelType;
            if (extension) {
                if (typeof (extension) == "string") {
                    if (extension.indexOf('chatChannel:') > -1) {
                        var arr = extension.split(":");
                        chatChannel = Number(arr[1]);
                    }
                }
            }
            if (channelType == yalla.data.jackaro.GameChatChannelType.CHAT_CHANNEL_TEAM) {
                chatChannel = 1;
            }
            if (chatChannel == 1) this._chatListUI = this.teamChatList;
            else this._chatListUI = this.chatList;

            this.initChatHash(chatChannel);

            var chatMsgList = this._chatMsgListHash[chatChannel];
            var chatIndex = this._chatIndexHash[chatChannel];

            if (this._chatListUI.numChildren >= yalla.Global.ChatMaxLen) {
                chatMsgList.shift();
                item = this._chatListUI.getChildAt(0) as Laya.View;
                poolKey = item['side'] == 0 ? key_L : key_R;
                item.removeSelf();
                Laya.Pool.recover(poolKey, item);
                this._chatListUI.refresh();

                if (this.visible && this.msgHintBtn.visible && this._newMsgScrollValue > item.height) this._newMsgScrollValue -= item.height;
            }

            var cls: any = chat.side == 0 ? ChatItemL : ChatItemR;
            poolKey = chat.side == 0 ? key_L : key_R;
            item = Laya.Pool.getItemByClass(poolKey, cls);

            item['update'](chat, chatIndex, chat.playerShowInfo.talkSkinId);

            this._chatListUI.addChild(item);
            chatIndex++;
            chatMsgList.push(this.createReportMsg(msg, chat.idx));
            this.hasChat = true;
            if (this.visible) this.updateChatScrollValue();

            // /** 举报信息重新赋值 如果当前频道和发送的消息频道一致，则直接赋值Global.ChatIndex，Global.ChatMsgList */
            if (chatChannel == this.chatChannel) {
                yalla.Global.ChatIndex = chatIndex;
                yalla.Global.ChatMsgList = chatMsgList;
            }
            this._chatIndexHash[chatChannel] = chatIndex;
            this._chatMsgListHash[chatChannel] = chatMsgList;
        }

        public clear(): void {
            super.clear();
            this._chatIndexHash = null;
            this._chatMsgListHash = null;
        }
    }
}