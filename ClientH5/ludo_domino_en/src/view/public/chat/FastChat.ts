module yalla.common {
    import ChatDataPool = yalla.data.ChatDataPool;
    export class FastChat extends ui.publics.chat.fast_chatUI {
        private _gameType: number;
        private _workSkin: string;
        private _tabLine: string;
        private _tabMid: string;
        private _wordPoints: Array<string>;

        private _selectedFastTabItem: FastTabItem;
        private _emojiThemeList: Array<EmojiThemeConf>;
        private _tabHash: Object;


        constructor(gameType: number) {
            super();
            this._gameType = gameType;
            this.initUI();
            this.initEvent();
            this.on("click", this, (e) => {
                e.stopPropagation();
            })

            if (this._gameType == GameType.LUDO) this._wordPoints = yalla.fastChat_Points.ludo;
            else if (this._gameType == GameType.DOMINO) this._wordPoints = yalla.fastChat_Points.domino;
            else if (this._gameType == GameType.SNAKEANDLADER) this._wordPoints = yalla.fastChat_Points.snake;
            else if (this._gameType == GameType.JACKARO) this._wordPoints = yalla.fastChat_Points.jackaro;

            yalla.data.ChatDataPool.Instance.sendEmojiResponse(() => {
                this._emojiThemeList = ChatDataPool.Instance.emojiThemeList;
                this.initEmoji();
            });
        }

        private initUI(): void {
            this.emojiList.array = [];//TODO::现在增加已购买表情考前，如果表情列表数量少于一屏，会有小马表情展示
            this.emojiList.renderHandler = new Laya.Handler(this, this.updateItem);

            if (yalla.util.IsBrowser()) {
                ChatDataPool.Instance.initEmojiData(ChatDataPool.Instance.testDataList);
                this._emojiThemeList = ChatDataPool.Instance.emojiThemeList;
                this.initEmoji();
            }

            //更换背景
            this.initImgSkin(this._gameType);
            this.wordList.renderHandler = new Laya.Handler(this, this.updateWordItem);
        }

        private initEvent(): void {
            this.wordList.on(Laya.Event.CLICK, this, this.onClickChatWord);
            this.emojiList.on(Laya.Event.CLICK, this, this.onClickChat);
            this.tabList.on(Laya.Event.CLICK, this, this.onClickSendFastTab);
            this.buyBtn.on(Laya.Event.CLICK, this, this.onClickBuyEmoji);
        }
        private initImgSkin(gameType: number): void {
            var urlFunc = yalla.getGame;
            var wordList = yalla.fastChat.ludoWords;
            if (gameType == GameType.DOMINO) {
                urlFunc = yalla.getDomino;
                wordList = yalla.fastChat.dominoWords;
            } else if (this._gameType == GameType.SNAKEANDLADER) {
                urlFunc = yalla.getSnake;
                wordList = yalla.fastChat.snakeWords;
            } else if (this._gameType == GameType.JACKARO) {
                urlFunc = yalla.getJackaro;
                wordList = yalla.fastChat.jackaroWords;
            }
            this.bg.skin = urlFunc('BG_Expression');
            this.emojiBg.skin = urlFunc('table_ROOM');
            this._workSkin = urlFunc('table_words');
            this.arrow.skin = urlFunc('BG_Expression_V');

            this.tabBg.skin = urlFunc('biaoqing_tab_bg');
            this.maskBg.skin = urlFunc('biaoqing_mask_game');
            this.line.skin = urlFunc('biaoqing_line');
            this.tabLeft.skin = urlFunc('biaoqing_bg_left');
            this.tabRight.skin = urlFunc('biaoqing_bg_right');
            this._tabLine = urlFunc('biaoqing_line_tab');
            this._tabMid = urlFunc('biaoqing_tab_mid');
            this.bg.sizeGrid = '15,15,15,15';
            this.emojiBg.sizeGrid = '17,18,17,18';
            this.tabBg.sizeGrid = '16,16,6,16';
            this.wordList.array = wordList;
        }

        private removeEvent(): void {
            this.wordList.off(Laya.Event.CLICK, this, this.onClickChatWord);
            this.emojiList.off(Laya.Event.CLICK, this, this.onClickChat);
            this.tabList.off(Laya.Event.CLICK, this, this.onClickSendFastTab);
            this.buyBtn.off(Laya.Event.CLICK, this, this.onClickBuyEmoji);
        }

        /**
         * 初始化表情
         * tab & 列表
         */
        private initEmoji(): void {

            this.initTabList();
            this.callLater(() => {
                var themeId = 0;
                if (this._emojiThemeList && this._emojiThemeList[0]) themeId = this._emojiThemeList[0].id;
                if (this._tabHash && this._tabHash[themeId]) {
                    this._selectedFastTabItem = this._tabHash[themeId];
                    this._selectedFastTabItem.selected = true;
                }

                this.updateEmojiList(themeId);
            })
        }

        private initTabList(): void {
            this.tabList.array = this._emojiThemeList || [];
            this.tabList.renderHandler = new Laya.Handler(this, this.renderFastTabItem);

            this.tabList.hScrollBarSkin = yalla.getPublic('scroll');
            if (this._emojiThemeList && this._emojiThemeList.length > ChatDataPool.Instance.pageCount) {
                if (this.tabList && this.tabList.scrollBar) {
                    this.tabList.scrollBar.isVertical = false;
                    this.tabList.scrollBar.elasticBackTime = 300;
                    this.tabList.scrollBar.elasticDistance = 200;
                }
            }
        }

        private renderFastTabItem(cell: any, index: number) {
            if (!this._emojiThemeList || !this._emojiThemeList[index]) return;
            if (!this._tabHash) this._tabHash = {};

            var emojiThemeConf = this._emojiThemeList[index];
            cell.dataSource = emojiThemeConf;
            var fastTabItem: FastTabItem = this._tabHash[emojiThemeConf.id];
            if (!fastTabItem) {
                fastTabItem = new FastTabItem(cell);
                this._tabHash[emojiThemeConf.id] = fastTabItem;
            }
            var tabIconName = ChatDataPool.Instance.emojiThemeIcon[emojiThemeConf.id];
            fastTabItem.initData(emojiThemeConf, tabIconName, [this._tabLine, this._tabMid]);
        }

        private updateWordItem(cell: Laya.Button, index: number) {
            cell.skin = this._workSkin;
            if (this._gameType == GameType.LUDO) cell.labelStrokeColor = '#575690';
            else if (this._gameType == GameType.DOMINO) cell.labelStrokeColor = '#105740';
            else if (this._gameType == GameType.SNAKEANDLADER) cell.labelStrokeColor = '#105740';
            else if (this._gameType == GameType.JACKARO) cell.labelStrokeColor = '#105740';
        }

        private updateItem(cell: Laya.Button, index: number) {
            cell.label = '';
            cell.skin = '';

            if (!this._selectedFastTabItem || this._selectedFastTabItem.themeId == 0) {
                if (this._selectedFastTabItem) cell.skin = "public/" + cell.dataSource;
                else cell.skin = '';
            } else {
                var emojiConf: EmojiConf = this._selectedFastTabItem.emojiTheme.emojiConfig[index];
                if (!emojiConf) return;
                var emojiId = emojiConf.uniqueID;

                var path = `${yalla.File.cachePath}/emoji_${emojiId}`;
                var imgUrl = emojiConf.url;
                if (yalla.File.existed(`${path}.png`)) {
                    imgUrl = yalla.File.filePath + `emoji_${emojiId}.png`;
                } else {
                    //TODO:如果icon资源没有下载，则使用后台配置资源 且 同时再次下载资源
                    imgUrl = yalla.DomainUtil.instance.getFileDomainType_flexible(imgUrl)
                    yalla.File.getFileByNative(`emoji_${emojiId}.png`);
                }

                Laya.loader.load(imgUrl, Laya.Handler.create(this, (imgPath, themeId, emojiId) => {
                    if (this._selectedFastTabItem && this._selectedFastTabItem.themeId != themeId) return;
                    let curBindUniqueID: number;
                    if (this._selectedFastTabItem && this._selectedFastTabItem.emojiTheme && this._selectedFastTabItem.emojiTheme.emojiConfig[index]) {
                        curBindUniqueID = this._selectedFastTabItem.emojiTheme.emojiConfig[index].uniqueID;
                    }
                    if (emojiId != curBindUniqueID) return;
                    // console.log(emojiConf.url + "====快捷表情skin===" + imgPath + "==path:"+path);

                    cell.skin = imgPath;
                }, [imgUrl, this._selectedFastTabItem.themeId, emojiId]));

                // if (yalla.util.IsBrowser()) {//TODO::测试
                //     Laya.loader.load(`res/test/emoji_${emojiId}.png`, Laya.Handler.create(this, () => {
                //         cell.skin = `res/test/emoji_${emojiId}.png`;
                //     }));
                // }
            }
        }

        /**
         * 根据不同的themeId，emojiList赋值不同的array
         * @param tabIndex 
         */
        private updateEmojiList(themeId: number = 0) {
            var emojiDataList = [];
            if (themeId == 0) {
                emojiDataList = yalla.fastChat.emojiSkin;
                this.maskBg.visible = false;
            } else {
                var emojiThemeConf: EmojiThemeConf = ChatDataPool.Instance.emojiThemeById(themeId);
                if (emojiThemeConf) {
                    emojiDataList = emojiThemeConf.emojiConfig;
                    if (!emojiThemeConf.isOwn) {
                        this.maskBg.visible = true;
                        Debug.log(emojiThemeConf.needRoyalLevel + "==表情==" + yalla.Global.Account.realRoyLevel);
                        if (emojiThemeConf.needRoyalLevel > 0 && emojiThemeConf.needRoyalLevel > yalla.Global.Account.realRoyLevel) {
                            if (emojiThemeConf.arbicNameDesc || emojiThemeConf.nameDesc) {
                                this.txt_emoji.text = yalla.Font.lan == 'ar' ? emojiThemeConf.arbicNameDesc : emojiThemeConf.nameDesc;
                            } else {
                                this.txt_emoji.text = 'Exclusive for Royal users';
                            }
                            this.maskBox.centerY = 0;
                            this.buyBtn.visible = false;
                        } else {
                            this.updateBuyEmojiUI(emojiThemeConf);
                        }
                        this.maskBox.centerX = 0;
                    } else {
                        this.maskBg.visible = false;
                    }
                } else {
                    emojiDataList = [];
                }
            }
            if (emojiDataList) {
                // var len = emojiDataList.length;
                // if (len > 0 && len % 8 == 0) {
                //     this.emojiList.repeatX = 4;
                //     this.emojiList.spaceX = 50;
                //     this.emojiList.width = 93 * 4 + 50 * 3;//613//484
                // } else {
                //     this.emojiList.repeatX = 5;
                //     this.emojiList.spaceX = 36;
                //     this.emojiList.width = 613
                // }
                // this.emojiList.centerX = 0;
                this.emojiList.array = emojiDataList;
            }
            this._themeId = themeId;
        }

        private _priceType: number = 2;//1金币 2钻石
        private updateBuyEmojiUI(emojiThemeConf: EmojiThemeConf): void {
            this.maskBox.centerY = -50;
            this.txt_emoji.text = 'Purchase to use';
            this.buyBtn.visible = true;
            if (emojiThemeConf.diamondPrice && emojiThemeConf.diamondPrice > 0) {
                this.priceTxt.text = yalla.util.filterNum(emojiThemeConf.diamondPrice);
                this.priceIcon.skin = 'public/Diamonds.png';
                this._priceType = 2;
            } else {
                this._priceType = 1;
                this.priceTxt.text = yalla.util.filterNum(emojiThemeConf.currencyPrice);
                this.priceIcon.skin = 'public/coins.png';
            }
        }
        private onClickBuyEmoji(e: Laya.Event): void {
            if (!this._selectedFastTabItem || !this._selectedFastTabItem.emojiTheme) return;
            var emojiThemeConf: EmojiThemeConf = this._selectedFastTabItem.emojiTheme;
            var money = yalla.Global.Account.currentGoldNum;
            var price = emojiThemeConf.currencyPrice;
            if (this._priceType == 2) {
                money = yalla.Global.Account.currentDiamondNum;
                price = emojiThemeConf.diamondPrice;
            }
            yalla.util.clogDBAmsg('10211', emojiThemeConf.id);
            var msg = "";
            Debug.log(money + "：money==购买表情==_priceType：" + this._priceType + "===price:" + price);

            // if (price > money) {
            //     if (this._priceType == 2) {
            //         msg = yalla.data.ChatDataPool.Instance.checkBuyEmojiError(402);
            //         //1.4.4 购买表情钻石不足，也直接触发buyEmojiTheme，由原生处理购买逻辑
            //     } else {
            //         msg = yalla.data.ChatDataPool.Instance.checkBuyEmojiError(401);
            //         yalla.Native.instance.showToast({ msg: msg });
            //         return;
            //     }
            // }

            yalla.Native.instance.buyEmojiTheme(emojiThemeConf.id, price, this._priceType, (data) => {
                if (data) {
                    ChatDataPool.Instance.isNewEmojiList = true;
                    var backEmojiThemeConf: EmojiThemeConf = ChatDataPool.Instance.emojiThemeById(data.id);
                    if (!backEmojiThemeConf) { yalla.Debug.log("购买表情后，返回的id找不到主题信息"); return; }

                    if (data.result == 1) {
                        backEmojiThemeConf.isOwn = true;
                        this.maskBg.visible = false;
                        //TODO::这里有疑问，原生会不会重复回调？会不会买A是消耗金币，快速切换B购买但是消耗钻石，这时候回调B 再回调 A；原生只会调用一次
                        if (backEmojiThemeConf.currencyPrice && backEmojiThemeConf.currencyPrice > 0) {
                            yalla.Global.Account.currentGoldNum -= backEmojiThemeConf.currencyPrice;
                        }
                        else if (backEmojiThemeConf.diamondPrice && backEmojiThemeConf.diamondPrice > 0) {
                            yalla.Global.Account.currentDiamondNum -= backEmojiThemeConf.diamondPrice;
                        }

                        yalla.Native.instance.event(yalla.Native.instance.Event.UPDATEMONEY);
                        yalla.util.clogDBAmsg('10220', backEmojiThemeConf.id);

                        ChatDataPool.Instance.canSortEmoji = true;
                        if (this._gameType == GameType.JACKARO) {//TODO::jack 大厅消费后，需要通知服务端游钻石消耗。如果不通知，当局解锁的头像服务端不认可
                            yalla.data.jackaro.JackaroUserService.instance.flushCurrencyRequest()
                        }
                    } else {
                        if (data.hasOwnProperty('code')) {
                            msg = yalla.data.ChatDataPool.Instance.checkBuyEmojiError(data.code);
                            msg && yalla.Native.instance.showToast({ msg: msg });
                        }
                    }
                } else {
                    yalla.Debug.log("======购买表情失败========");
                    ChatDataPool.Instance.isNewEmojiList = false;
                }
            });
        }

        private onClickSendFastTab(e: Laya.Event): void {
            if (!this._tabHash) return;
            // e.stopPropagation();
            if (e.target instanceof ui.publics.chat.fastTabItemUI) {
                var themeId = Number(e.target.name);
                var fastTabItem: FastTabItem = this._tabHash[themeId];
                // yalla.Debug.log("=======切换tab=====themeId：" + themeId);
                if (!this._emojiThemeList || this._emojiThemeList.length <= 1) {
                    this.emojiList.array = [];
                    return;
                }
                if (fastTabItem && this._selectedFastTabItem && fastTabItem.themeId == this._selectedFastTabItem.themeId) return;
                this.emojiList.array = [];

                //更新tab标签
                if (this._selectedFastTabItem && fastTabItem && this._selectedFastTabItem.themeId != fastTabItem.themeId) {
                    this._selectedFastTabItem.selected = false;
                    fastTabItem.selected = true;
                    this._selectedFastTabItem = fastTabItem;

                    if (fastTabItem.themeId) yalla.util.clogDBAmsg('10147', fastTabItem.themeId); //小马动画不埋点
                }

                //更新list列表
                this.updateEmojiList(themeId);
            }
        }

        /**
         * 
         * @param e 
         * @param chatType 1文字 其他事表情
         */
        private onClickChat(e: Laya.Event, chatType: number = 0): void {
            // e.stopPropagation();
            if (e.target instanceof Laya.Button) {
                console.log("======发送表情 chatType：" + chatType);
                if (e.target && e.target.dataSource) {
                    var dataSource = e.target.dataSource;
                    var result = {};
                    var msg = '';
                    var returnValue;
                    // var emojiId = "10101";
                    // result['extension'] = JSON.stringify({ id: emojiId });

                    var emojiId = 0;
                    if (chatType != 1 && (this._selectedFastTabItem && this._selectedFastTabItem.themeId != 0)) {
                        if (!this._selectedFastTabItem.emojiTheme.isOwn) return;
                        var id = dataSource.id;
                        emojiId = dataSource.uniqueID;
                        result['extension'] = JSON.stringify({ id: emojiId });
                        yalla.util.clogDBAmsg('10149', this._selectedFastTabItem.themeId + "_" + id);
                        yalla.Debug.log(id + ':id===发送新的快捷表情啦====emojiId:' + emojiId);

                    } else {
                        if (typeof dataSource != "string") return;
                        msg = dataSource.replace('\n', ' ')
                    }
                    result['msg'] = msg;
                    this.event("close", false);
                    if (this._gameType == GameType.LUDO) {
                        yalla.event.YallaEvent.instance.event("ludo_chat_my", result);
                        ludo.GameSocket.instance.sendChat(result);
                    } else if (this._gameType == GameType.DOMINO) {
                        yalla.data.RoomService.instance.sendChat(result);
                        yalla.event.YallaEvent.instance.event(yalla.data.EnumCustomizeCmd.Dominose_Chat_My, result);
                    } else if (this._gameType == GameType.SNAKEANDLADER) {
                        yalla.data.snake.UserService.instance.sendChat(result);
                        yalla.event.YallaEvent.instance.event(yalla.data.snake.EnumCustomizeCmd.Game_Chat_My, result);
                    } else if (this._gameType == GameType.JACKARO) {
                        let msgType = yalla.data.jackaro.GameChatMessageType.CHAT_MESSAGE_PHRASE;
                        if (result['extension']) {
                            msgType = yalla.data.jackaro.GameChatMessageType.CHAT_MESSAGE_EMOJI;
                            if (emojiId) result['msg'] = String(emojiId);
                        }
                        console.log(result);
                        yalla.data.jackaro.JackaroUserService.instance.sendChat(result, 0, msgType, yalla.data.jackaro.GameChatChannelType.CHAT_CHANNEL_ALL);
                        yalla.event.YallaEvent.instance.event(yalla.data.jackaro.EnumCustomizeCmd.Game_Chat_My, result);
                    }
                }
            }
        }

        private onClickChatWord(e: Laya.Event): void {
            this.onClickChat(e, 1);
            if (e.target instanceof Laya.Button) {
                if (e.target && e.target.dataSource) {
                    var index = e.target.parent.getChildIndex(e.target);
                    if (this._wordPoints && this._wordPoints[index]) {
                        yalla.Native.instance.mobClickEvent(this._wordPoints[index]);
                    }
                }
            }
        }

        private _themeId = 0;
        public setVisible(v: boolean): void {
            let isNewEmojiList = ChatDataPool.Instance.isNewEmojiList;
            // yalla.Debug.log(v+"：v====设置快捷面板v=====isNewEmojiList22=" + isNewEmojiList +' canSortEmoj:'+ChatDataPool.Instance.canSortEmoji);
            if (v) {
                ChatDataPool.Instance.canSortEmoji = false;
                if (this._tabHash) {
                    this._selectedFastTabItem = this._tabHash[this._themeId];
                    if (this._selectedFastTabItem) this._selectedFastTabItem.selected = true;
                }
            }

            this.visible = v;
            yalla.util.updateBackPressed(4, v ? 1 : 0);

            if (!v) {
                if (!isNewEmojiList) {
                    yalla.Debug.log('====表情列表重新获取 11===')
                    ChatDataPool.Instance.sendEmojiResponse(() => {
                        if (this._selectedFastTabItem) {
                            this._themeId = this._selectedFastTabItem.themeId;
                        }
                        this.updateEmojiList(this._themeId);
                    })
                } else if (ChatDataPool.Instance.canSortEmoji) {
                    if (this._selectedFastTabItem) {
                        this._selectedFastTabItem.selected = false;
                        this._themeId = this._selectedFastTabItem.themeId;
                    }
                    this._tabHash = {};
                    ChatDataPool.Instance.sortEmoji();
                    this._emojiThemeList = ChatDataPool.Instance.emojiThemeList;
                    this.tabList.array = this._emojiThemeList || [];
                }
            }
        }
        public clear(): void {
            yalla.util.updateBackPressed(4);
            this._tabHash = null;
            this.removeEvent();
            this.removeSelf();
            this.destroy(true);
        }
    }
}