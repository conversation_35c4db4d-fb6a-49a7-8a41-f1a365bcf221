module yalla.common {
    // export class FastTabItem extends ui.publics.chat.fastTabItemUI {
    export class FastTabItem{
        public ui: ui.publics.chat.fastTabItemUI;
        public tabName = 'icon_bq_horse';
        public emojiTheme: EmojiThemeConf;

        constructor(ui:ui.publics.chat.fastTabItemUI) {
            this.ui = ui;
        }
        public initData(emojiTheme, tabName: string, tabSkinList: Array<string>): void {
            this.emojiTheme = emojiTheme;
            var themeId = emojiTheme.id;
            this.ui.name = String(themeId);
            if (tabSkinList) {
                this.ui.tab_bg.skin = tabSkinList[1];
                this.ui.tab_line.skin = tabSkinList[0];
            }

            if (yalla.util.IsBrowser()) {
                this.ui.tab_icon.skin = yalla.getPublic(tabName);
            } else {
                if (this.emojiTheme.url) {
                    this.ui.tab_icon.skin = this.emojiTheme.url;
                } else {
                    this.ui.tab_icon.skin = yalla.getPublic(tabName);
                }
            }
        }

        public set selected(v: boolean) {
            this.ui.tab_bg.visible = v;
            this.ui.tab_line.visible = !v;
        }

        public get themeId(): number{
            if (this.emojiTheme) return this.emojiTheme.id;
            return 0;
        }

        public clear(): void{
            this.ui.offAll();
            this.ui.destroy(true);
        }
    }    
}