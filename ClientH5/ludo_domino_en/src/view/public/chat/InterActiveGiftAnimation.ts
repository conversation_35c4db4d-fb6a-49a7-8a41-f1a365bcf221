module yalla.common {
    export class InteractiveGiftAnimation extends Laya.EventDispatcher {
        private _templateList: Array<Laya.Templet> = [];
        private _skList: Array<Laya.Skeleton> = [];
        private _timeLineList: Array<Laya.TimeLine> = [];
        private _parent: Laya.Box;
        public isNextMsg: boolean = false;

        public static _instance: InteractiveGiftAnimation;

        Event = {
            UpdateGiftIcon: "UpdateGiftIcon",//更新头像上方礼物icon
        }

        public static get Instance(): InteractiveGiftAnimation {
            if (!this._instance) this._instance = new InteractiveGiftAnimation();
            return this._instance;
        }
        /**
         * @param sendData { giftId, senderId, recIdx} 
         * @param target 
         * @param fromPos 
         * @param toPos 
         */
        public playAni(isNextMsg:boolean, sendData: any, aniConf: GiftAniCnf, targetParent: any, fromPos: any, toPos: any = null): void {
            this._parent = targetParent;
            var isWake = yalla.Global.game_state != yalla.data.GameState.State_Sleep;
            if(isWake) this.skAnimationStart(isNextMsg, sendData, aniConf, fromPos, toPos);
        }
        /**
         * spine动画第一帧开始直线运动
         */
        private skAnimationStart(isNextMsg:boolean, sendData: any, aniConf: GiftAniCnf, fromPos: any, toPos: any = null): void{
            if (yalla.util.IsBrowser()) {
                this.initSK(isNextMsg, sendData, aniConf, fromPos, toPos);
            } else {
                yalla.event.YallaEvent.instance.once(`gift_${sendData.giftId}.sk`, this, (sendData: any, aniConf: GiftAniCnf, fromPos: any, toPos: any = null) => {
                    this.initSK(isNextMsg, sendData, aniConf, fromPos, toPos);
                    yalla.Debug.log('---initSK---giftId:'+sendData.giftId);
                }, [sendData, aniConf, fromPos, toPos]);
                yalla.event.YallaEvent.instance.once(`gift_${sendData.giftId}.png`, this, () => {
                    yalla.File.getFileByNative(`gift_${sendData.giftId}.sk`);
                })
                yalla.File.getFileByNative(`gift_${sendData.giftId}.png`);            
            }
        }

        private initSK(isNextMsg: boolean, sendData: any, aniConf: GiftAniCnf, fromPos: any, toPos: any = null) {
            var disx, disy, fPos, tPos;
            if (aniConf) {
                var side = sendData.side;
                if (!toPos) side = side + 4;
                yalla.Debug.log(aniConf.pos);
                var posVal = aniConf.pos.split('|');
                if (posVal[side]) posVal = posVal[side];
                else posVal = posVal[0];
                posVal = posVal.split(',');
                disx = Number(posVal[0]);
                disy = Number(posVal[1]);
                yalla.Debug.log(disx+":"+disy+'----side:'+side);
            }
            if (fromPos) fPos = { x: fromPos.x + disx, y: fromPos.y + disy };
            if (toPos) tPos = { x: toPos.x + disx, y: toPos.y + disy };
            
            // var template = new Laya.Templet();
            var template = Laya.Pool.getItemByClass("ludo_interActive_gift_templet", Laya.Templet);
            template.once(Laya.Event.COMPLETE, this, (template: Laya.Templet, isNextMsg: boolean, sendData: any, aniConf: GiftAniCnf, fromPos: any, toPos: any) => {
                yalla.Debug.log('--load sk complete--');
                var sk = template.buildArmature(0);
                var scale;
                if (aniConf && aniConf.scale) {
                    scale = aniConf.scale.split('|')[sendData.side];
                    scale = scale ? scale.split(',') : [1, 1];
                } else {
                    scale = [1, 1];
                }
                if(fromPos) sk.pos(fromPos.x, fromPos.y).scale(scale[0], scale[1]);

                if (toPos) {
                    sk.play('gift_play', false);
                    this.playSkLineAni(isNextMsg, sendData, aniConf, sk, toPos);
                } else {
                    this.playSkAnimation(isNextMsg, sendData, aniConf, sk);
                }
                this._parent && this._parent.addChild(sk);
                //TODO::1.4.3 不影响其他游戏逻辑
                if (Global.gameType == GameType.JACKARO) {
                    // this._parent.setChildIndex(sk, yalla.util.zOrderType.Z_InteractiveGift_Ani);
                } else {
                    sk.zOrder = 900;
                }
                if (!this._skList) this._skList = [];
                this._skList.push(sk);
            }, [template, isNextMsg, sendData, aniConf, fPos, tPos]);

            // template.once(Laya.Event.ERROR, this, (sendData: any, aniConf: GiftAniCnf, fromPos: any, toPos: any) => { 
                // console.log('互动表情动画资源加载失败，那继续下一条消息');
                // this.eventGiftIcon(sendData);
                // InteractiveGift.Instance.handleNextMsg();
            // }, [sendData, aniConf, fPos, tPos]);

            // var skUrl = "res/test/gift_" + Global.testGiftId + ".sk";
            var skUrl = "res/test/gift_" + sendData.giftId + ".sk";
            if (!yalla.util.IsBrowser()) skUrl = yalla.File.filePath + `gift_${sendData.giftId}.sk`;
            // if(sendData.giftId == 1009) var skUrl = "res/test/gift_1009.sk";
            yalla.Debug.log('-==========-skUrl:' + skUrl);
            template.loadAni(skUrl);
            if (!this._templateList) this._templateList = []; 
            this._templateList.push(template); 
        }

        private playSkLineAni(isNextMsg:boolean, sendData: any, aniConf: GiftAniCnf, sk: any, toPos: any): void {
            var isWake = yalla.Global.game_state != yalla.data.GameState.State_Sleep;
            if (isWake) {
                var timeLine1: Laya.TimeLine = Laya.Pool.getItemByClass("ludo_interActive_gift_timeLine", Laya.TimeLine);//new Laya.TimeLine();
                timeLine1.addLabel('line', 0).to(sk, { x: toPos.x, y: toPos.y }, 900, Laya.Ease.cubicOut);

                var timeLine2: Laya.TimeLine = Laya.Pool.getItemByClass("ludo_interActive_gift_timeLine", Laya.TimeLine);//new Laya.TimeLine();
                var big = aniConf ? aniConf.big : 1;
                timeLine2.addLabel("scale1", 0).to(sk, { scaleX: big, scaleY: big }, 360, Laya.Ease.cubicOut)
                    .addLabel("scale2", 0).to(sk, { scaleX: 1, scaleY: 1 }, 540, Laya.Ease.cubicOut)
            
                timeLine2.play(0);
                timeLine1.play(0);
                timeLine1.on(Laya.Event.COMPLETE, this, (timeLine1: Laya.TimeLine, timeLine2: Laya.TimeLine) => {
                    this.clearTimeLine(timeLine1);
                    this.clearTimeLine(timeLine2);
                }, [timeLine1, timeLine2]);
                if (!this._timeLineList) this._timeLineList = [];
                this._timeLineList.push(timeLine1);
                this._timeLineList.push(timeLine2);
                
                //TODO::有些动效要无缝衔接播放gift_first动效，如鸡蛋
                var playTime = 750;
                if (aniConf.time) playTime = aniConf.time;
                Laya.timer.once(playTime, this, (isNextMsg:boolean, sendData: any, aniConf: GiftAniCnf, sk: any, pos: any) => {
                    this.playSkAnimation(isNextMsg, sendData, aniConf, sk);
                }, [isNextMsg, sendData, aniConf, sk, toPos]);
            } else {
                this.playSkAnimation(isNextMsg, sendData, aniConf, sk);
            }
        }

        private playSkAnimation(isNextMsg:boolean, sendData: any, aniConf: GiftAniCnf, sk: any): void {
            var isWake = yalla.Global.game_state != yalla.data.GameState.State_Sleep;
            if (!isWake || !sk) {
                // this.eventGiftIcon(sendData);
                // if(isNextMsg) InteractiveGift.Instance.handleNextMsg();
                this.clearAni(sk);
            } else {
                var side = sendData.side;
                var giftId = sendData.giftId;
                yalla.Debug.log((yalla.File.cachePath + `/gift_${giftId}.mp3`)+'====互动礼物音效=='+yalla.Skin.instance.hasGiftSound(giftId) + '--' + yalla.File.existed(yalla.File.cachePath + `/gift_${giftId}.mp3`));
                if (yalla.Skin.instance.hasGiftSound(giftId))
                    yalla.Sound.playSkinSound(`gift_${giftId}`);

                sk.play('gift_first', false);
                sk.once(Laya.Event.STOPPED, this, (skSelf: any, sendData: any) => {
                    // this.eventGiftIcon(sendData);
                    // if(isNextMsg) InteractiveGift.Instance.handleNextMsg();
                    this.clearAni(skSelf);
                }, [sk, sendData]);
            }
        }

        public eventGiftIcon(sendData: any): void{
            this.event(this.Event.UpdateGiftIcon, [sendData]);
        }

        private clearTimeLine(timeLine:Laya.TimeLine): void{
            if (timeLine) {
                timeLine.pause();
                timeLine.offAll();
                timeLine.destroy();
            }
        }

        private clearAni(sk:Laya.Skeleton): void{
            if (sk) {
                sk.stop();
                sk.offAll();
                sk.destroy(true);
            }
        }

        public onBlur() {
            var i;
            if (this._timeLineList) {
                for (i = 0; i < this._timeLineList.length; i++){
                    this._timeLineList[i].pause();
                    this._timeLineList[i].offAll();
                    this._timeLineList[i].destroy()
                }  
            }
            if (this._skList) {
                for (i = 0; i < this._skList.length; i++){
                    this._skList[i].stop();
                    this._skList[i].offAll();
                    this._skList[i].destroy(true);
                }  
            }
            if (this._templateList) {
                for (i = 0; i < this._templateList.length; i++){
                    this._templateList[i].offAll();
                    this._templateList[i].destroy()
                }  
            }
            this._timeLineList = null;
            this._skList = null;
            this._templateList = null;
            Laya.Pool.clearBySign("ludo_interActive_gift_templet");
            Laya.Pool.clearBySign("ludo_interActive_gift_timeLine");
        }

        public clear() {
            this.onBlur();
        }
    }
}