module yalla.common {
    export class InteractiveGift extends ui.publics.chat.interactive_giftUI {
        private _side: number;                  //点击互动表情方向
        private _posPoint: Laya.Point;          //面板出现位置
        private _money: number;                 //玩家金币
        private _diamond: number;               //玩家钻石
        private _selectData: playerShowInfo;    //显示互动界面的玩家数据
        private _cellSkin: string;              //表情背景皮肤
        private _recIds: Array<any> = [];       //获得赠送表情的玩家id列表
        private _continueClick: number = 0;     //当玩家连续进行N次间隔小于0.5秒的单次的表情发送
        private _clickTime: number = 0;         //上次点击表情时间
        private _coolDownTime: number = 0;      //冷却结束时间
        private _thisParent: any;
        private _giftItemHash: any;
        public resData: any;                    //服务端配置{gameGiftCnf, limitCount, coolDownTime}
        public posHash: any;                    //{player_idx：{x,y}}
        public msgList: Array<any> = [];        //播放表情消息列表
        public giftDataList: Array<any> = [];   //已经排序的互动礼物列表

        public static _instance: InteractiveGift;
        private _coinUrl = "";
        private _diamondUrl = "";

        public static get Instance(): InteractiveGift {
            if (!this._instance) this._instance = new InteractiveGift();
            return this._instance;
        }
        public giftConf: Array<any> = [
            { id: 1009, icon: "res/test/giftIcon_1009.png", money: 102, diamond: 0, sort: 0, gameGiftType: 1 },
            { id: 1001, icon: "res/test/giftIcon_1001.png", money: 101, diamond: 0, sort: 0, gameGiftType: 1 },
            { id: 1002, icon: "res/test/giftIcon_1002.png", money: 102, diamond: 0, sort: 0, gameGiftType: 1 },
            { id: 1003, icon: "res/test/giftIcon_1003.png", money: 103, diamond: 0, sort: 0, gameGiftType: 1 },
            { id: 1004, icon: "res/test/giftIcon_1004.png", money: 101, diamond: 0, sort: 0, gameGiftType: 1 },
            { id: 1005, icon: "res/test/giftIcon_1005.png", money: 102, diamond: 0, sort: 0, gameGiftType: 1 },
            { id: 1006, icon: "res/test/giftIcon_1006.png", money: 103, diamond: 0, sort: 0, gameGiftType: 1 },
            { id: 1007, icon: "res/test/giftIcon_1007.png", money: 101, diamond: 0, sort: 0, gameGiftType: 1 },
            { id: 1008, icon: "res/test/giftIcon_1008.png", money: 102, diamond: 0, sort: 0, gameGiftType: 1 },
            { id: 1010, icon: "res/test/giftIcon_1010.png", money: 100, diamond: 0, sort: 0, gameGiftType: 1 },
            { id: 1011, icon: "res/test/giftIcon_1011.png", money: 50, diamond: 0, sort: 0, gameGiftType: 1 },
            { id: 1012, icon: "res/test/giftIcon_1012.png", money: 50, diamond: 0, sort: 0, gameGiftType: 1 },
            { id: 1017, icon: "res/test/giftIcon_1017.png", money: 50, diamond: 0, sort: 0, gameGiftType: 1 }
        ];

        public giftAniConf: Object = {
            "gameGifts": {
                "10019": {
                    "1001": { "frame": 24, "pos": "55,52|55,52|55,52|55,52|55,52|55,52|55,52|55,52", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1002": { "frame": 63, "pos": "52,45|52,45|52,45|52,45|52,45|52,45|52,45|52,45", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1003": { "frame": 50, "pos": "60,30|60,30|60,30|60,30|60,30|60,30|60,30|60,30", "scale": "1,1|1,1|1,1|1,1", "rotate": true, "big": 1.5 },
                    "1004": { "frame": 63, "pos": "54,28|54,28|54,28|54,28|54,28|54,28|54,28|54,28", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1005": { "frame": 24, "pos": "52,45|52,45|52,45|52,45|52,45|52,45|52,45|52,45", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1006": { "frame": 80, "pos": "112,50|112,50|0,50|0,50|112,50|112,50|0,50|0,50", "scale": "-1,1|-1,1|1,1|1,1", "big": 1.5 },
                    "1007": { "frame": 60, "pos": "52,38|52,38|52,38|52,38|52,38|52,38|52,38|52,38", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1008": { "frame": 50, "pos": "45,60|45,50|45,60|45,50|45,60|45,60|45,60|45,60", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1009": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,65|55,55|55,65", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1010": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1011": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1012": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5, time: 850 },
                    "1017": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5, time: 850 }
                },
                "10021": {
                    "1001": { "frame": 24, "pos": "50,44|44,42|44,42|44,42|50,44|44,42|44,42|44,42", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1002": { "frame": 63, "pos": "40,35|36,35|36,35|45,35|40,35|40,35|40,35|40,35", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1003": { "frame": 50, "pos": "50,18|45,15|40,10|45,15|50,18|50,18|50,18|50,18", "scale": "1,1|1,1|1,1|1,1", "rotate": true, "big": 1.5 },
                    "1004": { "frame": 63, "pos": "45,15|40,15|40,21|45,15|45,15|45,15|45,15|45,15", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1005": { "frame": 24, "pos": "40,35|36,35|36,35|45,35|40,35|40,35|40,35|40,35", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1006": { "frame": 80, "pos": "105,48|-10,40|-10,40|102,40|105,48|-10,40|-10,40|102,40", "scale": "-1,1|1,1|1,1|-1,1", "big": 1.5 },
                    "1007": { "frame": 60, "pos": "45,25|36,25|36,25|45,25|45,25|45,25|45,25|45,25", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1008": { "frame": 50, "pos": "50,55|45,48|45,42|45,48|50,55|45,48|45,42|45,42", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1009": { "frame": 50, "pos": "50,45|45,45|45,40|45,45|50,45|45,45|45,40|45,40", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1010": { "frame": 50, "pos": "50,45|45,42|45,42|45,42|50,45|45,42|45,42|45,42", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1011": { "frame": 50, "pos": "50,45|45,42|45,42|45,42|50,45|45,42|45,42|45,42", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1012": { "frame": 50, "pos": "50,45|45,42|45,42|45,42|50,45|45,42|45,42|45,42", "scale": "1,1|1,1|1,1|1,1", "big": 1.5, time: 850 },
                    "1017": { "frame": 50, "pos": "50,45|45,42|45,42|45,42|50,45|45,42|45,42|45,42", "scale": "1,1|1,1|1,1|1,1", "big": 1.5, time: 850 }
                },
                "10020": {
                    "1001": { "frame": 24, "pos": "55,52|55,52|55,52|55,52|55,52|55,52|55,52|55,52", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1002": { "frame": 63, "pos": "52,45|52,45|52,45|52,45|52,45|52,45|52,45|52,45", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1003": { "frame": 50, "pos": "60,30|60,30|60,30|60,30|60,30|60,30|60,30|60,30", "scale": "1,1|1,1|1,1|1,1", "rotate": true, "big": 1.5 },
                    "1004": { "frame": 63, "pos": "54,28|54,28|54,28|54,28|54,28|54,28|54,28|54,28", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1005": { "frame": 24, "pos": "52,45|52,45|52,45|52,45|52,45|52,45|52,45|52,45", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1006": { "frame": 80, "pos": "112,50|112,50|0,50|0,50|112,50|112,50|0,50|0,50", "scale": "-1,1|-1,1|1,1|1,1", "big": 1.5 },
                    "1007": { "frame": 60, "pos": "52,38|52,38|52,38|52,38|52,38|52,38|52,38|52,38", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1008": { "frame": 50, "pos": "45,60|45,50|45,60|45,50|45,60|45,60|45,60|45,60", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1009": { "frame": 50, "pos": "55,50|55,55|55,55|55,55|55,50|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1010": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1011": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1012": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5, time: 850 },
                    "1017": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5, time: 850 }
                },
                "10023": {
                    "1001": { "frame": 24, "pos": "55,52|55,52|55,52|55,52|55,52|55,52|55,52|55,52", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1002": { "frame": 63, "pos": "52,45|52,45|52,45|52,45|52,45|52,45|52,45|52,45", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1003": { "frame": 50, "pos": "60,30|60,30|60,30|60,30|60,30|60,30|60,30|60,30", "scale": "1,1|1,1|1,1|1,1", "rotate": true, "big": 1.5 },
                    "1004": { "frame": 63, "pos": "54,28|54,28|54,28|54,28|54,28|54,28|54,28|54,28", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1005": { "frame": 24, "pos": "52,45|52,45|52,45|52,45|52,45|52,45|52,45|52,45", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1006": { "frame": 80, "pos": "105,50|-10,50|-10,50|102,50|105,50|-10,50|-10,50|102,50", "scale": "-1,1|1,1|1,1|-1,1", "big": 1.5 },
                    "1007": { "frame": 60, "pos": "52,38|52,38|52,38|52,38|52,38|52,38|52,38|52,38", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1008": { "frame": 50, "pos": "45,60|45,50|45,60|45,50|45,60|45,60|45,60|45,60", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1009": { "frame": 50, "pos": "55,50|55,55|55,55|55,55|55,50|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1010": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1011": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5 },
                    "1012": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5, time: 850 },
                    "1014": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5, "time": 850 },
                    "1015": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5, "time": 850 },
                    "1016": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5, "time": 850 },
                    "1017": { "frame": 50, "pos": "55,55|55,55|55,55|55,55|55,55|55,55|55,55|55,55", "scale": "1,1|1,1|1,1|1,1", "big": 1.5, time: 850 }
                },
                "skins": {
                    "1001": { "sk": "gift_1001.sk", "images": ["gift_1001.png", "giftIcon_1001.png"], "sound": "gift_1001" },
                    "1002": { "sk": "gift_1002.sk", "images": ["gift_1002.png", "giftIcon_1002.png"], "sound": "gift_1002" },
                    "1003": { "sk": "gift_1003.sk", "images": ["gift_1003.png", "giftIcon_1003.png"], "sound": "gift_1003" },
                    "1004": { "sk": "gift_1004.sk", "images": ["gift_1004.png", "giftIcon_1004.png"], "sound": "gift_1004" },
                    "1005": { "sk": "gift_1005.sk", "images": ["gift_1005.png", "giftIcon_1005.png"], "sound": "gift_1005" },
                    "1006": { "sk": "gift_1006.sk", "images": ["gift_1006.png", "giftIcon_1006.png"], "sound": "gift_1006" },
                    "1007": { "sk": "gift_1007.sk", "images": ["gift_1007.png", "giftIcon_1007.png"], "sound": "gift_1007" },
                    "1008": { "sk": "gift_1008.sk", "images": ["gift_1008.png", "giftIcon_1008.png"], "sound": null },
                    "1009": { "sk": "gift_1009.sk", "images": ["gift_1009.png", "giftIcon_1009.png"], "sound": "gift_1009" },
                    "1010": { "sk": "gift_1010.sk", "images": ["gift_1010.png", "giftIcon_1010.png"], "sound": "gift_1010" },
                    "1011": { "sk": "gift_1011.sk", "images": ["gift_1011.png", "giftIcon_1011.png"], "sound": "gift_1011" },
                    "1012": { "sk": "gift_1012.sk", "images": ["gift_1012.png", "giftIcon_1012.png"], "sound": "gift_1012" },
                    "1017": { "sk": "gift_1017.sk", "images": ["gift_1017.png", "giftIcon_1017.png"], "sound": "gift_1017" },
                },
            }
        }

        constructor() {
            super();
            yalla.Debug.log("互礼物init=gameType：" + yalla.Global.gameType);
            this.initUI();
            this.initEvent();
        }

        private initUI(): void {
            this.visible = false;
            this.checkBox_all.visible = false;
            var gameType = yalla.Global.gameType;
            var urlFunc = yalla.getGame;
            if (gameType == GameType.DOMINO) {
                urlFunc = yalla.getDomino;
            } else if (gameType == GameType.SNAKEANDLADER) {
                urlFunc = yalla.getSnake;
            } else if (gameType == GameType.JACKARO) {
                urlFunc = yalla.getJackaro;
            }
            this.bg.skin = urlFunc('interactive_bg');
            this.coinImg.skin = urlFunc('coin');
            this.diamondImg.skin = urlFunc('Diamonds');
            this.img_arrow.skin = urlFunc('BG_Expression_V');
            this.checkBox_all.skin = urlFunc('interactive_select');
            this._cellSkin = urlFunc('interactive_btn');
            this.giftList.hScrollBarSkin = urlFunc('scroll');
            this._coinUrl = this.coinImg.skin;
            this._diamondUrl = this.diamondImg.skin;

            if (this.giftList && this.giftList.scrollBar) {
                this.giftList.scrollBar.isVertical = false;
                this.giftList.scrollBar.elasticBackTime = 300;
                this.giftList.scrollBar.elasticDistance = 200;
            }
        }

        private _cellX: number = 0;
        // private _isMouseOut: boolean = false;
        private initEvent(): void {
            this.on(Laya.Event.MOUSE_DOWN, this, (e: Laya.Event) => {
                e.stopPropagation();
            });
            this.on(Laya.Event.CLICK, this, (e: Laya.Event) => {
                e.stopPropagation();
            });
            // this.giftList.on(Laya.Event.CLICK, this, this.onClickSendGift);

            this.giftList['mouseEventFunc'] = (e: Laya.Event, index: number) => {
                if (e.type == 'mousedown') {
                    Laya.timer.clear(this, this.elasticOver);
                    this.giftList.scrollBar['isMouseOut'] = false;
                }
                if (e.type == 'mouseout' && !this.giftList.scrollBar['isMouseOut']) {
                    Laya.timer.once(360, this, this.elasticOver);
                    this.giftList.scrollBar['isMouseOut'] = true;
                }
            }
            // this.on(Laya.Event.MOUSE_DOWN, this, this.onmousedown);
            // this.on(Laya.Event.MOUSE_OUT, this, (e: Event) => {
            //     if (this.giftList && this.giftList.scrollBar) {
            //         this.giftList['_isMoved'] = false;
            //         var maxValue = this.giftList.scrollBar.max;
            //         this.giftList.scrollBar.value = Math.min(this._cellX, maxValue);
            //         this.giftList['_isScrollBarChange'] = false;
            //     }
            // })
        }
        private elasticOver(): void {
            if (Laya.stage.mouseX >= Laya.stage.width && this.giftList.scrollBar) {
                this.giftList.scrollBar['onStageMouseUp2'](null);
            }
        }

        // private onmousedown(e: Event): void{
        //     this._cellX = 0;
        //     var cell = e.target;
        //     this.giftList['_isScrollBarChange'] = true;
        //     if (cell instanceof Laya.Button) {
        //         if (cell.name.indexOf('giftItem_') > -1) {
        //             this._cellX = cell.x;
        //         }
        //     }
        // }

        public checkError(code: number): void {
            var msg = '';
            if (code == yalla.data.ErrorCode.ACCOUNT_BE_FROZEN) {
                msg = yalla.data.TranslationD.Game_Error_ACCOUNT_BE_FROZEN;
                if (yalla.Font.lan == 'ar') msg = 'تم تجميد حسابك';
            }
            else if (code == yalla.data.ErrorCode.NO_MONEY) {
                msg = "Send failed. You don't have enough golds/diamonds.";
                if (yalla.Font.lan == 'ar') msg = 'فشل الإرسال. ليس لديك ما يكفي من القطع الذهبية/ الماس.';
            }

            yalla.Native.instance.showToast({ msg: msg });
            this.hideGiftView();
        }

        public pushMsg(msg: any): void {
            if (!this.msgList) this.msgList = [];
            this.msgList.push(msg);
        }

        /**
         * 接受到互动表情消息
         *{ senderIdx, recIds, giftId}
         * @param msg 
         */
        public handleRevGift(msgData: any = null): void {
            yalla.Debug.log("=====收到互动礼物====");
            yalla.Debug.log(msgData);
            if (!this.posHash) this.posHash = {};
            var gameCnfGifts = yalla.Skin.instance.gameCnfGifts;
            if (!gameCnfGifts || !gameCnfGifts[yalla.Global.gameType])
                gameCnfGifts = this.giftAniConf['gameGifts'][yalla.Global.gameType];
            else gameCnfGifts = gameCnfGifts[yalla.Global.gameType];

            if (!msgData) return;
            // msgData.giftId = yalla.Global.testGiftId;
            var [senderIdx, myIdx] = [msgData.senderIdx, yalla.Global.Account.idx];
            var fromPos = this.posHash['player_' + senderIdx];
            var aniConf = gameCnfGifts[msgData.giftId];

            if (!aniConf) {//TODO::1.4.3 如果h5 礼物配置没有，回阻塞礼物队列
                yalla.Debug.log("===礼物没有配置：" + msgData.giftId);
                this.handleNextMsg();
                this.eventSender(msgData);
                return;
            }

            var isNextMsg: boolean = false;
            this.timeNextMsg(msgData, aniConf);
            if (msgData.rcvIdx) {//自己赠送礼物的协议
                var recIds: Array<any> = msgData.rcvIdx;
                var [playerNums, index] = [recIds.length, 0];
                recIds.forEach(idx => {
                    index += 1;
                    if (index >= playerNums) isNextMsg = true;
                    var toPos;
                    if (idx != senderIdx) {
                        toPos = this.posHash['player_' + idx];
                    }
                    if (idx != senderIdx && idx == yalla.Global.Account.idx) {
                        /**发送礼物不是自己，且接受方是自己，需要判定发送的玩家是否被自己举报 */
                        if (yalla.Mute.muted(senderIdx)) return;
                    }
                    var giftItemData: GameGiftCnf = this.getGiftItemData(msgData.giftId);
                    if (giftItemData) {
                        var sendData = { giftId: msgData.giftId, icon: giftItemData.icon, senderIdx: msgData.senderIdx, recIdx: idx, side: this.side(idx) };
                        InteractiveGiftAnimation.Instance.playAni(isNextMsg, sendData, aniConf, this._thisParent, fromPos, toPos);
                    }
                })
            } else {
                isNextMsg = true;
                var giftItemData: GameGiftCnf = this.getGiftItemData(msgData.giftId);
                if (giftItemData) {
                    var sendData = { giftId: msgData.giftId, icon: giftItemData.icon, senderIdx: msgData.senderIdx, recIdx: myIdx, side: this.side(myIdx) }
                    if (senderIdx == myIdx) {
                        InteractiveGiftAnimation.Instance.playAni(isNextMsg, sendData, aniConf, this._thisParent, fromPos);
                    } else {
                        InteractiveGiftAnimation.Instance.playAni(isNextMsg, sendData, aniConf, this._thisParent, fromPos, this.posHash['player_' + myIdx]);
                    }
                }
            }
            yalla.Native.instance.event(yalla.Native.instance.Event.UPDATEMONEY);
        }

        public eventSender(msgData: any): void {
            var giftItemData: GameGiftCnf = this.getGiftItemData(msgData.giftId);
            var [senderIdx, myIdx] = [msgData.senderIdx, yalla.Global.Account.idx];
            if (msgData.rcvIdx) {
                var recIds: Array<any> = msgData.rcvIdx;
                recIds.forEach(idx => {
                    if (idx != senderIdx && idx == myIdx) {
                        /**发送礼物不是自己，且接受方是自己，需要判定发送的玩家是否被自己举报 */
                        if (yalla.Mute.muted(senderIdx)) return;
                    }
                    var sendData = { giftId: msgData.giftId, icon: giftItemData ? giftItemData.icon : '', senderIdx: senderIdx, recIdx: idx, side: this.side(idx) };
                    InteractiveGiftAnimation.Instance.eventGiftIcon(sendData);
                });
            } else {//目前都有rcvIdx返回，else逻辑暂不处理
                var sendData = { giftId: msgData.giftId, icon: giftItemData ? giftItemData.icon : '', senderIdx: senderIdx, recIdx: myIdx, side: this.side(myIdx) };
                InteractiveGiftAnimation.Instance.eventGiftIcon(sendData);
            }
        }

        public timeNextMsg(msgData: any, aniConf: GiftAniCnf): void {
            var isWake = yalla.Global.game_state != yalla.data.GameState.State_Sleep;
            if (isWake && aniConf) {
                var time = Math.ceil(aniConf.frame / 30 * 1000) + 700;
                Laya.timer.once(time, this, (msgData: any) => {
                    this.handleNextMsg();
                    this.eventSender(msgData)
                }, [msgData]);
            } else {
                this.handleNextMsg();
                this.eventSender(msgData)
            }
        }

        public handleNextMsg(): void {
            yalla.Debug.log(this.msgList);
            if (this.msgList.length > 0) this.msgList.shift();
            this.handleRevGift(this.msgList[0]);
        }

        /**
         * @param res {gameGiftCnf, limitCount, coolDownTime}
         * @param posHash {idx:{x,y}}  821134
         * @param gameCb 外部Game 打开关闭时需要触发的gameCb
         */
        private gameCb = null;
        public handleUpdateGiftCnf(res: any, posHash: any, par: any, gameCb = null): void {
            this.setParentFunc(par);
            this.posHash = posHash;
            this.gameCb = gameCb;
            this.msgList = [];
            yalla.Debug.log(res);
            var giftDataList = yalla.util.sortListAsc(res.gameGiftCnf, 'sort');
            var isSame = this.checkIsSameGiftDataList(giftDataList);
            yalla.Debug.log("===互动礼物列表====" + isSame);
            if (isSame) {
                return;
            }
            this.giftDataList = giftDataList;
            // var giftDataList = this.giftConf;

            if (!this._giftItemHash) this._giftItemHash = {};
            var len = giftDataList.length;
            var maxValue = Math.max((Math.ceil(len / 2) * 120 - 480), 0);
            this.giftList.repeatX = len <= 4 ? len : Math.ceil(res.gameGiftCnf.length / 2);
            this.giftList.repeatY = len <= 4 ? 1 : 2;
            this.giftList.array = giftDataList;
            this.giftList.renderHandler = new Laya.Handler(this, this.renderGift);
            this.giftList.scrollBar && (this.giftList.scrollBar.max = maxValue);
            this.giftList.on(/*laya.events.Event.MOUSE_UP*/"mouseup", this, this.onGiftListMouseUp);
            // if (maxValue > 0) this.giftList.scrollBar.value = Math.max(0, maxValue - 60);

            var playerNums = 0;
            if (yalla.Global.gameType == GameType.LUDO) {
                playerNums = ludoRoom.instance.sitPlayers.length
            } else if (yalla.Global.gameType == GameType.DOMINO) {
                playerNums = yalla.data.RoomService.instance.room.playerNums
            } else if (yalla.Global.gameType == GameType.JACKARO) {
                playerNums = yalla.data.jackaro.JackaroUserService.instance.room.roomMaxNum;
            }
            this.checkBox_all.visible = playerNums > 2;
        }

        private renderGift(cell: any, index: number): void {
            if (this.resData && this.resData.gameGiftCnf) {
                var data: GameGiftCnf = this.giftDataList[index];//this.resData.gameGiftCnf[index];
                if (!data) return;
                var giftItem = this._giftItemHash[data.id];
                if (!giftItem) {
                    var moneyUrl = data.diamond > 0 ? this._diamondUrl : this._coinUrl;
                    giftItem = new InteractiveItem(cell);
                    giftItem.cellSkin = this._cellSkin;
                    giftItem.costSkin = moneyUrl;
                    giftItem.clickSendGift = this.onClickSendGift.bind(this);
                    giftItem.initData(data);
                    this._giftItemHash[data.id] = giftItem;
                }
                // cell.cellSkin = this._cellSkin;
                // cell.costSkin = moneyUrl;
                // cell.initData(data);
                // // var data: GameGiftCnf = this.giftConf[index];
                // var moneyUrl = data.diamond > 0 ? this._diamondUrl : this._coinUrl;
                // (cell.getChildByName("needCoinImg") as Laya.Image).skin = moneyUrl;
                // var giftIcon: Laya.Image = cell.getChildByName("img_icon") as Laya.Image;
                // var coinTxt: Laya.Label = cell.getChildByName("txt_coin") as Laya.Label;
                // cell.skin = this._cellSkin;
                // coinTxt.text = data.money ? String(data.money):String(data.diamond);
                // var giftId = data.id;
                // var path = `${yalla.File.cachePath}/giftIcon_${giftId}`;
                // if (yalla.File.existed(`${path}.png`)) {
                //     giftIcon.skin = yalla.File.filePath + `giftIcon_${giftId}.png`;
                // } else {
                //     giftIcon.skin = yalla.getFileDomainType_flexible(data.icon);
                // }
                // // console.log(data.icon + "====互动礼物skin===" + giftIcon.skin + "===path:"+path);
                // cell.name = 'giftItem_' + giftId;
            }
        }

        public setSide(side: number): void {
            this._side = side;
            if (yalla.Global.gameType == GameType.LUDO) {
                var dis = Laya.stage.height - 1334;
                if (dis > 0) dis = 120;
                else dis = 115 * 2;
                switch (side) {
                    case 0:
                        this.img_arrow.pos(3, 45 + dis + 55).rotation = 90;
                        this.x = this._posPoint.x + 115;
                        this.y = this._posPoint.y - dis - 65;
                        break;
                    case 1:
                        this.img_arrow.pos(3, 170).rotation = 90;
                        this.x = this._posPoint.x + 115;
                        this.y = this._posPoint.y - 115;
                        break;
                    case 2:
                        this.img_arrow.pos(522, 170).rotation = 270;
                        this.x = this._posPoint.x - this.width - 20;
                        this.y = this._posPoint.y - 115;
                        break;
                    case 3:
                        this.img_arrow.pos(522, 55 + dis + 55).rotation = 270;
                        this.x = this._posPoint.x - this.width - 20;
                        this.y = this._posPoint.y - dis - 65;
                        break;
                }
            } else if (yalla.Global.gameType == GameType.DOMINO) {
                switch (side) {
                    case 0:
                        this.img_arrow.pos(3, 170).rotation = 90;
                        this.x = this._posPoint.x + 105;
                        this.y = this._posPoint.y - 145;
                        break;
                    case 1:
                        this.img_arrow.pos(522, 170).rotation = 270;
                        this.x = this._posPoint.x - this.width - 20;
                        this.y = this._posPoint.y - 110;
                        break;
                    case 2:
                        this.img_arrow.pos(522, 145).rotation = 270;
                        this.x = this._posPoint.x - this.width - 20;
                        this.y = this._posPoint.y - 90;
                        break;
                    case 3:
                        this.img_arrow.pos(3, 170).rotation = 90;
                        this.x = this._posPoint.x + 95;
                        this.y = this._posPoint.y - 150;
                        break;
                }
            } else if (yalla.Global.gameType == GameType.SNAKEANDLADER) {
                var dis = Laya.stage.height - 1334;
                if (dis > 0) dis = 120;
                else dis = 115 * 2;
                switch (side) {
                    case 0:
                        this.img_arrow.pos(3, 45 + dis + 55).rotation = 90;
                        this.x = this._posPoint.x + 125;
                        this.y = this._posPoint.y - dis - 65;
                        break;
                    case 3:
                        this.img_arrow.pos(522, 45 + dis + 55).rotation = 270;
                        this.x = this._posPoint.x - this.width - 10;
                        this.y = this._posPoint.y - dis - 65;
                        break;
                }
            } else if (yalla.Global.gameType == GameType.JACKARO) {
                switch (side) {
                    case 0:
                        this.img_arrow.pos(3, 105).rotation = 90;
                        this.x = this._posPoint.x + 120;
                        this.y = this._posPoint.y - 100;
                        break;
                    case 1:
                        this.img_arrow.pos(522, 135).rotation = 270;
                        this.x = this._posPoint.x - this.width - 30;
                        this.y = this._posPoint.y - 100;
                        break;
                    case 2:
                        this.img_arrow.pos(522, 135).rotation = 270;
                        this.x = this._posPoint.x - this.width - 30;
                        this.y = this._posPoint.y - 90;
                        break;
                    case 3:
                        this.img_arrow.pos(3, 95).rotation = 90;
                        this.x = this._posPoint.x + 120;
                        this.y = this._posPoint.y - 90;
                        break;
                }
            }
        }

        public updateMoney(money: number, diamond: number): void {
            this._money = money;
            this._diamond = diamond;
            this.txt_money.text = yalla.util.filterNum(money);
            this.txt_diamond.text = yalla.util.filterNum(diamond);
        }

        public setParentFunc(pra): void {
            this._thisParent = pra;
        }

        public showGiftView(side: number, posPoint: Laya.Point, data: playerShowInfo = null): void {
            if (this.visible && this._posPoint == posPoint) return;
            if (this.giftList.scrollBar && this.giftList.scrollBar.target) this.giftList.scrollBar.target.mouseEnabled = true;
            this._selectData = data;
            this._posPoint = posPoint;
            this.visible = true;
            this.gameCb && this.gameCb(true);
            yalla.util.updateBackPressed(2, 1);
            this.checkBox_all.selected = true;
            var gameType = yalla.Global.gameType;
            // yalla.Debug.log("=====互动礼物面板 金币更新====gameType=" + gameType);
            switch (gameType) {
                case GameType.LUDO:
                    this.updateMoney(ludo.Global.myMoney, ludo.Global.myGold);
                    break;
                case GameType.DOMINO:
                    this.updateMoney(yalla.data.UserService.instance.user.money, yalla.data.UserService.instance.user.gold);
                    break;
                case GameType.SNAKEANDLADER:
                    var user = yalla.data.snake.UserService.instance.user;
                    user && this.updateMoney(user.money, user.gold);
                    break;
                case GameType.JACKARO:
                    let jackaroUser = yalla.data.jackaro.JackaroUserService.instance.user;
                    jackaroUser && this.updateMoney(jackaroUser.gold, jackaroUser.diamond);
                    break;
            }
            this.setSide(side);

            yalla.util.clogDBAmsg('10077', JSON.stringify({ gameId: yalla.Global.Account.gameId }));
            this.refreshGiftListState();
            yalla.util.ViewExclusion.instance.add(yalla.util.ViewType.InteractiveGift, this.hideGiftView.bind(this));
        }
        private refreshGiftListState() {
            this.resetItemState();
            if (this.giftList) {
                this.giftList.refresh();
            }
        }
        private onGiftListMouseUp() {
            yalla.Debug.log('onGiftListMouseUp');
            this.resetItemState();
            // this.refreshGiftListState();
        }

        public hideGiftView(): void {
            if (!this.displayedInStage) return;
            Laya.timer.clear(this, this.elasticOver);
            this.visible = false;
            this.gameCb && this.gameCb(false);
            yalla.util.updateBackPressed(2);
            this._continueClick = 0;
            this.giftList.scrollBar && (this.giftList.scrollBar.value = 0);
            yalla.util.ViewExclusion.instance.remove(yalla.util.ViewType.InteractiveGift)
        }

        // private onClickSendGift(e: Laya.Event): void {
        private onClickSendGift(item: InteractiveItem): void {
            var isMoved = this.giftList['_isMoved'];
            yalla.Debug.log('=====sendGift=_isMoved===' + isMoved);
            if (isMoved) return;
            if (this.resData && this.resData.gameGiftCnf) {
                var gameGiftCnf = this.resData.gameGiftCnf;
                // if (e.target instanceof Laya.Button) {
                // if (e.target && e.target.dataSource) {
                if (item) {
                    this._recIds = this.recIds;
                    // var index = e.target.parent.getChildIndex(e.target);
                    // var giftData: GameGiftCnf = gameGiftCnf[index];
                    var giftData: GameGiftCnf = item.data;
                    // yalla.Debug.log(this._money + "=:this._money===send 互动礼物 0====index:");
                    yalla.Debug.log(giftData);
                    if (giftData) {
                        var price = giftData.money;
                        var myMoney = this._money;
                        if (giftData.diamond > 0) {
                            price = giftData.diamond;
                            myMoney = this._diamond;
                        }
                        var needMoney = price * this._recIds.length;
                        if (needMoney > myMoney) {
                            this.checkError(yalla.data.ErrorCode.NO_MONEY);
                            return;
                        }
                        var gameType = yalla.Global.gameType;
                        yalla.Debug.log("===send 互动礼物 1===giftData.id：" + giftData.id);
                        switch (gameType) {
                            case GameType.LUDO:
                                ludo.GameSocket.instance.GameGiftSendRequest(giftData.id, this._recIds);
                                break;
                            case GameType.DOMINO:
                                yalla.data.RoomService.instance.sendGift(giftData.id, this._recIds);
                                break;
                            case GameType.SNAKEANDLADER:
                                yalla.data.snake.UserService.instance.sendGift(giftData.id, this._recIds)
                                break;
                            case GameType.JACKARO:
                                yalla.data.jackaro.JackaroUserService.instance.sendGift(giftData.id, this._recIds)
                                break;

                        }

                        this.hideGiftView();
                        //埋点
                        var rValue = { sendIdx: Global.Account.idx, rcvIdx: this._recIds, roomid: Global.Account.roomid, giftId: giftData.id, giftType: giftData.gameGiftType };
                        yalla.util.clogDBAmsg('10114', JSON.stringify(rValue));
                    }
                    // }
                }
            }

            // var nowTime = new Date().getTime();
            // if (nowTime <= this._coolDownTime) {
            //     yalla.Native.instance.showToast({ msg: "Operate too frequently, please try again later." });
            //     this.hideGiftView();
            //     return;
            // }
            // if (nowTime - this._clickTime <= 500) {
            //     this._continueClick += 1;
            //     yalla.Debug.log('------this._continueClick -'+this._continueClick );
            //     if (this.resData && this._continueClick >= this.resData.limitCount) this._coolDownTime = nowTime + this.resData.coolDownTime;
            // }
            // if (yalla.Global.gameType == GameType.LUDO) {
            //     ludo.GameSocket.instance.GameGiftSendRequest(gameGiftCnf[index].id, this._recIds);
            // } else {
            //     yalla.data.RoomService.instance.sendGift(gameGiftCnf[index].id, this._recIds);
            // }
            // // this._clickTime = nowTime;
            // // this._continueClick = 0;
            // this.hideGiftView();

            //------------test------------
            // var key = 'player_';
            // var fromPos, toPos;
            // fromPos = this.posHash[key+yalla.Global.Account.idx];
            // if (this._selectData.fPlayerInfo.idx != yalla.Global.Account.idx) {
            //     toPos = this.posHash[key+this._selectData.fPlayerInfo.idx];
            // }
            // console.log(fromPos, toPos,'----onClickSendGift----',this.posHash);
            // var aniConf = this.giftAniConf['gameGifts'][yalla.Global.gameType][giftData.id];
            // this._recIds.forEach(idx => {
            //     var sendData = { giftId: giftData.id, senderIdx: yalla.Global.Account.idx, recIdx: idx, side: this.side(idx) };
            //     InteractiveGiftAnimation.Instance.playAni(sendData, aniConf, this.parent, fromPos, toPos);
            // })
        }

        private get recIds(): Array<any> {
            var recIds: Array<any> = [];
            var myIdx = yalla.Global.Account.idx;
            if (this.checkBox_all.selected) {//只发送给点出互动表情位置的玩家
                recIds.push(this._selectData.fPlayerInfo.idx);
            } else {
                if (yalla.Global.gameType == GameType.LUDO) {
                    ludoRoom.instance.sitPlayers.forEach((info: playerShowInfo, index: number) => {
                        if (info.fPlayerInfo.idx != myIdx)
                            recIds.push(info.fPlayerInfo.idx);
                    });
                } else if (yalla.Global.gameType == GameType.JACKARO) {
                    var playerPool = JackaroGamePlayerManager.instance.playerPool;
                    for (var k in playerPool) {
                        if (myIdx != playerPool[k].idx) recIds.push(playerPool[k].idx);
                    }
                } else if (yalla.Global.gameType == GameType.DOMINO) {
                    yalla.data.RoomService.instance.room.player.player.forEach((info: playerShowInfo, index: number, arr: Array<playerShowInfo>) => {
                        if (info.fPlayerInfo.idx != myIdx) recIds.push(info.fPlayerInfo.idx);
                    });
                }
            }
            return recIds;
        }
        private side(idx: number): number {
            var side = 0;
            var gameType = yalla.Global.gameType;
            if (gameType == GameType.LUDO || gameType == GameType.SNAKEANDLADER) {
                side = ludoRoom.instance.sitSideHash[idx];
            } else if (gameType == GameType.DOMINO) {
                side = yalla.data.RoomService.instance.room.getSideByIdx(idx);
            } else if (gameType == GameType.JACKARO) {
                side = JackaroGamePlayerManager.instance.getSideByIdx(idx);
            }
            return side;
        }

        /**
         * TODO::引擎按钮有点坑，划过有概率出现被选中效果，，现做防护处理
         */
        private resetItemState(): void {
            if (!this.giftList || !this.giftList.array) return;
            var len = this.giftList.array.length;
            for (var i = 0; i < len; i++) {
                var btn: Laya.Button = this.giftList.getCell(i) as Laya.Button;
                if (btn) {
                    btn.selected = false;
                    btn['state'] = 0;
                }
            }
        }

        public getGiftItemData(giftId: number): GameGiftCnf {
            var d: GameGiftCnf;
            if (this.resData) {
                this.resData.gameGiftCnf.forEach((value: GameGiftCnf, index: number, arr: Array<GameGiftCnf>) => {
                    // this.giftConf.forEach((value: GameGiftCnf, index: number, arr: Array<GameGiftCnf>) => {
                    if (value.id == giftId) {
                        d = value;
                        if (d) d.icon = yalla.DomainUtil.instance.getFileDomainType_flexible(d.icon);
                        return true;
                    }
                });
            }
            return d;
        }

        /**1.4.5 考虑断线重连，反复请求这个接口，如果列表数据没有变化，则不刷新（因ios 偶现列表有钻石表情未显示） */
        public checkIsSameGiftDataList(giftDataList: Array<any>): boolean{
            var len = giftDataList ? giftDataList.length : 0;
            if (!this.giftDataList || len < 1 || this.giftDataList.length != len) return false;

            for (var i = 0; i < len; i++){
                if (this.giftDataList[i] && this.giftDataList[i].id != giftDataList[i].id) {
                    return false;
                }
            }
            return true;
        }

        public onBlur() {
            InteractiveGiftAnimation.Instance.onBlur();
        }

        private removeEvent(): void {
            this.bg.offAll();
            // this.giftList.off(Laya.Event.CLICK, this, this.onClickSendGift);
            this.offAll();
            Laya.timer.clearAll(this);
        }

        public clear(): void {
            this.msgList = null;
            this.giftDataList = null;
            InteractiveGiftAnimation.Instance.clear();
            this.gameCb = null;
            this.removeEvent();
            this.removeSelf();
            this.destroy(true);
        }
    }

    /**
     * 礼物类型
     */
    export class GameGiftCnf {
        public id;
        public icon;
        public money;
        public diamond;
        public sort;
        public gameGiftType;
    }

    export class GiftAniCnf {
        public frame;   //动画总帧数
        public pos;     //位置
        public scale;   //1 or -1
        public sdPos;   //图标位置
        public big;     //动效中最大缩放值
        public time;    //直线运动时间
        public FirstP   //直线运动是否是spine的第一帧开始
    }
}