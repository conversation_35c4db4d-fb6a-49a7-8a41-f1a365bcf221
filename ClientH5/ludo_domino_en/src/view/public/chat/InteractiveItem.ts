module yalla.common {
    export class InteractiveItem {
        private ui: ui.publics.chat.interactiveItemUI;
        private _cellSkin = "";
        private _costUrl = "";
        private _clickFunc: Function = null;
        public data;

        constructor(ui: ui.publics.chat.interactiveItemUI) {
            this.ui = ui;
            ui.on(Laya.Event.CLICK, this, this.onClickSendGift);
        }
        public initData(data): void {
            this.data = data;
            this.ui.txt_coin.text = data.money ? String(data.money) : String(data.diamond);
            var giftId = data.id;
            var path = `${yalla.File.cachePath}/giftIcon_${giftId}`;
            if (yalla.File.existed(`${path}.png`)) {
                this.ui.img_icon.skin = yalla.File.filePath + `giftIcon_${giftId}.png`;
            } else {
                this.ui.img_icon.skin = yalla.DomainUtil.instance.getFileDomainType_flexible(data.icon);
            }
            this.ui.name = 'giftItem_' + giftId;
        }

        private onClickSendGift(e) {
            console.log("===选择礼物=====");
            this._clickFunc && this._clickFunc(this);
        }

        set cellSkin(v) {
            this.ui.skin = v;
        }

        set costSkin(v) {
            this.ui.needCoinImg.skin = v;
        }

        set clickSendGift(v) {
            this._clickFunc = v;
        }

        public clear(): void {
            this.ui.offAll();
            this.ui.destroy(true);
        }
    }
}