module yalla.common {
    /*
    * name;
    */
    export class Listview extends Laya.Box {
        private _container: Laya.Box = null;//容器
        private _isDown: boolean = false;//按下
        private _startPos: Laya.Point = new Laya.Point(0, 0);//起始触摸点
        private _isTweening: boolean = false;//边界缓动
        private _tweenTime: number = 200;//缓动时间
        private _isBoundary: boolean = false;//底部边界
        private _isAutoJump: boolean = false;//自动跳转到最后一项
        private _isBounceEnabled: boolean = true;//弹回动作
        private _space:number = 0;
        private _maxLen: number = 15;
        private _items: Array<any> = [];//生成的15条Item对象列表
        private _allItemDataList: Array<any> = [];//所有聊天信息
        private _chatIndex: number = 0;
        public wid: number = 500;//默认宽高
        public hei: number = 400;
        public maxHeight: number = 0;
        public minHeight: number = 0;

        constructor(wid: number = 500, hei: number = 400) {
            super();
            this.init();
            this.handleMouseEvent();
            this.setContentSize(wid, hei);
        }
        private handleMouseEvent(): void {
            this._container.on(Laya.Event.MOUSE_DOWN, this, (event) => {
                if (!this._isTweening) {
                    this._isDown = true;
                    var target = event.currentTarget;
                    this._startPos = new Laya.Point(target.mouseX, target.mouseY);
                }
            });
            this._container.on(Laya.Event.MOUSE_MOVE, this, (event) => {
                if (this._isDown) {
                    var target = event.currentTarget;
                    var moveP: Laya.Point = new Laya.Point(target.mouseX, target.mouseY);
                    var pos: Laya.Point = new Laya.Point(moveP.x - this._startPos.x, moveP.y - this._startPos.y);
                    this._container.y += pos.y;

                    var yy = Math.abs(this._container.y);
                    if (yy >= this.maxHeight - this.hei) {
                        this.down();
                    } else if (yy <= this.minHeight) {
                        this.up();
                    }
                }
            });
            this._container.on(Laya.Event.MOUSE_UP, this, (event) => {
                yalla.Debug.log('==00 MOUSE_UP=='+this._isDown)
                if (this._isDown) {
                    this._isDown = false;
                    var pos = new Laya.Point(this._container.x, this._container.y);
                    yalla.Debug.log('==11 MOUSE_UP=='+pos.y)
                    this._handleOutsidePos(pos);
                    yalla.event.YallaEvent.instance.event('MouseEnd');
                }
            });
            this._container.on(Laya.Event.MOUSE_OUT, this, (event) => {
                if (this._isDown) {
                    this._isDown = false;
                    var pos = new Laya.Point(this._container.x, this._container.y);
                    this._handleOutsidePos(pos);
                    yalla.event.YallaEvent.instance.event('MouseEnd');
                }
            });
        }
 
        private init(): void {
            /*
            指定是否对使用了 scrollRect 的显示对象进行优化处理。默认为false(不优化)。 当值为ture时：将对此对象使用了scrollRect 设定的显示区域以外的显示内容不进行渲染，以提高性能(如果子对象有旋转缩放或者中心点偏移，则显示筛选会不精确)。
            */
            this.optimizeScrollRect = true;
            /*
            显示对象的滚动矩形范围，具有裁剪效果(如果只想限制子对象渲染区域，请使用viewport)，设置optimizeScrollRect=true，可以优化裁剪区域外的内容不进行渲染。 srollRect和viewport的区别： 1.srollRect自带裁剪效果，viewport只影响子对象渲染是否渲染，不具有裁剪效果（性能更高）。 2.设置rect的x,y属性均能实现区域滚动效果，但scrollRect会保持0,0点位置不变。
            */
            this.scrollRect = new Laya.Rectangle(0, 0, this.wid, this.hei);
            this._container = new Laya.Box();
            this._container.zOrder = 10;
            this._container.size(this.wid, this.hei);
            this.addChild(this._container);

            for (var i = 0; i < this._maxLen; i++) {
                var chatItem = new ChatItem();
                chatItem.height = 0;
                chatItem.visible = false;
                this._items.push(chatItem);
                this._container.addChild(chatItem);
            }
        }

        public setContentSize(wid: number, hei: number): void {
            this.wid = wid;
            this.hei = hei;
            this.size(wid, hei);
            this.scrollRect = new Laya.Rectangle(0, 0, wid, hei);
            this._setContainerSize(wid, hei);
        }
        
        public _setContainerSize(wid: number = this.wid, hei: number): void {
            if (hei < this.hei) { return; }
            this._container.size(this.wid, hei);
            if (this._isBoundary && this._isAutoJump) {
                this.jumpToLast();
            }
        }

        public setContainerBackgroundColor(color: string = "#ff0000"): void {
            this._container.graphics.drawRect(0, 0, this.wid, this.hei, color, "#ffff00", 1);
        }

        public addItem(chatMsg: any): void {
            this._allItemDataList.push(chatMsg);
            this.down();
            this.callLater(()=>{
            this._setContainerSize(this.wid, this.maxHeight);
            this.jumpToLast();
            })    
            // console.log('========addItem==========', this._container.height, this.maxHeight);
        }

        public down(): void {
            var length = this._allItemDataList.length;
            if (length <= 0) return;
            var data = this._allItemDataList[this._chatIndex];
            if (data) {
                var item = this._items.shift();
                if (length > this._maxLen) this.minHeight += item.height;
                item.initUI();
                item.update(data);
                item.visible = true;
                this.callLater(() => {
                    item.pos(0, this.maxHeight);
                    this.maxHeight += (item.height + this._space);
                });
                this._items.push(item);
                this._chatIndex++;
            }
        }

        private up(): void {
            var length = this._allItemDataList.length;
            if (length <= 0) return;
            var data = this._allItemDataList[this._chatIndex - this._maxLen - 1];
            if (data) {
                var item:ChatItem = this._items.pop();
                this.maxHeight -= (item.height + this._space);
                item.initUI();
                item.update(data);
                this.callLater(() => {
                    this.minHeight -= (item.height + this._space);
                    item.pos(0, this.minHeight);
                });

                this._items.unshift(item);
                this._chatIndex--;
            }
        }

        //边界控制
        private _handleOutsidePos(targetP: Laya.Point): void {
            var pos: Laya.Point = new Laya.Point(targetP.x, targetP.y);
            var time = this._tweenTime;
            yalla.Debug.log('==33 mouseUp _handleOutsidePos=='+targetP.y);
            if (targetP.y > 0) {
                this._isTweening = true;
                pos.y = 0;
                this._handleTweenAction(pos.y);
            }
            else if (targetP.y <= this.hei - this._container.height) {
                this._isTweening = true;
                pos.y = this.hei - this._container.height;
                this._handleTweenAction(pos.y);
                this._isBoundary = true;
            }
            else {
                yalla.Debug.log('==44 mouseUp _handleOutsidePos==');
                this._isBoundary = false;
            }
        }
        private _handleTweenAction(yy: number, time: number = this._tweenTime): void {
            time = (this._isBounceEnabled ? time : 0);
            yalla.Debug.log(this._container.y + '===_handleTweenAction=====' + yy + '---' + time + '-' + this._isBounceEnabled);
            Laya.Tween.to(this._container, { y: yy }, time, Laya.Ease.backOut, Laya.Handler.create(this, () => {
                this._isTweening = false;
            }));
        }
        //滚动到最后一项
        public scrollToLast(): void {
            this._isTweening = true;
            var yy = this.hei - this._container.height;
            this._handleTweenAction(yy);
        }
        //滚动到第一项
        public scrollToFirst(): void {
            this._isTweening = true;
            this._handleTweenAction(0);
        }
        //跳转到最后一项
        public jumpToLast(): void {
            this._isTweening = true;
            var yy = this.hei - this._container.height;
            this._handleTweenAction(yy, 0);
            // console.log(this.maxHeight+'=max=11====yy:'+yy, this.hei, this._container.height, this._container.y);
        }
        //跳转到第一项s
        public jumpToFirst(): void {
            this._isTweening = true;
            this._handleTweenAction(0, 1);
        }
        //拖到底部后，加入项时自动跳转到最后一项处
        public setAutoJumpToLast(jump: boolean): void {
            this._isAutoJump = jump;
        }
        //弹回动作
        public setBounceEnabled(bool: boolean): void {
            this._isBounceEnabled = bool;
        }

        public get listContainer():Laya.Box{
            return this._container;
        }
        public getAllItems(): Array<any> {
            return this._items;
        }

        get maxValue():number{
            return this.hei - this._container.height;
        }

        public removeAllItems(): void {
            for (var i = 0; i < this._items.length; i++) {
                this._items[i].item.destroy();
            }
            this._items = [];
            this._setContainerSize(this.wid, this.hei);
            this._container.pos(0, 0);
        }

        public clear(): void {
            for (var i = 0; i < this._items.length; i++) {
                this._items[i].item.destroy();
            }
            this._items = null;
            this.destroy(true);
        }
    }
}
