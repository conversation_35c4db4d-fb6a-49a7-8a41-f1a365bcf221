class LiveChat extends ui.publics.chat.liveChatUI {
    private _showLive: boolean = null;
    private _barrageManger: BarrageManger = null;
    private _newMsgScrollValue: number = 0;
    private _unread: number = 0;
    constructor(top: number = 0) {
        super();
        this.bg_right.top = top;
        this.init();
    }
    public trunVoice() { }
    public hideAllChat() { }
    private set unread(val: number) {
        if (val == this._unread) return;
        this._unread = val;
        this.unreadTx.label = val <= 99 ? String(val) : "99+";
        this.unreadTx.visible = val > 0;
    }
    private get unread(): number { return this._unread }
    private init() {
        this.chatList['sortItem'] = function (items) { return; }
        this._barrageManger = new BarrageManger();
        this._barrageManger.bottom = 80;
        this.addChildAt(this._barrageManger, 0);
        var showLiveChatItem = Laya.LocalStorage.getJSON("ludo_show_liveChat");
        this.showLive = showLiveChatItem ? showLiveChatItem.isOpen : true;
        this.addEventListener();
        if (yalla.Font.isRight()) { this.keyboard_btn.align = "right"; }
        if (yalla.Global.gameType == GameType.DOMINO)
            this.banner.skin = yalla.getDomino('BG_basechat');
    }
    public addEventListener() {
        this.showEmoji.on("click", this, this.showEmojiBoard);
        this.liveChat.on("click", this, this.checkLiveChat);
        this.chat_btn.on("click", this, this.checkChat);
        this.keyboard_btn.on("click", this, this.callKeyBoard);
        this.msgHintBtn.on(Laya.Event.CLICK, this, this.onClickToMsg);
        var native = yalla.Native.instance;
        native.off(native.Event.SHOWKEYBOARD, this, this.moveStage);
        native.off(native.Event.NATIVE_INPUT, this, this.onNativeInput);
        native.on(native.Event.NATIVE_INPUT, this, this.onNativeInput);
        native.on(native.Event.SHOWKEYBOARD, this, this.moveStage);
    }

    private moveStage(n) {
        var moveY = 0;
        if (n > 0) {
            if (yalla.Native.instance.deviceType == DeviceType.IOS) {
                moveY = -Laya.stage.height * n + this.bottom;
            } else {
                moveY = -Laya.stage.height * n + 80 + this.bottom;
            }
        }
        Laya.Tween.to(Laya.stage, { y: moveY }, 100, null, null, 0, true, true);
    }
    /**
     * 定位到最新的消息
     */
    private onClickToMsg(): void {
        this.chatPanel.vScrollBar.value = this._newMsgScrollValue + 146;
        this.msgHintBtn.visible = false;
        this._newMsgScrollValue = this.chatPanel.vScrollBar.max;
    }
    private updateChatScrollValue(): void {
        this.timer.clear(this, this.checkScrollShow);
        if (this.chatPanel.vScrollBar.max - this.chatPanel.vScrollBar.value > this.chat.height) {
            if (!this.msgHintBtn.visible) this._newMsgScrollValue = this.chatPanel.vScrollBar.max;
            this.msgHintBtn.visible = true;
        } else {
            this.timer.frameOnce(30, this, this.checkScrollShow);
        }
    }
    private checkScrollShow(): void {
        this.chatPanel.vScrollBar.value = this.chatPanel.vScrollBar.max;
    }
    private onNativeInput(json) {
        yalla.Debug.log('=====ludo recieve chat msg');
        if (json && (json.msg || json.id)) {
            yalla.Global.nativeIputMsg = json.msg;
            if (json.id && !json.msg) yalla.Global.nativeIputMsg = json.id;
            if (json.isSend) {
                switch (Number(json.type)) {
                    case 3:
                        this.sendEmoji(json.id);
                        break;
                    case 2:
                        this.sendHorseFace()
                        break;
                    default:
                        this.sendNormalMsg();
                        break;
                }
                this.keyboard_btn.text = "Tap here to chat";
            } else {
                this.keyboard_btn.text = json.msg;
            }
        }
    }
    private _sendEmojiFlag: boolean = false;
    private sendEmoji(emojiId: number) {
        yalla.Debug.log("=====liveChat.sendEmoji=====this._sendEmojiFlag:" + this._sendEmojiFlag);
        if (this._sendEmojiFlag) {
            yalla.common.TipManager.instance.showOneTip("Send messages too frequently.\n Please try again later", {}, 2000);
        } else {
            var success = this.sendMsg(3, emojiId);
            yalla.Debug.log("=====liveChat.sendEmoji=====success:" + success);
            if (success) {
                var cdTime = (yalla.Global.Account && yalla.Global.Account.leagueWatch) ? 3000 : 5000;
                yalla.Native.instance.mobClickEvent(buryPoint.GAME_SPECTATER_EMOJI);//围观发送表情
                this._sendEmojiFlag = true;
                setTimeout(() => { this._sendEmojiFlag = false }, cdTime);
            }
        }
        yalla.Global.nativeIputMsg = "";
    }

    private _sendHorseFlag: boolean = false;
    private sendHorseFace() {
        if (this._sendHorseFlag) {
            yalla.common.TipManager.instance.showOneTip("Send messages too frequently.\n Please try again later", {}, 2000);
        } else {
            var success = this.sendMsg(2);
            if (success) {
                var cdTime = (yalla.Global.Account && yalla.Global.Account.leagueWatch) ? 3000 : 5000;
                yalla.Native.instance.mobClickEvent(buryPoint.GAME_SPECTATER_EMOJI);//围观发送表情
                this._sendHorseFlag = true;
                setTimeout(() => { this._sendHorseFlag = false }, cdTime);
            }
        }
        yalla.Global.nativeIputMsg = "";
    }

    private _sendNormalFlag: boolean = false;
    private sendNormalMsg() {
        if (this._sendNormalFlag) {
            yalla.common.TipManager.instance.showOneTip("Send messages too frequently.\n Please try again later", {}, 2000);
        } else {
            var success = this.sendMsg()
            if (success) {
                var cdTime = (yalla.Global.Account && yalla.Global.Account.leagueWatch) ? 3000 : 1000;
                yalla.Native.instance.mobClickEvent(buryPoint.GAME_SPECTATER_MESSAGE);//围观发送聊天
                this._sendNormalFlag = true;
                setTimeout(() => { this._sendNormalFlag = false }, cdTime);
            }
            yalla.Global.nativeIputMsg = "";
        }
    }
    public onChat(chat: any, isMy: boolean, inSit: boolean = false) {
        /*!inSit && */this._showLive && this._barrageManger.addChat(chat, isMy);
        var item: Laya.View;
        var [key_L, key_R] = ['ludo-chatItem-L', 'ludo-chatItem-R'];
        var poolKey = '';
        if (this.chatList.numChildren >= yalla.Global.ChatMaxLen) {
            yalla.Global.ChatMsgList.shift();
            item = this.chatList.getChildAt(0) as Laya.View;
            poolKey = item['side'] == 0 ? key_L : key_R;
            item.removeSelf();
            Laya.Pool.recover(poolKey, item);
            this.chatList.refresh();
            if (this.chat.visible && this.msgHintBtn.visible && this._newMsgScrollValue > item.height) this._newMsgScrollValue -= item.height;
        }
        var cls: any = chat.side == 0 ? yalla.common.ChatItemL : yalla.common.ChatItemR;
        poolKey = chat.side == 0 ? key_L : key_R;
        item = Laya.Pool.getItemByClass(poolKey, cls);
        var skinID = 12000;
        if (chat.playerShowInfo && chat.playerShowInfo.talkSkinId) skinID = chat.playerShowInfo.talkSkinId;
        item['update'](chat, yalla.Global.ChatIndex, skinID);
        this.chatList.addChild(item);
        yalla.Global.ChatIndex++;
        yalla.Global.ChatMsgList.push(this.createReportMsg(chat.chat, chat.idx));
        if (!this.chat.visible && !isMy) {
            this.unread++;
        }
        if (this.chat.visible) this.updateChatScrollValue();
    }
    private createReportMsg(msgResult: any, idx: number, isReport: number = 0, type: number = 1): any {
        var msg = '';
        if (msgResult) {
            var extension = msgResult.extension;
            if (extension) {
                if (typeof (extension) == "string" && extension.indexOf('{') > -1) extension = JSON.parse(msgResult.extension);
                msg = extension.id;
            }
            else msg = msgResult.msg;
        }
        return { "AccountId": idx, "MsgContent": msg, "IsReport": isReport, "Type": type };
    }

    private checkChat(e: Laya.Event) {//有埋点待补充
        if (e) {
            e.stopPropagation(); yalla.Sound.playSound("click");
        }
        this.chat.visible ? this.hideChat() : this.showChat();
    }
    public set showLive(b: boolean) {
        if (b === this._showLive) return;
        this._showLive = b;
        Laya.LocalStorage.setJSON("ludo_show_liveChat", { isOpen: b });
        this.liveChat.selected = b;
        this._barrageManger.visible = b;
        if (!b) this._barrageManger.clearMsg();
    }
    public get showLive(): boolean {
        return this._showLive;
    }
    private showChat() {
        this.chat_btn.skin = "game/btn_closeChat.png"
        this.chat.visible = true;
        this.unread = 0;
        this._barrageManger.onChatShow();
        this.checkScrollShow();
    }
    private hideChat() {
        this.chat_btn.skin = "game/btn_chat.png";
        this.chat.visible = false;
        this.msgHintBtn.visible = false;
        yalla.common.BubbleReport.instance.hide();
        this._barrageManger.onChatHide();
    }
    public close() {
        this.hideChat();
    }
    private sendMsg(type: number = 1, emojiId?): boolean {
        if (yalla.Mute.isBanTalk) {//被运营禁言
            yalla.Mute.showBanTalkDialog();
            return;
        }
        var chat = yalla.Global.nativeIputMsg || "";
        chat = yalla.Emoji.onverterFastChat(chat);
        yalla.Debug.log(yalla.Global.nativeIputMsg + "==" + chat + "=====liveChat.sendMsg=====ChatLastMsg:" + yalla.Global.ChatLastMsg);
        if (chat == yalla.Global.ChatLastMsg) {
            yalla.common.TipManager.instance.showOneTip('Please do not send messages repeatedly', {}, 2000);
            return false;
        }
        var result = { msg: chat };
        if (type == 3 && emojiId) {
            yalla.Global.ChatLastMsg = emojiId;
            result['extension'] = JSON.stringify({ id: emojiId });
            result['msg'] = '';
        } else {
            yalla.Global.ChatLastMsg = chat;
        }
        yalla.Debug.log('=====ludo recieve chat msg ==== and send msg');
        yalla.Debug.log(result);
        if (chat.trim().length > 0) {
            yalla.event.YallaEvent.instance.event("ludo_chat_my", result);
            this.event("close", false);
            if (yalla.Global.gameType == GameType.LUDO)
                ludo.GameSocket.instance.sendChat(result);
            else if (yalla.Global.gameType == GameType.SNAKEANDLADER)
                yalla.data.snake.UserService.instance.sendChat(result);
        }
        return true;
    }
    private callKeyBoard(e) {
        yalla.Native.instance.callKeyBoard(1, yalla.Global.nativeIputMsg);
    }
    private showEmojiBoard() {
        yalla.Native.instance.callKeyBoard(2, yalla.Global.nativeIputMsg);
    }
    private checkLiveChat(e) {
        this.showLive = this.liveChat.selected;
        // if (this.barrageManager && !this.liveChat.selected) {
        //     this.barrageManager.close();
        // }
    }
    public removeEventListener() {
        this.showEmoji.off("click", this, this.showEmojiBoard);
        this.liveChat.off("click", this, this.checkLiveChat);
        this.chat_btn.off("click", this, this.checkChat);
        this.keyboard_btn.off("click", this, this.callKeyBoard);
        this.msgHintBtn.off(Laya.Event.CLICK, this, this.onClickToMsg);
        var native = yalla.Native.instance;
        native.off(native.Event.NATIVE_INPUT, this, this.onNativeInput);
        native.off(native.Event.SHOWKEYBOARD, this, this.moveStage);
    }
    public clear() {
        this._barrageManger.clear()
        this.removeEventListener();
        this.destroy(true);
    }
}