module yalla.common {
    export class MsgItem {
        public ui: any;
        private skin_id: number = 0;
        public SKINID: number = 12000;
        public side: number = 0;
        public defaultUrl: string = '';

        private _royalLevel: number = 0;
        private _vip: number = 0;
        private rlSk: Laya.Skeleton = null;
        private vipSk: Laya.Skeleton = null;
        private _rlKey = "watchBarrage_live_rl";
        private _vipKey = "watchBarrage_live_vip";
        private _beginX = 0;
        private _xx: number = 0;
        /**是否表情（表情不可以举报） */
        public isEmoji = false;

        constructor(ui: Laya.Box, beginX: number) {
            this.ui = ui;
            this._beginX = beginX;
            this.ui.lb.style.fontSize = 22;
            this.ui.lb.style.color = '#373737';
            this.ui.lb.style.valign = 'center';
            this.ui.lb.style.wordWrap = true;
            this.ui.lb.style.width = 540;
        }

        public update(skinId: number, defaultUrl: string, obj: any): void {
            this.defaultUrl = defaultUrl;
            this.skinId = skinId;
            this.side = obj.side;
            this.onChat(obj.chat);
            // this.chat = obj.chat;
        }

        public watchUI(obj: any, vx: number): void {
            if (!obj.watch) return;

            if (obj.watch) {
                console.log('===liveChat===', obj);
                this.royalLevel = obj.playerShowInfo.fPlayerInfo.royal;
                this.vip = obj.playerShowInfo.fPlayerInfo.vip;
                this.updatePos(vx)
            }
        }

        public set skinId(val: number) {
            this.defaultUrl.length > 0 && (this.ui.msgBox.skin = this.defaultUrl);

            if (this.skin_id != val) {
                this.skin_id = val;
                if (val > this.SKINID)
                    // this.ui.msgBox.skin = yalla.getPublic('chat_' + val);    
                    this.setSkin();
            }
        }

        public get skinId(): number {
            return this.skin_id;
        }

        private setSkin() {
            // if (ChatManager.instance.isMsgSkinLoaded(this.skin_id)) {
            //     this.updateSkin(this.skin_id);
            //     return;
            // }
            yalla.event.YallaEvent.instance.once(`chat_${this.skin_id}.png`, this, () => {
                this.updateSkin(this.skin_id);
            })
            yalla.File.getFileByNative(`chat_${this.skin_id}.png`);
        }

        private updateSkin(skinId: number): void {
            if (this.ui && this.ui.msgBox) {
                if (skinId > this.SKINID) {
                    var path = yalla.File.cachePath + `/chat_${skinId}.png`
                    if (yalla.File.existed(path)) {
                        (this.ui.msgBox as Laya.Image).skin = yalla.File.filePath + `chat_${skinId}.png`;
                        // ChatManager.instance.addLoadMsgSkin(skinId);
                    }
                } else {
                    this.defaultUrl.length > 0 && (this.ui.msgBox.skin = this.defaultUrl);
                }
            }
        }

        public onChat(msg: any): void {
            if (!msg) return;
            var messageType = msg.messageType;
            var extension = msg.extension;
            this.isEmoji = false;
            if (extension || messageType) {
                this.ui.chat_emoji.visible = true;
                this.ui.msgBox.visible = false;
                this.ui.height = 178;

                if (typeof (extension) == "string" && extension.indexOf('{') > -1) extension = JSON.parse(msg.extension);
                var emojiId = 0;
                if (extension) emojiId = extension.id;
                if (messageType == yalla.data.jackaro.GameChatMessageType.CHAT_MESSAGE_EMOJI) emojiId = Number(msg.msg);
                if (emojiId) {
                    this.isEmoji = true;
                    var fileName = `emoji_${emojiId}.png`;
                    yalla.event.YallaEvent.instance.once(fileName, this, d => {
                        this.ui.chat_emoji.skin = yalla.File.filePath + fileName;
                    })
                    yalla.File.getFileByNative(fileName);

                    // if (yalla.util.IsBrowser()) {
                    //     //TODO::测试
                    //     Laya.loader.load(`res/test/emoji_${id}.png`, Laya.Handler.create(this, () => {
                    //         this.ui.chat_emoji.skin = `res/test/emoji_${id}.png`;
                    //     }));
                    // }
                } else if (msg.msg) {
                    this.chat = msg.msg;
                }

            } else {
                if (msg.msg) {
                    this.chat = msg.msg;
                }
            }

        }

        public set chat(val: string) {
            var lb: Laya.HTMLDivElement = this.ui.lb;
            lb.innerHTML = '';
            lb.style.height = 28;//初始化至最初高度

            if (val.indexOf("icon_emoji_") > -1) {
                this.isEmoji = true;
                this.ui.chat_emoji.visible = true;
                this.ui.chat_emoji.skin = "public/" + val;
                this.ui.msgBox.visible = false;
                this.ui.height = 178;
            } else {
                this.ui.chat_emoji.visible = false;
                this.ui.msgBox.visible = true;
                lb.innerHTML = this.getChatStr(val);
                this.ui.msgBox.size(this.contextWidth, this.contentHeight);
                if (this.side != 0) {
                    this.ui.msgBox.right = 86 + this.ui.msgBox.width;
                    this.ui['lbBox'].right = this.ui.msgBox.right - (this.ui.msgBox.width - lb.contextWidth) / 2 - 18;
                } else {
                    lb.x = (this.ui.msgBox.width - lb.contextWidth) / 2;
                }
                this.ui.height = Math.max(148 + 30, this.ui.msgBox.height + 36);
            }
        }

        public get contentHeight(): number {
            return Math.max(this.ui.lb.height + 118 - 34, 114);
        }

        public get contextWidth(): number {
            return Math.max(Math.min(this.ui.lb.contextWidth + 86, 600), 142);
        }

        private getChatStr(msg: string): string {
            msg = msg.replace(/[\\<>]/gm, function (a) {
                if (a == '<')
                    return '&lt;';
                else if (a == '>')
                    return '&gt;';
                else
                    return "";
            });
            // var len = yalla.util.getBLen(msg);
            // if (len > yalla.Global.MAX_CHARS) return yalla.util.filterStr(msg, yalla.Global.MAX_CHARS);
            //36是之前默认表情的尺寸，统一一下。
            return yalla.Emoji.createHtmlString(msg, 36)
        }


        public set royalLevel(rl: number) {
            this._royalLevel = rl;
            yalla.Debug.log('=====royalLevel=' + rl);
            if (rl > 0) {
                this.ui.RL.visible = true;
                if (!this.rlSk) {
                    var __s = yalla.SkeletonManager.instance;
                    __s.on(this._rlKey, this, this.loadRLSk);
                    __s.initSk("royallevel/huangguan", this._rlKey);
                } else {
                    this.rlSk.visible = true;
                    //目前r+5等级标识和rl5一样
                    let rlAnimationLv: number = this._royalLevel > 5 ? 5 : this._royalLevel;
                    this.rlSk.play(`R${rlAnimationLv}`, true);
                }
            } else {
                this.ui.RL.visible = false;
                if (this.rlSk) {
                    this.rlSk.stop();
                    this.rlSk.visible = false;
                }
            }
        }
        public set vip(vip: number) {
            this._vip = vip;
            if (vip > 0) {
                this.ui.VIP.visible = true;
                if (!this.vipSk) {
                    var __s = yalla.SkeletonManager.instance;
                    __s.on(this._vipKey, this, this.loadVipSk);
                    __s.initSk("vip/xunzhang", this._vipKey);
                } else {
                    this.vipSk.visible = true;
                    this.vipSk.play(`vip_${this._vip}`, true);
                }
            } else {
                this.ui.VIP.visible = false;
                if (this.vipSk) {
                    this.vipSk.stop();
                    this.vipSk.visible = false;
                }
            }
        }

        /**
         * 1 左  -1 右
         * @param vx 
         */
        private updatePos(vx: number) {
            this._xx = this._beginX;
            if (this._royalLevel > 0) {
                this.ui.RL.x = this._xx;
                this._xx += (this.ui.VIP.width + 6) * vx;
            }
            if (this._vip > 0) {
                this.ui.VIP.x = this._xx;
                this._xx += (this.ui.VIP.width + 6) * vx;
            }
            this.ui.nickName.x = this._xx;
        }
        private loadRLSk(sk: Laya.Skeleton) {
            if (!this.rlSk) {
                this.rlSk = sk;
                this.ui.RL.addChild(this.rlSk);
                this.rlSk.pos(14, 10);
                this.rlSk.scale(0.6, 0.6);
                //目前r+5等级标识和rl5一样
                let rlAnimationLv: number = this._royalLevel > 5 ? 5 : this._royalLevel;
                this.rlSk.play(`R${rlAnimationLv}`, true);
                yalla.SkeletonManager.instance.off(this._rlKey, this, this.loadRLSk);
            }
        }
        private loadVipSk(sk: Laya.Skeleton) {
            if (!this.vipSk) {
                this.vipSk = sk;
                this.ui.VIP.addChild(this.vipSk);
                this.vipSk.pos(14, 10);
                this.vipSk.scale(0.6, 0.6);
                this.vipSk.play("vip_" + this._vip, true);
                yalla.SkeletonManager.instance.off(this._vipKey, this, this.loadVipSk);
            }
        }


        public clear() {
            // if (this.ui && this.ui.lb) this.ui.lb.innerHTML = '';
        }
    }
}