module yalla.common {
    export class UnReadMsg {
        public ui: any;
        public readNum: number = 0;

        constructor(ui: Laya.Box) {
            this.ui = ui;
            this.ui.mouseThrough = true;
        }

        public addReadNum(addValue: number = 1) {
            this.readNum += addValue;
            this.updateStatus(this.readNum);
        }

        public updateStatus(num:number = 0): void{
            this.readNum = num;
            if (this.readNum < 1) {
                this.ui.visible = false;
            } else {
                this.ui.visible = true;
                this.ui['label'] = this.readNum <= 99 ? this.readNum + '' : "99+";
            }
        }
    }
}