module yalla.common.connect {
    import EventDispatcher = laya.events.EventDispatcher;
    export class ReconnectControl extends EventDispatcher {
        static _instance: ReconnectControl;
        private _view: ReconnectView;
        public COUNT_NUM = 30;      //30次重连机会
        public Event = {
            RECONECT: "reconect",
            TIMEOVER: "timeover"
        }
        constructor() {
            super();
        }

        static get instance(): ReconnectControl {
            if (!this._instance) this._instance = new ReconnectControl();
            return this._instance;
        }

        /**
         * 重连
         * 如果结算，则不进行重连
         *noTicketLogin jack游戏需要，socket连上，但是游戏服的ticketLogin登陆失败，需要表现断线重连动画，且再次发起票据登陆
         */
        private loseNetCount = 0;
        private justShowAni = false;
        public connect(immediateConnect: boolean = true, justShowAni: boolean = false, noTicketLogin = false): void {
            yalla.Debug.log(noTicketLogin+" connect---IsGameOver:"+yalla.Global.IsGameOver+"isconnect:"+yalla.Global.isconnect+"immediateConnect:"+immediateConnect)
            if (yalla.Global.IsGameOver || (yalla.Global.isconnect && !noTicketLogin)) return;
            this.justShowAni = justShowAni;
            var time = yalla.System.getNowTime() - yalla.Global.onLoseNextTime;
            yalla.Debug.log(!immediateConnect + '--ReControl.connect--' + time+'--showAni:'+justShowAni);
            if (time <= yalla.Global.loseNet_interval - 1000 && !immediateConnect) {
                Laya.timer.clear(this, this.loopConnect);
                yalla.Debug.log("connect---!showAni"+!this.justShowAni)
                if(!this.justShowAni) this.event(this.Event.RECONECT);//默默发起重连 TODO::这里第一次开启重连计时器前，就不默认发起重连
                this.loseNetCount = Math.round(yalla.Global.loseNet_interval / 1000) - 1;
                Laya.timer.loop(1000, this, this.loopConnect);
            } else {
                this.createConnectView();
            }
        }

        private loopConnect():void{
            this.loseNetCount -= 1;
            if (this.loseNetCount <= 0) {
                if (!yalla.Global.isconnect) this.createConnectView();
            } else {
                // if (!yalla.Global.isconnect && this.loseNetCount % (Global.heart_interval/1000) == 0)  {
                if (!yalla.Global.isconnect && this.loseNetCount % 5 == 0)  {
                    if(!this.justShowAni) this.event(this.Event.RECONECT);
                }
            }
        }

        private createConnectView(): void {
            yalla.Debug.log("createConnectView:" + this.justShowAni);
            Laya.timer.clear(this, this.loopConnect);
            if (!this._view) this._view = new ReconnectView();
            if (!this._view.displayedInStage) {
                if(!this.justShowAni) this.event(this.Event.RECONECT);
                this._view.show();
                this._view.countNum = this.COUNT_NUM;
                Laya.timer.loop(1000, this, this.updateTimeCount);
            }
        }

        private updateTimeCount(): void {
            this._view.countNum -= 1;
            if (this._view.countNum <= 0) {
                this.event(this.Event.TIMEOVER);
                Laya.timer.clear(this, this.updateTimeCount);
                yalla.common.Confirm.instance.showConfirm("Lose Network Links",
                    Laya.Handler.create(this, () => {
                        if(!this.justShowAni) this.event(this.Event.RECONECT);
                        this._view.countNum = 10;//再发起一次重连
                        Laya.timer.loop(1000, this, this.updateTimeCount);
                        yalla.common.Confirm.instance.hideConfirm();
                        if (yalla.Global.gameType == GameType.LUDO) {
                            yalla.Native.instance.mobClickEvent(buryPoint.GAME_BREAK_CONNECT);
                        } else if (yalla.Global.gameType == GameType.DOMINO) {
                            yalla.Native.instance.mobClickEvent(buryPoint.DGAME_BREAK_CONNECT);
                        } else if (yalla.Global.gameType == GameType.SNAKEANDLADER) {
                        }
                    }),
                    Laya.Handler.create(this, () => {
                        // yalla.Debug.log("===reconnect===isPrivate:"+yalla.Global.Account.isPrivate);
                        this.backHall();
                        
                        yalla.util.sendExitType(yalla.data.ExitType.INITIATIVE_QUIT);
                        if (yalla.Global.gameType == GameType.LUDO) {
                            yalla.Native.instance.mobClickEvent(buryPoint.GAME_BREAK_EXIT);
                        }else if (yalla.Global.gameType == GameType.DOMINO) {
                            yalla.Native.instance.mobClickEvent(buryPoint.DGAME_BREAK_EXIT);
                        }else if (yalla.Global.gameType == GameType.SNAKEANDLADER) {
                        }
                    }), ['Connect', 'Exit'],false,false);
            } else {
                // if (this._view.countNum % (Global.heart_interval / 1000) == 0 && !yalla.Global.isconnect) {
                if (this._view.countNum % 5 == 0 && !yalla.Global.isconnect) {
                    if(!this.justShowAni) this.event(this.Event.RECONECT);
                }
            }
        }

        public get isConnectView(): boolean{
            return (this._view && this._view.displayedInStage);
        }

        /**
         * 重连成功
         */
        public connectSucss(): void {
            this.justShowAni = false;
            Laya.timer.clearAll(this);
            if (this._view) {
                this._view.hide();
            }
            if (Confirm.instance.displayedInStage) {
                Confirm.instance.hideConfirm();
            }
        }

        public hideConnect(): void {
            this.connectSucss();
        }

        private backHall(): void{
            if(yalla.Global.Account.isPrivate == GameRoomType.CHAMPION){//锦标赛
                yalla.Native.instance.event(yalla.Native.instance.Event.BACKCHAMPION, [{winnerId:-1}]);
            } else {
                var isBack = yalla.Native.instance.isBack;
                if(yalla.Global.Account.isPrivate == GameRoomType.VIP) yalla.Native.instance.backHall(isBack, { to: 'vip', gameId:yalla.Global.Account.gameId});
                else if (yalla.Global.Account.isPrivate == GameRoomType.LEAGUE) yalla.util.backHall(isBack);
                else yalla.Native.instance.backHall();
            }
        }
    }
}