module yalla.common.connect {
    export class ReconnectView extends ui.publics.dialog.reconnectUI {
        _countNum: number = 30;
        private _templete: Laya.Templet;
        private _sk: Laya.Skeleton;

        constructor() {
            super();
            if (!this._templete) {
                this._templete = new Laya.Templet();
                this._templete.on(Laya.Event.COMPLETE, this, () => {
                    if (!this._templete) return;
                    this._sk = this._templete.buildArmature(1);
                    this.ani_box.addChild(this._sk);
                    this._sk.pos(50, 50);
                    this._sk.play(0, true);
                })
                this._templete.on(Laya.Event.ERROR, this, () => {
                    yalla.Debug.log("----------loading skin error----------");
                })
                this._templete.loadAni(yalla.getSkeleton('loading/loading'));
            }
            this.load_tx.color = '#fff29f';
            this.load_tx.stroke = 0;
            this.bg_txt.visible = false;
        }
        set countNum(val: number) {
            if (val >= 0) {
                this._countNum = val;
                this.count.text = "(" + this._countNum + ")";
            }
        }
        get countNum(): number {
            return this._countNum;
        }
        show() {
            this.load_tx.text = '';
            this.load_tx.text = 'Reconnecting...';
            yalla.util.closeAllDialog();
            Laya.stage.addChild(this);
            this._sk && this._sk.play(0, true);
        }
        hide() {
            this.removeSelf();
            this._sk && this._sk.stop();
        }
        clear() {
            if (this._sk) {
                this._sk.stop();
                this._sk.destroy(true);
                this._sk = null
            }
            this.destroy(true);
        }
    }
}