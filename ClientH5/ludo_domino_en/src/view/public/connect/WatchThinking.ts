module yalla.common.connect {
    export class WatchThinking extends ReconnectView {
        constructor() {
            super();
            this.count.visible = false;
            this.load_tx.color = '#ffffff';
            this.load_tx.stroke = 2;
            this.load_tx.strokeColor = '#0F4C39';
        }
        public static _instance: WatchThinking = null;
        public static get instance(): WatchThinking {
            return this._instance ? this._instance : this._instance = new WatchThinking();
        }
        show() {
            this.load_tx.text = '';
            this.load_tx.text = 'Players are still thinking...';
            yalla.util.closeAllDialog();
            Laya.stage.addChild(this);
            this.bg_txt.visible = true;

            this.bg.visible = false;
            this.mouseEnabled = true;
            this.mouseThrough = true;
        }
        hide() {
            yalla.Debug.log("WatchThinking hide")
            this.displayedInStage && this.removeSelf();
        }
    }
}