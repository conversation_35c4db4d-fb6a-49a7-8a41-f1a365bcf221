module yalla.common {
    export class Confirm extends ui.publics.dialog.confirmUI {
        public isExit:boolean = false; //是否退出游戏面板
        constructor() {
            super();
        }
        public static _instance: Confirm;
        static get instance(): Confirm {
            if (!this._instance) {
                this._instance = new Confirm();
            }
            return this._instance;
        }
        showConfirm(text: string, connectHander: Laya.Handler = null, exitHander: Laya.Handler = null, btnNameList:Array<string>=null, closeOther: boolean = false, showEffect: boolean = true) {
            this.clear();
            if (!this.isPopup) yalla.DialogManager.instance.open(this, true, closeOther, showEffect);
            this.confrim_tx.text = text;
            if (!!connectHander) {
                if(btnNameList && btnNameList[0]) {
                    this._connect.label = btnNameList[0];
                } else {
                    this._connect.label = yalla.data.TranslationD.Game_Btn_Connect;
                }
                this.connect.on("click", null, (e:Laya.Event) => {
                    connectHander.run();
                });
            }
            if (!!exitHander) {
                if(btnNameList && btnNameList[1]) this._exit.label = btnNameList[1];
                else this._exit.label = yalla.data.TranslationD.Game_Btn_Exit;
                this.exit.on("click", null, () => {
                    exitHander.run();
                });
            }
            yalla.event.YallaEvent.instance.event("open_dialog");
        }
        
        hideConfirm() {
            this.isExit = false;
            this.close();
            this.exit.offAll();
            this.connect.offAll();
        }
        get exit(): Laya.Button {
            this._exit.visible = true;
            this._exit.centerX = 0;
            if (this._connect.visible)
                this.allBtnShow();
            return this._exit;
        }
        get connect(): Laya.Button {
            this._connect.visible = true;
            this._connect.centerX = 0;
            if (this._exit.visible){
                this.allBtnShow();
            } else {
                this._connect.width = 380;
            }
            return this._connect;
        }
        private allBtnShow() {
            this._exit.visible = true;
            this._connect.visible = true;
            if(yalla.Font.lan == 'en'){
                this._exit.centerX = -152;
                this._connect.centerX = 152;
            }else{
                this._connect.centerX = -152;
                this._exit.centerX = 152;
            }
            this._connect.width = 270;
        }

        public clear() {
            this._exit.visible = this._connect.visible = false;
            this._exit.offAll();
            this._connect.offAll();
        }
    }
}