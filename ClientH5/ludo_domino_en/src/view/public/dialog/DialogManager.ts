module yalla {

    export class DialogManager extends laya.events.EventDispatcher {

        static _instance: DialogManager;
        constructor() {
            super();
        }

        static get instance(): DialogManager {
            return this._instance || (this._instance = new DialogManager());
        }

        public open(view: Laya.Dialog, isModal: boolean = true, closeOther: boolean = false, showEffect: boolean = true, closeFunc: Function = null, closeDialogOnSide: boolean = true): void  {
            UIConfig.closeDialogOnSide = closeDialogOnSide;
            view.isModal = isModal;
            view.closeEffect = null;
            view.popup(closeOther, showEffect);
            view.onClosed = (e) => {
                closeFunc && closeFunc(e);
                UIConfig.closeDialogOnSide = !closeDialogOnSide;
            }
        }

        public close(view: Laya.Dialog): void  {
            if (view) {
                view.close();
            }
        }

        public showDialog(name: string, content: string, btnNameList: Array<string> = [], isBack:boolean = false): void {
            var caller = name.length > 0 ? yalla.Native.instance : this;
            yalla.Native.instance.removeMatchView();
            yalla.common.Confirm.instance.showConfirm(content, Laya.Handler.create(caller, () => {
                yalla.Native.instance.backHall(isBack);
                if (name.length > 0) yalla.Native.instance[name]();
            }), null, btnNameList);
        }
    }
}