module yalla.common {
    export class Tip extends ui.publics.dialog.TipUI {
        constructor() {
            super();
        }

        public static _instance: Tip;
        static get instance(): Tip {
            if (!this._instance) {
                this._instance = new Tip();
            }
            return this._instance;
        }
        showTip(msg: string, closeOther: boolean = false, showEffect: boolean = true) {
            this.msg.text = msg;
            if (!this.isPopup) yalla.DialogManager.instance.open(this, true, closeOther, showEffect);
        }

    }
}