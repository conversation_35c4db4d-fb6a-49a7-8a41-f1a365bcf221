module yalla.common {
    export class WaringDialog {
        static _callBack: Function;
        constructor() {
        }
        public static show(msg: string) {
            // yalla.Global.IsGameOver = true;
            yalla.common.Confirm.instance.showConfirm(msg, null, Laya.Handler.create(this, this._callBack));
        }

        public static showResultError(code: number, callBack: Function = null, callBack1: Function = null) {
            this._callBack = callBack;
            switch (code) {
                case yalla.data.ErrorCode.LOGIN_FAILD_PW://密码错误
                    this.show(yalla.data.TranslationD.Game_Error_LOGIN_FAILD_PW);
                    break;
                case yalla.data.ErrorCode.LOGIN_FAILD_OLDSIGN://登陆ID错误
                    this.show(yalla.data.TranslationD.Game_Error_FAILD_OLDSIGN);
                    break;
                case yalla.data.ErrorCode.LOGIN_FAILD_SIGN://重复的签名
                    this.show(yalla.data.TranslationD.Game_Error_LOGIN_FAILD_SIGN);
                    break;
                case yalla.data.ErrorCode.LOGIN_FAILD_TIME://客户端时间错误
                    this.show(yalla.data.TranslationD.Game_Error_LOGIN_FAILD_TIME);
                    break;
                case yalla.data.ErrorCode.QUICKSTART_FAILD_NOROOM://房间已满
                    this.show(yalla.data.TranslationD.Game_Error_QUICKSTART_FAILD_NOROOM);
                    break;
                case yalla.data.ErrorCode.ENTER_ROOM_FAILD_NOROOM://房间已满
                    this.show(yalla.data.TranslationD.Game_Error_ENTER_ROOM_FAILD_NOROOM);
                    break;
                case yalla.data.ErrorCode.ENTER_ROOM_FAILD_ROOMID://错误的房间号
                    this.show(yalla.data.TranslationD.Game_Error_ENTER_ROOM_FAILD_ROOMID);
                    break;
                case yalla.data.ErrorCode.GAMEOPERATE_FAILD_SIT_NOSTOOL://已有人
                    this.show(yalla.data.TranslationD.Game_Error_GAMEOPERATE_FAILD_SIT_NOSTOOL);
                    break;
                case yalla.data.ErrorCode.GAMEOPERATE_FAILD_SIT_WRONGSTOOLNUM://错误的凳子ID
                    this.show(yalla.data.TranslationD.Game_Error_GAMEOPERATE_FAILD_SIT_WRONGSTOOLNUM);
                    break;
                case yalla.data.ErrorCode.GAMEOPERATE_FAILD_SIT_WRONGTIME://错误的操作时间
                    this.show(yalla.data.TranslationD.Game_Error_GAMEOPERATE_FAILD_SIT_WRONGTIME);
                    break;
                case yalla.data.ErrorCode.NO_MONEY://余额不足
                    this.show(yalla.data.TranslationD.Game_Error_NO_MONEY);
                    break;
                case yalla.data.ErrorCode.NO_DIAMOND://钻石不足
                    this.show(yalla.data.TranslationD.Game_Error_NO_DIAMOND);
                    break;
                case yalla.data.ErrorCode.ACCOUNT_BE_FROZEN://账号被冻结
                    this.show(yalla.data.TranslationD.Game_Error_ACCOUNT_BE_FROZEN);
                    break;
                case yalla.data.ErrorCode.MAX_LIMIT://超出限制
                    this.show(yalla.data.TranslationD.Game_Error_MAX_LIMIT);
                    break;
                case yalla.data.ErrorCode.JOIN_ROOM_FAILD_NOROOM://无此房间
                    this.show(yalla.data.TranslationD.Game_Error_GAME_FINISH);
                    break;
                case yalla.data.ErrorCode.JOIN_ROOM_FAILD_MAX://房间人数已满
                    this.show(yalla.data.TranslationD.Game_Error_GAME_FINISH);
                    break;
                case yalla.data.ErrorCode.GAME_HAS_FINISH://房间已结束
                    this.show(yalla.data.TranslationD.Game_Error_GAME_FINISH);
                    break;
                case yalla.data.ErrorCode.NONE_ERROE://TODO::可能成功匹配了domino，但是连上了ludo的socket
                    this.show(yalla.data.TranslationD.Game_Error_NONE_ERROE);
                    break;
                // default:
                //     this.show(code.toString());
                //     break;
            }
        }


        public static showChampionError(code: number, callBack: Function = null, btnNameList: Array<string> = null) {
            this._callBack = callBack;
            var str = '';
            switch (code) {
                case yalla.data.ErrorCode.CHAMPIONSHIP_RIGISTER_LEVEL://等级不够
                    str = yalla.data.TranslationD.Champion_Error_RIGISTER_LEVEL;
                    break;
                case yalla.data.ErrorCode.CHAMPIONSHIP_RIGISTER_NOMONEY://金币不足
                    str = yalla.data.TranslationD.Champion_Error_RIGISTER_NOMONEY;
                    break;
                case yalla.data.ErrorCode.CHAMPIONSHIP_MATCH_NORIGISTER://未报名
                    str = yalla.data.TranslationD.Champion_Error_MATCH_NORIGISTER;
                    break;
                case yalla.data.ErrorCode.CHAMPIONSHIP_MATCH_INMATCH://匹配中
                    str = yalla.data.TranslationD.Champion_Error_MATCH_INMATCH;
                    break;
                case yalla.data.ErrorCode.CHAMPIONSHIP_MATCH_INGAME://游戏中
                    str = yalla.data.TranslationD.Champion_Error_MATCH_INGAME;
                    break;
                case yalla.data.ErrorCode.CHAMPIONSHIP_MATCH_UNKNOWN://未知错误
                    // str = "Unkown error";
                    break;
                case yalla.data.ErrorCode.CHAMPIONSHIP_MATCHCANCEL_FAILED://取消失败
                    str = yalla.data.TranslationD.Champion_Error_MATCHCANCEL_FAILED;
                    break;
                case yalla.data.ErrorCode.CHAMPIONSHIP_GETREWARD_NORIGISTER://未报名
                    str = yalla.data.TranslationD.Champion_Error_MATCH_NORIGISTER;
                    break;
                case yalla.data.ErrorCode.CHAMPIONSHIP_GETREWARD_INMATCH://匹配中
                    str = yalla.data.TranslationD.Champion_Error_MATCH_INMATCH;
                    break;
                case yalla.data.ErrorCode.CHAMPIONSHIP_GETREWARD_INGAME://游戏中
                    str = yalla.data.TranslationD.Champion_Error_MATCH_INGAME;
                    break;
                case yalla.data.ErrorCode.CHAMPIONSHIP_GETREWARD_GAMEOVER://已结算
                    str = yalla.data.TranslationD.Champion_Error_GETREWARD_GAMEOVER;
                    break;
                case yalla.data.ErrorCode.CHAMPIONSHIP_RIGISTER_EXIST://已注册报名
                    str = yalla.data.TranslationD.Champion_Error_RIGISTER_EXIST;
                    break;
                case yalla.data.ErrorCode.CHAMPIONSHIP_RIGISTER_WRONGID://注册传入错误的锦标赛id
                    str = yalla.data.TranslationD.Champion_Error_RIGISTER_WRONGID;
                    break;
                case yalla.data.ErrorCode.MATCH_GAME_FAILD_OUTTIME://匹配超时
                    str = yalla.data.TranslationD.Champion_Error_FAILD_OUTTIME;
                    break;
                default:
                    // str = code+'';
                    break;
            }
            if (!btnNameList) btnNameList = [yalla.data.TranslationD.Game_Btn_Confirm];
            if (str.length > 0) yalla.common.Confirm.instance.showConfirm(str, Laya.Handler.create(this, this._callBack), null, btnNameList);
        }

        /**网关登录错误提示 */
        public static showGatewayError(code: number, callBack: Function = null, callBack1: Function = null) {
            this._callBack = callBack;
            switch (code) {
                case GateWayCode.GATEWAY_ERROR:
                case GateWayCode.AUTH_ERROR:
                case GateWayCode.SECURITY_ERROR:
                    //发起断线重连，需要先把socket断掉    
                    yalla.Client.instance.closeSocket();
                    // this.show('Network error, please try again');
                    break;
                case GateWayCode.INVALID_APPLICATION:
                case GateWayCode.COMMAND_OFFLINE:
                case GateWayCode.COMMAND_INFO_DOSE_NOT_EXIST:
                case GateWayCode.COMMAND_NOT_EXIST_IN_APP:
                case GateWayCode.INVALID_COMMAND:
                    //只能返回大厅
                    var str = 'Network error. Please go back to the lobby and try again.';
                    yalla.common.Confirm.instance.showConfirm(str, null, Laya.Handler.create(this, () => {
                        yalla.Native.instance.backHall();
                    }));
                    break;
                case GateWayCode.ILLEGAL_ENTER:
                    //被顶号    
                    yalla.util.sendExitType(yalla.data.ExitType.LOGGED_ANOTHER_QUIT);
                    yalla.Native.instance.alertAccountLoggedView(() => {
                        yalla.Native.instance.backHall(false);
                    });
                    break;
                default:
                    //网络异常，继续游戏内操作 骰子或者出牌操作    
                    var msg = "Network error. Please try again."
                    if (yalla.Font.lan == 'ar') msg = 'خطأ في الشبكة. يرجى المحاولة مرة أخرى.';
                    yalla.Native.instance.showToast({ msg: msg });
                    break;
            }
        }
    }
}