class SnakeMain extends BaseMain {
    private _client;
    private _gameBox: Laya.Box;

    constructor(gameType:number) {
        super();
        Laya.stage.bgColor = "#312445";
        if(this._gameView) this.clear();
        if (!this._gameView) {
            this.initGameBox();
            this._gameView = new yalla.view.game.snake.Game();
            this._gameView.name = 'gameView';
            this._gameBox.addChild(this._gameView);

            if (yalla.util.IsBrowser()) {
                this._client = yalla.ClientBrower.instance;
            } else {
                this._client = yalla.Client.instance;
            }
        }
    }

    private initGameBox(): void{
        this._gameBox = new Laya.Box();
        this._gameBox.size(Laya.stage.width, Laya.stage.height);
        Laya.stage.addChild(this._gameBox);
    }

    /**
     * 切入后台
     */
    public onBlur(): void {
        super.onBlur();
        yalla.Global.game_state = yalla.data.GameState.State_Sleep;
        yalla.Sound.stopAllSound();

        if (this._gameView) {
            this._gameView.onBlur();
            yalla.common.InteractiveGift.Instance.onBlur();
        }
    }

    /**
     * 切到前台
     */
    public onForce(): void {
        super.onForce();
        if (yalla.Global.game_state != yalla.data.GameState.State_Wake && yalla.Global.game_state.length > 0) {
            yalla.Global.game_state = yalla.data.GameState.State_Wake;
            if (this._gameView) this._gameView.onForce();
        }

        //重新心跳时间，发送心跳包
        if(this._client) this._client.resumeHeart();
    }

    /**
     * 登陆游戏
     * @param msg 
     */
    public onLogin(msg: any): void {
        super.onLogin(msg);
        yalla.Debug.log('===snake====onLogin' + (JSON.stringify(msg)));
        if (msg) {
            yalla.data.snake.UserService.instance.clear();

            this.restartGame();
            yalla.data.snake.UserService.instance.user.update(msg);
            if (yalla.util.IsBrowser()) {
                //浏览器走这个逻辑，1.3.1使用原生 则不用主动发起
                yalla.ClientBrower.instance.init(Laya.Handler.create(this, () => {
                    yalla.data.snake.UserService.instance.ticketLogin();
                }));
            } else {
                //现在改为主动调起原生soket连接（因锦标赛）
                Laya.timer.once(yalla.Global.reconnect_interval, this, this.checkConnect);
            }
        }
    }
    private checkConnect() {
        this._client && this._client.checkConnect();
        // yalla.net.snake.NativeClient.instance.checkConnect();
    }

    /**
     * 再来一局匹配成功
     */
    public restartGame(): void {
        super.restartGame();
        yalla.Global.game_state = yalla.data.GameState.State_Playing;
        yalla.data.snake.UserService.instance.register();
    }

    /**
     * onKickedOut
     * 被顶号
     * 音效关闭
     */
    public onKickedOut() {
        super.onKickedOut();
        yalla.Global.IsGameOver = true;
        yalla.Sound.stopAll();
        this.clear(false);
    }
    /**
     * 断线重连
     * socket 断掉
     * game 的event 和 tween停止
     * 执行连接success 逻辑
     */
    public onReconnect(): void {
        if(yalla.Global.IsGameOver) return;
        super.onReconnect();
        Laya.timer.clearAll(this);

        if (this._client) this._client.clear();
        if (yalla.util.IsBrowser()) {
                //浏览器走这个逻辑，1.3.1使用原生 则不用主动发起
                yalla.ClientBrower.instance.init(Laya.Handler.create(this, () => {
                    yalla.data.snake.UserService.instance.ticketLogin();
                }));
            } else {
                yalla.NativeWebSocket.instance.sendNetReConnect();
        }
    }

    /**
     * 重连倒计时结束 手动结束socket连接
     */
    public onReTimeOver() {
        super.onReTimeOver();
        this._client && this._client.clear();
    }

    /**
     * 退出游戏(接受其他玩家请求加入游戏,并结束当前游戏 ps:被动返回大厅)
     * @param msg 
     */
    public onQuitGame(msg: any): void {
        super.onQuitGame(msg);
        yalla.data.snake.UserService.instance.quitRoom();
        // yalla.data.snake.UserService.instance.recordPointQuit(yalla.data.ExitType.JOIN_OTHER_GAME_QUIT);
    }

    public onWebSocketOpen() {
        super.onWebSocketOpen();
        Laya.timer.clear(this, this.checkConnect);
        yalla.Debug.log('======snakeMain.onWebSocketOpen====IsGameOver='+yalla.Global.IsGameOver);
        if(yalla.Global.IsGameOver) return; //TODO::因为蛇棋现在新的网关链接，又因原生把蛇棋和联赛共用一个websocket链接，即便游戏结束 websocket也不会断，这里通过gameOver判定

        yalla.Client.instance.init(Laya.Handler.create(this, () => {
            yalla.data.snake.UserService.instance.ticketLogin();
        }));
        yalla.common.connect.ReconnectControl.instance.connectSucss();
       
    }

    public updateMoney() {
        yalla.data.snake.UserService.instance.user.gold = yalla.Global.Account.currentDiamondNum;
        yalla.data.snake.UserService.instance.user.money = yalla.Global.Account.currentGoldNum;
        yalla.data.snake.UserService.instance.event(yalla.data.snake.EnumCmd.Game_Update_Player_Coin);
    }

    private removeInstance(): void {
        Laya.timer.clear(this, this.checkConnect);
        Laya.timer.clearAll(this);
        yalla.data.snake.UserService.instance.clear();
        if (this._client) {
            this._client.removeEvent();
            this._client.clear();
        }
        yalla.Client.instance.firstLogin = true;
    }

    public backHallClear(remove:boolean = false): void {
        super.backHallClear();
        yalla.Global.game_state = '';
        this.removeInstance();
        yalla.Debug.log('===snakemain.backHallClear====');
        if (!!this._gameView) {
            if (!remove) {
                this._gameView.backHallClear();
            } else {
                this._gameView.clear();
                this._gameView = null;
                if (this._gameBox) {
                    this._gameBox.removeSelf();
                    this._gameBox.destroy(true);
                    this._gameBox = null;
                }
            }
        }
    }

    public clear(removeView: Boolean = true): void {
        this.backHallClear(true);
        super.clear();
    }
}