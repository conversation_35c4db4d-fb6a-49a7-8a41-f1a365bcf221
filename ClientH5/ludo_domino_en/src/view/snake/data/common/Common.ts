namespace yalla.data {

    export const RectWid = 72;
    export const RectHei = 88.5//90;
    export const MapHei = 1032;
    export const Raw = 10;          //行
    export const Col = 10;          //列
    export const MoveTime = 100;    //毫秒
    export const BeginIndex = 0;    //初始位置
    export const Max_Num = 100;     //100格
    export const IsTestCode: boolean = false;
    
    export var Move_Base_Time = 180;//150;      /**单格子移动基础时间 */
    export var Move_Change_Time = 50;//100;     /**单格子移动浮动时间 */
    export var Step_Len = 45;//60;              /**爬梯一步 步长 */
    export var Step_Time = 130;//180;           /** 爬梯一步时间ms */
    export var Snake_Time = 190;//200;          /**每100像素移动需要200ms */
    export var Snake_Time_V = 1.5;//2;          /** 后半程变数系数 */

    export var JumpLadder = 10;        /**爬梯跳动幅度*/
    export var JumpHei = 1;             //60;  平移模式跳跃高度，暂时无用
    export var InitScale = 0.72;        /**起始点 & 棋子相遇同一个格子 缩放比例 */


    /**
     * 游戏中数据配置
     */
    export class SnakeGameConfig {
        public static Add_Exp = 5;                //结算增加经验
        public static Cost_Money = 500;           //进入房间消耗
        public static Ready_Max_Time = 600;       //匹配最大等候时间10分钟
        public static Dice_CD = 10000;              //掷骰子cd时间10s
        public static Face_List = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];//默认头像
        
        public static DiceTime: number = 400;//骰子转动时间
        public static WaitTime: number = 5000;//10000;//等待时间
        public static ResetTime: number = 2000;//重置时间

        public static Max_Player_Nums = 2;      //两人局
        public static Player_Pos = [{ "left": 4, "bottom": -30 }, { "right": 4, "bottom": -30 }];
    }

    // export const SnakeMapList = {
    //         100:[0,0,0],99:[0,1,-1],98:[0,2,-1],97:[0,3,-1],96:[0,4,-1],95:[0,5,-1],94:[0,6,-1],93:[0,7,-1],92:[0,8,-1],91:[0,9,-1],
    //         81:[1,0,1],82:[1,1,1],83:[1,2,1],84:[1,3,1],85:[1,4,1],86:[1,5,1],87:[1,6,1],88:[1,7,1],89:[1,8,1],90:[1,9,2],
    //         80:[2,0,2],79:[2,1,-1],78:[2,2,-1],77:[2,3,-1],76:[2,4,-1],75:[2,5,-1],74:[2,6,-1],73:[2,7,-1],72:[2,8,-1],71:[2,9,-1],
    //         61:[3,0,1],62:[3,1,1],63:[3,2,1],64:[3,3,1],65:[3,4,1],66:[3,5,1],67:[3,6,1],68:[3,7,1],69:[3,8,1],70:[3,9,2],
    //         60:[4,0,2],59:[4,1,-1],58:[4,2,-1],57:[4,3,-1],56:[4,4,-1],55:[4,5,-1],54:[4,6,-1],53:[4,7,-1],52:[4,8,-1],51:[4,9,-1],
    //         41:[5,0,1],42:[5,1,1],43:[5,2,1],44:[5,3,1],45:[5,4,1],46:[5,5,1],47:[5,6,1],48:[5,7,1],49:[5,8,1],50:[5,9,2],
    //         40:[6,0,2],39:[6,1,-1],38:[6,2,-1],37:[6,3,-1],36:[6,4,-1],35:[6,5,-1],34:[6,6,-1],33:[6,7,-1],32:[6,8,-1],31:[6,9,-1],
    //         21:[7,0,1],22:[7,1,1],23:[7,2,1],24:[7,3,1],25:[7,4,1],26:[7,5,1],27:[7,6,1],28:[7,7,1],29:[7,8,1],30:[7,9,2],
    //         20:[8,0,2],19:[8,1,-1],18:[8,2,-1],17:[8,3,-1],16:[8,4,-1],15:[8,5,-1],14:[8,6,-1],13:[8,7,-1],12:[8,8,-1],11:[8,9,-1],
    //         1:[9,0,1],2:[9,1,1],3:[9,2,1],4:[9,3,1],5:[9,4,1],6:[9,5,1],7:[9,6,1],8:[9,7,1],9:[9,8,1],10:[9,9,2]
    // }
}