namespace yalla.data.snake {

	export class EnumCustomizeCmd {
		static Snake_RoundEnd = 'Snake_RoundEnd';	//每一轮跑结束
		static Snake_PlayMsg = "Snake_PlayMsg";     //消息播放
		static Exit_Back = 'ExitBack';			//返回上一层view
		static Server_Closed = 'Server_Closed';		//服务器断联
		static Server_Error = 'Server_Error';		//socket连接失败
		static Game_JoinAgora = 'Game_JoinAgora';	//加入语音聊天房间
		static Game_Chat_My = 'Game_70_My';        	 			//聊天
		static Game_ChatOFF_My = 'Game_ChatOFF_My';				//设置屏蔽聊天
	}
	export class EnumCmd {
		// 主命令
		static Game_Login = 'Game_10';            	//登录成功相应
		static Game_TicketLogin = 'Game_11';          //票据登陆
		static Game_GameDataInfo = 'Game_12';		//游戏相关信息 地图
		static Game_EnterRoom = 'Game_20';         	//进入房间
		static Game_QuitRoom = 'Game_21';         	//推出房间
		static Game_GameEvent = 'Game_31';        	//游戏操作响应
		static Game_GAME_OPERATE = 'Game_30';			//游戏操作
		static Game_GameResult = 'Game_49';		//游戏用户状态广播
		static Game_OutGame = 'Game_126';        		//顶号
		static Game_Update_Player_Level = 'Game_81';	//玩家升级
		static Game_Chat = 'Game_70';        	 		//聊天
		static Game_GetAgoraToken = 'Game_90';		//获取语音token
		static Game_GetZegoToken = 'Game_92';		//获取语音token
		static Game_Agora_Operate = 'Snake_91'; 		//进入语音聊天房间
		static Game_Update_Player_Coin = 'Game_80';				//刷新用户金币钻石
		static Game_Gift_Cnf_List = 'Game_111';					//获取游戏内互动礼物配置列表 1.3.1 add
		static Game_Gift_Send = 'Game_112';						//游戏内互动礼物赠送, 1.3.1 add
	}

	//游戏命令
	export enum Command {
		PLAYER_LOGIN = 10,//登陆
		TICKET_LOGIN = 11,//票据登陆
		GAME_DATA_INFO = 12,//游戏相关信息
		QUICK_START = 20,//快速开始
		QUIT_ROOM = 21,//退出房间
		JOIN_ROOM = 22,//加入房间

		GAME_OPERATE = 30,//游戏操作
		GAME_EVENT = 31,//游戏操作
		GAMESTATUS = 40,//游戏状态

		GAME_PLAYER_COLOR_SELECT = 41,//颜色选择
		GAME_MAP_CHESS_MOVE = 46,//走棋子
		GAMERESULT = 49,//游戏结果
		GAMEPLAYERSTATUS = 50,//玩家状态 undo 投骰子 移动棋子开始结束
		PLAYER_ENTER = 60,//玩家进入

		PLAYER_CHAT_ALL = 70,//聊天
		UPDATA_PLAYER_COIN = 80,//刷新用户金币钻石
		UPDATA_PLAYER_LEVEL = 81,//玩家升级
		FLUSH_CURRENCY = 82,//刷新货币 参考domino

		GET_AGORA_TOKEN = 90,//获取语音频道token
		AGORA_OPERATE = 91,//语音频道操作
		GET_ZEGO_TOKEN = 92,
		FRIEND_ADD_MSG = 100,//添加好友
		FRIEND_LIST = 101,//好友列表
		FRIEND_INVITE = 102,//邀请好友

		GAME_GIFT_CNF_LIST = 111, // 获取游戏内互动礼物配置列表 1.3.1 add
		GAME_GIFT_SEND = 112, // 游戏内互动礼物赠送, 1.3.1 add


		OUT_GAME = 126,//踢出服务
		HEART = 127,//心跳包
	}
	export enum GameType {
		SNAKEANDLADER = 10020,//蛇棋
		DOMINO = 10021,//多米诺
		SHEEP = 10022,//顶
	}

	export enum PlayerColor {
		RED = 0,
		YELLOW = 2,
		BLUE = 3,
		GREEN = 1,
		ORANGE = 4,
	}

	export enum ErrorCode {
		NONE_ERROE = 1,//无错误
		LOGIN_FAILD_PW = 101,//密码错误
		LOGIN_FAILD_ID = 102,//登陆ID错误
		LOGIN_FAILD_OLDSIGN = 103,//重复的签名
		LOGIN_FAILD_SIGN = 104,//签名错误
		LOGIN_FAILD_TIME = 105,//客户端时间错误
		QUICKSTART_FAILD_NOROOM = 203,//房间已满
		ENTER_ROOM_FAILD_NOROOM = 204,//房间已满
		ENTER_ROOM_FAILD_ROOMID = 205,//错误的房间号
		GAMEOPERATE_FAILD_SIT_NOSTOOL = 301,// 已有人
		GAMEOPERATE_FAILD_SIT_WRONGSTOOLNUM = 302,//错误的凳子ID
		GAMEOPERATE_FAILD_SIT_WRONGTIME = 303,//错误的操作时间
		NO_MONEY = 401,//余额不足
		NO_DIAMOND = 402,//钻石不足
		MAX_LIMIT = 601,//超出限制
		JOIN_ROOM_FAILD_NOROOM = 701,//无此房间
		JOIN_ROOM_FAILD_MAX = 702,//房间人数已满
	}
	//游戏状态
	export enum GameStatus {
		GAME_PREPARE = 0,//开始报名
		GAME_START = 1,//游戏开始
		GAME_ING = 2,//游戏中
		GAME_RESULT = 3,//游戏结果
		GAME_END = 4,//游戏结束
	}
	//操作结果
	export enum OperateResult {
		FAILED = 0,//失败
		SUCCESS = 1,//成功
	}

	//游戏操作
	export enum GameOperate {
		SIT = 0,//入座
		UP = 1,//离开座位
		SIGN_UP = 2,//准备
		SIGN_CANCEL = 3,//取消准备
		THROW = 4,//掷骰子
		RESET_THROW = 5,//重置投掷
		CHOOSE_CHESS = 6,//选择棋子
		CHESS_MOVE = 7,//棋子移动
		SYSTEM_TRUST = 8,//系统托管
		SYSTEM_TRUST_CANCEL = 9,//取消系统托管
		CHAT_OFF = 23,//屏蔽聊天
		CHAT_OFF_CANCEL = 24,//取消屏蔽聊天
	}

	export enum GameEvent {
		GAME_STATE_CHANGE = 1,//游戏状态改变
		GAME_COLOR_SELECT = 2,//玩家颜色选择
		GAME_PLAYER_STATUS_CHANGE = 3,//用户状态改变
		GAME_PLAYER_THROW = 4,//玩家掷骰子
		GAME_SHOW_RESULT = 5,//游戏结果
		SAL_CHESS_MOVE = 7,//蛇棋棋子移动
		SAL_CHESS_MOVE_SNAKE = 8,//蛇棋🐍移动
		SAL_CHESS_MOVE_LADDER = 9,//蛇棋梯子移动
		SAL_CHESS_BORN = 10,//蛇棋棋子移动到初始点
	}

	export enum EnumPlayerTurnStatus {
		THROW_START = 0,				//开始投掷
		THROW_END = 1,					//投掷结束
		RETHROW_START = 2,				//是否重置开始
		RETHROW_END = 3,				//是否重置结束
		CHESSMOVE_START = 6,			//棋子移动开始
		CHESSMOVE_END = 7,			//棋子移动结束
	}

	export enum ChessEvent {
		BORN = 1,
		MOVE = 2,
		SNAKE = 3,
		LADDER = 4,
	}
}