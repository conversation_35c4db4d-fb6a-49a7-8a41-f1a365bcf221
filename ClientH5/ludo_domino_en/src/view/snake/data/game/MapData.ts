module yalla.data.snake {
    export class MapData {
        // public groupHash: any = {
        //     "1000_1x3": {
        //         "1": [[19.6, 4.8], [29.6, 13.2], [43.2, 23.6], [50, 33.6], [47.6, 46.4], [35.2, 57.2], [23.6, 65.2], [16.4, 77.6], [14, 89.2], [17.2, 102.8], [22.8, 117.6], [36, 125.2], [44, 139.6], [40.8, 150.4], [24.4, 163.6], [18.8, 177.2], [22, 187.2], [33.6, 189.6]],
        //         "-1": [[19.6, 4.8], [29.6, 13.2], [43.2, 23.6], [50, 33.6], [47.6, 46.4], [35.2, 57.2], [23.6, 65.2], [16.4, 77.6], [14, 89.2], [17.2, 102.8], [22.8, 117.6], [36, 125.2], [44, 139.6], [40.8, 150.4], [24.4, 163.6], [18.8, 177.2], [22, 187.2], [33.6, 189.6]]
        //     },
        //     "1000_3x3": {
        //         "1": [[23, 13], [37, 18], [48, 28], [55, 41], [56, 56], [53, 71], [51, 87], [56, 101], [65, 111], [77, 117], [91, 119], [106, 117], [121, 112], [137, 108], [153, 107], [167, 111], [179, 119], [186, 131], [189, 131], [189, 145], [188, 160], [183, 172], [173, 184]],
        //         "-1": [[23, 13], [37, 18], [48, 28], [55, 41], [56, 56], [53, 71], [51, 87], [56, 101], [65, 111], [77, 117], [91, 119], [106, 117], [121, 112], [137, 108], [153, 107], [167, 111], [179, 119], [186, 131], [189, 131], [189, 145], [188, 160], [183, 172], [173, 184]]
        //     },
        //     "1000_3x5": {
        //         "1": [[16, 19], [19, 31], [24, 42], [31, 54], [43, 63], [56, 73], [67, 86], [73, 103], [75, 123], [73, 143], [68, 163], [63, 186], [59, 209], [58, 232], [62, 255], [69, 274], [79, 291], [95, 304], [116, 305], [136, 301], [156, 301], [173, 307], [188, 317], [198, 331], [202, 350], [199, 368], [189, 379], [173, 383]],
        //         "-1": [[196, 22], [190.4, 33.6], [186, 49.2], [173.2, 58], [167.2, 66.4], [149.2, 81.6], [144.4, 96.4], [137.2, 112.8], [137.2, 134.8], [143.6, 158.8], [142.8, 180], [154.4, 203.6], [150, 229.6], [147.6, 252.8], [145.2, 270], [133.6, 286.8], [118.4, 298.4], [83.19999999999999, 305.6], [54.400000000000006, 306.4], [29.19999999999999, 314], [14.400000000000006, 328.8], [10.400000000000006, 350], [10.400000000000006, 367.2], [20, 380]]
        //     },
        //     "2000_1x5": {
        //         "1":[[36,386],[37,56]]
        //     },
        //     "2000_2x2": {
        //         "1": [[105,126],[41,48]],
        //         "-1":[[38,128],[105,47]]
        //     }
        // };
        public groupHash: any = {
            "1000_1x2": {
                "1": [[38,46],[48,64],[46,76],[21,107],[13,131],[13,146],[22,160],[44,160],[55,147],[52,134]],
                "-1": [[35,44],[25,64],[28,76],[59,124],[61,140],[54,156],[42,163],[23,154],[19,135],[24,128]]
            },
            "1000_1x6": {
                "1": [[24,56],[19,68],[26,97],[52,131],[55,150],[25,233],[21,250],[50,314],[52,332],[47,347],[22,393],[24,407],[58,457],[60,473]],
                "-1": [[51,56],[56,71],[39,107],[19,140],[21,166],[53,244],[53,260],[26,309],[23,330],[27,350],[51,393],[51,409],[15,455],[12,476]]
            },
            "1000_2x3": {
                "1": [[34,46],[56,34],[79,36],[96,51],[90,78],[53,112],[46,130],[57,150],[104,147],[107,164],[82,202],[86,221]],
                "-1": [[109,43],[87,34],[64,36],[49,56],[55,78],[99,115],[100,135],[87,149],[45,146],[38,160],[62,205],[55,224]]
            },
            "1000_2x5": {
                "1": [[49,43],[79,39],[96,54],[96,75],[82,92],[48,116],[35,150],[52,179],[105,212],[107,235],[96,257],[48,295],[41,316],[56,347],[104,368],[118,382]],
                "-1": [[93,40],[63,40],[49,58],[52,76],[68,95],[105,128],[109,152],[98,176],[45,205],[35,228],[48,254],[93,288],[103,307],[99,328],[74,355],[36,370],[28,384]]
            },
            "1000_2x7": {
                "1": [[35,46],[23,77],[26,105],[36,124],[85,161],[100,191],[98,222],[84,252],[43,315],[42,343],[56,378],[101,431],[103,465],[65,536],[66,564],[86,576]],
                "-1": [[113,51],[122,80],[115,116],[102,130],[58,166],[46,198],[51,230],[104,315],[104,343],[89,378],[48,426],[43,454],[81,535],[82,563],[60,576]]
            },
            "1000_1x4": {
                "1": [[51,33],[23,43],[24,73],[52,112],[21,173],[46,236],[19,291],[26,304]],
                "-1": [[22,34],[48,43],[47,75],[22,114],[50,169],[26,238],[53,291],[46,304]]
            },
            "1000_3x2": {
                "1": [[22,51],[20,74],[34,98],[61,99],[108,65],[128,65],[142,80],[140,113],[149,130],[162,135]],
                "-1": [[193,50],[199,66],[196,83],[176,101],[154,99],[113,70],[93,64],[75,81],[78,110],[68,128],[52,135]]
            },
            "1000_3x4": {
                "1": [[40,63],[29,109],[49,153],[64,160],[124,182],[134,198],[116,263],[127,292],[154,308]],
                "-1": [[176,64],[185,114],[165,153],[149,160],[94,180],[83,201],[99,263],[87,291],[60,310]]
            },
            "1000_3x6": {
                "1": [[50,51],[76,46],[121,62],[144,102],[128,154],[68,210],[63,230],[76,256],[145,311],[149,334],[112,419],[113,438],[133,471],[151,480]],
                "-1": [[169,50],[133,47],[93,65],[73,107],[100,162],[153,216],[152,238],[134,260],[73,306],[65,336],[100,411],[104,432],[81,472],[64,478]]
            },
            "1000_3x8": {
                "1": [[54,38],[110,63],[136,114],[71,245],[67,283],[86,321],[157,384],[160,434],[115,501],[100,559],[117,615],[150,648]],
                "-1": [[161,39],[107,59],[81,113],[147,246],[152,281],[137,316],[59,381],[56,425],[116,534],[117,586],[89,631],[57,655]]
            },
            "1000_4x3": {
                "1": [[37, 53], [50, 91], [76, 103], [139, 93], [166, 98], [181, 118], [180, 189], [193, 210], [215, 222], [231, 224]],
                "-1":[[251,50],[237,94],[213,103],[144,91],[119,101],[107,121],[109,182],[101,204],[76,222],[57,224]]
            },
            "1000_4x5": {
                "1": [[52,41],[85,47],[104,72],[104,90],[73,160],[77,186],[96,203],[173,232],[186,258],[179,336],[204,384],[220,394],[236,398]],
                "-1": [[237,42],[200,50],[184,86],[189,99],[217,168],[210,187],[193,203],[112,238],[101,264],[108,335],[84,384],[67,394],[48,397]]
            },
            "1000_4x7": {
                "1": [[33,57],[27,115],[35,139],[57,159],[152,195],[175,225],[184,255],[142,375],[145,403],[164,427],[244,502],[256,531],[258,554]],
                "-1": [[253,59],[262,115],[251,142],[232,158],[139,192],[113,225],[107,266],[145,365],[144,391],[132,419],[48,496],[32,526],[31,551]]
            },
            "1000_5x4": {
                "1": [[44,62],[84,50],[116,69],[126,94],[113,170],[125,198],[153,218],[187,224],[268,215],[290,228],[304,283],[311,298]],
                "-1": [[309,60],[271,53],[238,74],[232,98],[245,166],[236,196],[211,217],[176,223],[100,214],[75,224],[55,286],[48,300]]
            },
            "1000_5x6": {
                "1": [[50,48],[93,75],[97,96],[83,178],[92,200],[116,210],[214,219],[244,244],[258,283],[234,402],[250,447],[304,476]],
                "-1": [[309,49],[265,78],[263,95],[277,171],[273,195],[251,209],[148,220],[114,249],[105,286],[125,405],[106,450],[57,477]]
            },
            "1000_5x9": {
                "1": [[53,49],[141,88],[167,148],[136,316],[158,392],[249,469],[269,523],[237,639],[243,692],[267,726],[303,744]],
                "-1": [[306,50],[215,91],[194,140],[226,333],[215,377],[181,412],[111,470],[95,513],[99,570],[127,659],[105,711],[82,735],[55,745]]
            },
            "1000_6x7": {
                "1": [[38,60],[38,161],[65,206],[121,220],[241,193],[299,215],[315,266],[296,322],[236,430],[244,470],[276,500],[366,505],[395,526],[403,550]],
                "-1": [[395,70],[384,178],[355,210],[314,218],[189,196],[142,210],[118,260],[197,418],[197,462],[170,495],[77,506],[48,516],[34,545]]
            },
            "1000_7x6": {
                "1": [[35,66],[59,41],[120,50],[158,97],[158,135],[112,247],[126,295],[171,319],[363,308],[409,329],[424,375],[424,463],[441,482]],
                "-1": [[469,70],[450,42],[374,54],[344,124],[357,148],[395,258],[373,297],[331,324],[155,306],[115,318],[90,351],[83,457],[72,481]]
            },

            "2000_2x2": { "1": [[105, 127], [41, 47]], "-1": [[38, 127], [102, 47]] },
            "2000_2x6": { "1": [[107, 475], [37, 52]], "-1": [[36, 475], [106, 52]] },
            "2000_3x5": { "1": [[173, 379], [43, 57]], "-1": [[42, 379], [172, 57]] },
            "2000_4x2": { "1": [[238, 127], [49, 51]], "-1": [[49, 127], [238, 51]] },
            "2000_4x6": { "1": [[241, 460], [44, 57]], "-1": [[46, 460], [243, 57]] },
            "2000_5x5": { "1": [[311, 383], [42, 50]], "-1": [[48, 383], [317, 50]] },
            "2000_7x7": { "1": [[457, 557], [42, 52]], "-1": [[46, 557], [461, 52]] },
            
            "2000_1x3": { "1": [[37, 213], [37, 49]],"-1":[[37,213],[37,49]]},
            "2000_2x4": { "1": [[105, 293], [39, 57]], "-1": [[38, 293], [104, 57]] },
            "2000_3x3": { "1": [[176, 211], [44, 55]], "-1": [[39, 211], [171, 55]] },
            "2000_3x7": { "1": [[180, 565], [38, 53]], "-1": [[35, 565], [177, 53]] },
            "2000_4x4": { "1": [[244, 296], [35, 44]], "-1": [[43, 296], [252, 44]] },
            "2000_5x3": { "1": [[312, 212], [50, 51]], "-1": [[47, 212], [309, 51]] },
            "2000_5x9": { "1": [[316, 738], [36, 52]], "-1": [[44, 736], [320, 54]] },
            "2000_10x7": { "1": [[672, 562], [54, 60]], "-1": [[47, 562],[665, 60]] },
            "2000_1x5": {"1":[[37,384],[37,58]],"-1":[[37,384],[37,58]]}
        };
        /** 生成地图列表数据
         *100:[0,0,0],99:[0,1,-1],98:[0,2,-1],97:[0,3,-1],96:[0,4,-1],95:[0,5,-1],94:[0,6,-1],93:[0,7,-1],92:[0,8,-1],91:[0,9,-1],
            81:[1,0,1],82:[1,1,1],83:[1,2,1],84:[1,3,1],85:[1,4,1],86:[1,5,1],87:[1,6,1],88:[1,7,1],89:[1,8,1],90:[1,9,2],
            80:[2,0,2],79:[2,1,-1],78:[2,2,-1],77:[2,3,-1],76:[2,4,-1],75:[2,5,-1],74:[2,6,-1],73:[2,7,-1],72:[2,8,-1],71:[2,9,-1],
            61:[3,0,1],62:[3,1,1],63:[3,2,1],64:[3,3,1],65:[3,4,1],66:[3,5,1],67:[3,6,1],68:[3,7,1],69:[3,8,1],70:[3,9,2],
            60:[4,0,2],59:[4,1,-1],58:[4,2,-1],57:[4,3,-1],56:[4,4,-1],55:[4,5,-1],54:[4,6,-1],53:[4,7,-1],52:[4,8,-1],51:[4,9,-1],
            41:[5,0,1],42:[5,1,1],43:[5,2,1],44:[5,3,1],45:[5,4,1],46:[5,5,1],47:[5,6,1],48:[5,7,1],49:[5,8,1],50:[5,9,2],
            40:[6,0,2],39:[6,1,-1],38:[6,2,-1],37:[6,3,-1],36:[6,4,-1],35:[6,5,-1],34:[6,6,-1],33:[6,7,-1],32:[6,8,-1],31:[6,9,-1],
            21:[7,0,1],22:[7,1,1],23:[7,2,1],24:[7,3,1],25:[7,4,1],26:[7,5,1],27:[7,6,1],28:[7,7,1],29:[7,8,1],30:[7,9,2],
            20:[8,0,2],19:[8,1,-1],18:[8,2,-1],17:[8,3,-1],16:[8,4,-1],15:[8,5,-1],14:[8,6,-1],13:[8,7,-1],12:[8,8,-1],11:[8,9,-1],
            1:[9,0,1],2:[9,1,1],3:[9,2,1],4:[9,3,1],5:[9,4,1],6:[9,5,1],7:[9,6,1],8:[9,7,1],9:[9,8,1],10:[9,9,2]
        */
        public elementList = null;
        public endPos: number = 100;
        public row: number;
        public col: number;
        public snakeMapList: any = null;
        public generateMapList(row: number, col: number) {
            this.row = row;
            this.col = col;
            var totalGrids = row * col;
            this.endPos = totalGrids;
            var gridCount = 0;
            var mapHash = {};
            var i, j;
            for (i = 0; i < row; i++) {
                var side = (i % 2 == 0 ? 1 : -1);
                var jIndex = 0;
                for (j = 0; j < col; j++) {
                    gridCount += 1;
                    if (side == 1) jIndex = j;
                    else jIndex = col - j - 1;
                    if (gridCount == totalGrids) mapHash[gridCount] = [row - i - 1, jIndex, 0];
                    else {
                        if (j == col - 1) mapHash[gridCount] = [row - i - 1, jIndex, 2];//拐点 从下往上
                        else mapHash[gridCount] = [row - i - 1, jIndex, side];
                    }
                }
            }
            this.snakeMapList = mapHash;
            // console.log(this.snakeMapList);
            return mapHash;
        }

        /**地图数据  格子id 1-100
         *sceneId：地图id
            curPos:当前位置
            mapSkinId:地图皮肤id
            row:行
            col:列
            mapList:[
             {
                gId:蛇/梯子组合编号 id（目前 23 + 13种）
                skinId:蛇/梯子皮肤id
                type:蛇1 / 梯子2
                list:轨迹列表
                head:蛇头  梯头所在 格子id
                end:蛇尾   梯尾所在 格子id
                pos: 蛇/梯子 在地图中位置 的格子id
                scaleX:蛇或者梯子是否翻转 -1
             }
            ]
            
         * {sceneId:[{gId:1, skinId:1_1, type:1, list:[[x,y],[x,y]], head:99, end:65, pos:99, scaleX:-1}]}
         * 先用默认组合资源，如果蛇/梯子资源加载失败，则用默认组合资源和默认轨迹
         *
         *{sceneId,curPos,mapSkinId, row,col, mapList:[{gId,skinId,type,list:[[]],head,end,pos,scaleX}]}  
         */
        public mapJsonData: Array<MapInfo> = null;
        public initMapJsonData(msg: any) {
            if (msg && msg.mapInfo) {
                this.generateMapList(msg.row, msg.col);
                // var mapList: Array<mapElement> = msg.mapInfo;//JSON.parse(msg.mapInfo);
                // mapList.forEach(element => {
                //     var sX = element.scaleX;
                //     var pos = this.getPos(element.pos);
                //     var list:Array<any> = element.list;//JSON.parse(element.list);
                //     var realList = [];
                //     list.forEach(d => {
                //         realList.push([d[0]*sX + pos[0], d[1] + pos[1]]);    
                //     });
                //     element.realList = realList;                 
                // });
                // this.mapJsonData = mapList;
                this.mapJsonData = msg.mapInfo;
            }
            // console.log('======initMapJsonData========');
            // console.log(this.mapJsonData);
        }

        /**设置轨迹 */
        public setMapTrace(element: MapInfo, x, y, scaleX = 1) {
            var normalList = this.getListByGroupId(element.gId, scaleX);
            if (normalList) {
                //把默认路径复制给element
                var nList = [];
                normalList.forEach(d => {
                    nList.push([d[0] + x, d[1] + y]);
                });
                element.nList = nList;
            }
            // console.log(element.gId,'====setMapTrace===', element);
            var list: Array<any>;
            if (typeof (element.trace) === 'string') {
                var traceList = JSON.parse(element.trace);
                if (typeof (traceList) == 'object') {
                    console.log('====setMapTrace 0=====',);
                    if(traceList[String(scaleX)]) list = traceList[String(scaleX)];
                    // traceList.forEach(tItem => {
                    //     if (tItem.scaleX == scaleX) {
                    //         list = tItem.list;
                    //     }
                    // });
                }
            }
            else list = element.trace;
            var realList = [];
            if (list) {
                console.log('======setMapTrace 1========',list);
                list.forEach(d => {
                    realList.push([d[0] + x, d[1] + y]);
                });
                element.realList = realList;
            }
        }

        /**
         * 根据groupId 获取默认的路径
         * @param gId 
         */
        public getListByGroupId(gId, scaleX = 1) {
            if (!scaleX) scaleX = 1;
            console.log(gId, scaleX,'====getListByGroupId====',this.groupHash);
            if (this.groupHash) {
                if (!this.groupHash[gId]) yalla.Debug.log(`组合${gId}不存在`);
                else return this.groupHash[gId][scaleX];
            }
            return null;
        }

        /**获取格子在地图的位置 */
        public getPos(id) {
            var recWid = yalla.data.RectWid;
            var recHei = yalla.data.RectHei;
            var xx = this.snakeMapList[id][1] * recWid;
            var yy = this.snakeMapList[id][0] * recHei;
            return [xx, yy];
        }

        /** 地图上蛇或者梯子加载成功，则记录 */
        public addLoadEle(skinId: number) {
            if (!this.elementList) this.elementList = [];
            var index = -1;
            if (skinId > 0 && this.elementList.indexOf(skinId) < 0) {
                this.elementList.push(skinId);
            }
        }

        public isLoadEle(skinId): boolean {
            if (this.elementList) return this.elementList.indexOf(skinId) > -1;
            return false;
        }

        public getSide(posIndex:number): number{
            var v = 1;
            if (posIndex > 0) {
                var row = Math.ceil(posIndex / this.col);
                if (row % 2 == 0) v = -1;
                else v = 1;
            }
            return v;
        }

        public clear() {
            this.elementList = null;
            this.mapJsonData = null;
            this.snakeMapList = null;
        }
    }
}