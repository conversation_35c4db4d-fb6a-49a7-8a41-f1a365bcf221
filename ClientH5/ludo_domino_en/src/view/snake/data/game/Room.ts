
module yalla.data.snake {

    export class Room extends yalla.data.DataItem {
        public roomid: number = 0;
        public sceneId: number;                              //房间当前地图id
        public token: string;
        public gameType: number = 0;                        //玩法类型 (0积分场  1轮次场)
        public showId: number = 0;                          //房间号 再来一局传入showid，显示房间id也是showid
        public watchIdx: number = 0;
        public cost: number;
        public leftDiamond: number = 0;                      //剩余钻石
        public player: RoomInfoPlayerInfo;                  //用户列表
        public activited_id: number = 0;                    //当房间里当前激活的玩家id
        public colorList: Array<any>;                        //房间内玩家颜色分配{idx,color}
        public isPrivate: number = 0;                       //0公开  1私密  3锦标赛 4vip房间
        /**是否托管 */
        public isTrust: boolean = false;
        /**入座玩家 */
        public sitPlayers: Array<playerShowInfo> = [];
        /**观战玩家 */
        public audiencePlayers: Array<playerShowInfo> = [];
        /**玩家颜色&棋子 */
        public pcolor: Array<playerShowInfo> = [];
        /**地图信息 */
        public gameMapInfo: GameMapInfo;
        /**棋子信息 {idx,curPos} */
        public chessData: any;
        public quitPunishTime: number = 60;
        public gameBeginTime: number = 0;
        public throwDiceWaitTime: number = 5000;//用户投掷等待时间

        constructor() {
            super();
        }

        public initData(msg: any) {
            this.update(msg);
            this.players = msg.player.player;
            this.showId = msg.showRoomId;
        }

        /** 进入游戏获取玩家数据*/
        set players(val: Array<playerShowInfo>) {
            this.sitPlayers = [];
            this.audiencePlayers = [];
            val.forEach((pInfo, index) => {
                // pInfo.roylevel = 4;
                if (pInfo.sitNum >= 0) {
                    this.pushSitPlayers(pInfo);
                } else {
                    this.pushAudiencePlayers(pInfo);
                }
            })
            //自己的决定信息放前面
            var myIdxIndex = -1;
            this.sitPlayers.forEach((value: playerShowInfo, index: number, arr: Array<playerShowInfo>) => {
                if (value.fPlayerInfo.idx == yalla.Global.Account.idx) {
                    myIdxIndex = index;
                    return true;
                }
            });
            if (myIdxIndex > 0) {
                var list1 = this.sitPlayers.slice(0, myIdxIndex);
                var list2 = this.sitPlayers.slice(myIdxIndex);
                this.sitPlayers = list2.concat(list1);
            }
            // this.sitPlayers.sort((a: playerShowInfo, b: playerShowInfo) => {
            //     if (a.sitNum > b.sitNum) {
            //         return 1;
            //     } else if (a.sitNum < b.sitNum) {
            //         return -1;
            //     } else {
            //         return 0;
            //     }
            // })
        }
        get players(): Array<playerShowInfo> {
            return this.sitPlayers.concat(this.audiencePlayers);
        }

        /** 后续进入的玩家 */
        public pushPlayer(pInfo: playerShowInfo) {
            let have: boolean = false;
            for (var i = 0; i < this.players.length; i++) {
                var info = this.players[i];
                if (info && info.fPlayerInfo.idx == pInfo.fPlayerInfo.idx) {
                    have = true;
                    break;
                }
            }
            if (!have) {
                if (pInfo.sitNum >= 0) {
                    this.pushSitPlayers(pInfo);
                } else {
                    this.pushAudiencePlayers(pInfo);
                }
            }
        }
        private pushAudiencePlayers(pInfo: playerShowInfo) {
            this.audiencePlayers.push(pInfo);
            this.mutePlayer(pInfo.fPlayerInfo.idx);
        }
        private pushSitPlayers(pInfo: playerShowInfo) {
            this.sitPlayers.push(pInfo);
        }

        private mutePlayer(idx: number) {
            var voice = yalla.Voice.instance;
            voice.joinSuccess && !voice.muted(idx) && voice.muteRemoteAudioStream(idx, true, null);
        }

        public getPlayerByIdx(idx: number): playerShowInfo {
            let result: playerShowInfo;
            this.player.player.forEach((value: playerShowInfo, index: number, arr: Array<playerShowInfo>) => {
                if (value.fPlayerInfo.idx == idx) {
                    result = value;
                    return true;
                }
            });
            return result;
        }

        /**
         * 获取房间中的其他玩家
         * @param idx 
         */
        public getOtherPlayer(idx: number): Array<playerShowInfo> {
            let result: Array<playerShowInfo> = [];
            this.player.player.forEach((value: playerShowInfo, index: number, arr: Array<playerShowInfo>) => {
                if (value.fPlayerInfo.idx != idx) {
                    result.push(value);
                    return true;
                }
            });
            return result;
        }

        /**
         * 重新构造每局结果数据列表
         */
        public updateRoundResult(losersInfo: Array<PlayerDomino>, propType: string): Array<DefineRoundResult> {
            if (!losersInfo || losersInfo.length < 1) return [];

            var arr = [];
            var len = losersInfo.length;
            for (var i = 0; i < len; i++) {
                var item = losersInfo[i];
                var d = {
                    idx: item.playerId, intergral: item.playerIntergral, nickName: item.playerNickName,
                    roundIntergral: item.playerRoundIntergral, exp: item.exp
                };
                arr.push(d);
            }

            arr.sort((a: DefineRoundResult, b: DefineRoundResult) => {
                if (a[propType] < b[propType]) {
                    return 1;
                } else if (a[propType] > b[propType]) {
                    return -1;
                } else {
                    return 0;
                }
            });

            return arr;
        }

        public updateColor(colors: Array<any>) {
            this.colorList = colors;
            var sitPlayers: Array<playerShowInfo> = this.sitPlayers;
            sitPlayers.forEach(info => {
                info.color = this.getColorByIdx(info.fPlayerInfo.idx).color;
            })
        }

        /**
         * 根据idx 获取房间内玩家颜色
         * @param idx 
         */
        public getColorByIdx(idx: number): playerShowInfo {
            let result: any;
            this.player.player.forEach((value: playerShowInfo, index: number, arr: Array<playerShowInfo>) => {
                if (value.fPlayerInfo.idx == idx) {
                    result = value;
                    return true;
                }
            });
            return result;
        }

        /**
         * 根据color 获取玩家颜色
         * @param idx 
         */
        public getPlayerByColor(color: number): playerShowInfo {
            let result: any;
            this.player.player.forEach((value: playerShowInfo, index: number, arr: Array<playerShowInfo>) => {
                if (value.color == color) {
                    result = value;
                    return true;
                }
            });
            return result;
        }

        /**
         * 根据idx获取角色座位
         */
        public getSideByIdx(idx: number): number {
            var side = 0;
            if (!this.player || !this.player.player) return side;
            if (this.player.player.length == 2 && idx != yalla.Global.Account.idx) side = 1;
            else {
                this.player.player.forEach((value: playerShowInfo, index: number, arr: Array<playerShowInfo>) => {
                    if (value.fPlayerInfo.idx == idx) {
                        side = index;
                        return true;
                    }
                });
            }
            return side;
        }

        /**
         * 更新coin
         * @param d 
         */
        public addCoin(d: any): void {
            if (!d) return;
            if (d.diamond > 0) {
                this.leftDiamond += parseInt(d.diamond);
            }
        }

        public sortWinPlayers(): Array<playerShowInfo> {
            return this.sitPlayers.sort((a: playerShowInfo, b: playerShowInfo) => {
                if (a.winIndex == -1) {
                    return 1;
                }
                if (b.winIndex == -1) {
                    return -1;
                }
                if (a.winIndex < b.winIndex) {
                    return -1;
                } else {
                    return 1;
                }
            })
        }

        public getAward(): Array<number> {
            var len = this.sitPlayers.length;
            var royalty = yalla.Global.Account.royalty ? yalla.Global.Account.royalty : 0;
            var [rankNum1, rankNum2, allcost] = [75, 25, this.cost * len];
            var [winMoney1, winMoney2] = [0, 0];
            if (len === 4) {
                var rankPercentList = yalla.Global.Account.rankPercentList;
                if (rankPercentList && rankPercentList.length > 0) {
                    for (var key in rankPercentList) {
                        if (rankPercentList.hasOwnProperty(key)) {
                            var element = rankPercentList[key];
                            if (element['rankNum'] === 1) {
                                rankNum1 = element['rankPercent'];
                            } else if (element['rankNum'] === 2) {
                                rankNum2 = element['rankPercent'];
                            }
                        }
                    }
                }
                // yalla.Debug.log([rankNum1, rankNum2, allcost]);
                winMoney1 = allcost * ((rankNum1 - royalty) / 100);
                winMoney2 = allcost * (rankNum2 / 100);
            } else {
                [winMoney1, winMoney2] = [allcost * (1 - royalty / 100), 0];
            }
            return [winMoney1, winMoney2];
        }
        /**
         * 模拟结算信息 胜利玩家提前退出时显示
         */
        public resultMsg(): any {
            var [winMoney1, winMoney2] = this.getAward();
            var players = this.sortWinPlayers();
            if (players[1] && players[1].winIndex == -1) {
                winMoney2 = 0;
            }
            return {
                winMoney1: winMoney1,
                winMoney2: winMoney2,
                player: players
            }
        }

        public getChessData(idx) {
            if (this.chessData) {
                for (var key in this.chessData) {
                    if (this.chessData[key].idx == idx) return this.chessData[key];
                }
            }
            return { idx: idx, curPos: yalla.data.BeginIndex };
        }

        public getIsSamePos(): boolean {
            if (!this.chessData) return false;
            var v = -1;
            for (var k in this.chessData) {
                if (v == -1) v = this.chessData[k].curPos;
                if (v != this.chessData[k].curPos) return false;
            }
            return true;
        }


        public get playerNums() {
            if (this.player && this.player.player) return this.player.player.length;
            return 2;
        }

        /** 在座的 是否尊贵的royal level >= 4 */
        public get isHonorRoyal(): boolean{
            var players = this.sitPlayers;
            for (var key in players) {
                var element: playerShowInfo = players[key];
                if (element.realRoyLevel >= 4) return true;
            }
            return false;
        }

        public clear() {
            if (this.player) this.player.player = [];//用户列表
            // this.isPublic = 0;
        }
    }
}