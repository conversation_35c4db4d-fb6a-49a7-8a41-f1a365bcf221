module yalla.data.snake {

    import EventDispatcher = laya.events.EventDispatcher;
    import EnumCustomizeCmd = yalla.data.snake.EnumCustomizeCmd;
    import EnumCmd = yalla.data.snake.EnumCmd;

    export class UserService extends EventDispatcher {
        private static _instance: UserService;
        private _user = new yalla.data.User();
        private _room: Room = new Room();
        /**地图数据 */
        private _mapData: MapData = new MapData();
        /**消息队列 */
        public queue: Ludo.Queue = new Ludo.Queue();
        /**未收到cmd=20 快速进入房间消息 存放的消息列表 */
        private _readyMsgList: Array<any> = [];
        private _isReady: boolean = false;

        public LevelUp: boolean = false;
        private _client;
        public IsUpdatePlayerCoin: boolean = false;       //是否更新金币钻石

        // public isEndMsg: boolean = false;
        // public msgList: Array<any> = [];

        constructor() {
            super();
            if (yalla.util.IsBrowser()) {
                this._client = yalla.ClientBrower.instance;
            } else {
                this._client = yalla.Client.instance;
            }
        }

        static get instance(): UserService {
            return UserService._instance || (UserService._instance = new UserService());
        }

        public testData() {
            var raw = 10; var col = 10;
            // this.mapData.generateMapList(raw, col);

            //{sceneId,curPos,mapSkinId, row,col, mapList:[{gId,skinId,type,list:[[]],head,end,pos,scaleX}]}   
            //wid 72    hei 100

            var msgData: any = {
                sceneId: 1, curPos: 0, mapSkinId: 1, row: raw, col: col, mapInfo: [
                    { gId: "1000_1x2", skinId: 1001, type: 1, trace: [[38, 46], [48, 64], [46, 76], [21, 107], [13, 131], [13, 146], [22, 160], [44, 160], [55, 147], [52, 134]], head: 12, end: 9, pos: 12, scaleX: 1 },
                    { gId: "1000_2x3", skinId: 1001, type: 1, trace: [[109, 43], [87, 34], [64, 36], [49, 56], [55, 78], [99, 115], [100, 135], [87, 149], [45, 146], [38, 160], [62, 205], [55, 224]], head: 36, end: 17, pos: 37, scaleX: -1 },
                    { gId: "1000_3x6", skinId: 1001, type: 1, trace: [[50, 51], [76, 46], [121, 62], [144, 102], [128, 154], [68, 210], [63, 230], [76, 256], [145, 311], [149, 334], [112, 419], [113, 438], [133, 471], [151, 480]], head: 61, end: 18, pos: 61, scaleX: 1 },
                    { gId: "1000_3x4", skinId: 1001, type: 1, trace: [[176, 64], [185, 114], [165, 153], [149, 160], [94, 180], [83, 201], [99, 263], [87, 291], [60, 310]], head: 52, end: 27, pos: 54, scaleX: -1 },
                    { gId: "1000_3x6", skinId: 1001, type: 1, trace: [[169, 50], [133, 47], [93, 65], [73, 107], [100, 162], [153, 216], [152, 238], [134, 260], [73, 306], [65, 336], [100, 411], [104, 432], [81, 472], [64, 478]], head: 93, end: 46, pos: 95, scaleX: -1 },
                    { gId: "1000_2x3", skinId: 1001, type: 1, trace: [[34, 46], [56, 34], [79, 36], [96, 51], [90, 78], [53, 112], [46, 130], [57, 150], [104, 147], [107, 164], [82, 202], [86, 221]], head: 97, end: 65, pos: 97, scaleX: 1 },
                    { gId: "1000_1x4", skinId: 1001, type: 1, trace: [[51, 33], [23, 43], [24, 73], [52, 112], [21, 173], [46, 236], [19, 291], [26, 304]], head: 91, end: 70, pos: 91, scaleX: -1 },
                    { gId: "1000_3x2", skinId: 1001, type: 1, trace: [[22, 51], [20, 74], [34, 98], [61, 99], [108, 65], [128, 65], [142, 80], [140, 113], [149, 130], [162, 135]], head: 81, end: 78, pos: 81, scaleX: 1 },
                    { gId: "2000_3x5", skinId: 1001, type: 2, trace: [[36, 386], [37, 56]], head: 48, end: 10, pos: 48, scaleX: 1 },
                    { gId: "2000_3x3", skinId: 1001, type: 2, trace: [[105, 126], [41, 48]], head: 26, end: 4, pos: 24, scaleX: -1 },
                    { gId: "2000_4x4", skinId: 1001, type: 2, trace: [[38, 128], [105, 47]], head: 78, end: 45, pos: 79, scaleX: 1 }
                ]
            };
            this.mapData.initMapJsonData(msgData);
            var playerList = [{
                chessSkinId: 11000, color: 3, diceSkinId: 10000, fPlayerInfo: { idx: 10000, faceId: 0, nikeName: 'test10000', faceUrl: 'unkown', placeId: 0, viplevel: 2 },
                isFighted: 0, isInChatOff: 0, isInSysTrust: 0, isLeft: 0, isQuit: 0, isSignUp: 0, prettyId: 10000, reThrowCost: 2, reThrowNum: 0, royVisibility: true, roylevel: 3, segId: 0,
                sitNum: 2, starNum: 0, talkSkinId: 12000, playColor: 0
            },
            {
                chessSkinId: 11000, color: 0, diceSkinId: 10000, fPlayerInfo: { idx: 10001, faceId: 0, nikeName: 'test10001', faceUrl: 'unkown', placeId: 0, viplevel: 2 },
                isFighted: 0, isInChatOff: 0, isInSysTrust: 0, isLeft: 0, isQuit: 0, isSignUp: 0, prettyId: 10001, reThrowCost: 2, reThrowNum: 0, royVisibility: false, roylevel: 0, segId: 0,
                sitNum: 1, starNum: 0, talkSkinId: 12000, playColor: 3
            }
            ];
            yalla.Native.instance.loginDominoBrowser(GameType.SNAKEANDLADER);
            this._room.gameMapInfo = msgData;
            this._room.initData({ player: { player: playerList } });

            var rand = 1.1//Math.random() * 2;
            Laya.timer.once(2000, this, () => {
                var msg0 = { command: 31, idx: 10000, event: 10 };//离开起点
                var msg1 = { command: 31, idx: 10000, event: 7, throwNum: 3, oldPos: 1, targetPos: 4 };//走棋子
                var msg2 = { command: 31, idx: 10000, event: 9, throwNum: 0, oldPos: 4, targetPos: 26 };//爬梯子
                var msg3, msg4;
                if (rand > 1) {
                    msg3 = { command: 31, idx: 10000, event: 7, throwNum: 35, oldPos: 26, targetPos: 61 };//走棋子
                    msg4 = { command: 31, idx: 10000, event: 8, oldPos: 61, targetPos: 18 };//蛇吻 81格蛇头
                    this.mapData.endPos = 18;
                } else {
                    msg3 = { command: 31, idx: 10000, event: 7, throwNum: 10, oldPos: 26, targetPos: 36 };//走棋子
                    msg4 = { command: 31, idx: 10000, event: 8, oldPos: 36, targetPos: 17 };//蛇吻 36格蛇头
                    this.mapData.endPos = 17;
                }
                this.pushMsg(msg0);
                this.pushMsg(msg1);
                this.pushMsg(msg2);
                this.pushMsg(msg3);
                this.pushMsg(msg4);
            });
        }

        public inSit(): boolean {
            if (this._user && this._user.playerShowInfo) {
                return this._user.playerShowInfo.sitNum >= 1;
            }
            return false;
        }
        public register(): void {
            if (!this._user) this._user = new yalla.data.User();
            if (!this._room) this._room = new Room();
            if (!this._mapData) this._mapData = new MapData();

            // this._client.on('Game_4_1', this, this.GameResponse);
            this._client.on('Game_4', this, this.GameResponse);
            this._client.on('Game_SocketClose', this, this.handleSocketClose);
            this._client.on('Game_GateWay_Error', this, this.handleGatewayError);
        }

        public clear(): void {
            this.IsUpdatePlayerCoin = false;
            // this._client.off('Game_4_1', this, this.GameResponse);
            this._client.off('Game_4', this, this.GameResponse);
            this._client.off('Game_SocketClose', this, this.handleSocketClose);
            this._client.off('Game_GateWay_Error', this, this.handleGatewayError)

            this._room && this._room.clear();
            this._mapData && this._mapData.clear();
            this.clearQueue();

            this._room = null;
            this._user = null;
            this._mapData = null;
            this._readyMsgList = null;
            this._isReady = false;
        }

        public get user() { return this._user; }
        public get room() { return this._room; }
        public get mapData() { return this._mapData; }

        /**
         * 票据登录
         *message SnakeLadderMessage
            Command command = 1;
            int64 roomId = 2;
            TicketLoginRequest ticketLogin = 3;
            GameOperateRequest gameOperateRequest = 4;
            GameRoomPlayerChatToAllRequestAndResponse chatToAll = 5;
            GetAgoraTokenRequest agoraTokenRequest = 6;
         */
        private sendMsg(cmd, cmdName, bodyData?, businessOrder = 1): void {
            var msg = bodyData
            if (!msg) msg = {};
            msg.roomId = Global.Account.roomid;
            msg.command = cmd;
            console.log(cmdName + '=====发送消息====' + bodyData.hasOwnProperty('data'));
            console.log(msg);
            // this._client.sendMsg('SnakeLadderMessage', cmd, msg, { ringRouteKey: Global.Account.roomid });
            this._client.sendMsg(cmdName, cmd, msg, { ringRouteKey: Global.Account.roomid }, businessOrder);
        }
        public ticketLogin(): void {
            if (!this._user) return;

            var msgindex = 1;
            if (yalla.util.IsBrowser()) msgindex = this.msgProgress - 1 >= 0 ? this.msgProgress - 1 : 0;
            yalla.Debug.log(yalla.Global.Account.token + '--ticketLogin---' + this._user.idx);
            // this.sendMsg(yalla.data.snake.Command.TICKET_LOGIN, 'SnakeLadderMessage', {
            var obj = {
                idx: this._user.idx,
                token: yalla.Global.Account.token,
                version: yalla.Global.Account.version,
                roomid: yalla.Global.Account.roomid
            }
            if (ludo.Proto.instance.root_snake) {
                var loginMessage = ludo.Proto.instance.root_snake.lookupType('TicketLoginRequest');
                var message = loginMessage.create(obj);
                var byteArray = loginMessage.encode(message).finish();
                
                this.sendMsg(yalla.data.snake.Command.TICKET_LOGIN, 'SnakeLadderStreamRequest', {
                    data: byteArray
                }, BusinessOrderCode.snake_message);
            }
        }

        /**
         * 退出房间
         * 先通知网关断流（通知加入其它游戏、主动退出）
         */
        public quitRoom(): void {
            // console.log("退出游戏请求roomid：" + this._room.roomid);
            // this.sendMsg(yalla.data.snake.Command.QUIT_ROOM, 'QuitRoomRequest',{ quitRoomRequest: { idx: Global.Account.idx, roomId: this._room.roomid } });
            this.sendMsg(yalla.data.snake.Command.QUIT_ROOM, 'QuitRoomRequest', { idx: Global.Account.idx, roomId: Global.Account.roomid }, BusinessOrderCode.snake_quitRoom);
            // this.clearStream();
        }

        // public clearStream() {
        //     if (this._client) {
        //         yalla.Debug.log('===退出房间发起断流=====');
        //         this._client.sendCleanStream(1);
        //     }
        // }

        /**
         * 快捷短语聊天
         */
        public sendChat(msgResult: any = {}, idx: number = 0): void {
            msgResult.idx = idx ? idx : this._user.idx;
            msgResult.roomid = this.room.roomid;
            this.sendMsg(yalla.data.snake.Command.PLAYER_CHAT_ALL, 'GameRoomPlayerChatToAllRequestAndResponse', msgResult, BusinessOrderCode.snake_playerChatToAll);
        }

        /**
         * 获取金币接口  0金币 1钻石
         */
        public flushCurrencyRequest(type: number = 0): void {
            if (!this.IsUpdatePlayerCoin) return;
            this.sendMsg(yalla.data.snake.Command.FLUSH_CURRENCY, 'FlushCurrencyRequest', {
                // flushCurrencyRequest: {
                type: type
                // }
            }, 19);
        }

        /**
         * 取消托管
         */
        cancleTrust() {
            if (!this._user) return;
            if (this._room.roomid && this._user.idx) {
                this.sendMsg(yalla.data.snake.Command.GAME_OPERATE, 'GameOperateRequest', {
                    // gameOperateRequest: {
                    idx: this._user.idx,
                    roomId: this._room.roomid,
                    op: yalla.data.GameOperate.SYSTEM_TRUST_CANCEL,
                    msgindex: this.msgProgress
                    // }
                }, BusinessOrderCode.snake_gameOperate);
            }
        }

        /**摇骰子 */
        clickDice() {
            if (!this._user) return;
            yalla.Debug.log("=====发送请求 掷骰子====");
            yalla.Debug.log(this._room.roomid + "---" + this._user.idx);
            if (this._room.roomid && this._user.idx) {
                this.sendMsg(yalla.data.snake.Command.GAME_OPERATE, 'GameOperateRequest', {
                    // gameOperateRequest: {
                    idx: this._user.idx,
                    msgindex: this.msgProgress,
                    roomId: this._room.roomid,
                    op: yalla.data.GameOperate.THROW
                    // }
                }, BusinessOrderCode.snake_gameOperate);
            }
        }
        /**重置骰子 */
        clickReset() {
            if (!this._user) return;
            yalla.Debug.log("=====发送请求 重置骰子====");
            yalla.Debug.log(this._room.roomid + "---" + this._user.idx);
            this.sendMsg(yalla.data.snake.Command.GAME_OPERATE, 'GameOperateRequest', {
                // gameOperateRequest: {
                idx: this._user.idx,
                msgindex: this.msgProgress,
                op: yalla.data.GameOperate.RESET_THROW
                // }
            }, BusinessOrderCode.snake_gameOperate);
        }

        /**选择棋子移动 */
        sendChooseMsg() {
            if (!this._user) return;
            this.sendMsg(yalla.data.snake.Command.GAME_OPERATE, 'GameOperateRequest', {
                // gameOperateRequest: {
                idx: this._user.idx,
                msgindex: this.msgProgress,
                op: yalla.data.GameOperate.CHESS_MOVE,
                // }
            }, BusinessOrderCode.snake_gameOperate);
        }

        /**
         * 设置聊天屏蔽  
         * @param chatOffType false取消屏蔽   true设置屏蔽
         */
        public sendChatOff(isChatOff: number): void {
            if (!this._user) return;
            this.sendMsg(yalla.data.snake.Command.GAME_OPERATE, 'GameOperateRequest', {
                // gameOperateRequest: {
                idx: this._user.idx,
                roomId: this._room.roomid,
                op: isChatOff ? yalla.data.GameOperate.CHAT_OFF : yalla.data.GameOperate.CHAT_OFF_CANCEL,
                // }
            }, BusinessOrderCode.snake_gameOperate);

            ludo.muteSpectator.isMute = isChatOff;
            //TODO::自己操作的话不等服务端返回，避免因消息不返回，设置的状态和玩家头像上的状态不一致
            yalla.event.YallaEvent.instance.event(yalla.data.snake.EnumCustomizeCmd.Game_ChatOFF_My, { idx: this.user.idx, num: isChatOff });
        }

        public getGiftCnfList(): void {
            if (!this._user) return;
            console.log('=====getGiftCnfList======');
            this.sendMsg(yalla.data.snake.Command.GAME_GIFT_CNF_LIST, 'GameGiftCnfListRequest', {
                // gameGiftCnfListRequest: {
                idx: this._user.idx
                // }
            }, BusinessOrderCode.snake_gameGiftCnfList);
        }

        public sendGift(giftId: number, rcvIdxs: Array<any>): void {
            if (!this._user) return;
            this.sendMsg(yalla.data.snake.Command.GAME_GIFT_SEND, 'GameGiftSendRequest', {
                // gameGiftSendRequest: {
                idx: this._user.idx,
                roomId: this._room.roomid,
                giftId: giftId,
                rcvIdx: rcvIdxs
                // }
            }, BusinessOrderCode.snake_gameGiftSend);
        }

        /**
         * 获取语音房间token
         */
        public getToken() {
            if (!this._user) return;
            yalla.Debug.log("==getToken==voiceType:" + yalla.Global.Account.voiceType);
            switch (yalla.Global.Account.voiceType) {
                case VoiceType.Agora:
                    this.sendMsg(yalla.data.snake.Command.GET_AGORA_TOKEN, 'GetAgoraTokenRequest', {
                        // agoraTokenRequest: {
                        idx: this.user.idx,
                        roomId: this.room.roomid
                        // }
                    }, BusinessOrderCode.snake_getAgoraToken);

                    break;
                case VoiceType.Zego://1.2.8接入zego
                    this.sendMsg(yalla.data.snake.Command.GET_ZEGO_TOKEN, 'GetZegoTokenRequest', {
                        // zegoTokenRequest: {
                        idx: this.user.idx,
                        roomId: this.room.roomid,
                        version: 4
                        // }
                    }, BusinessOrderCode.snake_getZegoToken);
                    break;
            }
        }


        //-----------------------------------------handle=------------------------------------
        //GameGiftCnfListRespone、 agoraTokenResponse、 zegoTokenResponse、flushCurrencyResponse 走双向流，其他仍然是snakeLadderMessage保持不变
        private processMsg(businessOrder, cmd, data) {
            var msg;
            var eventType = EnumCustomizeCmd.Snake_PlayMsg;
            yalla.Debug.log(businessOrder + ':businessOrder===processMsg===cmd:' + cmd);
            switch (businessOrder) {
                case BusinessOrderCode.snake_message:
                    this.doubleStreamResponse(cmd, data);
                    break;
                case BusinessOrderCode.snake_getAgoraToken:
                    msg = this.decodeMsg(data, "GetAgoraTokenResponse");
                    if (!msg) return;
                    this.event(yalla.data.snake.EnumCmd.Game_GetAgoraToken, [90, msg.token, msg.cName]);
                    break;
                case BusinessOrderCode.snake_getZegoToken:
                    msg = this.decodeMsg(data, "GetZegoTokenResponse");
                    if (!msg) return;
                    this.event(yalla.data.snake.EnumCmd.Game_GetZegoToken, [92, msg.token, msg.cName]);
                    break;
                case BusinessOrderCode.snake_flushCurrency:
                    msg = this.decodeMsg(data, "FlushCurrencyResponse");
                    if (!msg) return;
                    this.flushCurrencyResponse(msg);
                    break;
                case BusinessOrderCode.snake_gameGiftCnfList:
                    msg = this.decodeMsg(data, "GetGameGiftCnfListResponse");
                    if (!msg) return;
                    yalla.event.YallaEvent.instance.event(eventType, msg);
                    break;
            }
            // if(msg) this.msgList.push({ 'cmd': cmd, 'msg': msg,'time':yalla.System.getNowTime() });
            // console.log(msg);
        }

        private doubleStreamResponse(cmd, data): void {
            var d;
            var msg;
            var eventType = EnumCustomizeCmd.Snake_PlayMsg;
            switch (cmd) {
                case Command.TICKET_LOGIN:
                    d = this._client.decodeMsg(data, "SnakeLadderStreamResponse");
                    yalla.Debug.log('===loginresponese===roomid:' + yalla.Global.Account.roomid);
                    yalla.Debug.log(d);
                    if (!d || !d.data) return;
                    if (d && d.roomId && d.roomId != yalla.Global.Account.roomid) return;
                    msg = this._client.decodeMsg(d.data, "LoginResponse");
                    msg.command = Command.TICKET_LOGIN;
                    yalla.Debug.log(msg);
                    this.loginResponse(msg);
                    break;
                case Command.QUICK_START:
                    msg = this.decodeMsg(data, "QuickEnterRoomResponse", true);
                    if (!msg) return;
                    msg.command = Command.QUICK_START;
                    this._isReady = true;
                    this.quickEnterRoomResponse(msg);
                    break;
                case Command.GAME_DATA_INFO:
                    msg = this.decodeMsg(data, "GameDataResponse", true);
                    if (!msg) return;
                    msg.command = Command.GAME_DATA_INFO;
                    this.gameDataResponse(msg);
                    break;
                case Command.QUIT_ROOM:
                    msg = this.decodeMsg(data, "QuitRoomResponse", true);
                    if (!msg) return;
                    msg.command = Command.QUIT_ROOM;
                    this.quitRoomResponse(msg);
                    break;
                case Command.GAME_OPERATE:
                    msg = this.decodeMsg(data, "GameOperateResponse", true);
                    if (!msg) return;
                    msg.command = Command.GAME_OPERATE;
                    this.gameOperateResponse(msg);
                    break;
                case Command.GAME_EVENT:
                    msg = this.decodeMsg(data, "GameEventResponse", true);
                    if (!msg) return;
                    msg.command = Command.GAME_EVENT;
                    this.gameEventResponse(msg);
                    break;
                case Command.GAMESTATUS:
                    msg = this.decodeMsg(data, "GameStatusResponse", true);
                    if (!msg) return;
                    msg.command = Command.GAMESTATUS;
                    yalla.event.YallaEvent.instance.event(eventType, msg);
                    break;
                case Command.GAMERESULT:
                    msg = this.decodeMsg(data, "GameResultResponse", true);
                    if (!msg) return;
                    msg.command = Command.GAMERESULT;
                    this.gameResultResponse(msg);
                    // this.isEndMsg = true;
                    break;
                case Command.GAMEPLAYERSTATUS:
                    msg = this.decodeMsg(data, "GamePlayerStatusResponse", true);
                    if (!msg) return;
                    msg.command = Command.GAMEPLAYERSTATUS;
                    yalla.event.YallaEvent.instance.event(eventType, msg);
                    break;
                case Command.PLAYER_CHAT_ALL:
                    msg = this.decodeMsg(data, "GameRoomPlayerChatToAllRequestAndResponse", true);
                    console.log("=====收到消息====");
                    console.log(msg);
                    if (!msg) return;
                    this.event(EnumCmd.Game_Chat, msg);
                    break;
                case Command.UPDATA_PLAYER_COIN:
                    msg = this.decodeMsg(data, "UpdateCoinResponse", true);
                    if (!msg) return;
                    msg.command = Command.UPDATA_PLAYER_COIN;
                    this.flushCurrencyResponse(msg);
                    break;
                case Command.GAME_GIFT_SEND:
                    msg = this.decodeMsg(data, "GameGiftSendResponse", true);
                    if (!msg) return;
                    msg.command = Command.GAME_GIFT_SEND;
                    this.gameGiftSendResponse(msg);
                    break;
                case Command.OUT_GAME:
                    msg = this.decodeMsg(data, "OutGameResponse", true);
                    if (!msg) return;
                    msg.command = Command.OUT_GAME;
                    this.outGameResponse(msg);
                    break;
            }
            // msg.time = yalla.System.getNowTime();
            // if (msg) this.msgList.push(msg);
            // if (this.isEndMsg) {
            //     console.log("====gameOver 游戏接口数据=====");
            //     console.log(this.msgList);
            //     this.isEndMsg = false;
            // }
        }
        private decodeMsg(data: any, messageName: string, isSnakeLadderStreamResponse: boolean = false): void {
            var msg;
            if (isSnakeLadderStreamResponse) {
                var d = this._client.decodeMsg(data, "SnakeLadderStreamResponse");
                yalla.Debug.log(d);
                if (!d || !d.data) return;
                msg = this._client.decodeMsg(d.data, messageName);
            } else {
                msg = this._client.decodeMsg(data, messageName);
            }
            yalla.Debug.log(messageName + "=====snake.decodeMsg=====" + isSnakeLadderStreamResponse);
            yalla.Debug.log(msg);
            return msg;
        }
        private GameResponse(code: any, msg: any, cmd, businessOrder?): void {
            if (yalla.Global.gameType != GameType.SNAKEANDLADER) return;
            if (yalla.data.IsTestCode) return;
            if (yalla.Global.IsGameOver) return;   //TODO:;条件需要的，测试阶段先去掉
            if (cmd == 0 || !msg) return;
            // var msg: any = this._client.decodeMsg(d, "SnakeLadderResponse");

            yalla.Debug.log(`[${yalla.getTimeHMS()} ][response] [snake-gateway-code:${code}] [cmd:${cmd}]`);
            yalla.Debug.log(this._isReady);
            if (!this._isReady) {
                if (!this._readyMsgList) this._readyMsgList = [];
                if (cmd > 20) {//没有收到房间信息之前除登录和房间信息之外任何消息暂存不处理
                    this._readyMsgList.push({
                        cmd: cmd,
                        msg: msg,
                        businessOrder: businessOrder
                    })
                    return;
                }
            }
            yalla.Debug.log(cmd + '======this._readyMsgList==' + this._readyMsgList.length);
            yalla.Debug.log(this._readyMsgList);
            this.processMsg(businessOrder, cmd, msg);
        }
        /**
         * 成功登录游戏
         * @param d 
         */
        private loginResponse(msg: any): void {
            yalla.Debug.log('==Userservice.loginResponse===' + msg.result);
            if (!msg) {
                yalla.common.WaringDialog.showResultError(yalla.data.ErrorCode.NONE_ERROE, () => {
                    this.backHall(true);
                });
                return;
            }
            if (msg.result == 1) {
                this._user.idx = msg.palyerInfo.playerShowInfo.fPlayerInfo.idx;
                this._user.update(msg.palyerInfo);
                yalla.Global.Account.banTalkData = msg.banTalkData || "";
                yalla.Global.Account.voiceType = msg.voiceType || VoiceType.Agora;//1.2.8 add 2021年11月1日10:02:06
            } else {
                yalla.Native.instance.removeMatchView();
                yalla.Global.IsGameOver = true;
                yalla.common.WaringDialog.showResultError(msg.code, () => {
                    this.backHall(true);
                });
            }
        }

        private quickEnterRoomResponse(msg: any): void {
            yalla.Debug.log('==Userservice.quickEnterRoomResponse===' + msg.result);
            if (msg.result == 1) {

                this._room.initData(msg);
                let myUser = this._room.getPlayerByIdx(this._user.idx);
                this._user.update({ playerShowInfo: myUser });
                let undo = ludo.Undo.instance.reset();
                //undo 赋值
                [undo.maxReThrowNum, undo.maxTurnReThrowNum, undo.reThrowCost, undo.currentReThrowNum, undo.currentTurnReThrowNum] =
                    [msg.maxReThrowNum, msg.maxTurnReThrowNum, msg.reThrowCost, msg.currentReThrowNum, msg.currentTurnReThrowNum];

                yalla.Global.Account.isPrivate = this._room.isPrivate;
                if (this._user && this._user.playerShowInfo) {
                    yalla.Global.Account.roylevel = this._user.playerShowInfo.roylevel;
                    yalla.Global.Account.realRoyLevel = this._user.playerShowInfo.realRoyLevel;
                }
                if (yalla.Native.instance.deviceType == DeviceType.IOS) {
                    yalla.Native.instance.removeMatchView();
                }
                // this.pushMsg(msg);
                yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Snake_PlayMsg, msg);
                this._readyMsgList.forEach(msgs => {
                    this.processMsg(msgs.businessOrder, msgs.cmd, msgs.msg);
                })
                this._readyMsgList = [];
                this.sendChatOff(ludo.muteSpectator.isMute);//TODO::避免设置取消屏蔽协议发送失败（但本地已改），导致房间内同个玩家状态不一致
                this.getGiftCnfList();
            } else {
                console.log("进入房间回复错误1");
                yalla.common.WaringDialog.showResultError(msg.code, () => {
                    this.backHall(true);
                });
            }
        }

        /**下发游戏地图信息 */
        private gameDataResponse(msg: any): void {
            yalla.Debug.log('==Userservice.gameDataResponse===');
            this._room.sceneId = msg.gameMapInfo.sceneId;
            this._room.gameMapInfo = msg.gameMapInfo;
            this._room.chessData = msg.chessData;
            yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Snake_PlayMsg, msg);
            // this.pushMsg(msg);
        }

        private gameEventResponse(msg: any): void {
            if (msg.event == yalla.data.GameEvent.GAME_COLOR_SELECT) {
                this._room.updateColor(msg.color);
                this._room.colorList = msg.color;
            }
            this.pushMsg(msg);
        }

        /**
         * 游戏操作
         * @param d 
         */
        private gameOperateResponse(msg: any): void {
            if (msg && msg.code == yalla.data.ErrorCode.ACCOUNT_BE_FROZEN) {
                yalla.common.Confirm.instance.showConfirm(yalla.data.TranslationD.Game_Error_ACCOUNT_BE_FROZEN, Laya.Handler.create(this, () => {
                    yalla.common.Confirm.instance.hideConfirm();
                }), null, [yalla.data.TranslationD.Game_Btn_Confirm]);
            }
            if (!msg || msg.result == 0) {
                return;
            }
            yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Snake_PlayMsg, msg);
        }

        private gameResultResponse(msg): void {
            this.pushMsg(msg);
            // Global.IsGameOver = true;//TODO::等移动结束播放结算
            // this.event(yalla.data.snake.EnumCmd.Game_GameResult, msg);
        }

        private quitRoomResponse(msg: any): void {
            if (msg.idx != this._user.idx) return;

            yalla.Global.IsGameOver = true;
            if (msg.result == 1) {
                this.event(yalla.data.snake.EnumCmd.Game_QuitRoom, msg);
            } else {
                yalla.common.WaringDialog.showResultError(msg.code, () => {
                    this.backHall(true);
                });
            }
        }

        private outGameResponse(msg: any): void {
            if (!msg) return;
            var reason = msg.reason;
            if (msg.idx && msg.idx == this.user.idx) yalla.Global.IsGameOver = true;
            yalla.data.VoiceService.instance.levelGameRoom();//TODO::1.3.4 因被顶号后backhall有概率退出语音房释放，这里再次执行levelGameRoom
            this._client && this._client.clear();
            switch (reason) {
                case 1:
                    yalla.Debug.log('别处登陆');
                    // yalla.data.UserService.instance.recordPointQuit(yalla.data.ExitType.LOGGED_ANOTHER_QUIT);
                    // this.showDialog('loginOut', TranslationD.Game_LoginOut_Another_Content, [TranslationD.Game_LoginOut_Btn]);
                    yalla.Native.instance.alertAccountLoggedView(() => {
                        yalla.Native.instance.backHall(false);
                    });
                    break;
                case 3:
                    yalla.Debug.log('被踢');
                    yalla.DialogManager.instance.showDialog('loginOut', yalla.data.TranslationD.Game_LoginOut_KickOut_Content, [yalla.data.TranslationD.Game_LoginOut_Btn]);
                    // yalla.data.UserService.instance.recordPointQuit(yalla.data.ExitType.KICKED_QUIT);
                    break;
                case 2:
                    yalla.Debug.log('服务器维护');
                    yalla.DialogManager.instance.showDialog('loginOut', yalla.data.TranslationD.Game_LoginOut_Maintenance_Content, [yalla.data.TranslationD.Game_LoginOut_Btn]);
                    // yalla.data.UserService.instance.recordPointQuit(yalla.data.ExitType.SERVER_MAINTENANCE_QUIT);
                    break;
            }
        }

        private flushCurrencyResponse(msg: any): void {
            this._user.updateCoin(msg);
            if (this._room.leftDiamond != this.user.gold) {
                this._room.leftDiamond = this.user.gold;
            }
            this.event(yalla.data.snake.EnumCmd.Game_Update_Player_Coin, msg);
        }

        private handleUpdatePlayLevel(msg: any): void {
            var oldLv = this._user.playerShowInfo.fPlayerInfo.level;
            this._user.updatePlayerLevel(msg);
            var nowLv = this._user.playerShowInfo.fPlayerInfo.level;

            if (nowLv > oldLv) {
                this.LevelUp = true;
            }
        }

        private gameGiftSendResponse(msg: any): void {
            yalla.Debug.log("==========jieshou 礼物=========");
            yalla.Debug.log(msg);
            if (msg.code > 0) {
                yalla.common.InteractiveGift.Instance.checkError(msg.code);
                return;
            }
            yalla.Debug.log("==========jieshou 礼物 1=========");
            if (msg.senderIdx == yalla.Global.Account.idx) {
                var giftData = yalla.common.InteractiveGift.Instance.getGiftItemData(msg.giftId);
                if(msg.senderMoney || (giftData && giftData.money)) this.user.updateCoin({ type: 0, value: msg.senderMoney });
                if (msg.senderDiamond || (giftData && giftData.diamond)) this.user.updateCoin({ type: 1, value: msg.senderDiamond });
            }
            yalla.common.InteractiveGift.Instance.pushMsg(msg);
            yalla.Debug.log("==========jieshou 礼物 2=========");
            yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Snake_PlayMsg, msg);
        }

        private handleGatewayError(code: number) {
            // if (yalla.Global.gameType != GameType.SNAKEANDLADER) return;
            if (code > 0) {
                yalla.common.WaringDialog.showGatewayError(code, () => {
                    this.backHall(true);
                });
                return;
            }
        }

        private handleSocketClose() {
            this._readyMsgList = null;
            this._isReady = false;
        }

        public leaveVoiceRoom() {
            if (yalla.Voice.instance.joinSuccess) yalla.Voice.instance.levelGameRoom();
        }


        private progress: number = 0;
        public isQueue: boolean = false;
        public nextMsg() {
            // this.isQueue = false;
            if (this.queue.isEmpty()) {
                this.isQueue = false;
                return;
            }
            let msg = this.queue.dequeue();
            this.isQueue = true;
            yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Snake_PlayMsg, msg['msg']);
        }

        public pushMsg(msg: any) {
            if (!this.isQueue || !yalla.Global.isFouce) {//不经过队列
                this.isQueue = true;
                yalla.event.YallaEvent.instance.event(EnumCustomizeCmd.Snake_PlayMsg, msg);
                // this.processMsg(cmd, msg);
            } else {//放进队列
                this.queue.enqueue(msg.command, msg);
            }

            if (this.queue.size() > 2) {
                this.nextMsg();
            }
        }

        get msgProgress(): number {
            if (this.queue) {
                return this.queue.progress;
            } else {
                return 0;
            }
        }

        public clearQueue() {
            this.queue && this.queue.reset();
            this.isQueue = false;
        }

        /**
         * 是否立即重连
         */
        public get ImmediateConnect(): boolean {
            if (!this._client.msgHash || this._client.msgIndex <= 0) return true;
            if (this._client.msgHash) {
                var msg = this._client.msgHash[this._client.msgIndex];
                if (msg && this._user) {
                    if (msg.idx != this._user.idx) return true;
                }
            }
            return false;
        }

        /**
         * @param isRun 是否逃跑   true，返回大厅，false根据情况定
         */
        public backHall(isRun: boolean = false, delayTime: number = 0): void {
            this._client && this._client.clear();
            yalla.Debug.log(isRun + "===snake.backHall===isPrivate=" + yalla.Global.Account.isPrivate);
            yalla.data.UserService.instance.backHall(isRun, delayTime);
        }
    }
}