// interface mapElement{
//     gId:number,         //蛇/梯子组合编号 id（目前 23 + 13种）
//     skinId:number,      //蛇/梯子皮肤id
//     type:number,        //蛇1 / 梯子2
//     list: any,        //[[x,y]]
//     realList: Array<any>, //蛇相对地图的坐标列表
//     nList:Array<any>,       //默认蛇 相对地图坐标列表
//     head:number,        //蛇头  梯头所在 格子id
//     end:number,         //蛇尾   梯尾所在 格子id
//     pos: number,        //蛇/梯子 在地图中位置 的格子id
//     scaleX:number,      //蛇或者梯子是否翻转 -1
// }

interface GameMapInfo{
  sceneId:number,//地图id
  curPos:number,//当前位置
  mapSkinId:number,//地图皮肤id
  row:number,//地图行数
  col:number,//地图列数
  mapInfo:Array<MapInfo>//地图具体信息
}

interface MapInfo{
  gId:string,//蛇/梯子组合编号 id
  skinId:number,//皮肤id
  type:number,//蛇1 梯子2
  trace: string,//轨迹信息
  realList: Array<any>, //蛇相对地图的坐标列表
  nList: Array<any>,       //默认蛇 相对地图坐标列表  
  head:number,//蛇头  梯头所在 格子id
  end:number,//蛇尾   梯尾所在 格子id
  pos:number,//蛇/梯子 在地图中位置 的格子id
  scaleX:number//蛇或者梯子是否翻转
}