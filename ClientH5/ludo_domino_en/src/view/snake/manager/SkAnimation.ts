module yalla {
    import EventDispatcher = laya.events.EventDispatcher;
    /**
     * 日锦赛 动画
     */
    export class SkAnimation extends EventDispatcher {
        static _instance: SkAnimation;
        private _parent: any;
        public templet: Laya.Templet;
        public snakeSk: Laya.Skeleton;

        Event = {
            COMPLETE: "complete",//动画加载完成
        }

        constructor() {
            super();
        }

        static get instance(): SkAnimation {
            return SkAnimation._instance || (SkAnimation._instance = new SkAnimation());
        }

        private _callBack: Laya.Handler;
        public initAnimation(parent: any): void {
            this._parent = parent;
            if (!this.templet) {
                this.templet = new Laya.Templet();
                this.templet.on(Laya.Event.COMPLETE, this, this.skComplete);
                this.templet.loadAni(yalla.getSkeleton('snake/sheqizhongdian'));
            }
        }

        //VSdonghua   pipei   pipeibaodian   qizi1   qizi2
        private skComplete() {
            if(!this.templet) return;
            this.snakeSk = this.templet.buildArmature(1);
            // let self = this;
            // this.snakeSk.once(Laya.Event.STOPPED, this, () => {
            //     self._callBack && self._callBack.run();
            // });
        }

        public playSnakeEndPos(pos: any, callback: Laya.Handler) {
            Laya.timer.clearAll(this);
            if (this._parent && this.snakeSk) {
                if(pos) this.snakeSk.pos(pos[0], pos[1]);
                this._parent.addChild(this.snakeSk);
            }
            this.snakeSk && this.snakeSk.play(0, false);
            
            Laya.timer.once(150, this, () => {
                callback && callback.run();
            })
        }

        private removeEvent(): void {
            if (this.snakeSk) {
                this.snakeSk.stop();
                this.snakeSk.offAll();
            }
            
            Laya.timer.clearAll(this);
        }

        public clear(): void {
            this.removeEvent();
            this.templet && this.templet.destroy();
            this.templet = null;
        }
    }
}