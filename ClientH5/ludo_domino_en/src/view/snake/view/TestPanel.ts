module yalla {
    /**
     * topMc
     */
    export class TestPanel extends ui.snake.item.TestPanelUI {
        constructor() {
            super();
            this.saveBtn.on(Laya.Event.CLICK, this, this.onSave);
            this.initBtn.on(Laya.Event.CLICK, this, this.onInitPos);
            this.closeBtn.on(Laya.Event.CLICK, this, this.onClose);

            var snake_move_basetime = Laya.LocalStorage.getItem('snake_move_basetime');
            var snake_move_changetime = Laya.LocalStorage.getItem('snake_move_changetime');

            var snake_kiss_time = Laya.LocalStorage.getItem('snake_kiss_time');
            var snake_kiss_time_v = Laya.LocalStorage.getItem('snake_kiss_time_v');

            var snake_ladder_step = Laya.LocalStorage.getItem('snake_ladder_step');
            var snake_ladder_step_time = Laya.LocalStorage.getItem('snake_ladder_step_time');
            if (snake_move_basetime) this.moveInitTimeTxt.text = snake_move_basetime + '';
            if (snake_move_changetime) this.moveChangeTimeTxt.text = snake_move_changetime + '';

            if (snake_kiss_time) this.kissTimeTxt.text = snake_kiss_time + '';
            if (snake_kiss_time_v) this.kissTimeVTxt.text = snake_kiss_time_v + '';
            
            if (snake_ladder_step) this.ladderStepTxt.text = snake_ladder_step + '';
            if (snake_ladder_step_time) this.ladderStepTimeTxt.text = snake_ladder_step_time + '';
        }

        private onSave(e:Event): void{
            Laya.LocalStorage.setItem('snake_move_basetime', this.moveInitTimeTxt.text);
            Laya.LocalStorage.setItem('snake_move_changetime', this.moveChangeTimeTxt.text);

            Laya.LocalStorage.setItem('snake_kiss_time', this.kissTimeTxt.text);
            Laya.LocalStorage.setItem('snake_kiss_time_v', this.kissTimeVTxt.text);
            Laya.LocalStorage.setItem('snake_ladder_step', this.ladderStepTxt.text);
            Laya.LocalStorage.setItem('snake_ladder_step_time', this.ladderStepTimeTxt.text);

            yalla.data.Move_Base_Time = Number(this.moveInitTimeTxt.text);
            yalla.data.Move_Change_Time = Number(this.moveChangeTimeTxt.text);

            yalla.data.Step_Len = Number(this.ladderStepTxt.text);
            yalla.data.Step_Time = Number(this.ladderStepTimeTxt.text);
            yalla.data.Snake_Time = Number(this.kissTimeTxt.text);
            yalla.data.Snake_Time_V = Number(this.kissTimeVTxt.text);

            console.log(yalla.data.Move_Base_Time, yalla.data.Move_Change_Time, yalla.data.Step_Len, yalla.data.Step_Time, yalla.data.Snake_Time);
            this.close();
        }

        private onInitPos(e:Event): void{
            
        }

        private onClose(e:Event): void{
            this.close();
        }
    }
}