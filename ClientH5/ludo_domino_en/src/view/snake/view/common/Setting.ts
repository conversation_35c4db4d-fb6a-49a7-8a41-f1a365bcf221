module yalla.view.game.snake {
    export class Setting  {
        private _ui;
        constructor(ui) {
            this._ui = ui;
            this.initUI();
        }

        private initUI(): void  {
            this._ui.ruleBtn.on(Laya.Event.CLICK, this, this.onClickRules);
            this._ui.exitBtn.on(Laya.Event.CLICK, this, this.onExit);
            this._ui.soundBtn.on(Laya.Event.CLICK, this, this.onClickSound);
            this._ui.chatSwitchBtn.on(Laya.Event.CLICK, this, this.onClickChatSwitch);
            // this._ui.musicBtn.on(Laya.Event.CLICK, this, this.onClickMusic);

            // this._ui.musicBtn.visible = false;
            if(yalla.Font.lan == 'ar'){
                this._ui.soundBtn.labelPadding = '-4,0,0,-8';
                this._ui.exitBtn.labelPadding = '-4,0,0,-35';
            }
        }

        public update(): void  {
            this.updateSound();
            // this.updateMusic();
            this.updateChatSwitch();
        }

        public updateSound():void{
            if(yalla.Sound.sound){
                this._ui.soundIcon.skin = yalla.getSnake('voice_off');
                this._ui.soundTxt.text = 'OFF';
            }else{
                this._ui.soundIcon.skin = yalla.getSnake('voice_on');
                this._ui.soundTxt.text = 'ON';
            }
        }

        // public updateMusic():void{
        //     if(yalla.Sound.music){
        //         this._ui.musicIcon.skin = yalla.getSnake('musicOFF');
        //         this._ui.musicTxt.text = 'OFF';
        //     }else{
        //         this._ui.musicIcon.skin = yalla.getSnake('musicON');
        //         this._ui.musicTxt.text = 'ON';
        //     }
        // }

        public updateChatSwitch(): void{
            var user = yalla.data.snake.UserService.instance.user;
            if(user && user.playerShowInfo && user.playerShowInfo.isInChatOff){
                this._ui.switchIcon.skin = yalla.getSnake('ChatOFF');
                this._ui.switchTxt.text = 'Chat OFF';
            }else{
                this._ui.switchIcon.skin = yalla.getSnake('ChatON');
                this._ui.switchTxt.text = 'Chat ON';
            }
        }

        private onExit(): void  {
            yalla.event.YallaEvent.instance.event(yalla.data.snake.EnumCustomizeCmd.Exit_Back);
        }

        private onClickRules(): void  {
            yalla.Native.instance.mobClickEvent(buryPoint.DGAME_SETTING_RULES);
            yalla.Native.instance.showGameRules(yalla.Global.gameType, 0, 5);
            // yalla.DialogManager.instance.open(new TestPanel(), true, false, false);
        }

        private onClickSound(e:Laya.Event): void  {
            yalla.Sound.sound = !yalla.Sound.sound;
            if (yalla.Sound.sound) yalla.Sound.stopAllSound();
            this.updateSound();

            // if(yalla.Sound.sound) yalla.Native.instance.mobClickEvent(buryPoint.DGAME_SETTING_CLOSESOUND);
            // else yalla.Native.instance.mobClickEvent(buryPoint.DGAME_SETTING_OPENSOUND);
        }

        private onClickChatSwitch(e: Laya.Event): void{
            yalla.data.snake.UserService.instance.sendChatOff(1 - ludo.muteSpectator.isMute);
        }

        // private onClickMusic(e:Laya.Event):void{
        //     yalla.Sound.music = !yalla.Sound.music;
        //     if(yalla.Sound.music){
        //         yalla.Sound.stopMusic('bgm_domino');
        //     }else{
        //         yalla.Sound.playMusic('bgm_domino', 0);
        //     }
        //     this.updateMusic();
        // }

        public clear(): void  {
            Laya.timer.clearAll(this);
            this._ui.ruleBtn.off(Laya.Event.CLICK, this, this.onClickRules);
            this._ui.exitBtn.off(Laya.Event.CLICK, this, this.onExit);
            this._ui.soundBtn.off(Laya.Event.CLICK, this, this.onClickSound);
            this._ui.chatSwitchBtn.off(Laya.Event.CLICK, this, this.onClickChatSwitch);
            // this._ui.musicBtn.off(Laya.Event.CLICK, this, this.onClickMusic);
        }
    }
}