module yalla.view.game.snake {
    export class BaseMap extends ui.snake.baseMapUI {
        private gameUI: ui.snake.gameUI = null;
        private _mapData: yalla.data.snake.MapData;

        public defaultBgId = 30000;
        private _sceneId: number = 30000;
        private _templet: Laya.Templet = null;
        private _bgSk: Laya.Skeleton = null;

        private _prefixSnake = 'snake_group';
        private _prefixLadder = 'ladder_group';
        constructor() {
            super();
        }

        public initUI(gameUI: ui.snake.gameUI): void {
            this.gameUI = gameUI;
            this.mouseThrough = false;
        }

        public setData(val: number, mapData: yalla.data.snake.MapData) {
            // this.sceneId = val;
            this._mapData = mapData;
            this.generateMapElement();
        }

        protected loadSk() {
            if (this._templet) {
                this._bgSk = this._templet.buildArmature(0);
                this.gameUI.bg.addChildAt(this._bgSk, 0);
                this._bgSk.size(Laya.stage.width, Laya.stage.height);
                var sX = Laya.stage.width / 746;
                var sY = Laya.stage.height / 1330;
                if (sX > sY) {
                    this._bgSk.scale(sX, sX);
                } else {
                    this._bgSk.scale(sY, sY);
                }
                this._bgSk.pos(Laya.stage.width / 2, Laya.stage.height / 2);
                this._bgSk.play(0, true);
                yalla.event.YallaEvent.instance.on("stopAni", this, this.stopAni);
                yalla.event.YallaEvent.instance.on("startAni", this, this.startAni);
            }
        }

        set sceneId(val: number) {
            if (val != this.skinId && val > this.defaultBgId) {
                var fevent = yalla.event.YallaEvent.instance;
                fevent.once(`BG_${val}.jpg`, this, () => {
                    if (this.gameUI && this.gameUI.bg) this.gameUI.bg.skin = yalla.File.filePath + `BG_${val}.jpg`;
                })
                yalla.File.getFileByNative(`BG_${val}.jpg`)
                this._sceneId = val;
                this.mapSkinId = val;
                if (yalla.Skin.instance.hasFurniture(val)) {
                    fevent.once(`furniture_${val}.png`, this, () => {
                        if (this.gameUI && this.gameUI.furniture) {
                            var scale = Laya.stage.height / 1334;
                            this.gameUI.furniture.scale(scale, scale);
                            this.gameUI.furniture.skin = yalla.File.filePath + `furniture_${val}.png`;
                        }
                    })
                    yalla.File.getFileByNative(`furniture_${val}.png`)
                }
                if (yalla.Skin.instance.isDynamic(val)) {
                    fevent.once(`BGANI_${val}.sk`, this, () => {
                        this._templet = new Laya.Templet();
                        this._templet.on(Laya.Event.COMPLETE, this, this.loadSk);
                        this._templet.on(Laya.Event.ERROR, this, (e) => {
                            yalla.Debug.log('====BaseGame isDynamic 3=====');
                        })
                        this._templet.loadAni(yalla.File.filePath + `BGANI_${val}.sk`);
                    })
                    fevent.once(`BGANI_${val}.png`, this, () => {
                        yalla.File.getFileByNative(`BGANI_${val}.sk`);
                    })
                    yalla.File.getFileByNative(`BGANI_${val}.png`);
                }
            }
        }

        set mapSkinId(val: number) {
            if (val != this.skinId && val > this.defaultBgId) {
                this._sceneId = val;
                var fevent = yalla.event.YallaEvent.instance;
                fevent.once(`Checkerboard_${val}.png`, this, () => {
                    !this.destroyed && this.setSkin(this._sceneId);
                })
                fevent.once(`Base_${val}.png`, this, () => {
                    !this.destroyed && this.setSkin(this._sceneId);
                })
                yalla.File.getFileByNative(`Checkerboard_${val}.png`);
                yalla.File.getFileByNative(`Base_${val}.png`);
            }
        }

        private setSkin(skinid: number) {
            var [basePath, checkPath] = [yalla.File.cachePath + `/Base_${skinid}.png`, yalla.File.cachePath + `/Checkerboard_${skinid}.png`];
            if (yalla.File.existed(basePath) && yalla.File.existed(checkPath)) {
                this.check.skin = yalla.File.filePath + `Checkerboard_${skinid}.png`;
                this.bg.skin = yalla.File.filePath + `Base_${skinid}.png`;
            }
        }

        /**
         * 生成地图元素数据
         * @param mapData
         *{sceneId,curPos,mapSkinId, row,col, mapList:[{gId,skinId,type,list:[[]],head,end,pos,scaleX}]} 
         */
        private generateMapElement() {
            if (this._mapData && this._mapData.mapJsonData) {
                this.element_box.removeChildren(0, this.element_box.numChildren-1);
                var mapJsonDataList = this._mapData.mapJsonData;
                mapJsonDataList.forEach(element => {
                    this.initElement(element);
                    // if (element.type == 1) {
                    //     this.initSnake(element);
                    // } else if (element.type == 2) {
                    //     this.initLadder(element);
                    // }
                });
                this._mapData.mapJsonData = mapJsonDataList;
                console.log(this._mapData.mapJsonData, '==0==',this.element_box.numChildren);
            }
        }

        private defaultSkinId = 1001;
        /** 蛇 梯子元素 */
        private initElement(element: MapInfo) {
            // var prefix = element.type == 1 ? this._prefixSnake : this._prefixLadder;
            var img: Laya.Image = new Laya.Image();
            // console.log('======initElement=====element.gId=',element.gId);
            img.autoSize = true;
            img.anchorX = 0.5;
            var skinId = element.skinId;
            var skinName = `${element.gId}_${skinId}.png`;//prefix + `_${skinId}.png`;
            var path = yalla.File.filePath + skinName;
            if (this._mapData) {
                var pos = this._mapData.getPos(element.pos);
                if (skinId == this.defaultSkinId) {
                    this.setImg(img, `snake/${skinName}`, pos, element);
                } else {
                    if (this._mapData.isLoadEle(skinId)) {
                        this.setImg(img, path, pos, element);

                    } else {
                        this.setImg(img, `snake/${skinName}`, pos, element);

                        yalla.event.YallaEvent.instance.once(skinName, this, () => {
                            this.setImg(img, path, pos, element);
                            this._mapData.addLoadEle(skinId);
                        });
                        yalla.File.getFileByNative(skinName);
                    }
                }
                // console.log(element.gId, '=====11====', element.pos, pos, img.width, img.height);
                // console.log(element.trace, '==addTestDot===', element.realList);
                this.element_box.addChild(img);
                // this.addTestDot(element);
            }
        }
        private setImg(img:Laya.Image, url:string, pos:any, element:MapInfo): void{
            img.skin = url;
            img.x = pos[0] + img.width / 2;
            img.y = pos[1];
            img.scaleX = element.scaleX; //TODO::这是测试
            this._mapData.setMapTrace(element, pos[0], pos[1],element.scaleX);
        }

        private addTestDot(element: MapInfo): void{
            var nList = element.nList;
            if (!nList) return;
            for (var i = 0; i < nList.length; i++){
                var sp = new Laya.Sprite();
                sp.graphics.drawCircle(nList[i][0], nList[i][1], 4, '#0f0');
                this.ani_box.addChild(sp);
            }
        }
        // /**梯子元素 */
        // private initLadder(element: mapElement) {
        //     var img = Laya.Pool.getItemByClass(this._prefixLadder, Image);
        //     var skinId = element.skinId;
        //     var skinName = this._prefixLadder + `_${skinId}.png`;
        //     var path = yalla.File.filePath + skinName;

        //     if (this._mapData) {
        //         var pos = this._mapData.getPos(element.pos);
        //         if (this._mapData.isLoadEle(skinId)) {
        //             img.skin = path;
        //             img.scaleX = element.scaleX;
        //         } else {
        //             img.skin = `snake/${this._prefixLadder}_${element.gId}.png`;
        //             yalla.event.YallaEvent.instance.once(skinName, this, () => {
        //                 img.skin = yalla.File.filePath + skinName;
        //                 img.scaleX = element.scaleX;
        //                 this._mapData.addLoadEle(skinId);
        //             })
        //             yalla.File.getFileByNative(skinName);
        //         }
        //         img.pos(pos[0],pos[1]);
        //     }
        // }

        /**添加示例棋子  [23.5,919]  [52.5,919]*/
        public addSampleChess(pcolor: Array<playerShowInfo>) {
            var posList = [[20, 27.5], [48, 27.5]];
            for (let i = 0; i < pcolor.length; i++) {
                var chess = ChessManger.instance.getChessById(pcolor[i].chessSkinId);
                if (!chess) {
                    var skinID: number = pcolor[i].chessSkinId;
                    chess = new LudoChess(skinID, pcolor[i].color);
                }
                chess.size(26, 28).scale(0.5,0.5);
                chess.pos(posList[i][0], posList[i][1]);
                this.beginGrid.addChild(chess);
                // ChessManger.instance.addNewChess(chess);
            }
        }

        get skinId(): number {
            return this._sceneId;
        }

        public stopAni() {
            this._bgSk && this._bgSk.paused();
        }
        public startAni() {
            this._bgSk && this._bgSk.play(0, true);
        }

        get sceneId(): number {
            return this._sceneId;
        }

        clear() {
            if (this._templet) {
                this._templet.destroy();
                this._templet.offAll();
                this._templet = null;
            }
            if (this._bgSk) {
                this._bgSk.offAll();
                this._bgSk.destroy();
                this._bgSk = null;
            }
            this._mapData && this._mapData.clear();
            this._mapData = null;
            this.removeSelf();
            this.destroy(true);
        }
    }
}