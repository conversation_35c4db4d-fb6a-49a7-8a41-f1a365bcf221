module yalla.view.game.snake {
    /**
     * 自己相关信息   聊天fastchat   chat  加入语音房间
     */
    export class BottomView {
        private gameUI: ui.snake.gameUI;

        private _isSpectator: boolean = null;
        private _isSendResetMsg: boolean = false;

        public chat: any;
        public readNum: number = 0;
        public activated_id: number = 0;

        constructor(ui: ui.snake.gameUI) {
            this.gameUI = ui;
            this.init();
            this.initEvent();
        }

        private init(): void {
        }

        private initEvent(): void {
            yalla.data.snake.UserService.instance.on(yalla.data.snake.EnumCmd.Game_GetAgoraToken, this, this.voiceTokenInit);
            yalla.data.snake.UserService.instance.on(yalla.data.snake.EnumCmd.Game_GetZegoToken, this, this.voiceTokenInit);
        }

        set isSpectator(val: boolean) {
            if (this._isSpectator == val) return;
            this._isSpectator = val;

            if (!val) {
                this.chat = new LudoChat(this.gameUI.topImg.height);
            } else {
                // this.chat = new LiveChat(this.gameUI.topImg.height);
            }

            // var profileInfo = yalla.Global.ProfileInfo;
            // if (profileInfo.hasHair) {
            //     this.chat.bottom = 20;
            // }
            this.gameUI.addChild(this.chat);
            this.chat.zOrder = 991;
        }

        /**
     * 语音功能及按钮初始化
     */
        public voiceInit() {
            yalla.Debug.log("voiceInit");

            if (!yalla.Voice.instance.token) {
                yalla.data.snake.UserService.instance.getToken();
            }
        }
        public voiceTokenInit(cmd, token: string, cName: string) {
            if (cmd == 92 && yalla.Global.IsUpdateZegoToken) {
                yalla.Native.instance.zegoTokenResponse(token, cName);
                yalla.Global.IsUpdateZegoToken = false;
            } else {
                var voice: yalla.Voice = yalla.Voice.instance;
                if (!voice.joinSuccess) {
                    [voice.token, voice.channelId] = [token, cName];
                    voice.joinTimes = 0;
                    voice.joinGameRoomWithToken(yalla.Global.Account.idx);
                } else {
                    this.joinVoiceCallback();
                }
            }
        }

        public joinVoiceCallback() {
            ludoRoom.instance.muteAudiencePlayers();
            if (yalla.Global.Account.isLimitVisitor && yalla.Global.Account.isPrivate == 1) return;//阿联酋游客账号不可开麦 1.3.6加入模式
            if (yalla.Mute.isBanTalk) return;//后台管理员禁言用户
            if (Laya.LocalStorage.getJSON("voice_open").isOpen && !this._isSpectator) {
                yalla.Voice.instance.enableAudio(() => {
                    this.chat && this.chat.trunVoice();
                    if (!Laya.LocalStorage.getJSON("voice_first")) {
                        if (this.chat && this.chat.voice_btn) {
                            Laya.LocalStorage.setJSON("voice_first", { isFirst: false });
                            var point: Laya.Point = this.chat.voice_btn.localToGlobal(new Laya.Point(), true);
                            point.x += 28;
                            var tip = new VoiceTip(point);
                            this.gameUI.addChild(tip);
                            tip.on("click", this, () => {
                                tip.removeSelf();
                            })
                        }
                    }
                });
            }
        }

        public onChat(msg: any, playerInfo: playerShowInfo, myIdx: number, color) {
            if (!playerInfo) return;
            let isMe = msg.idx == myIdx;
            let side = isMe;
            if (yalla.Font.lan == "ar") side = !side;
            let chat = {
                color: color,
                idx: msg.idx,
                playerShowInfo: playerInfo,
                chat: msg,
                side: side,
                isMe: isMe
            }
            this.chat && this.chat.onChat(chat, isMe, playerInfo.sitNum > -1);
        }

        public onClickGameUI(): void {
            this.hide();
        }

        public hide(): void {
            this.chat && this.chat.hideAllChat();
        }

        private removeEvent(): void {
            yalla.data.snake.UserService.instance.off(yalla.data.snake.EnumCmd.Game_GetAgoraToken, this, this.voiceTokenInit);
            yalla.data.snake.UserService.instance.off(yalla.data.snake.EnumCmd.Game_GetZegoToken, this, this.voiceTokenInit);
        }

        public clear(): void {
            this.removeEvent();
            
            this.chat && this.chat.clear();
            this.chat = null;
        }
    }
}