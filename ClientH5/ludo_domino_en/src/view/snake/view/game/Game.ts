module yalla.view.game.snake {
    import UserService = yalla.data.snake.UserService;
    import Command = yalla.data.snake.Command;
    import GameOperate = yalla.data.snake.GameOperate;
    import PlayerTurnStatus = yalla.data.snake.EnumPlayerTurnStatus;

    export class Game extends ui.snake.gameUI {
        private _topView: TopView;
        private _bottomView: BottomView;
        private _myHanderView: MyHanderView;
        private _resultView: yalla.view.result.snake.Result;
        private _baseMap: BaseMap;
        private _chessBubbleManager: ChessBubbleManager;
        private throwBtn: Laya.Image;
        public playerHash: Object = {};
        private _chessHash: Object = {};
        private _user: yalla.data.User;
        private _room: yalla.data.snake.Room;
        /**是否购买钻石  */
        private _buyCoin: boolean = false;
        /**当前激活玩家 */
        private activated_id: number = 0;
        public playerStatus: number = 0;
        /**骰子转动时间 */
        public diceTime: number = 400;
        private waitTime: number = 5000;
        private timeValue: number = 2000; //cdAni 剩余时间开始播放倒计时音效
        private topMc_top: number = 0;

        constructor() {
            super();
            yalla.Global.IsGameOver = false;
            this.initUI();
            this.initEvent();
        }

        private initUI(): void {
            this._topView = new TopView(this);
            this._bottomView = new BottomView(this);
            this._myHanderView = new MyHanderView(this);
            this._baseMap = new BaseMap();
            this._baseMap.initUI(this);
            this._chessBubbleManager = new ChessBubbleManager();

            yalla.SkAnimation.instance.initAnimation(this._baseMap.ani_box);

            this._user = UserService.instance.user;
            this._room = UserService.instance.room;
            this._myHanderView.initData(this._room, this._user);
            this._topView.init();
            this.topMc_top = this._topView.topUI['topMc'].top;

            if (yalla.data.IsTestCode) {
                UserService.instance.testData();
                this.handleEnterRoom(null);
                this.handleGameDataInfo(this._room);
                this.activated_id = Global.Account.idx;
                this.changeChessLayer();
            }
        }

        private adapterUI() {
            var vBottom = 0;
            var hHei = 0;
            var profileInfo = yalla.Global.ProfileInfo;
            if (profileInfo.hasHair) {
                hHei = profileInfo.hairHeight ? profileInfo.hairHeight : 0;
                vBottom = hHei ? hHei / yalla.Screen.screen_scale : 50;
                this._topView.topUI['topMc'].top = this.topMc_top + vBottom;
                this.topImg.height = 105 + vBottom;
                this.myHander.bottom = 180;
            } else {
                this.myHander.bottom = 130;
            }
            this.players_layer.bottom = this.myHander.bottom;
            this.giftAniBox.bottom = this.myHander.bottom;
            this._baseMap.centerY = -(this.myHander.bottom + 220 + 60 - (this._topView.topUI['topMc'].top + 100)) / 2; //yalla.Screen.screen_top_height / 2;
        }

        private initEvent(): void {
            this.quick_buy_btn.on(Laya.Event.CLICK, this, this.buyCoin);
            UserService.instance.on(yalla.data.snake.EnumCmd.Game_QuitRoom, this, this.handleQuitGame);
            UserService.instance.on(yalla.data.snake.EnumCmd.Game_Update_Player_Coin, this, this.handleUpdatePlayerCoin);
            UserService.instance.on(yalla.data.snake.EnumCmd.Game_Chat, this, this.handleChat);

            this.on(Laya.Event.CLICK, this, this.onClickGameUI);
            yalla.event.YallaEvent.instance.on(yalla.data.snake.EnumCustomizeCmd.Exit_Back, this, this.onClickBack);
            yalla.event.YallaEvent.instance.on(yalla.data.snake.EnumCustomizeCmd.Game_Chat_My, this, this.handleChatMy);
            yalla.event.YallaEvent.instance.on(yalla.data.snake.EnumCustomizeCmd.Game_ChatOFF_My, this, this.handleChatOff);
            yalla.event.YallaEvent.instance.on(yalla.data.snake.EnumCustomizeCmd.Snake_RoundEnd, this, this.handleNextRound);
            yalla.event.YallaEvent.instance.on(yalla.data.snake.EnumCustomizeCmd.Snake_PlayMsg, this, this.handlePlayMsg);
            yalla.Voice.instance.on("change", this, this.voiceChange);
            yalla.Voice.instance.on("join", this, this.joinVoiceCallback);
            yalla.Native.instance.on(yalla.Native.instance.Event.SPEAKER, this, this.handleSpeakResponse);
            yalla.common.InteractiveGiftAnimation.Instance.on(yalla.common.InteractiveGiftAnimation.Instance.Event.UpdateGiftIcon, this, this.updateGiftIcon);
            yalla.Mute.event.on(yalla.Mute.MUTE, this, this.onMute)//监听举报
            yalla.Mute.event.on(yalla.Mute.UNMUTE, this, this.unMute);//监听取消举报
        }

        private removeEvent(): void {
            this.quick_buy_btn.off(Laya.Event.CLICK, this, this.buyCoin);
            this._baseMap && this._baseMap.initBox.off(Laya.Event.CLICK, this, this.onClickChooseChess_init);

            UserService.instance.off(yalla.data.snake.EnumCmd.Game_QuitRoom, this, this.handleQuitGame);
            UserService.instance.off(yalla.data.snake.EnumCmd.Game_Update_Player_Coin, this, this.handleUpdatePlayerCoin);
            UserService.instance.off(yalla.data.snake.EnumCmd.Game_Chat, this, this.handleChat);

            this.off(Laya.Event.CLICK, this, this.onClickGameUI);
            yalla.event.YallaEvent.instance.off(yalla.data.snake.EnumCustomizeCmd.Exit_Back, this, this.onClickBack);
            yalla.event.YallaEvent.instance.off(yalla.data.snake.EnumCustomizeCmd.Game_Chat_My, this, this.handleChatMy);
            yalla.event.YallaEvent.instance.off(yalla.data.snake.EnumCustomizeCmd.Game_ChatOFF_My, this, this.handleChatOff);
            yalla.event.YallaEvent.instance.off(yalla.data.snake.EnumCustomizeCmd.Snake_RoundEnd, this, this.handleNextRound);
            yalla.event.YallaEvent.instance.off(yalla.data.snake.EnumCustomizeCmd.Snake_PlayMsg, this, this.handlePlayMsg);

            yalla.Voice.instance.off("change", this, this.voiceChange);
            yalla.Voice.instance.off("join", this, this.joinVoiceCallback);
            yalla.Native.instance.off(yalla.Native.instance.Event.SHARE, this, this.shareResponse);
            yalla.Native.instance.off(yalla.Native.instance.Event.SPEAKER, this, this.handleSpeakResponse);
            yalla.common.InteractiveGiftAnimation.Instance.off(yalla.common.InteractiveGiftAnimation.Instance.Event.UpdateGiftIcon, this, this.updateGiftIcon);

            yalla.Mute.event.off(yalla.Mute.MUTE, this, this.onMute)//监听举报
            yalla.Mute.event.off(yalla.Mute.UNMUTE, this, this.unMute);//监听取消举报

            Laya.timer.clear(this, this.frameLoopName);
            Laya.timer.clearAll(this);
        }

        private onMute(idx: number) {
            var player: GamePlayer = this.gamePlayer(idx);
            player && yalla.Voice.instance.muteRemoteAudioStream(idx, true, (val, uid) => {
                player = this.gamePlayer(uid);
                if (val == 0 && player) {
                    this.timer.clear(this, player.pause);
                    player.isMute = true;
                    player.playVoiceAni(0, false, "stop");
                }
            })
        }
        private unMute(idx: number) {
            var player: GamePlayer = this.gamePlayer(idx);
            if (player) {
                yalla.Voice.instance.muteRemoteAudioStream(idx, false, (val, uid) => {
                    player = this.gamePlayer(uid);
                    if (val == 0 && player) {
                        player.isMute = false;
                        player.pause();
                    }
                })
            }
        }

        private trustDely() {
            this.addTrustEvent();
            this.trust_wait.visible = false;
            this.trust.selected = false;
        }

        private addTrustEvent() {
            this.bg.on(Laya.Event.CLICK, this, this.onTrust);
            this.bodyMc.on(Laya.Event.CLICK, this, this.onTrust);
            this.trusteeship.on(Laya.Event.CLICK, this, this.onTrust);

        }
        private removeTrustEvent() {
            this.bg.off(Laya.Event.CLICK, this, this.onTrust);
            this.trust.off(Laya.Event.CLICK, this, this.onTrust);
            this.bodyMc.off(Laya.Event.CLICK, this, this.onTrust);
        }

        public update() {
            this._topView && this._topView.updateView();
            this._myHanderView && this._myHanderView.updateView();
            this._user && yalla.common.InteractiveGift.Instance.updateMoney(this._user.money, this._user.gold);
        }

        private openFrameLoop(): void {
            Laya.timer.clear(this, this.frameLoopName);
            var isHonorRoyal = UserService.instance.room.isHonorRoyal;
            yalla.Debug.log("===snake是否打开frameLoop==isHonorRoyal=" + isHonorRoyal);
            if (isHonorRoyal) {
                Laya.timer.frameLoop(1, this, this.frameLoopName);
            }
        }
        private frameLoopName(): void {
            for (var k in this.playerHash) {
                var data = this.playerHash[k].data;
                var canShowRLNameAnimation = yalla.util.canShowRLNameAnimation(data);
                if (canShowRLNameAnimation) this.playerHash[k].frameLoopName();
            }

            if (this._resultView && this._resultView.displayedInStage) {
                this._resultView.frameLoopName();
            }
        }


        /**
         * 票据登录后执行，或者网页端调试直接出Game初始化后执行(通知外部隐藏匹配界面)
         * @param msg 
         */
        private handleEnterRoom(msg: any): void {
            this._user = UserService.instance.user;
            this._room = UserService.instance.room;
            this.waitTime = this._room.throwDiceWaitTime ? this._room.throwDiceWaitTime : yalla.data.SnakeGameConfig.WaitTime;

            if (this._resultView) {
                this._resultView.backHallClear();
                this._resultView.removeSelf();
            }

            if (yalla.Native.instance.deviceType == DeviceType.Android) {
                yalla.Native.instance.removeMatchView();
            }
            // this.clearPlayer();

            this.hideThrowJump();
            this._myHanderView.initData(this._room, this._user);
            this._topView.setRank(this._room);

            this.update();

            var myColor = 0;
            var isSpectator = this._room.watchIdx > 0;
            if (!isSpectator) {
                var playershowInfo = this._room.getColorByIdx(yalla.Global.Account.idx);
                if (playershowInfo) myColor = playershowInfo.playColor;
                this._bottomView.voiceInit();
                this._myHanderView.initView(isSpectator);
                this._myHanderView.updateUndoUI();
            }
            this.adapterUI();
            this._bottomView.isSpectator = isSpectator;
            this._topView.isSpectator = isSpectator;

            var sitPlayers = this._room.sitPlayers;
            var i, j, sitNum;
            if (!this.playerHash) this.playerHash = {};
            for (i = 0; i < sitPlayers.length; i++) {
                var playerInfo: playerShowInfo = sitPlayers[i];
                var idx = playerInfo.fPlayerInfo.idx;
                var player: GamePlayer = this.gamePlayer(idx);
                // yalla.Debug.log(!player + "=====进入房间 玩家信息====realRoyLevel=" + playerInfo.realRoyLevel);
                if (playerInfo.fPlayerInfo.idx == this._user.idx) sitNum = 0;
                else sitNum = 3;
                if (!player) {
                    player = new GamePlayer(playerInfo.playColor, sitNum, false, playerInfo, GameType.SNAKEANDLADER);
                    player.bottom = -70;
                    player.head.gameUI = this.parent;
                    this.players_layer.addChild(player);
                    this.playerHash["player_" + idx] = player;
                }
                this.updatePlayer(playerInfo, playerInfo.playColor);
            }
            this.openFrameLoop();
        }

        /**播放消息 */
        private handlePlayMsg(msg: any, isPlayNext: boolean = true): void {
            !this._user && (this._user = UserService.instance.user);
            !this._room && (this._room = UserService.instance.room);
            if (!msg || !msg.command) return;
            // console.log(`[${yalla.getTimeHMS()}][nextMsg:][${msg.command}]`, msg);
            switch (msg.command) {
                case Command.QUICK_START:
                    this.handleEnterRoom(msg);
                    break;
                case Command.QUIT_ROOM:
                    this.handleQuitGame(msg);
                    break;
                case Command.GAME_OPERATE:
                    this.handleGameOperate(msg);
                    break;
                case Command.GAME_EVENT:
                    this.handleGameEvent(msg);
                    isPlayNext && UserService.instance.nextMsg();
                    break;
                case Command.GAME_DATA_INFO:
                    this.handleGameDataInfo(msg);
                    break;
                case Command.GAMEPLAYERSTATUS:
                    this.hanldePlayerStatus(msg);
                    break;
                case Command.GAMERESULT:
                    Global.IsGameOver = true;
                    this.gameOver(msg);
                    break;
                case Command.PLAYER_CHAT_ALL:
                    this.handleChat(msg);
                    break;
                case Command.GAME_GIFT_CNF_LIST:
                    this.handleUpdateGiftCnf(msg);
                    break;
                case Command.GAME_GIFT_SEND:
                    this.handleRevGift(msg);
                    break;
            }
        }

        private handleGameEvent(msg: any): void {
            if (!msg || !msg.event) return;
            this.moveChessIdx = 0;
            this.removeChessEvent();

            switch (msg.event) {
                case yalla.data.snake.GameEvent.SAL_CHESS_BORN:
                    this.leaveInitPos(msg.idx);
                    break;
                case yalla.data.snake.GameEvent.SAL_CHESS_MOVE://移动
                    this.moveChess(msg);
                    break;
                case yalla.data.snake.GameEvent.SAL_CHESS_MOVE_SNAKE://蛇吻
                    this.snakeKiss(msg);
                    break;
                case yalla.data.snake.GameEvent.SAL_CHESS_MOVE_LADDER://爬梯
                    this.climbLadder(msg);
                    break;
            }
        }

        private handleGameDataInfo(msg: any): void {
            yalla.Debug.log('===handleGameDataInfo===');
            yalla.Debug.log(msg);
            UserService.instance.mapData.initMapJsonData(msg.gameMapInfo);
            if (this._baseMap && !this._baseMap.displayedInStage) {
                this.bodyMc.addChild(this._baseMap);
            }
            if (this._baseMap.sceneId != this._room.sceneId) {
                this._baseMap.setData(this._room.sceneId, UserService.instance.mapData);
            }
            this.createChess();
        }

        /**
         * 玩家主动操作
         * @param msg 
         */
        public handleGameOperate(msg: any = { operate: 200 }) {
            var player;
            switch (msg.op) {
                case GameOperate.THROW://掷骰子
                    yalla.Debug.log("=====玩家主动操作得到响应 掷骰子====");
                    yalla.Debug.log(msg);
                    this.playAndShowDice(msg);
                    break;
                case GameOperate.RESET_THROW://重置投掷
                    yalla.Debug.log("=====玩家主动操作得到响应 重置投掷====");
                    yalla.Debug.log(msg);
                    this._myHanderView.resetDice(msg, this.gamePlayer(this.activated_id));
                    break;
                case GameOperate.SYSTEM_TRUST://系统托管
                    yalla.Debug.log("=====玩家主动操作得到响应 系统托管====");
                    yalla.Debug.log(msg);
                    player = this.gamePlayer(yalla.Global.Account.idx);
                    var v = msg.idx == yalla.Global.Account.idx && !this._room.isTrust;
                    if (v) {
                        this._room.isTrust = true;
                        this.updateTrust(player, true);
                        this._myHanderView.showTrust(false);
                        this.addTrustEvent();
                        if (player) {
                            // player.head.trustee = true;
                            // player.UI.playerBg.visible = false;
                            this.throwBtn && this.throwBtn.on("click", this, this.onTrust);
                            player.head.on("click", this, this.onTrust);
                        }
                    }
                    break;
                case GameOperate.SYSTEM_TRUST_CANCEL://取消托管
                    yalla.Debug.log("=====玩家主动操作得到响应 取消托管====");
                    yalla.Debug.log(msg);
                    var v = msg.idx == yalla.Global.Account.idx && this._room.isTrust == true;
                    if (v) {
                        this.hideTrust(true);
                        this._room.isTrust = false;
                        player = this.gamePlayer(yalla.Global.Account.idx);
                        if (player) {
                            this.throwBtn && this.throwBtn.off("click", this, this.onTrust);
                            player.head.off("click", this, this.onTrust);
                            this.updateTrust(player, false);
                            // player.head.trustee = false;
                            // player.UI.playerBg.visible = true;
                            // this.activated_id != yalla.Global.Account.idx ? player.showName() : player.hideName();
                        }
                    }
                    break;
                case GameOperate.CHAT_OFF://屏蔽聊天
                case GameOperate.CHAT_OFF_CANCEL://取消屏蔽聊天
                    this.setChatOff(msg);
                    break;
            }
            return this;
        }

        private setChatOff(msg: any): void {
            // console.log('=====屏蔽聊天======',msg);        
            var isChatOff = (msg.op == GameOperate.CHAT_OFF) ? true : false;
            if (this._user && this._user.idx == msg.idx) {
                this._user.updateChatOff(isChatOff ? 1 : 0);
            }
            var player: GamePlayer = this.gamePlayer(msg.idx);
            if (player) player.isInChatOff = isChatOff
            if (this._topView && this._topView.setting) this._topView.setting.updateChatSwitch();
        }

        /**
         * 玩家游戏状态 50
         */
        private moveChessIdx: number = 0;
        public hanldePlayerStatus(msg: any) {
            this.playerStatus = msg.status;
            if (this.activated_id == null) {//第一次玩家
                if (this.playerStatus === PlayerTurnStatus.RETHROW_START) {
                    this.resetStart(msg, this.activated_id);
                    return;
                }
                // yalla.Debug.log(msg.idx+'===hanldePlayerStatus 0===='+yalla.Global.Account.idx);
                if (msg.idx != yalla.Global.Account.idx) {
                    var player: GamePlayer = this.gamePlayer(msg.idx);
                    if (player) {
                        player.waitCdStart(this.waitTime, this.timeValue);
                        player.showDice();
                    }
                    ludo.Undo.instance.newRound();
                    this._myHanderView.updateUndoUI();

                }
                this.activated_id = msg.idx;
            }
            if (this.activated_id != msg.idx && this.playerStatus == PlayerTurnStatus.THROW_START) {//切换玩家
                let lastPlayer: GamePlayer = this.gamePlayer(this.activated_id);
                let nextpPlayer: GamePlayer = this.gamePlayer(msg.idx);//下一个玩家

                //TODO::为了避免服务端移动时间和客户端不一样，只要再切换玩家时，就直接刷新到最新位置
                this.updateChessPos();

                nextpPlayer && nextpPlayer.newRound().playRoleAni();
                if (msg.idx == yalla.Global.Account.idx) {
                    yalla.Sound.playSound("my_turn");
                    ludo.Undo.instance.newRound();
                    this._myHanderView.updateUndoUI();

                } else {
                    yalla.Sound.playSound("opp_turn");
                    nextpPlayer && nextpPlayer.showDice();
                }
                lastPlayer && lastPlayer.stopAndShowDice(7).waitCdEnd();
                if (this.activated_id == yalla.Global.Account.idx) {
                    this.updateTrust(lastPlayer, this._room.isTrust);
                    if (this._room.isTrust) {
                        this._myHanderView.showTrust();
                    }
                    ludo.Undo.instance.newRound();
                    this._myHanderView.updateUndoUI();
                    lastPlayer && lastPlayer.clearDice(this._room.isTrust);
                } else {
                    lastPlayer && lastPlayer.clearDice(false).hideDice();
                }
                // yalla.Debug.log('==头像框CD  更新玩家状态 status=' + this.playerStatus);
                nextpPlayer && nextpPlayer.waitCdStart(this.waitTime, this.timeValue);
                this.activated_id = msg.idx;
            }

            let activatedPlayer: GamePlayer;
            switch (this.playerStatus) {
                case PlayerTurnStatus.THROW_START://("玩家" + res.idx + "开始投掷");//添加点击事件 倒计时动画
                    this.throwStart();
                    break;
                case PlayerTurnStatus.THROW_END: //("玩家" + res.idx + "投掷结束");//移除点击事件
                    this.throwEnd();
                    this.changeChessLayer(this.getChessByIdx(msg.idx));
                    break;
                case PlayerTurnStatus.RETHROW_START:
                    // yalla.Debug.log(yalla.getTimeHMS()+'======可以选择棋子=====');    
                    this.resetStart(msg, this.activated_id);
                    break;
                case PlayerTurnStatus.RETHROW_END: //("是否重置结束");
                    // yalla.Debug.log(yalla.getTimeHMS()+'======undo结束=====');       
                    this.resetEnd();
                    break;
                case PlayerTurnStatus.CHESSMOVE_START://("移动棋子开始");
                    this.moveChessIdx = 0;
                    activatedPlayer = this.gamePlayer(this.activated_id);
                    // yalla.Debug.log('==头像框CD  移动棋子开始 status=6===');
                    activatedPlayer && activatedPlayer.waitCdEnd().waitCdStart(this.waitTime, this.timeValue);
                    break;
                case PlayerTurnStatus.CHESSMOVE_END://("移动棋子结束");
                    if (this.activated_id) {
                        activatedPlayer = this.gamePlayer(this.activated_id);
                        activatedPlayer && activatedPlayer.waitCdEnd();
                    }
                    break;
            }
        }

        /*
        * 开始掷骰子
        */
        throwStart() {
            if (!this.activated_id) return;
            // yalla.Debug.log('==头像框CD  开始投掷骰子 status=0===');
            var player: GamePlayer = this.gamePlayer(this.activated_id);
            if (player) player.waitCdStart(this.waitTime, this.timeValue);
            if (this.activated_id == yalla.Global.Account.idx && this._room && !this._room.isTrust) {//添加骰子的点击事件 非托管
                this.throwBtn && this.throwBtn.once(Laya.Event.CLICK, this, this.clickDice);
                this._myHanderView.showThrowJump();
            }
        }

        /**
         * 结束掷骰子
         */
        protected throwEnd() {
            if (!this.activated_id)
                return;

            // yalla.Debug.log('==头像框CD  结束投掷骰子 status=1===');
            let player = this.gamePlayer(this.activated_id);
            if (player) player.waitCdEnd();
            if (this.activated_id == yalla.Global.Account.idx) {//移除骰子的点击事件
                this.hideThrowJump();
            }
        }
        private hideThrowJump(): void {
            this.throwBtn && this.throwBtn.off("click", this, this.clickDice);
            this._myHanderView && this._myHanderView.hideThrowJump();
        }

        /**
         * 重置开始
         *1.断线重连的时候 服务端主动推送 msg中有costDiamond字段 表示修正本地文字显示,不需要转动undo的雷达
         *2.重置开始的时候 msg中有msgindex字段而无costDiamond 字段 需要转动undo的雷达
         */
        public resetStart(msg: any, activated_id: number) {
            this._myHanderView && this._myHanderView.resetStart(msg, activated_id);
            if (msg.msgindex && msg.idx != yalla.Global.Account.idx) {
                var player: GamePlayer = this.gamePlayer(msg.idx);
                if (player) player.waitCdEnd().waitCdStart(this.waitTime, this.timeValue);
            }
        }

        public resetEnd() {
            if (!this.activated_id)
                return;
            if (this.activated_id == yalla.Global.Account.idx) {
                this._myHanderView && (this._myHanderView.resetEnd());
            }
        }

        /**
         * GameEvent 2
         * 更新角色颜色
         * 语音状态(进入游戏前请求了加入语音聊天房间)
         */
        private updatePlayer(playerInfo: playerShowInfo, color: number): void {
            console.log(playerInfo.fPlayerInfo.idx + "=idx===player用户更新=color=" + color);
            var player: GamePlayer = this.gamePlayer(playerInfo.fPlayerInfo.idx);
            var is_Trust: boolean = false;
            if (player) {
                player.setPlayerInfo(playerInfo, this.activated_id);
                player.resetHeadScale();
                if (playerInfo.fPlayerInfo.idx == yalla.Global.Account.idx) {
                    this._room.isTrust = Boolean(playerInfo.isInSysTrust);
                    this._topView.updateChatSwitch();
                    player.isInChatOff = Boolean(ludo.muteSpectator.isMute);
                    player.UI && (this.throwBtn = player.UI.dice_bg);
                    this.updateTrust(player, this._room.isTrust);

                    // if (playerInfo.winIndex < 0) {
                    if (this._room.isTrust) {
                        // player.hideName();
                        this._myHanderView.showTrust();
                        this.throwBtn && this.throwBtn.on("click", this, this.onTrust);
                        player.head.on("click", this, this.onTrust);
                    } else {
                        this.hideTrust(true);
                    }
                    // } else {
                    //     if (this._room.isTrust) {
                    //         this._room.isTrust = false
                    //         this.hideTrust(true);
                    //     }
                    // }
                    // player.head.trustee = this._room.isTrust;
                    // player.UI.playerBg.visible = !this._room.isTrust;
                    this._bottomView.chat && this._bottomView.chat.trunVoice();
                    is_Trust = this._room.isTrust;
                } else {
                    player.UI && (player.UI.dice_bg.mouseThrough = true);
                }
                // 1.2.6 修改
                var nums: Array<number> = playerInfo.diceNum;
                if (nums && nums.length > 0) {
                    player.flushDice(nums, is_Trust);
                } else {
                    player.clearDice(is_Trust);
                }
                //TODO::1.4.3 重启服务器，用户颜色更新后需要重新刷新
                player.color = color;

                player.UI.playerBg.visible = true;
                player.UI.playerBg.skin = `game/playerBg_${color}.png`;

                player.UI.flag.visible = true;
                player.UI.flag.removeChildren();
                var img = new Laya.Image();
                img.skin = `snake/flag_${color}.png`;
                img.pos(0, 2);
                player.UI.flag.addChild(img);
            }
        }

        /**
         * 播放骰子动画
         * @param msg 
         */
        private playAndShowDice(msg): void {
            if (!this.activated_id) return;
            if (msg.result == 1) {
                this.removeChessEvent();
                let player: GamePlayer = this.gamePlayer(this.activated_id);
                let num: number = msg.throwNum;
                if (this.activated_id == yalla.Global.Account.idx) {
                    if (player && player.dice) {
                        this.throwBtn && this.throwBtn.off("click", this, this.clickDice);
                    }
                }
                if (yalla.Global.isFouce) {
                    if (player) {
                        player.waitCdEnd().waitCdStart(this.waitTime, this.timeValue);
                        player.dice && player.dice.play();
                        this.timer.once(this.diceTime, this, this.stopAndShowDice, [player, num, msg.moveStatus]);
                    }
                } else {
                    this.stopAndShowDice(player, num, msg.moveStatus);
                }
            }
        }

        private clickDice() {
            UserService.instance.clickDice();
            this.resetEnd();
            if (this.activated_id == yalla.Global.Account.idx) {//优化点击立即播放筛子动画
                var player: GamePlayer = this.gamePlayer(this.activated_id);
                player && player.dice && player.dice.play();
            }
        }

        private stopAndShowDice(player: GamePlayer, num: number, moveStatus: number = 0) {
            //TODO::ludo有多个骰子，但是snake只有一个，这里就不隐藏托管
            // if (this._room.isTrust && this.activated_id == yalla.Global.Account.idx)
            //     this.hideTrust(false);
            player && player.stopAndShowDice(num);

            if (this.activated_id == yalla.Global.Account.idx && !moveStatus) {
                // this.isMoveChess = true;
                this.moveChessIdx = this.activated_id;
                this.chooseChessStart();
            }
        }

        /**
         * 调整地图方位 创建棋子
         */
        private createChess() {
            if (!this._chessHash) this._chessHash = {};
            var players = this._room.players;
            var len = players.length;
            var isSamePos = this._room.getIsSamePos();
            var initPos = [[29, 916], [72, 916]];
            // var initWid = 41, initHei = 45;

            for (var i = 0; i < len; i++) {
                var idx = players[i].fPlayerInfo.idx;
                var chess: Chess = this.getChessByIdx(idx);//this._chessHash[idx];
                var chessData = this._room.getChessData(idx);
                var color = players[i].playColor;
                if (!chess) {
                    chess = new Chess(color, idx, this._chessBubbleManager);
                    //TODO::这里要判定是否在预备点  [[22, 903], [50, 903]];  10 11
                    // yalla.Debug.log(idx + '=====Game.curPos=' + chessData.curPos);
                    chess.initData(players[i].chessSkinId, chessData.curPos);
                    this._baseMap.chess_box.addChild(chess);
                    this._chessHash[idx] = chess;
                    chess.on(Laya.Event.CLICK, this, this.onClickChooseChess);
                }
                if (chess.color != color) {
                    chess.color = color;
                    chess.skinId = players[i].chessSkinId;
                    chess.name = color.toString();
                }
                chess.posIndex = chessData.curPos;
                chess.targetPos = chessData.curPos;

                var posList;
                if (chessData.curPos < 1) {
                    posList = initPos[i];
                    // chess.setSize(initWid, initHei);
                    chess.setScale(yalla.data.InitScale);
                } else {
                    if (isSamePos) {
                        // chess.setSize(initWid, initHei);
                        chess.setScale(yalla.data.InitScale);
                        posList = chess.getPilePos(chess.posIndex, i == 0 ? -12 : 12);
                    }
                    else {
                        chess.setScale(1);
                        // chess.setSize(chess.Wid, chess.Hei);
                        posList = chess.getPos(chess.posIndex);
                    }
                }
                chess.pos(posList[0], posList[1]);
            }
            this._baseMap && this._baseMap.initBox.on(Laya.Event.CLICK, this, this.onClickChooseChess_init);
        }

        private snakeKiss(msg: any): void {
            var chess = this.getChessByIdx(msg.idx);
            if (!chess) return;//无法移动
            var mapData = this.getMapDataByPos(msg.oldPos);
            yalla.Debug.log('====Game.snakeKiss====' + chess.posIndex + '--' + chess.targetPos);
            // yalla.Debug.log(msg);

            if (!mapData) return;
            chess.setData(msg, mapData);
            this.updateOtherChessState(chess);
        }
        /** num 指定位置 */
        private climbLadder(msg: any): void {
            var chess = this.getChessByIdx(msg.idx);
            if (!chess) return;//无法移动
            var mapData = this.getMapDataByPos(msg.oldPos, 2);
            yalla.Debug.log('====Game.climbLadder====' + chess.posIndex + '--' + chess.targetPos);
            // yalla.Debug.log(msg);
            chess.setData(msg, mapData);
            this.updateOtherChessState(chess);
        }

        /**走棋子   num 是走到的格子位置 */
        private moveChess(msg: any) {
            var endPos = msg.targetPos;
            var chess: Chess = this.getChessByIdx(msg.idx) as Chess;
            if (!chess || endPos > yalla.data.Max_Num) return;//无法移动
            yalla.Debug.log('====Game.moveChess====' + chess.posIndex + '--' + chess.targetPos);
            // yalla.Debug.log(msg);
            chess.setData(msg, endPos);

            this.updateOtherChessState(chess);
        }

        /**
         * 如果棋子移到相同位置，则重新刷新
         */
        private handleNextRound(chess: Chess): void {
            var isSamePos = this.getIsSamePos(chess);
            // console.log(isSamePos,'=====handleNextRound======',chess.name);
            if (isSamePos && Global.game_state != yalla.data.GameState.State_Sleep) {
                this.movePilePos(isSamePos, chess);
                Laya.timer.once(150, this, () => {
                    UserService.instance.isQueue = false;
                    UserService.instance.nextMsg();
                });
            } else {
                UserService.instance.isQueue = false;
                UserService.instance.nextMsg();
            }
        }
        private movePilePos(isSamePos: boolean, chess: Chess = null) {
            var mapData = UserService.instance.mapData;
            if (!mapData) return;
            // var i = 0, wid = 41, hei = 45;
            var i = 0;
            var chessSide = 1;
            if (chess) chessSide = mapData.getSide(chess.targetPos);
            for (var key in this._chessHash) {
                var cItem: Chess = this._chessHash[key];
                var vx = i == 0 ? -12 : 12;
                if (isSamePos && chess) {
                    if (chess.name == cItem.name) {
                        vx = chessSide == 1 ? 12 : -12;
                    } else {
                        vx = chessSide == 1 ? -12 : 12;
                    }
                    // if (chess) vx = chess.name == cItem.name ? 12 : -12;
                }
                if (mapData.snakeMapList[cItem.targetPos]) {
                    var posList = cItem.getPilePos(cItem.targetPos, vx);
                    var isWake = yalla.Global.game_state != yalla.data.GameState.State_Sleep;
                    if (isWake) {
                        // var goal = { width: wid, height: hei, x: posList[0], y: posList[1] };
                        var goal = { scaleX: yalla.data.InitScale, scaleY: yalla.data.InitScale, x: posList[0], y: posList[1] };
                        var tw = Laya.Tween.to(cItem, goal, 150, null, Laya.Handler.create(this, function (cs: Chess) {
                            // cs.setSize(wid, hei);
                            cs.setScale(yalla.data.InitScale);
                        }, [cItem]), 0, true, true);
                    } else {
                        cItem.pos(posList[0], posList[1]);
                        // cItem.setSize(wid, hei); 
                        cItem.setScale(yalla.data.InitScale);
                    }
                }
                i += 1;
            }
        }

        /**
         * 一个棋子飞行后，另一个棋子恢复大小
         * @param chess 
         */
        private updateOtherChessState(chess: Chess): void {
            for (var key in this._chessHash) {
                var cItem: Chess = this._chessHash[key];
                if (cItem.name != chess.name && cItem.posIndex > 0) {
                    cItem.setScale(1);
                    cItem.checkPos(cItem.targetPos);
                }
            }
        }

        /**
         * 离开起点
         */
        private leaveInitPos(idx): void {
            var chess = this.getChessByIdx(idx);
            if (!chess) return;
            yalla.Sound.playSound("token_move");
            chess.posIndex += 1;
            chess.targetPos = chess.posIndex;

            if (yalla.Global.game_state == yalla.data.GameState.State_Sleep) {
                this.handleNextRound(chess);
            } else {
                var self = this;
                var posList = chess.getPos(chess.posIndex);
                // var wid = chess.Wid, hei = chess.Hei;
                var scale = 1;
                var isSamePos = this.getIsSamePos(chess);
                if (isSamePos) {
                    // wid = 41; hei = 45;
                    scale = yalla.data.InitScale;
                }
                // var goal = { width: wid, height: hei, x: posList[0], y: posList[1] };
                var goal = { scaleX: scale, scaleY: scale, x: posList[0], y: posList[1] };
                var tw = Laya.Tween.to(chess, goal, yalla.data.MoveTime, null, Laya.Handler.create(this, function (cs) {
                    // cs.setSize(wid, hei);
                    cs.setScale(scale);
                    Laya.Tween.clear(tw);
                    self.handleNextRound(cs);
                }, [chess]));
            }
        }

        private onClickChooseChess_init(e: Event): void {
            e.stopPropagation();
            if (this.moveChessIdx != this._user.idx || (this._room && this._room.isTrust)) return;
            var chess: Chess = this.getChessByIdx(this.moveChessIdx);
            if (chess.posIndex > 0) return;

            UserService.instance.sendChooseMsg();
            this.moveChessIdx = 0;
            this.onClickChooseChess(e);
        }

        private onClickChooseChess(e: Event): void {
            e.stopPropagation();
            var chess: any = e.currentTarget;
            if (this.moveChessIdx != this._user.idx || (this._room && this._room.isTrust) || chess.idx != this._user.idx) return;
            UserService.instance.sendChooseMsg();
            this.moveChessIdx = 0;
        }

        public chooseChessStart() {
            if (this.activated_id != this._user.idx) return;
            // console.log('======chooseChessStart======!this._room.isTrust:',!this._room.isTrust);
            if (!this._room.isTrust) {
                var chess: Chess = this.getChessByIdx(this._user.idx);
                if (chess) {
                    chess.choose = true;
                }
            }
        }

        /** 改变棋子层级  1、都在起点，红棋子层级在上   2.当前操作玩家棋子在上  3.如果传入棋子，则棋子在上*/
        private changeChessLayer(chess: Chess = null) {
            if (chess) chess.zOrder = 2;
            for (var key in this._chessHash) {
                var cItem = this._chessHash[key];
                if (chess && chess.idx != cItem.idx) cItem.zOrder = 1;
            }
        }

        /**切换用户后，校验棋子位置 */
        private updateChessPos() {
            var isSamePos = false;
            var tempPos = 0;
            var moveChess = null;
            for (var key in this._chessHash) {
                var chess: Chess = this._chessHash[key];
                if (chess.isMoving) moveChess = chess;
                if (chess.targetPos > 0 && tempPos == chess.targetPos) {
                    isSamePos = true;
                }
                tempPos = chess.targetPos;
            }
            // console.log(isSamePos,'===updateChessPos 0=====',moveChess);
            if (moveChess) {
                if (!isSamePos) {
                    for (var key in this._chessHash) {
                        var chess: Chess = this._chessHash[key];
                        chess.resetSnakeLadder(chess.targetPos);
                    }
                } else {
                    this.movePilePos(isSamePos, moveChess);
                }
            }
        }

        /**
         * 聊天返回
         */
        private handleChat(msg: any): void {
            var user = UserService.instance.user;
            if (yalla.Global.IsGameOver || !msg || yalla.Mute.muted(msg.idx)) return;
            if (!msg.msg && !msg.extension) return;
            if (msg.idx == user.idx || (user.playerShowInfo && user.playerShowInfo.isInChatOff)) return;
            if (yalla.Global.Account.isPrivate == 1 && !yalla.Mute.isShowMsgByLimit(msg)) return;

            var color: number = null;
            var playerInfo: playerShowInfo = this._room.getPlayerByIdx(msg.idx);
            if (!playerInfo) return;
            var insit = playerInfo.sitNum > -1;
            if (this._bottomView.chat && insit && !this._bottomView.chat.showLive) {//弹幕被打开后不显示气泡
                var player: GamePlayer = this.gamePlayer(msg.idx);
                if (player) {
                    player.showChat(msg);
                    color = player.color;
                }
            }
            msg.msg = msg.msg;
            this._bottomView && this._bottomView.onChat(msg, playerInfo, yalla.Global.Account.idx, color);
        }

        /**
         * 调用语音
         */
        private handleSpeakResponse(data: Array<number>): void {
            if (yalla.Global.isFouce) {
                for (let i = 0; i < data.length; i++) {
                    let player = this.gamePlayer(data[i]);
                    if (player) player.play();
                }
            }
        }

        private voiceChange(isOpen: boolean) {
            yalla.Debug.log("开启声音:" + isOpen);
            if (yalla.Global.Account.idx) {
                var player = this.gamePlayer(yalla.Global.Account.idx);
                player && player.playVoiceAni(0, false, isOpen ? "pause" : "stop");
            }
        }

        public joinVoiceCallback() {
            if (!this._room) return;
            this._bottomView.joinVoiceCallback();
            this._room.sitPlayers && this._room.sitPlayers.forEach(pInfo => {
                var idx = pInfo.fPlayerInfo.idx;
                var player = this.gamePlayer(idx);
                if (player) {
                    if (yalla.Mute.muted(idx)) {
                        yalla.Voice.instance.muteRemoteAudioStream(idx, true, (val, uid) => {
                            yalla.Debug.log('snake updateMuteVoice():' + val + "uid:" + uid);
                            if (val == 0) {
                                var cbPlayer = this.gamePlayer(uid);
                                if (cbPlayer) {
                                    cbPlayer.playVoiceAni(0, false, "stop");
                                    cbPlayer.isMute = true;
                                }
                            } else {
                                yalla.Voice.instance.muteRemoteAudioStream(uid, false, (val, uid) => {
                                    if (val == 0) {
                                        yalla.Mute.remove(uid);
                                    }
                                    yalla.Debug.log(' snake 屏蔽别人失败后，取消屏蔽 updateMuteVoice():' + val);
                                });
                            }
                        })
                    }
                }
            })
        }

        /**
         * 自己发送消息，不等返回先显示
         * 需要屏蔽敏感词
         */
        private handleChatMy(msg: any): void {
            if (!msg.msg && !msg.extension) return;
            msg['idx'] = UserService.instance.user.idx;
            var player: GamePlayer = this.gamePlayer(msg.idx);
            if (player) {
                player.showChat(msg);
                this._bottomView && this._bottomView.onChat(msg, player.data, yalla.Global.Account.idx, player.color);
            }
        }

        private handleChatOff(msg: any): void {
            var player: GamePlayer = this.gamePlayer(msg.idx);
            player && (player.isInChatOff = false);
            if (this._topView && this._topView.setting) this._topView.setting.updateChatSwitch();
        }

        /**
         * 金币更新
         * @param msg 
         */
        private handleUpdatePlayerCoin(msg: any): void {
            this.update();
        }

        private handleUpdateGiftCnf(msg: any): void {
            yalla.common.InteractiveGift.Instance.resData = msg;
            var posHash = {};
            for (var key in this.playerHash) {
                var player: GamePlayer = this.playerHash[key];
                if (player) {
                    // var facePos = player.head.localToGlobal(new Laya.Point());
                    // posHash[key] = { x: facePos.x, y: facePos.y };
                    // posHash[key] = { x: player.x+player.head.x, y: player.y+player.head.y };
                    //TODO::这里不做坐标转化（相对屋顶定位，使用localToGlobal转化的坐标不准确）
                    posHash[key] = { x: player.x + player.UI.head_box.x - player.UI.head_box.width / 2 - 10, y: player.y + player.UI.head_box.y - player.UI.head_box.height / 2 - 10 };

                    if (player.head) player.head.isSendGift = !player.offLine && !this._topView.isSpectator;
                    if (player.data.giftId > 0) {
                        var giftData = yalla.common.InteractiveGift.Instance.getGiftItemData(player.data.giftId);
                        if (giftData) player.head.showGift(giftData.id, giftData.icon);
                    } else {
                        player.head.btn_gift.visible = player.head.isSendGift;
                    }
                }
            }
            yalla.Debug.log("====h互动礼物列表====");
            yalla.Debug.log(posHash);
            yalla.common.InteractiveGift.Instance.handleUpdateGiftCnf(msg, posHash, this.giftAniBox);
        }

        private handleRevGift(msg: any): void {
            var msgList = yalla.common.InteractiveGift.Instance.msgList;
            // yalla.Debug.log('=====Game.handleRevGift===msgList.length=='+msgList.length);
            if (msgList.length <= 1) yalla.common.InteractiveGift.Instance.handleRevGift(msg);
        }
        private updateGiftIcon(sendData: any): void {
            for (var key in this.playerHash) {
                if (key == 'player_' + sendData.recIdx) {
                    var player: GamePlayer = this.playerHash[key];
                    if (player) {
                        if (sendData.giftId > 0) {
                            player.head.showGift(sendData.giftId, sendData.icon);
                        }
                    }
                }
            }
        }

        /**
         * 游戏结束
         *通知网关断流
         */
        private gameOver(msg: any): void {
            yalla.Debug.log('==蛇棋结算===');
            yalla.Debug.log(msg);
            // UserService.instance.clearStream();

            UserService.instance.clearQueue();
            this.resetCdProgress();

            if (msg.msgindex) {
                yalla.Global.ProtocolMsgIndex = msg.msgindex;
                yalla.Global.ProtoEnterRoomMsgIndex = 0;
            }
            var isPrivate = UserService.instance.room.isPrivate;
            var isWin = (msg.player[0] && msg.player[0].fPlayerInfo.idx == UserService.instance.user.idx);
            var resultSound = isWin ? 'cheer' : 'ludo_fail';
            Laya.timer.clear(this, this.forceQuit);
            // yalla.Native.instance.mobClickEvent(buryPoint.DOMINO_SETTLE);
            // UserService.instance.recordPointQuit(yalla.data.ExitType.GAMEOVER_QUIT);
            // yalla.Sound.playSound(resultSound, Laya.Handler.create(this, () => { UserService.instance.leaveVoiceRoom(); }));
            yalla.Voice.instance.gameOverSound(resultSound);
            
            yalla.Native.instance.closeUserProfile();
            yalla.Native.instance.closeReportMsg();

            yalla.util.closeAllDialog();
            this._bottomView.hide();

            if (!this._resultView) {
                this._resultView = new yalla.view.result.snake.Result(msg, () => {//返回锦标赛界面
                    if (isPrivate == GameRoomType.CHAMPION) yalla.Native.instance.event(yalla.Native.instance.Event.BACKCHAMPION, [msg.gResut]);
                    else UserService.instance.backHall(false, 500);
                    this.gameOverBack();
                }, () => {
                    if (isPrivate == GameRoomType.VIP) this.share();
                    else {
                        this.playAgain();
                        this.gameOverBack();
                    }
                });
                this._resultView.name = 'resultView';
            }
            if (this._resultView && !this._resultView.displayedInStage) {
                Laya.stage.addChild(this._resultView);
            }

            if (isPrivate == GameRoomType.CHAMPION) {
                if (yalla.util.IsBrowser) yalla.net.Client.instance.clear();//避免已经结算了，但是长时间未操作，已经还是收到game is finished
                else yalla.net.NativeClient.instance.clear();
            }
            yalla.Native.instance.removeGameRulesView();

            //棋子事件等取消
            for (var key in this._chessHash) {
                this._chessHash[key].clear();
            }
        }

        /**
         * ******* 只有ios 结算的back playAgain 才先关闭界面，避免下一次进入游戏有上一局的界面
         */
        public gameOverBack() {
            if (yalla.Native.instance.deviceType == DeviceType.IOS) {
                this.backHallClear();
            }
        }

        /**
         * 获取棋子
         * @param idx 
         */
        private getChessByIdx(idx): Chess {
            for (var key in this._chessHash) {
                if (Number(key) == idx) return this._chessHash[key];
            }
            return null;
        }

        /**取消托管UI */
        public hideTrust(removeEvent: boolean) {
            this.timer.clear(this, this.trustDely);
            this._myHanderView && this._myHanderView.hideTrust(removeEvent);
            removeEvent && this.removeTrustEvent();
        }

        /**
         * 取消托管事件监听
         */
        public onTrust() {
            if (yalla.Global.IsGameOver) return;
            yalla.Debug.log("=====发送取消托管啦 send=====");
            this._myHanderView && this._myHanderView.setTrustWaitVisible(true);
            UserService.instance.cancleTrust();
            this.timer.once(3000, this, this.trustDely);
            this.removeTrustEvent();
        }

        private updateTrust(player: GamePlayer, isTrust: boolean): void {
            player.head.trustee = isTrust;
            player.UI.playerBg.visible = !isTrust;
            if (isTrust) {
                player.hideName();
            } else {
                player.showName();
            }
        }

        /**
         * 返回准备界面
         */
        private onClickBack(): void {
            yalla.Native.instance.isBack = true;
            var room = UserService.instance.room;
            var str = yalla.data.TranslationD.Game_Quit;
            if (room.isPrivate != GameRoomType.PRIVATE) {
                if (room.gameBeginTime + room.quitPunishTime > new Date().getTime() / 1000) {
                    str = yalla.data.TranslationD.Game_Quit_Punish;
                } else {
                    str = yalla.data.TranslationD.Game_Quit_Lose;
                }
            }

            yalla.common.Confirm.instance.isExit = true;
            yalla.common.Confirm.instance.showConfirm(str,
                Laya.Handler.create(this, () => {
                    // yalla.Native.instance.mobClickEvent(buryPoint.DGAME_EXIT_CONFIRM);
                    UserService.instance.quitRoom();
                    // yalla.data.UserService.instance.recordPointQuit(yalla.data.ExitType.INITIATIVE_QUIT);
                    var time = room.isPrivate == GameRoomType.CHAMPION ? 4000 : 2000; //TODO::quitRoom没返回，2s后回到锦标赛，但锦标赛获取信息还在游戏中，又跳转游戏情况,so延长未响应时间
                    Laya.timer.once(time, this, this.forceQuit, [room.isPrivate]);
                }),
                Laya.Handler.create(this, () => {
                    // yalla.Native.instance.mobClickEvent(buryPoint.DGAME_EXIT_CANCEL);
                    yalla.common.Confirm.instance.hideConfirm();
                }), ['Confirm', 'Cancel']);
        }

        /**
         * 主动退出游戏，quitRoom未响应，则强制退出
         * 但是需要再结算界面弹出时候把这个计时器清掉
         */
        private forceQuit(isPrivate: number): void {
            UserService.instance.clearQueue();
            if (isPrivate != GameRoomType.CHAMPION) {
                UserService.instance.backHall(true, 500);
            } else {
                if (!this._resultView) yalla.Native.instance.event(yalla.Native.instance.Event.BACKCHAMPION, [{ winnerId: -1 }]);
            }
        }

        /**
         * 主动发起退出房间的人才返回ready界面，否则等到gameevent=5结束游戏
         * @param msg 
         */
        private handleQuitGame(msg: any): void {
            if (msg.idx == UserService.instance.user.idx) {
                if (yalla.Global.Account.isPrivate != GameRoomType.CHAMPION) {
                    UserService.instance.backHall(true, 500);
                }
            }
        }

        /**
         * 游戏再来一局   TODO::backHall 返回大厅事件不抛，等在下次再进入时销毁
         */
        private playAgain(): void {
            UserService.instance.leaveVoiceRoom();
            // UserService.instance.recordPointQuit(yalla.data.ExitType.PLAY_AGAIN);
            yalla.util.clogPlayAgain(this._room.gameType);
            var p: playAgainArg = {
                gamePay: this._room.cost,
                gameId: yalla.Global.gameType,
                gameType: this._room.gameType,
                playerNum: this._room.playerNums,
                roomId: this._room.showId,
                gameGroup: 0,
                isPrivate: yalla.Global.Account.isPrivate
            };
            yalla.Native.instance.playAgain(p);
            this.backHallClear();
        }

        /** 结算分享 */
        public share() {
            UserService.instance.leaveVoiceRoom();
            yalla.Native.instance.shortScreenNative();
            yalla.util.clogShare(this._room.gameType);
            yalla.Native.instance.on(yalla.Native.instance.Event.SHARE, this, this.shareResponse);
        }
        private shareResponse(): void {
            UserService.instance.backHall();
            yalla.Native.instance.off(yalla.Native.instance.Event.SHARE, this, this.shareResponse);
        }

        /** 点击gameUI 需要隐藏UI */
        private onClickGameUI(e: Laya.Event): void {
            this._topView && this._topView.onClickGameUI();
            this._bottomView && this._bottomView.onClickGameUI();
            this._myHanderView && this._myHanderView.onClickGameUI();
            yalla.common.InteractiveGift.Instance.hideGiftView();
        }

        /**快速购买 */
        private buyCoin(callBack: Function) {
            yalla.Native.instance.buyGood((obj) => {
                if (obj.status) {
                    this._user.addCoin(obj);
                    this._room.addCoin(obj);
                    this.update();

                    if (!yalla.Global.IsGameOver) UserService.instance.IsUpdatePlayerCoin = true;
                    // yalla.Native.instance.mobClickEvent(buryPoint.LUDO_GAME_UNDO_LACK_BUY);//埋点 点击购买
                } else {
                    if (!yalla.Global.IsGameOver) UserService.instance.flushCurrencyRequest(1);
                    var tip = yalla.common.TipItem.instance;
                    tip.pushTip((Laya.stage.width - tip.width) / 2, Laya.stage.height / 2, "Fail to purchase diamonds, please try again.", { alpha: 0 }, 0, 5000);
                }
                this._buyCoin = true;
            });
        }

        /**根据位置获取地图各自数据
         * @param pos 
         */
        private getMapDataByPos(pos: number, type: number = 1): any {
            var mapJsonData = UserService.instance.mapData.mapJsonData;
            for (var k in mapJsonData) {
                if ((type == 1 && pos == mapJsonData[k].head) || (type == 2 && pos == mapJsonData[k].end)) {
                    return mapJsonData[k];
                }
            }
        }

        private resetCdProgress(): void {
            for (var key in this.playerHash) {
                var player: GamePlayer = this.playerHash[key];
                if (player) player.waitCdEnd()
            }
        }

        public onForce() {//获得焦点
            yalla.Sound.stopAllSound();
            Laya.timer.callLater(this, () => {
                for (var key in this.playerHash) {
                    var player: GamePlayer = this.playerHash[key];
                    if (player) {
                        player.resetHeadScale();
                    }
                }
                for (var key in this._chessHash) {
                    this._chessHash[key].show();
                }
            })
            if (this._myHanderView) {
                this._myHanderView.changeTrustAni(this.trust_jump.displayedInStage);
                this._myHanderView.onFocus()
            }
            this._baseMap && this._baseMap.startAni();
        }

        public onBlur() {//失去焦点
            yalla.Sound.stopAllSound();
            this._baseMap && this._baseMap.stopAni();
            var queue = UserService.instance.queue;
            queue.items.forEach(msgs => {
                this.handlePlayMsg(msgs['msg'], false);
            });
            UserService.instance.isQueue = false;

            for (var key in this.playerHash) {
                var player: GamePlayer = this.playerHash[key];
                if (player) player.stop()
            }
            this._myHanderView && (this._myHanderView.onBlur());

            for (var key in this._chessHash) {
                this._chessHash[key].hide();
            }
        }

        getIsSamePos(chess: Chess): boolean {
            for (var key in this._chessHash) {
                if (this._chessHash[key].name != chess.name && this._chessHash[key].targetPos == chess.targetPos) return true;
            }
            return false;
        }

        gamePlayer(idx): GamePlayer {
            if (!this.playerHash) this.playerHash = {};
            if (this.playerHash['player_' + idx]) return this.playerHash['player_' + idx];
        }

        /**
         * 移除选择棋子的事件
         */
        public removeChessEvent() {
            for (var key in this._chessHash) {
                this._chessHash[key].choose = false;
            }
        }

        public clearPlayer() {
            if (!this.playerHash) return;
            for (var key in this.playerHash) {
                if (this.playerHash.hasOwnProperty(key)) {
                    var player: GamePlayer = this.playerHash[key];
                    if (player) {
                        player.clear();
                        player = null;
                    }
                }
            }
            this.playerHash = null;
        }

        public backHallClear(): void {
            yalla.Debug.log('=====Game.backHallClear=======');
            this.removeEvent();
            for (var key in this.playerHash) {
                var player = this.playerHash[key];
                if (player) {
                    player.clear();
                    player = null;
                }
            }

            this.hideThrowJump();
            this._topView && this._topView.clear();
            this._bottomView && this._bottomView.clear();
            this._myHanderView && this._myHanderView.clear();
            this._resultView && this._resultView.clear();
            this._resultView = null;
            this._baseMap && this._baseMap.clear();
            this.clearPlayer();
            yalla.SkAnimation.instance.clear();
            this._topView = null;
            this._bottomView = null;
            this._myHanderView = null;
            this._baseMap = null;
            this.playerHash = null;
        }

        public clear(isRemove: boolean = false): void {
            for (var key in this._chessHash) {
                this._chessHash[key].clear();
            }
            this._chessHash = null;
            this.backHallClear();
            this.removeSelf();
            this.destroy(true);
        }
    }
}