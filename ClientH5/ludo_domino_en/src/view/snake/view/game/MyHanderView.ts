module yalla.view.game.snake {
    /** Game.myHander */
    export class MyHanderView {
        private gameUI: ui.snake.gameUI;
        public resetCd: Ludo.CdAni;//重置Cd

        public room: any = null;
        public user: any = null;

        constructor(ui: ui.snake.gameUI) {
            this.gameUI = ui;
        }

        public initData(room: any, user: any) {
            this.room = room;
            this.user = user;
        }

        public initView(val: boolean) {
            if (!val) {
                this.resetCd = new Ludo.CdAni(this.gameUI.reset.width / 2 - 4, yalla.data.SnakeGameConfig.ResetTime);
                this.gameUI.myHander.addChild(this.resetCd);
                this.resetCd.pos(this.gameUI.reset.x + 4, this.gameUI.reset.y + 5);
            }
            this.gameUI.reset.visible = !val;
            this.initEvent();
        }
        public initEvent() {
            this.gameUI.reset.on("click", this, this.showResetInfo);

        }
        public removeEvent() {
            this.gameUI.reset.off("click", this, this.showResetInfo);
        }

        public updateView() {
            this.updateUndoUI();
            this.checkReset();
        }

        /** 重置投掷 */
        public resetDice(msg: any, player: GamePlayer) {
            if (msg.code == 403) {
                yalla.common.Confirm.instance.showConfirm("Your account has been frozen", null, Laya.Handler.create(this, () => {
                    yalla.common.Confirm.instance.hideConfirm();
                }), ["", "Confirm"]);
            } else if (msg.result == 1) {
                if (msg.idx == yalla.Global.Account.idx) {
                    ludo.Undo.instance.happenUndo();
                    this.updateUndoUI();
                }
                player && player.popDice();
            }
        }

        // /**系统托管 */
        // public systemTrust(msg: any, player: GamePlayer) {
        //     var v = msg.idx == yalla.Global.Account.idx && !this.room.isTrust;
        //     if (v) {
        //         this.room.isTrust = true;
        //         var hasdice = false;
        //         console.log('===systemTrust 0===', hasdice);
        //         if (player) {
        //             hasdice = player.UI ? player.UI.dice_box.numChildren > 0 : false;
        //         }
        //         console.log('===systemTrust 1===', hasdice);
        //         this.showTrust(hasdice);
        //     }
        //     return v;
        // }

        /**取消托管 */
        public cancelTrust(msg: any, player: GamePlayer) {
            var v = msg.idx == yalla.Global.Account.idx && this.room.isTrust == true;
            if (v) {
                this.hideTrust(true);
                this.room.isTrust = false;
            }
            return v;
        }

        /**
     * 显示托管状态
     */
        public showTrust(hasdice: boolean = false) {
            console.log('====showTrust=====hasdice:', hasdice);
            this.gameUI.trusteeship.visible = !hasdice;
            yalla.Global.isFouce && this.changeTrustAni(!hasdice);
            this.gameUI.trust.offAll();
            this.gameUI.trust_wait.visible = false;
            this.gameUI.trust.selected = false;
            // this.addTrustEvent()
        }
        /**取消托管UI */
        public hideTrust(removeEvent: boolean) {
            // Laya.timer.clear(this, this.trustDely);
            this.gameUI.trusteeship.visible = false;
            this.changeTrustAni(false);
            this.gameUI.trust.selected = false;
            this.gameUI.trust_wait.visible = false;
            // removeEvent && this.removeTrustEvent();
        }

        public changeTrustAni(show: boolean) {
            if (show) {
                this.gameUI.trust_jump.play(0, true);
                this.gameUI.trust_wait.play(0, true);
            } else {
                this.gameUI.trust_jump.gotoAndStop(0);
                this.gameUI.trust_wait.gotoAndStop(0);
            }
        }

        public setTrustWaitVisible(v) {
            this.gameUI.trust_wait && (this.gameUI.trust_wait.visible = v);
        }

        public onFocus() {
            if (this.gameUI.throw_jump.displayedInStage) {
                this.gameUI.throw_jump.play(0, true);
            } else {
                (this.gameUI.throw_jump as Laya.Animation).gotoAndStop(0);
            }
        }

        /**
     * 重置开始
     *1.断线重连的时候 服务端主动推送 msg中有costDiamond字段 表示修正本地文字显示,不需要转动undo的雷达
     *2.重置开始的时候 msg中有msgindex字段而无costDiamond 字段 需要转动undo的雷达
     */
        public resetStart(msg: any, activated_id: number) {
            var undo = ludo.Undo.instance;
            if (msg.costDiamond) {
                if (undo.costDiamond > 0 && this.user.gold > undo.costDiamond) {
                    undo.costDiamond = msg.costDiamond;
                    this.gameUI.useCoin.text = String(undo.costDiamond);
                }
            }
            // yalla.Debug.log(msg.msgindex+'----resetStart----'+msg.idx+'-'+activated_id);
            if (msg.msgindex) {
                if (msg.idx == yalla.Global.Account.idx) {//添加重置按钮的点击事件 发送重置消息
                    if (ludo.Undo.instance.residueTurnReThrowNum <= 0 || activated_id != msg.idx) return;
                    if (this.room && !this.room.isTrust && !this.gameUI.isreset.selected) {//没有托管且
                        if (undo.costDiamond <= this.user.gold) {
                            this.gameUI.reset.off("click", this, this.showResetInfo);
                            this.gameUI.reset.once("click", this, this.sendResetMsg);
                        }
                        this.resetCd.start(yalla.data.SnakeGameConfig.ResetTime,-1);
                    }
                }
            }
        }

        /** undo*/
        public updateUndoUI() {
            var undo = ludo.Undo.instance;
            this.gameUI.round_count_text.text = String(undo.residueTurnReThrowNum);//当前回合剩余重置次数
            this.gameUI.count_text.text = String(undo.residueReThrowNum);//总剩余重置次数
            if (undo.costDiamond == 0) {
                this.gameUI.useCoin.fontSize = 16;
                this.gameUI.resetCoin.pos(22, 35);
                this.gameUI.useCoin.pos(17, 8);
                this.gameUI.useCoin.text = "Free";
                return;
            }
            var diamondShort = this.user.gold < undo.costDiamond;
            if (undo.residueTurnReThrowNum <= 0 || diamondShort) {//钻石不足 当前回合次数用尽 重置按钮置灰
                this.gameUI.reset.skin = "snake/btn_refresh_off.png";
                if (undo.residueReThrowNum <= 0 || diamondShort) {//总次数用尽
                    this.gameUI.useCoin.fontSize = 16;
                    this.gameUI.resetCoin.pos(22, 35);
                    this.gameUI.useCoin.pos(17, 8);
                    this.gameUI.useCoin.text = "Lack";
                    return;
                } else {//购买之后&&当前次用尽
                    this.gameUI.useCoin.fontSize = 20;
                    this.gameUI.resetCoin.pos(38, 24);
                    this.gameUI.useCoin.pos(5, 18);
                }
            } else {
                if (!this.gameUI.isreset.selected) this.gameUI.reset.skin = "snake/btn_refresh_on.png";
                this.gameUI.useCoin.fontSize = 20;
                this.gameUI.resetCoin.pos(38, 24);
                this.gameUI.useCoin.pos(5, 18);
            }
            this.gameUI.useCoin.text = String(undo.costDiamond);//下次使用钻石
        }

        private showResetInfo(e: Laya.Event) {
            e.stopPropagation();
            yalla.Sound.playSound("click");
            var undo = ludo.Undo.instance;
            if (undo.residueReThrowNum > 0 && this.user.gold < undo.costDiamond) {
                this.gameUI.quick_buy_btn.visible = !this.gameUI.quick_buy_btn.visible;
                return;
            }
            this.gameUI.reset_info.visible = !this.gameUI.reset_info.visible;
            if (this.gameUI.reset_info.visible) {
                this.gameUI.isreset.on("click", this, this.checkReset);
            } else {
                this.gameUI.isreset.off("click", this, this.checkReset);
            }
        }
        private checkReset() {
            if (!this.gameUI.isreset.selected) {
                this.gameUI.isreset_tx.text = "Re-roll ON";
                this.gameUI.reset_title.text = "Re-roll Dice";
                this.gameUI.reset_body.text = "You can re-roll dice within 2 secs.";
                var undo = ludo.Undo.instance;
                if (undo.residueTurnReThrowNum <= 0 || this.user.gold < undo.costDiamond) {
                    this.gameUI.reset.skin = "snake/btn_refresh_off.png";
                } else {
                    this.gameUI.reset.skin = "snake/btn_refresh_on.png";
                }
            } else {
                this.gameUI.isreset_tx.text = "Re-roll OFF";
                this.gameUI.reset_title.text = "Re-roll is off";
                this.gameUI.reset_body.text = "Re-roll is off, tap to enable it.";
                this.gameUI.reset.skin = "snake/btn_refresh_off.png";
                this.resetEnd();
            }
        }

        /**
         * 重置结束
         */
        public resetEnd() {
            if (!this.gameUI.isreset.selected) {//移除重置按钮的点击事
                this.gameUI.reset.off("click", this, this.sendResetMsg);
                this.gameUI.reset.on("click", this, this.showResetInfo);
                this.resetCd && this.resetCd.end();
            }
        }

        private sendResetMsg() {
            this.resetCd.end();
            yalla.data.snake.UserService.instance.clickReset();
            this.gameUI.reset.on("click", this, this.showResetInfo);

            yalla.util.clogDBAmsg('10117', JSON.stringify({gameId:yalla.Global.Account.gameId}));
        }

        public showThrowJump() {
            // this.throwBtn && this.throwBtn.once("click", this, this.clickDice);
            this.gameUI.throw_jump.visible = true;
            yalla.Global.isFouce && this.gameUI.throw_jump.play(0, true);
        }
        public hideThrowJump() {
            // this.throwBtn && this.throwBtn.off("click", this, this.clickDice);
            this.gameUI.throw_jump.visible = false;
            (this.gameUI.throw_jump as Laya.Animation).gotoAndStop(0);
        }

        public onClickGameUI(): void {
            if (this.gameUI.reset_info.visible) {
                this.gameUI.reset_info.visible = false;
            }
            if (this.gameUI.quick_buy_btn.visible) {
                this.gameUI.quick_buy_btn.visible = false;
            }
        }

        public onBlur() {
            this.gameUI.trust_jump.displayedInStage && this.changeTrustAni(false);
            this.gameUI.throw_jump.displayedInStage && (this.gameUI.throw_jump as Laya.Animation).gotoAndStop(0);
        }
        public clear(): void {
            this.removeEvent();
            this.resetCd && this.resetCd.clear();
        }
    }
}