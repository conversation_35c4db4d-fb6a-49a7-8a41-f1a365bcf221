module yalla.view.game.snake
{
    export class SnakeEffect extends Laya.EventDispatcher
    {
        private _tartget:any;

        constructor(target:any)
        {
            super();
            this._tartget = target;
        }

        /**
         * 蛇形动画
         */
        public playSnakeKiss(routeList:Array<any>, time:number, callBack:Function = null):void
        {
            if(routeList.length < 1) {
                Laya.Tween.clearAll(this._tartget);
                return;
            }
            var self = this;
            var goal = routeList.shift();
            Laya.Tween.to(this._tartget, {x:goal[0], y:goal[1]}, time, Laya.Ease.linearIn,Laya.Handler.create(this, function(){
                self.playSnakeKiss(routeList, time);
                callBack && callBack();
            }), 0, true, true);
        }
        
        /**
         * 爬梯动画
         */
        public playClimpLadder(routeList:Array<any>, time:number, callBack:Function = null):void
        {
            if(routeList.length < 1) {
                Laya.Tween.clearAll(this._tartget);
                return;
            }
            var self = this;
            var goal = routeList.shift();
            Laya.Tween.to(this._tartget, {x:goal[0], y:goal[1]}, time, Laya.Ease.elasticOut, Laya.Handler.create(this, function(){
                self.playClimpLadder(routeList, time);
                callBack && callBack();
            }), 0, true, true);
        }
    }
}