module yalla.view.game.snake {
    /**
     * topMc
     */
    export class TopView {
        private _gameUI: ui.snake.gameUI;
        private _topUI = null;
        public setting: Setting;
        private _playerNums: number = 0;
        private _isSpectator: boolean = null;

        constructor(gameUI: ui.snake.gameUI) {
            this._gameUI = gameUI;
            if (yalla.Font.lan == 'en') {
                this._topUI = new ui.snake.item.game_top_enUI();
            } else {
                this._topUI = new ui.snake.item.game_top_arUI();
            }
            gameUI.topMc.addChild(this._topUI);
            this.initEvent();
        }

        private initEvent(): void {
            this._topUI['settingBtn'].on(Laya.Event.CLICK, this, this.onClickSetting);
            this._topUI['winBtn'].on(Laya.Event.CLICK, this, this.onClickPrize);
            // this._topUI.store_btn.off("click", this, this.storeHander);
            this._topUI.audience_btn.on("click", this, this.audienceHander);
            // this._topUI.muteSpectator_btn.on("click", this, this.muteSpectator);
            this._topUI['btn_shop'].on(Laya.Event.CLICK, this, this.showSameStore);
        }

        public init(): void {
            this.onClickPrize();
        }

        set isSpectator(val: boolean) {
            if (this._isSpectator == val) return;
            this._isSpectator = val;
            if (val && yalla.Global.Account.isPrivate != 1) this._topUI.audience_btn.visible = true;
            // this._topUI.muteSpectator_btn.visible = !val;
            this._topUI.store_btn.visible = !val;
        }

        public updateView() {
            var inst = yalla.data.snake.UserService.instance;
            this._topUI.store_btn.label = yalla.util.filterNum(inst.user.gold);
            this.updateAudiencePLayer();
        }

        public updateAudiencePLayer() {
            // this._topUI.audience_num.text = ludoRoom.instance.getAudiencePlayers().length.toString();
            // if (this._topUI.audience_btn.visible && Audience.instance.displayedInStage) {
            //     Audience.instance.list.array = ludoRoom.instance.getAudiencePlayers();
            // }
        }

        public setRank(room: yalla.data.snake.Room) {//设置榜单的信息
            this._playerNums = room.playerNums;

            if (room.isPrivate == GameRoomType.VIP) {
                this._gameUI.topImg.skin = yalla.getPublic('top-bg');
            } else {
                this._gameUI.topImg.skin = yalla.getSnake('image_ludo_bg0');
            }

            // this._topUI['typeTxt'].text = yalla.data.GameConfig.typeNames[room.gameType];
            this._topUI['rank_roomId'].text = room.showId + '';
            if (room.isPrivate == GameRoomType.CHAMPION) {
                this._topUI['rank_1'].text = yalla.util.filterNum(room.cost);
            } else {
                var royalty = yalla.Global.Account.royalty ? yalla.Global.Account.royalty : 0;
                this._topUI['rank_1'].text = yalla.util.filterNum(room.cost * this._playerNums * (1 - royalty / 100));

            }

            if (yalla.Font.lan == 'en') this._topUI['rankImgTxt1'].text = 'RANK 1';
            else this._topUI['rankImgTxt1'].text = 'الترتيب ' + 1;

            if (room.isPrivate == GameRoomType.PRIVATE) {
                this._topUI['rank1'].visible = false;
            } else {
                this._topUI['rank1'].visible = true;
                this._topUI['rankMc'].height = 143;
            }
        }

        private onClickSetting(e: Laya.Event): void {
            if (e) {
                yalla.Sound.playSound('click');
                e.stopPropagation();
            }
            if (this._topUI['rankMc'].visible) {
                Laya.timer.clear(this, this.hideRank);
                this.hideRank();
            }

            if (!this.setting) this.setting = new Setting(this._topUI);
            this._topUI['set'].visible = !this._topUI['set'].visible;
            if (this._topUI['set'].visible) this.setting.update();
            yalla.event.YallaEvent.instance.event('click_gameTopview');

            // yalla.Native.instance.mobClickEvent(buryPoint.DGAME_SETTING);
        }

        /**
         * 奖励信息 显示隐藏
         */
        private onClickPrize(e: Laya.Event = null): void {
            if (e) {
                yalla.Sound.playSound('click');
                e.stopPropagation();
            }
            if (this._topUI['set'].visible) this._topUI['set'].visible = false;

            Laya.timer.clear(this, this.hideRank);
            this._topUI['rankMc'].visible = !this._topUI['rankMc'].visible;
            if (this._topUI['rankMc'].visible) {
                if (yalla.Global.game_state != yalla.data.GameState.State_Sleep) {
                    Laya.timer.once(2000, this, this.hideRank);
                } else {
                    this.hideRank();
                }
            }
            yalla.event.YallaEvent.instance.event('click_gameTopview');
            // yalla.Native.instance.mobClickEvent(buryPoint.DGAME_REWARD);
        }

        private audienceHander() {
            Audience.instance.popup(true, true);
            // yalla.Native.instance.mobClickEvent(buryPoint.GAME_SPECTATER);
        }

        public showSameStore() {//显示快速商店
            var inst = yalla.data.snake.UserService.instance;
            var playerList = inst.room.player.player;
            if (playerList) {
                var players = [];
                var base64Encode = laya.utils.Browser.window.Base64.encode;
                playerList.forEach(val => {
                    var f = val.fPlayerInfo
                    players.push({
                        idx: f.idx,
                        faceUrl: base64Encode(f.faceUrl),
                        nickName: base64Encode(f.nikeName),
                        color: val.playColor
                    })
                })
                yalla.Native.instance.showSameStyleStore(players, inst.user.gold, inst.user.money);
            }
        }

        public updateChatSwitch() {
            this.setting && (this.setting.updateChatSwitch());
        }

        public get topUI(): Laya.View {
            return this._topUI;
        }

        get isSpectator(): boolean {
            return this._isSpectator;
        }

        /**
         * 隐藏奖励信息
         */
        private hideRank(): void {
            this._topUI.rankMc.visible = false;
        }

        public onClickGameUI(): void {
            this.hideRank();
            this._topUI.set.visible = false;
            Laya.timer.clear(this, this.hideRank);
        }

        private removeEvent(): void {
            this._topUI['settingBtn'].off(Laya.Event.CLICK, this, this.onClickSetting);
            this._topUI['winBtn'].off(Laya.Event.CLICK, this, this.onClickPrize);
            // this._topUI.store_btn.off("click", this, this.storeHander);
            this._topUI.audience_btn.off("click", this, this.audienceHander);
            // this._topUI.muteSpectator_btn.off("click", this, this.muteSpectator);
            this._topUI['btn_shop'].off(Laya.Event.CLICK, this, this.showSameStore);
            Laya.timer.clear(this, this.hideRank);
            Laya.timer.clearAll(this);
        }

        public clear(): void {
            this.removeEvent();
            this.setting && this.setting.clear();
            if (this._topUI) {
                this._topUI.removeSelf();
                this._topUI.destroy(true);
            }
            this.setting = null;
        }
    }
}