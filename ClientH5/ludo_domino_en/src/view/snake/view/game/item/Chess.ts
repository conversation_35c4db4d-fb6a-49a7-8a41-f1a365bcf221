module yalla.view.game.snake {
    import Sprite = Laya.Sprite;
    import UserService = yalla.data.snake.UserService;

    export class Chess extends Laya.Image {
        // public chessImg: Laya.Image = null;
        public color: number = 0;          //RED=0;YELLOW=2;BLUE=3;GREEN=1;ORANGE=4;
        private _scale: number = 0;
        private _posIndex: number = 0;       //棋子当前位置

        // public Wid: number = 48;
        // public Hei: number = 76;
        public Wid: number = 52;
        public Hei: number = 56;

        private ScaleSp: number = 0.015;
        private _num: number = 0;                    //可以移动步数
        private _isHorizontalMove: boolean = false;
        private _v: number = -1;                     //缩放系数
        private _angle = 0;
        private _centX = 0;
        private _centY = 0;

        public Vy: number = 1.6; //1.2 2.5
        private _isVertialMove: boolean = false;
        private _snakeVerticalVy: number = 0;    //加速度
        private _snakeVertialAddY: number = 0;  //y向上偏移
        private _targetY: number = 0;            //目标y

        private _isSnakeKissMove: boolean = false;
        private _snakeRoutList: Array<any> = [];
        private _snakeKissLen: number = 0;
        private _snakeSpeed: number = 0;
        private _snakePoint: Laya.Point;
        private _snakeToPoint: Laya.Point;
        private _snakeRadian: number;
        // private _snakeAngle: number;
        private _snakeRoutRegionList = [];

        private _ladderRoutList: Array<any> = [];
        private _nextEndPos: number = 0;
        private skin_id: number = null;
        private _skinIsLoaded: boolean = false;
        public idx: number = 0;


        private timeLine: Laya.TimeLine;
        private _frameNum: number = 1;
        private _isMoving: boolean = false;
        private _choose: boolean = false
        private template: Laya.Templet;
        private sk: Laya.Skeleton;//波纹动画
        private chessBubbleManager: ChessBubbleManager;

        constructor(color: number, idx: number, chessBubbleManager:any) {
            super();
            this.chessBubbleManager = chessBubbleManager;
            this.color = color;
            this.idx = idx;
            // this._snakeSpeed = yalla.data.Snake_Speed;
            this.init();
        }

        private init(): void {
            this.anchorX = this.anchorY = 0.5;
            // this.chessImg = new Laya.Image();
            // this.chessImg.width = this.Wid;
            // this.chessImg.height = this.Hei;
            // this.addChild(this.chessImg);
            this.name = this.color.toString();
            // var skin = this.skin[this.color];
            // var iconSp = new Sprite();
            // var texture = Laya.loader.getRes('game/chess_'+skin+'.png');
            // iconSp.graphics.drawTexture(texture);
            // this.addChild(iconSp);
            // this.size(yalla.data.RectWid, yalla.data.RectHei);
            // iconSp.pos(this.width/2 - this.Hei/2, this.height - this.Hei - 16);
            this.setSize(this.Wid, this.Hei);

            this.template = new Laya.Templet();
            this.template.on(Laya.Event.COMPLETE, this, this.onSkComplete);
            this.template.loadAni("res/sk/chooseChess_snake/skeleton.sk");

            this.timeLine = new Laya.TimeLine();
            this.timeLine.on(Laya.Event.COMPLETE, this, () => {
                if (this.destroyed) return;
                this.timeLine.reset();
                // if (!this.choose)
                // this.setChScale();
                this.moveEnd();
            })
            // Laya.timer.frameLoop(this._frameNum, this, this.onEnterFrame);
        }

        private setSkin(name: string) {
            // if (this.color == 0) {
            //     this.skin = "snake/chess_red.png";
            // } else {
            //     this.skin = "snake/chess_blue.png";
            // }
            if (this.skin_id == 11000) {
                this.skin = `snake/${name}_11000_${this.color}.png`;
            } else if (this._skinIsLoaded) {
                this.skin = `chess_${this.skin_id}/${name}_${this.skin_id}_${this.color}.png`;
            } else {
                this.skin = `snake/${name}_11000_${this.color}.png`;
                yalla.event.YallaEvent.instance.once(`chess_${this.skin_id}.atlas`, this, () => {
                    this.skin = `chess_${this.skin_id}/${name}_${this.skin_id}_${this.color}.png`;
                    this._skinIsLoaded = true;
                })
                yalla.event.YallaEvent.instance.once(`chess_${this.skin_id}.png`, this, () => {
                    yalla.File.getFileByNative(`chess_${this.skin_id}.atlas`);
                })
                yalla.File.getFileByNative(`chess_${this.skin_id}.png`);
            }
        }

        set skinId(val: number) {
            this.skin_id = val;
            this.setSkin("chess");
        }
        get skinId(): number {
            return this.skin_id;
        }

        public show(): void {
            if (this.choose && this.sk) this.sk.play(1, true);
            // Laya.timer.clear(this, this.onEnterFrame);
            // Laya.timer.frameLoop(this._frameNum, this, this.onEnterFrame);
        }

        public hide(): void {
            this._isHorizontalMove = false;
            this._isSnakeKissMove = false;
            this._isVertialMove = false;
            this._angle = 0;
            this._centX = this._centY = this._targetY = 0;
            this._snakeKissLen = 0;
            this._snakeRoutList = [];
            this._ladderRoutList = [];
            // Laya.timer.clear(this, this.onEnterFrame);
            if (this._nextEndPos > 0) {
                // console.log('====roundEnd 1====');
                this.roundEnd(this._nextEndPos);
            }
        }

        // private onEnterFrame(): void {
        //     if (this._isHorizontalMove) {
        //         if ((this._side == 1 && this._angle >= 90) || (this._side == -1 && this._angle <= 270)) {
        //             --this._num;
        //             ++this.posIndex;
        //             this.setMove();
        //             return;
        //         }
        //         this._angle += this._side * 5;
        //         // this.x = this._centX + Math.sin(this._angle / 180 * Math.PI) * yalla.data.RectWid;
        //         // this.y = this._centY - Math.cos(this._angle / 180 * Math.PI) * yalla.data.RectHei / 2;
        //         // this.scaleY += this.ScaleSp * this._v;
        //         // this.y -= yalla.data.JumpHei * (1 - this.scaleY);//跳动y坐标偏移

        //         this.x = this._centX + Math.sin(this._angle / 180 * Math.PI) * yalla.data.RectWid;
        //         this.y = this._centY - Math.cos(this._angle / 180 * Math.PI) * yalla.data.JumpHei;
        //         this.scaleY += this.ScaleSp * this._v;
        //         // this.y -= yalla.data.JumpHei * (1 - this.scaleY);//跳动y坐标偏移

        //         if (this._angle % 45 == 0) this._v *= -1;
        //     }

        //     if (this._isVertialMove) {
        //         if ((this.y <= this._targetY - this._snakeVertialAddY) && this._vSide == -1) {//上
        //             this._vSide = 1;
        //             return;
        //         } else if (this.y >= this._targetY && this._vSide == 1) {//下
        //             this.y = this._targetY;
        //             --this._num;
        //             ++this.posIndex;
        //             this.setMove();
        //             return;
        //         }
        //         // this._snakeVerticalVy += this.Vy * this._vSide;
        //         // this.y += this._snakeVerticalVy;

        //         this._snakeVerticalVy += 0.01 * this._vSide;
        //         this.y += (this._vSide * 5 + this._snakeVerticalVy);
        //     }

        //     if (this._isSnakeKissMove) {
        //         var dis: number = this._snakeToPoint.distance(this.x, this.y);
        //         // console.log(this._snakeSpeed,'=====kiss dis:', dis);
        //         if (dis <= 15) {
        //             this.setKissMove();
        //             return;
        //         }
        //         this.x = this.x + this._snakeSpeed * Math.cos(this._snakeRadian);
        //         this.y = this.y + this._snakeSpeed * Math.sin(this._snakeRadian);
        //     }
        // }

        private stepNum = 0;
        public moving: boolean = false;
        private _soundTimeOut = null;
        private _playTimeOut = null;
        private getPathsPos() {
            var arr = [];
            for (var i = this._posIndex + 1; i <= this._nextEndPos; i++) {
                // var list = this.getSnakeMapList(i);
                var posList = this.getPos(i);
                arr.push({ x: posList[0], y: posList[1] });
            }
            console.log(this._posIndex, this._nextEndPos, '===getPathsPos==', arr);
            return arr;
        }

        private moveEnd(stepNum: number = this.stepNum) {//移动结束事件
            // console.log('=====moveEnd======');
            if (this._isSnakeKissMove) {
                yalla.Sound.playSound('Snake_end');
                this.chessBubbleManager.play(this, "cry", {x:25,y:6});
                this._isSnakeKissMove = false;
            }
            this._snakeRoutList = null;
            this._isSnakeKissMove = false;

            this.moving = false;
            this.stepNum = 0;
            this.roundEnd(this._nextEndPos);
        }
        private lineCount = 0;
        private lineMove() {//直线平滑移动
            this.moving = true;
            this.lineCount = 0;
            var arr: Array<any> = this.getPathsPos();
            var len = arr.length;
            if (len > 6) {
                this.checkPos(this._nextEndPos);
                return;
            }
            if (len > 0) {
                this.scale(1, 1);
                arr.forEach((val, index) => {
                    var time = yalla.data.Move_Base_Time + Math.floor(yalla.data.Move_Change_Time / (index + 1));//yalla.data.Move_Time
                    // console.log('=====time:'+time);
                    this.timeLine.addLabel(index.toString(), 0).to(this, val, time);
                })
                this.playsound(len);
                this.timeLine.play(0, false);
            } else {
                this.moveEnd();
            }
        }

        public clearTimeLine() {
            if (this.timeLine) {
                this.timeLine.pause();
                this.timeLine.reset();
            }
        }
        playsound(n: number) {
            var isSleep = yalla.Global.game_state == yalla.data.GameState.State_Sleep;
            if (this.lineCount >= n || isSleep) {
                return;
            }
            var time = yalla.data.Move_Base_Time + Math.floor(yalla.data.Move_Change_Time / (this.lineCount + 1));
            // console.log('==playsound===time:',time);
            yalla.Sound.playSound("token_move");
            this._soundTimeOut = setTimeout(() => {
                clearTimeout(this._soundTimeOut);
                this.playsound(n);
            }, time);
            this.lineCount += 1;
        }


        public initData(skinId: number, posIndex: number): void {
            this.skinId = skinId;
            this._posIndex = posIndex;
            // this.setScale(scale);
        }

        public setData(msg: any, p: any): void {
            this._nextEndPos = msg.targetPos;
            this.reset(msg.oldPos);
            this.setSize(this.Wid, this.Hei);
            console.log(this.posIndex, '======chess.setData=====', this.x, this.y);
            this._isMoving = true;
            switch (msg.event) {
                case yalla.data.GameEvent.SAL_CHESS_MOVE://移动
                    console.log(this.posIndex, '---', '===chess.move==', this._nextEndPos);
                    if (yalla.Global.game_state == yalla.data.GameState.State_Sleep) {
                        this.roundEnd(this._nextEndPos);
                        return;
                    }
                    // this._num = msg.throwNum;
                    this.stepNum = msg.throwNum;
                    // console.log(this.idx, '-----setMove 3');
                    // console.log(this.getPathsPos());
                    this.setMove();
                    break;
                case yalla.data.GameEvent.SAL_CHESS_MOVE_SNAKE://蛇吻
                    this._isSnakeKissMove = true;    
                    // this._snakeSpeed = yalla.data.Snake_Speed;

                    // this.posIndex = p.end;//先把位置索引设置好
                    if (yalla.Global.game_state == yalla.data.GameState.State_Sleep) {
                        this.roundEnd(this._nextEndPos);
                        return;
                    }
                    var posList = this.getPos(this._nextEndPos);
                    this._snakeRoutList = this.getRoutList(p).concat([]);
                    // this._snakeRoutList.push(posList);
                    this._snakeKissLen = this._snakeRoutList.length;
                    console.log(this.posIndex, posList, '====case snake===', this._snakeRoutList, this._snakeRoutList.length);
                    this.setKissMove();
                    yalla.Sound.playSound('Snake_Eat');
                    break;
                case yalla.data.GameEvent.SAL_CHESS_MOVE_LADDER://爬梯
                    if (yalla.Global.game_state == yalla.data.GameState.State_Sleep) {
                        this.roundEnd(this._nextEndPos);
                        return;
                    }
                    console.log('====setLadderMove=====', p);
                    this.setLadderMove(p);
                    break;
            }
        }

        /**
         * 移动
         * @param num 
         */
        private setMove(): void {
            var isSleep = yalla.Global.game_state == yalla.data.GameState.State_Sleep;
            if (isSleep) {
                this.moveEnd();
            } else {
                this.lineMove();
            }

            // this._isHorizontalMove = this._isVertialMove = false;
            // if (this._num < 1 || this.posIndex >= yalla.data.Max_Num) {
            //     console.log(this.idx, this._nextEndPos, '====setMoveEnd====posIndex:', this.posIndex);
            //     this.roundEnd(this._nextEndPos);
            //     return;
            // }
            // var list = this.getSnakeMapList(this._posIndex);
            // if (!list) { console.log('===非法移动===posIndex:', this._posIndex); return; }
            // console.log(list, '===setMove===', this._posIndex);
            // this._side = list[2];
            // switch (this._side) {
            //     case 1:
            //         this._centX = this.x;
            //         this._centY = this.y;
            //         this._angle = 0;
            //         this._isHorizontalMove = true;
            //         break;
            //     case -1:
            //         this._centX = this.x;
            //         this._centY = this.y;
            //         this._angle = 360;
            //         this._isHorizontalMove = true;
            //         break;
            //     case 2:
            //         this._vSide = -1;//先上 后下
            //         this._isVertialMove = true;
            //         this._snakeVerticalVy = 0;
            //         this._targetY = this.getSnakeMapList(this._posIndex + 1)[0] * yalla.data.RectHei + yalla.data.RectHei / 2;
            //         break;
            // }
            // yalla.Sound.playSound('token_move');
        }

        // /**
        //  * 蛇吻
        //  */
        // private setKissMove(): void {
        //     if (!this._snakeRoutList || this._snakeRoutList.length < 1) {
        //         console.log('====setKissMove=====', this._nextEndPos);
        //         this._isSnakeKissMove = false;
        //         this.roundEnd(this._nextEndPos);
        //         return;
        //     }
        //     if (this._snakeRoutList.length < this._snakeKissLen/2) this._snakeSpeed = yalla.data.Snake_Speed * 0.5;
        //     var endPos = this._snakeRoutList.shift();
        //     console.log(endPos[0], endPos[1], this.x, this.y, '=====setKissMove 0=======', this._snakeRoutList);
        //     this._isSnakeKissMove = true;
        //     this._snakePoint = new Laya.Point(this.x, this.y);
        //     this._snakeToPoint = new Laya.Point(endPos[0], endPos[1]);

        //     this._snakeRadian = Math.atan2(this._snakeToPoint.y - this._snakePoint.y, this._snakeToPoint.x - this._snakePoint.x);
        // }
        /**
         * 蛇吻
         */
        private setKissMove(): void {
            // if (!this._snakeRoutList || this._snakeRoutList.length < 1) {
            //     this._isSnakeKissMove = false;
            //     this.roundEnd(this._nextEndPos);
            //     return;
            // }
            // this.kissMove();
            var isSleep = yalla.Global.game_state == yalla.data.GameState.State_Sleep;
            if (isSleep) {
                this.moveEnd();
            } else {
                this.kissMove();
            }
        }

        private kissMove(): void {
            // if (!this._snakeRoutList || this._snakeRoutList.length < 1) {
            //     this._isSnakeKissMove = false;
            //     this.roundEnd(this._nextEndPos);
            //     return;
            // }
            // var goal = this._snakeRoutList.shift();
            // var dis = yalla.util.getDistanceNum({ x: this.x, y: this.y }, { x: goal[0], y: goal[1] });
            // var time = dis * 5 / 100;
            // console.log(time, '==========dis:',dis);
            // var time = yalla.data.Step_Time;
            // if (this._snakeRoutList.length < this._snakeKissLen / 2) this._snakeSpeed = yalla.data.Snake_Speed * 0.5;

            // var self = this;
            // var tw = Laya.Tween.to(this, { x: goal[0], y: goal[1] }, time, null, Laya.Handler.create(this, function () {
            //     // Laya.Tween.clear(tw);
            //     self.kissMove();
            // }));

            if (this._snakeRoutList && this._snakeRoutList.length > 0) {
                // var xx = this.x;
                // var yy = this.y;
                var xx = this._snakeRoutList[0][0];
                var yy = this._snakeRoutList[0][1];
                var totalTime = 0;
                var count = 1;
                this._snakeRoutList.shift();
                this._snakeRoutList.forEach((val, index) => {
                    count += 1;
                    var dis = yalla.util.getDistanceNum({ x: xx, y: yy }, { x: val[0], y: val[1] });
                    var time = dis * yalla.data.Snake_Time / 100;
                    if (count >= this._snakeKissLen * 0.66) {
                        time *= yalla.data.Snake_Time_V;
                    }
                    totalTime += time;
                    this.timeLine.addLabel('snake' + index, 0).to(this, { x: val[0], y: val[1] }, time);
                    // console.log(count, time, totalTime,'==========dis:', dis, '---', xx, yy, ':', val[0], val[1]);
                    xx = val[0];
                    yy = val[1];
                })
                console.log('=========蛇移动时间 totalTime：', totalTime);
                this.timeLine.play(0, false);
            } else {
                this.moveEnd();
            }
        }

        /**
         * 爬梯
         */
        private setLadderMove(element: MapInfo): void {
            //跳跃 
            var ladderList = this.getRoutList(element).concat([]);
            if (!ladderList || ladderList.length < 1) return;

            var beginX = ladderList[0][0], beginY = ladderList[0][1];
            var endX = ladderList[1][0], endY = ladderList[1][1];
            this._ladderRoutList = yalla.util.getPointList(new Laya.Point(beginX, beginY), new Laya.Point(endX, endY), yalla.data.Step_Len);

            ////直线运动
            // var ladderList = this.getRoutList(element);
            // var beginX = ladderList[0][0], beginY = ladderList[0][1];
            // var endX = ladderList[1][0], endY = ladderList[1][1];
            // this._ladderRoutList = ladderList;
            var len = this._ladderRoutList.length;
            this._playTimeOut = setTimeout(() => {
                clearTimeout(this._playTimeOut);
                yalla.Sound.playSound('ladder_end');
                this.chessBubbleManager.play(this, 'laugh', {x:25, y:6});
            }, len * 230);
            this.ladderMove();
        }
        private _totalTime = 0;
        private ladderMove(): void {
            if (!this._ladderRoutList || this._ladderRoutList.length < 1) {
                // this.chessBubbleManager.play(this, 'laugh', {x:25, y:6});
                this.roundEnd(this._nextEndPos);
                return;
            }

            var time = yalla.data.Step_Time;
            var self = this;
            var goal = this._ladderRoutList.shift();
            var topY = goal[1] - yalla.data.JumpLadder;
            this._totalTime += 230;
            var tw = Laya.Tween.to(this, { x: goal[0], y: topY }, time, null, Laya.Handler.create(this, function () {
                yalla.Sound.playSound('token_move');
                var tw1 = Laya.Tween.to(self, { y: goal[1] }, 50, null, Laya.Handler.create(self, function () {
                    Laya.Tween.clear(tw1);
                    Laya.Tween.clear(tw);
                    self.ladderMove();
                }));
            }));
        }

        private roundEnd(posIndex: number): void {
            this._isMoving = false;
            var mapData = UserService.instance.mapData;
            if (mapData && posIndex == mapData.endPos) {
                var isWake = yalla.Global.game_state != yalla.data.GameState.State_Sleep;
                /**到达终点 */
                yalla.Sound.playSound("firework");
                this.visible = false;
                if (isWake) {
                    yalla.SkAnimation.instance.playSnakeEndPos([this.x, this.y], Laya.Handler.create(this, () => {
                        this.visible = true;
                    }));
                } else {
                    this.visible = true;
                }
            }
            Laya.Tween.clearAll(this);
            this.checkPos(posIndex);
            yalla.event.YallaEvent.instance.event(yalla.data.snake.EnumCustomizeCmd.Snake_RoundEnd, [this]);
        }

        /**
         * [1 - 100] 动画后最终最终位置校验
         * @param posIndex 
         */
        public checkPos(posIndex: number): void {
            if (posIndex < 1) return;
            var posList = this.getPos(posIndex);
            this._posIndex = posIndex;
            this.pos(posList[0], posList[1]);
        }

        public setScale(scale: number): void {
            this._scale = scale;
            this.scale(scale, scale);
            // this.setSize(this.Wid * scale, this.Hei * scale);
            // this.pivot(this.width / 2, this.height / 2);
            // this.scale(scale, scale);
        }

        public setSize(wid: number, hei: number): void {
            this.size(wid, hei);
            this.sk && (this.sk.pos(this.width / 2, this.height / 2));
            // this.pivot(this.width / 2, this.height / 2);
        }

        public get targetPos() { return this._nextEndPos; }
        public set targetPos(i: number) { this._nextEndPos = i; }

        public get posIndex() { return this._posIndex; }
        public set posIndex(i: number) { this._posIndex = i; }
        public get Side() { return this.getSnakeMapList(this._posIndex) }
        public get isMoving() { return this._isMoving; }

        public getPos(posIndex: number) {
            var list = this.getSnakeMapList(posIndex);
            var x = 0, y = 0;
            if (list) {
                x = list[1] * yalla.data.RectWid + yalla.data.RectWid / 2;
                y = list[0] * yalla.data.RectHei + yalla.data.RectHei / 2;
            }
            return [x, y];
        }

        /**走到同一个格子 */
        public getPilePos(posIndex: number, vx: number) {
            var list = this.getSnakeMapList(posIndex);
            var x = 0, y = 0;
            if (list) {
                x = list[1] * yalla.data.RectWid + yalla.data.RectWid / 2 + vx;
                y = list[0] * yalla.data.RectHei + yalla.data.RectHei / 2;
            }
            return [x, y];
        }

        /**如果皮肤没有加载到，则用默认蛇皮肤的路径 */
        getRoutList(element: MapInfo) {
            if (!element) return [];
            
            var mapData = UserService.instance.mapData;
            if (mapData) {
                console.log('===chess.getRoutList====', element)
                if (mapData.isLoadEle(element.skinId)) {
                    return element.realList;
                }
            }
            return element.nList;
        }

        getSnakeMapList(pos) {
            var mapData = UserService.instance.mapData;
            if (mapData && mapData.snakeMapList) {
                var mList = mapData.snakeMapList[pos];
                if (!mList) mList = mapData.snakeMapList[1];
                return mList;
            }
        }

        private onSkComplete() {
            if (!this.template || this.destroyed) return;
            var isWake = yalla.Global.game_state != yalla.data.GameState.State_Sleep;
            this.sk = this.template.buildArmature(1);
            this.addChild(this.sk);
            this.sk.pos(this.width / 2, this.height / 2);
            // this.sk.pos(40, 40);
            this.sk.scale(1.4, 1.4);
            this.setChildIndex(this.sk, 0);
            this.sk.visible = false;
            if (this._choose && isWake) {
                this.sk.visible = this._choose;
                if(this.sk && !this.isMoving) this.sk.play(1, true);
            }
        }

        set choose(boo: boolean) {
            if (this.isMoving || !this.sk) return;
            var isSleep = yalla.Global.game_state == yalla.data.GameState.State_Sleep;
            this._choose = boo
            this.sk.visible = boo;
            if (isSleep) return;
            if (boo) {
                this.sk.play(1, true);
            } else {
                this.sk.stop();
            }
        }
        get choose(): boolean {
            return this._choose;
        }

        public reset(posIndex) {
            this.choose = false;
            this.scale(1, 1);
            this.clearTimeLine();
            // if (this._isHorizontalMove || this._isVertialMove || this._num > 0) {
            if (this.moving || this.stepNum > 0) {
                this.stepNum = 0;
                this.moving = false;
                // console.log('====roundEnd 0====');
                this.roundEnd(posIndex);

            }
            this.resetSnakeLadder(posIndex);
        }

        public resetSnakeLadder(posIndex) {
            if (this._isSnakeKissMove || (this._ladderRoutList && this._ladderRoutList.length > 0)) {
                this._isSnakeKissMove = false;
                this._snakeRoutList = null;
                this._snakeKissLen = 0;
                this._ladderRoutList = null;
                this.checkPos(posIndex);
                this._isMoving = false;
            }
        }

        public clear(): void {
            this._isMoving = false;
            this._soundTimeOut && clearTimeout(this._soundTimeOut);
            this._playTimeOut && clearTimeout(this._playTimeOut);
            this.sk && this.sk.destroy();
            if (this.template) {
                this.template.offAll();
                this.template.destroy();
                this.template = null;
            }
            if (this.timeLine) {
                this.timeLine.pause();
                this.timeLine.destroy();
                this.timeLine = null;
            }
            Laya.Tween.clearAll(this);
            // Laya.timer.clear(this, this.onEnterFrame);
            this._snakeRoutList = null;
            this._ladderRoutList = null;
            this._snakeRoutRegionList = null;
        }
    }
}