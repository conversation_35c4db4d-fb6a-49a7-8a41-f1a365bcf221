module yalla.view.game.snake {
    import Sprite = Laya.Sprite;
    import UserService = yalla.data.snake.UserService;

    export class ChessOld extends Laya.Image {
        // public chessImg: Laya.Image = null;
        public color: number = 0;          //RED=0;YELLOW=2;BLUE=3;GREEN=1;ORANGE=4;
        private _scale: number = 0;
        private _posIndex: number = 0;       //棋子当前位置

        // public Wid: number = 48;
        // public Hei: number = 76;
        public Wid: number = 52;
        public Hei: number = 56;

        private ScaleSp: number = 0.015;
        private _num: number = 0;                    //可以移动步数
        private _isHorizontalMove: boolean = false;
        private _side: number = 0;
        private _v: number = -1;                     //缩放系数
        private _angle = 0;
        private _centX = 0;
        private _centY = 0;

        public Vy: number = 1.6; //1.2 2.5
        private _isVertialMove: boolean = false;
        private _snakeVerticalVy: number = 0;    //加速度
        private _snakeVertialAddY: number = 0;  //y向上偏移
        private _targetY: number = 0;            //目标y
        private _vSide: number = 0;

        private _isSnakeKissMove: boolean = false;
        private _snakeRoutList: Array<any> = [];
        private _snakeSpeed: number = 0;
        private _snakePoint: Laya.Point;
        private _snakeToPoint: Laya.Point;
        private _snakeRadian: number;
        // private _snakeAngle: number;
        private _snakeRoutRegionList = [];

        private _ladderRoutList: Array<any> = [];
        private _nextEndPos: number = 0;
        private skin_id: number = null;
        private _skinIsLoaded: boolean = false;
        public idx: number = 0;

        constructor(color: number,idx:number) {
            super();
            this.color = color;
            this.idx = idx;
            // this._snakeSpeed = yalla.data.Snake_Speed;
            this.init();
        }

        private init(): void  {
            this.anchorX = this.anchorY = 0.5;
            // this.chessImg = new Laya.Image();
            // this.chessImg.width = this.Wid;
            // this.chessImg.height = this.Hei;
            // this.addChild(this.chessImg);
            this.name = this.color.toString();
            // var skin = this.skin[this.color];
            // var iconSp = new Sprite();
            // var texture = Laya.loader.getRes('game/chess_'+skin+'.png');
            // iconSp.graphics.drawTexture(texture);
            // this.addChild(iconSp);
            // this.size(yalla.data.RectWid, yalla.data.RectHei);
            // iconSp.pos(this.width/2 - this.Hei/2, this.height - this.Hei - 16);
            this.setSize(this.Wid, this.Hei);
            Laya.timer.frameLoop(1, this, this.onEnterFrame);
        }

        private setSkin(name: string) {
            // if (this.color == 0) {
            //     this.skin = "snake/chess_red.png";
            // } else {
            //     this.skin = "snake/chess_blue.png";
            // }
            if (this.skin_id == 11000) {
                this.skin = `game/${name}_11000_${this.color}.png`;
            } else if (this._skinIsLoaded) {
                this.skin = `chess_${this.skin_id}/${name}_${this.skin_id}_${this.color}.png`;
            } else {
                this.skin = `game/${name}_11000_${this.color}.png`;
                yalla.event.YallaEvent.instance.once(`chess_${this.skin_id}.atlas`, this, () => {
                    this.skin = `chess_${this.skin_id}/${name}_${this.skin_id}_${this.color}.png`;
                    this._skinIsLoaded = true;
                })
                yalla.event.YallaEvent.instance.once(`chess_${this.skin_id}.png`, this, () => {
                    yalla.File.getFileByNative(`chess_${this.skin_id}.atlas`);
                })
                yalla.File.getFileByNative(`chess_${this.skin_id}.png`);
            }
        }

        set skinId(val: number) {
            this.skin_id = val;
            this.setSkin("chess");
        }
        get skinId(): number {
            return this.skin_id;
        }

        public show(): void  {
            Laya.timer.clear(this, this.onEnterFrame);
            Laya.timer.frameLoop(1, this, this.onEnterFrame);
        }

        public hide(): void  {
            this._isHorizontalMove = false;
            this._isSnakeKissMove = false;
            this._isVertialMove = false;
            this._vSide = this._side = 0;
            this._angle = 0;
            this._centX = this._centY = this._targetY = 0;
            this._snakeRoutList = [];
            this._ladderRoutList = [];
            Laya.timer.clear(this, this.onEnterFrame);
            if (this._nextEndPos > 0) {
                this.roundEnd(this._nextEndPos);
            }
        }

        private onEnterFrame(): void  {
            if (this._isHorizontalMove)  {
                if ((this._side == 1 && this._angle >= 90) || (this._side == -1 && this._angle <= 270)) {
                    --this._num;
                    ++this.posIndex;
                    this.setMove();
                    return;
                }
                this._angle += this._side * 5;
                // this.x = this._centX + Math.sin(this._angle / 180 * Math.PI) * yalla.data.RectWid;
                // this.y = this._centY - Math.cos(this._angle / 180 * Math.PI) * yalla.data.RectHei / 2;
                // this.scaleY += this.ScaleSp * this._v;
                // this.y -= yalla.data.JumpHei * (1 - this.scaleY);//跳动y坐标偏移

                this.x = this._centX + Math.sin(this._angle / 180 * Math.PI) * yalla.data.RectWid;
                this.y = this._centY - Math.cos(this._angle / 180 * Math.PI) * yalla.data.JumpHei;
                this.scaleY += this.ScaleSp * this._v;
                // this.y -= yalla.data.JumpHei * (1 - this.scaleY);//跳动y坐标偏移

                if (this._angle % 45 == 0) this._v *= -1;
            }

            if (this._isVertialMove)  {
                if ((this.y <= this._targetY - this._snakeVertialAddY) && this._vSide == -1) {//上
                    this._vSide = 1;
                    return;
                } else if (this.y >= this._targetY && this._vSide == 1) {//下
                    this.y = this._targetY;
                    --this._num;
                    ++this.posIndex;
                    this.setMove();
                    return;
                }
                // this._snakeVerticalVy += this.Vy * this._vSide;
                // this.y += this._snakeVerticalVy;

                this._snakeVerticalVy += 0.01 * this._vSide;
                this.y += (this._vSide * 5 + this._snakeVerticalVy);
            }

            if (this._isSnakeKissMove)  {
                var dis: number = this._snakeToPoint.distance(this.x, this.y);
                if (dis <= 15)  {
                    this.setKissMove();
                    return;
                }
                this.x = this.x + this._snakeSpeed * Math.cos(this._snakeRadian);
                this.y = this.y + this._snakeSpeed * Math.sin(this._snakeRadian);
            }
        }

        public initData(skinId: number, posIndex: number): void  {
            this.skinId = skinId;
            this._posIndex = posIndex;
            // this.setScale(scale);
        }

        public setData(msg: any, p: any): void  {
            this._nextEndPos = msg.targetPos;
            this.reset(this._nextEndPos);
            this.setSize(this.Wid, this.Hei);
            this.checkPos(msg.oldPos);
            console.log(this.posIndex,'======chess.setData=====',this.x, this.y);
            switch (msg.event) {
                case yalla.data.GameEvent.SAL_CHESS_MOVE://移动
                    console.log(this.posIndex, '---', '===chess.move==', this._nextEndPos);
                    if (yalla.Global.game_state == yalla.data.GameState.State_Sleep) {
                        this.roundEnd(this._nextEndPos);
                        return;
                    }
                    this._num = msg.throwNum;
                     console.log(this.idx, '-----setMove 3');
                    this.setMove();
                    break;
                case yalla.data.GameEvent.SAL_CHESS_MOVE_SNAKE://蛇吻
                    // this._snakeSpeed = yalla.data.Snake_Speed; 

                    // this.posIndex = p.end;//先把位置索引设置好
                    if (yalla.Global.game_state == yalla.data.GameState.State_Sleep) {
                        this.roundEnd(this._nextEndPos);
                        return;
                    }
                    var posList = this.getPos(this._nextEndPos);
                    this._snakeRoutList = this.getRoutList(p).concat([]);
                    // if (this._mapData.isLoadEle(p.skinId)) this._snakeRoutList = p.realList.concat([]);
                    // else this._snakeRoutList = p.nList.concat([]);
                    this._snakeRoutList.push(posList);
                    console.log(this.posIndex, posList,'====case snake===',this._snakeRoutList, this._snakeRoutList.length);
                    this.setKissMove();
                    yalla.Sound.playSound('snake_kiss');
                    break;
                case yalla.data.GameEvent.SAL_CHESS_MOVE_LADDER://爬梯
                    if (yalla.Global.game_state == yalla.data.GameState.State_Sleep) {
                        this.roundEnd(this._nextEndPos);
                        return;
                    }
                    this.setLadderMove(p);
                    break;
            }
        }

        /**
         * 移动
         * @param num 
         */
        private setMove(): void  {
            this._isHorizontalMove = this._isVertialMove = false;
            if (this._num < 1 || this.posIndex >= yalla.data.Max_Num) {
                console.log(this.idx, this._nextEndPos,'====setMoveEnd====posIndex:',this.posIndex);
                this.roundEnd(this._nextEndPos);
                return;
            }
            var list = this.getSnakeMapList(this._posIndex);
            if (!list) { console.log('===非法移动===posIndex:', this._posIndex); return; }
            console.log(list,'===setMove===', this._posIndex);
            this._side = list[2];
            switch (this._side) {
                case 1:
                    this._centX = this.x;
                    this._centY = this.y;
                    this._angle = 0;
                    this._isHorizontalMove = true;
                    break;
                case -1:
                    this._centX = this.x;
                    this._centY = this.y;
                    this._angle = 360;
                    this._isHorizontalMove = true;
                    break;
                case 2:
                    this._vSide = -1;//先上 后下
                    this._isVertialMove = true;
                    this._snakeVerticalVy = 0;
                    this._targetY = this.getSnakeMapList(this._posIndex + 1)[0] * yalla.data.RectHei + yalla.data.RectHei/2;
                    break;
            }
            yalla.Sound.playSound('token_move');
        }

        /**
         * 蛇吻
         */
        private setKissMove(): void  {
            if (!this._snakeRoutList || this._snakeRoutList.length < 1) {
                console.log('====setKissMove=====',this._nextEndPos);
                this._isSnakeKissMove = false;
                this.roundEnd(this._nextEndPos);
                return;
            }
            // if (this._snakeRoutList.length < this._snakeRoutRegionList.length / 2) this._snakeSpeed = yalla.data.Snake_Speed * 0.6;
            var endPos = this._snakeRoutList.shift();
            console.log(endPos[0], endPos[1], this.x, this.y,'=====setKissMove 0=======',this._snakeRoutList);
            this._isSnakeKissMove = true;
            this._snakePoint = new Laya.Point(this.x, this.y);
            this._snakeToPoint = new Laya.Point(endPos[0], endPos[1]);

            this._snakeRadian = Math.atan2(this._snakeToPoint.y - this._snakePoint.y, this._snakeToPoint.x - this._snakePoint.x);
        }

        /**
         * 爬梯
         */
        private setLadderMove(element:MapInfo): void  {
            // var endPosList = this.getSnakeMapList(this._nextEndPos);
            // var xx = endPosList[1] * yalla.data.RectWid + (yalla.data.RectWid / 2 + (this.width - this.Wid) / 2);
            // var yy = endPosList[0] * yalla.data.RectHei + yalla.data.RectHei;
            // this._ladderRoutList = yalla.util.getPointList(new Laya.Point(this.x, this.y), new Laya.Point(xx, yy), yalla.data.Step_Len);
           
           //跳跃 
            var ladderList = this.getRoutList(element).concat([]);
            var beginX = ladderList[0][0], beginY = ladderList[0][1];
            var endX = ladderList[1][0], endY = ladderList[1][1];
            this._ladderRoutList = yalla.util.getPointList(new Laya.Point(beginX, beginY), new Laya.Point(endX, endY), yalla.data.Step_Len);
            
            ////直线运动
            // var ladderList = this.getRoutList(element);
            // var beginX = ladderList[0][0], beginY = ladderList[0][1];
            // var endX = ladderList[1][0], endY = ladderList[1][1];
            // this._ladderRoutList = ladderList;
            console.log(this._ladderRoutList);
            this.ladderMove();
        }
        private ladderMove(): void  {
            if (!this._ladderRoutList || this._ladderRoutList.length < 1) {
                this.roundEnd(this._nextEndPos);
                return;
            }
            // var time = yalla.data.Step_Time * 2.5;
            // var self = this;
            // var goal = this._ladderRoutList[1];
            // var tw = Laya.Tween.to(this, { x: goal[0], y: goal[1] }, time, Laya.Ease.sineInOut, Laya.Handler.create(this, function () {
            //     yalla.Sound.playSound('token_move');
            //         Laya.Tween.clear(tw);
            //         self.ladderMove();
            //         self._ladderRoutList = [];
            // }));

            var time = yalla.data.Step_Time;
            var self = this;
            var goal = this._ladderRoutList.shift();
            var topY = goal[1] - yalla.data.JumpLadder;
            var tw = Laya.Tween.to(this, { x: goal[0], y: topY }, time, null, Laya.Handler.create(this, function () {
                yalla.Sound.playSound('token_move');
                var tw1 = Laya.Tween.to(self, { y: goal[1] }, 50, null, Laya.Handler.create(self, function () {
                    Laya.Tween.clear(tw1);
                    Laya.Tween.clear(tw);
                    self.ladderMove();
                }));
            }));
        }

        private roundEnd(posIndex: number): void  {
            var mapData = UserService.instance.mapData;
            if (mapData && posIndex == mapData.endPos) {
                /**到达终点 */
                this.visible = false;
                yalla.SkAnimation.instance.playSnakeEndPos([this.x,this.y],Laya.Handler.create(this, () => {
                    this.visible = true;
                }))
            }
            Laya.Tween.clearAll(this);
            this.checkPos(posIndex);
            yalla.event.YallaEvent.instance.event(yalla.data.snake.EnumCustomizeCmd.Snake_RoundEnd, [this]);
        }

        /**
         * [1 - 100] 动画后最终最终位置校验
         * @param posIndex 
         */
        public checkPos(posIndex: number): void  {
            if (posIndex < 1) return;
            var posList = this.getPos(posIndex);
            this._posIndex = posIndex;
            this.pos(posList[0],posList[1]);
        }

        public setScale(scale: number): void  {
            this._scale = scale;
            this.size(this.Wid * scale, this.Hei * scale);
            // this.pivot(this.width / 2, this.height / 2);
            // this.scale(scale, scale);
        }

        public setSize(wid:number, hei:number): void{
            this.size(wid, hei);
            // this.pivot(this.width / 2, this.height / 2);
        }

        public get posIndex() { return this._posIndex; }
        public set posIndex(i: number) { this._posIndex = i; }
        public get Side() { return this.getSnakeMapList(this._posIndex) }

        public getPos(posIndex: number)  {
            var list = this.getSnakeMapList(posIndex);
            var x = 0, y = 0;
            if (list) {
                x = list[1] * yalla.data.RectWid + yalla.data.RectWid /2;
                y = list[0] * yalla.data.RectHei + yalla.data.RectHei/2;
            }
            return [x,y];
        }

        /**走到同一个格子 */
        public getPilePos(posIndex: number, i:number) {
            var vx = i == 0 ? -12 : 12;
            var list = this.getSnakeMapList(posIndex);
            var x = 0, y = 0;
            if (list) {
                x = list[1] * yalla.data.RectWid + yalla.data.RectWid /2 + vx;
                y = list[0] * yalla.data.RectHei + yalla.data.RectHei/2;
            }
            return [x, y];
        }

        /**如果皮肤没有加载到，则用默认蛇皮肤的路径 */
        getRoutList(element: MapInfo) {
            var mapData = UserService.instance.mapData;
            if (mapData) {
                console.log('===chess.getRoutList====',element)
                if (mapData.isLoadEle(element.skinId)) {
                    return element.realList;
                }
            }
            return element.nList;
        }

        getSnakeMapList(pos) {
            var mapData = UserService.instance.mapData;
            if (mapData && mapData.snakeMapList) {
                var mList = mapData.snakeMapList[pos];
                if (!mList) mList = mapData.snakeMapList[1];
                return mList;
            }
        }

        private reset(posIndex) {
            this.scale(1, 1);
            if (this._isHorizontalMove || this._isVertialMove || this._num > 0) {
                this._num = 0;
                this.roundEnd(posIndex);

            } else if (this._isSnakeKissMove) {
                this._isSnakeKissMove = false;
                this._snakeRoutList = null;
            }
            this._ladderRoutList = null;
        }

        public clear(): void  {
            Laya.Tween.clearAll(this);
            Laya.timer.clear(this, this.onEnterFrame);
            this._snakeRoutList = null;
            this._ladderRoutList = null;
            this._snakeRoutRegionList = null;
        }
    }
}