module yalla.view.result.snake {
    import UserService = yalla.data.snake.UserService;
    export class Result extends ui.snake.resultUI {

        private _backFunc: Function;
        private _playAgainFunc: Function;

        private _templet: Laya.Templet;
        private _sk: Laya.Skeleton;
        private _gameHeadHash: Object = {};
        private _nameItemHash: Object = {};

        private _data: any;
        public isWin: boolean = true;

        constructor(msg: any, back: Function, playAgain: Function) {
            super();
            yalla.util.swapPos(this.again, this.back);
            this._data = msg;
            this._backFunc = back;
            this._playAgainFunc = playAgain;
            this.initUI();
            this.initEvent();
            if (yalla.Global.Account.roomid) yalla.util.clogDBAmsg('200019', { roomid: yalla.Global.Account.roomid }, 3);
        }

        private initUI() {
            this.name = 'resultView';

            this.playAni(this._data);
            this.titleTxt.text = this.isWin ? 'You Win' : 'You Lose';

            var resList = [];
            var name = this.isWin ? 'result/win' : 'result/lose';
            resList = [{ url: yalla.getSkeletonPng(name), type: Laya.Loader.IMAGE },
            { url: yalla.getSkeleton(name), type: Laya.Loader.BUFFER }];
            //偶现动画预加载失败的情况，这里重新再加载一次
            Laya.loader.load(resList, Laya.Handler.create(this, () => {
                if (!this._templet) this._templet = new Laya.Templet();
                this._templet.on(Laya.Event.COMPLETE, this, this.parseComplete);
                this._templet.loadAni(yalla.getSkeleton(name));
            }));

            var room = UserService.instance.room;
            if (room.player && room.player.player) {
                var len = room.player.player.length;
                if (room.isPrivate == GameRoomType.CHAMPION) {
                    this.again.visible = false;
                    this.back.centerX = 0;
                    this.back.y = (this.back.y - (4 - len) * (125 + 14));
                    this.back.scale(0, 0);
                    this.back.label = 'Back';
                } else {
                    this.back.y = this.again.y = (this.back.y - (4 - len) * (125 + 14));
                    this.back.scaleX = this.back.scaleY = 0;
                    this.again.scaleX = this.again.scaleY = 0;
                    if (room.isPrivate == GameRoomType.VIP) {
                        this.again.label = 'Share';
                    } else {
                        this.again.label = 'Play Again';
                    }
                }
            }

            if (this.isWin) {
                this.titleTxt.strokeColor = '#A62619';
            } else {
                this.titleTxt.color = '#ffffff';
                this.titleTxt.strokeColor = '#297BC5';
            }
            this.titleTxt.stroke = 4;
            // this.goldExp.visible = !(room.isPrivate == GameRoomType.PRIVATE);
        }

        private initEvent(): void {
            if (UserService.instance.room.isPrivate != GameRoomType.CHAMPION) {
                Laya.timer.once(10000, this, this._backFunc);
                this.again.once('click', this, this.playagainHander);
            }
            this.back.once("click", this, this.backHander);
        }

        private backHander = yalla.util.withLock(() => {
            if (UserService.instance.room.isPrivate != GameRoomType.CHAMPION) {
                yalla.Native.instance.mobClickEvent(buryPoint.DGAME_BACK);
                Laya.timer.clear(this, this._backFunc);
            }
            this._backFunc();
            if (this.back.label == 'Back' && yalla.Global.Account.roomid) {
                yalla.util.clogDBAmsg('10269', { roomid: yalla.Global.Account.roomid });
            }
        })

        private playagainHander = yalla.util.withLock(() => {
            this._playAgainFunc();
            Laya.timer.clear(this, this._backFunc);
        })

        private parseComplete() {
            this._sk = this._templet.buildArmature(1);
            this._sk.pos(this.topMc.width / 2, this.topMc.height / 2 + 30);
            this.topMc.addChild(this._sk);
            this.topMc.setChildIndex(this._sk, 0);
            this._sk.play(0, false);
            this._sk.on(Laya.Event.STOPPED, this, () => {
                this._sk.play(1, true);
            });
        }

        private playAni(result: any): void {
            // var losersInfo: Array<DefineRoundResult> = UserService.instance.room.updateRoundResult(result.losersInfo, 'intergral');
            var room = UserService.instance.room;
            var roleList: Array<playerShowInfo> = result.player;//[{ idx: result.winnerId, nickName: result.winnerNickName, intergral: result.winnerIntergral }].concat(losersInfo);
            var len = roleList.length;
            for (var i = 0; i < len; i++) {
                this['info_' + i].x = 750;
                this['info_' + i].visible = true;
                Laya.Tween.to(this['info_' + i], { x: 0 }, 500, Laya.Ease.backOut, null, i * 80, true);

                var playerData = roleList[i];
                if (playerData) {
                    var idx = playerData.fPlayerInfo.idx;
                    if (!this._gameHeadHash[idx]) {
                        var gameHeadUI = new ui.domino.item.gameHeadUI();
                        if (i == 0) gameHeadUI.pos(108, 23).scale(0.7, 0.7);
                        else gameHeadUI.pos(108, 8).scale(0.7, 0.7);
                        this['info_' + i].addChild(gameHeadUI);
                        this._gameHeadHash[idx] = new yalla.view.game.GameHead(gameHeadUI);
                    }
                    this._gameHeadHash[idx].setData(playerData);

                    if (idx == UserService.instance.user.idx) {
                        this['my_' + i].visible = true;
                        if (i != 0) this.isWin = false;
                    }

                    var nameItem: yalla.common.NameItem = this._nameItemHash[i];
                    if (!nameItem) {
                        nameItem = new yalla.common.NameItem(this['nameItem_' + i], this['nikeName_' + i]);
                        this._nameItemHash[i] = nameItem;
                    }
                    var playerInfo = room.getPlayerByIdx(idx);
                    nameItem.resultName(playerInfo, yalla.util.filterName(playerData.fPlayerInfo.nikeName, 13, "m"));
                    // yalla.Emoji.lateLable(yalla.util.filterName(playerData.fPlayerInfo.nikeName, 13, "m"), this['nikeName_' + i], ['color', 'fontSize', 'stroke', 'strokeColor']);
                }
            }


            if (result.winnerExp) {
                this.goldExp.visible = !(room.isPrivate == GameRoomType.PRIVATE);
                this.expTxt.text = '+' + result.winnerExp;
            }

            Laya.Tween.to(this.back, { scaleX: 1, scaleY: 1 }, 200, Laya.Ease.backOut, null, len * 80 + 500);
            if (UserService.instance.room.isPrivate == GameRoomType.CHAMPION) {
                this['gold_0'].text = '+' + yalla.util.filterNum(UserService.instance.room.cost);
            } else {
                var royalty = yalla.Global.Account.royalty ? yalla.Global.Account.royalty : 0;
                this['gold_0'].text = '+' + yalla.util.filterNum(result.winMoney1);
                Laya.Tween.to(this.again, { scaleX: 1, scaleY: 1 }, 200, Laya.Ease.backOut, null, len * 80 + 500 + 100);
            }

            Laya.timer.once(2500, this, () => {
                if (this.back.scaleX != 1) this.back.scale(1, 1);
                if (this.again.visible && this.again.scaleX != 1) this.again.scale(1, 1);
            })
        }

        public frameLoopName(): void {
            for (var key in this._nameItemHash) {
                var nameItem = this._nameItemHash[key];
                nameItem.frameLoopName();
            }
        }

        public backHallClear(): void {
            this.removeEvent();
        }

        private removeEvent(): void {
            Laya.timer.clearAll(this);
            Laya.timer.clear(this, this.playAni);
            Laya.timer.clear(this, this._backFunc);
        }

        public clear(): void {
            this.backHallClear();
            for (var j in this._gameHeadHash) {
                this._gameHeadHash[j].clear();
                delete this._gameHeadHash[j];
            }
            if (this._templet) {
                this._templet.offAll();
                this._templet.destroy();
                this._templet = null;
            }
            if (this._sk) {
                this._sk.offAll();
                this._sk.destroy();
            }
            this.topMc.destroy(true);
            this.removeSelf();
            this.destroy(true);
        }
    }
}