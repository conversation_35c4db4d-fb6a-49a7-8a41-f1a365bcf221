@echo off

:: 删除目录
rd /s /q "D:\Develop\Ugit\ludo_domino\ClientH5\release\web\newVer\libs"
del /q "D:\Develop\Ugit\ludo_domino\ClientH5\release\web\newVer\js\*"

:: 获取当前时间戳
for /f "tokens=1-6 delims=/-: " %%a in ('date /t ^& time /t') do (
    set "year=%%c"
    set "month=%%a"
    set "day=%%b"
    set "hour=%%d"
    set "minute=%%e"
    set "second=%%f"
)
set "currentTimeStamp=%year%%month%%day%%hour%%minute%%second%"

:: 移动文件
move "D:\Develop\Ugit\ludo_domino\ClientH5\release\web\newVer\code.js" "D:\Develop\Ugit\ludo_domino\ClientH5\release\web\newVer\js\code%currentTimeStamp%.js"

:: 临时文件路径
set "tempFile=%TEMP%\temp_index.html"

:: 删除包含 script 和 !-- 的行
findstr /v /c:"script" "D:\Develop\Ugit\ludo_domino\ClientH5\release\web\newVer\index.html" > "%tempFile%"
findstr /v /c:"!--" "%tempFile%" > "D:\Develop\Ugit\ludo_domino\ClientH5\release\web\newVer\index.html"

:: 替换 <body> 标签
powershell -Command "(Get-Content 'D:\Develop\Ugit\ludo_domino\ClientH5\release\web\newVer\index.html') -replace '<body>', '<body><script src=\"js/code%currentTimeStamp%.js\"></script>' | Set-Content 'D:\Develop\Ugit\ludo_domino\ClientH5\release\web\newVer\index.html'"

:: 删除临时文件
del "%tempFile%"